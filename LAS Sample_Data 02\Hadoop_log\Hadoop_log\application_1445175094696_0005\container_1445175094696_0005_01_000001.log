2015-10-18 21:45:46,935 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445175094696_0005_000001
2015-10-18 21:45:47,279 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-18 21:45:47,279 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 5 cluster_timestamp: 1445175094696 } attemptId: 1 } keyId: -2054027300)
2015-10-18 21:45:47,545 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-18 21:45:48,545 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-18 21:45:48,607 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-18 21:45:48,638 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-18 21:45:48,638 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-18 21:45:48,638 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-18 21:45:48,638 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-18 21:45:48,638 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-18 21:45:48,654 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-18 21:45:48,654 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-18 21:45:48,654 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-18 21:45:48,701 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 21:45:48,717 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 21:45:48,748 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 21:45:48,748 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-18 21:45:48,795 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-18 21:45:49,060 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 21:45:49,185 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 21:45:49,185 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-18 21:45:49,185 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445175094696_0005 to jobTokenSecretManager
2015-10-18 21:45:49,920 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445175094696_0005 because: not enabled; too many maps; too much input;
2015-10-18 21:45:49,951 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445175094696_0005 = 1313861632. Number of splits = 10
2015-10-18 21:45:49,951 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445175094696_0005 = 1
2015-10-18 21:45:49,951 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0005Job Transitioned from NEW to INITED
2015-10-18 21:45:49,951 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445175094696_0005.
2015-10-18 21:45:50,014 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-18 21:45:50,045 INFO [Socket Reader #1 for port 4226] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 4226
2015-10-18 21:45:50,092 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-18 21:45:50,092 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-18 21:45:50,092 INFO [IPC Server listener on 4226] org.apache.hadoop.ipc.Server: IPC Server listener on 4226: starting
2015-10-18 21:45:50,092 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-39.fareast.corp.microsoft.com/**************:4226
2015-10-18 21:45:50,217 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-18 21:45:50,232 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-18 21:45:50,248 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-18 21:45:50,248 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-18 21:45:50,248 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-18 21:45:50,264 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-18 21:45:50,264 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-18 21:45:50,279 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 4233
2015-10-18 21:45:50,279 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-18 21:45:50,326 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_4233_mapreduce____.fhx83j\webapp
2015-10-18 21:45:50,639 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:4233
2015-10-18 21:45:50,639 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 4233
2015-10-18 21:45:51,092 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-18 21:45:51,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445175094696_0005
2015-10-18 21:45:51,092 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-18 21:45:51,107 INFO [Socket Reader #1 for port 4236] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 4236
2015-10-18 21:45:51,107 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-18 21:45:51,107 INFO [IPC Server listener on 4236] org.apache.hadoop.ipc.Server: IPC Server listener on 4236: starting
2015-10-18 21:45:51,139 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-18 21:45:51,139 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-18 21:45:51,139 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-18 21:45:51,185 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-18 21:45:51,279 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-18 21:45:51,279 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-18 21:45:51,279 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-18 21:45:51,295 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-18 21:45:51,295 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0005Job Transitioned from INITED to SETUP
2015-10-18 21:45:51,295 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-18 21:45:51,310 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0005Job Transitioned from SETUP to RUNNING
2015-10-18 21:45:51,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:45:51,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-18 21:45:51,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:45:51,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:45:51,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:45:51,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:45:51,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:45:51,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:45:51,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:45:51,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:45:51,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:45:51,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:45:51,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:45:51,357 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-18 21:45:51,373 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-18 21:45:51,404 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445175094696_0005, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0005/job_1445175094696_0005_1.jhist
2015-10-18 21:45:52,279 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-18 21:45:52,326 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0005: ask=6 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:19456, vCores:-8> knownNMs=4
2015-10-18 21:45:52,326 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:19456, vCores:-8>
2015-10-18 21:45:52,342 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 10
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0005_01_000002 to attempt_1445175094696_0005_m_000000_0
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0005_01_000003 to attempt_1445175094696_0005_m_000001_0
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0005_01_000004 to attempt_1445175094696_0005_m_000002_0
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0005_01_000005 to attempt_1445175094696_0005_m_000003_0
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0005_01_000006 to attempt_1445175094696_0005_m_000004_0
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0005_01_000007 to attempt_1445175094696_0005_m_000005_0
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0005_01_000008 to attempt_1445175094696_0005_m_000006_0
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0005_01_000011 to attempt_1445175094696_0005_m_000007_0
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0005_01_000009 to attempt_1445175094696_0005_m_000008_0
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0005_01_000010 to attempt_1445175094696_0005_m_000009_0
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-18>
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:45:53,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-18 21:45:53,467 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:53,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0005/job.jar
2015-10-18 21:45:53,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0005/job.xml
2015-10-18 21:45:53,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-18 21:45:53,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-18 21:45:53,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-18 21:45:53,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:45:53,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:53,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:45:53,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:53,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:45:53,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:53,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:45:53,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:53,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:45:53,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:53,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:45:53,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:53,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:45:53,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:53,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:45:53,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:53,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:45:53,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:45:53,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:45:53,576 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0005_01_000003 taskAttempt attempt_1445175094696_0005_m_000001_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0005_01_000002 taskAttempt attempt_1445175094696_0005_m_000000_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0005_01_000004 taskAttempt attempt_1445175094696_0005_m_000002_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0005_01_000005 taskAttempt attempt_1445175094696_0005_m_000003_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0005_01_000006 taskAttempt attempt_1445175094696_0005_m_000004_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0005_01_000007 taskAttempt attempt_1445175094696_0005_m_000005_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0005_01_000008 taskAttempt attempt_1445175094696_0005_m_000006_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0005_01_000011 taskAttempt attempt_1445175094696_0005_m_000007_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0005_01_000009 taskAttempt attempt_1445175094696_0005_m_000008_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0005_01_000010 taskAttempt attempt_1445175094696_0005_m_000009_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0005_m_000005_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0005_m_000001_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0005_m_000004_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0005_m_000006_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0005_m_000003_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0005_m_000007_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0005_m_000000_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0005_m_000008_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0005_m_000009_0
2015-10-18 21:45:53,576 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0005_m_000002_0
2015-10-18 21:45:53,592 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:45:53,623 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:45:53,623 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 21:45:53,623 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 21:45:53,639 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:45:53,639 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:45:53,639 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:45:53,639 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 21:45:53,639 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:45:53,639 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:45:53,732 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0005_m_000007_0 : 13562
2015-10-18 21:45:53,732 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0005_m_000002_0 : 13562
2015-10-18 21:45:53,732 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0005_m_000000_0 : 13562
2015-10-18 21:45:53,732 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0005_m_000009_0 : 13562
2015-10-18 21:45:53,732 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0005_m_000007_0] using containerId: [container_1445175094696_0005_01_000011 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 21:45:53,748 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0005_m_000001_0 : 13562
2015-10-18 21:45:53,748 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0005_m_000004_0 : 13562
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0005_m_000002_0] using containerId: [container_1445175094696_0005_01_000004 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0005_m_000000_0] using containerId: [container_1445175094696_0005_01_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0005_m_000009_0] using containerId: [container_1445175094696_0005_01_000010 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:45:53,748 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0005_m_000005_0 : 13562
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0005_m_000001_0] using containerId: [container_1445175094696_0005_01_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0005_m_000007
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:45:53,748 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0005_m_000003_0 : 13562
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0005_m_000004_0] using containerId: [container_1445175094696_0005_01_000006 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0005_m_000002
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0005_m_000000
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0005_m_000009
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0005_m_000005_0] using containerId: [container_1445175094696_0005_01_000007 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0005_m_000001
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0005_m_000003_0] using containerId: [container_1445175094696_0005_01_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:45:53,748 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0005_m_000006_0 : 13562
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0005_m_000004
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0005_m_000005
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0005_m_000003
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0005_m_000006_0] using containerId: [container_1445175094696_0005_01_000008 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 21:45:53,748 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:45:53,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0005_m_000006
2015-10-18 21:45:53,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:45:53,764 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0005_m_000008_0 : 13562
2015-10-18 21:45:53,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0005_m_000008_0] using containerId: [container_1445175094696_0005_01_000009 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 21:45:53,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:45:53,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0005_m_000008
2015-10-18 21:45:53,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:45:54,392 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0005: ask=6 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-18> knownNMs=4
2015-10-18 21:45:56,486 INFO [Socket Reader #1 for port 4236] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0005 (auth:SIMPLE)
2015-10-18 21:45:56,501 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0005_m_000011 asked for a task
2015-10-18 21:45:56,501 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0005_m_000011 given task: attempt_1445175094696_0005_m_000007_0
2015-10-18 21:45:57,126 INFO [Socket Reader #1 for port 4236] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0005 (auth:SIMPLE)
2015-10-18 21:45:57,126 INFO [Socket Reader #1 for port 4236] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0005 (auth:SIMPLE)
2015-10-18 21:45:57,142 INFO [Socket Reader #1 for port 4236] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0005 (auth:SIMPLE)
2015-10-18 21:45:57,142 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0005_m_000005 asked for a task
2015-10-18 21:45:57,142 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0005_m_000005 given task: attempt_1445175094696_0005_m_000003_0
2015-10-18 21:45:57,157 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0005_m_000004 asked for a task
2015-10-18 21:45:57,157 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0005_m_000004 given task: attempt_1445175094696_0005_m_000002_0
2015-10-18 21:45:57,157 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0005_m_000007 asked for a task
2015-10-18 21:45:57,157 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0005_m_000007 given task: attempt_1445175094696_0005_m_000005_0
2015-10-18 21:45:57,189 INFO [Socket Reader #1 for port 4236] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0005 (auth:SIMPLE)
2015-10-18 21:45:57,204 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0005_m_000002 asked for a task
2015-10-18 21:45:57,204 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0005_m_000002 given task: attempt_1445175094696_0005_m_000000_0
2015-10-18 21:45:57,283 INFO [Socket Reader #1 for port 4236] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0005 (auth:SIMPLE)
2015-10-18 21:45:57,283 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0005_m_000006 asked for a task
2015-10-18 21:45:57,283 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0005_m_000006 given task: attempt_1445175094696_0005_m_000004_0
2015-10-18 21:45:57,298 INFO [Socket Reader #1 for port 4236] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0005 (auth:SIMPLE)
2015-10-18 21:45:57,298 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0005_m_000003 asked for a task
2015-10-18 21:45:57,298 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0005_m_000003 given task: attempt_1445175094696_0005_m_000001_0
2015-10-18 21:45:59,095 INFO [Socket Reader #1 for port 4236] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0005 (auth:SIMPLE)
2015-10-18 21:45:59,095 INFO [Socket Reader #1 for port 4236] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0005 (auth:SIMPLE)
2015-10-18 21:45:59,095 INFO [Socket Reader #1 for port 4236] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0005 (auth:SIMPLE)
2015-10-18 21:45:59,126 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0005_m_000008 asked for a task
2015-10-18 21:45:59,126 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0005_m_000010 asked for a task
2015-10-18 21:45:59,126 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0005_m_000008 given task: attempt_1445175094696_0005_m_000006_0
2015-10-18 21:45:59,126 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0005_m_000010 given task: attempt_1445175094696_0005_m_000009_0
2015-10-18 21:45:59,126 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0005_m_000009 asked for a task
2015-10-18 21:45:59,126 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0005_m_000009 given task: attempt_1445175094696_0005_m_000008_0
2015-10-18 21:45:59,392 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:11264, vCores:-17>
2015-10-18 21:45:59,392 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 21:46:03,455 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.13103712
2015-10-18 21:46:04,330 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.13104042
2015-10-18 21:46:04,361 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.13102192
2015-10-18 21:46:04,767 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.131014
2015-10-18 21:46:04,830 INFO [IPC Server handler 12 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.13102706
2015-10-18 21:46:04,892 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.13101934
2015-10-18 21:46:04,892 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.13104132
2015-10-18 21:46:06,470 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.13103712
2015-10-18 21:46:07,361 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.13104042
2015-10-18 21:46:07,377 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.13102192
2015-10-18 21:46:07,783 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.131014
2015-10-18 21:46:07,845 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.13102706
2015-10-18 21:46:07,908 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.13101934
2015-10-18 21:46:07,908 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.13104132
2015-10-18 21:46:08,720 INFO [IPC Server handler 12 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.08760495
2015-10-18 21:46:08,799 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.13101135
2015-10-18 21:46:08,845 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.032188423
2015-10-18 21:46:09,470 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.13103712
2015-10-18 21:46:10,377 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.23578477
2015-10-18 21:46:10,392 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.23472299
2015-10-18 21:46:10,814 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.2252523
2015-10-18 21:46:10,877 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.22356683
2015-10-18 21:46:10,939 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.23649302
2015-10-18 21:46:10,939 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.23921585
2015-10-18 21:46:11,783 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.13102318
2015-10-18 21:46:11,908 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.13101135
2015-10-18 21:46:11,924 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.06272961
2015-10-18 21:46:12,502 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.23279914
2015-10-18 21:46:13,393 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.23924798
2015-10-18 21:46:13,408 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.23921879
2015-10-18 21:46:13,846 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.23919508
2015-10-18 21:46:13,908 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.23922287
2015-10-18 21:46:13,971 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.23922269
2015-10-18 21:46:13,971 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.23921585
2015-10-18 21:46:14,877 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.13102318
2015-10-18 21:46:15,002 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.13455208
2015-10-18 21:46:15,002 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.13101135
2015-10-18 21:46:15,502 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.23924637
2015-10-18 21:46:16,424 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.23924798
2015-10-18 21:46:16,440 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.23921879
2015-10-18 21:46:16,861 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.23919508
2015-10-18 21:46:16,924 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.23922287
2015-10-18 21:46:16,986 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.23922269
2015-10-18 21:46:16,986 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.23921585
2015-10-18 21:46:17,955 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.13102318
2015-10-18 21:46:18,127 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.16604526
2015-10-18 21:46:18,127 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.13101135
2015-10-18 21:46:18,502 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.23924637
2015-10-18 21:46:19,440 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.34742972
2015-10-18 21:46:19,455 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.34743196
2015-10-18 21:46:19,877 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.3474061
2015-10-18 21:46:19,940 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.3473985
2015-10-18 21:46:20,002 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.3474054
2015-10-18 21:46:20,002 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.3474062
2015-10-18 21:46:21,065 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.13102318
2015-10-18 21:46:21,205 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.13101135
2015-10-18 21:46:21,221 INFO [IPC Server handler 11 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.16604526
2015-10-18 21:46:21,518 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.23924637
2015-10-18 21:46:22,455 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.34742972
2015-10-18 21:46:22,471 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.34743196
2015-10-18 21:46:22,909 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.3474061
2015-10-18 21:46:22,971 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.3473985
2015-10-18 21:46:23,034 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.3474054
2015-10-18 21:46:23,034 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.3474062
2015-10-18 21:46:24,315 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.13102318
2015-10-18 21:46:24,487 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.23291758
2015-10-18 21:46:24,534 INFO [IPC Server handler 1 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.32927248
2015-10-18 21:46:24,549 INFO [IPC Server handler 12 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.16604526
2015-10-18 21:46:25,471 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.36352667
2015-10-18 21:46:25,487 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.34743196
2015-10-18 21:46:25,924 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.3474061
2015-10-18 21:46:25,987 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.3473985
2015-10-18 21:46:26,049 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.3474054
2015-10-18 21:46:26,049 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.40267667
2015-10-18 21:46:27,487 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.1476783
2015-10-18 21:46:27,549 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.34743145
2015-10-18 21:46:27,549 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.23923388
2015-10-18 21:46:27,612 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.16604526
2015-10-18 21:46:28,503 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.45565325
2015-10-18 21:46:28,503 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.455629
2015-10-18 21:46:28,940 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.45561612
2015-10-18 21:46:29,003 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.45559394
2015-10-18 21:46:29,065 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.45560944
2015-10-18 21:46:29,065 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.45563135
2015-10-18 21:46:30,565 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.34743145
2015-10-18 21:46:30,737 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.2239264
2015-10-18 21:46:30,799 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.23923388
2015-10-18 21:46:30,846 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.16604526
2015-10-18 21:46:31,518 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.45565325
2015-10-18 21:46:31,518 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.455629
2015-10-18 21:46:31,956 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.45561612
2015-10-18 21:46:32,018 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.45559394
2015-10-18 21:46:32,081 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.45560944
2015-10-18 21:46:32,081 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.45563135
2015-10-18 21:46:33,581 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.3642591
2015-10-18 21:46:33,846 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.23921506
2015-10-18 21:46:33,909 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.23923388
2015-10-18 21:46:33,956 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.23363665
2015-10-18 21:46:34,550 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.5431776
2015-10-18 21:46:34,550 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.530537
2015-10-18 21:46:34,972 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.46638545
2015-10-18 21:46:35,034 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.45559394
2015-10-18 21:46:35,112 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.45560944
2015-10-18 21:46:35,112 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.56380075
2015-10-18 21:46:36,597 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.4556257
2015-10-18 21:46:36,972 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.23921506
2015-10-18 21:46:37,034 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.23923388
2015-10-18 21:46:37,065 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.3031575
2015-10-18 21:46:37,565 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.5638328
2015-10-18 21:46:37,565 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.5638294
2015-10-18 21:46:38,003 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.56381613
2015-10-18 21:46:38,065 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.5637838
2015-10-18 21:46:38,128 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.56381226
2015-10-18 21:46:38,128 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.56380075
2015-10-18 21:46:39,597 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.4556257
2015-10-18 21:46:40,097 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.23921506
2015-10-18 21:46:40,175 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.23923388
2015-10-18 21:46:40,190 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.3031575
2015-10-18 21:46:40,581 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.5638328
2015-10-18 21:46:40,597 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.5638294
2015-10-18 21:46:41,019 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.56381613
2015-10-18 21:46:41,081 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.5637838
2015-10-18 21:46:41,144 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.56380075
2015-10-18 21:46:41,144 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.56381226
2015-10-18 21:46:42,612 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.4556257
2015-10-18 21:46:43,347 INFO [IPC Server handler 11 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.5638294
2015-10-18 21:46:43,519 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.23921506
2015-10-18 21:46:43,550 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.3031575
2015-10-18 21:46:43,612 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.56380075
2015-10-18 21:46:43,612 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.6639012
2015-10-18 21:46:43,628 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.3404702
2015-10-18 21:46:43,628 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.667
2015-10-18 21:46:43,722 INFO [IPC Server handler 15 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.6639012
2015-10-18 21:46:44,034 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.5842842
2015-10-18 21:46:44,097 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.5637838
2015-10-18 21:46:44,175 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.61376935
2015-10-18 21:46:44,175 INFO [IPC Server handler 11 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.667
2015-10-18 21:46:45,550 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.61376935
2015-10-18 21:46:45,613 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.51883334
2015-10-18 21:46:46,050 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.5842842
2015-10-18 21:46:46,269 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.5637838
2015-10-18 21:46:46,613 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.23921506
2015-10-18 21:46:46,628 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.667
2015-10-18 21:46:46,644 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.667
2015-10-18 21:46:46,691 INFO [IPC Server handler 1 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.3031575
2015-10-18 21:46:46,722 INFO [IPC Server handler 15 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.34743717
2015-10-18 21:46:47,050 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.667
2015-10-18 21:46:47,113 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.667
2015-10-18 21:46:47,191 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.667
2015-10-18 21:46:47,191 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.667
2015-10-18 21:46:48,628 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.56380385
2015-10-18 21:46:49,644 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.667
2015-10-18 21:46:49,660 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.667
2015-10-18 21:46:49,660 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.29311118
2015-10-18 21:46:49,722 INFO [IPC Server handler 15 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.3031575
2015-10-18 21:46:49,785 INFO [IPC Server handler 9 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.34743717
2015-10-18 21:46:50,066 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.667
2015-10-18 21:46:50,144 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.667
2015-10-18 21:46:50,222 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.667
2015-10-18 21:46:50,222 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.667
2015-10-18 21:46:51,644 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.56380385
2015-10-18 21:46:52,660 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.6886936
2015-10-18 21:46:52,675 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.69082516
2015-10-18 21:46:52,832 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.3474145
2015-10-18 21:46:52,863 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.3031575
2015-10-18 21:46:52,910 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.34743717
2015-10-18 21:46:53,097 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.667
2015-10-18 21:46:53,160 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.667
2015-10-18 21:46:53,238 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.667
2015-10-18 21:46:53,238 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.6800879
2015-10-18 21:46:54,660 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.5670609
2015-10-18 21:46:55,691 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.7336628
2015-10-18 21:46:55,707 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.73608947
2015-10-18 21:46:55,941 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.3474145
2015-10-18 21:46:55,957 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.3870214
2015-10-18 21:46:56,019 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.34743717
2015-10-18 21:46:56,113 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.7036165
2015-10-18 21:46:56,176 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.6988248
2015-10-18 21:46:56,254 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.7288778
2015-10-18 21:46:56,254 INFO [IPC Server handler 11 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.7115905
2015-10-18 21:46:57,082 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.5670609
2015-10-18 21:46:57,676 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.667
2015-10-18 21:46:58,707 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.78042823
2015-10-18 21:46:58,722 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.78296405
2015-10-18 21:46:59,051 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.3474145
2015-10-18 21:46:59,082 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.4402952
2015-10-18 21:46:59,129 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.753141
2015-10-18 21:46:59,144 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.34743717
2015-10-18 21:46:59,191 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.7489277
2015-10-18 21:46:59,285 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.7776767
2015-10-18 21:46:59,285 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.76236415
2015-10-18 21:47:00,676 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.667
2015-10-18 21:47:01,738 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.83116484
2015-10-18 21:47:01,738 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.8342221
2015-10-18 21:47:02,145 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.80264467
2015-10-18 21:47:02,160 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.3474145
2015-10-18 21:47:02,207 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.7991525
2015-10-18 21:47:02,238 INFO [IPC Server handler 11 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.4402952
2015-10-18 21:47:02,301 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.82637423
2015-10-18 21:47:02,301 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.8130899
2015-10-18 21:47:02,316 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.4371913
2015-10-18 21:47:03,676 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.667
2015-10-18 21:47:04,754 INFO [IPC Server handler 1 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.88177395
2015-10-18 21:47:04,754 INFO [IPC Server handler 15 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.8850058
2015-10-18 21:47:05,176 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.8522093
2015-10-18 21:47:05,223 INFO [IPC Server handler 11 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.8494191
2015-10-18 21:47:05,317 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.3474145
2015-10-18 21:47:05,317 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.86395144
2015-10-18 21:47:05,317 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.87257373
2015-10-18 21:47:05,379 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.4402952
2015-10-18 21:47:05,410 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.455643
2015-10-18 21:47:06,692 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.667
2015-10-18 21:47:07,785 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.9308045
2015-10-18 21:47:07,785 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.93268
2015-10-18 21:47:08,192 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.8969654
2015-10-18 21:47:08,239 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.89434755
2015-10-18 21:47:08,332 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.91604227
2015-10-18 21:47:08,332 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.90953434
2015-10-18 21:47:08,364 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.36142966
2015-10-18 21:47:08,457 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.4402952
2015-10-18 21:47:08,473 INFO [IPC Server handler 15 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.455643
2015-10-18 21:47:09,707 INFO [IPC Server handler 1 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.6932121
2015-10-18 21:47:10,801 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 0.9797402
2015-10-18 21:47:10,801 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 0.9782633
2015-10-18 21:47:11,207 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.9454113
2015-10-18 21:47:11,270 INFO [IPC Server handler 11 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.9431647
2015-10-18 21:47:11,364 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 0.95986575
2015-10-18 21:47:11,364 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 0.96337664
2015-10-18 21:47:11,457 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.41301748
2015-10-18 21:47:11,551 INFO [IPC Server handler 1 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.4402952
2015-10-18 21:47:11,582 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.455643
2015-10-18 21:47:12,192 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000001_0 is : 1.0
2015-10-18 21:47:12,192 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0005_m_000001_0
2015-10-18 21:47:12,192 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:47:12,192 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0005_01_000003 taskAttempt attempt_1445175094696_0005_m_000001_0
2015-10-18 21:47:12,192 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0005_m_000001_0
2015-10-18 21:47:12,192 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:47:12,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:47:12,223 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0005_m_000001_0
2015-10-18 21:47:12,223 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:47:12,223 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-18 21:47:12,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-18 21:47:12,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:11264, vCores:-17>
2015-10-18 21:47:12,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-18 21:47:12,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-18 21:47:12,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-18 21:47:12,723 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.72805226
2015-10-18 21:47:13,207 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445175094696_0005_m_000008
2015-10-18 21:47:13,207 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 21:47:13,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445175094696_0005_m_000008
2015-10-18 21:47:13,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:47:13,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:47:13,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000008_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 21:47:13,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-18 21:47:13,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0005: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:12288, vCores:-16> knownNMs=4
2015-10-18 21:47:13,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0005_01_000003
2015-10-18 21:47:13,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-18 21:47:13,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0005_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:47:13,707 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000000_0 is : 1.0
2015-10-18 21:47:13,707 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0005_m_000000_0
2015-10-18 21:47:13,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:47:13,707 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0005_01_000002 taskAttempt attempt_1445175094696_0005_m_000000_0
2015-10-18 21:47:13,723 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0005_m_000000_0
2015-10-18 21:47:13,723 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:47:13,739 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:47:13,739 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0005_m_000000_0
2015-10-18 21:47:13,739 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:47:13,739 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-18 21:47:13,832 INFO [IPC Server handler 12 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000005_0 is : 1.0
2015-10-18 21:47:13,832 INFO [IPC Server handler 9 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0005_m_000005_0
2015-10-18 21:47:13,832 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:47:13,832 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0005_01_000007 taskAttempt attempt_1445175094696_0005_m_000005_0
2015-10-18 21:47:13,832 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0005_m_000005_0
2015-10-18 21:47:13,832 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:47:13,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:47:13,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0005_m_000005_0
2015-10-18 21:47:13,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:47:13,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-18 21:47:14,161 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000004_0 is : 1.0
2015-10-18 21:47:14,161 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0005_m_000004_0
2015-10-18 21:47:14,161 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:47:14,161 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0005_01_000006 taskAttempt attempt_1445175094696_0005_m_000004_0
2015-10-18 21:47:14,161 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0005_m_000004_0
2015-10-18 21:47:14,161 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:47:14,176 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:47:14,176 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0005_m_000004_0
2015-10-18 21:47:14,176 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:47:14,192 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-18 21:47:14,254 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 0.9930114
2015-10-18 21:47:14,301 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 0.99182665
2015-10-18 21:47:14,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:4 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-18 21:47:14,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0005_01_000002
2015-10-18 21:47:14,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0005_01_000007
2015-10-18 21:47:14,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0005_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:47:14,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-18 21:47:14,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0005_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:47:14,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-18 21:47:14,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0005_01_000012 to attempt_1445175094696_0005_r_000000_0
2015-10-18 21:47:14,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445175094696_0005_01_000013 to attempt_1445175094696_0005_m_000008_1
2015-10-18 21:47:14,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:47:14,411 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:47:14,411 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:47:14,411 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 21:47:14,411 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000008_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 21:47:14,411 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0005_01_000012 taskAttempt attempt_1445175094696_0005_r_000000_0
2015-10-18 21:47:14,411 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445175094696_0005_01_000013 taskAttempt attempt_1445175094696_0005_m_000008_1
2015-10-18 21:47:14,411 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0005_m_000008_1
2015-10-18 21:47:14,411 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445175094696_0005_r_000000_0
2015-10-18 21:47:14,411 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:47:14,426 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:47:14,442 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0005_m_000008_1 : 13562
2015-10-18 21:47:14,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0005_m_000008_1] using containerId: [container_1445175094696_0005_01_000013 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:47:14,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000008_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:47:14,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0005_m_000008
2015-10-18 21:47:14,442 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445175094696_0005_r_000000_0 : 13562
2015-10-18 21:47:14,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445175094696_0005_r_000000_0] using containerId: [container_1445175094696_0005_01_000012 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:25649]
2015-10-18 21:47:14,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 21:47:14,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445175094696_0005_r_000000
2015-10-18 21:47:14,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 21:47:14,551 INFO [IPC Server handler 1 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.45562187
2015-10-18 21:47:14,661 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.45651457
2015-10-18 21:47:14,708 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.455643
2015-10-18 21:47:15,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445175094696_0005: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:13312, vCores:-15> knownNMs=4
2015-10-18 21:47:15,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0005_01_000006
2015-10-18 21:47:15,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:47:15,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0005_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:47:15,458 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000002_0 is : 1.0
2015-10-18 21:47:15,458 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0005_m_000002_0
2015-10-18 21:47:15,458 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:47:15,473 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0005_01_000004 taskAttempt attempt_1445175094696_0005_m_000002_0
2015-10-18 21:47:15,473 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0005_m_000002_0
2015-10-18 21:47:15,473 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:47:15,489 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:47:15,489 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0005_m_000002_0
2015-10-18 21:47:15,489 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:47:15,489 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-18 21:47:15,708 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000003_0 is : 1.0
2015-10-18 21:47:15,708 INFO [IPC Server handler 12 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0005_m_000003_0
2015-10-18 21:47:15,708 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:47:15,708 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0005_01_000005 taskAttempt attempt_1445175094696_0005_m_000003_0
2015-10-18 21:47:15,708 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0005_m_000003_0
2015-10-18 21:47:15,708 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:47:15,739 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:47:15,739 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0005_m_000003_0
2015-10-18 21:47:15,739 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:47:15,739 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-18 21:47:15,739 INFO [IPC Server handler 9 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.7627918
2015-10-18 21:47:16,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:47:16,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0005_01_000004
2015-10-18 21:47:16,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:47:16,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0005_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:47:17,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0005_01_000005
2015-10-18 21:47:17,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:47:17,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0005_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:47:17,645 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.45562187
2015-10-18 21:47:17,770 INFO [IPC Server handler 5 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.53917074
2015-10-18 21:47:17,833 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.455643
2015-10-18 21:47:17,911 INFO [Socket Reader #1 for port 4236] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0005 (auth:SIMPLE)
2015-10-18 21:47:17,911 INFO [Socket Reader #1 for port 4236] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445175094696_0005 (auth:SIMPLE)
2015-10-18 21:47:17,926 INFO [IPC Server handler 14 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0005_r_000012 asked for a task
2015-10-18 21:47:17,926 INFO [IPC Server handler 14 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0005_r_000012 given task: attempt_1445175094696_0005_r_000000_0
2015-10-18 21:47:17,926 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445175094696_0005_m_000013 asked for a task
2015-10-18 21:47:17,926 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445175094696_0005_m_000013 given task: attempt_1445175094696_0005_m_000008_1
2015-10-18 21:47:18,755 INFO [IPC Server handler 9 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.798442
2015-10-18 21:47:19,286 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 0 maxEvents 10000
2015-10-18 21:47:20,302 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:20,755 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.45562187
2015-10-18 21:47:20,864 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.5773621
2015-10-18 21:47:20,942 INFO [IPC Server handler 14 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.5248024
2015-10-18 21:47:21,302 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:21,770 INFO [IPC Server handler 5 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.8385038
2015-10-18 21:47:22,302 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:23,302 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:23,817 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.45562187
2015-10-18 21:47:23,942 INFO [IPC Server handler 14 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.5773621
2015-10-18 21:47:24,052 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.5638263
2015-10-18 21:47:24,302 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:24,786 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.8805857
2015-10-18 21:47:25,239 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.13333334
2015-10-18 21:47:25,239 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.13102318
2015-10-18 21:47:25,302 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:26,302 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:26,958 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.45562187
2015-10-18 21:47:27,005 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.5773621
2015-10-18 21:47:27,146 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.5638263
2015-10-18 21:47:27,302 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:27,786 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.91478294
2015-10-18 21:47:28,271 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.20000002
2015-10-18 21:47:28,271 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.14158678
2015-10-18 21:47:28,302 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:29,302 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:30,021 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.4622854
2015-10-18 21:47:30,099 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.5773621
2015-10-18 21:47:30,224 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.5638263
2015-10-18 21:47:30,318 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:30,802 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.94981813
2015-10-18 21:47:31,302 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.20000002
2015-10-18 21:47:31,302 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.23921506
2015-10-18 21:47:31,318 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:32,318 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:33,099 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.5317978
2015-10-18 21:47:33,177 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.5773621
2015-10-18 21:47:33,318 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:33,333 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.5638263
2015-10-18 21:47:33,818 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 0.98495317
2015-10-18 21:47:34,318 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:34,333 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.20000002
2015-10-18 21:47:34,333 INFO [IPC Server handler 11 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.23921506
2015-10-18 21:47:35,286 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000007_0 is : 1.0
2015-10-18 21:47:35,286 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0005_m_000007_0
2015-10-18 21:47:35,286 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:47:35,286 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0005_01_000011 taskAttempt attempt_1445175094696_0005_m_000007_0
2015-10-18 21:47:35,286 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0005_m_000007_0
2015-10-18 21:47:35,286 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 21:47:35,302 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:47:35,302 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0005_m_000007_0
2015-10-18 21:47:35,302 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:47:35,302 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-18 21:47:35,318 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 21:47:35,396 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:47:36,240 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.56384325
2015-10-18 21:47:36,287 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.5980934
2015-10-18 21:47:36,318 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:36,396 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0005_01_000011
2015-10-18 21:47:36,396 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:47:36,396 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0005_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:47:36,443 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.5638263
2015-10-18 21:47:37,318 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:37,349 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.20000002
2015-10-18 21:47:37,349 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.33928362
2015-10-18 21:47:38,333 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:38,880 INFO [IPC Server handler 9 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.5980934
2015-10-18 21:47:39,334 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:39,412 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.56384325
2015-10-18 21:47:39,474 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.667
2015-10-18 21:47:39,552 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.61488706
2015-10-18 21:47:40,334 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:40,380 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:47:40,380 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.3474145
2015-10-18 21:47:41,115 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.61488706
2015-10-18 21:47:41,334 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:42,334 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:42,552 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.56384325
2015-10-18 21:47:42,584 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.667
2015-10-18 21:47:42,662 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.667
2015-10-18 21:47:43,334 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:43,396 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.3474145
2015-10-18 21:47:43,412 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:47:44,334 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:45,334 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:45,599 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.56384325
2015-10-18 21:47:45,646 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.667
2015-10-18 21:47:45,709 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.667
2015-10-18 21:47:46,349 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:46,428 INFO [IPC Server handler 11 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.45562187
2015-10-18 21:47:46,443 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:47:47,349 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:48,350 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:48,646 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.56384325
2015-10-18 21:47:48,693 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.667
2015-10-18 21:47:48,756 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.667
2015-10-18 21:47:49,350 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:49,443 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.45562187
2015-10-18 21:47:49,459 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:47:50,350 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:51,350 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:51,725 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.63735896
2015-10-18 21:47:51,740 INFO [IPC Server handler 1 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.667
2015-10-18 21:47:51,803 INFO [IPC Server handler 15 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.667
2015-10-18 21:47:52,350 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:52,475 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.45562187
2015-10-18 21:47:52,490 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:47:53,022 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.63735896
2015-10-18 21:47:53,350 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:54,350 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:54,850 INFO [IPC Server handler 12 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.667
2015-10-18 21:47:54,881 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.683054
2015-10-18 21:47:54,912 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.667
2015-10-18 21:47:55,350 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:55,506 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.56384325
2015-10-18 21:47:55,522 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:47:56,350 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:57,350 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:57,912 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.667
2015-10-18 21:47:57,975 INFO [IPC Server handler 9 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.7098599
2015-10-18 21:47:57,991 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.667
2015-10-18 21:47:58,366 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:47:58,537 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:47:58,553 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.56384325
2015-10-18 21:47:59,366 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:00,366 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:01,131 INFO [IPC Server handler 5 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.667
2015-10-18 21:48:01,131 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.67981833
2015-10-18 21:48:01,131 INFO [IPC Server handler 14 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.7361237
2015-10-18 21:48:01,366 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:01,569 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:48:01,584 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.56384325
2015-10-18 21:48:02,366 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:03,369 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:03,400 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.56384325
2015-10-18 21:48:04,244 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.76547545
2015-10-18 21:48:04,244 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.667
2015-10-18 21:48:04,275 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.70227784
2015-10-18 21:48:04,369 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:04,604 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:48:04,619 INFO [IPC Server handler 1 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.667
2015-10-18 21:48:05,369 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:06,369 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:07,354 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.667
2015-10-18 21:48:07,354 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.7946339
2015-10-18 21:48:07,369 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.7246521
2015-10-18 21:48:07,369 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:07,635 INFO [IPC Server handler 1 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:48:07,635 INFO [IPC Server handler 15 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.667
2015-10-18 21:48:08,369 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:09,369 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:10,369 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:10,588 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.82219946
2015-10-18 21:48:10,619 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.667832
2015-10-18 21:48:10,619 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.74679834
2015-10-18 21:48:10,666 INFO [IPC Server handler 12 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:48:10,666 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.6673236
2015-10-18 21:48:11,385 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:12,385 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:13,385 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:13,698 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.84851265
2015-10-18 21:48:13,713 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:48:13,713 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.6982072
2015-10-18 21:48:13,713 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.6909063
2015-10-18 21:48:13,745 INFO [IPC Server handler 14 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.7676184
2015-10-18 21:48:14,385 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:15,385 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:16,385 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:16,745 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:48:16,745 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.7244872
2015-10-18 21:48:16,776 INFO [IPC Server handler 9 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.8758983
2015-10-18 21:48:16,823 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.71419203
2015-10-18 21:48:16,838 INFO [IPC Server handler 5 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.78984463
2015-10-18 21:48:17,385 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:18,385 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:19,385 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:19,792 INFO [IPC Server handler 14 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:48:19,792 INFO [IPC Server handler 9 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.7631813
2015-10-18 21:48:19,839 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.9039943
2015-10-18 21:48:19,901 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.7364235
2015-10-18 21:48:19,917 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.8122341
2015-10-18 21:48:20,386 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:21,401 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:22,401 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:22,839 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:48:22,839 INFO [IPC Server handler 5 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.8008156
2015-10-18 21:48:22,870 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.9307269
2015-10-18 21:48:22,948 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.7592992
2015-10-18 21:48:22,979 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.8343734
2015-10-18 21:48:23,401 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:24,401 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:25,401 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:25,870 INFO [IPC Server handler 5 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.83245444
2015-10-18 21:48:25,870 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:48:25,933 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.95923996
2015-10-18 21:48:25,995 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.7807694
2015-10-18 21:48:26,026 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.85639864
2015-10-18 21:48:26,401 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:27,401 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:28,401 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:28,901 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:48:28,901 INFO [IPC Server handler 5 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.87850296
2015-10-18 21:48:28,964 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 0.98758745
2015-10-18 21:48:29,042 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.80132514
2015-10-18 21:48:29,105 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.8793137
2015-10-18 21:48:29,401 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:30,402 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:30,558 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000009_0 is : 1.0
2015-10-18 21:48:30,558 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0005_m_000009_0
2015-10-18 21:48:30,558 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:48:30,558 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0005_01_000010 taskAttempt attempt_1445175094696_0005_m_000009_0
2015-10-18 21:48:30,558 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0005_m_000009_0
2015-10-18 21:48:30,558 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 21:48:30,589 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:48:30,589 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0005_m_000009_0
2015-10-18 21:48:30,589 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:48:30,589 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-18 21:48:31,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:48:31,402 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 21:48:31,917 INFO [IPC Server handler 5 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.9214087
2015-10-18 21:48:31,933 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:48:32,089 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.8247377
2015-10-18 21:48:32,167 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.9018351
2015-10-18 21:48:32,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0005_01_000010
2015-10-18 21:48:32,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:48:32,402 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0005_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:48:32,417 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:48:33,417 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:48:34,417 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:48:34,964 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.95435095
2015-10-18 21:48:34,980 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:48:35,167 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.8446382
2015-10-18 21:48:35,214 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.9218166
2015-10-18 21:48:35,417 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:48:36,417 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:48:37,417 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:48:37,996 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 0.98510617
2015-10-18 21:48:38,011 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:48:38,246 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_0 is : 0.86485845
2015-10-18 21:48:38,277 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.94134927
2015-10-18 21:48:38,418 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:48:39,418 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:48:40,027 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000008_1 is : 1.0
2015-10-18 21:48:40,027 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0005_m_000008_1
2015-10-18 21:48:40,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000008_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:48:40,027 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0005_01_000013 taskAttempt attempt_1445175094696_0005_m_000008_1
2015-10-18 21:48:40,027 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0005_m_000008_1
2015-10-18 21:48:40,027 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:48:40,043 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000008_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:48:40,043 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0005_m_000008_1
2015-10-18 21:48:40,043 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445175094696_0005_m_000008_0
2015-10-18 21:48:40,043 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:48:40,043 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-18 21:48:40,043 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000008_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 21:48:40,043 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0005_01_000009 taskAttempt attempt_1445175094696_0005_m_000008_0
2015-10-18 21:48:40,043 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0005_m_000008_0
2015-10-18 21:48:40,043 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 21:48:40,152 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000008_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 21:48:40,152 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 21:48:40,168 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out5/_temporary/1/_temporary/attempt_1445175094696_0005_m_000008_0
2015-10-18 21:48:40,168 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000008_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 21:48:40,293 INFO [Socket Reader #1 for port 4236] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 4236: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 21:48:40,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:48:40,418 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 21:48:41,058 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.23333333
2015-10-18 21:48:41,324 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.9635134
2015-10-18 21:48:41,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0005_01_000009
2015-10-18 21:48:41,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0005_01_000013
2015-10-18 21:48:41,402 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0005_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:48:41,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:48:41,402 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0005_m_000008_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:48:41,433 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:48:42,433 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:48:43,433 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:48:44,090 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.26666668
2015-10-18 21:48:44,371 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 0.9892702
2015-10-18 21:48:44,433 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:48:45,433 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:48:45,683 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_m_000006_0 is : 1.0
2015-10-18 21:48:45,699 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0005_m_000006_0
2015-10-18 21:48:45,699 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:48:45,699 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0005_01_000008 taskAttempt attempt_1445175094696_0005_m_000006_0
2015-10-18 21:48:45,699 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0005_m_000006_0
2015-10-18 21:48:45,699 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 21:48:45,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:48:45,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0005_m_000006_0
2015-10-18 21:48:45,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:48:45,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-18 21:48:46,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:48:46,433 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 21:48:47,137 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.26666668
2015-10-18 21:48:47,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445175094696_0005_01_000008
2015-10-18 21:48:47,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:48:47,402 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445175094696_0005_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 21:48:47,449 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:48:48,449 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:48:49,449 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:48:50,184 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.26666668
2015-10-18 21:48:50,450 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:48:51,450 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:48:52,450 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:48:53,216 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.26666668
2015-10-18 21:48:53,450 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:48:54,450 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:48:55,450 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:48:56,247 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.3
2015-10-18 21:48:56,451 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:48:57,466 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:48:58,468 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:48:59,296 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.3
2015-10-18 21:48:59,468 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:00,468 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:01,468 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:02,328 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.3
2015-10-18 21:49:02,468 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:03,484 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:04,484 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:05,375 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.3
2015-10-18 21:49:05,484 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:06,484 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:07,484 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:08,422 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.3
2015-10-18 21:49:08,484 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:09,500 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:10,500 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:11,453 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.3
2015-10-18 21:49:11,500 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:12,500 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:13,500 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:14,500 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.3
2015-10-18 21:49:14,500 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:15,500 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:16,516 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:17,516 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:17,531 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.3
2015-10-18 21:49:18,520 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:19,520 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:20,520 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:20,583 INFO [IPC Server handler 11 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.3
2015-10-18 21:49:21,521 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:22,521 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:23,521 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:23,614 INFO [IPC Server handler 12 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.3
2015-10-18 21:49:24,536 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:25,536 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445175094696_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 21:49:26,193 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.3
2015-10-18 21:49:26,255 INFO [IPC Server handler 14 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.3
2015-10-18 21:49:26,661 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.66666955
2015-10-18 21:49:29,693 INFO [IPC Server handler 14 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.66867524
2015-10-18 21:49:32,740 INFO [IPC Server handler 9 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.67193353
2015-10-18 21:49:35,771 INFO [IPC Server handler 15 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.67473173
2015-10-18 21:49:38,802 INFO [IPC Server handler 9 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.6779296
2015-10-18 21:49:41,834 INFO [IPC Server handler 15 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.68169945
2015-10-18 21:49:44,865 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.68475336
2015-10-18 21:49:47,912 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.6879531
2015-10-18 21:49:50,959 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.6906621
2015-10-18 21:49:53,991 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.69423044
2015-10-18 21:49:57,022 INFO [IPC Server handler 5 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.69863135
2015-10-18 21:50:00,053 INFO [IPC Server handler 1 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.70160705
2015-10-18 21:50:03,101 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.70615745
2015-10-18 21:50:06,134 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.70941836
2015-10-18 21:50:09,166 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.7130686
2015-10-18 21:50:12,213 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.715835
2015-10-18 21:50:15,244 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.71847886
2015-10-18 21:50:18,275 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.7213111
2015-10-18 21:50:21,307 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.72559905
2015-10-18 21:50:24,355 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.7296599
2015-10-18 21:50:27,386 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.733392
2015-10-18 21:50:30,433 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.7360698
2015-10-18 21:50:33,464 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.7392677
2015-10-18 21:50:36,512 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.7424631
2015-10-18 21:50:39,558 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.7456513
2015-10-18 21:50:42,590 INFO [IPC Server handler 12 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.74993306
2015-10-18 21:50:45,637 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.7541754
2015-10-18 21:50:48,668 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.75688547
2015-10-18 21:50:51,700 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.75914335
2015-10-18 21:50:54,747 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.762785
2015-10-18 21:50:57,778 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.7659819
2015-10-18 21:51:00,825 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.76811993
2015-10-18 21:51:03,856 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.7713863
2015-10-18 21:51:06,903 INFO [IPC Server handler 15 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.77587175
2015-10-18 21:51:09,935 INFO [IPC Server handler 9 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.77982
2015-10-18 21:51:12,966 INFO [IPC Server handler 14 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.7826719
2015-10-18 21:51:16,013 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.7858881
2015-10-18 21:51:19,046 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.7906169
2015-10-18 21:51:22,077 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.79395765
2015-10-18 21:51:25,124 INFO [IPC Server handler 11 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.7976977
2015-10-18 21:51:28,155 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.80243623
2015-10-18 21:51:31,202 INFO [IPC Server handler 5 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8067342
2015-10-18 21:51:34,234 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8110293
2015-10-18 21:51:37,284 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.81473625
2015-10-18 21:51:40,315 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8179587
2015-10-18 21:51:43,346 INFO [IPC Server handler 1 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8219413
2015-10-18 21:51:46,393 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.82542586
2015-10-18 21:51:49,425 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.82893395
2015-10-18 21:51:52,456 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8323735
2015-10-18 21:51:55,488 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8331924
2015-10-18 21:51:58,519 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.83357114
2015-10-18 21:52:01,550 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.83398145
2015-10-18 21:52:04,597 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.83480245
2015-10-18 21:52:07,629 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8357256
2015-10-18 21:52:10,676 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.83654785
2015-10-18 21:52:13,707 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.83751523
2015-10-18 21:52:16,754 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8384044
2015-10-18 21:52:19,801 INFO [IPC Server handler 16 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.84138656
2015-10-18 21:52:22,832 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.84385526
2015-10-18 21:52:25,864 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8461822
2015-10-18 21:52:28,895 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.84848523
2015-10-18 21:52:31,927 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.85069144
2015-10-18 21:52:34,959 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8530507
2015-10-18 21:52:37,990 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.85538566
2015-10-18 21:52:41,021 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8581128
2015-10-18 21:52:44,068 INFO [IPC Server handler 12 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.86036116
2015-10-18 21:52:47,100 INFO [IPC Server handler 27 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8644622
2015-10-18 21:52:50,131 INFO [IPC Server handler 12 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8686715
2015-10-18 21:52:53,163 INFO [IPC Server handler 12 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8733869
2015-10-18 21:52:56,194 INFO [IPC Server handler 20 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8772309
2015-10-18 21:52:59,225 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8819646
2015-10-18 21:53:02,257 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.88601243
2015-10-18 21:53:05,304 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.88973475
2015-10-18 21:53:08,335 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8938303
2015-10-18 21:53:11,366 INFO [IPC Server handler 4 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.8980303
2015-10-18 21:53:14,399 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9014561
2015-10-18 21:53:17,430 INFO [IPC Server handler 9 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9062304
2015-10-18 21:53:20,462 INFO [IPC Server handler 15 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9113662
2015-10-18 21:53:23,493 INFO [IPC Server handler 29 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.91570616
2015-10-18 21:53:26,524 INFO [IPC Server handler 18 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9197887
2015-10-18 21:53:29,556 INFO [IPC Server handler 26 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9239358
2015-10-18 21:53:32,590 INFO [IPC Server handler 28 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.92917943
2015-10-18 21:53:35,638 INFO [IPC Server handler 5 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9318169
2015-10-18 21:53:38,669 INFO [IPC Server handler 5 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9343964
2015-10-18 21:53:41,700 INFO [IPC Server handler 11 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.93691814
2015-10-18 21:53:44,747 INFO [IPC Server handler 3 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.93935144
2015-10-18 21:53:47,779 INFO [IPC Server handler 1 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.941939
2015-10-18 21:53:50,810 INFO [IPC Server handler 24 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.944279
2015-10-18 21:53:53,857 INFO [IPC Server handler 0 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9469053
2015-10-18 21:53:56,904 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9493581
2015-10-18 21:53:59,936 INFO [IPC Server handler 10 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.95216537
2015-10-18 21:54:02,983 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9546969
2015-10-18 21:54:06,014 INFO [IPC Server handler 14 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9568906
2015-10-18 21:54:09,045 INFO [IPC Server handler 6 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.95900303
2015-10-18 21:54:12,092 INFO [IPC Server handler 21 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9619058
2015-10-18 21:54:15,124 INFO [IPC Server handler 23 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.96699214
2015-10-18 21:54:18,155 INFO [IPC Server handler 25 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.97154343
2015-10-18 21:54:21,186 INFO [IPC Server handler 12 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9756357
2015-10-18 21:54:24,218 INFO [IPC Server handler 19 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.98061657
2015-10-18 21:54:27,249 INFO [IPC Server handler 8 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.98564994
2015-10-18 21:54:30,281 INFO [IPC Server handler 7 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9896979
2015-10-18 21:54:33,328 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.9939185
2015-10-18 21:54:36,359 INFO [IPC Server handler 2 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 0.99781775
2015-10-18 21:54:37,875 INFO [IPC Server handler 15 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445175094696_0005_r_000000_0
2015-10-18 21:54:37,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-18 21:54:37,890 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445175094696_0005_r_000000_0 given a go for committing the task output.
2015-10-18 21:54:37,890 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445175094696_0005_r_000000_0
2015-10-18 21:54:37,890 INFO [IPC Server handler 17 on 4236] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445175094696_0005_r_000000_0:true
2015-10-18 21:54:37,921 INFO [IPC Server handler 22 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445175094696_0005_r_000000_0 is : 1.0
2015-10-18 21:54:37,921 INFO [IPC Server handler 13 on 4236] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445175094696_0005_r_000000_0
2015-10-18 21:54:37,921 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 21:54:37,921 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445175094696_0005_01_000012 taskAttempt attempt_1445175094696_0005_r_000000_0
2015-10-18 21:54:37,921 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445175094696_0005_r_000000_0
2015-10-18 21:54:37,921 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:25649
2015-10-18 21:54:37,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445175094696_0005_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 21:54:37,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445175094696_0005_r_000000_0
2015-10-18 21:54:37,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445175094696_0005_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 21:54:37,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-18 21:54:37,953 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0005Job Transitioned from RUNNING to COMMITTING
2015-10-18 21:54:37,953 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-18 21:54:38,015 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-18 21:54:38,015 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445175094696_0005Job Transitioned from COMMITTING to SUCCEEDED
2015-10-18 21:54:38,015 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-18 21:54:38,015 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-18 21:54:38,015 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-18 21:54:38,015 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-18 21:54:38,015 INFO [Thread-106] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-18 21:54:38,015 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-18 21:54:38,015 INFO [Thread-106] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-18 21:54:38,140 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0005/job_1445175094696_0005_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0005-1445175181929-msrabi-word+count-1445176478000-10-1-SUCCEEDED-default-1445175951295.jhist_tmp
2015-10-18 21:54:38,453 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:54:38,515 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0005-1445175181929-msrabi-word+count-1445176478000-10-1-SUCCEEDED-default-1445175951295.jhist_tmp
2015-10-18 21:54:38,515 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0005/job_1445175094696_0005_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0005_conf.xml_tmp
2015-10-18 21:54:38,578 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0005_conf.xml_tmp
2015-10-18 21:54:38,578 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0005.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0005.summary
2015-10-18 21:54:38,593 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0005_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0005_conf.xml
2015-10-18 21:54:38,593 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0005-1445175181929-msrabi-word+count-1445176478000-10-1-SUCCEEDED-default-1445175951295.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445175094696_0005-1445175181929-msrabi-word+count-1445176478000-10-1-SUCCEEDED-default-1445175951295.jhist
2015-10-18 21:54:38,593 INFO [Thread-106] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-18 21:54:38,593 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-18 21:54:38,593 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-39.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445175094696_0005
2015-10-18 21:54:38,609 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-18 21:54:39,609 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-18 21:54:39,609 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445175094696_0005
2015-10-18 21:54:39,609 INFO [Thread-106] org.apache.hadoop.ipc.Server: Stopping server on 4236
2015-10-18 21:54:39,625 INFO [IPC Server listener on 4236] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 4236
2015-10-18 21:54:39,625 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-18 21:54:39,625 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
