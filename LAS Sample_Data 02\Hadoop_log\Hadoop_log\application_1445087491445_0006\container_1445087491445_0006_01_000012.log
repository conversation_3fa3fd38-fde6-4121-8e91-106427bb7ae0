2015-10-17 22:29:53,473 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 22:29:53,551 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 22:29:53,551 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-17 22:29:53,566 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 22:29:53,566 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445087491445_0006, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@7f0eb4b4)
2015-10-17 22:29:53,660 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 22:29:54,191 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445087491445_0006
2015-10-17 22:29:54,426 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 22:29:54,895 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 22:29:54,910 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@6ad3381f
2015-10-17 22:29:54,926 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@4fad9bb2
2015-10-17 22:29:54,942 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=130652568, maxSingleShuffleLimit=32663142, mergeThreshold=86230696, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-17 22:29:54,942 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445087491445_0006_r_000000_0 Thread started: EventFetcher for fetching Map Completion Events
2015-10-17 22:29:54,957 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-17 22:29:54,957 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-17 22:29:54,957 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445087491445_0006_r_000000_0: Got 6 new map-outputs
2015-10-17 22:29:55,051 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445087491445_0006&reduce=0&map=attempt_1445087491445_0006_m_000000_0 sent hash and received reply
2015-10-17 22:29:55,051 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0006_m_000000_0: Shuffling to disk since 216988123 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 22:29:55,067 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445087491445_0006_m_000000_0 decomp: 216988123 len: 216988127 to DISK
2015-10-17 22:29:55,957 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445087491445_0006_r_000000_0: Got 1 new map-outputs
2015-10-17 22:29:56,957 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216988127 bytes from map-output for attempt_1445087491445_0006_m_000000_0
2015-10-17 22:29:56,957 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 2012ms
2015-10-17 22:29:56,957 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 6 to fetcher#5
2015-10-17 22:29:56,957 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 6 of 6 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-17 22:29:56,988 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445087491445_0006&reduce=0&map=attempt_1445087491445_0006_m_000003_0,attempt_1445087491445_0006_m_000007_0,attempt_1445087491445_0006_m_000002_0,attempt_1445087491445_0006_m_000006_0,attempt_1445087491445_0006_m_000004_0,attempt_1445087491445_0006_m_000005_0 sent hash and received reply
2015-10-17 22:29:56,988 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0006_m_000003_0: Shuffling to disk since 216972750 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 22:29:56,988 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445087491445_0006_m_000003_0 decomp: 216972750 len: 216972754 to DISK
2015-10-17 22:29:59,707 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216972754 bytes from map-output for attempt_1445087491445_0006_m_000003_0
2015-10-17 22:29:59,707 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0006_m_000007_0: Shuffling to disk since 216976206 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 22:30:00,020 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#4
2015-10-17 22:30:00,020 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#4
2015-10-17 22:30:00,020 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445087491445_0006_r_000000_0: Got 2 new map-outputs
2015-10-17 22:30:00,035 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445087491445_0006&reduce=0&map=attempt_1445087491445_0006_m_000009_0 sent hash and received reply
2015-10-17 22:30:00,129 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445087491445_0006_m_000007_0 decomp: 216976206 len: 216976210 to DISK
2015-10-17 22:30:00,129 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0006_m_000009_0: Shuffling to disk since 172334804 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 22:30:00,129 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#4 about to shuffle output of map attempt_1445087491445_0006_m_000009_0 decomp: 172334804 len: 172334808 to DISK
2015-10-17 22:30:01,754 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 172334808 bytes from map-output for attempt_1445087491445_0006_m_000009_0
2015-10-17 22:30:01,754 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#4 in 1735ms
2015-10-17 22:30:02,351 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216976210 bytes from map-output for attempt_1445087491445_0006_m_000007_0
2015-10-17 22:30:02,367 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0006_m_000002_0: Shuffling to disk since 216991624 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 22:30:02,367 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445087491445_0006_m_000002_0 decomp: 216991624 len: 216991628 to DISK
2015-10-17 22:30:04,367 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216991628 bytes from map-output for attempt_1445087491445_0006_m_000002_0
2015-10-17 22:30:04,367 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0006_m_000006_0: Shuffling to disk since 217011663 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 22:30:04,382 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445087491445_0006_m_000006_0 decomp: 217011663 len: 217011667 to DISK
2015-10-17 22:30:07,258 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217011667 bytes from map-output for attempt_1445087491445_0006_m_000006_0
2015-10-17 22:30:07,273 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0006_m_000004_0: Shuffling to disk since 216999709 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 22:30:07,273 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445087491445_0006_m_000004_0 decomp: 216999709 len: 216999713 to DISK
2015-10-17 22:30:11,086 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216999713 bytes from map-output for attempt_1445087491445_0006_m_000004_0
2015-10-17 22:30:11,086 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0006_m_000005_0: Shuffling to disk since 216990140 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 22:30:11,086 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445087491445_0006_m_000005_0 decomp: 216990140 len: 216990144 to DISK
2015-10-17 22:30:13,914 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445087491445_0006_r_000000_0: Got 1 new map-outputs
2015-10-17 22:30:13,914 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#4
2015-10-17 22:30:13,914 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#4
2015-10-17 22:30:13,914 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445087491445_0006&reduce=0&map=attempt_1445087491445_0006_m_000001_0 sent hash and received reply
2015-10-17 22:30:13,914 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0006_m_000001_0: Shuffling to disk since 217009502 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 22:30:15,433 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216990144 bytes from map-output for attempt_1445087491445_0006_m_000005_0
2015-10-17 22:30:16,808 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#4 about to shuffle output of map attempt_1445087491445_0006_m_000001_0 decomp: 217009502 len: 217009506 to DISK
2015-10-17 22:30:16,808 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 19842ms
2015-10-17 22:30:16,808 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-17 22:30:16,808 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-17 22:30:16,808 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445087491445_0006&reduce=0&map=attempt_1445087491445_0006_m_000008_0 sent hash and received reply
2015-10-17 22:30:16,808 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0006_m_000008_0: Shuffling to disk since 217015228 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 22:30:16,823 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445087491445_0006_m_000008_0 decomp: 217015228 len: 217015232 to DISK
2015-10-17 22:30:39,199 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217009506 bytes from map-output for attempt_1445087491445_0006_m_000001_0
2015-10-17 22:30:39,215 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#4 in 25305ms
2015-10-17 22:30:50,012 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217015232 bytes from map-output for attempt_1445087491445_0006_m_000008_0
2015-10-17 22:30:50,028 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 33221ms
2015-10-17 22:30:50,028 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: EventFetcher is interrupted.. Returning
2015-10-17 22:30:50,028 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: finalMerge called with 0 in-memory map-outputs and 10 on-disk map-outputs
2015-10-17 22:31:26,576 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 10 files, 2125289789 bytes from disk
2015-10-17 22:31:26,576 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 0 segments, 0 bytes from memory into reduce
2015-10-17 22:31:26,592 INFO [main] org.apache.hadoop.mapred.Merger: Merging 10 sorted segments
2015-10-17 22:31:42,233 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 10 segments left of total size: 2125289689 bytes
2015-10-17 22:31:42,499 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: mapred.skip.on is deprecated. Instead, use mapreduce.job.skiprecords
2015-10-17 22:33:32,150 WARN [ResponseProcessor for block BP-1347369012-**************-1444972147527:blk_1073743032_2244] org.apache.hadoop.hdfs.DFSClient: DFSOutputStream ResponseProcessor exception  for block BP-1347369012-**************-1444972147527:blk_1073743032_2244
java.io.IOException: Bad response ERROR for block BP-1347369012-**************-1444972147527:blk_1073743032_2244 from datanode *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer$ResponseProcessor.run(DFSOutputStream.java:898)
2015-10-17 22:33:32,166 WARN [DataStreamer for file /out/out2/_temporary/1/_temporary/attempt_1445087491445_0006_r_000000_0/part-r-00000 block BP-1347369012-**************-1444972147527:blk_1073743032_2244] org.apache.hadoop.hdfs.DFSClient: Error Recovery for block BP-1347369012-**************-1444972147527:blk_1073743032_2244 in pipeline **************:50010, *************:50010: bad datanode *************:50010
2015-10-17 22:33:32,291 INFO [communication thread] org.apache.hadoop.mapred.Task: Communication exception: java.io.IOException: Failed on local exception: java.io.IOException: An existing connection was forcibly closed by the remote host; Host Details : local host is: "MSRA-SA-39/**************"; destination host is: "minint-fnanli5.fareast.corp.microsoft.com":55219; 
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:772)
	at org.apache.hadoop.ipc.Client.call(Client.java:1472)
	at org.apache.hadoop.ipc.Client.call(Client.java:1399)
	at org.apache.hadoop.ipc.WritableRpcEngine$Invoker.invoke(WritableRpcEngine.java:244)
	at com.sun.proxy.$Proxy8.statusUpdate(Unknown Source)
	at org.apache.hadoop.mapred.Task$TaskReporter.run(Task.java:737)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.net.SocketInputStream$Reader.performIO(SocketInputStream.java:57)
	at org.apache.hadoop.net.SocketIOWithTimeout.doIO(SocketIOWithTimeout.java:142)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:161)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:131)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at org.apache.hadoop.ipc.Client$Connection$PingInputStream.read(Client.java:513)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:246)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:265)
	at java.io.DataInputStream.readInt(DataInputStream.java:387)
	at org.apache.hadoop.ipc.Client$Connection.receiveRpcResponse(Client.java:1071)
	at org.apache.hadoop.ipc.Client$Connection.run(Client.java:966)

2015-10-17 22:33:54,870 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 0 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:34:14,870 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 0 time(s); maxRetries=45
2015-10-17 22:34:34,871 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 1 time(s); maxRetries=45
2015-10-17 22:34:54,872 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 2 time(s); maxRetries=45
2015-10-17 22:35:14,873 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 3 time(s); maxRetries=45
2015-10-17 22:35:34,874 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 4 time(s); maxRetries=45
2015-10-17 22:35:54,874 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 5 time(s); maxRetries=45
2015-10-17 22:35:58,390 INFO [DataStreamer for file /out/out2/_temporary/1/_temporary/attempt_1445087491445_0006_r_000000_0/part-r-00000] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-17 22:35:58,390 INFO [DataStreamer for file /out/out2/_temporary/1/_temporary/attempt_1445087491445_0006_r_000000_0/part-r-00000] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073743047_2263
2015-10-17 22:35:58,406 INFO [DataStreamer for file /out/out2/_temporary/1/_temporary/attempt_1445087491445_0006_r_000000_0/part-r-00000] org.apache.hadoop.hdfs.DFSClient: Excluding datanode *************:50010
2015-10-17 22:36:14,875 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 6 time(s); maxRetries=45
2015-10-17 22:36:34,876 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 7 time(s); maxRetries=45
2015-10-17 22:36:54,877 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 8 time(s); maxRetries=45
2015-10-17 22:37:14,878 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 9 time(s); maxRetries=45
2015-10-17 22:37:34,879 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 10 time(s); maxRetries=45
2015-10-17 22:37:54,879 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 11 time(s); maxRetries=45
2015-10-17 22:38:14,880 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 12 time(s); maxRetries=45
2015-10-17 22:38:34,881 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 13 time(s); maxRetries=45
2015-10-17 22:38:54,882 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 14 time(s); maxRetries=45
2015-10-17 22:39:14,883 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 15 time(s); maxRetries=45
2015-10-17 22:39:34,883 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 16 time(s); maxRetries=45
2015-10-17 22:39:37,634 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445087491445_0006_r_000000_0 is done. And is in the process of committing
2015-10-17 22:39:54,884 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 17 time(s); maxRetries=45
2015-10-17 22:40:14,887 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 18 time(s); maxRetries=45
2015-10-17 22:40:34,887 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 19 time(s); maxRetries=45
2015-10-17 22:40:54,889 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 20 time(s); maxRetries=45
2015-10-17 22:41:14,890 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 21 time(s); maxRetries=45
2015-10-17 22:41:34,892 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 22 time(s); maxRetries=45
2015-10-17 22:41:54,894 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 23 time(s); maxRetries=45
2015-10-17 22:42:14,895 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 24 time(s); maxRetries=45
2015-10-17 22:42:34,896 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 25 time(s); maxRetries=45
2015-10-17 22:42:54,897 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 26 time(s); maxRetries=45
2015-10-17 22:43:14,899 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 27 time(s); maxRetries=45
2015-10-17 22:43:34,902 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 28 time(s); maxRetries=45
2015-10-17 22:43:54,903 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 29 time(s); maxRetries=45
2015-10-17 22:44:14,904 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 30 time(s); maxRetries=45
2015-10-17 22:44:34,904 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:55219. Already tried 31 time(s); maxRetries=45
