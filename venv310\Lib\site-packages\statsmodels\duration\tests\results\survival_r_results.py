import numpy as np

coef_20_1_bre = np.array([-0.9185611])

se_20_1_bre = np.array([0.4706831])

time_20_1_bre = np.array([
    0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 1.1, 1.2, 1.3, 1.4, 1.5])

hazard_20_1_bre = np.array([
    0, 0, 0.04139181, 0.1755379, 0.3121216, 0.3121216, 0.4263121,
    0.6196358, 0.6196358, 0.6196358, 0.909556, 1.31083, 1.31083])

coef_20_1_et_bre = np.array([-0.8907007])

se_20_1_et_bre = np.array([0.4683384])

time_20_1_et_bre = np.array([0])

hazard_20_1_et_bre = np.array([0])

coef_20_1_st_bre = np.array([-0.5766809])

se_20_1_st_bre = np.array([0.4418918])

time_20_1_st_bre = np.array([0])

hazard_20_1_st_bre = np.array([0])

coef_20_1_et_st_bre = np.array([-0.5785683])

se_20_1_et_st_bre = np.array([0.4388437])

time_20_1_et_st_bre = np.array([0])

hazard_20_1_et_st_bre = np.array([0])

coef_20_1_efr = np.array([-0.9975319])

se_20_1_efr = np.array([0.4792421])

time_20_1_efr = np.array([
    0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 1.1, 1.2, 1.3, 1.4, 1.5])

hazard_20_1_efr = np.array([
    0, 0, 0.03934634, 0.1663316, 0.2986427, 0.2986427, 0.4119189,
    0.6077373, 0.6077373, 0.6077373, 0.8933041, 1.285732, 1.285732])

coef_20_1_et_efr = np.array([-0.9679541])

se_20_1_et_efr = np.array([0.4766406])

time_20_1_et_efr = np.array([0])

hazard_20_1_et_efr = np.array([0])

coef_20_1_st_efr = np.array([-0.6345294])

se_20_1_st_efr = np.array([0.4455952])

time_20_1_st_efr = np.array([0])

hazard_20_1_st_efr = np.array([0])

coef_20_1_et_st_efr = np.array([-0.6355622])

se_20_1_et_st_efr = np.array([0.4423104])

time_20_1_et_st_efr = np.array([0])

hazard_20_1_et_st_efr = np.array([0])

coef_50_1_bre = np.array([-0.6761247])

se_50_1_bre = np.array([0.25133])

time_50_1_bre = np.array([
    0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1, 1.2,
    1.5, 1.6, 1.7, 1.8, 1.9, 2.4, 2.8])

hazard_50_1_bre = np.array([
    0, 0.04895521, 0.08457461, 0.2073863, 0.2382473, 0.2793018,
    0.3271622, 0.3842953, 0.3842953, 0.5310807, 0.6360276,
    0.7648251, 0.7648251, 0.9294298, 0.9294298, 0.9294298,
    1.206438, 1.555569, 1.555569])

coef_50_1_et_bre = np.array([-0.6492871])

se_50_1_et_bre = np.array([0.2542493])

time_50_1_et_bre = np.array([0])

hazard_50_1_et_bre = np.array([0])

coef_50_1_st_bre = np.array([-0.7051135])

se_50_1_st_bre = np.array([0.2852093])

time_50_1_st_bre = np.array([0])

hazard_50_1_st_bre = np.array([0])

coef_50_1_et_st_bre = np.array([-0.8672546])

se_50_1_et_st_bre = np.array([0.3443235])

time_50_1_et_st_bre = np.array([0])

hazard_50_1_et_st_bre = np.array([0])

coef_50_1_efr = np.array([-0.7119322])

se_50_1_efr = np.array([0.2533563])

time_50_1_efr = np.array([
    0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1, 1.2,
    1.5, 1.6, 1.7, 1.8, 1.9, 2.4, 2.8])

hazard_50_1_efr = np.array([
    0, 0.04773902, 0.08238731, 0.2022993, 0.2327053, 0.2736316,
    0.3215519, 0.3787123, 0.3787123, 0.526184, 0.6323073,
    0.7627338, 0.7627338, 0.9288858, 0.9288858, 0.9288858,
    1.206835, 1.556054, 1.556054])

coef_50_1_et_efr = np.array([-0.7103063])

se_50_1_et_efr = np.array([0.2598129])

time_50_1_et_efr = np.array([0])

hazard_50_1_et_efr = np.array([0])

coef_50_1_st_efr = np.array([-0.7417904])

se_50_1_st_efr = np.array([0.2846437])

time_50_1_st_efr = np.array([0])

hazard_50_1_st_efr = np.array([0])

coef_50_1_et_st_efr = np.array([-0.9276112])

se_50_1_et_st_efr = np.array([0.3462638])

time_50_1_et_st_efr = np.array([0])

hazard_50_1_et_st_efr = np.array([0])

coef_50_2_bre = np.array([-0.5935189, 0.5035724])

se_50_2_bre = np.array([0.2172841, 0.2399933])

time_50_2_bre = np.array([
    0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1, 1.2,
    1.3, 1.4, 1.5, 1.9, 2.7, 2.9])

hazard_50_2_bre = np.array([
    0.02695812, 0.09162381, 0.1309537, 0.1768423, 0.2033353,
    0.2033353, 0.3083449, 0.3547287, 0.4076453, 0.4761318,
    0.5579718, 0.7610905, 0.918962, 0.918962, 1.136173,
    1.605757, 2.457676, 2.457676])

coef_50_2_et_bre = np.array([-0.4001465, 0.4415933])

se_50_2_et_bre = np.array([0.1992302, 0.2525949])

time_50_2_et_bre = np.array([0])

hazard_50_2_et_bre = np.array([0])

coef_50_2_st_bre = np.array([-0.6574891, 0.4416079])

se_50_2_st_bre = np.array([0.2753398, 0.269458])

time_50_2_st_bre = np.array([0])

hazard_50_2_st_bre = np.array([0])

coef_50_2_et_st_bre = np.array([-0.3607069, 0.2731982])

se_50_2_et_st_bre = np.array([0.255415, 0.306942])

time_50_2_et_st_bre = np.array([0])

hazard_50_2_et_st_bre = np.array([0])

coef_50_2_efr = np.array([-0.6107485, 0.5309737])

se_50_2_efr = np.array([0.2177713, 0.2440535])

time_50_2_efr = np.array([
    0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1, 1.2,
    1.3, 1.4, 1.5, 1.9, 2.7, 2.9])

hazard_50_2_efr = np.array([
    0.02610571, 0.08933637, 0.1279094, 0.1731699, 0.19933,
    0.19933, 0.303598, 0.3497025, 0.4023939, 0.4706978,
    0.5519237, 0.7545023, 0.9129989, 0.9129989, 1.13186,
    1.60574, 2.472615, 2.472615])

coef_50_2_et_efr = np.array([-0.4092002, 0.4871344])

se_50_2_et_efr = np.array([0.1968905, 0.2608527])

time_50_2_et_efr = np.array([0])

hazard_50_2_et_efr = np.array([0])

coef_50_2_st_efr = np.array([-0.6631286, 0.4663285])

se_50_2_st_efr = np.array([0.2748224, 0.273603])

time_50_2_st_efr = np.array([0])

hazard_50_2_st_efr = np.array([0])

coef_50_2_et_st_efr = np.array([-0.3656059, 0.2943912])

se_50_2_et_st_efr = np.array([0.2540752, 0.3124632])

time_50_2_et_st_efr = np.array([0])

hazard_50_2_et_st_efr = np.array([0])

coef_100_5_bre = np.array([
    -0.529776, -0.2916374, -0.1205425, 0.3493476, 0.6034305])

se_100_5_bre = np.array([
    0.1789305, 0.1482505, 0.1347422, 0.1528205, 0.1647927])

time_100_5_bre = np.array([
    0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1, 1.2,
    1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2, 2.1, 2.5, 2.8, 3.2, 3.3])

hazard_100_5_bre = np.array([
    0.02558588, 0.05608812, 0.1087773, 0.1451098, 0.1896703,
    0.2235791, 0.3127521, 0.3355107, 0.439452, 0.504983,
    0.5431706, 0.5841462, 0.5841462, 0.5841462, 0.6916466,
    0.7540191, 0.8298704, 1.027876, 1.170335, 1.379306,
    1.648758, 1.943177, 1.943177, 1.943177, 4.727101])

coef_100_5_et_bre = np.array([
    -0.4000784, -0.1790941, -0.1378969, 0.3288529, 0.533246])

se_100_5_et_bre = np.array([
    0.1745655, 0.1513545, 0.1393968, 0.1487803, 0.1686992])

time_100_5_et_bre = np.array([0])

hazard_100_5_et_bre = np.array([0])

coef_100_5_st_bre = np.array([
    -0.53019, -0.3225739, -0.1241568, 0.3246598, 0.6196859])

se_100_5_st_bre = np.array([
    0.1954581, 0.1602811, 0.1470644, 0.17121, 0.1784115])

time_100_5_st_bre = np.array([0])

hazard_100_5_st_bre = np.array([0])

coef_100_5_et_st_bre = np.array([
    -0.3977171, -0.2166136, -0.1387623, 0.3251726, 0.5664705])

se_100_5_et_st_bre = np.array([
    0.1951054, 0.1707925, 0.1501968, 0.1699932, 0.1843428])

time_100_5_et_st_bre = np.array([0])

hazard_100_5_et_st_bre = np.array([0])

coef_100_5_efr = np.array([
    -0.5641909, -0.3233021, -0.1234858, 0.3712328, 0.6421963])

se_100_5_efr = np.array([
    0.1804027, 0.1496253, 0.1338531, 0.1529832, 0.1670848])

time_100_5_efr = np.array([
    0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1, 1.2,
    1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2, 2.1, 2.5, 2.8, 3.2, 3.3])

hazard_100_5_efr = np.array([
    0.02393412, 0.05276399, 0.1028432, 0.1383859, 0.1823461,
    0.2158107, 0.3037825, 0.3264864, 0.4306648, 0.4964367,
    0.5348595, 0.5760305, 0.5760305, 0.5760305, 0.6842238,
    0.7468135, 0.8228841, 1.023195, 1.166635, 1.379361,
    1.652898, 1.950119, 1.950119, 1.950119, 4.910635])

coef_100_5_et_efr = np.array([
    -0.4338666, -0.2140139, -0.1397387, 0.3535993, 0.5768645])

se_100_5_et_efr = np.array([
    0.1756485, 0.1527244, 0.138298, 0.1488427, 0.1716654])

time_100_5_et_efr = np.array([0])

hazard_100_5_et_efr = np.array([0])

coef_100_5_st_efr = np.array([
    -0.5530876, -0.3331652, -0.128381, 0.3503472, 0.6397813])

se_100_5_st_efr = np.array([
    0.1969338, 0.1614976, 0.1464088, 0.171299, 0.1800787])

time_100_5_st_efr = np.array([0])

hazard_100_5_st_efr = np.array([0])

coef_100_5_et_st_efr = np.array([
    -0.421153, -0.2350069, -0.1433638, 0.3538863, 0.5934568])

se_100_5_et_st_efr = np.array([
    0.1961729, 0.1724719, 0.1492979, 0.170464, 0.1861849])

time_100_5_et_st_efr = np.array([0])

hazard_100_5_et_st_efr = np.array([0])

coef_1000_10_bre = np.array([
    -0.4699279, -0.464557, -0.308411, -0.2158298, -0.09048563,
    0.09359662, 0.112588, 0.3343705, 0.3480601, 0.5634985])

se_1000_10_bre = np.array([
    0.04722914, 0.04785291, 0.04503528, 0.04586872, 0.04429793,
    0.0446141, 0.04139944, 0.04464292, 0.04559903, 0.04864393])

time_1000_10_bre = np.array([
    0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1, 1.2,
    1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2, 2.1, 2.2, 2.3, 2.4,
    2.5, 2.6, 2.7, 2.8, 2.9, 3, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6,
    3.7, 3.8, 3.9, 4, 4.1, 4.2, 4.3, 4.4, 4.6, 4.8, 4.9, 5, 5.1,
    5.2, 5.7, 5.8, 5.9, 6.9])

hazard_1000_10_bre = np.array([
    0.01610374, 0.04853538, 0.08984849, 0.1311329, 0.168397,
    0.2230488, 0.2755388, 0.3312606, 0.3668702, 0.4146558,
    0.477935, 0.5290705, 0.5831775, 0.6503129, 0.7113068,
    0.7830385, 0.8361717, 0.8910061, 0.9615944, 1.024011,
    1.113399, 1.165349, 1.239827, 1.352902, 1.409548, 1.53197,
    1.601843, 1.682158, 1.714907, 1.751564, 1.790898, 1.790898,
    1.83393, 1.83393, 1.936055, 1.992303, 2.050778, 2.118776,
    2.263056, 2.504999, 2.739343, 2.895514, 3.090349, 3.090349,
    3.391772, 3.728142, 4.152769, 4.152769, 4.152769, 4.725957,
    4.725957, 5.69653, 5.69653, 5.69653])

coef_1000_10_et_bre = np.array([
    -0.410889, -0.3929442, -0.2975845, -0.1851533, -0.0918359,
    0.1011997, 0.106735, 0.2899179, 0.3220672, 0.5069589])

se_1000_10_et_bre = np.array([
    0.04696754, 0.04732169, 0.04537707, 0.04605371, 0.04365232,
    0.04450021, 0.04252475, 0.04482007, 0.04562374, 0.04859727])

time_1000_10_et_bre = np.array([0])

hazard_1000_10_et_bre = np.array([0])

coef_1000_10_st_bre = np.array([
    -0.471015, -0.4766859, -0.3070839, -0.2091938, -0.09190845,
    0.0964942, 0.1138269, 0.3307131, 0.3543551, 0.562492])

se_1000_10_st_bre = np.array([
    0.04814778, 0.04841938, 0.04572291, 0.04641227, 0.04502525,
    0.04517603, 0.04203737, 0.04524356, 0.04635037, 0.04920866])

time_1000_10_st_bre = np.array([0])

hazard_1000_10_st_bre = np.array([0])

coef_1000_10_et_st_bre = np.array([
    -0.4165849, -0.4073504, -0.2980959, -0.1765194, -0.09152798,
    0.1013213, 0.1009838, 0.2859668, 0.3247608, 0.5044448])

se_1000_10_et_st_bre = np.array([
    0.04809818, 0.04809499, 0.0460829, 0.04679922, 0.0445294,
    0.04514045, 0.04339298, 0.04580591, 0.04652447, 0.04920744])

time_1000_10_et_st_bre = np.array([0])

hazard_1000_10_et_st_bre = np.array([0])

coef_1000_10_efr = np.array([
    -0.4894399, -0.4839746, -0.3227769, -0.2261293, -0.09318482,
    0.09767154, 0.1173205, 0.3493732, 0.3640146, 0.5879749])

se_1000_10_efr = np.array([
    0.0474181, 0.04811855, 0.04507655, 0.04603044, 0.04440409,
    0.04478202, 0.04136728, 0.04473343, 0.045768, 0.04891375])

time_1000_10_efr = np.array([
    0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1, 1.2,
    1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2, 2.1, 2.2, 2.3, 2.4,
    2.5, 2.6, 2.7, 2.8, 2.9, 3, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6,
    3.7, 3.8, 3.9, 4, 4.1, 4.2, 4.3, 4.4, 4.6, 4.8, 4.9, 5, 5.1,
    5.2, 5.7, 5.8, 5.9, 6.9])

hazard_1000_10_efr = np.array([
    0.01549698, 0.04680035, 0.08682564, 0.1269429, 0.1632388,
    0.2167291, 0.2682311, 0.3231316, 0.3582936, 0.4054892, 0.4681098,
    0.5188697, 0.5727059, 0.639571, 0.7003012, 0.7718979, 0.825053,
    0.880063, 0.950935, 1.013828, 1.103903, 1.156314, 1.231707,
    1.346235, 1.40359, 1.527475, 1.598231, 1.6795, 1.712779,
    1.750227, 1.790455, 1.790455, 1.834455, 1.834455, 1.938997,
    1.996804, 2.056859, 2.126816, 2.275217, 2.524027, 2.76669,
    2.929268, 3.13247, 3.13247, 3.448515, 3.80143, 4.249649,
    4.249649, 4.249649, 4.851365, 4.851365, 5.877307, 5.877307, 5.877307])

coef_1000_10_et_efr = np.array([
    -0.4373066, -0.4131901, -0.3177637, -0.1978493, -0.09679451,
    0.1092037, 0.1136069, 0.3088907, 0.3442007, 0.5394121])

se_1000_10_et_efr = np.array([
    0.04716041, 0.04755342, 0.04546713, 0.04627802, 0.04376583,
    0.04474868, 0.04259991, 0.04491564, 0.04589027, 0.04890847])

time_1000_10_et_efr = np.array([0])

hazard_1000_10_et_efr = np.array([0])

coef_1000_10_st_efr = np.array([
    -0.4911117, -0.4960756, -0.3226152, -0.220949, -0.09478141,
    0.1015735, 0.1195524, 0.3446977, 0.3695904, 0.5878576])

se_1000_10_st_efr = np.array([
    0.04833676, 0.04868554, 0.04578407, 0.04661755, 0.04518267,
    0.04537135, 0.04202183, 0.04531266, 0.0464931, 0.04949831])

time_1000_10_st_efr = np.array([0])

hazard_1000_10_st_efr = np.array([0])

coef_1000_10_et_st_efr = np.array([
    -0.444355, -0.4283278, -0.3198815, -0.1901781, -0.09727039,
    0.1106191, 0.1092104, 0.3034778, 0.3451699, 0.5382381])

se_1000_10_et_st_efr = np.array([
    0.04830664, 0.04833619, 0.04617371, 0.04706401, 0.04472699,
    0.0454208, 0.04350539, 0.04588588, 0.04675675, 0.04950987])

time_1000_10_et_st_efr = np.array([0])

hazard_1000_10_et_st_efr = np.array([0])
