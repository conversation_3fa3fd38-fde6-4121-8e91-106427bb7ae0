pip install Lasio

import os
import numpy as np
import lasio as las
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
 
# defining a function that extract LAS files from a folder

def load_las_files(folder_path):
    wells = []
    for filename in os.listdir(folder_path):
        if filename.endswith(".las"):
            las_path = os.path.join(folder_path, filename)
            well = las.read(las_path)
            wells.append(well)
    return wells
folder_path = '/kaggle/input/logging-data'

# Call the function with the folder path
wells = load_las_files(folder_path)

print(f"Loaded {len(wells)} LAS files.")
Loaded 12 LAS files.
for well in wells:
    print(well.keys())


data = []
for well in wells:
    df = well.df()  # Convert LAS data to a DataFrame

    # Ensure DEPT is not used as the index
    if df.index.name == 'DEPT':
        df = df.reset_index()

    # Ensure consistent data type for DEPT
    df['DEPT'] = df['DEPT'].astype(float)

    data.append(df)

# Concatenate all DataFrames
data = pd.concat(data, axis=0, ignore_index=True)

print(data.info())
print(data.head())
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 17398 entries, 0 to 17397
Data columns (total 6 columns):
 #   Column       Non-Null Count  Dtype  
---  ------       --------------  -----  
 0   DEPT         17398 non-null  float64
 1   GAMMA        17342 non-null  float64
 2   PERM         17342 non-null  float64
 3   POROSITY     17335 non-null  float64
 4   RESISTIVITY  17329 non-null  float64
 5   LITH         17333 non-null  float64
dtypes: float64(6)
memory usage: 815.7 KB
None
        DEPT      GAMMA      PERM  POROSITY  RESISTIVITY  LITH
0  1805.4539        NaN       NaN       NaN          NaN   NaN
1  1805.9539        NaN       NaN       NaN          NaN   NaN
2  1806.4539  77.788834  0.000755  0.002280     0.370593   3.0
3  1806.9539  76.094276  0.000974  0.002282     0.615444   3.0
4  1807.4539  77.303818  0.000868  0.002270     0.494150   3.0
/usr/local/lib/python3.10/dist-packages/pandas/io/formats/format.py:1458: RuntimeWarning: invalid value encountered in greater
  has_large_values = (abs_vals > 1e6).any()
/usr/local/lib/python3.10/dist-packages/pandas/io/formats/format.py:1459: RuntimeWarning: invalid value encountered in less
  has_small_values = ((abs_vals < 10 ** (-self.digits)) & (abs_vals > 0)).any()
/usr/local/lib/python3.10/dist-packages/pandas/io/formats/format.py:1459: RuntimeWarning: invalid value encountered in greater
  has_small_values = ((abs_vals < 10 ** (-self.digits)) & (abs_vals > 0)).any()
data.isnull().sum()
DEPT            0
GAMMA          56
PERM           56
POROSITY       63
RESISTIVITY    69
LITH           65
dtype: int64
# remove the nulls
data.dropna(inplace=True)

# making LITH as a int
data['LITH']=data['LITH'].astype(int)
data
DEPT	GAMMA	PERM	POROSITY	RESISTIVITY	LITH
2	1806.4539	77.788834	0.000755	0.002280	0.370593	3
3	1806.9539	76.094276	0.000974	0.002282	0.615444	3
4	1807.4539	77.303818	0.000868	0.002270	0.494150	3
5	1807.9539	78.808640	0.000729	0.002337	0.326674	3
6	1808.4539	75.472939	0.000661	0.002493	0.233171	3
...	...	...	...	...	...	...
17390	2430.2308	84.685150	0.003023	0.001227	23.206961	1
17391	2430.7308	81.467796	0.004139	0.001256	41.304214	1
17392	2431.2308	79.031670	0.004823	0.001276	54.126698	1
17393	2431.7308	77.506615	0.005004	0.001283	57.577251	1
17394	2432.2308	77.704376	0.004635	0.001271	50.467602	1
17329 rows × 6 columns

#  Gamma Ray vs. Porosity

plt.figure(figsize=(10, 6))
sns.scatterplot(x='GAMMA', y='POROSITY', hue='LITH', data=data)
plt.title('Gamma Ray vs. Porosity')
plt.xlabel('Gamma Ray (API)')
plt.ylabel('Porosity')
plt.show()

# Porosity vs Permeability

plt.figure(figsize=(10, 6))
sns.scatterplot(x='POROSITY', y='PERM', hue='LITH', data=data)
plt.title('Porosity vs Permeability')
plt.xlabel('Porosity')
plt.ylabel('Permeability')
plt.show()

# getting LITH count to determine if there is Imbalance in the data
lith_counts = data['LITH'].value_counts()
lith_counts
LITH
1    7710
2    5734
3    2849
0    1036
Name: count, dtype: int64
# plotting it
plt.figure(figsize=(10, 6))
sns.barplot(x=lith_counts.index, y=lith_counts.values,palette='colorblind')
plt.title('LITH Count')
plt.xlabel('LITH')
plt.ylabel('Count')
plt.show()

# Separate features and target
X = data.drop(columns=['LITH'])  # Features
y = data['LITH']  # Target
print(X) # Checking for data leakage
            DEPT      GAMMA      PERM  POROSITY  RESISTIVITY
2      1806.4539  77.788834  0.000755  0.002280     0.370593
3      1806.9539  76.094276  0.000974  0.002282     0.615444
4      1807.4539  77.303818  0.000868  0.002270     0.494150
5      1807.9539  78.808640  0.000729  0.002337     0.326674
6      1808.4539  75.472939  0.000661  0.002493     0.233171
...          ...        ...       ...       ...          ...
17390  2430.2308  84.685150  0.003023  0.001227    23.206961
17391  2430.7308  81.467796  0.004139  0.001256    41.304214
17392  2431.2308  79.031670  0.004823  0.001276    54.126698
17393  2431.7308  77.506615  0.005004  0.001283    57.577251
17394  2432.2308  77.704376  0.004635  0.001271    50.467602

[17329 rows x 5 columns]
# Split the data into training and testing sets (e.g., 80% training, 20% testing)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
# Feature Scaling
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)
rf_model=RandomForestClassifier(random_state=42)
rf_model.fit(X_train_scaled, y_train)

RandomForestClassifier
RandomForestClassifier(random_state=42)
y_pred = rf_model.predict(X_test_scaled)
class_report = classification_report(y_test, y_pred)
print("Classification Report:")
print(class_report)
Classification Report:
              precision    recall  f1-score   support

           0       0.99      1.00      1.00       199
           1       1.00      1.00      1.00      1501
           2       1.00      1.00      1.00      1173
           3       1.00      1.00      1.00       593

    accuracy                           1.00      3466
   macro avg       1.00      1.00      1.00      3466
weighted avg       1.00      1.00      1.00      3466

#confustion matrix

sns.heatmap(confusion_matrix(y_test, y_pred), annot=True, fmt='d', cmap='Blues')
plt.title('SVM Confusion Matrix')
plt.xlabel('Predicted')
plt.ylabel('True')
plt.show()

Observations:
the model acheived 100% accuracy on the test set
This is highly unusual for real-world datasets and suggests there might be an issue with the data or the model's evaluation process.
Let's try using an SVM (Support Vector Machine) to see if the issue persists. If the SVM also achieves 100% accuracy, it strongly indicates a problem with the dataset.
# Split the data into training and testing sets (e.g., 80% training, 20% testing)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
# Feature Scaling
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)
# Train SVM model
svm_model = SVC(kernel='rbf', random_state=42)
svm_model.fit(X_train_scaled, y_train)

SVC
SVC(random_state=42)
# Evaluate SVM model
y_pred_svm = svm_model.predict(X_test_scaled)
print("SVM Classification Report:\n", classification_report(y_test, y_pred_svm))
print("SVM Confusion Matrix:\n", confusion_matrix(y_test, y_pred_svm))
SVM Classification Report:
               precision    recall  f1-score   support

           0       0.98      0.92      0.95       199
           1       0.98      1.00      0.99      1501
           2       0.98      0.99      0.98      1173
           3       0.98      0.96      0.97       593

    accuracy                           0.98      3466
   macro avg       0.98      0.96      0.97      3466
weighted avg       0.98      0.98      0.98      3466

SVM Confusion Matrix:
 [[ 183   16    0    0]
 [   3 1495    3    0]
 [   0    8 1156    9]
 [   0    0   26  567]]
#confusion matrix
sns.heatmap(confusion_matrix(y_test, y_pred_svm), annot=True, fmt='d', cmap='Blues')
plt.title('SVM Confusion Matrix')
plt.xlabel('Predicted')
plt.ylabel('True')
plt.show()

Observations:
Random Forest model achieving 100% accuracy while SVM achieves around 98% strongly suggests one or more of the following issues: ##### 1- Overfitting in Random Forest ##### 2- Feature Dominance ##### 3- Data Leakage ##### 4- Dataset Might Be Too Easy
First let's check feature importance and correlation to determine which feature is dominent.
# Feature importance
importances = rf_model.feature_importances_
feature_names = X.columns
feature_importance_df = pd.DataFrame({'Feature': feature_names, 'Importance': importances})
print(feature_importance_df.sort_values(by='Importance', ascending=False))

# plotting it for better visual

sns.barplot(x='Importance', y='Feature', data=feature_importance_df.sort_values(by='Importance', ascending=False))
plt.title('Feature Importance')
plt.show()
       Feature  Importance
3     POROSITY    0.767856
0         DEPT    0.100463
2         PERM    0.051924
1        GAMMA    0.045560
4  RESISTIVITY    0.034197

# data correlation
corr_matrix = data.corr()
plt.figure(figsize=(10, 6))
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', fmt=".2f")
plt.title('Correlation Matrix')
plt.show()

Observaion
It seem evident that the Porosity feature is dominant
and there's high correlation between porosity and the target (Lithology)
Let's try ommitting the porosity and check model performance.
X_reduced = X.drop(columns=['POROSITY'])

X_train_reduced, X_test_reduced, y_train, y_test = train_test_split(X_reduced, y, test_size=0.2, random_state=42, shuffle=True)
 # made sure the data is shuffled to avoid data memorization


# Feature Scaling
scaler = StandardScaler()
X_train_scaled1 = scaler.fit_transform(X_train_reduced)
X_test_scaled1 = scaler.transform(X_test_reduced)


SVM_model_reduced = SVC(kernel='rbf',random_state=42, class_weight='balanced')
SVM_model_reduced.fit(X_train_scaled1, y_train)

SVM_y_pred_reduced = SVM_model_reduced.predict(X_test_scaled1)

print("Reduced Feature RF Classification Report:\n", classification_report(y_test, SVM_y_pred_reduced))
Reduced Feature RF Classification Report:
               precision    recall  f1-score   support

           0       0.39      0.94      0.56       199
           1       0.87      0.80      0.83      1501
           2       0.82      0.52      0.63      1173
           3       0.58      0.84      0.69       593

    accuracy                           0.72      3466
   macro avg       0.67      0.78      0.68      3466
weighted avg       0.77      0.72      0.73      3466

#confusion matrix
sns.heatmap(confusion_matrix(y_test, SVM_y_pred_reduced), annot=True, fmt='d', cmap='Blues')
plt.title('SVM Confusion Matrix')
plt.xlabel('Predicted')
plt.ylabel('True')
plt.show()

Observations:
As it seems the model acheived good results on class 1 and 2, this may due to the inbalance mentioned earlier that was not addressed, or due to features not scaled.
As shown above the model had improved.
BUT It's facing some issues with Class 0 persicion and Class 2 recall.
that's indicating that omitting Porosity had a negative impact on the model.
Conclusion
- as shown in the following figure:porosity vs permeability ML logging.png
there's a natural seperation between data points, making it easy for the Random Forest machine learning algorithm to learn such easy pattern.
So, we conclude that eventhough, there is a great correlation between the Lithology and the Porosity, model overfitting is premirly beacuse of : (4- Dataset Might Be Too Easy)