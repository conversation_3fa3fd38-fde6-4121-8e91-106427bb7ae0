"""
Test script for the Offline Semantic Search Application
"""

import time
import json
from semantic_search_engine import SemanticSearchEngine
from data_generator import create_sample_dataset
import os

def test_basic_functionality():
    """Test basic search functionality"""
    print("🧪 Testing Basic Functionality")
    print("=" * 50)
    
    # Create sample data if it doesn't exist
    if not os.path.exists("data/technical_articles.json"):
        print("📝 Creating sample dataset...")
        create_sample_dataset()
    
    # Initialize search engine
    print("🔍 Initializing search engine...")
    engine = SemanticSearchEngine()
    
    # Load components
    print("📚 Loading articles...")
    articles = engine.load_articles()
    print(f"✅ Loaded {len(articles)} articles")
    
    print("🤖 Loading SBERT model...")
    engine.load_model()
    print("✅ Model loaded successfully")
    
    print("🔢 Generating embeddings...")
    embeddings = engine.generate_embeddings()
    print(f"✅ Generated embeddings with shape: {embeddings.shape}")
    
    # Test FAISS index building
    try:
        print("⚡ Building FAISS index...")
        engine.build_faiss_index()
        print("✅ FAISS index built successfully")
    except Exception as e:
        print(f"⚠️  FAISS not available: {e}")
    
    return engine

def test_search_queries(engine):
    """Test various search queries"""
    print("\n🔍 Testing Search Queries")
    print("=" * 50)
    
    test_queries = [
        "drilling fluid optimization",
        "reservoir characterization",
        "corrosion prevention offshore",
        "seismic interpretation",
        "enhanced oil recovery",
        "wellbore stability"
    ]
    
    for query in test_queries:
        print(f"\n🔎 Query: '{query}'")
        start_time = time.time()
        results = engine.search(query, top_k=3)
        search_time = time.time() - start_time
        
        print(f"⏱️  Search time: {search_time:.3f} seconds")
        print(f"📊 Results found: {len(results)}")
        
        for i, result in enumerate(results[:2], 1):  # Show top 2 results
            print(f"  {i}. {result['title']} (Score: {result['similarity_score']:.3f})")

def test_performance(engine):
    """Test search performance"""
    print("\n⚡ Testing Performance")
    print("=" * 50)
    
    query = "drilling fluid optimization high temperature"
    num_tests = 10
    
    print(f"🔄 Running {num_tests} searches for: '{query}'")
    
    times = []
    for i in range(num_tests):
        start_time = time.time()
        results = engine.search(query, top_k=5)
        search_time = time.time() - start_time
        times.append(search_time)
        
        if i == 0:  # Show results for first search
            print(f"📊 Found {len(results)} results")
    
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    
    print(f"📈 Performance Results:")
    print(f"  Average time: {avg_time:.3f} seconds")
    print(f"  Min time: {min_time:.3f} seconds")
    print(f"  Max time: {max_time:.3f} seconds")

def test_edge_cases(engine):
    """Test edge cases and error handling"""
    print("\n🧪 Testing Edge Cases")
    print("=" * 50)
    
    edge_cases = [
        "",  # Empty query
        "xyz123nonexistentterm",  # Non-existent term
        "a",  # Single character
        "the and or but",  # Common words
        "drilling" * 50,  # Very long query
    ]
    
    for query in edge_cases:
        print(f"\n🔎 Edge case: '{query[:50]}{'...' if len(query) > 50 else ''}'")
        try:
            results = engine.search(query, top_k=3)
            print(f"✅ Results: {len(results)}")
        except Exception as e:
            print(f"❌ Error: {e}")

def display_statistics(engine):
    """Display search engine statistics"""
    print("\n📊 Search Engine Statistics")
    print("=" * 50)
    
    stats = engine.get_statistics()
    
    print(f"📚 Total Articles: {stats['total_articles']}")
    print(f"📂 Total Categories: {stats['total_categories']}")
    print(f"🔢 Embedding Dimensions: {stats['embedding_dimensions']}")
    print(f"🤖 Model: {stats['model_name']}")
    print(f"📋 Categories: {', '.join(stats['categories'])}")

def main():
    """Main test function"""
    print("🔍 ONGC Knowledge Management System - Test Suite")
    print("=" * 60)
    
    try:
        # Test basic functionality
        engine = test_basic_functionality()
        
        # Test search queries
        test_search_queries(engine)
        
        # Test performance
        test_performance(engine)
        
        # Test edge cases
        test_edge_cases(engine)
        
        # Display statistics
        display_statistics(engine)
        
        print("\n🎉 All tests completed successfully!")
        print("\nNext steps:")
        print("1. Run the Streamlit app: streamlit run streamlit_app.py")
        print("2. Or use the CLI: python cli_app.py search --interactive")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
