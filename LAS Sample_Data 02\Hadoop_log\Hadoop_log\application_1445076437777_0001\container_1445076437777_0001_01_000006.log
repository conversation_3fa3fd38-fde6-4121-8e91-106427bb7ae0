2015-10-17 18:09:38,365 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 18:09:38,552 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 18:09:38,552 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 18:09:38,584 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 18:09:38,584 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445076437777_0001, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@490ef5a5)
2015-10-17 18:09:39,052 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 18:09:40,677 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445076437777_0001
2015-10-17 18:09:42,177 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 18:09:43,724 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 18:09:43,849 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@5bff8187
2015-10-17 18:09:46,224 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:536870912+134217728
2015-10-17 18:09:46,396 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 18:09:46,396 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 18:09:46,396 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 18:09:46,396 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 18:09:46,396 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 18:09:46,411 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 18:10:11,614 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:10:11,614 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48250246; bufvoid = 104857600
2015-10-17 18:10:11,614 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17305444(69221776); length = 8908953/6553600
2015-10-17 18:10:11,614 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57318998 kvi 14329744(57318976)
2015-10-17 18:10:42,316 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 18:10:42,316 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57318998 kv 14329744(57318976) kvi 12130124(48520496)
2015-10-17 18:10:49,378 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:10:49,378 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57318998; bufend = 707922; bufvoid = 104857599
2015-10-17 18:10:49,378 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14329744(57318976); kvend = 5419856(21679424); length = 8909889/6553600
2015-10-17 18:10:49,378 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9776658 kvi 2444160(9776640)
2015-10-17 18:11:20,487 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 18:11:20,534 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9776658 kv 2444160(9776640) kvi 247856(991424)
2015-10-17 18:11:27,799 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:11:27,799 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9776658; bufend = 57994455; bufvoid = 104857600
2015-10-17 18:11:27,799 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2444160(9776640); kvend = 19741496(78965984); length = 8917065/6553600
2015-10-17 18:11:27,799 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67063207 kvi 16765796(67063184)
2015-10-17 18:11:47,471 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 18:11:47,486 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67063207 kv 16765796(67063184) kvi 14570840(58283360)
2015-10-17 18:12:15,282 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:12:15,282 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67063207; bufend = 10480387; bufvoid = 104857600
2015-10-17 18:12:15,282 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16765796(67063184); kvend = 7862980(31451920); length = 8902817/6553600
2015-10-17 18:12:15,282 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19549139 kvi 4887280(19549120)
2015-10-17 18:12:42,032 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 18:12:42,079 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 19549139 kv 4887280(19549120) kvi 2679652(10718608)
2015-10-17 18:13:05,218 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:13:05,218 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 19549139; bufend = 67751785; bufvoid = 104857600
2015-10-17 18:13:05,218 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 4887280(19549120); kvend = 22180828(88723312); length = 8920853/6553600
2015-10-17 18:13:05,218 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 76820537 kvi 19205128(76820512)
2015-10-17 18:13:21,937 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 18:13:21,937 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 76820537 kv 19205128(76820512) kvi 16995388(67981552)
2015-10-17 18:13:35,640 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:13:35,640 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 76820537; bufend = 20214918; bufvoid = 104857600
2015-10-17 18:13:35,640 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 19205128(76820512); kvend = 10296608(41186432); length = 8908521/6553600
2015-10-17 18:13:35,640 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29283670 kvi 7320912(29283648)
2015-10-17 18:14:04,545 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-17 18:14:04,561 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29283670 kv 7320912(29283648) kvi 5125060(20500240)
2015-10-17 18:14:14,451 INFO [communication thread] org.apache.hadoop.mapred.Task: Communication exception: java.io.IOException: Failed on local exception: java.io.IOException: An existing connection was forcibly closed by the remote host; Host Details : local host is: "MININT-75DGDAM1/************"; destination host is: "minint-fnanli5.fareast.corp.microsoft.com":52839; 
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:772)
	at org.apache.hadoop.ipc.Client.call(Client.java:1472)
	at org.apache.hadoop.ipc.Client.call(Client.java:1399)
	at org.apache.hadoop.ipc.WritableRpcEngine$Invoker.invoke(WritableRpcEngine.java:244)
	at com.sun.proxy.$Proxy9.statusUpdate(Unknown Source)
	at org.apache.hadoop.mapred.Task$TaskReporter.run(Task.java:737)
	at java.lang.Thread.run(Thread.java:724)
Caused by: java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.net.SocketInputStream$Reader.performIO(SocketInputStream.java:57)
	at org.apache.hadoop.net.SocketIOWithTimeout.doIO(SocketIOWithTimeout.java:142)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:161)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:131)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at org.apache.hadoop.ipc.Client$Connection$PingInputStream.read(Client.java:513)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:235)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:254)
	at java.io.DataInputStream.readInt(DataInputStream.java:387)
	at org.apache.hadoop.ipc.Client$Connection.receiveRpcResponse(Client.java:1071)
	at org.apache.hadoop.ipc.Client$Connection.run(Client.java:966)

2015-10-17 18:14:26,077 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:14:26,077 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29283670; bufend = 77538589; bufvoid = 104857600
2015-10-17 18:14:26,077 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7320912(29283648); kvend = 24627528(98510112); length = 8907785/6553600
2015-10-17 18:14:26,077 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 86607341 kvi 21651828(86607312)
2015-10-17 18:14:37,499 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 0 time(s); maxRetries=45
2015-10-17 18:14:53,546 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-17 18:14:53,561 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 86607341 kv 21651828(86607312) kvi 19456628(77826512)
2015-10-17 18:14:57,499 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 1 time(s); maxRetries=45
2015-10-17 18:15:17,499 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 2 time(s); maxRetries=45
2015-10-17 18:15:37,499 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 3 time(s); maxRetries=45
2015-10-17 18:15:57,499 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 4 time(s); maxRetries=45
2015-10-17 18:16:17,500 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 5 time(s); maxRetries=45
2015-10-17 18:16:37,500 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 6 time(s); maxRetries=45
2015-10-17 18:16:57,500 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 7 time(s); maxRetries=45
2015-10-17 18:17:17,500 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 8 time(s); maxRetries=45
2015-10-17 18:17:37,501 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 9 time(s); maxRetries=45
2015-10-17 18:17:57,501 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 10 time(s); maxRetries=45
2015-10-17 18:18:17,501 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 11 time(s); maxRetries=45
2015-10-17 18:18:37,501 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 12 time(s); maxRetries=45
2015-10-17 18:18:57,502 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 13 time(s); maxRetries=45
2015-10-17 18:19:17,502 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 14 time(s); maxRetries=45
2015-10-17 18:19:37,503 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 15 time(s); maxRetries=45
2015-10-17 18:19:57,503 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 16 time(s); maxRetries=45
2015-10-17 18:20:17,504 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 17 time(s); maxRetries=45
2015-10-17 18:20:37,504 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 18 time(s); maxRetries=45
2015-10-17 18:20:57,505 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 19 time(s); maxRetries=45
2015-10-17 18:21:17,506 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 20 time(s); maxRetries=45
2015-10-17 18:21:37,506 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 21 time(s); maxRetries=45
2015-10-17 18:21:57,507 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 22 time(s); maxRetries=45
2015-10-17 18:22:17,508 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 23 time(s); maxRetries=45
2015-10-17 18:22:37,508 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 24 time(s); maxRetries=45
2015-10-17 18:22:57,509 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 25 time(s); maxRetries=45
2015-10-17 18:23:17,509 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 26 time(s); maxRetries=45
2015-10-17 18:23:37,510 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 27 time(s); maxRetries=45
2015-10-17 18:23:57,511 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 28 time(s); maxRetries=45
2015-10-17 18:24:17,511 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 29 time(s); maxRetries=45
2015-10-17 18:24:37,512 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 30 time(s); maxRetries=45
2015-10-17 18:24:57,512 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 31 time(s); maxRetries=45
2015-10-17 18:25:17,513 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 32 time(s); maxRetries=45
2015-10-17 18:25:37,514 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 33 time(s); maxRetries=45
2015-10-17 18:25:57,514 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 34 time(s); maxRetries=45
2015-10-17 18:26:17,514 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 35 time(s); maxRetries=45
2015-10-17 18:26:37,514 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 36 time(s); maxRetries=45
2015-10-17 18:26:57,529 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 37 time(s); maxRetries=45
2015-10-17 18:27:17,529 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-fnanli5.fareast.corp.microsoft.com/*************:52839. Already tried 38 time(s); maxRetries=45
