2015-10-18 18:01:49,319 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445144423722_0022_000001
2015-10-18 18:01:49,776 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-18 18:01:49,777 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 22 cluster_timestamp: 1445144423722 } attemptId: 1 } keyId: -127633188)
2015-10-18 18:01:50,118 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-18 18:01:50,777 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-18 18:01:50,827 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-18 18:01:50,855 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-18 18:01:50,856 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-18 18:01:50,857 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-18 18:01:50,858 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-18 18:01:50,859 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-18 18:01:50,865 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-18 18:01:50,866 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-18 18:01:50,867 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-18 18:01:50,907 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:01:50,929 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:01:50,951 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:01:50,960 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-18 18:01:51,007 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-18 18:01:51,240 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 18:01:51,300 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 18:01:51,300 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-18 18:01:51,307 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445144423722_0022 to jobTokenSecretManager
2015-10-18 18:01:51,452 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445144423722_0022 because: not enabled; too many maps; too much input;
2015-10-18 18:01:51,470 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445144423722_0022 = 1256521728. Number of splits = 10
2015-10-18 18:01:51,471 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445144423722_0022 = 1
2015-10-18 18:01:51,471 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0022Job Transitioned from NEW to INITED
2015-10-18 18:01:51,472 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445144423722_0022.
2015-10-18 18:01:51,504 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-18 18:01:51,514 INFO [Socket Reader #1 for port 29618] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 29618
2015-10-18 18:01:51,535 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-18 18:01:51,535 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-18 18:01:51,536 INFO [IPC Server listener on 29618] org.apache.hadoop.ipc.Server: IPC Server listener on 29618: starting
2015-10-18 18:01:51,536 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-41.fareast.corp.microsoft.com/**************:29618
2015-10-18 18:01:51,626 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-18 18:01:51,635 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-18 18:01:51,659 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-18 18:01:51,670 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-18 18:01:51,670 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-18 18:01:51,677 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-18 18:01:51,677 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-18 18:01:51,699 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 29625
2015-10-18 18:01:51,699 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-18 18:01:51,771 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\4\Jetty_0_0_0_0_29625_mapreduce____q675pj\webapp
2015-10-18 18:01:52,031 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:29625
2015-10-18 18:01:52,031 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 29625
2015-10-18 18:01:52,683 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-18 18:01:52,686 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445144423722_0022
2015-10-18 18:01:52,687 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-18 18:01:52,690 INFO [Socket Reader #1 for port 29630] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 29630
2015-10-18 18:01:52,695 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-18 18:01:52,695 INFO [IPC Server listener on 29630] org.apache.hadoop.ipc.Server: IPC Server listener on 29630: starting
2015-10-18 18:01:52,711 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-18 18:01:52,711 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-18 18:01:52,711 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-18 18:01:52,754 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at MSRA-SA-41/**************:8030
2015-10-18 18:01:52,812 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-18 18:01:52,812 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-18 18:01:52,815 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-18 18:01:52,817 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-18 18:01:52,822 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0022Job Transitioned from INITED to SETUP
2015-10-18 18:01:52,824 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-18 18:01:52,831 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0022Job Transitioned from SETUP to RUNNING
2015-10-18 18:01:52,846 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,847 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:01:52,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:01:52,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:01:52,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:01:52,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:01:52,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:01:52,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:01:52,852 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,852 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,852 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:01:52,852 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,852 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,852 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:01:52,852 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,852 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:52,853 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:01:52,853 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:01:52,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:01:52,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:01:52,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:01:52,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:01:52,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:01:52,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:01:52,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:01:52,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:01:52,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:01:52,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:01:52,856 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:01:52,857 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-18 18:01:52,863 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-18 18:01:52,894 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445144423722_0022, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0022/job_1445144423722_0022_1.jhist
2015-10-18 18:01:53,814 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-18 18:01:53,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:14336, vCores:-13> knownNMs=4
2015-10-18 18:01:53,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:14336, vCores:-13>
2015-10-18 18:01:53,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:01:54,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 4
2015-10-18 18:01:54,914 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000002 to attempt_1445144423722_0022_m_000000_0
2015-10-18 18:01:54,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000003 to attempt_1445144423722_0022_m_000001_0
2015-10-18 18:01:54,918 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000004 to attempt_1445144423722_0022_m_000002_0
2015-10-18 18:01:54,918 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000005 to attempt_1445144423722_0022_m_000003_0
2015-10-18 18:01:54,919 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-17>
2015-10-18 18:01:54,919 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:01:54,919 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:4 RackLocal:0
2015-10-18 18:01:55,016 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:55,047 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0022/job.jar
2015-10-18 18:01:55,052 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0022/job.xml
2015-10-18 18:01:55,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-18 18:01:55,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-18 18:01:55,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-18 18:01:55,148 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:01:55,161 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:55,162 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:01:55,163 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:55,164 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:01:55,164 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:55,166 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:01:55,169 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000002 taskAttempt attempt_1445144423722_0022_m_000000_0
2015-10-18 18:01:55,169 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000003 taskAttempt attempt_1445144423722_0022_m_000001_0
2015-10-18 18:01:55,169 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000004 taskAttempt attempt_1445144423722_0022_m_000002_0
2015-10-18 18:01:55,170 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000005 taskAttempt attempt_1445144423722_0022_m_000003_0
2015-10-18 18:01:55,175 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000003_0
2015-10-18 18:01:55,175 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000001_0
2015-10-18 18:01:55,175 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000002_0
2015-10-18 18:01:55,175 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000000_0
2015-10-18 18:01:55,177 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:01:55,214 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:01:55,216 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:01:55,218 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:01:55,308 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000002_0 : 13562
2015-10-18 18:01:55,308 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000001_0 : 13562
2015-10-18 18:01:55,308 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000003_0 : 13562
2015-10-18 18:01:55,309 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000000_0 : 13562
2015-10-18 18:01:55,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000002_0] using containerId: [container_1445144423722_0022_01_000004 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:7109]
2015-10-18 18:01:55,317 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:01:55,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000000_0] using containerId: [container_1445144423722_0022_01_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:7109]
2015-10-18 18:01:55,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:01:55,319 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000003_0] using containerId: [container_1445144423722_0022_01_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:7109]
2015-10-18 18:01:55,319 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:01:55,320 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000001_0] using containerId: [container_1445144423722_0022_01_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:7109]
2015-10-18 18:01:55,320 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:01:55,321 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000002
2015-10-18 18:01:55,321 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:01:55,322 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000000
2015-10-18 18:01:55,322 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:01:55,322 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000003
2015-10-18 18:01:55,322 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:01:55,323 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000001
2015-10-18 18:01:55,323 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:01:55,923 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-17> knownNMs=4
2015-10-18 18:01:55,923 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-17>
2015-10-18 18:01:55,923 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:01:56,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:01:56,927 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:56,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000006 to attempt_1445144423722_0022_m_000004_0
2015-10-18 18:01:56,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-19>
2015-10-18 18:01:56,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:01:56,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:4 RackLocal:1
2015-10-18 18:01:56,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:56,929 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:01:56,929 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000006 taskAttempt attempt_1445144423722_0022_m_000004_0
2015-10-18 18:01:56,929 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000004_0
2015-10-18 18:01:56,929 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 18:01:57,209 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000004_0 : 13562
2015-10-18 18:01:57,210 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000004_0] using containerId: [container_1445144423722_0022_01_000006 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:52368]
2015-10-18 18:01:57,210 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:01:57,210 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000004
2015-10-18 18:01:57,210 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:01:57,367 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:01:57,370 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:01:57,387 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000003 asked for a task
2015-10-18 18:01:57,387 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000002 asked for a task
2015-10-18 18:01:57,387 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000003 given task: attempt_1445144423722_0022_m_000001_0
2015-10-18 18:01:57,387 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000002 given task: attempt_1445144423722_0022_m_000000_0
2015-10-18 18:01:57,405 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:01:57,418 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000005 asked for a task
2015-10-18 18:01:57,418 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000005 given task: attempt_1445144423722_0022_m_000003_0
2015-10-18 18:01:57,453 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:01:57,466 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000004 asked for a task
2015-10-18 18:01:57,466 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000004 given task: attempt_1445144423722_0022_m_000002_0
2015-10-18 18:01:57,931 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:7168, vCores:-20> knownNMs=4
2015-10-18 18:01:57,931 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:01:57,931 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:57,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000007 to attempt_1445144423722_0022_m_000005_0
2015-10-18 18:01:57,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-20>
2015-10-18 18:01:57,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:01:57,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:4 RackLocal:2
2015-10-18 18:01:57,932 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:57,933 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:01:57,933 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000007 taskAttempt attempt_1445144423722_0022_m_000005_0
2015-10-18 18:01:57,934 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000005_0
2015-10-18 18:01:57,934 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:01:57,965 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000005_0 : 13562
2015-10-18 18:01:57,965 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000005_0] using containerId: [container_1445144423722_0022_01_000007 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:01:57,965 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:01:57,965 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000005
2015-10-18 18:01:57,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:01:58,935 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:5120, vCores:-22> knownNMs=4
2015-10-18 18:01:58,935 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:01:58,935 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:58,935 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000008 to attempt_1445144423722_0022_m_000006_0
2015-10-18 18:01:58,935 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-22>
2015-10-18 18:01:58,935 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:01:58,935 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:4 RackLocal:3
2015-10-18 18:01:58,935 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:58,936 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:01:58,937 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000008 taskAttempt attempt_1445144423722_0022_m_000006_0
2015-10-18 18:01:58,937 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000006_0
2015-10-18 18:01:58,937 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 18:01:59,158 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000006_0 : 13562
2015-10-18 18:01:59,158 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000006_0] using containerId: [container_1445144423722_0022_01_000008 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:52368]
2015-10-18 18:01:59,159 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:01:59,159 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000006
2015-10-18 18:01:59,159 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:01:59,938 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:4096, vCores:-23> knownNMs=4
2015-10-18 18:01:59,938 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:01:59,939 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:59,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000009 to attempt_1445144423722_0022_m_000007_0
2015-10-18 18:01:59,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-18 18:01:59,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:01:59,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:4 RackLocal:4
2015-10-18 18:01:59,939 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:01:59,940 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:01:59,941 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000009 taskAttempt attempt_1445144423722_0022_m_000007_0
2015-10-18 18:01:59,941 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000007_0
2015-10-18 18:01:59,941 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:02:00,691 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000007_0 : 13562
2015-10-18 18:02:00,692 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000007_0] using containerId: [container_1445144423722_0022_01_000009 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:02:00,692 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:02:00,693 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000007
2015-10-18 18:02:00,693 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:02:00,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-24> knownNMs=4
2015-10-18 18:02:00,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-18 18:02:00,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:02:01,073 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:02:01,198 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000006 asked for a task
2015-10-18 18:02:01,199 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000006 given task: attempt_1445144423722_0022_m_000004_0
2015-10-18 18:02:01,945 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-18 18:02:01,945 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:02:01,945 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000010 to attempt_1445144423722_0022_m_000008_0
2015-10-18 18:02:01,945 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:02:01,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000011 to attempt_1445144423722_0022_m_000009_0
2015-10-18 18:02:01,946 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:02:01,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-18 18:02:01,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:02:01,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:4 RackLocal:6
2015-10-18 18:02:01,946 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:02:01,947 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:02:01,947 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:02:01,948 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000010 taskAttempt attempt_1445144423722_0022_m_000008_0
2015-10-18 18:02:01,948 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000011 taskAttempt attempt_1445144423722_0022_m_000009_0
2015-10-18 18:02:01,948 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000008_0
2015-10-18 18:02:01,948 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000009_0
2015-10-18 18:02:01,948 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:02:01,949 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:02:02,355 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:02:02,477 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000007 asked for a task
2015-10-18 18:02:02,477 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000007 given task: attempt_1445144423722_0022_m_000005_0
2015-10-18 18:02:02,949 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-26> knownNMs=4
2015-10-18 18:02:03,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:02:03,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:02:04,609 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.1066108
2015-10-18 18:02:04,681 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.106493875
2015-10-18 18:02:04,717 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.10635664
2015-10-18 18:02:04,721 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.10660437
2015-10-18 18:02:05,191 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000009_0 : 13562
2015-10-18 18:02:05,192 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000009_0] using containerId: [container_1445144423722_0022_01_000011 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:02:05,192 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:02:05,192 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000009
2015-10-18 18:02:05,192 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:02:05,241 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:02:05,261 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000008_0 : 13562
2015-10-18 18:02:05,261 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000008_0] using containerId: [container_1445144423722_0022_01_000010 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:02:05,262 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:02:05,262 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000008
2015-10-18 18:02:05,262 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:02:05,273 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000009 asked for a task
2015-10-18 18:02:05,273 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000009 given task: attempt_1445144423722_0022_m_000007_0
2015-10-18 18:02:07,594 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:02:07,627 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000008 asked for a task
2015-10-18 18:02:07,627 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000008 given task: attempt_1445144423722_0022_m_000006_0
2015-10-18 18:02:07,638 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.1066108
2015-10-18 18:02:07,705 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.106493875
2015-10-18 18:02:07,741 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.10635664
2015-10-18 18:02:07,744 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.10660437
2015-10-18 18:02:10,675 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.1066108
2015-10-18 18:02:10,730 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.106493875
2015-10-18 18:02:10,772 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.10660437
2015-10-18 18:02:10,777 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.10635664
2015-10-18 18:02:11,492 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:02:11,594 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000011 asked for a task
2015-10-18 18:02:11,594 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000011 given task: attempt_1445144423722_0022_m_000009_0
2015-10-18 18:02:11,634 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:02:11,785 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000010 asked for a task
2015-10-18 18:02:11,785 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000010 given task: attempt_1445144423722_0022_m_000008_0
2015-10-18 18:02:13,695 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.14486027
2015-10-18 18:02:13,749 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.16238885
2015-10-18 18:02:13,792 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.158654
2015-10-18 18:02:13,795 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.1457766
2015-10-18 18:02:14,770 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.03289379
2015-10-18 18:02:16,715 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.19211523
2015-10-18 18:02:16,769 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.19209063
2015-10-18 18:02:16,811 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.19212553
2015-10-18 18:02:16,813 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.19158794
2015-10-18 18:02:18,245 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.096660666
2015-10-18 18:02:19,039 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.03635987
2015-10-18 18:02:19,734 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.19211523
2015-10-18 18:02:19,788 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.19209063
2015-10-18 18:02:19,831 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.19212553
2015-10-18 18:02:19,832 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.19158794
2015-10-18 18:02:21,880 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.10680563
2015-10-18 18:02:22,559 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.06676328
2015-10-18 18:02:22,613 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.037776325
2015-10-18 18:02:22,754 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.19211523
2015-10-18 18:02:22,811 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.19209063
2015-10-18 18:02:22,850 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.20256835
2015-10-18 18:02:22,851 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.19158794
2015-10-18 18:02:23,947 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.08291497
2015-10-18 18:02:24,156 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.027979959
2015-10-18 18:02:25,044 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.10680563
2015-10-18 18:02:25,746 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.063830666
2015-10-18 18:02:25,765 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.08663035
2015-10-18 18:02:25,782 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.27191436
2015-10-18 18:02:25,839 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.2636133
2015-10-18 18:02:25,874 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.27772525
2015-10-18 18:02:25,885 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.27696857
2015-10-18 18:02:26,621 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.024096776
2015-10-18 18:02:27,198 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.18696898
2015-10-18 18:02:27,423 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.060574878
2015-10-18 18:02:28,805 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.27776006
2015-10-18 18:02:28,860 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.27765483
2015-10-18 18:02:28,895 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.27772525
2015-10-18 18:02:28,904 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.27696857
2015-10-18 18:02:29,045 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.10680563
2015-10-18 18:02:29,399 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.099006735
2015-10-18 18:02:29,620 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.102622695
2015-10-18 18:02:30,167 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.050151818
2015-10-18 18:02:30,927 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.28263596
2015-10-18 18:02:31,136 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.09679757
2015-10-18 18:02:31,824 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.27776006
2015-10-18 18:02:31,879 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.27765483
2015-10-18 18:02:31,914 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.27772525
2015-10-18 18:02:31,922 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.27696857
2015-10-18 18:02:32,574 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.10680563
2015-10-18 18:02:32,868 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.10685723
2015-10-18 18:02:33,060 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.106964506
2015-10-18 18:02:33,575 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.08274127
2015-10-18 18:02:34,542 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.295472
2015-10-18 18:02:34,654 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.10681946
2015-10-18 18:02:34,860 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.2988367
2015-10-18 18:02:34,910 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.29970163
2015-10-18 18:02:34,945 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.32728902
2015-10-18 18:02:34,951 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.33079174
2015-10-18 18:02:36,075 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.10680563
2015-10-18 18:02:36,585 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.10685723
2015-10-18 18:02:36,624 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.106964506
2015-10-18 18:02:37,162 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.106881365
2015-10-18 18:02:37,881 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.36319977
2015-10-18 18:02:37,930 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.36323506
2015-10-18 18:02:37,964 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.36317363
2015-10-18 18:02:37,967 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.3624012
2015-10-18 18:02:38,105 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.295472
2015-10-18 18:02:38,118 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.10681946
2015-10-18 18:02:39,511 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.10680563
2015-10-18 18:02:40,043 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.106964506
2015-10-18 18:02:40,181 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.10685723
2015-10-18 18:02:40,634 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.106881365
2015-10-18 18:02:40,917 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.36319977
2015-10-18 18:02:40,967 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.36323506
2015-10-18 18:02:41,000 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.3624012
2015-10-18 18:02:41,004 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.36317363
2015-10-18 18:02:41,554 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.10681946
2015-10-18 18:02:41,608 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.295472
2015-10-18 18:02:43,167 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.10680563
2015-10-18 18:02:43,624 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.106964506
2015-10-18 18:02:43,727 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.10685723
2015-10-18 18:02:43,952 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.36319977
2015-10-18 18:02:43,998 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.36323506
2015-10-18 18:02:44,025 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.36317363
2015-10-18 18:02:44,041 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.3624012
2015-10-18 18:02:44,335 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.106881365
2015-10-18 18:02:44,978 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.10681946
2015-10-18 18:02:45,104 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.295472
2015-10-18 18:02:46,670 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.10680563
2015-10-18 18:02:46,991 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.36319977
2015-10-18 18:02:47,036 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.3754562
2015-10-18 18:02:47,064 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.39228597
2015-10-18 18:02:47,082 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.3624012
2015-10-18 18:02:47,106 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.106964506
2015-10-18 18:02:47,239 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.10685723
2015-10-18 18:02:48,183 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.106881365
2015-10-18 18:02:48,545 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.10681946
2015-10-18 18:02:48,732 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.295472
2015-10-18 18:02:50,022 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.36345652
2015-10-18 18:02:50,063 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.4486067
2015-10-18 18:02:50,092 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.44698045
2015-10-18 18:02:50,106 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.40904924
2015-10-18 18:02:50,274 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.14609829
2015-10-18 18:02:50,647 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.10685723
2015-10-18 18:02:50,719 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.106964506
2015-10-18 18:02:51,841 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.106881365
2015-10-18 18:02:51,985 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.10681946
2015-10-18 18:02:52,203 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.295472
2015-10-18 18:02:53,055 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.43273848
2015-10-18 18:02:53,093 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.4486067
2015-10-18 18:02:53,123 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.44859612
2015-10-18 18:02:53,138 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.44789755
2015-10-18 18:02:53,854 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.19015539
2015-10-18 18:02:54,087 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.10685723
2015-10-18 18:02:54,279 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.106964506
2015-10-18 18:02:55,195 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.106881365
2015-10-18 18:02:55,424 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.10681946
2015-10-18 18:02:55,539 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.295472
2015-10-18 18:02:56,075 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.448704
2015-10-18 18:02:56,111 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.4486067
2015-10-18 18:02:56,142 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.44859612
2015-10-18 18:02:56,157 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.44789755
2015-10-18 18:02:57,303 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.19242907
2015-10-18 18:02:57,396 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.10685723
2015-10-18 18:02:57,717 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.106964506
2015-10-18 18:02:58,430 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.106881365
2015-10-18 18:02:58,776 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.10681946
2015-10-18 18:02:58,900 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.295472
2015-10-18 18:02:59,095 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.448704
2015-10-18 18:02:59,131 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.46337706
2015-10-18 18:02:59,162 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.45011145
2015-10-18 18:02:59,177 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.44789755
2015-10-18 18:03:00,375 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.19242907
2015-10-18 18:03:00,812 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.10685723
2015-10-18 18:03:00,861 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.11887157
2015-10-18 18:03:01,947 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.106881365
2015-10-18 18:03:02,114 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.448704
2015-10-18 18:03:02,151 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.5343203
2015-10-18 18:03:02,181 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.5342037
2015-10-18 18:03:02,197 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.50314575
2015-10-18 18:03:02,243 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.295472
2015-10-18 18:03:02,243 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.10681946
2015-10-18 18:03:04,171 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.19242907
2015-10-18 18:03:04,185 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.12736817
2015-10-18 18:03:04,389 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.13779871
2015-10-18 18:03:05,133 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.53425497
2015-10-18 18:03:05,170 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.5343203
2015-10-18 18:03:05,201 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.5342037
2015-10-18 18:03:05,215 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.53341997
2015-10-18 18:03:05,447 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.106881365
2015-10-18 18:03:05,697 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.10681946
2015-10-18 18:03:05,716 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.295472
2015-10-18 18:03:07,735 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.16348982
2015-10-18 18:03:07,817 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.19242907
2015-10-18 18:03:08,041 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.17904976
2015-10-18 18:03:08,152 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.53425497
2015-10-18 18:03:08,189 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.5343203
2015-10-18 18:03:08,220 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.5342037
2015-10-18 18:03:08,233 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.53341997
2015-10-18 18:03:08,952 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.106881365
2015-10-18 18:03:09,200 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.12766378
2015-10-18 18:03:09,452 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.3870572
2015-10-18 18:03:11,173 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.53425497
2015-10-18 18:03:11,208 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.60336405
2015-10-18 18:03:11,239 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.5883447
2015-10-18 18:03:11,248 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.19242907
2015-10-18 18:03:11,253 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.53341997
2015-10-18 18:03:11,340 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.19266446
2015-10-18 18:03:11,357 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.19247705
2015-10-18 18:03:12,516 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.12824954
2015-10-18 18:03:12,801 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.16363661
2015-10-18 18:03:12,994 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.48844615
2015-10-18 18:03:14,193 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.58420813
2015-10-18 18:03:14,227 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.6199081
2015-10-18 18:03:14,259 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.6196791
2015-10-18 18:03:14,272 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.61898744
2015-10-18 18:03:14,607 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.19242907
2015-10-18 18:03:14,766 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.19266446
2015-10-18 18:03:15,231 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.19247705
2015-10-18 18:03:16,203 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.16361044
2015-10-18 18:03:16,317 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.19255035
2015-10-18 18:03:16,576 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.5323719
2015-10-18 18:03:17,212 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.6197233
2015-10-18 18:03:17,245 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.6199081
2015-10-18 18:03:17,277 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.6196791
2015-10-18 18:03:17,290 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.61898744
2015-10-18 18:03:18,179 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.19242907
2015-10-18 18:03:18,264 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.19266446
2015-10-18 18:03:18,978 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.19247705
2015-10-18 18:03:19,874 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.19258286
2015-10-18 18:03:20,132 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.19255035
2015-10-18 18:03:20,230 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.6197233
2015-10-18 18:03:20,264 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.6199081
2015-10-18 18:03:20,298 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.6196791
2015-10-18 18:03:20,308 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.61898744
2015-10-18 18:03:20,311 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.5323719
2015-10-18 18:03:21,266 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.6199081
2015-10-18 18:03:21,704 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.21654941
2015-10-18 18:03:21,744 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.6196791
2015-10-18 18:03:21,764 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.19266446
2015-10-18 18:03:22,346 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.61898744
2015-10-18 18:03:22,620 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.19247705
2015-10-18 18:03:23,249 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.6197233
2015-10-18 18:03:23,283 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.667
2015-10-18 18:03:23,309 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.19258286
2015-10-18 18:03:23,316 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.667
2015-10-18 18:03:23,326 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.667
2015-10-18 18:03:23,727 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.19255035
2015-10-18 18:03:23,787 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.5323719
2015-10-18 18:03:24,232 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.6197233
2015-10-18 18:03:25,620 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.26497683
2015-10-18 18:03:25,643 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.19266446
2015-10-18 18:03:26,118 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.19247705
2015-10-18 18:03:26,268 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.667
2015-10-18 18:03:26,302 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.667
2015-10-18 18:03:26,335 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.667
2015-10-18 18:03:26,344 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.667
2015-10-18 18:03:26,732 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.19258286
2015-10-18 18:03:27,227 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.19255035
2015-10-18 18:03:27,385 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.5323719
2015-10-18 18:03:29,177 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.2781602
2015-10-18 18:03:29,288 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.667
2015-10-18 18:03:29,322 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.6800718
2015-10-18 18:03:29,356 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.6757204
2015-10-18 18:03:29,363 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.667
2015-10-18 18:03:29,461 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.19266446
2015-10-18 18:03:29,553 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.19247705
2015-10-18 18:03:30,350 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.19258286
2015-10-18 18:03:30,735 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.19255035
2015-10-18 18:03:30,960 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.5323719
2015-10-18 18:03:32,318 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.67117447
2015-10-18 18:03:32,349 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.72194594
2015-10-18 18:03:32,385 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.7178649
2015-10-18 18:03:32,391 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.69847965
2015-10-18 18:03:32,839 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.2781602
2015-10-18 18:03:33,145 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.19266446
2015-10-18 18:03:33,151 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.19247705
2015-10-18 18:03:33,827 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.19258286
2015-10-18 18:03:34,149 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.19255035
2015-10-18 18:03:34,489 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.5323719
2015-10-18 18:03:35,340 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.6922764
2015-10-18 18:03:35,370 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.7649398
2015-10-18 18:03:35,406 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.7601146
2015-10-18 18:03:35,411 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.7223649
2015-10-18 18:03:36,259 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.2781602
2015-10-18 18:03:36,565 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.19266446
2015-10-18 18:03:36,646 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.19247705
2015-10-18 18:03:37,185 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.19258286
2015-10-18 18:03:37,584 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.19255035
2015-10-18 18:03:38,038 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.5323719
2015-10-18 18:03:38,359 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.71722347
2015-10-18 18:03:38,388 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.79949045
2015-10-18 18:03:38,425 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.7949263
2015-10-18 18:03:38,427 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.7499961
2015-10-18 18:03:39,540 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.2781602
2015-10-18 18:03:39,886 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.20420222
2015-10-18 18:03:39,961 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.19247705
2015-10-18 18:03:40,617 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.19258286
2015-10-18 18:03:41,009 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.19255035
2015-10-18 18:03:41,384 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.7463839
2015-10-18 18:03:41,412 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.83425903
2015-10-18 18:03:41,449 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.82770365
2015-10-18 18:03:41,454 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.78104204
2015-10-18 18:03:41,502 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.5323719
2015-10-18 18:03:43,254 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.2781602
2015-10-18 18:03:43,415 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.19611235
2015-10-18 18:03:43,687 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.23123185
2015-10-18 18:03:43,964 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.19258286
2015-10-18 18:03:44,368 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.19255035
2015-10-18 18:03:44,412 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.7730916
2015-10-18 18:03:44,438 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.8782952
2015-10-18 18:03:44,476 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.8712361
2015-10-18 18:03:44,479 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.8091937
2015-10-18 18:03:44,951 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.5323719
2015-10-18 18:03:46,816 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.2781602
2015-10-18 18:03:47,153 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.2700979
2015-10-18 18:03:47,211 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.24033839
2015-10-18 18:03:47,342 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.19258286
2015-10-18 18:03:47,432 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.8086361
2015-10-18 18:03:47,456 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.9208681
2015-10-18 18:03:47,495 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.914199
2015-10-18 18:03:47,495 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.8475318
2015-10-18 18:03:47,870 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.19255035
2015-10-18 18:03:48,630 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.5515099
2015-10-18 18:03:50,170 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.2781602
2015-10-18 18:03:50,474 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.8401477
2015-10-18 18:03:50,499 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.9533706
2015-10-18 18:03:50,541 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.94543755
2015-10-18 18:03:50,545 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.8806673
2015-10-18 18:03:50,688 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.2783809
2015-10-18 18:03:50,823 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.27294564
2015-10-18 18:03:50,922 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.19258286
2015-10-18 18:03:51,421 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.2195381
2015-10-18 18:03:52,170 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.6468472
2015-10-18 18:03:52,792 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.6468472
2015-10-18 18:03:53,473 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.2781602
2015-10-18 18:03:53,511 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.8631512
2015-10-18 18:03:53,535 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 0.9886995
2015-10-18 18:03:53,575 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 0.97886896
2015-10-18 18:03:53,584 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.9050332
2015-10-18 18:03:54,108 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.2783809
2015-10-18 18:03:54,288 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.27813601
2015-10-18 18:03:54,387 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.22003616
2015-10-18 18:03:54,464 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000003_0 is : 1.0
2015-10-18 18:03:54,465 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0022_m_000003_0
2015-10-18 18:03:54,466 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:03:54,466 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000005 taskAttempt attempt_1445144423722_0022_m_000003_0
2015-10-18 18:03:54,466 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000003_0
2015-10-18 18:03:54,467 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:03:54,486 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:03:54,492 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0022_m_000003_0
2015-10-18 18:03:54,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:03:54,496 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-18 18:03:54,860 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0022_m_000007
2015-10-18 18:03:54,860 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:03:54,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0022_m_000007
2015-10-18 18:03:54,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:03:54,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:03:54,862 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000007_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:03:55,071 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.2620626
2015-10-18 18:03:55,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:4 RackLocal:6
2015-10-18 18:03:55,083 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 18:03:55,083 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-18 18:03:55,084 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-18 18:03:55,084 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:10240, vCores:-17> finalMapResourceLimit:<memory:9216, vCores:-16> finalReduceResourceLimit:<memory:1024, vCores:-1> netScheduledMapResource:<memory:11264, vCores:11> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:03:55,084 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-18 18:03:55,084 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:4 RackLocal:6
2015-10-18 18:03:55,187 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000002_0 is : 1.0
2015-10-18 18:03:55,190 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0022_m_000002_0
2015-10-18 18:03:55,190 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:03:55,191 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000004 taskAttempt attempt_1445144423722_0022_m_000002_0
2015-10-18 18:03:55,191 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000002_0
2015-10-18 18:03:55,191 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:03:55,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:03:55,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0022_m_000002_0
2015-10-18 18:03:55,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:03:55,208 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-18 18:03:55,856 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.667
2015-10-18 18:03:56,084 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:4 RackLocal:6
2015-10-18 18:03:56,093 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=1 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 18:03:56,094 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000005
2015-10-18 18:03:56,095 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:4 RackLocal:6
2015-10-18 18:03:56,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:03:56,531 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.8966434
2015-10-18 18:03:56,603 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.94183934
2015-10-18 18:03:56,939 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.2781602
2015-10-18 18:03:57,099 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000004
2015-10-18 18:03:57,100 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:03:57,100 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:4 RackLocal:6
2015-10-18 18:03:57,688 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.2783809
2015-10-18 18:03:57,712 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.27813601
2015-10-18 18:03:57,887 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.25649196
2015-10-18 18:03:58,480 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.27825075
2015-10-18 18:03:59,571 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.9279138
2015-10-18 18:03:59,636 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.9744506
2015-10-18 18:03:59,697 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.667
2015-10-18 18:04:00,422 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.32721806
2015-10-18 18:04:01,130 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.2783809
2015-10-18 18:04:01,306 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.27813601
2015-10-18 18:04:01,597 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.27811313
2015-10-18 18:04:01,983 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.27825075
2015-10-18 18:04:02,607 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.9458333
2015-10-18 18:04:02,664 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 0.99303895
2015-10-18 18:04:03,250 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.667
2015-10-18 18:04:03,465 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000000_0 is : 1.0
2015-10-18 18:04:03,466 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0022_m_000000_0
2015-10-18 18:04:03,467 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:04:03,467 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000002 taskAttempt attempt_1445144423722_0022_m_000000_0
2015-10-18 18:04:03,467 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000000_0
2015-10-18 18:04:03,467 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:04:03,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:04:03,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0022_m_000000_0
2015-10-18 18:04:03,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:04:03,477 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-18 18:04:03,961 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.36388028
2015-10-18 18:04:04,107 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:4 RackLocal:6
2015-10-18 18:04:04,595 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.2783809
2015-10-18 18:04:04,774 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.27813601
2015-10-18 18:04:05,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000002
2015-10-18 18:04:05,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:7 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:4 RackLocal:6
2015-10-18 18:04:05,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:04:05,172 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.27811313
2015-10-18 18:04:05,506 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.27825075
2015-10-18 18:04:05,642 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 0.97597253
2015-10-18 18:04:06,679 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.667
2015-10-18 18:04:07,438 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.36388028
2015-10-18 18:04:08,111 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.2783809
2015-10-18 18:04:08,344 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.27813601
2015-10-18 18:04:08,555 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.27811313
2015-10-18 18:04:08,558 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000001_0 is : 1.0
2015-10-18 18:04:08,560 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0022_m_000001_0
2015-10-18 18:04:08,561 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:04:08,561 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000003 taskAttempt attempt_1445144423722_0022_m_000001_0
2015-10-18 18:04:08,561 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000001_0
2015-10-18 18:04:08,561 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:04:08,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:04:08,615 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0022_m_000001_0
2015-10-18 18:04:08,617 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:04:08,620 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-18 18:04:08,942 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.27825075
2015-10-18 18:04:09,112 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:7 AssignedReds:0 CompletedMaps:4 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:4 RackLocal:6
2015-10-18 18:04:10,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000003
2015-10-18 18:04:10,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:6 AssignedReds:0 CompletedMaps:4 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:4 RackLocal:6
2015-10-18 18:04:10,115 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:04:10,179 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.667
2015-10-18 18:04:10,842 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.36388028
2015-10-18 18:04:11,484 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.2783809
2015-10-18 18:04:11,863 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.27813601
2015-10-18 18:04:12,171 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.27811313
2015-10-18 18:04:12,486 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.27825075
2015-10-18 18:04:13,695 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.667
2015-10-18 18:04:14,211 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.36388028
2015-10-18 18:04:14,961 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.2783809
2015-10-18 18:04:15,308 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.27813601
2015-10-18 18:04:15,665 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.27811313
2015-10-18 18:04:15,992 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.27825075
2015-10-18 18:04:17,126 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.667
2015-10-18 18:04:17,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-18 18:04:17,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-18 18:04:17,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000012 to attempt_1445144423722_0022_r_000000_0
2015-10-18 18:04:17,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000013 to attempt_1445144423722_0022_m_000007_1
2015-10-18 18:04:17,132 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:5 RackLocal:6
2015-10-18 18:04:17,145 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:04:17,146 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:04:17,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:04:17,148 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000007_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:04:17,148 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000012 taskAttempt attempt_1445144423722_0022_r_000000_0
2015-10-18 18:04:17,148 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000013 taskAttempt attempt_1445144423722_0022_m_000007_1
2015-10-18 18:04:17,148 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_r_000000_0
2015-10-18 18:04:17,148 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000007_1
2015-10-18 18:04:17,148 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:04:17,150 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:04:17,177 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_r_000000_0 : 13562
2015-10-18 18:04:17,178 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_r_000000_0] using containerId: [container_1445144423722_0022_01_000012 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:04:17,178 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:04:17,179 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_r_000000
2015-10-18 18:04:17,179 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:04:17,179 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000007_1 : 13562
2015-10-18 18:04:17,180 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000007_1] using containerId: [container_1445144423722_0022_01_000013 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:04:17,180 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000007_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:04:17,180 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000007
2015-10-18 18:04:17,405 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.36388028
2015-10-18 18:04:17,865 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0022_m_000005
2015-10-18 18:04:17,865 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:04:17,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0022_m_000005
2015-10-18 18:04:17,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:04:17,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:04:17,866 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000005_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:04:18,132 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:5 RackLocal:6
2015-10-18 18:04:18,134 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 18:04:18,286 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.30386016
2015-10-18 18:04:18,800 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.27813601
2015-10-18 18:04:19,151 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.27811313
2015-10-18 18:04:19,373 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.27825075
2015-10-18 18:04:20,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:04:20,140 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000014 to attempt_1445144423722_0022_m_000005_1
2015-10-18 18:04:20,140 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:6 RackLocal:6
2015-10-18 18:04:20,141 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:04:20,142 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000005_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:04:20,142 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000014 taskAttempt attempt_1445144423722_0022_m_000005_1
2015-10-18 18:04:20,143 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000005_1
2015-10-18 18:04:20,143 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:04:20,164 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000005_1 : 13562
2015-10-18 18:04:20,165 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000005_1] using containerId: [container_1445144423722_0022_01_000014 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:04:20,165 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000005_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:04:20,166 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000005
2015-10-18 18:04:20,496 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.667
2015-10-18 18:04:20,741 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:04:20,765 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_r_000012 asked for a task
2015-10-18 18:04:20,765 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_r_000012 given task: attempt_1445144423722_0022_r_000000_0
2015-10-18 18:04:20,968 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.36388028
2015-10-18 18:04:21,142 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 18:04:21,158 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:04:21,181 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000013 asked for a task
2015-10-18 18:04:21,181 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000013 given task: attempt_1445144423722_0022_m_000007_1
2015-10-18 18:04:21,733 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.33790198
2015-10-18 18:04:22,163 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.27813601
2015-10-18 18:04:22,311 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 0 maxEvents 10000
2015-10-18 18:04:22,590 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.27811313
2015-10-18 18:04:22,812 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.27825075
2015-10-18 18:04:23,127 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:04:23,143 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000014 asked for a task
2015-10-18 18:04:23,143 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000014 given task: attempt_1445144423722_0022_m_000005_1
2015-10-18 18:04:23,330 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:23,986 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.68283623
2015-10-18 18:04:24,331 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:24,358 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.36388028
2015-10-18 18:04:25,186 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.36404583
2015-10-18 18:04:25,332 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:25,668 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.30423886
2015-10-18 18:04:26,027 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.27811313
2015-10-18 18:04:26,219 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.27825075
2015-10-18 18:04:26,332 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:27,332 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:27,546 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.70756394
2015-10-18 18:04:27,686 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.36388028
2015-10-18 18:04:28,243 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:04:28,332 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:28,615 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.36404583
2015-10-18 18:04:28,664 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.09997123
2015-10-18 18:04:29,270 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.34265807
2015-10-18 18:04:29,333 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:29,693 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.27811313
2015-10-18 18:04:29,900 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.28236562
2015-10-18 18:04:30,332 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:30,812 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.10685723
2015-10-18 18:04:31,000 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.36392084
2015-10-18 18:04:31,091 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.7290312
2015-10-18 18:04:31,251 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:04:31,332 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:31,678 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.10681946
2015-10-18 18:04:32,095 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.36404583
2015-10-18 18:04:32,332 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:32,800 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.36390656
2015-10-18 18:04:32,866 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0022_m_000008
2015-10-18 18:04:32,866 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:04:32,866 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0022_m_000008
2015-10-18 18:04:32,867 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:04:32,867 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:04:32,868 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000008_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:04:33,155 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:6 RackLocal:6
2015-10-18 18:04:33,158 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 18:04:33,161 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.27811313
2015-10-18 18:04:33,332 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:33,400 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.31439272
2015-10-18 18:04:33,812 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.10685723
2015-10-18 18:04:34,250 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:04:34,332 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:34,361 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.41262576
2015-10-18 18:04:34,600 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.75260717
2015-10-18 18:04:34,690 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.10681946
2015-10-18 18:04:35,336 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:35,562 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.36404583
2015-10-18 18:04:36,317 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.36390656
2015-10-18 18:04:36,332 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:36,754 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.31881228
2015-10-18 18:04:36,816 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.10685723
2015-10-18 18:04:36,913 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.35099953
2015-10-18 18:04:37,253 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:04:37,334 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:37,703 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.10681946
2015-10-18 18:04:37,980 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.44968578
2015-10-18 18:04:38,064 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.7759393
2015-10-18 18:04:38,332 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:39,078 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.36404583
2015-10-18 18:04:39,332 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:39,823 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.36390656
2015-10-18 18:04:39,835 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.123431735
2015-10-18 18:04:40,268 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:04:40,332 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:40,441 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.3638923
2015-10-18 18:04:40,441 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.34801725
2015-10-18 18:04:40,707 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.1526579
2015-10-18 18:04:41,332 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:41,455 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.44968578
2015-10-18 18:04:41,592 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.8002907
2015-10-18 18:04:42,332 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:42,562 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.36404583
2015-10-18 18:04:42,842 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.19247705
2015-10-18 18:04:43,280 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:04:43,332 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:43,370 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.36390656
2015-10-18 18:04:43,717 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.19255035
2015-10-18 18:04:44,018 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.3638923
2015-10-18 18:04:44,092 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.3637686
2015-10-18 18:04:44,333 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:45,030 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.44968578
2015-10-18 18:04:45,165 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.82498014
2015-10-18 18:04:45,333 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:45,843 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.19247705
2015-10-18 18:04:46,282 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:04:46,333 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:46,433 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.36404583
2015-10-18 18:04:46,719 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.19255035
2015-10-18 18:04:46,745 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.36390656
2015-10-18 18:04:47,333 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:47,512 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.3638923
2015-10-18 18:04:47,667 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.3637686
2015-10-18 18:04:48,333 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:48,541 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.8521137
2015-10-18 18:04:48,846 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.19247705
2015-10-18 18:04:49,281 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:04:49,333 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:49,721 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.19255035
2015-10-18 18:04:50,146 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.44968578
2015-10-18 18:04:50,228 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.36390656
2015-10-18 18:04:50,333 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:50,585 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.36404583
2015-10-18 18:04:50,948 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.3638923
2015-10-18 18:04:51,212 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.3637686
2015-10-18 18:04:51,333 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:51,862 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.23171084
2015-10-18 18:04:52,024 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.8788285
2015-10-18 18:04:52,184 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:04:52,184 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000015 to attempt_1445144423722_0022_m_000008_1
2015-10-18 18:04:52,184 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:7 RackLocal:6
2015-10-18 18:04:52,185 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:04:52,185 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000008_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:04:52,185 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000015 taskAttempt attempt_1445144423722_0022_m_000008_1
2015-10-18 18:04:52,185 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000008_1
2015-10-18 18:04:52,185 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:04:52,199 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000008_1 : 13562
2015-10-18 18:04:52,200 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000008_1] using containerId: [container_1445144423722_0022_01_000015 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:7109]
2015-10-18 18:04:52,200 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000008_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:04:52,200 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000008
2015-10-18 18:04:52,287 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:04:52,333 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:52,734 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.22994857
2015-10-18 18:04:52,874 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0022_m_000006
2015-10-18 18:04:52,874 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:04:52,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0022_m_000006
2015-10-18 18:04:52,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:04:52,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:04:52,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:04:53,184 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:7 RackLocal:6
2015-10-18 18:04:53,185 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-18 18:04:53,333 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:53,684 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.36390656
2015-10-18 18:04:53,973 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.44968578
2015-10-18 18:04:53,980 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.36736783
2015-10-18 18:04:54,333 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:54,384 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.3638923
2015-10-18 18:04:54,617 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.3637686
2015-10-18 18:04:54,745 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:04:54,756 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000015 asked for a task
2015-10-18 18:04:54,756 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000015 given task: attempt_1445144423722_0022_m_000008_1
2015-10-18 18:04:54,875 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.27813601
2015-10-18 18:04:55,296 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:04:55,333 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:55,417 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.9046103
2015-10-18 18:04:55,733 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.27825075
2015-10-18 18:04:56,332 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:57,095 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.36390656
2015-10-18 18:04:57,332 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:57,354 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_0 is : 0.44968578
2015-10-18 18:04:57,360 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_0 is : 0.37746617
2015-10-18 18:04:57,850 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.3638923
2015-10-18 18:04:57,874 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.27813601
2015-10-18 18:04:58,198 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.3637686
2015-10-18 18:04:58,297 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:04:58,332 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:04:58,734 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.27825075
2015-10-18 18:04:58,886 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.9313468
2015-10-18 18:04:59,333 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:05:00,333 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:05:00,483 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.36390656
2015-10-18 18:05:00,875 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.27813601
2015-10-18 18:05:01,289 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.3638923
2015-10-18 18:05:01,298 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:05:01,333 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:05:01,544 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.3637686
2015-10-18 18:05:01,734 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.27825075
2015-10-18 18:05:01,808 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.106881365
2015-10-18 18:05:02,221 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.9576363
2015-10-18 18:05:02,333 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:05:03,333 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:05:03,880 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.33469898
2015-10-18 18:05:03,957 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.36671713
2015-10-18 18:05:04,304 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:05:04,333 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:05:04,734 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.34854403
2015-10-18 18:05:04,806 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.3638923
2015-10-18 18:05:04,833 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.106881365
2015-10-18 18:05:05,125 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.3637686
2015-10-18 18:05:05,333 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:05:05,621 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 0.98178715
2015-10-18 18:05:06,333 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:05:06,892 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.36390656
2015-10-18 18:05:07,312 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:05:07,323 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.40189213
2015-10-18 18:05:07,332 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:05:07,734 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.3638923
2015-10-18 18:05:07,852 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.106881365
2015-10-18 18:05:08,219 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.3638923
2015-10-18 18:05:08,333 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:05:08,417 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.3637686
2015-10-18 18:05:09,108 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 1.0
2015-10-18 18:05:09,333 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:05:09,581 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000009_0 is : 1.0
2015-10-18 18:05:09,598 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0022_m_000009_0
2015-10-18 18:05:09,598 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:05:09,598 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000011 taskAttempt attempt_1445144423722_0022_m_000009_0
2015-10-18 18:05:09,598 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000009_0
2015-10-18 18:05:09,599 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:05:09,890 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.36390656
2015-10-18 18:05:10,312 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:05:10,332 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:05:10,360 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:05:10,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0022_m_000009_0
2015-10-18 18:05:10,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:05:10,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-18 18:05:10,734 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.3638923
2015-10-18 18:05:10,880 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.14232346
2015-10-18 18:05:10,899 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.43942794
2015-10-18 18:05:11,206 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:7 RackLocal:6
2015-10-18 18:05:11,333 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 4 maxEvents 10000
2015-10-18 18:05:12,025 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.3812046
2015-10-18 18:05:12,101 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.3637686
2015-10-18 18:05:12,333 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:12,892 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.36390656
2015-10-18 18:05:13,210 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000011
2015-10-18 18:05:13,210 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:7 RackLocal:6
2015-10-18 18:05:13,210 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000009_0: Container killed by the ApplicationMaster.

2015-10-18 18:05:13,317 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:05:13,332 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:13,736 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.36571795
2015-10-18 18:05:13,900 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.19258286
2015-10-18 18:05:14,197 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.44950968
2015-10-18 18:05:14,333 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:15,334 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:15,471 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.3999362
2015-10-18 18:05:15,606 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.38255548
2015-10-18 18:05:15,891 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.42857397
2015-10-18 18:05:16,331 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:05:16,334 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:16,734 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.44964966
2015-10-18 18:05:16,938 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.19258286
2015-10-18 18:05:17,334 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:17,548 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.44950968
2015-10-18 18:05:18,333 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:18,645 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.41198665
2015-10-18 18:05:18,765 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.4057999
2015-10-18 18:05:18,891 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.44950968
2015-10-18 18:05:19,329 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:05:19,333 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:19,734 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.44964966
2015-10-18 18:05:19,967 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.19258286
2015-10-18 18:05:20,334 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:20,755 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.44950968
2015-10-18 18:05:21,333 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:21,891 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.44950968
2015-10-18 18:05:21,900 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.422409
2015-10-18 18:05:21,966 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.42306063
2015-10-18 18:05:22,328 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:05:22,333 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:22,734 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.44964966
2015-10-18 18:05:22,986 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.20419098
2015-10-18 18:05:23,334 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:24,333 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:24,401 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.44950968
2015-10-18 18:05:24,899 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.46310148
2015-10-18 18:05:25,330 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:05:25,334 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:25,433 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.4488762
2015-10-18 18:05:25,548 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.44032207
2015-10-18 18:05:25,739 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.5064225
2015-10-18 18:05:25,767 INFO [Socket Reader #1 for port 29630] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 29630: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:05:26,017 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.27811313
2015-10-18 18:05:26,334 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:27,333 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:27,553 WARN [ResponseProcessor for block BP-1347369012-**************-1444972147527:blk_1073743510_2729] org.apache.hadoop.hdfs.DFSClient: Slow ReadProcessor read fields took 48905ms (threshold=30000ms); ack: seqno: -2 status: SUCCESS status: ERROR downstreamAckTimeNanos: 0, targets: [**************:50010, *************:50010]
2015-10-18 18:05:27,553 WARN [ResponseProcessor for block BP-1347369012-**************-1444972147527:blk_1073743510_2729] org.apache.hadoop.hdfs.DFSClient: DFSOutputStream ResponseProcessor exception  for block BP-1347369012-**************-1444972147527:blk_1073743510_2729
java.io.IOException: Bad response ERROR for block BP-1347369012-**************-1444972147527:blk_1073743510_2729 from datanode *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer$ResponseProcessor.run(DFSOutputStream.java:898)
2015-10-18 18:05:27,555 WARN [DataStreamer for file /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0022/job_1445144423722_0022_1.jhist block BP-1347369012-**************-1444972147527:blk_1073743510_2729] org.apache.hadoop.hdfs.DFSClient: Error Recovery for block BP-1347369012-**************-1444972147527:blk_1073743510_2729 in pipeline **************:50010, *************:50010: bad datanode *************:50010
2015-10-18 18:05:27,907 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.530552
2015-10-18 18:05:27,963 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.44950968
2015-10-18 18:05:28,289 INFO [Socket Reader #1 for port 29630] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 29630: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:05:28,328 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.13333334
2015-10-18 18:05:28,333 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:28,750 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.5352825
2015-10-18 18:05:28,930 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.44950172
2015-10-18 18:05:29,024 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.44964966
2015-10-18 18:05:29,058 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.27811313
2015-10-18 18:05:29,333 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:30,334 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:30,915 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.5352021
2015-10-18 18:05:31,183 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.44950968
2015-10-18 18:05:31,333 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:05:31,334 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:31,759 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.5352825
2015-10-18 18:05:32,100 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.27811313
2015-10-18 18:05:32,212 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.44950172
2015-10-18 18:05:32,335 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:32,400 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.44964966
2015-10-18 18:05:33,334 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:33,924 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.5352021
2015-10-18 18:05:34,334 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:34,346 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:05:34,665 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.44950968
2015-10-18 18:05:34,771 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.5352825
2015-10-18 18:05:35,130 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.27811313
2015-10-18 18:05:35,334 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:35,636 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.44950172
2015-10-18 18:05:35,743 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.44964966
2015-10-18 18:05:36,334 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:36,931 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.5610621
2015-10-18 18:05:37,334 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:37,352 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:05:37,785 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.5896667
2015-10-18 18:05:37,977 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.44950968
2015-10-18 18:05:38,160 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.3404365
2015-10-18 18:05:38,335 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:38,988 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.44950172
2015-10-18 18:05:39,120 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.44964966
2015-10-18 18:05:39,334 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:39,941 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.6209487
2015-10-18 18:05:40,334 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:40,361 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:05:40,801 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.620844
2015-10-18 18:05:41,193 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.3637686
2015-10-18 18:05:41,298 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.4861191
2015-10-18 18:05:41,334 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:42,288 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.44950172
2015-10-18 18:05:42,334 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:42,414 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.44964966
2015-10-18 18:05:42,955 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.6209487
2015-10-18 18:05:43,334 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:43,368 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:05:43,814 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.620844
2015-10-18 18:05:44,234 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.3637686
2015-10-18 18:05:44,334 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:44,925 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.53083783
2015-10-18 18:05:45,335 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:45,745 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.44950172
2015-10-18 18:05:45,949 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.44964966
2015-10-18 18:05:45,963 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.6209487
2015-10-18 18:05:46,335 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:46,379 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:05:46,821 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.620844
2015-10-18 18:05:47,266 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.3637686
2015-10-18 18:05:47,335 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:48,210 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.5352021
2015-10-18 18:05:48,335 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:48,514 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.620844
2015-10-18 18:05:48,972 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.6209487
2015-10-18 18:05:48,994 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.44950172
2015-10-18 18:05:49,215 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.44964966
2015-10-18 18:05:49,334 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:49,378 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:05:49,830 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.667
2015-10-18 18:05:50,312 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.41561744
2015-10-18 18:05:50,335 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:51,335 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:51,396 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.6209487
2015-10-18 18:05:51,676 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.5352021
2015-10-18 18:05:51,972 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.667
2015-10-18 18:05:52,277 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.45419794
2015-10-18 18:05:52,335 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:52,379 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:05:52,527 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.44964966
2015-10-18 18:05:52,830 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.667
2015-10-18 18:05:53,335 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:53,360 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.44870117
2015-10-18 18:05:54,335 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:54,842 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.5352021
2015-10-18 18:05:54,995 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.667
2015-10-18 18:05:55,335 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:55,393 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:05:55,416 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.49406132
2015-10-18 18:05:55,658 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.47419345
2015-10-18 18:05:55,833 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.68445116
2015-10-18 18:05:56,335 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:56,405 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.44950172
2015-10-18 18:05:57,290 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:05:57,292 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:05:57,292 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000016 to attempt_1445144423722_0022_m_000006_1
2015-10-18 18:05:57,292 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:7 RackLocal:7
2015-10-18 18:05:57,293 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:05:57,295 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:05:57,295 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000016 taskAttempt attempt_1445144423722_0022_m_000006_1
2015-10-18 18:05:57,295 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000006_1
2015-10-18 18:05:57,296 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:05:57,335 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:57,942 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0022_m_000004
2015-10-18 18:05:57,943 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:05:57,943 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0022_m_000004
2015-10-18 18:05:57,944 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:05:57,945 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:05:57,945 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000004_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:05:58,003 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.667
2015-10-18 18:05:58,111 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000006_1 : 13562
2015-10-18 18:05:58,112 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000006_1] using containerId: [container_1445144423722_0022_01_000016 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:05:58,112 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:05:58,113 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000006
2015-10-18 18:05:58,292 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:7 RackLocal:7
2015-10-18 18:05:58,294 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-27> knownNMs=4
2015-10-18 18:05:58,332 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.5352021
2015-10-18 18:05:58,335 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:58,400 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:05:58,846 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.73870134
2015-10-18 18:05:58,924 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.5222606
2015-10-18 18:05:59,334 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:05:59,356 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.50513405
2015-10-18 18:05:59,438 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.44950172
2015-10-18 18:06:00,300 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:06:00,300 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:06:00,300 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0022_01_000017 to attempt_1445144423722_0022_m_000004_1
2015-10-18 18:06:00,300 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:06:00,301 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:06:00,301 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000004_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:06:00,301 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0022_01_000017 taskAttempt attempt_1445144423722_0022_m_000004_1
2015-10-18 18:06:00,301 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0022_m_000004_1
2015-10-18 18:06:00,301 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:06:00,334 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:00,925 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0022_m_000004_1 : 13562
2015-10-18 18:06:00,926 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0022_m_000004_1] using containerId: [container_1445144423722_0022_01_000017 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:06:00,927 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000004_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:06:00,927 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0022_m_000004
2015-10-18 18:06:01,011 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.673636
2015-10-18 18:06:01,304 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0022: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-28> knownNMs=4
2015-10-18 18:06:01,336 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:01,410 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:06:01,852 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.7934606
2015-10-18 18:06:01,997 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.5352021
2015-10-18 18:06:02,336 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:02,448 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.53521925
2015-10-18 18:06:02,471 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.44950172
2015-10-18 18:06:02,949 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.5352825
2015-10-18 18:06:03,336 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:04,019 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.70181847
2015-10-18 18:06:04,336 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:04,432 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:06:04,860 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.8479123
2015-10-18 18:06:05,335 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:05,450 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.5352021
2015-10-18 18:06:05,515 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.44950172
2015-10-18 18:06:05,981 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.53521925
2015-10-18 18:06:06,335 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:06,427 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.5352825
2015-10-18 18:06:07,023 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.73349035
2015-10-18 18:06:07,335 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:07,440 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:06:07,860 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.9023462
2015-10-18 18:06:08,335 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:08,555 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.5291067
2015-10-18 18:06:09,121 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.5352021
2015-10-18 18:06:09,335 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:09,870 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.53521925
2015-10-18 18:06:10,035 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.7646142
2015-10-18 18:06:10,126 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:06:10,172 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.5352825
2015-10-18 18:06:10,335 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:10,446 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:06:10,493 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000016 asked for a task
2015-10-18 18:06:10,493 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000016 given task: attempt_1445144423722_0022_m_000006_1
2015-10-18 18:06:10,861 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 0.95659184
2015-10-18 18:06:11,335 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:11,593 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.53521925
2015-10-18 18:06:12,335 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:12,668 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.5352021
2015-10-18 18:06:13,035 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.79761904
2015-10-18 18:06:13,116 INFO [Socket Reader #1 for port 29630] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0022 (auth:SIMPLE)
2015-10-18 18:06:13,317 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0022_m_000017 asked for a task
2015-10-18 18:06:13,317 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0022_m_000017 given task: attempt_1445144423722_0022_m_000004_1
2015-10-18 18:06:13,322 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.53521925
2015-10-18 18:06:13,335 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:13,354 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_1 is : 1.0
2015-10-18 18:06:13,355 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0022_m_000007_1
2015-10-18 18:06:13,356 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000007_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:06:13,356 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000013 taskAttempt attempt_1445144423722_0022_m_000007_1
2015-10-18 18:06:13,356 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000007_1
2015-10-18 18:06:13,356 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:06:13,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000007_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:06:13,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0022_m_000007_1
2015-10-18 18:06:13,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0022_m_000007_0
2015-10-18 18:06:13,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:06:13,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-18 18:06:13,370 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000007_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:06:13,370 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000009 taskAttempt attempt_1445144423722_0022_m_000007_0
2015-10-18 18:06:13,370 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000007_0
2015-10-18 18:06:13,370 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:06:13,454 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.16666667
2015-10-18 18:06:13,464 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000007_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:06:13,465 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:06:13,473 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445144423722_0022_m_000007_0
2015-10-18 18:06:13,474 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000007_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:06:13,683 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000007_0 is : 0.5352825
2015-10-18 18:06:14,323 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:06:14,335 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 5 maxEvents 10000
2015-10-18 18:06:14,633 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.53521925
2015-10-18 18:06:15,045 INFO [Socket Reader #1 for port 29630] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 29630: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:06:15,330 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000013
2015-10-18 18:06:15,330 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:06:15,330 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000007_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:06:15,335 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:15,963 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.5382068
2015-10-18 18:06:16,041 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.8537284
2015-10-18 18:06:16,333 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000009
2015-10-18 18:06:16,333 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:06:16,334 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:06:16,336 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:16,457 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.20000002
2015-10-18 18:06:16,721 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.53521925
2015-10-18 18:06:17,336 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:17,670 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.53521925
2015-10-18 18:06:18,336 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:19,058 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.88822925
2015-10-18 18:06:19,164 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.5851919
2015-10-18 18:06:19,336 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:19,458 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.20000002
2015-10-18 18:06:19,832 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.53521925
2015-10-18 18:06:20,336 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:20,711 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.556112
2015-10-18 18:06:21,336 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:22,066 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.9186946
2015-10-18 18:06:22,335 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:22,457 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.20000002
2015-10-18 18:06:22,830 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.61911726
2015-10-18 18:06:23,335 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:23,696 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.53521925
2015-10-18 18:06:23,742 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.6207798
2015-10-18 18:06:24,335 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:24,992 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.03194851
2015-10-18 18:06:25,073 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.9572573
2015-10-18 18:06:25,336 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:25,456 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.20000002
2015-10-18 18:06:26,335 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:26,356 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.6209487
2015-10-18 18:06:26,602 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.037909247
2015-10-18 18:06:26,773 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.6207798
2015-10-18 18:06:27,136 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.53521925
2015-10-18 18:06:27,336 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:28,080 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 0.99154127
2015-10-18 18:06:28,335 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:28,393 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.07381743
2015-10-18 18:06:28,455 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.20000002
2015-10-18 18:06:28,668 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_1 is : 1.0
2015-10-18 18:06:28,670 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0022_m_000005_1
2015-10-18 18:06:28,670 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000005_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:06:28,671 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000014 taskAttempt attempt_1445144423722_0022_m_000005_1
2015-10-18 18:06:28,671 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000005_1
2015-10-18 18:06:28,673 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:06:28,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000005_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:06:28,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0022_m_000005_1
2015-10-18 18:06:28,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0022_m_000005_0
2015-10-18 18:06:28,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:06:28,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-18 18:06:28,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000005_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:06:28,691 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000007 taskAttempt attempt_1445144423722_0022_m_000005_0
2015-10-18 18:06:28,691 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000005_0
2015-10-18 18:06:28,692 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:06:28,969 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0022_m_000005
2015-10-18 18:06:28,969 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:06:29,335 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 6 maxEvents 10000
2015-10-18 18:06:29,356 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:06:29,804 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.6207798
2015-10-18 18:06:29,856 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000005_0 is : 0.6209487
2015-10-18 18:06:30,335 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:30,351 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000005_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:06:30,352 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:06:30,354 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.08182904
2015-10-18 18:06:30,354 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445144423722_0022_m_000005_0
2015-10-18 18:06:30,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000005_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:06:30,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000014
2015-10-18 18:06:30,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:06:30,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000005_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:06:30,769 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.53521925
2015-10-18 18:06:31,336 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:31,457 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.23333333
2015-10-18 18:06:31,482 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.6207798
2015-10-18 18:06:32,337 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:32,367 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.106964506
2015-10-18 18:06:32,369 INFO [Socket Reader #1 for port 29630] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 29630: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:06:32,837 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.667
2015-10-18 18:06:33,336 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:33,745 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.10680563
2015-10-18 18:06:34,337 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:34,358 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.53521925
2015-10-18 18:06:34,371 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000007
2015-10-18 18:06:34,371 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:06:34,371 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:06:34,465 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.23333333
2015-10-18 18:06:35,336 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:35,809 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.106964506
2015-10-18 18:06:35,876 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.667
2015-10-18 18:06:36,336 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:36,983 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.10680563
2015-10-18 18:06:37,336 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:37,473 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.23333333
2015-10-18 18:06:37,637 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.57700145
2015-10-18 18:06:38,336 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:38,906 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.67684275
2015-10-18 18:06:39,201 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.106964506
2015-10-18 18:06:39,336 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:40,336 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:40,480 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.23333333
2015-10-18 18:06:40,495 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.10680563
2015-10-18 18:06:41,047 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.61609197
2015-10-18 18:06:41,336 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:41,938 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.71224725
2015-10-18 18:06:42,336 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:42,590 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.106964506
2015-10-18 18:06:43,336 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:43,489 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.23333333
2015-10-18 18:06:43,901 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.10680563
2015-10-18 18:06:44,336 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:44,567 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.6207798
2015-10-18 18:06:44,968 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.7519724
2015-10-18 18:06:45,336 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:46,042 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.106964506
2015-10-18 18:06:46,337 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:46,512 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.23333333
2015-10-18 18:06:47,114 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.10680563
2015-10-18 18:06:47,337 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:47,979 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.6207798
2015-10-18 18:06:47,998 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.78345275
2015-10-18 18:06:48,337 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:49,333 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.106964506
2015-10-18 18:06:49,337 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:49,520 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.23333333
2015-10-18 18:06:50,337 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:50,543 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.10680563
2015-10-18 18:06:51,038 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.81892306
2015-10-18 18:06:51,264 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.6207798
2015-10-18 18:06:51,337 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:52,337 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:52,525 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.106964506
2015-10-18 18:06:52,526 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.23333333
2015-10-18 18:06:53,337 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:53,792 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.10680563
2015-10-18 18:06:54,079 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.8451604
2015-10-18 18:06:54,336 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:54,652 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.6207798
2015-10-18 18:06:55,337 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:55,537 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.23333333
2015-10-18 18:06:55,936 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.106964506
2015-10-18 18:06:56,336 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:57,027 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.10680563
2015-10-18 18:06:57,111 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.87992275
2015-10-18 18:06:57,336 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:57,853 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.6207798
2015-10-18 18:06:58,337 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:06:58,559 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.23333333
2015-10-18 18:06:59,152 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.1152265
2015-10-18 18:06:59,336 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:07:00,152 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.9106471
2015-10-18 18:07:00,277 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.11135346
2015-10-18 18:07:00,336 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:07:01,013 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.6207798
2015-10-18 18:07:01,336 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:07:01,567 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.23333333
2015-10-18 18:07:02,337 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:07:02,356 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.16935374
2015-10-18 18:07:03,183 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.9371851
2015-10-18 18:07:03,337 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:07:03,701 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.1673711
2015-10-18 18:07:04,337 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:07:04,559 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.6207798
2015-10-18 18:07:04,574 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.23333333
2015-10-18 18:07:05,337 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:07:05,794 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.19266446
2015-10-18 18:07:06,213 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.9605429
2015-10-18 18:07:06,337 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:07:07,000 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.19242907
2015-10-18 18:07:07,337 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:07:07,583 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.23333333
2015-10-18 18:07:07,762 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.65586036
2015-10-18 18:07:08,337 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:07:08,419 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_0 is : 0.65586036
2015-10-18 18:07:08,833 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.19266446
2015-10-18 18:07:09,244 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 0.99111605
2015-10-18 18:07:09,337 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:07:09,973 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000008_1 is : 1.0
2015-10-18 18:07:09,974 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0022_m_000008_1
2015-10-18 18:07:09,975 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000008_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:07:09,975 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000015 taskAttempt attempt_1445144423722_0022_m_000008_1
2015-10-18 18:07:09,976 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000008_1
2015-10-18 18:07:09,977 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:07:09,991 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000008_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:07:09,991 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0022_m_000008_1
2015-10-18 18:07:09,992 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0022_m_000008_0
2015-10-18 18:07:09,992 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:07:09,992 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-18 18:07:09,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000008_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:07:09,993 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000010 taskAttempt attempt_1445144423722_0022_m_000008_0
2015-10-18 18:07:09,994 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000008_0
2015-10-18 18:07:09,995 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:07:10,009 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0022_m_000008
2015-10-18 18:07:10,009 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:07:10,077 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.19242907
2015-10-18 18:07:10,080 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000008_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:07:10,081 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:07:10,088 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445144423722_0022_m_000008_0
2015-10-18 18:07:10,089 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000008_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:07:10,187 INFO [Socket Reader #1 for port 29630] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 29630: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:07:10,337 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 7 maxEvents 10000
2015-10-18 18:07:10,432 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:07:10,590 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.23333333
2015-10-18 18:07:11,337 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:11,437 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000015
2015-10-18 18:07:11,437 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000010
2015-10-18 18:07:11,437 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000008_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:07:11,437 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:07:11,437 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:07:11,869 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.19266446
2015-10-18 18:07:12,337 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:13,257 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.19242907
2015-10-18 18:07:13,337 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:13,599 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:14,337 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:14,902 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.19266446
2015-10-18 18:07:15,336 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:16,296 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.19242907
2015-10-18 18:07:16,337 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:16,604 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:17,337 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:17,932 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.19266446
2015-10-18 18:07:18,337 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:19,327 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.19242907
2015-10-18 18:07:19,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:19,615 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:20,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:20,984 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.24513456
2015-10-18 18:07:21,337 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:22,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:22,599 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.26042476
2015-10-18 18:07:22,622 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:23,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:24,056 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.2783809
2015-10-18 18:07:24,337 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:25,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:25,631 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:25,652 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.2781602
2015-10-18 18:07:26,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:27,090 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.2783809
2015-10-18 18:07:27,337 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:28,337 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:28,637 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:28,682 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.2781602
2015-10-18 18:07:29,337 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:30,125 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.2783809
2015-10-18 18:07:30,337 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:31,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:31,646 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:31,725 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.2781602
2015-10-18 18:07:32,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:33,158 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.2783809
2015-10-18 18:07:33,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:34,339 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:34,653 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:34,775 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.2781602
2015-10-18 18:07:35,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:36,201 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.30501854
2015-10-18 18:07:36,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:37,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:37,662 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:37,866 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.3246016
2015-10-18 18:07:38,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:39,297 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.36404583
2015-10-18 18:07:39,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:40,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:40,668 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:40,931 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.36388028
2015-10-18 18:07:41,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:42,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:42,353 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.36404583
2015-10-18 18:07:43,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:43,677 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:43,962 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.36388028
2015-10-18 18:07:44,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:45,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:45,386 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.36404583
2015-10-18 18:07:46,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:46,684 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:46,994 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.36388028
2015-10-18 18:07:47,339 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:48,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:48,417 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.36404583
2015-10-18 18:07:49,339 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:49,694 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:50,028 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.36388028
2015-10-18 18:07:50,339 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:51,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:51,457 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.3735598
2015-10-18 18:07:52,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:52,700 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:53,066 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.383329
2015-10-18 18:07:53,339 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:54,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:54,565 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.4321811
2015-10-18 18:07:55,339 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:55,709 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:56,206 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.44968578
2015-10-18 18:07:56,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:57,339 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:57,634 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.44980705
2015-10-18 18:07:58,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:07:58,715 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:07:59,260 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.44968578
2015-10-18 18:07:59,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:00,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:00,669 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.44980705
2015-10-18 18:08:01,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:01,725 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:02,296 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.44968578
2015-10-18 18:08:02,338 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:03,339 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:03,698 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.44980705
2015-10-18 18:08:04,339 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:04,732 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:05,330 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.44968578
2015-10-18 18:08:05,339 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:06,339 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:06,735 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.44980705
2015-10-18 18:08:07,339 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:07,741 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:08,339 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:08,372 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.4632093
2015-10-18 18:08:09,339 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:09,770 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.49408877
2015-10-18 18:08:10,339 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:10,741 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:11,339 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:11,471 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.5352028
2015-10-18 18:08:12,339 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:12,857 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.53543663
2015-10-18 18:08:13,339 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:13,763 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:14,338 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:14,510 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.5352028
2015-10-18 18:08:15,338 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:15,884 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.53543663
2015-10-18 18:08:16,339 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:16,778 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:17,338 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:17,542 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.5352028
2015-10-18 18:08:18,339 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:18,932 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.53543663
2015-10-18 18:08:19,340 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:19,789 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:20,339 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:20,574 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.5352028
2015-10-18 18:08:21,339 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:21,969 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.53543663
2015-10-18 18:08:22,339 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:22,795 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:23,339 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:23,613 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.5445014
2015-10-18 18:08:24,339 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:25,002 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.53543663
2015-10-18 18:08:25,339 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:25,804 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:26,339 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:26,681 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.6208445
2015-10-18 18:08:27,339 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:28,109 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.6047682
2015-10-18 18:08:28,339 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:28,811 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:29,339 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:29,766 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.6208445
2015-10-18 18:08:30,339 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:31,152 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.6210422
2015-10-18 18:08:31,339 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:31,819 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:32,339 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:32,792 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.6208445
2015-10-18 18:08:33,340 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:34,185 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.6210422
2015-10-18 18:08:34,340 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:34,827 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:35,340 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:35,826 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.6208445
2015-10-18 18:08:36,340 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:37,222 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.6210422
2015-10-18 18:08:37,340 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:37,836 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:38,340 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:38,856 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.6208445
2015-10-18 18:08:39,340 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:40,251 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.6210422
2015-10-18 18:08:40,340 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:40,842 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:41,339 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:41,777 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.6208445
2015-10-18 18:08:41,885 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.667
2015-10-18 18:08:42,340 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:43,278 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.6596632
2015-10-18 18:08:43,340 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:43,562 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.6596632
2015-10-18 18:08:43,852 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:44,339 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:44,916 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.667
2015-10-18 18:08:45,339 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:46,310 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.667
2015-10-18 18:08:46,339 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:46,856 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:47,340 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:47,954 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.667
2015-10-18 18:08:48,339 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:49,341 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:49,342 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.667
2015-10-18 18:08:49,875 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:50,340 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:50,981 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.667
2015-10-18 18:08:51,340 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:52,340 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:52,375 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.667
2015-10-18 18:08:52,891 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:53,340 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:54,093 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.6847031
2015-10-18 18:08:54,340 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:55,341 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:55,403 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.67852324
2015-10-18 18:08:55,905 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:56,340 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:57,125 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.7111986
2015-10-18 18:08:57,340 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:58,340 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:08:58,435 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.7051297
2015-10-18 18:08:58,914 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:08:59,340 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:00,156 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.73962724
2015-10-18 18:09:00,340 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:01,340 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:01,469 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.7333324
2015-10-18 18:09:01,921 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:09:02,340 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:03,197 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.76805055
2015-10-18 18:09:03,340 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:04,340 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:04,503 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.76132
2015-10-18 18:09:04,931 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:09:05,341 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:06,232 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.7967445
2015-10-18 18:09:06,341 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:07,341 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:07,528 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.7895916
2015-10-18 18:09:07,938 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:09:08,341 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:09,265 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.8251029
2015-10-18 18:09:09,341 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:10,341 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:10,569 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.8160007
2015-10-18 18:09:10,946 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:09:11,341 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:12,298 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.8514066
2015-10-18 18:09:12,340 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:13,341 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:13,609 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.843411
2015-10-18 18:09:13,953 INFO [IPC Server handler 26 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:09:14,341 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:15,326 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.8797944
2015-10-18 18:09:15,340 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:16,341 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:16,641 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.8683388
2015-10-18 18:09:16,961 INFO [IPC Server handler 14 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:09:17,340 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:18,340 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:18,357 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.9063169
2015-10-18 18:09:19,342 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:19,668 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.89717686
2015-10-18 18:09:19,969 INFO [IPC Server handler 21 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:09:20,341 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:21,342 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:21,386 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.93481076
2015-10-18 18:09:22,342 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:22,717 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.92466044
2015-10-18 18:09:22,979 INFO [IPC Server handler 19 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:09:23,341 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:24,342 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:24,419 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.9630363
2015-10-18 18:09:25,341 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:25,746 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.95229286
2015-10-18 18:09:25,985 INFO [IPC Server handler 23 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:09:26,341 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:27,341 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:27,450 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 0.9908907
2015-10-18 18:09:28,341 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:28,607 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000004_1 is : 1.0
2015-10-18 18:09:28,611 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0022_m_000004_1
2015-10-18 18:09:28,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000004_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:09:28,612 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000017 taskAttempt attempt_1445144423722_0022_m_000004_1
2015-10-18 18:09:28,612 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000004_1
2015-10-18 18:09:28,614 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:09:28,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000004_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:09:28,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0022_m_000004_1
2015-10-18 18:09:28,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0022_m_000004_0
2015-10-18 18:09:28,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:09:28,644 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-18 18:09:28,645 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000004_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:09:28,645 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000006 taskAttempt attempt_1445144423722_0022_m_000004_0
2015-10-18 18:09:28,645 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000004_0
2015-10-18 18:09:28,647 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 18:09:28,654 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:09:28,789 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 0.97979736
2015-10-18 18:09:29,000 INFO [IPC Server handler 13 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:09:29,208 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0022_m_000004
2015-10-18 18:09:29,208 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:09:29,340 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 8 maxEvents 10000
2015-10-18 18:09:29,659 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000017
2015-10-18 18:09:29,659 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:09:29,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000004_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:09:30,341 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 18:09:31,210 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_m_000006_1 is : 1.0
2015-10-18 18:09:31,250 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0022_m_000006_1
2015-10-18 18:09:31,250 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000006_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:09:31,251 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000016 taskAttempt attempt_1445144423722_0022_m_000006_1
2015-10-18 18:09:31,251 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000006_1
2015-10-18 18:09:31,251 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:09:31,338 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000006_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:09:31,338 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0022_m_000006_1
2015-10-18 18:09:31,339 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0022_m_000006_0
2015-10-18 18:09:31,339 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:09:31,339 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-18 18:09:31,340 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000006_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:09:31,340 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000008 taskAttempt attempt_1445144423722_0022_m_000006_0
2015-10-18 18:09:31,341 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_m_000006_0
2015-10-18 18:09:31,341 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 18:09:31,341 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 9 maxEvents 10000
2015-10-18 18:09:31,661 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:09:32,009 INFO [IPC Server handler 6 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:09:32,341 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:32,666 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000016
2015-10-18 18:09:32,666 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:09:32,666 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000006_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:09:33,341 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:34,341 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:35,016 INFO [IPC Server handler 28 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:09:35,342 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:36,342 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:37,342 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:38,034 INFO [IPC Server handler 7 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:09:38,341 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:39,342 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:40,342 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:41,041 INFO [IPC Server handler 18 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.26666668
2015-10-18 18:09:41,342 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:42,342 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:43,342 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:44,050 INFO [IPC Server handler 29 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.3
2015-10-18 18:09:44,209 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0022_m_000006
2015-10-18 18:09:44,209 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:09:44,341 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:45,342 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:46,342 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:47,056 INFO [IPC Server handler 16 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.3
2015-10-18 18:09:47,341 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:48,341 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:48,650 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 0 time(s); maxRetries=45
2015-10-18 18:09:49,342 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:50,063 INFO [IPC Server handler 15 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.3
2015-10-18 18:09:50,341 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:51,342 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0022_r_000000_0. startIndex 10 maxEvents 10000
2015-10-18 18:09:51,344 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 0 time(s); maxRetries=45
2015-10-18 18:09:52,099 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.3
2015-10-18 18:09:52,130 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.3
2015-10-18 18:09:53,079 INFO [IPC Server handler 11 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.6698621
2015-10-18 18:09:56,095 INFO [IPC Server handler 12 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.68696696
2015-10-18 18:09:59,104 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.6954033
2015-10-18 18:09:59,210 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0022_m_000006
2015-10-18 18:09:59,210 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:10:02,104 INFO [IPC Server handler 3 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.6954033
2015-10-18 18:10:08,654 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 1 time(s); maxRetries=45
2015-10-18 18:10:11,345 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 1 time(s); maxRetries=45
2015-10-18 18:10:17,108 INFO [IPC Server handler 24 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.7163867
2015-10-18 18:10:20,119 INFO [IPC Server handler 10 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.7357901
2015-10-18 18:10:23,128 INFO [IPC Server handler 20 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.7616735
2015-10-18 18:10:26,137 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.7820563
2015-10-18 18:10:28,655 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 2 time(s); maxRetries=45
2015-10-18 18:10:29,135 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.8019711
2015-10-18 18:10:31,346 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 2 time(s); maxRetries=45
2015-10-18 18:10:32,136 INFO [IPC Server handler 17 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.8220396
2015-10-18 18:10:35,140 INFO [IPC Server handler 0 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.8388602
2015-10-18 18:10:38,152 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.85967207
2015-10-18 18:10:41,152 INFO [IPC Server handler 9 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.88368
2015-10-18 18:10:44,159 INFO [IPC Server handler 22 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.9121337
2015-10-18 18:10:47,167 INFO [IPC Server handler 4 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.9406432
2015-10-18 18:10:48,658 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 3 time(s); maxRetries=45
2015-10-18 18:10:50,174 INFO [IPC Server handler 27 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.96913236
2015-10-18 18:10:51,349 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 3 time(s); maxRetries=45
2015-10-18 18:10:53,184 INFO [IPC Server handler 2 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 0.99753463
2015-10-18 18:10:53,596 INFO [IPC Server handler 8 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445144423722_0022_r_000000_0
2015-10-18 18:10:53,596 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-18 18:10:53,597 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445144423722_0022_r_000000_0 given a go for committing the task output.
2015-10-18 18:10:53,598 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445144423722_0022_r_000000_0
2015-10-18 18:10:53,598 INFO [IPC Server handler 1 on 29630] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445144423722_0022_r_000000_0:true
2015-10-18 18:10:53,618 INFO [IPC Server handler 25 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0022_r_000000_0 is : 1.0
2015-10-18 18:10:53,620 INFO [IPC Server handler 5 on 29630] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0022_r_000000_0
2015-10-18 18:10:53,620 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:10:53,621 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0022_01_000012 taskAttempt attempt_1445144423722_0022_r_000000_0
2015-10-18 18:10:53,621 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0022_r_000000_0
2015-10-18 18:10:53,623 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:10:53,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:10:53,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0022_r_000000_0
2015-10-18 18:10:53,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0022_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:10:53,644 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-18 18:10:53,645 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0022Job Transitioned from RUNNING to COMMITTING
2015-10-18 18:10:53,646 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-18 18:10:53,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-18 18:10:53,708 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0022Job Transitioned from COMMITTING to SUCCEEDED
2015-10-18 18:10:53,710 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-18 18:10:53,710 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-18 18:10:53,710 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-18 18:10:53,710 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-18 18:10:53,710 INFO [Thread-115] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-18 18:10:53,710 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-18 18:10:53,712 INFO [Thread-115] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-18 18:10:53,792 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:10:53,812 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0022/job_1445144423722_0022_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0022-1445162506766-msrabi-pagerank-1445163053703-10-1-SUCCEEDED-default-1445162512818.jhist_tmp
2015-10-18 18:10:53,886 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0022-1445162506766-msrabi-pagerank-1445163053703-10-1-SUCCEEDED-default-1445162512818.jhist_tmp
2015-10-18 18:10:53,890 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0022/job_1445144423722_0022_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0022_conf.xml_tmp
2015-10-18 18:10:53,957 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0022_conf.xml_tmp
2015-10-18 18:10:53,962 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0022.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0022.summary
2015-10-18 18:10:53,964 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0022_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0022_conf.xml
2015-10-18 18:10:53,967 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0022-1445162506766-msrabi-pagerank-1445163053703-10-1-SUCCEEDED-default-1445162512818.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0022-1445162506766-msrabi-pagerank-1445163053703-10-1-SUCCEEDED-default-1445162512818.jhist
2015-10-18 18:10:53,968 INFO [Thread-115] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-18 18:10:54,796 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000012
2015-10-18 18:10:54,797 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:10:54,797 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:11:08,661 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 4 time(s); maxRetries=45
2015-10-18 18:11:11,351 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 4 time(s); maxRetries=45
2015-10-18 18:11:28,664 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 5 time(s); maxRetries=45
2015-10-18 18:11:31,353 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 5 time(s); maxRetries=45
2015-10-18 18:11:48,665 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 6 time(s); maxRetries=45
2015-10-18 18:11:51,355 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 6 time(s); maxRetries=45
2015-10-18 18:12:08,669 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 7 time(s); maxRetries=45
2015-10-18 18:12:11,358 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 7 time(s); maxRetries=45
2015-10-18 18:12:28,671 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 8 time(s); maxRetries=45
2015-10-18 18:12:31,360 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 8 time(s); maxRetries=45
2015-10-18 18:12:48,673 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 9 time(s); maxRetries=45
2015-10-18 18:12:51,362 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 9 time(s); maxRetries=45
2015-10-18 18:13:08,674 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 10 time(s); maxRetries=45
2015-10-18 18:13:11,364 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 10 time(s); maxRetries=45
2015-10-18 18:13:28,678 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 11 time(s); maxRetries=45
2015-10-18 18:13:31,365 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 11 time(s); maxRetries=45
2015-10-18 18:13:48,679 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 12 time(s); maxRetries=45
2015-10-18 18:13:51,367 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 12 time(s); maxRetries=45
2015-10-18 18:14:08,681 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 13 time(s); maxRetries=45
2015-10-18 18:14:11,369 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 13 time(s); maxRetries=45
2015-10-18 18:14:28,684 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 14 time(s); maxRetries=45
2015-10-18 18:14:31,371 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 14 time(s); maxRetries=45
2015-10-18 18:14:48,686 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 15 time(s); maxRetries=45
2015-10-18 18:14:51,372 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 15 time(s); maxRetries=45
2015-10-18 18:15:08,688 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 16 time(s); maxRetries=45
2015-10-18 18:15:11,373 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 16 time(s); maxRetries=45
2015-10-18 18:15:28,692 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 17 time(s); maxRetries=45
2015-10-18 18:15:31,375 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 17 time(s); maxRetries=45
2015-10-18 18:15:48,693 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 18 time(s); maxRetries=45
2015-10-18 18:15:51,376 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 18 time(s); maxRetries=45
2015-10-18 18:16:08,695 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 19 time(s); maxRetries=45
2015-10-18 18:16:11,377 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 19 time(s); maxRetries=45
2015-10-18 18:16:28,697 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 20 time(s); maxRetries=45
2015-10-18 18:16:31,379 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 20 time(s); maxRetries=45
2015-10-18 18:16:48,700 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 21 time(s); maxRetries=45
2015-10-18 18:16:51,380 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 21 time(s); maxRetries=45
2015-10-18 18:17:04,365 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445144423722_0022_m_000004_0 because it is running on unusable node:MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 18:17:04,365 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445144423722_0022_m_000006_0 because it is running on unusable node:MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-18 18:17:04,365 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000008
2015-10-18 18:17:04,365 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0022_01_000006
2015-10-18 18:17:04,365 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000006_0: Container released on a *lost* node
2015-10-18 18:17:04,365 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:17:04,365 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0022_m_000004_0: Container released on a *lost* node
2015-10-18 18:17:08,702 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 22 time(s); maxRetries=45
2015-10-18 18:17:11,381 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 22 time(s); maxRetries=45
2015-10-18 18:17:28,703 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 23 time(s); maxRetries=45
2015-10-18 18:17:31,384 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 23 time(s); maxRetries=45
2015-10-18 18:17:48,705 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 24 time(s); maxRetries=45
2015-10-18 18:17:51,385 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 24 time(s); maxRetries=45
2015-10-18 18:18:08,706 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 25 time(s); maxRetries=45
2015-10-18 18:18:11,386 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 25 time(s); maxRetries=45
2015-10-18 18:18:28,707 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 26 time(s); maxRetries=45
2015-10-18 18:18:31,387 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 26 time(s); maxRetries=45
2015-10-18 18:18:48,710 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 27 time(s); maxRetries=45
2015-10-18 18:18:51,389 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 27 time(s); maxRetries=45
2015-10-18 18:19:08,711 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 28 time(s); maxRetries=45
2015-10-18 18:19:11,391 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 28 time(s); maxRetries=45
2015-10-18 18:19:28,712 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 29 time(s); maxRetries=45
2015-10-18 18:19:31,392 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 29 time(s); maxRetries=45
2015-10-18 18:19:48,714 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 30 time(s); maxRetries=45
2015-10-18 18:19:51,395 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 30 time(s); maxRetries=45
2015-10-18 18:20:08,716 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 31 time(s); maxRetries=45
2015-10-18 18:20:11,396 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 31 time(s); maxRetries=45
2015-10-18 18:20:28,717 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 32 time(s); maxRetries=45
2015-10-18 18:20:31,397 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 32 time(s); maxRetries=45
2015-10-18 18:20:48,719 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 33 time(s); maxRetries=45
2015-10-18 18:20:51,399 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 33 time(s); maxRetries=45
2015-10-18 18:21:08,721 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 34 time(s); maxRetries=45
2015-10-18 18:21:11,400 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 34 time(s); maxRetries=45
2015-10-18 18:21:28,722 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 35 time(s); maxRetries=45
2015-10-18 18:21:31,402 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 35 time(s); maxRetries=45
2015-10-18 18:21:48,723 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 36 time(s); maxRetries=45
2015-10-18 18:21:51,404 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 36 time(s); maxRetries=45
2015-10-18 18:22:08,725 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 37 time(s); maxRetries=45
2015-10-18 18:22:11,405 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 37 time(s); maxRetries=45
2015-10-18 18:22:28,726 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 38 time(s); maxRetries=45
2015-10-18 18:22:31,406 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 38 time(s); maxRetries=45
2015-10-18 18:22:48,729 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 39 time(s); maxRetries=45
2015-10-18 18:22:51,408 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 39 time(s); maxRetries=45
2015-10-18 18:23:08,732 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 40 time(s); maxRetries=45
2015-10-18 18:23:11,411 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 40 time(s); maxRetries=45
2015-10-18 18:23:28,738 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 41 time(s); maxRetries=45
2015-10-18 18:23:31,413 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 41 time(s); maxRetries=45
2015-10-18 18:23:48,739 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 42 time(s); maxRetries=45
2015-10-18 18:23:51,415 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 42 time(s); maxRetries=45
2015-10-18 18:24:08,742 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 43 time(s); maxRetries=45
2015-10-18 18:24:11,418 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 43 time(s); maxRetries=45
2015-10-18 18:24:28,745 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 44 time(s); maxRetries=45
2015-10-18 18:24:31,420 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 44 time(s); maxRetries=45
2015-10-18 18:25:18,757 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 0 time(s); maxRetries=45
2015-10-18 18:25:21,425 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 0 time(s); maxRetries=45
2015-10-18 18:25:38,758 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 1 time(s); maxRetries=45
2015-10-18 18:25:41,428 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 1 time(s); maxRetries=45
2015-10-18 18:25:58,762 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 2 time(s); maxRetries=45
2015-10-18 18:26:01,430 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 2 time(s); maxRetries=45
2015-10-18 18:26:18,763 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 3 time(s); maxRetries=45
2015-10-18 18:26:21,432 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 3 time(s); maxRetries=45
2015-10-18 18:26:38,764 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 4 time(s); maxRetries=45
2015-10-18 18:26:41,433 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 4 time(s); maxRetries=45
2015-10-18 18:26:58,766 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 5 time(s); maxRetries=45
2015-10-18 18:27:01,435 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 5 time(s); maxRetries=45
2015-10-18 18:27:18,769 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 6 time(s); maxRetries=45
2015-10-18 18:27:21,437 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 6 time(s); maxRetries=45
2015-10-18 18:27:38,771 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 7 time(s); maxRetries=45
2015-10-18 18:27:41,438 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 7 time(s); maxRetries=45
2015-10-18 18:27:58,774 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 8 time(s); maxRetries=45
2015-10-18 18:28:01,440 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 8 time(s); maxRetries=45
2015-10-18 18:28:18,777 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 9 time(s); maxRetries=45
2015-10-18 18:28:21,441 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 9 time(s); maxRetries=45
2015-10-18 18:28:38,780 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 10 time(s); maxRetries=45
2015-10-18 18:28:41,442 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:52368. Already tried 10 time(s); maxRetries=45
2015-10-18 18:28:44,508 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000006_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:28:44,509 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:28:44,516 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445144423722_0022_m_000006_0
2015-10-18 18:28:44,517 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000006_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:28:47,814 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000004_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:28:47,816 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:28:47,818 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-18 18:28:47,819 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445144423722_0022_m_000004_0
2015-10-18 18:28:47,819 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0022_m_000004_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:28:47,820 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-41.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445144423722_0022
2015-10-18 18:28:47,831 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-18 18:28:48,833 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-18 18:28:48,835 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0022
2015-10-18 18:28:48,845 INFO [Thread-115] org.apache.hadoop.ipc.Server: Stopping server on 29630
2015-10-18 18:28:48,849 INFO [IPC Server listener on 29630] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 29630
2015-10-18 18:28:48,850 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-18 18:28:48,850 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
