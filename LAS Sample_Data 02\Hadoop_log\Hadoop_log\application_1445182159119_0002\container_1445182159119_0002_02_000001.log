2015-10-19 14:32:15,446 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445182159119_0002_000002
2015-10-19 14:32:16,312 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-19 14:32:16,312 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 2 cluster_timestamp: 1445182159119 } attemptId: 2 } keyId: 1694045684)
2015-10-19 14:32:16,499 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-19 14:32:17,999 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-19 14:32:18,107 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-19 14:32:18,177 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-19 14:32:18,179 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-19 14:32:18,182 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-19 14:32:18,185 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-19 14:32:18,186 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-19 14:32:18,202 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-19 14:32:18,203 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-19 14:32:18,206 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-19 14:32:18,307 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 14:32:18,349 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 14:32:18,391 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 14:32:18,410 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-19 14:32:18,415 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Recovery is enabled. Will try to recover from previous life on best effort basis.
2015-10-19 14:32:18,458 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 14:32:18,466 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Previous history file is at hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0002/job_1445182159119_0002_1.jhist
2015-10-19 14:32:21,274 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445182159119_0002_m_000001
2015-10-19 14:32:21,275 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445182159119_0002_m_000000
2015-10-19 14:32:21,275 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445182159119_0002_m_000003
2015-10-19 14:32:21,276 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445182159119_0002_m_000002
2015-10-19 14:32:21,276 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445182159119_0002_m_000005
2015-10-19 14:32:21,276 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445182159119_0002_m_000004
2015-10-19 14:32:21,277 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445182159119_0002_m_000007
2015-10-19 14:32:21,278 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445182159119_0002_m_000006
2015-10-19 14:32:21,278 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445182159119_0002_m_000009
2015-10-19 14:32:21,279 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445182159119_0002_m_000008
2015-10-19 14:32:21,279 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read completed tasks from history 10
2015-10-19 14:32:21,366 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-19 14:32:21,467 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 14:32:21,625 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 14:32:21,625 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-19 14:32:21,686 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445182159119_0002 to jobTokenSecretManager
2015-10-19 14:32:21,793 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445182159119_0002 because: not enabled; too many maps; too much input;
2015-10-19 14:32:21,841 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445182159119_0002 = 1313861632. Number of splits = 10
2015-10-19 14:32:21,845 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445182159119_0002 = 1
2015-10-19 14:32:21,845 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0002Job Transitioned from NEW to INITED
2015-10-19 14:32:21,849 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445182159119_0002.
2015-10-19 14:32:21,945 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 14:32:21,972 INFO [Socket Reader #1 for port 44079] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 44079
2015-10-19 14:32:22,039 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-19 14:32:22,040 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 14:32:22,040 INFO [IPC Server listener on 44079] org.apache.hadoop.ipc.Server: IPC Server listener on 44079: starting
2015-10-19 14:32:22,043 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-41.fareast.corp.microsoft.com/**************:44079
2015-10-19 14:32:22,241 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-19 14:32:22,252 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-19 14:32:22,280 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-19 14:32:22,293 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-19 14:32:22,293 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-19 14:32:22,302 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-19 14:32:22,302 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-19 14:32:22,327 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 44086
2015-10-19 14:32:22,328 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-19 14:32:22,407 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_44086_mapreduce____25aoqz\webapp
2015-10-19 14:32:22,769 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:44086
2015-10-19 14:32:22,769 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 44086
2015-10-19 14:32:23,584 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-19 14:32:23,591 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445182159119_0002
2015-10-19 14:32:23,594 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 14:32:23,601 INFO [Socket Reader #1 for port 44089] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 44089
2015-10-19 14:32:23,611 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 14:32:23,611 INFO [IPC Server listener on 44089] org.apache.hadoop.ipc.Server: IPC Server listener on 44089: starting
2015-10-19 14:32:23,652 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-19 14:32:23,653 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-19 14:32:23,653 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-19 14:32:23,768 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at MSRA-SA-41/**************:8030
2015-10-19 14:32:23,911 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-19 14:32:23,911 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-19 14:32:23,917 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-19 14:32:23,922 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-19 14:32:23,929 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0002Job Transitioned from INITED to SETUP
2015-10-19 14:32:23,933 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-19 14:32:23,957 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0002Job Transitioned from SETUP to RUNNING
2015-10-19 14:32:23,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445182159119_0002_m_000000 from prior app attempt, status was SUCCEEDED
2015-10-19 14:32:23,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:23,998 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,020 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,024 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000000_0] using containerId: [container_1445182159119_0002_01_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:32:24,031 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000000_0 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000000_0
2015-10-19 14:32:24,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000000 Task Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445182159119_0002_m_000001 from prior app attempt, status was SUCCEEDED
2015-10-19 14:32:24,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000001_0] using containerId: [container_1445182159119_0002_01_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:32:24,050 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445182159119_0002, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0002/job_1445182159119_0002_2.jhist
2015-10-19 14:32:24,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000001_0 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000001_0
2015-10-19 14:32:24,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000001 Task Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,052 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445182159119_0002_m_000002 from prior app attempt, status was SUCCEEDED
2015-10-19 14:32:24,052 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,052 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,053 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,053 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000002_1] using containerId: [container_1445182159119_0002_01_000015 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:32:24,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000002_1 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,056 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,057 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000002_0] using containerId: [container_1445182159119_0002_01_000004 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 14:32:24,058 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000002_0 TaskAttempt Transitioned from NEW to KILLED
2015-10-19 14:32:24,058 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000002_1
2015-10-19 14:32:24,058 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000002 Task Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,059 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445182159119_0002_m_000003 from prior app attempt, status was SUCCEEDED
2015-10-19 14:32:24,059 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,059 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000003_1] using containerId: [container_1445182159119_0002_01_000013 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:32:24,061 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000003_1 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,061 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,061 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000003_0] using containerId: [container_1445182159119_0002_01_000005 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 14:32:24,063 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000003_0 TaskAttempt Transitioned from NEW to KILLED
2015-10-19 14:32:24,063 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000003_1
2015-10-19 14:32:24,063 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000003 Task Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445182159119_0002_m_000004 from prior app attempt, status was SUCCEEDED
2015-10-19 14:32:24,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,065 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,065 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000004_1] using containerId: [container_1445182159119_0002_01_000017 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:32:24,066 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000004_1 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,066 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,066 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,067 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000004_0] using containerId: [container_1445182159119_0002_01_000006 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 14:32:24,069 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000004_0 TaskAttempt Transitioned from NEW to KILLED
2015-10-19 14:32:24,069 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000004_1
2015-10-19 14:32:24,069 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000004 Task Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,069 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445182159119_0002_m_000005 from prior app attempt, status was SUCCEEDED
2015-10-19 14:32:24,070 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,070 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,070 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,071 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000005_1] using containerId: [container_1445182159119_0002_01_000014 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:32:24,071 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000005_1 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,072 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,072 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,073 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,073 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000005_0] using containerId: [container_1445182159119_0002_01_000007 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 14:32:24,073 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000005_0 TaskAttempt Transitioned from NEW to KILLED
2015-10-19 14:32:24,074 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000005_1
2015-10-19 14:32:24,074 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000005 Task Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,074 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445182159119_0002_m_000006 from prior app attempt, status was SUCCEEDED
2015-10-19 14:32:24,075 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,075 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,075 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000006_1] using containerId: [container_1445182159119_0002_01_000018 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:32:24,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000006_1 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,077 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,077 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,077 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,078 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000006_0] using containerId: [container_1445182159119_0002_01_000008 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 14:32:24,079 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000006_0 TaskAttempt Transitioned from NEW to KILLED
2015-10-19 14:32:24,079 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000006_1
2015-10-19 14:32:24,079 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000006 Task Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,079 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445182159119_0002_m_000007 from prior app attempt, status was SUCCEEDED
2015-10-19 14:32:24,080 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,080 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,080 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,081 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000007_0] using containerId: [container_1445182159119_0002_01_000009 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 14:32:24,081 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000007_0 TaskAttempt Transitioned from NEW to FAILED
2015-10-19 14:32:24,082 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,082 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,083 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,083 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000007_1] using containerId: [container_1445182159119_0002_01_000019 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:32:24,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000007_1 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000007_1
2015-10-19 14:32:24,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000007 Task Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,085 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445182159119_0002_m_000008 from prior app attempt, status was SUCCEEDED
2015-10-19 14:32:24,086 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,086 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,086 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,087 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000008_1] using containerId: [container_1445182159119_0002_01_000016 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:32:24,087 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000008_1 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,088 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,088 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,089 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,090 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000008_0] using containerId: [container_1445182159119_0002_01_000010 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 14:32:24,090 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000008_0 TaskAttempt Transitioned from NEW to KILLED
2015-10-19 14:32:24,091 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000008_1
2015-10-19 14:32:24,091 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000008 Task Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,091 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445182159119_0002_m_000009 from prior app attempt, status was SUCCEEDED
2015-10-19 14:32:24,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000009_1] using containerId: [container_1445182159119_0002_01_000020 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:32:24,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000009_1 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:24,095 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000009_0] using containerId: [container_1445182159119_0002_01_000011 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 14:32:24,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000009_0 TaskAttempt Transitioned from NEW to KILLED
2015-10-19 14:32:24,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000009_1
2015-10-19 14:32:24,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000009 Task Transitioned from NEW to SUCCEEDED
2015-10-19 14:32:24,097 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:32:24,107 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-19 14:32:24,107 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-19 14:32:24,108 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-19 14:32:24,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-19 14:32:24,110 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-19 14:32:24,111 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-19 14:32:24,112 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-19 14:32:24,113 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-19 14:32:24,113 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-19 14:32:24,114 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-19 14:32:24,117 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_r_000000_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:32:24,119 INFO [Thread-51] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-19 14:32:24,914 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 14:32:25,008 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:12288, vCores:-8>
2015-10-19 14:32:25,009 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-19 14:32:25,009 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-19 14:32:25,019 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 14:32:26,022 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:12288, vCores:-8> knownNMs=3
2015-10-19 14:32:27,036 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:32:27,038 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-19 14:32:27,041 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_02_000002 to attempt_1445182159119_0002_r_000000_1000
2015-10-19 14:32:27,041 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 14:32:27,111 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:32:27,148 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0002/job.jar
2015-10-19 14:32:27,153 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0002/job.xml
2015-10-19 14:32:27,155 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-19 14:32:27,156 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-19 14:32:27,156 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-19 14:32:27,291 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_r_000000_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:32:27,295 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_02_000002 taskAttempt attempt_1445182159119_0002_r_000000_1000
2015-10-19 14:32:27,300 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_r_000000_1000
2015-10-19 14:32:27,303 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:32:27,433 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_r_000000_1000 : 13562
2015-10-19 14:32:27,437 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_r_000000_1000] using containerId: [container_1445182159119_0002_02_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:32:27,439 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_r_000000_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:32:27,440 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_r_000000
2015-10-19 14:32:27,440 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:32:28,044 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-9> knownNMs=3
2015-10-19 14:32:30,789 INFO [Socket Reader #1 for port 44089] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:32:30,834 INFO [IPC Server handler 1 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_r_000002 asked for a task
2015-10-19 14:32:30,835 INFO [IPC Server handler 1 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_r_000002 given task: attempt_1445182159119_0002_r_000000_1000
2015-10-19 14:32:32,435 INFO [IPC Server handler 2 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 0 maxEvents 10000
2015-10-19 14:32:33,449 INFO [IPC Server handler 4 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:34,449 INFO [IPC Server handler 4 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:35,449 INFO [IPC Server handler 4 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:36,449 INFO [IPC Server handler 4 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:37,449 INFO [IPC Server handler 4 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:38,335 INFO [IPC Server handler 4 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.16666667
2015-10-19 14:32:38,448 INFO [IPC Server handler 3 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:39,448 INFO [IPC Server handler 3 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:40,448 INFO [IPC Server handler 3 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:41,372 INFO [IPC Server handler 3 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.20000002
2015-10-19 14:32:41,448 INFO [IPC Server handler 12 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:42,450 INFO [IPC Server handler 26 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:43,450 INFO [IPC Server handler 26 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:44,388 INFO [IPC Server handler 12 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.23333333
2015-10-19 14:32:44,449 INFO [IPC Server handler 26 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:45,449 INFO [IPC Server handler 26 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:46,450 INFO [IPC Server handler 26 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:47,407 INFO [IPC Server handler 26 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.26666668
2015-10-19 14:32:47,451 INFO [IPC Server handler 5 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:48,450 INFO [IPC Server handler 5 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:49,449 INFO [IPC Server handler 5 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:50,413 INFO [IPC Server handler 5 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.3
2015-10-19 14:32:50,449 INFO [IPC Server handler 11 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_1000. startIndex 11 maxEvents 10000
2015-10-19 14:32:51,202 INFO [IPC Server handler 2 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.3
2015-10-19 14:32:53,431 INFO [IPC Server handler 11 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.33333334
2015-10-19 14:32:56,446 INFO [IPC Server handler 8 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.33333334
2015-10-19 14:33:09,558 INFO [IPC Server handler 0 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.33333334
2015-10-19 14:33:14,466 INFO [IPC Server handler 0 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.67065865
2015-10-19 14:33:17,486 INFO [IPC Server handler 29 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.6718097
2015-10-19 14:33:20,503 INFO [IPC Server handler 13 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.6738915
2015-10-19 14:33:23,520 INFO [IPC Server handler 20 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.6757533
2015-10-19 14:33:26,538 INFO [IPC Server handler 7 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.67696756
2015-10-19 14:33:29,555 INFO [IPC Server handler 27 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.6784481
2015-10-19 14:33:32,571 INFO [IPC Server handler 6 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.68009144
2015-10-19 14:33:35,585 INFO [IPC Server handler 18 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.6821044
2015-10-19 14:33:38,603 INFO [IPC Server handler 23 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.68452257
2015-10-19 14:33:41,617 INFO [IPC Server handler 25 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.68656415
2015-10-19 14:33:44,634 INFO [IPC Server handler 14 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.68848103
2015-10-19 14:33:47,646 INFO [IPC Server handler 28 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.6901845
2015-10-19 14:33:50,665 INFO [IPC Server handler 24 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.69227237
2015-10-19 14:33:53,678 INFO [IPC Server handler 1 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.69504374
2015-10-19 14:33:56,697 INFO [IPC Server handler 2 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.6975108
2015-10-19 14:33:59,712 INFO [IPC Server handler 4 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.6996297
2015-10-19 14:34:02,730 INFO [IPC Server handler 3 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7012763
2015-10-19 14:34:05,745 INFO [IPC Server handler 12 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.702642
2015-10-19 14:34:08,765 INFO [IPC Server handler 26 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7039584
2015-10-19 14:34:11,774 INFO [IPC Server handler 5 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.70515704
2015-10-19 14:34:14,799 INFO [IPC Server handler 11 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.70637196
2015-10-19 14:34:17,821 INFO [IPC Server handler 8 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.70788765
2015-10-19 14:34:20,837 INFO [IPC Server handler 0 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7092266
2015-10-19 14:34:23,852 INFO [IPC Server handler 29 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.710981
2015-10-19 14:34:26,868 INFO [IPC Server handler 13 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7126252
2015-10-19 14:34:29,884 INFO [IPC Server handler 20 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7148737
2015-10-19 14:34:32,900 INFO [IPC Server handler 7 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7162381
2015-10-19 14:34:35,916 INFO [IPC Server handler 27 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7174787
2015-10-19 14:34:38,932 INFO [IPC Server handler 6 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7188952
2015-10-19 14:34:41,949 INFO [IPC Server handler 18 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.72032106
2015-10-19 14:34:44,965 INFO [IPC Server handler 23 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7216276
2015-10-19 14:34:47,981 INFO [IPC Server handler 25 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.72326666
2015-10-19 14:34:50,996 INFO [IPC Server handler 14 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.72451013
2015-10-19 14:34:54,012 INFO [IPC Server handler 28 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7255354
2015-10-19 14:34:57,028 INFO [IPC Server handler 10 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.72670597
2015-10-19 14:35:00,043 INFO [IPC Server handler 17 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.72763264
2015-10-19 14:35:03,059 INFO [IPC Server handler 24 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7284208
2015-10-19 14:35:06,074 INFO [IPC Server handler 19 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.72881544
2015-10-19 14:35:09,091 INFO [IPC Server handler 15 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.72974604
2015-10-19 14:35:12,112 INFO [IPC Server handler 9 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.731323
2015-10-19 14:35:15,121 INFO [IPC Server handler 21 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.73189306
2015-10-19 14:35:18,138 INFO [IPC Server handler 16 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.73230964
2015-10-19 14:35:21,161 INFO [IPC Server handler 22 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.73363143
2015-10-19 14:35:24,175 INFO [IPC Server handler 1 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.73615843
2015-10-19 14:35:27,193 INFO [IPC Server handler 2 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7385957
2015-10-19 14:35:30,207 INFO [IPC Server handler 4 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.74135315
2015-10-19 14:35:33,223 INFO [IPC Server handler 3 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.74385077
2015-10-19 14:35:36,239 INFO [IPC Server handler 12 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7462243
2015-10-19 14:35:39,247 INFO [IPC Server handler 26 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7490866
2015-10-19 14:35:42,269 INFO [IPC Server handler 5 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7518554
2015-10-19 14:35:45,285 INFO [IPC Server handler 11 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7545268
2015-10-19 14:35:48,297 INFO [IPC Server handler 8 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7569982
2015-10-19 14:35:51,314 INFO [IPC Server handler 0 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7594484
2015-10-19 14:35:54,333 INFO [IPC Server handler 29 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.76192915
2015-10-19 14:35:57,343 INFO [IPC Server handler 13 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7649903
2015-10-19 14:36:00,364 INFO [IPC Server handler 20 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7676571
2015-10-19 14:36:03,380 INFO [IPC Server handler 7 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.77025396
2015-10-19 14:36:06,397 INFO [IPC Server handler 27 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7718308
2015-10-19 14:36:09,412 INFO [IPC Server handler 6 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7742906
2015-10-19 14:36:12,430 INFO [IPC Server handler 18 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7755713
2015-10-19 14:36:15,437 INFO [IPC Server handler 23 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.77734035
2015-10-19 14:36:18,459 INFO [IPC Server handler 25 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7793727
2015-10-19 14:36:21,476 INFO [IPC Server handler 14 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7811879
2015-10-19 14:36:24,492 INFO [IPC Server handler 28 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.78202164
2015-10-19 14:36:27,503 INFO [IPC Server handler 10 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.78323
2015-10-19 14:36:30,520 INFO [IPC Server handler 17 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7856536
2015-10-19 14:36:33,538 INFO [IPC Server handler 24 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7878533
2015-10-19 14:36:36,549 INFO [IPC Server handler 19 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.790415
2015-10-19 14:36:39,578 INFO [IPC Server handler 15 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.79243356
2015-10-19 14:36:42,592 INFO [IPC Server handler 9 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7933794
2015-10-19 14:36:45,608 INFO [IPC Server handler 21 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.79423016
2015-10-19 14:36:48,625 INFO [IPC Server handler 16 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7950773
2015-10-19 14:36:51,641 INFO [IPC Server handler 22 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7960378
2015-10-19 14:36:54,658 INFO [IPC Server handler 1 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7973681
2015-10-19 14:36:57,675 INFO [IPC Server handler 2 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7986183
2015-10-19 14:37:00,690 INFO [IPC Server handler 4 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.7998036
2015-10-19 14:37:03,706 INFO [IPC Server handler 3 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.80112314
2015-10-19 14:37:06,722 INFO [IPC Server handler 12 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8028117
2015-10-19 14:37:09,733 INFO [IPC Server handler 26 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8047687
2015-10-19 14:37:12,754 INFO [IPC Server handler 5 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.80624765
2015-10-19 14:37:15,763 INFO [IPC Server handler 11 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.80713475
2015-10-19 14:37:18,783 INFO [IPC Server handler 8 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8083785
2015-10-19 14:37:21,802 INFO [IPC Server handler 0 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8102922
2015-10-19 14:37:24,816 INFO [IPC Server handler 29 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.81237817
2015-10-19 14:37:27,831 INFO [IPC Server handler 13 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8139533
2015-10-19 14:37:30,848 INFO [IPC Server handler 20 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8160408
2015-10-19 14:37:33,864 INFO [IPC Server handler 7 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.81781256
2015-10-19 14:37:36,879 INFO [IPC Server handler 27 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8197065
2015-10-19 14:37:39,896 INFO [IPC Server handler 6 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8216604
2015-10-19 14:37:42,911 INFO [IPC Server handler 18 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.82331795
2015-10-19 14:37:45,928 INFO [IPC Server handler 23 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8253298
2015-10-19 14:37:48,938 INFO [IPC Server handler 25 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.82714903
2015-10-19 14:37:51,957 INFO [IPC Server handler 14 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.82850444
2015-10-19 14:37:54,974 INFO [IPC Server handler 28 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8303002
2015-10-19 14:37:57,989 INFO [IPC Server handler 10 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8325846
2015-10-19 14:38:01,011 INFO [IPC Server handler 17 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8351167
2015-10-19 14:38:04,026 INFO [IPC Server handler 24 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.83755946
2015-10-19 14:38:07,045 INFO [IPC Server handler 19 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.84006125
2015-10-19 14:38:10,057 INFO [IPC Server handler 15 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.84272885
2015-10-19 14:38:13,069 INFO [IPC Server handler 9 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8454966
2015-10-19 14:38:16,090 INFO [IPC Server handler 21 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.84804934
2015-10-19 14:38:19,099 INFO [IPC Server handler 16 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.85069203
2015-10-19 14:38:22,122 INFO [IPC Server handler 22 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.85351187
2015-10-19 14:38:25,140 INFO [IPC Server handler 1 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8561056
2015-10-19 14:38:28,157 INFO [IPC Server handler 2 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.85855955
2015-10-19 14:38:31,172 INFO [IPC Server handler 4 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.86132437
2015-10-19 14:38:34,192 INFO [IPC Server handler 3 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.86457133
2015-10-19 14:38:37,208 INFO [IPC Server handler 12 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8663131
2015-10-19 14:38:40,226 INFO [IPC Server handler 26 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.86837554
2015-10-19 14:38:43,242 INFO [IPC Server handler 5 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8702233
2015-10-19 14:38:46,255 INFO [IPC Server handler 11 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.87227154
2015-10-19 14:38:49,280 INFO [IPC Server handler 8 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8744658
2015-10-19 14:38:52,291 INFO [IPC Server handler 0 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8762833
2015-10-19 14:38:55,313 INFO [IPC Server handler 29 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8781535
2015-10-19 14:38:58,330 INFO [IPC Server handler 13 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8802124
2015-10-19 14:39:01,347 INFO [IPC Server handler 20 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.882046
2015-10-19 14:39:04,381 INFO [IPC Server handler 7 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.88409793
2015-10-19 14:39:07,397 INFO [IPC Server handler 27 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.88617814
2015-10-19 14:39:10,416 INFO [IPC Server handler 6 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.88858294
2015-10-19 14:39:13,430 INFO [IPC Server handler 18 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.8911878
2015-10-19 14:39:16,450 INFO [IPC Server handler 23 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.89376056
2015-10-19 14:39:19,470 INFO [IPC Server handler 25 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.89630646
2015-10-19 14:39:22,491 INFO [IPC Server handler 14 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.89889973
2015-10-19 14:39:25,502 INFO [IPC Server handler 28 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9023315
2015-10-19 14:39:28,525 INFO [IPC Server handler 10 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9048806
2015-10-19 14:39:31,540 INFO [IPC Server handler 17 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9076047
2015-10-19 14:39:34,558 INFO [IPC Server handler 24 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.91003674
2015-10-19 14:39:37,570 INFO [IPC Server handler 19 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.91386986
2015-10-19 14:39:40,588 INFO [IPC Server handler 15 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.91794693
2015-10-19 14:39:43,612 INFO [IPC Server handler 9 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9205414
2015-10-19 14:39:46,624 INFO [IPC Server handler 21 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.92342913
2015-10-19 14:39:49,646 INFO [IPC Server handler 16 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.92679805
2015-10-19 14:39:52,659 INFO [IPC Server handler 22 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9296348
2015-10-19 14:39:55,682 INFO [IPC Server handler 1 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9327692
2015-10-19 14:39:58,692 INFO [IPC Server handler 2 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9356081
2015-10-19 14:40:01,716 INFO [IPC Server handler 4 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9382829
2015-10-19 14:40:04,726 INFO [IPC Server handler 3 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9413245
2015-10-19 14:40:07,751 INFO [IPC Server handler 12 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.94449717
2015-10-19 14:40:10,768 INFO [IPC Server handler 26 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9468153
2015-10-19 14:40:13,785 INFO [IPC Server handler 5 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.94927347
2015-10-19 14:40:16,802 INFO [IPC Server handler 11 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.95185083
2015-10-19 14:40:19,816 INFO [IPC Server handler 8 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.95459795
2015-10-19 14:40:22,839 INFO [IPC Server handler 0 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9570858
2015-10-19 14:40:25,858 INFO [IPC Server handler 29 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.95982647
2015-10-19 14:40:28,873 INFO [IPC Server handler 13 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9625778
2015-10-19 14:40:31,889 INFO [IPC Server handler 20 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.96417785
2015-10-19 14:40:34,912 INFO [IPC Server handler 7 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9658284
2015-10-19 14:40:37,924 INFO [IPC Server handler 27 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9672746
2015-10-19 14:40:40,942 INFO [IPC Server handler 6 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9688703
2015-10-19 14:40:43,961 INFO [IPC Server handler 18 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9701953
2015-10-19 14:40:46,981 INFO [IPC Server handler 23 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9717971
2015-10-19 14:40:49,993 INFO [IPC Server handler 25 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9734112
2015-10-19 14:40:53,015 INFO [IPC Server handler 14 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9748959
2015-10-19 14:40:56,032 INFO [IPC Server handler 28 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.976282
2015-10-19 14:40:59,050 INFO [IPC Server handler 10 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9776699
2015-10-19 14:41:02,063 INFO [IPC Server handler 17 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9788792
2015-10-19 14:41:05,084 INFO [IPC Server handler 24 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.97967315
2015-10-19 14:41:08,100 INFO [IPC Server handler 19 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.98056793
2015-10-19 14:41:11,120 INFO [IPC Server handler 15 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9815986
2015-10-19 14:41:14,137 INFO [IPC Server handler 9 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9826764
2015-10-19 14:41:17,153 INFO [IPC Server handler 21 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9837914
2015-10-19 14:41:20,171 INFO [IPC Server handler 16 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.98490745
2015-10-19 14:41:23,191 INFO [IPC Server handler 22 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.98600703
2015-10-19 14:41:26,206 INFO [IPC Server handler 1 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.98667973
2015-10-19 14:41:29,225 INFO [IPC Server handler 2 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.98729473
2015-10-19 14:41:32,241 INFO [IPC Server handler 4 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9878319
2015-10-19 14:41:35,258 INFO [IPC Server handler 3 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9888101
2015-10-19 14:41:38,276 INFO [IPC Server handler 12 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.99036735
2015-10-19 14:41:41,293 INFO [IPC Server handler 26 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9912553
2015-10-19 14:41:44,309 INFO [IPC Server handler 5 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9919182
2015-10-19 14:41:47,324 INFO [IPC Server handler 11 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.99326456
2015-10-19 14:41:50,346 INFO [IPC Server handler 8 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9952728
2015-10-19 14:41:53,357 INFO [IPC Server handler 0 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 0.9987105
2015-10-19 14:41:54,808 INFO [IPC Server handler 29 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445182159119_0002_r_000000_1000
2015-10-19 14:41:54,810 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_r_000000_1000 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-19 14:41:54,810 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445182159119_0002_r_000000_1000 given a go for committing the task output.
2015-10-19 14:41:54,812 INFO [IPC Server handler 13 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445182159119_0002_r_000000_1000
2015-10-19 14:41:54,812 INFO [IPC Server handler 13 on 44089] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445182159119_0002_r_000000_1000:true
2015-10-19 14:41:54,847 INFO [IPC Server handler 20 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_1000 is : 1.0
2015-10-19 14:41:54,851 INFO [IPC Server handler 7 on 44089] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0002_r_000000_1000
2015-10-19 14:41:54,853 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_r_000000_1000 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:41:54,854 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_02_000002 taskAttempt attempt_1445182159119_0002_r_000000_1000
2015-10-19 14:41:54,855 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_r_000000_1000
2015-10-19 14:41:54,856 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:41:54,886 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_r_000000_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:41:54,887 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_r_000000_1000
2015-10-19 14:41:54,887 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:41:54,888 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-19 14:41:54,889 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0002Job Transitioned from RUNNING to COMMITTING
2015-10-19 14:41:54,890 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-19 14:41:54,965 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-19 14:41:54,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0002Job Transitioned from COMMITTING to SUCCEEDED
2015-10-19 14:41:54,968 INFO [Thread-86] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-19 14:41:54,968 INFO [Thread-86] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-19 14:41:54,968 INFO [Thread-86] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-19 14:41:54,968 INFO [Thread-86] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-19 14:41:54,968 INFO [Thread-86] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-19 14:41:54,968 INFO [Thread-86] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-19 14:41:54,969 INFO [Thread-86] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-19 14:41:55,277 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0002/job_1445182159119_0002_2.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0002-1445235687677-msrabi-word+count-1445236914959-10-1-SUCCEEDED-default-1445235697502.jhist_tmp
2015-10-19 14:41:55,410 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0002-1445235687677-msrabi-word+count-1445236914959-10-1-SUCCEEDED-default-1445235697502.jhist_tmp
2015-10-19 14:41:55,415 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0002/job_1445182159119_0002_2_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0002_conf.xml_tmp
2015-10-19 14:41:55,435 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:1 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 14:41:55,625 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0002_conf.xml_tmp
2015-10-19 14:41:55,631 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0002.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0002.summary
2015-10-19 14:41:55,635 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0002_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0002_conf.xml
2015-10-19 14:41:55,638 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0002-1445235687677-msrabi-word+count-1445236914959-10-1-SUCCEEDED-default-1445235697502.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0002-1445235687677-msrabi-word+count-1445236914959-10-1-SUCCEEDED-default-1445235697502.jhist
2015-10-19 14:41:55,639 INFO [Thread-86] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-19 14:41:55,644 INFO [Thread-86] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-19 14:41:55,646 INFO [Thread-86] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-41.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445182159119_0002
2015-10-19 14:41:55,657 INFO [Thread-86] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-19 14:41:56,659 INFO [Thread-86] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:1 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 14:41:56,660 INFO [Thread-86] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0002
2015-10-19 14:41:56,665 INFO [Thread-86] org.apache.hadoop.ipc.Server: Stopping server on 44089
2015-10-19 14:41:56,667 INFO [IPC Server listener on 44089] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 44089
2015-10-19 14:41:56,668 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
2015-10-19 14:41:56,668 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
