2015-10-17 15:38:16,220 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 15:38:16,627 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 15:38:16,627 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 15:38:16,720 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 15:38:16,720 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445062781478_0013, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@54e0a229)
2015-10-17 15:38:17,111 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 15:38:18,236 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445062781478_0013
2015-10-17 15:38:19,861 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 15:38:20,767 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 15:38:20,830 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@6119d61b
2015-10-17 15:38:21,236 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:939524096+134217728
2015-10-17 15:38:21,471 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 15:38:21,471 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 15:38:21,471 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 15:38:21,471 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 15:38:21,471 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 15:38:21,502 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 15:39:03,254 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:39:03,254 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48252774; bufvoid = 104857600
2015-10-17 15:39:03,254 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17306072(69224288); length = 8908325/6553600
2015-10-17 15:39:03,270 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57321526 kvi 14330376(57321504)
2015-10-17 15:39:22,193 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 15:39:22,255 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57321526 kv 14330376(57321504) kvi 12128560(48514240)
2015-10-17 15:39:48,757 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:39:48,757 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57321526; bufend = 695920; bufvoid = 104857600
2015-10-17 15:39:48,757 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14330376(57321504); kvend = 5416856(21667424); length = 8913521/6553600
2015-10-17 15:39:48,757 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9764656 kvi 2441160(9764640)
2015-10-17 15:40:02,570 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 15:40:02,570 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9764656 kv 2441160(9764640) kvi 236684(946736)
2015-10-17 15:40:39,759 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:40:39,759 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9764656; bufend = 58004947; bufvoid = 104857600
2015-10-17 15:40:39,759 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2441160(9764640); kvend = 19744112(78976448); length = 8911449/6553600
2015-10-17 15:40:39,759 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67073683 kvi 16768416(67073664)
2015-10-17 15:40:56,510 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 15:40:57,182 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67073683 kv 16768416(67073664) kvi 14561752(58247008)
2015-10-17 15:41:07,151 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:41:07,151 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67073683; bufend = 10426966; bufvoid = 104857600
2015-10-17 15:41:07,151 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16768416(67073664); kvend = 7849620(31398480); length = 8918797/6553600
2015-10-17 15:41:07,151 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19495718 kvi 4873924(19495696)
2015-10-17 15:41:29,215 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 15:41:29,246 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 19495718 kv 4873924(19495696) kvi 2677448(10709792)
2015-10-17 15:41:41,466 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:41:41,466 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 19495718; bufend = 67755457; bufvoid = 104857600
2015-10-17 15:41:41,466 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 4873924(19495696); kvend = 22181748(88726992); length = 8906577/6553600
2015-10-17 15:41:41,466 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 76824209 kvi 19206048(76824192)
2015-10-17 15:42:08,561 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 15:42:08,764 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 76824209 kv 19206048(76824192) kvi 16996444(67985776)
2015-10-17 15:42:16,796 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:42:16,796 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 76824209; bufend = 20191510; bufvoid = 104857600
2015-10-17 15:42:16,796 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 19206048(76824192); kvend = 10290756(41163024); length = 8915293/6553600
2015-10-17 15:42:16,796 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29260262 kvi 7315060(29260240)
2015-10-17 15:42:41,860 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-17 15:42:41,860 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29260262 kv 7315060(29260240) kvi 5114312(20457248)
2015-10-17 15:42:55,642 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:42:55,642 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29260262; bufend = 77519736; bufvoid = 104857600
2015-10-17 15:42:55,642 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7315060(29260240); kvend = 24622816(98491264); length = 8906645/6553600
2015-10-17 15:42:55,642 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 86588488 kvi 21647116(86588464)
2015-10-17 15:43:20,409 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-17 15:43:20,409 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 86588488 kv 21647116(86588464) kvi 19453336(77813344)
2015-10-17 15:43:36,331 INFO [communication thread] org.apache.hadoop.mapred.Task: Communication exception: java.io.IOException: Failed on local exception: java.io.IOException: An existing connection was forcibly closed by the remote host; Host Details : local host is: "MININT-FNANLI5/*************"; destination host is: "04dn8iq.fareast.corp.microsoft.com":49470; 
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:772)
	at org.apache.hadoop.ipc.Client.call(Client.java:1472)
	at org.apache.hadoop.ipc.Client.call(Client.java:1399)
	at org.apache.hadoop.ipc.WritableRpcEngine$Invoker.invoke(WritableRpcEngine.java:244)
	at com.sun.proxy.$Proxy9.statusUpdate(Unknown Source)
	at org.apache.hadoop.mapred.Task$TaskReporter.run(Task.java:737)
	at java.lang.Thread.run(Thread.java:724)
Caused by: java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.net.SocketInputStream$Reader.performIO(SocketInputStream.java:57)
	at org.apache.hadoop.net.SocketIOWithTimeout.doIO(SocketIOWithTimeout.java:142)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:161)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:131)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at org.apache.hadoop.ipc.Client$Connection$PingInputStream.read(Client.java:513)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:235)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:254)
	at java.io.DataInputStream.readInt(DataInputStream.java:387)
	at org.apache.hadoop.ipc.Client$Connection.receiveRpcResponse(Client.java:1071)
	at org.apache.hadoop.ipc.Client$Connection.run(Client.java:966)

2015-10-17 15:43:56,020 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 0 time(s); maxRetries=45
2015-10-17 15:44:16,021 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 1 time(s); maxRetries=45
2015-10-17 15:44:36,022 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 2 time(s); maxRetries=45
2015-10-17 15:44:56,023 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 3 time(s); maxRetries=45
2015-10-17 15:45:16,024 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 4 time(s); maxRetries=45
2015-10-17 15:45:36,024 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 5 time(s); maxRetries=45
2015-10-17 15:45:56,026 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 6 time(s); maxRetries=45
2015-10-17 15:46:16,027 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 7 time(s); maxRetries=45
2015-10-17 15:46:36,028 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 8 time(s); maxRetries=45
2015-10-17 15:46:56,030 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 9 time(s); maxRetries=45
2015-10-17 15:47:16,031 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 10 time(s); maxRetries=45
2015-10-17 15:47:36,032 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 11 time(s); maxRetries=45
2015-10-17 15:47:56,034 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 12 time(s); maxRetries=45
2015-10-17 15:48:16,035 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 13 time(s); maxRetries=45
2015-10-17 15:48:36,036 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 14 time(s); maxRetries=45
2015-10-17 15:48:56,038 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 15 time(s); maxRetries=45
2015-10-17 15:49:16,039 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 16 time(s); maxRetries=45
2015-10-17 15:49:36,040 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 17 time(s); maxRetries=45
2015-10-17 15:49:56,042 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 18 time(s); maxRetries=45
2015-10-17 15:50:16,043 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 19 time(s); maxRetries=45
2015-10-17 15:50:36,044 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 20 time(s); maxRetries=45
2015-10-17 15:50:56,046 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 21 time(s); maxRetries=45
2015-10-17 15:51:16,047 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 22 time(s); maxRetries=45
2015-10-17 15:51:36,048 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 23 time(s); maxRetries=45
2015-10-17 15:51:56,049 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 24 time(s); maxRetries=45
2015-10-17 15:52:16,050 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 25 time(s); maxRetries=45
2015-10-17 15:52:36,051 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 26 time(s); maxRetries=45
2015-10-17 15:52:56,052 INFO [main] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 27 time(s); maxRetries=45
