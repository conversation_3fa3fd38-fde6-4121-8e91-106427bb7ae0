2015-10-17 15:43:47,586 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445062781478_0015_000001
2015-10-17 15:43:48,036 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 15:43:48,036 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 15 cluster_timestamp: 1445062781478 } attemptId: 1 } keyId: 471522253)
2015-10-17 15:43:48,206 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 15:43:49,009 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 15:43:49,067 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 15:43:49,109 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 15:43:49,111 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 15:43:49,112 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 15:43:49,113 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 15:43:49,114 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 15:43:49,122 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 15:43:49,123 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 15:43:49,124 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 15:43:49,169 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:43:49,191 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:43:49,212 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:43:49,222 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 15:43:49,277 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 15:43:49,569 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 15:43:49,649 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 15:43:49,649 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 15:43:49,658 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445062781478_0015 to jobTokenSecretManager
2015-10-17 15:43:49,839 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445062781478_0015 because: not enabled; too many maps; too much input;
2015-10-17 15:43:49,861 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445062781478_0015 = 1256521728. Number of splits = 10
2015-10-17 15:43:49,863 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445062781478_0015 = 1
2015-10-17 15:43:49,863 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0015Job Transitioned from NEW to INITED
2015-10-17 15:43:49,864 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445062781478_0015.
2015-10-17 15:43:49,907 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 15:43:49,919 INFO [Socket Reader #1 for port 40645] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 40645
2015-10-17 15:43:49,948 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 15:43:49,949 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 15:43:49,949 INFO [IPC Server listener on 40645] org.apache.hadoop.ipc.Server: IPC Server listener on 40645: starting
2015-10-17 15:43:49,950 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-41.fareast.corp.microsoft.com/**************:40645
2015-10-17 15:43:50,063 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 15:43:50,067 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 15:43:50,081 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 15:43:50,087 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 15:43:50,087 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 15:43:50,091 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 15:43:50,091 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 15:43:50,104 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 40654
2015-10-17 15:43:50,104 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 15:43:50,149 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_40654_mapreduce____gzasjk\webapp
2015-10-17 15:43:50,558 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:40654
2015-10-17 15:43:50,558 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 40654
2015-10-17 15:43:51,441 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 15:43:51,449 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445062781478_0015
2015-10-17 15:43:51,452 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 15:43:51,459 INFO [Socket Reader #1 for port 40658] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 40658
2015-10-17 15:43:51,470 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 15:43:51,470 INFO [IPC Server listener on 40658] org.apache.hadoop.ipc.Server: IPC Server listener on 40658: starting
2015-10-17 15:43:51,514 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 15:43:51,514 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 15:43:51,514 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 15:43:51,629 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at MSRA-SA-41/**************:8030
2015-10-17 15:43:51,782 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 15:43:51,783 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 15:43:51,791 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 15:43:51,797 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 15:43:51,810 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0015Job Transitioned from INITED to SETUP
2015-10-17 15:43:51,814 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 15:43:51,829 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0015Job Transitioned from SETUP to RUNNING
2015-10-17 15:43:51,871 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,873 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,878 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:43:51,879 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,879 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,880 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:43:51,880 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:43:51,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,883 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:43:51,883 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:43:51,885 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,885 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,886 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:43:51,886 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,887 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,887 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:43:51,888 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,888 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,888 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:43:51,889 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,889 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,890 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:43:51,890 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,891 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:43:51,891 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:43:51,892 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:43:51,895 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:43:51,896 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:43:51,896 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:43:51,896 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:43:51,897 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:43:51,897 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:43:51,897 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:43:51,898 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:43:51,898 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:43:51,899 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:43:51,899 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:43:51,901 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 15:43:51,918 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 15:43:51,999 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445062781478_0015, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0015/job_1445062781478_0015_1.jhist
2015-10-17 15:43:52,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 15:43:52,863 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:43:52,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:43:52,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:43:52,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:43:52,869 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:43:53,870 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:43:53,871 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:43:53,871 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:43:53,871 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:43:54,872 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:43:54,872 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:43:54,873 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:43:54,873 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:43:55,875 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:43:55,875 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:43:55,876 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:43:55,876 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:43:56,878 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:43:56,878 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:43:56,878 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:43:56,878 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:43:57,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:43:57,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:43:57,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:43:57,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:43:58,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:43:58,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:43:58,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:43:58,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:43:59,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:43:59,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:43:59,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:43:59,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:00,886 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:00,886 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:00,886 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:00,886 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:01,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:01,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:01,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:01,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:02,889 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:02,889 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:02,889 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:02,890 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:03,891 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:03,891 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:03,891 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:03,891 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:04,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:04,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:04,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:04,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:05,894 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:05,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:05,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:05,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:06,897 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:06,898 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:06,898 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:06,898 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:07,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:07,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:07,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:07,903 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:08,904 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:08,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:08,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:08,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:09,907 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:09,908 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:09,908 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:09,908 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:10,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:10,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:10,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:10,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:11,913 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:11,913 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:11,913 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:11,913 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:12,914 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:12,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:12,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:12,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:13,717 INFO [Thread-56] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as ***********:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-17 15:44:13,719 INFO [Thread-56] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073742524_1719
2015-10-17 15:44:13,728 INFO [Thread-56] org.apache.hadoop.hdfs.DFSClient: Excluding datanode ***********:50010
2015-10-17 15:44:13,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:13,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:13,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:13,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:14,918 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:14,918 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:14,918 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:14,918 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:15,919 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:15,919 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:15,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:15,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:16,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping down all scheduled reduces:0
2015-10-17 15:44:16,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Going to preempt 1 due to lack of space for maps
2015-10-17 15:44:16,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:16,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:17,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:44:17,933 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0015_01_000002 to attempt_1445062781478_0015_m_000000_0
2015-10-17 15:44:17,935 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:17,935 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:17,935 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:1 RackLocal:0
2015-10-17 15:44:17,987 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:44:18,003 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0015/job.jar
2015-10-17 15:44:18,005 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0015/job.xml
2015-10-17 15:44:18,006 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 15:44:18,007 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 15:44:18,007 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 15:44:18,063 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:44:18,067 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0015_01_000002 taskAttempt attempt_1445062781478_0015_m_000000_0
2015-10-17 15:44:18,070 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0015_m_000000_0
2015-10-17 15:44:18,070 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:44:18,126 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0015_m_000000_0 : 13562
2015-10-17 15:44:18,128 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0015_m_000000_0] using containerId: [container_1445062781478_0015_01_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:44:18,130 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:44:18,131 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0015_m_000000
2015-10-17 15:44:18,131 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:44:18,936 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:44:18,936 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:18,937 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:19,938 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:19,938 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:20,067 INFO [Socket Reader #1 for port 40658] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0015 (auth:SIMPLE)
2015-10-17 15:44:20,091 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0015_m_000002 asked for a task
2015-10-17 15:44:20,091 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0015_m_000002 given task: attempt_1445062781478_0015_m_000000_0
2015-10-17 15:44:20,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:20,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:21,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:21,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:22,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:22,943 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:23,945 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:23,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:24,948 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:24,948 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:25,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:25,952 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:26,955 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:26,955 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:27,327 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.048837516
2015-10-17 15:44:27,957 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:27,957 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:28,958 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:28,959 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:29,960 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:29,960 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:30,363 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.08754695
2015-10-17 15:44:30,961 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:30,961 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:31,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:31,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:32,964 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:32,964 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:33,383 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.10635664
2015-10-17 15:44:33,965 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:44:33,965 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:34,966 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:44:34,966 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:35,967 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:44:35,967 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:36,404 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.10635664
2015-10-17 15:44:36,968 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:44:36,968 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:37,969 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:44:37,969 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:38,976 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:44:38,976 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:44:38,977 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0015_01_000003 to attempt_1445062781478_0015_m_000001_0
2015-10-17 15:44:38,977 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:38,977 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:38,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:44:38,977 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:1 RackLocal:1
2015-10-17 15:44:38,978 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:44:38,978 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0015_01_000003 taskAttempt attempt_1445062781478_0015_m_000001_0
2015-10-17 15:44:38,979 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0015_m_000001_0
2015-10-17 15:44:38,979 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 15:44:39,350 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0015_m_000001_0 : 13562
2015-10-17 15:44:39,351 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0015_m_000001_0] using containerId: [container_1445062781478_0015_01_000003 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 15:44:39,351 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:44:39,351 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0015_m_000001
2015-10-17 15:44:39,352 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:44:39,435 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.10635664
2015-10-17 15:44:39,978 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:44:39,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:39,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:40,981 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:40,981 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:41,984 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:41,984 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:42,475 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.13197348
2015-10-17 15:44:42,987 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:42,987 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:43,989 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:43,989 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:44,991 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:44:44,991 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:45,506 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.16579036
2015-10-17 15:44:45,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:44:45,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:46,886 INFO [Socket Reader #1 for port 40658] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0015 (auth:SIMPLE)
2015-10-17 15:44:46,915 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0015_m_000003 asked for a task
2015-10-17 15:44:46,915 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0015_m_000003 given task: attempt_1445062781478_0015_m_000001_0
2015-10-17 15:44:46,994 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:44:46,994 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:47,995 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:44:47,995 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:48,526 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.19158794
2015-10-17 15:44:48,998 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:44:48,999 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:44:48,999 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0015_01_000004 to attempt_1445062781478_0015_m_000002_0
2015-10-17 15:44:48,999 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:48,999 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:48,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:44:48,999 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:1 RackLocal:2
2015-10-17 15:44:49,000 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:44:49,001 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0015_01_000004 taskAttempt attempt_1445062781478_0015_m_000002_0
2015-10-17 15:44:49,001 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0015_m_000002_0
2015-10-17 15:44:49,001 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 15:44:49,214 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0015_m_000002_0 : 13562
2015-10-17 15:44:49,215 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0015_m_000002_0] using containerId: [container_1445062781478_0015_01_000004 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 15:44:49,215 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:44:49,215 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0015_m_000002
2015-10-17 15:44:49,215 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:44:50,000 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:44:50,000 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:50,000 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:51,001 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:51,001 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:51,545 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.19158794
2015-10-17 15:44:52,002 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:52,002 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:53,003 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:53,003 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:53,242 INFO [Socket Reader #1 for port 40658] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0015 (auth:SIMPLE)
2015-10-17 15:44:53,304 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0015_m_000004 asked for a task
2015-10-17 15:44:53,304 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0015_m_000004 given task: attempt_1445062781478_0015_m_000002_0
2015-10-17 15:44:54,004 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:54,004 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:54,564 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.19158794
2015-10-17 15:44:55,005 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:55,005 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:56,007 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:56,007 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:57,010 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:57,010 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:57,602 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.21942717
2015-10-17 15:44:58,011 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:58,011 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:44:59,012 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:44:59,012 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:00,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:00,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:00,634 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.26759285
2015-10-17 15:45:01,014 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:01,014 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:01,921 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.03940511
2015-10-17 15:45:02,015 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:02,015 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:02,135 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.025725577
2015-10-17 15:45:03,017 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:03,017 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:03,663 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.27696857
2015-10-17 15:45:04,018 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:04,018 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:05,020 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:05,021 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:05,728 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.06513385
2015-10-17 15:45:05,742 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.050477643
2015-10-17 15:45:06,022 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:06,023 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:06,682 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.27696857
2015-10-17 15:45:07,024 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:07,024 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:08,027 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:45:08,028 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0015_01_000005 to attempt_1445062781478_0015_m_000003_0
2015-10-17 15:45:08,028 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:08,028 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:08,028 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:2 RackLocal:2
2015-10-17 15:45:08,029 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:45:08,029 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:45:08,030 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0015_01_000005 taskAttempt attempt_1445062781478_0015_m_000003_0
2015-10-17 15:45:08,030 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0015_m_000003_0
2015-10-17 15:45:08,030 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:45:08,047 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0015_m_000003_0 : 13562
2015-10-17 15:45:08,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0015_m_000003_0] using containerId: [container_1445062781478_0015_01_000005 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:45:08,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:45:08,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0015_m_000003
2015-10-17 15:45:08,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:45:09,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:45:09,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:09,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:09,682 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.09216622
2015-10-17 15:45:09,686 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.080767736
2015-10-17 15:45:09,702 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.2953871
2015-10-17 15:45:10,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:45:10,032 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0015_01_000006 to attempt_1445062781478_0015_m_000004_0
2015-10-17 15:45:10,032 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:45:10,032 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:10,032 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:45:10,032 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:3 RackLocal:2
2015-10-17 15:45:10,033 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:45:10,033 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0015_01_000006 taskAttempt attempt_1445062781478_0015_m_000004_0
2015-10-17 15:45:10,034 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0015_m_000004_0
2015-10-17 15:45:10,034 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:45:10,049 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0015_m_000004_0 : 13562
2015-10-17 15:45:10,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0015_m_000004_0] using containerId: [container_1445062781478_0015_01_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:45:10,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:45:10,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0015_m_000004
2015-10-17 15:45:10,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:45:11,033 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 15:45:11,033 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:45:11,033 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:11,955 INFO [Socket Reader #1 for port 40658] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0015 (auth:SIMPLE)
2015-10-17 15:45:11,977 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0015_m_000005 asked for a task
2015-10-17 15:45:11,977 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0015_m_000005 given task: attempt_1445062781478_0015_m_000003_0
2015-10-17 15:45:12,034 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:45:12,034 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:12,733 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.35267678
2015-10-17 15:45:13,036 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:45:13,036 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:13,441 INFO [Socket Reader #1 for port 40658] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0015 (auth:SIMPLE)
2015-10-17 15:45:13,461 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0015_m_000006 asked for a task
2015-10-17 15:45:13,461 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0015_m_000006 given task: attempt_1445062781478_0015_m_000004_0
2015-10-17 15:45:14,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:45:14,043 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:45:14,043 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0015_01_000007 to attempt_1445062781478_0015_m_000005_0
2015-10-17 15:45:14,043 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:14,044 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:14,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:45:14,044 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:3 RackLocal:3
2015-10-17 15:45:14,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:45:14,045 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0015_01_000007 taskAttempt attempt_1445062781478_0015_m_000005_0
2015-10-17 15:45:14,045 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0015_m_000005_0
2015-10-17 15:45:14,045 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:45:14,191 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0015_m_000005_0 : 13562
2015-10-17 15:45:14,191 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0015_m_000005_0] using containerId: [container_1445062781478_0015_01_000007 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:51951]
2015-10-17 15:45:14,192 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:45:14,192 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0015_m_000005
2015-10-17 15:45:14,192 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:45:14,397 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.100835785
2015-10-17 15:45:14,449 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.10660437
2015-10-17 15:45:15,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:45:15,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:15,046 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:15,773 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.3624012
2015-10-17 15:45:16,047 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:16,047 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:17,048 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:17,048 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:17,787 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.1066108
2015-10-17 15:45:17,840 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.10660437
2015-10-17 15:45:18,049 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:18,049 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:18,799 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.3624012
2015-10-17 15:45:19,050 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:19,050 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:19,540 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.106493875
2015-10-17 15:45:20,051 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:20,051 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:20,746 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.10680563
2015-10-17 15:45:21,053 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:21,053 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:21,062 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.1066108
2015-10-17 15:45:21,159 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.10660437
2015-10-17 15:45:21,827 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.3796212
2015-10-17 15:45:22,054 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:22,054 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:22,543 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.106493875
2015-10-17 15:45:23,056 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:23,056 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:23,452 INFO [Socket Reader #1 for port 40658] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0015 (auth:SIMPLE)
2015-10-17 15:45:23,545 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0015_m_000007 asked for a task
2015-10-17 15:45:23,546 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0015_m_000007 given task: attempt_1445062781478_0015_m_000005_0
2015-10-17 15:45:23,745 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.10680563
2015-10-17 15:45:24,058 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:24,058 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:24,355 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.1066108
2015-10-17 15:45:24,500 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.10660437
2015-10-17 15:45:24,853 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.41280526
2015-10-17 15:45:25,059 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:25,059 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:25,563 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.106493875
2015-10-17 15:45:26,060 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:26,060 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:26,747 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.10680563
2015-10-17 15:45:27,061 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:27,061 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:27,658 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.1066108
2015-10-17 15:45:27,761 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.10660437
2015-10-17 15:45:27,872 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.44789755
2015-10-17 15:45:28,063 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:28,063 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:28,581 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.1386629
2015-10-17 15:45:29,064 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:29,064 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:29,761 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.13320313
2015-10-17 15:45:30,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:30,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:30,864 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.10942704
2015-10-17 15:45:30,891 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.44789755
2015-10-17 15:45:30,986 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.10660437
2015-10-17 15:45:31,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:31,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:31,592 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.18594472
2015-10-17 15:45:32,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:32,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:32,761 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.19242907
2015-10-17 15:45:33,068 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:33,068 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:33,911 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.44789755
2015-10-17 15:45:33,928 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.11528684
2015-10-17 15:45:34,061 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.11854709
2015-10-17 15:45:34,070 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:34,070 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:34,605 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.19209063
2015-10-17 15:45:35,071 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:35,071 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:35,761 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.19242907
2015-10-17 15:45:36,072 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:36,072 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:36,929 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.48431322
2015-10-17 15:45:37,056 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.12603647
2015-10-17 15:45:37,073 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:37,073 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:37,172 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.15013738
2015-10-17 15:45:37,216 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.038429566
2015-10-17 15:45:37,605 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.19209063
2015-10-17 15:45:38,074 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:38,074 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:38,762 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.19242907
2015-10-17 15:45:39,075 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:39,075 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:39,947 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.53341997
2015-10-17 15:45:40,076 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:40,076 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:40,352 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.1318977
2015-10-17 15:45:40,457 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.07425525
2015-10-17 15:45:40,469 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.1781858
2015-10-17 15:45:40,611 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.21149835
2015-10-17 15:45:41,077 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:41,077 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:41,768 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.19496758
2015-10-17 15:45:42,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:42,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:42,967 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.53341997
2015-10-17 15:45:43,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:45:43,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0015_01_000008 to attempt_1445062781478_0015_m_000006_0
2015-10-17 15:45:43,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:43,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:43,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:4 RackLocal:3
2015-10-17 15:45:43,083 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:45:43,083 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:45:43,084 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0015_01_000008 taskAttempt attempt_1445062781478_0015_m_000006_0
2015-10-17 15:45:43,084 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0015_m_000006_0
2015-10-17 15:45:43,084 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:45:43,100 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0015_m_000006_0 : 13562
2015-10-17 15:45:43,101 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0015_m_000006_0] using containerId: [container_1445062781478_0015_01_000008 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:45:43,101 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:45:43,101 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0015_m_000006
2015-10-17 15:45:43,101 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:45:43,604 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.14167021
2015-10-17 15:45:43,628 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.26197326
2015-10-17 15:45:43,838 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.19212553
2015-10-17 15:45:43,949 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.09966264
2015-10-17 15:45:44,083 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:45:44,083 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:44,083 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:44,786 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.23004374
2015-10-17 15:45:45,085 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:45,085 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:45,987 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.53341997
2015-10-17 15:45:46,086 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:46,086 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:46,489 INFO [Socket Reader #1 for port 40658] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0015 (auth:SIMPLE)
2015-10-17 15:45:46,508 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0015_m_000008 asked for a task
2015-10-17 15:45:46,509 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0015_m_000008 given task: attempt_1445062781478_0015_m_000006_0
2015-10-17 15:45:46,644 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.27765483
2015-10-17 15:45:46,848 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.14981231
2015-10-17 15:45:47,052 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.19212553
2015-10-17 15:45:47,087 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:47,087 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:47,301 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.10685723
2015-10-17 15:45:47,799 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.27678025
2015-10-17 15:45:48,088 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:48,088 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:49,005 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.5706801
2015-10-17 15:45:49,089 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:49,089 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:49,661 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.27765483
2015-10-17 15:45:50,090 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:50,090 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:50,153 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.16624427
2015-10-17 15:45:50,327 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.19212553
2015-10-17 15:45:50,599 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.10685723
2015-10-17 15:45:50,813 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.2781602
2015-10-17 15:45:51,091 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:51,091 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:52,037 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.61567444
2015-10-17 15:45:52,092 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:52,092 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:52,672 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.28393722
2015-10-17 15:45:53,095 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:53,095 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:53,479 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.19211523
2015-10-17 15:45:53,734 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.19212553
2015-10-17 15:45:53,745 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.106964506
2015-10-17 15:45:53,812 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.2781602
2015-10-17 15:45:53,879 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.10685723
2015-10-17 15:45:54,097 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:54,097 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:55,055 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.61898744
2015-10-17 15:45:55,098 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:55,098 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:55,672 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.36323506
2015-10-17 15:45:56,105 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 15:45:56,105 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0015_01_000009 to attempt_1445062781478_0015_m_000007_0
2015-10-17 15:45:56,106 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0015_01_000010 to attempt_1445062781478_0015_m_000008_0
2015-10-17 15:45:56,106 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:56,106 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:56,106 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:6 RackLocal:3
2015-10-17 15:45:56,106 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:45:56,107 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:45:56,107 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:45:56,108 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:45:56,108 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0015_01_000009 taskAttempt attempt_1445062781478_0015_m_000007_0
2015-10-17 15:45:56,108 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0015_01_000010 taskAttempt attempt_1445062781478_0015_m_000008_0
2015-10-17 15:45:56,108 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0015_m_000007_0
2015-10-17 15:45:56,109 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0015_m_000008_0
2015-10-17 15:45:56,109 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:45:56,110 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:45:56,124 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0015_m_000008_0 : 13562
2015-10-17 15:45:56,124 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0015_m_000008_0] using containerId: [container_1445062781478_0015_01_000010 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:45:56,124 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:45:56,125 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0015_m_000008
2015-10-17 15:45:56,125 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:45:56,125 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0015_m_000007_0 : 13562
2015-10-17 15:45:56,126 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0015_m_000007_0] using containerId: [container_1445062781478_0015_01_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:45:56,126 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:45:56,126 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0015_m_000007
2015-10-17 15:45:56,127 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:45:56,759 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.106964506
2015-10-17 15:45:56,766 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.19211523
2015-10-17 15:45:56,821 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.29195476
2015-10-17 15:45:57,057 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.19212553
2015-10-17 15:45:57,111 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:45:57,112 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:45:57,113 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:45:57,113 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0015_01_000011 to attempt_1445062781478_0015_m_000009_0
2015-10-17 15:45:57,113 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:57,114 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:45:57,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:57,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:6 RackLocal:4
2015-10-17 15:45:57,115 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:45:57,116 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0015_01_000011 taskAttempt attempt_1445062781478_0015_m_000009_0
2015-10-17 15:45:57,116 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0015_m_000009_0
2015-10-17 15:45:57,116 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:45:57,213 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.10685723
2015-10-17 15:45:57,386 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0015_m_000009_0 : 13562
2015-10-17 15:45:57,387 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0015_m_000009_0] using containerId: [container_1445062781478_0015_01_000011 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:51951]
2015-10-17 15:45:57,387 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:45:57,387 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0015_m_000009
2015-10-17 15:45:57,387 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:45:58,086 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.61898744
2015-10-17 15:45:58,120 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:45:58,120 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:45:58,121 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Cannot assign container Container: [ContainerId: container_1445062781478_0015_01_000012, NodeId: MSRA-SA-39.fareast.corp.microsoft.com:49130, NodeHttpAddress: MSRA-SA-39.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 20, Token: Token { kind: ContainerToken, service: **************:49130 }, ] for a map as either  container memory less than required <memory:1024, vCores:1> or no pending map tasks - maps.isEmpty=true
2015-10-17 15:45:58,121 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:45:58,121 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:58,121 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:6 RackLocal:4
2015-10-17 15:45:58,674 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.36323506
2015-10-17 15:45:59,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=0 release= 1 newContainers=0 finishedContainers=1 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 15:45:59,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000012
2015-10-17 15:45:59,146 ERROR [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Container complete event for unknown container id container_1445062781478_0015_01_000012
2015-10-17 15:45:59,146 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:45:59,146 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:45:59,345 INFO [Socket Reader #1 for port 40658] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0015 (auth:SIMPLE)
2015-10-17 15:45:59,373 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0015_m_000009 asked for a task
2015-10-17 15:45:59,374 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0015_m_000009 given task: attempt_1445062781478_0015_m_000007_0
2015-10-17 15:45:59,767 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.106964506
2015-10-17 15:45:59,830 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.32032588
2015-10-17 15:45:59,973 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.19211523
2015-10-17 15:46:00,230 INFO [Socket Reader #1 for port 40658] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0015 (auth:SIMPLE)
2015-10-17 15:46:00,233 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.19703753
2015-10-17 15:46:00,265 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0015_m_000010 asked for a task
2015-10-17 15:46:00,266 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0015_m_000010 given task: attempt_1445062781478_0015_m_000008_0
2015-10-17 15:46:00,581 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.10685723
2015-10-17 15:46:01,122 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.61898744
2015-10-17 15:46:01,689 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.36323506
2015-10-17 15:46:02,768 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.14143999
2015-10-17 15:46:02,852 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.33838394
2015-10-17 15:46:03,205 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.19211523
2015-10-17 15:46:03,449 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.23123357
2015-10-17 15:46:04,145 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.64071554
2015-10-17 15:46:04,307 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.10685723
2015-10-17 15:46:04,704 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.41491908
2015-10-17 15:46:05,766 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.19266446
2015-10-17 15:46:05,779 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.64071554
2015-10-17 15:46:05,860 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.3566215
2015-10-17 15:46:06,391 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.19211523
2015-10-17 15:46:06,721 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.25761482
2015-10-17 15:46:07,189 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.667
2015-10-17 15:46:07,488 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.10685723
2015-10-17 15:46:07,705 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.4486067
2015-10-17 15:46:07,904 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.07394144
2015-10-17 15:46:08,664 INFO [Socket Reader #1 for port 40658] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0015 (auth:SIMPLE)
2015-10-17 15:46:08,716 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.076192155
2015-10-17 15:46:08,769 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.19266446
2015-10-17 15:46:08,848 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0015_m_000011 asked for a task
2015-10-17 15:46:08,848 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0015_m_000011 given task: attempt_1445062781478_0015_m_000009_0
2015-10-17 15:46:08,860 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.36388028
2015-10-17 15:46:10,030 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.27772525
2015-10-17 15:46:10,172 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.19211523
2015-10-17 15:46:10,219 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.667
2015-10-17 15:46:10,719 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.4486067
2015-10-17 15:46:10,930 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.10681946
2015-10-17 15:46:11,115 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.10685723
2015-10-17 15:46:11,750 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.106881365
2015-10-17 15:46:11,782 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.19266446
2015-10-17 15:46:11,860 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.36388028
2015-10-17 15:46:13,226 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.27772525
2015-10-17 15:46:13,238 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.667
2015-10-17 15:46:13,371 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.20094542
2015-10-17 15:46:13,720 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.46011546
2015-10-17 15:46:13,958 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.10681946
2015-10-17 15:46:14,202 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.11463851
2015-10-17 15:46:14,781 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.106881365
2015-10-17 15:46:14,790 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.22487062
2015-10-17 15:46:14,868 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.39931786
2015-10-17 15:46:16,263 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.67075133
2015-10-17 15:46:16,435 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.27772525
2015-10-17 15:46:16,723 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.5264342
2015-10-17 15:46:16,778 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.23318699
2015-10-17 15:46:16,982 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.10681946
2015-10-17 15:46:17,760 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.12995972
2015-10-17 15:46:17,797 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.2783809
2015-10-17 15:46:17,804 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.106881365
2015-10-17 15:46:17,875 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.43867835
2015-10-17 15:46:19,170 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-17 15:46:19,170 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:46:19,283 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.7051129
2015-10-17 15:46:19,735 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.5343203
2015-10-17 15:46:19,799 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.27772525
2015-10-17 15:46:20,001 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.10681946
2015-10-17 15:46:20,067 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.26054472
2015-10-17 15:46:20,800 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.2783809
2015-10-17 15:46:20,823 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.106881365
2015-10-17 15:46:20,875 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.44968578
2015-10-17 15:46:20,934 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.147207
2015-10-17 15:46:22,194 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.043202803
2015-10-17 15:46:22,302 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.7426676
2015-10-17 15:46:22,735 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.5343203
2015-10-17 15:46:23,019 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.15889211
2015-10-17 15:46:23,798 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.2783809
2015-10-17 15:46:23,842 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.14332475
2015-10-17 15:46:23,875 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.44968578
2015-10-17 15:46:23,997 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.27776006
2015-10-17 15:46:24,314 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.27772525
2015-10-17 15:46:24,479 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.15339658
2015-10-17 15:46:25,320 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.78122383
2015-10-17 15:46:25,735 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.5696537
2015-10-17 15:46:25,753 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.049743433
2015-10-17 15:46:26,040 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.19255035
2015-10-17 15:46:26,806 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.3223589
2015-10-17 15:46:26,861 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.19258286
2015-10-17 15:46:26,882 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.45599407
2015-10-17 15:46:27,325 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.27776006
2015-10-17 15:46:27,539 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.2823663
2015-10-17 15:46:28,352 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.8153049
2015-10-17 15:46:28,692 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.15404664
2015-10-17 15:46:28,735 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.6199081
2015-10-17 15:46:28,931 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.090010166
2015-10-17 15:46:29,061 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.19255035
2015-10-17 15:46:29,816 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.36404583
2015-10-17 15:46:29,882 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.19258286
2015-10-17 15:46:29,901 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.5003857
2015-10-17 15:46:30,497 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.27776006
2015-10-17 15:46:30,717 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.30125418
2015-10-17 15:46:31,382 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.846866
2015-10-17 15:46:31,735 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.6199081
2015-10-17 15:46:31,738 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.15977843
2015-10-17 15:46:32,035 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.11316477
2015-10-17 15:46:32,092 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.19255035
2015-10-17 15:46:32,813 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.36404583
2015-10-17 15:46:32,907 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.19258286
2015-10-17 15:46:32,908 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.5352028
2015-10-17 15:46:34,050 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.3315436
2015-10-17 15:46:34,406 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.8806016
2015-10-17 15:46:34,735 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.6199081
2015-10-17 15:46:34,772 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.17130725
2015-10-17 15:46:34,887 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.27776006
2015-10-17 15:46:35,086 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.1477406
2015-10-17 15:46:35,115 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.21163516
2015-10-17 15:46:35,813 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.36404583
2015-10-17 15:46:35,907 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.5352028
2015-10-17 15:46:35,928 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.19258286
2015-10-17 15:46:36,281 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.6199081
2015-10-17 15:46:37,425 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.9179183
2015-10-17 15:46:37,557 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.36317363
2015-10-17 15:46:37,736 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.667
2015-10-17 15:46:37,819 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.17616208
2015-10-17 15:46:38,137 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.2644384
2015-10-17 15:46:38,138 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.16741973
2015-10-17 15:46:38,230 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.27776006
2015-10-17 15:46:38,814 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.41784215
2015-10-17 15:46:38,908 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.5352028
2015-10-17 15:46:38,968 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.20079996
2015-10-17 15:46:40,461 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.94105947
2015-10-17 15:46:40,736 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.36317363
2015-10-17 15:46:40,738 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.667
2015-10-17 15:46:40,897 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.18317625
2015-10-17 15:46:41,163 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.27825075
2015-10-17 15:46:41,511 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.31605083
2015-10-17 15:46:41,711 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.18511407
2015-10-17 15:46:41,813 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.44980705
2015-10-17 15:46:41,908 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.59958273
2015-10-17 15:46:41,998 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.22651227
2015-10-17 15:46:43,479 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 0.9751246
2015-10-17 15:46:43,736 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.68039316
2015-10-17 15:46:44,015 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.36317363
2015-10-17 15:46:44,190 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.27825075
2015-10-17 15:46:44,676 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.19247705
2015-10-17 15:46:44,758 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.23222911
2015-10-17 15:46:44,814 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.44980705
2015-10-17 15:46:44,878 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.3468504
2015-10-17 15:46:44,908 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.6208445
2015-10-17 15:46:45,017 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.27067876
2015-10-17 15:46:45,200 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-17 15:46:45,200 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:46:45,655 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000000_0 is : 1.0
2015-10-17 15:46:45,656 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0015_m_000000_0
2015-10-17 15:46:45,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:46:45,658 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0015_01_000002 taskAttempt attempt_1445062781478_0015_m_000000_0
2015-10-17 15:46:45,658 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0015_m_000000_0
2015-10-17 15:46:45,658 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:46:45,675 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:46:45,682 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0015_m_000000_0
2015-10-17 15:46:45,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:46:45,685 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 15:46:46,200 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:6 RackLocal:4
2015-10-17 15:46:46,201 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-17 15:46:46,201 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 15:46:46,202 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-17 15:46:46,202 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:6 RackLocal:4
2015-10-17 15:46:46,609 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0015_m_000005
2015-10-17 15:46:46,610 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:46:46,610 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0015_m_000005
2015-10-17 15:46:46,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:46:46,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:46:46,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000005_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:46:46,736 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.7344836
2015-10-17 15:46:47,202 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:6 RackLocal:4
2015-10-17 15:46:47,204 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:5120, vCores:-30> knownNMs=5
2015-10-17 15:46:47,204 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000002
2015-10-17 15:46:47,206 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0015_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:46:47,206 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:6 RackLocal:4
2015-10-17 15:46:47,215 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.27825075
2015-10-17 15:46:47,421 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.36317363
2015-10-17 15:46:47,761 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.19495212
2015-10-17 15:46:47,814 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.45446435
2015-10-17 15:46:47,816 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.2770031
2015-10-17 15:46:47,908 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.6208445
2015-10-17 15:46:48,042 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.27811313
2015-10-17 15:46:48,211 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 15:46:48,212 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 15:46:48,212 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0015_01_000013 to attempt_1445062781478_0015_r_000000_0
2015-10-17 15:46:48,212 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0015_01_000014 to attempt_1445062781478_0015_m_000005_1
2015-10-17 15:46:48,212 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:1 HostLocal:7 RackLocal:4
2015-10-17 15:46:48,222 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:46:48,223 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:46:48,223 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:46:48,224 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000005_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:46:48,224 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0015_01_000013 taskAttempt attempt_1445062781478_0015_r_000000_0
2015-10-17 15:46:48,224 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0015_01_000014 taskAttempt attempt_1445062781478_0015_m_000005_1
2015-10-17 15:46:48,224 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0015_r_000000_0
2015-10-17 15:46:48,224 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0015_m_000005_1
2015-10-17 15:46:48,224 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:46:48,225 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:46:48,238 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0015_m_000005_1 : 13562
2015-10-17 15:46:48,239 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0015_m_000005_1] using containerId: [container_1445062781478_0015_01_000014 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:46:48,239 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0015_r_000000_0 : 13562
2015-10-17 15:46:48,239 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000005_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:46:48,239 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0015_m_000005
2015-10-17 15:46:48,239 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0015_r_000000_0] using containerId: [container_1445062781478_0015_01_000013 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:46:48,240 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:46:48,240 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0015_r_000000
2015-10-17 15:46:48,240 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:46:48,998 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.36319977
2015-10-17 15:46:49,215 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-32> knownNMs=5
2015-10-17 15:46:49,745 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.783144
2015-10-17 15:46:50,233 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.30168363
2015-10-17 15:46:50,519 INFO [Socket Reader #1 for port 40658] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0015 (auth:SIMPLE)
2015-10-17 15:46:50,534 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0015_m_000014 asked for a task
2015-10-17 15:46:50,534 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0015_m_000014 given task: attempt_1445062781478_0015_m_000005_1
2015-10-17 15:46:50,793 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.36317363
2015-10-17 15:46:50,824 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.5115939
2015-10-17 15:46:50,850 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.2136473
2015-10-17 15:46:50,898 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.295472
2015-10-17 15:46:50,914 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.6208445
2015-10-17 15:46:51,062 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.27811313
2015-10-17 15:46:51,472 INFO [Socket Reader #1 for port 40658] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0015 (auth:SIMPLE)
2015-10-17 15:46:51,528 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0015_r_000013 asked for a task
2015-10-17 15:46:51,528 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0015_r_000013 given task: attempt_1445062781478_0015_r_000000_0
2015-10-17 15:46:52,318 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.36319977
2015-10-17 15:46:52,754 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.8320052
2015-10-17 15:46:53,046 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 15:46:53,253 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.35399258
2015-10-17 15:46:53,833 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.53543663
2015-10-17 15:46:53,927 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.6480159
2015-10-17 15:46:53,949 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.22838572
2015-10-17 15:46:54,002 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.295472
2015-10-17 15:46:54,057 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:46:54,080 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.28101987
2015-10-17 15:46:54,142 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.36317363
2015-10-17 15:46:54,865 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.6480159
2015-10-17 15:46:55,056 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:46:55,660 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.36319977
2015-10-17 15:46:55,754 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.8870865
2015-10-17 15:46:56,057 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:46:56,271 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.3638923
2015-10-17 15:46:56,833 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.53543663
2015-10-17 15:46:56,926 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.667
2015-10-17 15:46:57,041 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.2442605
2015-10-17 15:46:57,057 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:46:57,094 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.295472
2015-10-17 15:46:57,098 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.34512812
2015-10-17 15:46:57,372 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.36639252
2015-10-17 15:46:57,745 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.102966405
2015-10-17 15:46:58,056 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:46:58,756 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.9362815
2015-10-17 15:46:58,918 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.36319977
2015-10-17 15:46:58,982 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.033333335
2015-10-17 15:46:59,058 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:46:59,293 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.3638923
2015-10-17 15:46:59,834 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.53543663
2015-10-17 15:46:59,930 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.667
2015-10-17 15:47:00,058 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:47:00,113 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.25598422
2015-10-17 15:47:00,118 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.3637686
2015-10-17 15:47:00,184 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.3222481
2015-10-17 15:47:00,563 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.38007197
2015-10-17 15:47:00,766 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.10685723
2015-10-17 15:47:01,058 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:47:01,611 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0015_m_000001
2015-10-17 15:47:01,611 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:47:01,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0015_m_000001
2015-10-17 15:47:01,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:47:01,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:47:01,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000001_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:47:01,755 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 0.9905405
2015-10-17 15:47:01,998 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.033333335
2015-10-17 15:47:02,058 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:47:02,229 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:1 HostLocal:7 RackLocal:4
2015-10-17 15:47:02,230 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-32> knownNMs=5
2015-10-17 15:47:02,312 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.38655856
2015-10-17 15:47:02,365 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000003_0 is : 1.0
2015-10-17 15:47:02,367 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0015_m_000003_0
2015-10-17 15:47:02,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:47:02,367 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0015_01_000005 taskAttempt attempt_1445062781478_0015_m_000003_0
2015-10-17 15:47:02,368 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0015_m_000003_0
2015-10-17 15:47:02,368 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:47:02,378 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:47:02,379 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0015_m_000003_0
2015-10-17 15:47:02,379 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:47:02,379 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 15:47:02,842 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.5750935
2015-10-17 15:47:02,933 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.67305523
2015-10-17 15:47:02,989 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.36319977
2015-10-17 15:47:03,058 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 15:47:03,136 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.3637686
2015-10-17 15:47:03,186 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.27324772
2015-10-17 15:47:03,230 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:13 ContRel:1 HostLocal:7 RackLocal:4
2015-10-17 15:47:03,232 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:47:03,233 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:47:03,233 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0015_01_000015 to attempt_1445062781478_0015_m_000001_1
2015-10-17 15:47:03,233 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:7 RackLocal:5
2015-10-17 15:47:03,234 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:47:03,234 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000001_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:47:03,234 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0015_01_000015 taskAttempt attempt_1445062781478_0015_m_000001_1
2015-10-17 15:47:03,234 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0015_m_000001_1
2015-10-17 15:47:03,235 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:47:03,258 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.3312482
2015-10-17 15:47:03,687 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0015_m_000001_1 : 13562
2015-10-17 15:47:03,687 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0015_m_000001_1] using containerId: [container_1445062781478_0015_01_000015 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:51951]
2015-10-17 15:47:03,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000001_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:47:03,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0015_m_000001
2015-10-17 15:47:03,783 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.10685723
2015-10-17 15:47:03,851 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.41036034
2015-10-17 15:47:04,058 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:04,235 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=4 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:5120, vCores:-30> knownNMs=5
2015-10-17 15:47:04,235 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000005
2015-10-17 15:47:04,235 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:7 RackLocal:5
2015-10-17 15:47:04,235 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0015_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:47:05,012 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.06666667
2015-10-17 15:47:05,058 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:05,332 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.43984106
2015-10-17 15:47:05,857 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.6210422
2015-10-17 15:47:05,943 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.7057567
2015-10-17 15:47:06,058 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:06,154 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.37092695
2015-10-17 15:47:06,195 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.38397956
2015-10-17 15:47:06,403 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.27813601
2015-10-17 15:47:06,443 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.34834456
2015-10-17 15:47:06,802 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.12924723
2015-10-17 15:47:06,952 INFO [Socket Reader #1 for port 40658] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0015 (auth:SIMPLE)
2015-10-17 15:47:07,034 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.4266445
2015-10-17 15:47:07,057 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:07,890 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0015_m_000015 asked for a task
2015-10-17 15:47:07,890 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0015_m_000015 given task: attempt_1445062781478_0015_m_000001_1
2015-10-17 15:47:08,021 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.06666667
2015-10-17 15:47:08,058 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:08,350 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.44964966
2015-10-17 15:47:08,871 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.6210422
2015-10-17 15:47:08,942 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.75825083
2015-10-17 15:47:09,057 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:09,172 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.43489355
2015-10-17 15:47:09,434 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.4067759
2015-10-17 15:47:09,537 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.27813601
2015-10-17 15:47:09,561 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.3645531
2015-10-17 15:47:09,820 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.18837634
2015-10-17 15:47:10,057 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:10,372 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.44702384
2015-10-17 15:47:11,025 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.06666667
2015-10-17 15:47:11,057 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:11,368 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.44964966
2015-10-17 15:47:11,880 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.6210422
2015-10-17 15:47:11,942 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.80310273
2015-10-17 15:47:12,057 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:12,190 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.44950172
2015-10-17 15:47:12,860 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.19247705
2015-10-17 15:47:12,880 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.27813601
2015-10-17 15:47:12,991 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.39785367
2015-10-17 15:47:13,058 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:13,270 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.43348342
2015-10-17 15:47:13,781 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.44859612
2015-10-17 15:47:14,038 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.06666667
2015-10-17 15:47:14,056 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:14,389 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.46029085
2015-10-17 15:47:14,890 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.6520487
2015-10-17 15:47:14,949 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.84840393
2015-10-17 15:47:15,058 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:15,223 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.44950172
2015-10-17 15:47:15,765 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.6520487
2015-10-17 15:47:15,893 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.19247705
2015-10-17 15:47:16,058 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:16,612 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0015_m_000002
2015-10-17 15:47:16,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0015_m_000002
2015-10-17 15:47:16,612 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:47:16,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:47:16,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:47:16,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000002_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:47:16,749 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.3022329
2015-10-17 15:47:16,921 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.010742675
2015-10-17 15:47:16,990 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.41586033
2015-10-17 15:47:17,004 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.448704
2015-10-17 15:47:17,039 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.06666667
2015-10-17 15:47:17,057 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:17,141 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.44859612
2015-10-17 15:47:17,249 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:7 RackLocal:5
2015-10-17 15:47:17,250 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:5120, vCores:-30> knownNMs=5
2015-10-17 15:47:17,408 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.49526364
2015-10-17 15:47:17,904 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.667
2015-10-17 15:47:17,958 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.8881068
2015-10-17 15:47:18,058 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:18,243 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.44950172
2015-10-17 15:47:18,252 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:47:18,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0015_01_000016 to attempt_1445062781478_0015_m_000002_1
2015-10-17 15:47:18,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:47:18,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:47:18,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000002_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:47:18,254 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0015_01_000016 taskAttempt attempt_1445062781478_0015_m_000002_1
2015-10-17 15:47:18,254 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0015_m_000002_1
2015-10-17 15:47:18,254 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:47:18,268 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0015_m_000002_1 : 13562
2015-10-17 15:47:18,268 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0015_m_000002_1] using containerId: [container_1445062781478_0015_01_000016 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:47:18,268 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000002_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:47:18,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0015_m_000002
2015-10-17 15:47:18,913 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.19247705
2015-10-17 15:47:19,061 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:19,254 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:5120, vCores:-30> knownNMs=5
2015-10-17 15:47:19,797 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.3318701
2015-10-17 15:47:19,971 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.022470115
2015-10-17 15:47:20,048 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.06666667
2015-10-17 15:47:20,060 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:20,100 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.4761695
2015-10-17 15:47:20,313 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.448704
2015-10-17 15:47:20,426 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.5352825
2015-10-17 15:47:20,527 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.44859612
2015-10-17 15:47:20,924 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.667
2015-10-17 15:47:20,941 INFO [Socket Reader #1 for port 40658] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0015 (auth:SIMPLE)
2015-10-17 15:47:20,961 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0015_m_000016 asked for a task
2015-10-17 15:47:20,961 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0015_m_000016 given task: attempt_1445062781478_0015_m_000002_1
2015-10-17 15:47:20,966 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.9316211
2015-10-17 15:47:21,061 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:21,271 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.48138365
2015-10-17 15:47:21,942 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.23308897
2015-10-17 15:47:22,061 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:22,892 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.35206342
2015-10-17 15:47:23,061 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:23,063 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.06666667
2015-10-17 15:47:23,124 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.037775017
2015-10-17 15:47:23,194 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.5323719
2015-10-17 15:47:23,454 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.5352825
2015-10-17 15:47:23,777 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.448704
2015-10-17 15:47:23,873 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.44859612
2015-10-17 15:47:23,935 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.667
2015-10-17 15:47:23,984 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.9644294
2015-10-17 15:47:24,061 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:24,299 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.5058598
2015-10-17 15:47:24,970 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.2575332
2015-10-17 15:47:25,061 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:25,994 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.36390656
2015-10-17 15:47:26,061 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:26,075 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.06666667
2015-10-17 15:47:26,275 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.06285491
2015-10-17 15:47:26,321 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.5323719
2015-10-17 15:47:26,481 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.5352825
2015-10-17 15:47:26,954 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.6917217
2015-10-17 15:47:26,999 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 0.9975282
2015-10-17 15:47:27,030 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.448704
2015-10-17 15:47:27,061 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:27,157 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.44859612
2015-10-17 15:47:27,327 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000004_0 is : 1.0
2015-10-17 15:47:27,329 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0015_m_000004_0
2015-10-17 15:47:27,329 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:47:27,329 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0015_01_000006 taskAttempt attempt_1445062781478_0015_m_000004_0
2015-10-17 15:47:27,329 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0015_m_000004_0
2015-10-17 15:47:27,330 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:47:27,332 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.52731735
2015-10-17 15:47:27,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:47:27,343 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0015_m_000004_0
2015-10-17 15:47:27,343 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:47:27,343 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 15:47:28,000 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.2750053
2015-10-17 15:47:28,061 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 15:47:28,250 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.095078476
2015-10-17 15:47:28,263 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:47:29,061 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:29,095 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.10000001
2015-10-17 15:47:29,265 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000006
2015-10-17 15:47:29,265 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:47:29,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0015_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:47:29,340 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.36390656
2015-10-17 15:47:29,500 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.55328846
2015-10-17 15:47:29,545 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.09346759
2015-10-17 15:47:29,585 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.5323719
2015-10-17 15:47:29,962 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.7280973
2015-10-17 15:47:30,061 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:30,170 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.448704
2015-10-17 15:47:30,233 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.45693335
2015-10-17 15:47:30,366 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.53521925
2015-10-17 15:47:31,029 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.27813601
2015-10-17 15:47:31,062 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:31,267 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.10660437
2015-10-17 15:47:32,061 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:32,110 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.10000001
2015-10-17 15:47:32,468 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.36390656
2015-10-17 15:47:32,520 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.59152824
2015-10-17 15:47:32,653 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.1066108
2015-10-17 15:47:32,707 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.5323719
2015-10-17 15:47:32,972 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.7728463
2015-10-17 15:47:33,062 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:33,243 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.45628193
2015-10-17 15:47:33,297 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.4976413
2015-10-17 15:47:33,397 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.53521925
2015-10-17 15:47:34,056 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.27813601
2015-10-17 15:47:34,062 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:34,282 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.10660437
2015-10-17 15:47:35,061 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:35,126 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.10000001
2015-10-17 15:47:35,538 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.620844
2015-10-17 15:47:35,853 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.36390656
2015-10-17 15:47:35,978 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.8222863
2015-10-17 15:47:36,005 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.1066108
2015-10-17 15:47:36,061 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.5323719
2015-10-17 15:47:36,061 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:36,416 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.53521925
2015-10-17 15:47:36,979 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.47321686
2015-10-17 15:47:37,061 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:37,074 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.2820096
2015-10-17 15:47:37,298 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.10660437
2015-10-17 15:47:37,854 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.5214188
2015-10-17 15:47:38,061 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:38,142 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.10000001
2015-10-17 15:47:38,564 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.620844
2015-10-17 15:47:38,910 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.36390656
2015-10-17 15:47:38,984 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.869536
2015-10-17 15:47:39,061 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:39,064 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.1066108
2015-10-17 15:47:39,121 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.5985843
2015-10-17 15:47:39,443 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.5498574
2015-10-17 15:47:40,062 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:40,104 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.3078149
2015-10-17 15:47:40,191 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.50480735
2015-10-17 15:47:40,313 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.1517675
2015-10-17 15:47:41,061 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:41,140 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.5342037
2015-10-17 15:47:41,159 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.10000001
2015-10-17 15:47:41,591 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.620844
2015-10-17 15:47:41,975 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.38307005
2015-10-17 15:47:41,999 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.9041418
2015-10-17 15:47:42,062 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:42,137 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.1066108
2015-10-17 15:47:42,244 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.6426921
2015-10-17 15:47:42,471 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.5726485
2015-10-17 15:47:43,061 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:43,133 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.3303531
2015-10-17 15:47:43,328 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.19212553
2015-10-17 15:47:43,375 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.53425497
2015-10-17 15:47:43,497 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.6426921
2015-10-17 15:47:44,062 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:44,173 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.10000001
2015-10-17 15:47:44,632 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.6363881
2015-10-17 15:47:45,010 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 0.9568348
2015-10-17 15:47:45,061 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:45,226 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.41842192
2015-10-17 15:47:45,513 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.5982757
2015-10-17 15:47:45,523 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.1066108
2015-10-17 15:47:45,572 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.667
2015-10-17 15:47:46,063 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:46,175 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.35414803
2015-10-17 15:47:46,347 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.19212553
2015-10-17 15:47:46,562 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.53425497
2015-10-17 15:47:46,729 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.5342037
2015-10-17 15:47:47,062 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:47,189 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.10000001
2015-10-17 15:47:47,501 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000006_0 is : 1.0
2015-10-17 15:47:47,504 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0015_m_000006_0
2015-10-17 15:47:47,505 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:47:47,505 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0015_01_000008 taskAttempt attempt_1445062781478_0015_m_000006_0
2015-10-17 15:47:47,506 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0015_m_000006_0
2015-10-17 15:47:47,508 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:47:47,528 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:47:47,529 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0015_m_000006_0
2015-10-17 15:47:47,529 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:47:47,530 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 15:47:47,668 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.6607407
2015-10-17 15:47:48,063 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:47:48,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:47:48,472 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.6607407
2015-10-17 15:47:48,490 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.44950968
2015-10-17 15:47:48,546 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.6207798
2015-10-17 15:47:48,759 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.11843929
2015-10-17 15:47:48,880 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.667
2015-10-17 15:47:49,063 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:47:49,220 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.36390656
2015-10-17 15:47:49,293 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000008
2015-10-17 15:47:49,294 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:47:49,294 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0015_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:47:49,361 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.19212553
2015-10-17 15:47:49,738 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.53425497
2015-10-17 15:47:49,925 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.5342037
2015-10-17 15:47:50,062 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:47:50,206 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:47:50,687 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.667
2015-10-17 15:47:51,062 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:47:51,566 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.6207798
2015-10-17 15:47:51,697 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.44950968
2015-10-17 15:47:52,063 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:47:52,106 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.17293617
2015-10-17 15:47:52,212 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.667
2015-10-17 15:47:52,257 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.36390656
2015-10-17 15:47:52,378 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.19405268
2015-10-17 15:47:52,882 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.53425497
2015-10-17 15:47:53,062 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:47:53,079 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.56065816
2015-10-17 15:47:53,221 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:47:53,723 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.667
2015-10-17 15:47:54,063 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:47:54,598 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.6207798
2015-10-17 15:47:54,958 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.44950968
2015-10-17 15:47:55,063 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:47:55,292 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.36390656
2015-10-17 15:47:55,301 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.19211523
2015-10-17 15:47:55,394 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.24427226
2015-10-17 15:47:55,421 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.667
2015-10-17 15:47:56,062 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:47:56,237 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:47:56,246 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.55789804
2015-10-17 15:47:56,408 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.6194932
2015-10-17 15:47:56,756 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.667
2015-10-17 15:47:57,062 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:47:57,624 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.6207798
2015-10-17 15:47:58,035 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.44950968
2015-10-17 15:47:58,061 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:47:58,317 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.36672592
2015-10-17 15:47:58,346 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.19211523
2015-10-17 15:47:58,401 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.27772525
2015-10-17 15:47:58,475 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.667
2015-10-17 15:47:59,062 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:47:59,253 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:47:59,499 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.6197233
2015-10-17 15:47:59,623 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.6196791
2015-10-17 15:47:59,794 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.667
2015-10-17 15:48:00,062 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:00,666 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.63507944
2015-10-17 15:48:01,063 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:01,124 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.44950968
2015-10-17 15:48:01,358 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.3933855
2015-10-17 15:48:01,411 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.27772525
2015-10-17 15:48:01,488 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.19211523
2015-10-17 15:48:01,581 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.6809463
2015-10-17 15:48:02,063 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:02,268 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:48:02,672 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.6197233
2015-10-17 15:48:02,800 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.6196791
2015-10-17 15:48:02,838 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.66927654
2015-10-17 15:48:03,063 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:03,707 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.6559559
2015-10-17 15:48:04,063 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:04,178 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.44950968
2015-10-17 15:48:04,398 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.41397968
2015-10-17 15:48:04,417 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.27772525
2015-10-17 15:48:04,536 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.19211523
2015-10-17 15:48:04,629 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.7307717
2015-10-17 15:48:05,063 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:05,284 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:48:05,312 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.6559559
2015-10-17 15:48:05,810 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.6197233
2015-10-17 15:48:05,876 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.6842294
2015-10-17 15:48:06,015 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.6196791
2015-10-17 15:48:06,063 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:06,748 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.667
2015-10-17 15:48:07,063 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:07,325 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.49368775
2015-10-17 15:48:07,423 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.3032654
2015-10-17 15:48:07,424 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.43691805
2015-10-17 15:48:07,628 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.19211523
2015-10-17 15:48:07,945 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.7759415
2015-10-17 15:48:08,063 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:08,301 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:48:08,904 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.699671
2015-10-17 15:48:09,019 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.6197233
2015-10-17 15:48:09,063 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:09,144 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.6196791
2015-10-17 15:48:09,772 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.667
2015-10-17 15:48:10,062 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:10,441 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.35298955
2015-10-17 15:48:10,446 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.44950968
2015-10-17 15:48:10,666 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.5264648
2015-10-17 15:48:10,897 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.19286336
2015-10-17 15:48:11,062 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:11,312 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:48:11,314 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.8250686
2015-10-17 15:48:11,924 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.7210623
2015-10-17 15:48:12,062 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:12,184 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.6197233
2015-10-17 15:48:12,275 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.6611354
2015-10-17 15:48:12,792 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.667
2015-10-17 15:48:12,911 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.6611354
2015-10-17 15:48:13,062 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:13,455 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.36317363
2015-10-17 15:48:13,467 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.44950968
2015-10-17 15:48:13,971 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.23771028
2015-10-17 15:48:14,062 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:14,271 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.5352021
2015-10-17 15:48:14,322 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:48:14,385 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.8696928
2015-10-17 15:48:14,943 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.75002635
2015-10-17 15:48:15,062 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:15,285 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.64908653
2015-10-17 15:48:15,390 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.667
2015-10-17 15:48:15,811 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.6970399
2015-10-17 15:48:16,062 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:16,477 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.36317363
2015-10-17 15:48:16,486 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.4739429
2015-10-17 15:48:17,063 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:17,303 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.2724525
2015-10-17 15:48:17,323 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:48:17,962 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.78615427
2015-10-17 15:48:18,065 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:18,070 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.64908653
2015-10-17 15:48:18,086 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.9202993
2015-10-17 15:48:18,122 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.5352021
2015-10-17 15:48:18,830 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.73458016
2015-10-17 15:48:18,954 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.667
2015-10-17 15:48:19,065 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:19,488 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.36317363
2015-10-17 15:48:19,503 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.52913475
2015-10-17 15:48:19,514 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.667
2015-10-17 15:48:20,065 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:20,334 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:48:20,508 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.27776006
2015-10-17 15:48:20,979 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.82289124
2015-10-17 15:48:21,065 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:21,275 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 0.961687
2015-10-17 15:48:21,369 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.5352021
2015-10-17 15:48:21,848 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.7700238
2015-10-17 15:48:22,066 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:22,094 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.667
2015-10-17 15:48:22,505 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.41361427
2015-10-17 15:48:22,521 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.5352021
2015-10-17 15:48:22,734 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.667
2015-10-17 15:48:23,065 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:23,340 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:48:23,681 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.27776006
2015-10-17 15:48:23,997 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.8574554
2015-10-17 15:48:24,065 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:24,439 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.5352021
2015-10-17 15:48:24,454 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 1.0
2015-10-17 15:48:24,522 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000009_0 is : 1.0
2015-10-17 15:48:24,529 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0015_m_000009_0
2015-10-17 15:48:24,529 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:48:24,530 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0015_01_000011 taskAttempt attempt_1445062781478_0015_m_000009_0
2015-10-17 15:48:24,530 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0015_m_000009_0
2015-10-17 15:48:24,531 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:48:24,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:48:24,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0015_m_000009_0
2015-10-17 15:48:24,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:48:24,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 15:48:24,866 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.8048604
2015-10-17 15:48:25,066 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:48:25,292 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.66884017
2015-10-17 15:48:25,340 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:48:25,520 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.44859612
2015-10-17 15:48:25,537 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.5352021
2015-10-17 15:48:25,933 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.667
2015-10-17 15:48:26,065 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 15:48:26,340 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:48:26,342 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000011
2015-10-17 15:48:26,342 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:48:26,342 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0015_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:48:27,001 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.27776006
2015-10-17 15:48:27,015 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.8922392
2015-10-17 15:48:27,065 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 15:48:27,704 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.5352021
2015-10-17 15:48:27,884 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.84036577
2015-10-17 15:48:28,065 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 15:48:28,536 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.44859612
2015-10-17 15:48:28,555 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.5634544
2015-10-17 15:48:28,572 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.69399077
2015-10-17 15:48:29,065 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 15:48:29,239 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.667
2015-10-17 15:48:29,340 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:48:30,031 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.9304173
2015-10-17 15:48:30,064 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 15:48:30,294 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.27776006
2015-10-17 15:48:30,901 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.8778561
2015-10-17 15:48:30,992 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.5352021
2015-10-17 15:48:31,065 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 15:48:31,550 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.44859612
2015-10-17 15:48:31,572 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.6163268
2015-10-17 15:48:31,760 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.7196426
2015-10-17 15:48:32,066 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 15:48:32,350 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.13333334
2015-10-17 15:48:32,440 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.6871202
2015-10-17 15:48:33,051 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 0.9671788
2015-10-17 15:48:33,065 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 15:48:33,394 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.27776006
2015-10-17 15:48:33,919 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.9136357
2015-10-17 15:48:34,065 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 15:48:34,071 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.5629965
2015-10-17 15:48:34,568 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.49230593
2015-10-17 15:48:34,594 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.6209487
2015-10-17 15:48:35,064 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.74695516
2015-10-17 15:48:35,065 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 15:48:35,365 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.16666667
2015-10-17 15:48:35,641 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.71554613
2015-10-17 15:48:35,980 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000007_0 is : 1.0
2015-10-17 15:48:35,981 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0015_m_000007_0
2015-10-17 15:48:35,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:48:35,982 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0015_01_000009 taskAttempt attempt_1445062781478_0015_m_000007_0
2015-10-17 15:48:35,982 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0015_m_000007_0
2015-10-17 15:48:35,983 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:48:35,991 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:48:35,991 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0015_m_000007_0
2015-10-17 15:48:35,992 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:48:35,992 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 15:48:36,066 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 15:48:36,352 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:48:36,457 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.27776006
2015-10-17 15:48:36,938 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.94817716
2015-10-17 15:48:37,066 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 15:48:37,276 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.60521686
2015-10-17 15:48:37,354 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000009
2015-10-17 15:48:37,355 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:48:37,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0015_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:48:37,585 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.5342037
2015-10-17 15:48:37,623 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.6209487
2015-10-17 15:48:38,066 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 15:48:38,235 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.77455753
2015-10-17 15:48:38,378 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.20000002
2015-10-17 15:48:38,797 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.74338996
2015-10-17 15:48:39,066 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 15:48:39,906 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.31037673
2015-10-17 15:48:39,967 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.9814741
2015-10-17 15:48:40,065 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 15:48:40,380 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.6209487
2015-10-17 15:48:40,600 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.5342037
2015-10-17 15:48:40,663 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.63631064
2015-10-17 15:48:41,065 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 15:48:41,397 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.20000002
2015-10-17 15:48:42,065 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 15:48:42,340 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.77552724
2015-10-17 15:48:42,625 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.8136377
2015-10-17 15:48:43,007 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 0.99697477
2015-10-17 15:48:43,066 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 15:48:43,066 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.35661998
2015-10-17 15:48:43,551 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.6209487
2015-10-17 15:48:43,615 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.5342037
2015-10-17 15:48:43,703 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.65485114
2015-10-17 15:48:43,830 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000008_0 is : 1.0
2015-10-17 15:48:43,832 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0015_m_000008_0
2015-10-17 15:48:43,833 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:48:43,833 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0015_01_000010 taskAttempt attempt_1445062781478_0015_m_000008_0
2015-10-17 15:48:43,834 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0015_m_000008_0
2015-10-17 15:48:43,834 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:48:43,853 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:48:43,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0015_m_000008_0
2015-10-17 15:48:43,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:48:43,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 15:48:44,065 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 15:48:44,365 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:48:44,412 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.20000002
2015-10-17 15:48:45,066 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:45,370 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000010
2015-10-17 15:48:45,371 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:48:45,371 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0015_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:48:45,513 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.80203456
2015-10-17 15:48:45,697 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.65485114
2015-10-17 15:48:45,767 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.839952
2015-10-17 15:48:46,066 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:46,115 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.36319977
2015-10-17 15:48:46,632 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.53966445
2015-10-17 15:48:46,744 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.667
2015-10-17 15:48:46,938 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.6209487
2015-10-17 15:48:47,067 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:47,430 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.23333333
2015-10-17 15:48:48,066 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:48,656 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.83145356
2015-10-17 15:48:48,957 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.86882627
2015-10-17 15:48:49,066 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:49,183 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.36319977
2015-10-17 15:48:49,648 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.5940383
2015-10-17 15:48:49,770 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.667
2015-10-17 15:48:50,019 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.6209487
2015-10-17 15:48:50,066 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:50,442 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.23333333
2015-10-17 15:48:51,066 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:51,813 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.8590173
2015-10-17 15:48:52,066 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:52,100 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.89572906
2015-10-17 15:48:52,246 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.36319977
2015-10-17 15:48:52,662 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.6196791
2015-10-17 15:48:52,796 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.667
2015-10-17 15:48:53,066 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:53,103 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.6209487
2015-10-17 15:48:53,452 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.23333333
2015-10-17 15:48:54,066 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:54,849 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.8856935
2015-10-17 15:48:55,066 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:55,298 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.92313147
2015-10-17 15:48:55,317 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.36319977
2015-10-17 15:48:55,676 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.6196791
2015-10-17 15:48:55,824 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.6933235
2015-10-17 15:48:56,066 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:56,179 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.6582309
2015-10-17 15:48:56,452 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.23333333
2015-10-17 15:48:56,601 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.6582309
2015-10-17 15:48:57,066 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:58,034 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.9124021
2015-10-17 15:48:58,066 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:58,361 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.36319977
2015-10-17 15:48:58,538 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.95104647
2015-10-17 15:48:58,695 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.6196791
2015-10-17 15:48:58,851 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.72638553
2015-10-17 15:48:59,066 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:48:59,267 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.667
2015-10-17 15:48:59,460 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.23333333
2015-10-17 15:49:00,067 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:49:01,067 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:49:01,152 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.93808734
2015-10-17 15:49:01,418 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.3976561
2015-10-17 15:49:01,702 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 0.97591007
2015-10-17 15:49:01,711 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.6656581
2015-10-17 15:49:01,803 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.6656581
2015-10-17 15:49:01,877 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.7597788
2015-10-17 15:49:02,067 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:49:02,311 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.667
2015-10-17 15:49:02,472 INFO [IPC Server handler 28 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.23333333
2015-10-17 15:49:03,067 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:49:04,066 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:49:04,332 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.9659047
2015-10-17 15:49:04,694 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_0 is : 1.0
2015-10-17 15:49:04,711 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0015_m_000002_0
2015-10-17 15:49:04,712 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:49:04,712 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0015_01_000004 taskAttempt attempt_1445062781478_0015_m_000002_0
2015-10-17 15:49:04,712 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0015_m_000002_0
2015-10-17 15:49:04,714 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 15:49:04,724 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000002_1 is : 0.667
2015-10-17 15:49:04,903 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.7933222
2015-10-17 15:49:04,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:49:04,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0015_m_000002_0
2015-10-17 15:49:04,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0015_m_000002_1
2015-10-17 15:49:04,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:49:04,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 15:49:04,913 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000002_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 15:49:04,913 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0015_01_000016 taskAttempt attempt_1445062781478_0015_m_000002_1
2015-10-17 15:49:04,914 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0015_m_000002_1
2015-10-17 15:49:04,915 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:49:04,936 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000002_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 15:49:04,937 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 15:49:04,954 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445062781478_0015_m_000002_1
2015-10-17 15:49:04,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000002_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 15:49:04,964 INFO [Socket Reader #1 for port 40658] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 40658: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 15:49:05,067 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:49:05,392 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:49:05,491 INFO [IPC Server handler 4 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.23333333
2015-10-17 15:49:06,058 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.4478234
2015-10-17 15:49:06,058 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.667
2015-10-17 15:49:06,070 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:49:06,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000004
2015-10-17 15:49:06,396 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000016
2015-10-17 15:49:06,396 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0015_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:49:06,396 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:49:06,396 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0015_m_000002_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:49:07,070 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:49:07,542 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 0.99112403
2015-10-17 15:49:07,931 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.8264852
2015-10-17 15:49:08,071 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:49:08,511 INFO [IPC Server handler 27 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.23333333
2015-10-17 15:49:09,071 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:49:09,182 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.667
2015-10-17 15:49:09,183 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.448704
2015-10-17 15:49:10,072 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:49:10,636 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 1.0
2015-10-17 15:49:10,957 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.85955364
2015-10-17 15:49:11,072 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:49:11,529 INFO [IPC Server handler 9 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.23333333
2015-10-17 15:49:12,072 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:49:12,195 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_0 is : 1.0
2015-10-17 15:49:12,263 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000001_1 is : 0.448704
2015-10-17 15:49:12,273 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0015_m_000001_0
2015-10-17 15:49:12,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:49:12,274 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0015_01_000003 taskAttempt attempt_1445062781478_0015_m_000001_0
2015-10-17 15:49:12,275 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0015_m_000001_0
2015-10-17 15:49:12,275 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 15:49:12,278 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.6838446
2015-10-17 15:49:12,727 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:49:12,728 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0015_m_000001_0
2015-10-17 15:49:12,728 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0015_m_000001_1
2015-10-17 15:49:12,729 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:49:12,729 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 15:49:12,730 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000001_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 15:49:12,730 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0015_01_000015 taskAttempt attempt_1445062781478_0015_m_000001_1
2015-10-17 15:49:12,730 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0015_m_000001_1
2015-10-17 15:49:12,732 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:49:12,901 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000001_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 15:49:12,902 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 15:49:12,905 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445062781478_0015_m_000001_1
2015-10-17 15:49:12,906 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000001_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 15:49:13,072 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:49:13,141 INFO [Socket Reader #1 for port 40658] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 40658: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 15:49:13,405 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:49:13,983 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.8926681
2015-10-17 15:49:14,072 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:49:14,407 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000015
2015-10-17 15:49:14,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000003
2015-10-17 15:49:14,408 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0015_m_000001_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:49:14,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:49:14,408 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0015_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:49:14,545 INFO [IPC Server handler 24 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.23333333
2015-10-17 15:49:15,073 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:49:15,325 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.7078951
2015-10-17 15:49:16,073 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:49:17,011 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.9248849
2015-10-17 15:49:17,074 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:49:17,564 INFO [IPC Server handler 11 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.26666668
2015-10-17 15:49:18,074 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:49:18,381 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.7368315
2015-10-17 15:49:19,074 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:49:20,038 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.95715636
2015-10-17 15:49:20,074 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:49:20,578 INFO [IPC Server handler 26 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.26666668
2015-10-17 15:49:21,074 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:49:21,453 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_0 is : 0.76596457
2015-10-17 15:49:22,074 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:49:23,065 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 0.98962176
2015-10-17 15:49:23,074 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:49:23,596 INFO [IPC Server handler 22 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.26666668
2015-10-17 15:49:24,074 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:49:24,147 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_m_000005_1 is : 1.0
2015-10-17 15:49:24,149 INFO [IPC Server handler 13 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0015_m_000005_1
2015-10-17 15:49:24,150 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000005_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:49:24,150 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0015_01_000014 taskAttempt attempt_1445062781478_0015_m_000005_1
2015-10-17 15:49:24,151 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0015_m_000005_1
2015-10-17 15:49:24,152 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:49:24,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000005_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:49:24,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0015_m_000005_1
2015-10-17 15:49:24,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0015_m_000005_0
2015-10-17 15:49:24,171 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:49:24,171 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 15:49:24,172 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000005_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 15:49:24,172 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0015_01_000007 taskAttempt attempt_1445062781478_0015_m_000005_0
2015-10-17 15:49:24,173 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0015_m_000005_0
2015-10-17 15:49:24,173 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:49:24,300 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000005_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 15:49:24,301 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 15:49:24,305 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445062781478_0015_m_000005_0
2015-10-17 15:49:24,305 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_m_000005_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 15:49:24,381 INFO [Socket Reader #1 for port 40658] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 40658: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 15:49:24,419 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:49:25,075 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:49:25,424 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000007
2015-10-17 15:49:25,424 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000014
2015-10-17 15:49:25,425 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0015_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:49:25,425 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:49:25,425 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0015_m_000005_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:49:26,074 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 15:49:26,610 INFO [IPC Server handler 10 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.3
2015-10-17 15:49:27,074 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 15:49:28,074 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0015_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 15:49:28,127 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.3
2015-10-17 15:49:28,171 INFO [IPC Server handler 0 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.3
2015-10-17 15:49:29,626 INFO [IPC Server handler 7 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.6719566
2015-10-17 15:49:32,633 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.69203454
2015-10-17 15:49:35,631 INFO [IPC Server handler 23 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.71751565
2015-10-17 15:49:38,639 INFO [IPC Server handler 25 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.73901767
2015-10-17 15:49:41,655 INFO [IPC Server handler 18 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.7550194
2015-10-17 15:49:44,672 INFO [IPC Server handler 29 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.7720804
2015-10-17 15:49:47,683 INFO [IPC Server handler 17 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.7903959
2015-10-17 15:49:50,703 INFO [IPC Server handler 5 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.80756795
2015-10-17 15:49:53,720 INFO [IPC Server handler 1 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.82513607
2015-10-17 15:49:56,735 INFO [IPC Server handler 2 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.8415024
2015-10-17 15:49:59,742 INFO [IPC Server handler 3 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.8621995
2015-10-17 15:50:02,751 INFO [IPC Server handler 12 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.8809179
2015-10-17 15:50:05,757 INFO [IPC Server handler 16 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.9048852
2015-10-17 15:50:08,764 INFO [IPC Server handler 14 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.93512964
2015-10-17 15:50:11,782 INFO [IPC Server handler 20 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.96095467
2015-10-17 15:50:14,797 INFO [IPC Server handler 6 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 0.98627937
2015-10-17 15:50:16,382 INFO [IPC Server handler 8 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445062781478_0015_r_000000_0
2015-10-17 15:50:16,383 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-17 15:50:16,383 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445062781478_0015_r_000000_0 given a go for committing the task output.
2015-10-17 15:50:16,384 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445062781478_0015_r_000000_0
2015-10-17 15:50:16,385 INFO [IPC Server handler 21 on 40658] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445062781478_0015_r_000000_0:true
2015-10-17 15:50:16,406 INFO [IPC Server handler 15 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0015_r_000000_0 is : 1.0
2015-10-17 15:50:16,408 INFO [IPC Server handler 19 on 40658] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0015_r_000000_0
2015-10-17 15:50:16,408 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:50:16,409 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0015_01_000013 taskAttempt attempt_1445062781478_0015_r_000000_0
2015-10-17 15:50:16,410 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0015_r_000000_0
2015-10-17 15:50:16,411 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:50:16,434 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0015_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:50:16,434 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0015_r_000000_0
2015-10-17 15:50:16,435 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0015_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:50:16,435 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 15:50:16,437 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0015Job Transitioned from RUNNING to COMMITTING
2015-10-17 15:50:16,438 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-17 15:50:16,515 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-17 15:50:16,517 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0015Job Transitioned from COMMITTING to SUCCEEDED
2015-10-17 15:50:16,517 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:50:16,519 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-17 15:50:16,519 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-17 15:50:16,519 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-17 15:50:16,519 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-17 15:50:16,519 INFO [Thread-104] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-17 15:50:16,519 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-17 15:50:16,520 INFO [Thread-104] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-17 15:50:16,665 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0015/job_1445062781478_0015_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0015-1445067477119-msrabi-pagerank-1445068216511-10-1-SUCCEEDED-default-1445067831801.jhist_tmp
2015-10-17 15:50:17,523 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0015_01_000013
2015-10-17 15:50:17,523 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:50:17,523 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0015_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:50:37,707 INFO [Thread-107] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as ***********:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-17 15:50:37,708 INFO [Thread-107] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073742539_1734
2015-10-17 15:50:37,719 INFO [Thread-107] org.apache.hadoop.hdfs.DFSClient: Excluding datanode ***********:50010
2015-10-17 15:50:37,825 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0015-1445067477119-msrabi-pagerank-1445068216511-10-1-SUCCEEDED-default-1445067831801.jhist_tmp
2015-10-17 15:50:37,830 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0015/job_1445062781478_0015_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0015_conf.xml_tmp
2015-10-17 15:50:58,880 INFO [Thread-110] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as ***********:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-17 15:50:58,881 INFO [Thread-110] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073742541_1736
2015-10-17 15:50:58,883 INFO [Thread-110] org.apache.hadoop.hdfs.DFSClient: Excluding datanode ***********:50010
2015-10-17 15:50:59,024 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0015_conf.xml_tmp
2015-10-17 15:50:59,030 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0015.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0015.summary
2015-10-17 15:50:59,033 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0015_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0015_conf.xml
2015-10-17 15:50:59,037 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0015-1445067477119-msrabi-pagerank-1445068216511-10-1-SUCCEEDED-default-1445067831801.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0015-1445067477119-msrabi-pagerank-1445068216511-10-1-SUCCEEDED-default-1445067831801.jhist
2015-10-17 15:50:59,038 INFO [Thread-104] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-17 15:50:59,043 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-17 15:50:59,045 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-41.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445062781478_0015
2015-10-17 15:50:59,056 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-17 15:51:00,060 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 15:51:00,062 INFO [Thread-104] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0015
2015-10-17 15:51:00,072 INFO [Thread-104] org.apache.hadoop.ipc.Server: Stopping server on 40658
2015-10-17 15:51:00,076 INFO [IPC Server listener on 40658] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 40658
2015-10-17 15:51:00,077 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-17 15:51:00,077 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
