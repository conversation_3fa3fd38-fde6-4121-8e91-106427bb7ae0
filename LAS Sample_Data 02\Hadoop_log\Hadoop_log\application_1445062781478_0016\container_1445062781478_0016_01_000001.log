2015-10-17 16:47:34,992 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445062781478_0016_000001
2015-10-17 16:47:35,712 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 16:47:35,712 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 16 cluster_timestamp: 1445062781478 } attemptId: 1 } keyId: 471522253)
2015-10-17 16:47:35,998 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 16:47:37,473 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 16:47:37,595 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 16:47:37,659 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 16:47:37,662 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 16:47:37,664 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 16:47:37,667 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 16:47:37,668 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 16:47:37,682 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 16:47:37,684 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 16:47:37,686 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 16:47:37,776 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 16:47:37,827 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 16:47:37,875 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 16:47:37,897 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 16:47:38,004 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 16:47:38,548 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 16:47:38,662 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 16:47:38,662 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 16:47:38,677 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445062781478_0016 to jobTokenSecretManager
2015-10-17 16:47:38,983 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445062781478_0016 because: not enabled; too many maps; too much input;
2015-10-17 16:47:39,022 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445062781478_0016 = 1256521728. Number of splits = 10
2015-10-17 16:47:39,025 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445062781478_0016 = 1
2015-10-17 16:47:39,025 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0016Job Transitioned from NEW to INITED
2015-10-17 16:47:39,028 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445062781478_0016.
2015-10-17 16:47:39,102 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 16:47:39,120 INFO [Socket Reader #1 for port 19051] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 19051
2015-10-17 16:47:39,162 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 16:47:39,162 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 16:47:39,162 INFO [IPC Server listener on 19051] org.apache.hadoop.ipc.Server: IPC Server listener on 19051: starting
2015-10-17 16:47:39,164 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-41.fareast.corp.microsoft.com/**************:19051
2015-10-17 16:47:39,334 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 16:47:39,356 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 16:47:39,375 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 16:47:39,384 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 16:47:39,384 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 16:47:39,390 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 16:47:39,390 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 16:47:39,407 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 19058
2015-10-17 16:47:39,408 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 16:47:39,468 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_19058_mapreduce____z1gj2\webapp
2015-10-17 16:47:39,782 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:19058
2015-10-17 16:47:39,783 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 19058
2015-10-17 16:47:40,434 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 16:47:40,439 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445062781478_0016
2015-10-17 16:47:40,441 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 16:47:40,447 INFO [Socket Reader #1 for port 19061] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 19061
2015-10-17 16:47:40,456 INFO [IPC Server listener on 19061] org.apache.hadoop.ipc.Server: IPC Server listener on 19061: starting
2015-10-17 16:47:40,456 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 16:47:40,489 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 16:47:40,489 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 16:47:40,489 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 16:47:40,576 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at MSRA-SA-41/**************:8030
2015-10-17 16:47:40,691 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 16:47:40,691 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 16:47:40,697 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 16:47:40,701 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 16:47:40,712 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0016Job Transitioned from INITED to SETUP
2015-10-17 16:47:40,715 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 16:47:40,728 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0016Job Transitioned from SETUP to RUNNING
2015-10-17 16:47:40,759 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:40,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:40,766 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,766 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,766 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:40,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:40,768 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,768 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,768 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:40,769 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,769 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,769 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:40,770 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,770 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,770 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:40,771 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,771 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,771 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:40,772 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,772 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,772 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:40,773 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,773 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:40,773 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:40,774 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:40,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:40,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:40,777 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:40,778 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:40,778 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:40,778 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:40,778 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:40,779 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:40,779 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:40,779 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:40,779 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:40,782 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 16:47:40,795 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 16:47:40,860 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445062781478_0016, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0016/job_1445062781478_0016_1.jhist
2015-10-17 16:47:41,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 16:47:41,761 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0016: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:32768, vCores:-3> knownNMs=5
2015-10-17 16:47:41,764 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:32768, vCores:-3>
2015-10-17 16:47:41,764 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:42,810 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 10
2015-10-17 16:47:42,813 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0016_01_000002 to attempt_1445062781478_0016_m_000000_0
2015-10-17 16:47:42,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0016_01_000003 to attempt_1445062781478_0016_m_000001_0
2015-10-17 16:47:42,816 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0016_01_000004 to attempt_1445062781478_0016_m_000002_0
2015-10-17 16:47:42,817 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0016_01_000005 to attempt_1445062781478_0016_m_000003_0
2015-10-17 16:47:42,817 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0016_01_000006 to attempt_1445062781478_0016_m_000004_0
2015-10-17 16:47:42,817 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0016_01_000007 to attempt_1445062781478_0016_m_000005_0
2015-10-17 16:47:42,818 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0016_01_000008 to attempt_1445062781478_0016_m_000006_0
2015-10-17 16:47:42,818 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0016_01_000009 to attempt_1445062781478_0016_m_000007_0
2015-10-17 16:47:42,819 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0016_01_000010 to attempt_1445062781478_0016_m_000008_0
2015-10-17 16:47:42,819 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0016_01_000011 to attempt_1445062781478_0016_m_000009_0
2015-10-17 16:47:42,819 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:22528, vCores:-13>
2015-10-17 16:47:42,820 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:42,820 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 16:47:42,913 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:42,944 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0016/job.jar
2015-10-17 16:47:42,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0016/job.xml
2015-10-17 16:47:42,951 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 16:47:42,951 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 16:47:42,951 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 16:47:43,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:43,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:43,056 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:43,057 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:43,058 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:43,059 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:43,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:43,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:43,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:43,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:43,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:43,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:43,065 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:43,066 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:43,067 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:43,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:43,069 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:43,070 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:43,071 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:43,074 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0016_01_000002 taskAttempt attempt_1445062781478_0016_m_000000_0
2015-10-17 16:47:43,074 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0016_01_000003 taskAttempt attempt_1445062781478_0016_m_000001_0
2015-10-17 16:47:43,075 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0016_01_000004 taskAttempt attempt_1445062781478_0016_m_000002_0
2015-10-17 16:47:43,075 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0016_01_000005 taskAttempt attempt_1445062781478_0016_m_000003_0
2015-10-17 16:47:43,075 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0016_01_000006 taskAttempt attempt_1445062781478_0016_m_000004_0
2015-10-17 16:47:43,076 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0016_01_000007 taskAttempt attempt_1445062781478_0016_m_000005_0
2015-10-17 16:47:43,076 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0016_01_000008 taskAttempt attempt_1445062781478_0016_m_000006_0
2015-10-17 16:47:43,076 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0016_01_000009 taskAttempt attempt_1445062781478_0016_m_000007_0
2015-10-17 16:47:43,077 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0016_01_000010 taskAttempt attempt_1445062781478_0016_m_000008_0
2015-10-17 16:47:43,077 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0016_01_000011 taskAttempt attempt_1445062781478_0016_m_000009_0
2015-10-17 16:47:43,080 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0016_m_000002_0
2015-10-17 16:47:43,080 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0016_m_000004_0
2015-10-17 16:47:43,080 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0016_m_000007_0
2015-10-17 16:47:43,080 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0016_m_000001_0
2015-10-17 16:47:43,080 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0016_m_000006_0
2015-10-17 16:47:43,080 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0016_m_000000_0
2015-10-17 16:47:43,080 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0016_m_000008_0
2015-10-17 16:47:43,080 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0016_m_000005_0
2015-10-17 16:47:43,080 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0016_m_000003_0
2015-10-17 16:47:43,080 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0016_m_000009_0
2015-10-17 16:47:43,082 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:47:43,123 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:47:43,125 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:47:43,127 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:47:43,129 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:47:43,131 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:47:43,132 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:47:43,134 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:47:43,136 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:47:43,138 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:47:43,239 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0016_m_000005_0 : 13562
2015-10-17 16:47:43,239 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0016_m_000007_0 : 13562
2015-10-17 16:47:43,239 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0016_m_000004_0 : 13562
2015-10-17 16:47:43,239 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0016_m_000006_0 : 13562
2015-10-17 16:47:43,239 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0016_m_000008_0 : 13562
2015-10-17 16:47:43,239 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0016_m_000000_0 : 13562
2015-10-17 16:47:43,239 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0016_m_000003_0 : 13562
2015-10-17 16:47:43,239 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0016_m_000009_0 : 13562
2015-10-17 16:47:43,239 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0016_m_000001_0 : 13562
2015-10-17 16:47:43,239 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0016_m_000002_0 : 13562
2015-10-17 16:47:43,242 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0016_m_000003_0] using containerId: [container_1445062781478_0016_01_000005 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 16:47:43,248 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:43,249 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0016_m_000009_0] using containerId: [container_1445062781478_0016_01_000011 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 16:47:43,249 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:43,250 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0016_m_000002_0] using containerId: [container_1445062781478_0016_01_000004 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 16:47:43,250 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:43,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0016_m_000008_0] using containerId: [container_1445062781478_0016_01_000010 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 16:47:43,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:43,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0016_m_000001_0] using containerId: [container_1445062781478_0016_01_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 16:47:43,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:43,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0016_m_000006_0] using containerId: [container_1445062781478_0016_01_000008 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 16:47:43,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:43,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0016_m_000005_0] using containerId: [container_1445062781478_0016_01_000007 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 16:47:43,254 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:43,254 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0016_m_000004_0] using containerId: [container_1445062781478_0016_01_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 16:47:43,255 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:43,255 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0016_m_000007_0] using containerId: [container_1445062781478_0016_01_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 16:47:43,256 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:43,256 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0016_m_000000_0] using containerId: [container_1445062781478_0016_01_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 16:47:43,257 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:43,257 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0016_m_000003
2015-10-17 16:47:43,258 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:43,258 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0016_m_000009
2015-10-17 16:47:43,258 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:43,259 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0016_m_000002
2015-10-17 16:47:43,259 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:43,259 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0016_m_000008
2015-10-17 16:47:43,260 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:43,260 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0016_m_000001
2015-10-17 16:47:43,260 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:43,260 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0016_m_000006
2015-10-17 16:47:43,261 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:43,261 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0016_m_000005
2015-10-17 16:47:43,261 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:43,262 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0016_m_000004
2015-10-17 16:47:43,262 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:43,262 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0016_m_000007
2015-10-17 16:47:43,263 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:43,263 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0016_m_000000
2015-10-17 16:47:43,263 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:43,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0016: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:20480, vCores:-15> knownNMs=5
2015-10-17 16:47:43,824 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:20480, vCores:-15>
2015-10-17 16:47:43,824 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:45,827 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:18432, vCores:-17>
2015-10-17 16:47:45,827 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:45,895 INFO [Socket Reader #1 for port 19061] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0016 (auth:SIMPLE)
2015-10-17 16:47:45,913 INFO [Socket Reader #1 for port 19061] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0016 (auth:SIMPLE)
2015-10-17 16:47:45,914 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0016_m_000009 asked for a task
2015-10-17 16:47:45,914 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0016_m_000009 given task: attempt_1445062781478_0016_m_000007_0
2015-10-17 16:47:45,914 INFO [Socket Reader #1 for port 19061] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0016 (auth:SIMPLE)
2015-10-17 16:47:45,915 INFO [Socket Reader #1 for port 19061] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0016 (auth:SIMPLE)
2015-10-17 16:47:45,925 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0016_m_000010 asked for a task
2015-10-17 16:47:45,925 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0016_m_000010 given task: attempt_1445062781478_0016_m_000008_0
2015-10-17 16:47:45,927 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0016_m_000008 asked for a task
2015-10-17 16:47:45,927 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0016_m_000008 given task: attempt_1445062781478_0016_m_000006_0
2015-10-17 16:47:45,927 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0016_m_000011 asked for a task
2015-10-17 16:47:45,927 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0016_m_000011 given task: attempt_1445062781478_0016_m_000009_0
2015-10-17 16:47:46,828 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:15360, vCores:-20>
2015-10-17 16:47:46,828 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:47,161 INFO [Socket Reader #1 for port 19061] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0016 (auth:SIMPLE)
2015-10-17 16:47:47,163 INFO [Socket Reader #1 for port 19061] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0016 (auth:SIMPLE)
2015-10-17 16:47:47,182 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0016_m_000006 asked for a task
2015-10-17 16:47:47,182 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0016_m_000006 given task: attempt_1445062781478_0016_m_000004_0
2015-10-17 16:47:47,183 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0016_m_000002 asked for a task
2015-10-17 16:47:47,183 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0016_m_000002 given task: attempt_1445062781478_0016_m_000000_0
2015-10-17 16:47:47,387 INFO [Socket Reader #1 for port 19061] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0016 (auth:SIMPLE)
2015-10-17 16:47:47,389 INFO [Socket Reader #1 for port 19061] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0016 (auth:SIMPLE)
2015-10-17 16:47:47,411 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0016_m_000003 asked for a task
2015-10-17 16:47:47,411 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0016_m_000003 given task: attempt_1445062781478_0016_m_000001_0
2015-10-17 16:47:47,412 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0016_m_000007 asked for a task
2015-10-17 16:47:47,412 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0016_m_000007 given task: attempt_1445062781478_0016_m_000005_0
2015-10-17 16:47:47,421 INFO [Socket Reader #1 for port 19061] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0016 (auth:SIMPLE)
2015-10-17 16:47:47,443 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0016_m_000005 asked for a task
2015-10-17 16:47:47,443 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0016_m_000005 given task: attempt_1445062781478_0016_m_000003_0
2015-10-17 16:47:47,450 INFO [Socket Reader #1 for port 19061] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0016 (auth:SIMPLE)
2015-10-17 16:47:47,473 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0016_m_000004 asked for a task
2015-10-17 16:47:47,473 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0016_m_000004 given task: attempt_1445062781478_0016_m_000002_0
2015-10-17 16:47:47,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:13312, vCores:-22>
2015-10-17 16:47:47,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:48,832 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-25>
2015-10-17 16:47:48,832 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:49,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-28>
2015-10-17 16:47:49,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:50,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-17 16:47:50,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:51,836 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-33>
2015-10-17 16:47:51,836 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:53,002 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.106964506
2015-10-17 16:47:53,003 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.10681946
2015-10-17 16:47:53,175 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.295472
2015-10-17 16:47:53,188 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.106881365
2015-10-17 16:47:54,738 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.09891106
2015-10-17 16:47:54,740 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.097940914
2015-10-17 16:47:54,951 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.1066108
2015-10-17 16:47:54,974 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.10685723
2015-10-17 16:47:55,017 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.10660437
2015-10-17 16:47:55,032 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.106493875
2015-10-17 16:47:56,032 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.106964506
2015-10-17 16:47:56,032 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.10681946
2015-10-17 16:47:56,197 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.295472
2015-10-17 16:47:56,207 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.106881365
2015-10-17 16:47:57,737 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.10635664
2015-10-17 16:47:57,753 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.10680563
2015-10-17 16:47:57,955 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.1066108
2015-10-17 16:47:57,987 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.10685723
2015-10-17 16:47:58,017 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.10660437
2015-10-17 16:47:58,033 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.106493875
2015-10-17 16:47:59,063 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.10681946
2015-10-17 16:47:59,066 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.106964506
2015-10-17 16:47:59,235 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.106881365
2015-10-17 16:47:59,236 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.295472
2015-10-17 16:48:00,741 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.10635664
2015-10-17 16:48:00,763 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.10680563
2015-10-17 16:48:00,956 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.1066108
2015-10-17 16:48:01,016 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.10685723
2015-10-17 16:48:01,022 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.10660437
2015-10-17 16:48:01,037 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.106493875
2015-10-17 16:48:02,106 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.12198941
2015-10-17 16:48:02,108 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.15675715
2015-10-17 16:48:02,282 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.15238282
2015-10-17 16:48:02,283 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.40048748
2015-10-17 16:48:03,755 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.10635664
2015-10-17 16:48:03,771 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.10680563
2015-10-17 16:48:03,958 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.1066108
2015-10-17 16:48:04,026 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.10685723
2015-10-17 16:48:04,036 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.110508144
2015-10-17 16:48:04,056 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.10961348
2015-10-17 16:48:05,128 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.19266446
2015-10-17 16:48:05,129 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.19255035
2015-10-17 16:48:05,304 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.19258286
2015-10-17 16:48:05,305 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.5323719
2015-10-17 16:48:06,775 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.10635664
2015-10-17 16:48:06,789 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.10680563
2015-10-17 16:48:06,973 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.16908878
2015-10-17 16:48:07,038 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.1734444
2015-10-17 16:48:07,050 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.18289515
2015-10-17 16:48:07,069 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.1835172
2015-10-17 16:48:08,150 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.19255035
2015-10-17 16:48:08,150 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.19266446
2015-10-17 16:48:08,327 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.5323719
2015-10-17 16:48:08,327 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.19258286
2015-10-17 16:48:09,785 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.16187021
2015-10-17 16:48:09,800 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.16925344
2015-10-17 16:48:09,995 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.19211523
2015-10-17 16:48:10,052 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.19247705
2015-10-17 16:48:10,058 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.19212553
2015-10-17 16:48:10,082 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.19209063
2015-10-17 16:48:11,180 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.19255035
2015-10-17 16:48:11,183 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.19266446
2015-10-17 16:48:11,354 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.19258286
2015-10-17 16:48:11,368 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.5323719
2015-10-17 16:48:12,802 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.19242907
2015-10-17 16:48:12,806 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.19158794
2015-10-17 16:48:13,011 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.19211523
2015-10-17 16:48:13,068 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.19212553
2015-10-17 16:48:13,074 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.19247705
2015-10-17 16:48:13,081 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.19209063
2015-10-17 16:48:14,223 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.25511676
2015-10-17 16:48:14,225 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.19686303
2015-10-17 16:48:14,226 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.5323719
2015-10-17 16:48:14,397 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.24077183
2015-10-17 16:48:14,409 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.667
2015-10-17 16:48:15,820 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.19242907
2015-10-17 16:48:15,824 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.19158794
2015-10-17 16:48:16,026 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.19211523
2015-10-17 16:48:16,090 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.19247705
2015-10-17 16:48:16,090 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.19209063
2015-10-17 16:48:16,091 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.19212553
2015-10-17 16:48:17,244 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.27825075
2015-10-17 16:48:17,244 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.2783809
2015-10-17 16:48:17,419 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.27811313
2015-10-17 16:48:17,430 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.667
2015-10-17 16:48:18,834 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.19242907
2015-10-17 16:48:18,840 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.19158794
2015-10-17 16:48:19,040 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.23600085
2015-10-17 16:48:19,102 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.24028382
2015-10-17 16:48:19,103 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.2592051
2015-10-17 16:48:19,104 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.24434264
2015-10-17 16:48:20,273 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.2783809
2015-10-17 16:48:20,274 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.27825075
2015-10-17 16:48:20,447 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.27811313
2015-10-17 16:48:20,457 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.667
2015-10-17 16:48:21,849 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.20118782
2015-10-17 16:48:21,853 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.21300635
2015-10-17 16:48:22,053 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.27776006
2015-10-17 16:48:22,116 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.27772525
2015-10-17 16:48:22,117 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.27813601
2015-10-17 16:48:22,118 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.27765483
2015-10-17 16:48:23,295 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.27825075
2015-10-17 16:48:23,312 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.2783809
2015-10-17 16:48:23,477 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.27811313
2015-10-17 16:48:23,485 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.7161634
2015-10-17 16:48:24,865 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.27356932
2015-10-17 16:48:24,867 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.2781602
2015-10-17 16:48:25,067 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.27776006
2015-10-17 16:48:25,133 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.27813601
2015-10-17 16:48:25,141 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.27765483
2015-10-17 16:48:25,141 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.27772525
2015-10-17 16:48:26,338 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.31583473
2015-10-17 16:48:26,352 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.2783809
2015-10-17 16:48:26,519 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.33389172
2015-10-17 16:48:26,526 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.77691853
2015-10-17 16:48:27,881 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.27696857
2015-10-17 16:48:27,885 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.2781602
2015-10-17 16:48:28,086 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.27776006
2015-10-17 16:48:28,151 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.27772525
2015-10-17 16:48:28,152 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.27765483
2015-10-17 16:48:28,152 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.27813601
2015-10-17 16:48:29,359 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.3638923
2015-10-17 16:48:29,373 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.3577748
2015-10-17 16:48:29,539 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.3637686
2015-10-17 16:48:29,544 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.8562689
2015-10-17 16:48:30,902 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.27696857
2015-10-17 16:48:30,903 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.2781602
2015-10-17 16:48:31,101 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.33292565
2015-10-17 16:48:31,164 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.33118758
2015-10-17 16:48:31,166 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.34748396
2015-10-17 16:48:31,168 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.35412648
2015-10-17 16:48:32,378 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.3638923
2015-10-17 16:48:32,391 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.36404583
2015-10-17 16:48:32,558 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.3637686
2015-10-17 16:48:32,561 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 0.9526969
2015-10-17 16:48:33,912 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.2781602
2015-10-17 16:48:33,913 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.27696857
2015-10-17 16:48:34,092 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000009_0 is : 1.0
2015-10-17 16:48:34,093 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0016_m_000009_0
2015-10-17 16:48:34,095 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:48:34,095 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0016_01_000011 taskAttempt attempt_1445062781478_0016_m_000009_0
2015-10-17 16:48:34,095 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0016_m_000009_0
2015-10-17 16:48:34,096 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:48:34,110 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:48:34,113 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.36319977
2015-10-17 16:48:34,116 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0016_m_000009_0
2015-10-17 16:48:34,117 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:48:34,119 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 16:48:34,178 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.36317363
2015-10-17 16:48:34,178 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.36323506
2015-10-17 16:48:34,179 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.36390656
2015-10-17 16:48:34,544 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0016_m_000000
2015-10-17 16:48:34,544 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 16:48:34,544 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0016_m_000000
2015-10-17 16:48:34,545 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:48:34,545 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:48:34,545 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:48:34,889 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 16:48:34,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0016: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-33> knownNMs=5
2015-10-17 16:48:34,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-33>
2015-10-17 16:48:34,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 16:48:34,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:12288, vCores:-23> finalMapResourceLimit:<memory:11060, vCores:-21> finalReduceResourceLimit:<memory:1228, vCores:-2> netScheduledMapResource:<memory:11264, vCores:11> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-17 16:48:34,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-17 16:48:34,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 16:48:35,423 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.3638923
2015-10-17 16:48:35,438 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.36404583
2015-10-17 16:48:35,605 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.3637686
2015-10-17 16:48:35,907 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0016: ask=1 release= 0 newContainers=1 finishedContainers=1 resourcelimit=<memory:2048, vCores:-33> knownNMs=5
2015-10-17 16:48:35,907 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0016_01_000011
2015-10-17 16:48:35,909 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 16:48:35,909 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0016_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:48:35,909 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0016_01_000012 to attempt_1445062781478_0016_m_000000_1
2015-10-17 16:48:35,910 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 16:48:35,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:48:35,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:48:35,912 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0016_01_000012 taskAttempt attempt_1445062781478_0016_m_000000_1
2015-10-17 16:48:35,912 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0016_m_000000_1
2015-10-17 16:48:35,913 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:48:35,936 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0016_m_000000_1 : 13562
2015-10-17 16:48:35,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0016_m_000000_1] using containerId: [container_1445062781478_0016_01_000012 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 16:48:35,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:48:35,938 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0016_m_000000
2015-10-17 16:48:36,919 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0016: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 16:48:36,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 16:48:36,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 16:48:36,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0016_01_000013 to attempt_1445062781478_0016_r_000000_0
2015-10-17 16:48:36,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 16:48:36,927 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.31546527
2015-10-17 16:48:36,928 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.3510154
2015-10-17 16:48:36,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:48:36,969 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:48:36,970 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0016_01_000013 taskAttempt attempt_1445062781478_0016_r_000000_0
2015-10-17 16:48:36,970 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0016_r_000000_0
2015-10-17 16:48:36,970 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 16:48:37,128 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.36319977
2015-10-17 16:48:37,191 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.36317363
2015-10-17 16:48:37,192 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.36390656
2015-10-17 16:48:37,193 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.36323506
2015-10-17 16:48:37,256 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0016_r_000000_0 : 13562
2015-10-17 16:48:37,257 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0016_r_000000_0] using containerId: [container_1445062781478_0016_01_000013 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:51951]
2015-10-17 16:48:37,257 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:48:37,258 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0016_r_000000
2015-10-17 16:48:37,258 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:48:37,924 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0016: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 16:48:38,467 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.3638923
2015-10-17 16:48:38,483 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.36404583
2015-10-17 16:48:38,646 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.39904705
2015-10-17 16:48:39,816 INFO [Socket Reader #1 for port 19061] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0016 (auth:SIMPLE)
2015-10-17 16:48:39,844 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0016_m_000012 asked for a task
2015-10-17 16:48:39,844 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0016_m_000012 given task: attempt_1445062781478_0016_m_000000_1
2015-10-17 16:48:39,943 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.3624012
2015-10-17 16:48:39,948 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.36388028
2015-10-17 16:48:40,132 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.36319977
2015-10-17 16:48:40,208 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.36323506
2015-10-17 16:48:40,213 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.36317363
2015-10-17 16:48:40,214 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.36390656
2015-10-17 16:48:41,495 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.44964966
2015-10-17 16:48:41,510 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.4120921
2015-10-17 16:48:41,676 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.4492364
2015-10-17 16:48:42,962 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.3624012
2015-10-17 16:48:42,967 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.36388028
2015-10-17 16:48:43,154 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.36319977
2015-10-17 16:48:43,234 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.3915007
2015-10-17 16:48:43,235 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.38482797
2015-10-17 16:48:43,236 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.36390656
2015-10-17 16:48:44,526 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.44964966
2015-10-17 16:48:44,527 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.44980705
2015-10-17 16:48:44,696 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.44950172
2015-10-17 16:48:45,223 INFO [Socket Reader #1 for port 19061] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0016 (auth:SIMPLE)
2015-10-17 16:48:45,250 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0016_r_000013 asked for a task
2015-10-17 16:48:45,250 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0016_r_000013 given task: attempt_1445062781478_0016_r_000000_0
2015-10-17 16:48:45,976 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.3624012
2015-10-17 16:48:45,979 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.36388028
2015-10-17 16:48:46,163 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.4333771
2015-10-17 16:48:46,242 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.44859612
2015-10-17 16:48:46,248 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.43558502
2015-10-17 16:48:46,249 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.4486067
2015-10-17 16:48:47,547 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.44964966
2015-10-17 16:48:47,547 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.44980705
2015-10-17 16:48:47,716 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.44950172
2015-10-17 16:48:47,927 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.10635664
2015-10-17 16:48:48,235 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 16:48:48,990 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.36864096
2015-10-17 16:48:48,997 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.4041975
2015-10-17 16:48:49,180 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.448704
2015-10-17 16:48:49,244 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:48:49,257 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.4486067
2015-10-17 16:48:49,257 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.44950968
2015-10-17 16:48:49,261 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.44859612
2015-10-17 16:48:49,545 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0016_m_000004
2015-10-17 16:48:49,545 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 16:48:49,546 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0016_m_000004
2015-10-17 16:48:49,546 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:48:49,546 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:48:49,546 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000004_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:48:49,941 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 16:48:49,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0016: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 16:48:50,261 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:48:50,569 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.44980705
2015-10-17 16:48:50,569 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.46956736
2015-10-17 16:48:50,734 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.44950172
2015-10-17 16:48:50,945 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 16:48:50,945 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:48:50,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0016_01_000014 to attempt_1445062781478_0016_m_000004_1
2015-10-17 16:48:50,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-17 16:48:50,946 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:48:50,947 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000004_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:48:50,947 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.10635664
2015-10-17 16:48:50,947 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0016_01_000014 taskAttempt attempt_1445062781478_0016_m_000004_1
2015-10-17 16:48:50,947 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0016_m_000004_1
2015-10-17 16:48:50,947 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 16:48:51,270 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0016_m_000004_1 : 13562
2015-10-17 16:48:51,270 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0016_m_000004_1] using containerId: [container_1445062781478_0016_01_000014 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:51951]
2015-10-17 16:48:51,271 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000004_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:48:51,271 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0016_m_000004
2015-10-17 16:48:51,296 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:48:51,948 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0016: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 16:48:52,007 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.44968578
2015-10-17 16:48:52,007 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.4476187
2015-10-17 16:48:52,195 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.448704
2015-10-17 16:48:52,273 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.44859612
2015-10-17 16:48:52,274 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.4486067
2015-10-17 16:48:52,275 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.44950968
2015-10-17 16:48:52,338 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:48:53,356 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:48:53,589 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.5183089
2015-10-17 16:48:53,589 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.5352825
2015-10-17 16:48:53,752 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.53521925
2015-10-17 16:48:53,966 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.10635664
2015-10-17 16:48:54,282 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.0
2015-10-17 16:48:54,426 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:48:55,020 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.44789755
2015-10-17 16:48:55,027 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.44968578
2015-10-17 16:48:55,215 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.448704
2015-10-17 16:48:55,299 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.4884843
2015-10-17 16:48:55,299 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.47776788
2015-10-17 16:48:55,301 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.44950968
2015-10-17 16:48:55,454 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:48:56,355 INFO [Socket Reader #1 for port 19061] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0016 (auth:SIMPLE)
2015-10-17 16:48:56,494 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0016_m_000014 asked for a task
2015-10-17 16:48:56,495 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:48:56,495 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0016_m_000014 given task: attempt_1445062781478_0016_m_000004_1
2015-10-17 16:48:56,608 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.53543663
2015-10-17 16:48:56,609 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.5352825
2015-10-17 16:48:56,771 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.53521925
2015-10-17 16:48:56,986 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.19158794
2015-10-17 16:48:57,582 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:48:57,904 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.0
2015-10-17 16:48:58,026 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.44789755
2015-10-17 16:48:58,041 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.44968578
2015-10-17 16:48:58,240 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.5263245
2015-10-17 16:48:58,303 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.5342037
2015-10-17 16:48:58,309 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.5343203
2015-10-17 16:48:58,310 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.5092059
2015-10-17 16:48:58,589 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:48:59,625 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:48:59,628 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.5352825
2015-10-17 16:48:59,644 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.53543663
2015-10-17 16:48:59,796 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.53521925
2015-10-17 16:49:00,012 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.19158794
2015-10-17 16:49:00,635 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:00,964 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.0
2015-10-17 16:49:01,035 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.44789755
2015-10-17 16:49:01,056 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.48079014
2015-10-17 16:49:01,240 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.53425497
2015-10-17 16:49:01,305 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.5342037
2015-10-17 16:49:01,321 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.5343203
2015-10-17 16:49:01,322 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.5352021
2015-10-17 16:49:01,653 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:02,647 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.54851955
2015-10-17 16:49:02,661 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.53543663
2015-10-17 16:49:02,682 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:02,815 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.60033816
2015-10-17 16:49:03,030 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.19158794
2015-10-17 16:49:03,746 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:04,035 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.53341997
2015-10-17 16:49:04,067 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.5352028
2015-10-17 16:49:04,213 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.0
2015-10-17 16:49:04,240 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.53425497
2015-10-17 16:49:04,323 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.5342037
2015-10-17 16:49:04,334 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.5352021
2015-10-17 16:49:04,338 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.5343203
2015-10-17 16:49:04,546 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0016_m_000002
2015-10-17 16:49:04,546 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 16:49:04,546 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0016_m_000002
2015-10-17 16:49:04,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:49:04,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:49:04,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000002_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:49:04,791 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:04,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-17 16:49:04,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0016: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 16:49:05,678 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.620844
2015-10-17 16:49:05,691 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.6210422
2015-10-17 16:49:05,848 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.6207798
2015-10-17 16:49:05,858 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:06,063 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.23425564
2015-10-17 16:49:07,035 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.53341997
2015-10-17 16:49:07,072 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.5352028
2015-10-17 16:49:07,243 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.53425497
2015-10-17 16:49:07,254 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:07,337 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.5384651
2015-10-17 16:49:07,337 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.5352021
2015-10-17 16:49:07,351 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.55612713
2015-10-17 16:49:07,471 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.0
2015-10-17 16:49:08,265 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:08,697 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.620844
2015-10-17 16:49:08,709 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.6210422
2015-10-17 16:49:08,868 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.6207798
2015-10-17 16:49:09,083 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.27696857
2015-10-17 16:49:09,278 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:10,039 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.53341997
2015-10-17 16:49:10,090 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.5352028
2015-10-17 16:49:10,258 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.60338515
2015-10-17 16:49:10,284 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:10,351 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.567434
2015-10-17 16:49:10,355 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.61711156
2015-10-17 16:49:10,368 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.6199081
2015-10-17 16:49:10,654 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.0
2015-10-17 16:49:11,284 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:11,717 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.620844
2015-10-17 16:49:11,727 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.6210422
2015-10-17 16:49:11,886 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.6207798
2015-10-17 16:49:12,102 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.27696857
2015-10-17 16:49:12,304 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:13,054 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.53341997
2015-10-17 16:49:13,099 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.55855775
2015-10-17 16:49:13,138 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.6207798
2015-10-17 16:49:13,152 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.620844
2015-10-17 16:49:13,273 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.6197233
2015-10-17 16:49:13,342 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:13,365 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.6209487
2015-10-17 16:49:13,371 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.6196791
2015-10-17 16:49:13,379 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.6199081
2015-10-17 16:49:13,568 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.6210422
2015-10-17 16:49:14,407 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:14,709 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.0
2015-10-17 16:49:14,742 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.667
2015-10-17 16:49:14,751 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.667
2015-10-17 16:49:14,911 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.667
2015-10-17 16:49:15,120 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.27696857
2015-10-17 16:49:15,483 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:16,068 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.5968952
2015-10-17 16:49:16,102 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.6208445
2015-10-17 16:49:16,270 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.6197233
2015-10-17 16:49:16,366 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.6209487
2015-10-17 16:49:16,381 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.6196791
2015-10-17 16:49:16,385 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.6199081
2015-10-17 16:49:16,554 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:17,574 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:17,761 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.667
2015-10-17 16:49:17,761 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.0
2015-10-17 16:49:17,769 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.667
2015-10-17 16:49:17,930 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.667
2015-10-17 16:49:18,139 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.3624012
2015-10-17 16:49:18,888 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_1 is : 0.031915102
2015-10-17 16:49:19,067 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.61898744
2015-10-17 16:49:19,118 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.6208445
2015-10-17 16:49:19,271 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.6197233
2015-10-17 16:49:19,341 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.6199081
2015-10-17 16:49:19,369 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.6209487
2015-10-17 16:49:19,379 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.6478494
2015-10-17 16:49:19,401 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.667
2015-10-17 16:49:19,798 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:20,184 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.6478494
2015-10-17 16:49:20,780 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.6697126
2015-10-17 16:49:20,786 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.6671628
2015-10-17 16:49:20,948 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.6816341
2015-10-17 16:49:21,104 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.6197233
2015-10-17 16:49:21,165 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.3624012
2015-10-17 16:49:21,267 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:22,018 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.0
2015-10-17 16:49:22,067 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.61898744
2015-10-17 16:49:22,076 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_1 is : 0.045232575
2015-10-17 16:49:22,133 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.6208445
2015-10-17 16:49:22,270 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.667
2015-10-17 16:49:22,281 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:22,381 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.6521816
2015-10-17 16:49:22,383 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.667
2015-10-17 16:49:22,413 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.667
2015-10-17 16:49:22,742 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.6521816
2015-10-17 16:49:23,292 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:23,798 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.7006421
2015-10-17 16:49:23,803 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.6964144
2015-10-17 16:49:23,967 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.7238027
2015-10-17 16:49:24,182 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.3624012
2015-10-17 16:49:24,325 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:25,071 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.0
2015-10-17 16:49:25,075 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.61898744
2015-10-17 16:49:25,141 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_1 is : 0.060576398
2015-10-17 16:49:25,149 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.6317873
2015-10-17 16:49:25,277 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.667
2015-10-17 16:49:25,341 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:25,381 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.667
2015-10-17 16:49:25,401 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.667
2015-10-17 16:49:25,412 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.667
2015-10-17 16:49:26,438 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:26,502 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.6317873
2015-10-17 16:49:26,823 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.7400202
2015-10-17 16:49:26,824 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.7349242
2015-10-17 16:49:26,992 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.7549046
2015-10-17 16:49:27,206 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.43478608
2015-10-17 16:49:27,568 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:28,090 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.66374034
2015-10-17 16:49:28,170 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.667
2015-10-17 16:49:28,237 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.0
2015-10-17 16:49:28,247 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.66374034
2015-10-17 16:49:28,265 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_1 is : 0.06995645
2015-10-17 16:49:28,290 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.667
2015-10-17 16:49:28,388 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.667
2015-10-17 16:49:28,419 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.66961026
2015-10-17 16:49:28,419 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.6829933
2015-10-17 16:49:28,637 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:29,714 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:29,843 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.774858
2015-10-17 16:49:29,848 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.7697088
2015-10-17 16:49:30,012 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.7959576
2015-10-17 16:49:30,238 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.44789755
2015-10-17 16:49:30,809 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:31,107 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.667
2015-10-17 16:49:31,183 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.667
2015-10-17 16:49:31,311 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.6906332
2015-10-17 16:49:31,403 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_1 is : 0.08109478
2015-10-17 16:49:31,403 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.667
2015-10-17 16:49:31,410 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.0
2015-10-17 16:49:31,436 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.70332134
2015-10-17 16:49:31,437 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.7128906
2015-10-17 16:49:31,901 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:32,864 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.8159053
2015-10-17 16:49:32,867 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.81154543
2015-10-17 16:49:33,034 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.824539
2015-10-17 16:49:33,261 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.44789755
2015-10-17 16:49:33,309 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:34,117 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.667
2015-10-17 16:49:34,199 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.667
2015-10-17 16:49:34,321 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.7221241
2015-10-17 16:49:34,376 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:34,415 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.68868554
2015-10-17 16:49:34,446 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.74410576
2015-10-17 16:49:34,448 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.73604655
2015-10-17 16:49:35,338 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.0
2015-10-17 16:49:35,400 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_1 is : 0.08630328
2015-10-17 16:49:35,451 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:35,883 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.8502096
2015-10-17 16:49:35,883 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.8455861
2015-10-17 16:49:36,052 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.86751026
2015-10-17 16:49:36,279 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.44789755
2015-10-17 16:49:36,471 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:37,134 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.667
2015-10-17 16:49:37,214 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.6874147
2015-10-17 16:49:37,342 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.756753
2015-10-17 16:49:37,436 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.72271883
2015-10-17 16:49:37,471 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.77138335
2015-10-17 16:49:37,472 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.77736306
2015-10-17 16:49:37,514 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:38,383 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.0
2015-10-17 16:49:38,477 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_1 is : 0.09281945
2015-10-17 16:49:38,541 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:38,915 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.8830722
2015-10-17 16:49:38,916 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.88751197
2015-10-17 16:49:39,087 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.8917594
2015-10-17 16:49:39,323 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.50410664
2015-10-17 16:49:40,155 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.68599457
2015-10-17 16:49:40,230 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.72004294
2015-10-17 16:49:40,355 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.7863779
2015-10-17 16:49:40,355 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:40,450 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.75324297
2015-10-17 16:49:40,479 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.80224854
2015-10-17 16:49:40,480 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.80539095
2015-10-17 16:49:41,371 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:41,954 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.9074503
2015-10-17 16:49:41,955 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.903208
2015-10-17 16:49:42,111 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.9103471
2015-10-17 16:49:42,344 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.53341997
2015-10-17 16:49:42,402 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:49:42,404 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:42,482 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_1 is : 0.095098466
2015-10-17 16:49:43,162 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.7226829
2015-10-17 16:49:43,244 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.76081693
2015-10-17 16:49:43,369 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.8180306
2015-10-17 16:49:43,463 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.785593
2015-10-17 16:49:43,491 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:43,500 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.83539176
2015-10-17 16:49:43,501 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.83516544
2015-10-17 16:49:44,591 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:44,985 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.9378064
2015-10-17 16:49:44,986 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.9365301
2015-10-17 16:49:45,137 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.93341553
2015-10-17 16:49:45,372 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.53341997
2015-10-17 16:49:45,521 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:49:45,590 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_1 is : 0.102590136
2015-10-17 16:49:45,673 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:46,169 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.7548221
2015-10-17 16:49:46,260 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.7952774
2015-10-17 16:49:46,386 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.847559
2015-10-17 16:49:46,479 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.8163827
2015-10-17 16:49:46,511 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.86373514
2015-10-17 16:49:46,512 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.86470354
2015-10-17 16:49:46,733 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:47,762 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:48,026 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 0.96936107
2015-10-17 16:49:48,026 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.96828294
2015-10-17 16:49:48,171 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 0.9718405
2015-10-17 16:49:48,409 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.53341997
2015-10-17 16:49:48,784 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:49:48,870 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:48,907 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_1 is : 0.10680563
2015-10-17 16:49:49,184 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.79284453
2015-10-17 16:49:49,271 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.83577657
2015-10-17 16:49:49,404 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.87896824
2015-10-17 16:49:49,498 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.8497071
2015-10-17 16:49:49,523 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.8940938
2015-10-17 16:49:49,529 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.89752537
2015-10-17 16:49:49,966 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:51,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 16:49:51,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0016_01_000015 to attempt_1445062781478_0016_m_000002_1
2015-10-17 16:49:51,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:49:51,032 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:49:51,032 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:51,033 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000002_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:49:51,034 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0016_01_000015 taskAttempt attempt_1445062781478_0016_m_000002_1
2015-10-17 16:49:51,034 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0016_m_000002_1
2015-10-17 16:49:51,034 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:49:51,051 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0016_m_000002_1 : 13562
2015-10-17 16:49:51,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0016_m_000002_1] using containerId: [container_1445062781478_0016_01_000015 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 16:49:51,052 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 0.99982154
2015-10-17 16:49:51,052 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000002_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:49:51,052 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0016_m_000002
2015-10-17 16:49:51,054 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 1.0
2015-10-17 16:49:51,109 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000007_0 is : 1.0
2015-10-17 16:49:51,110 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0016_m_000007_0
2015-10-17 16:49:51,110 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:49:51,110 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0016_01_000009 taskAttempt attempt_1445062781478_0016_m_000007_0
2015-10-17 16:49:51,110 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0016_m_000007_0
2015-10-17 16:49:51,111 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:49:51,119 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:49:51,120 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0016_m_000007_0
2015-10-17 16:49:51,120 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:49:51,120 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 16:49:51,146 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000006_0 is : 1.0
2015-10-17 16:49:51,147 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0016_m_000006_0
2015-10-17 16:49:51,148 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:49:51,148 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0016_01_000008 taskAttempt attempt_1445062781478_0016_m_000006_0
2015-10-17 16:49:51,148 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0016_m_000006_0
2015-10-17 16:49:51,148 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:49:51,157 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:49:51,158 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0016_m_000006_0
2015-10-17 16:49:51,158 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:49:51,158 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 16:49:51,187 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 1.0
2015-10-17 16:49:51,248 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000008_0 is : 1.0
2015-10-17 16:49:51,249 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0016_m_000008_0
2015-10-17 16:49:51,249 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:49:51,250 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0016_01_000010 taskAttempt attempt_1445062781478_0016_m_000008_0
2015-10-17 16:49:51,250 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0016_m_000008_0
2015-10-17 16:49:51,250 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:49:51,258 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:49:51,258 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0016_m_000008_0
2015-10-17 16:49:51,258 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:49:51,259 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 16:49:51,427 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.53341997
2015-10-17 16:49:52,025 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:49:52,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:49:52,033 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0016: ask=4 release= 0 newContainers=0 finishedContainers=2 resourcelimit=<memory:2048, vCores:-33> knownNMs=5
2015-10-17 16:49:52,033 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0016_01_000008
2015-10-17 16:49:52,033 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0016_01_000009
2015-10-17 16:49:52,033 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0016_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:49:52,033 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:49:52,033 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0016_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:49:52,053 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_1 is : 0.12799253
2015-10-17 16:49:52,074 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 16:49:52,195 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.82967895
2015-10-17 16:49:52,281 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.8740344
2015-10-17 16:49:52,419 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.9073992
2015-10-17 16:49:52,514 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.8795314
2015-10-17 16:49:52,529 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.92291236
2015-10-17 16:49:52,545 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.9271114
2015-10-17 16:49:52,870 INFO [Socket Reader #1 for port 19061] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0016 (auth:SIMPLE)
2015-10-17 16:49:52,882 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0016_m_000015 asked for a task
2015-10-17 16:49:52,882 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0016_m_000015 given task: attempt_1445062781478_0016_m_000002_1
2015-10-17 16:49:53,035 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0016_01_000010
2015-10-17 16:49:53,035 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:49:53,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0016_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:49:53,469 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 16:49:54,455 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.5994448
2015-10-17 16:49:54,490 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 16:49:55,215 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.8709402
2015-10-17 16:49:55,287 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.9175763
2015-10-17 16:49:55,428 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.93996274
2015-10-17 16:49:55,527 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.91432065
2015-10-17 16:49:55,536 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.9548178
2015-10-17 16:49:55,560 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.96085393
2015-10-17 16:49:55,576 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_1 is : 0.1406947
2015-10-17 16:49:55,576 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 16:49:55,858 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:49:56,657 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 16:49:57,485 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.61898744
2015-10-17 16:49:57,731 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 16:49:58,230 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.9123633
2015-10-17 16:49:58,295 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.9605454
2015-10-17 16:49:58,433 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 0.97091734
2015-10-17 16:49:58,543 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.9474163
2015-10-17 16:49:58,544 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 0.9851681
2015-10-17 16:49:58,572 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 0.993646
2015-10-17 16:49:58,787 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 16:49:58,880 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_1 is : 0.15339568
2015-10-17 16:49:59,066 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:49:59,342 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000002_0 is : 1.0
2015-10-17 16:49:59,344 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0016_m_000002_0
2015-10-17 16:49:59,345 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:49:59,345 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0016_01_000004 taskAttempt attempt_1445062781478_0016_m_000002_0
2015-10-17 16:49:59,346 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0016_m_000002_0
2015-10-17 16:49:59,347 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:49:59,364 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:49:59,364 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0016_m_000002_0
2015-10-17 16:49:59,365 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0016_m_000002_1
2015-10-17 16:49:59,365 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:49:59,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 16:49:59,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000002_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 16:49:59,367 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0016_01_000015 taskAttempt attempt_1445062781478_0016_m_000002_1
2015-10-17 16:49:59,367 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0016_m_000002_1
2015-10-17 16:49:59,367 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:49:59,386 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000002_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 16:49:59,387 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 16:49:59,404 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out1/_temporary/1/_temporary/attempt_1445062781478_0016_m_000002_1
2015-10-17 16:49:59,407 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000002_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 16:49:59,442 INFO [Socket Reader #1 for port 19061] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 19061: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 16:49:59,802 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 16:50:00,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:50:00,049 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000003_0 is : 1.0
2015-10-17 16:50:00,052 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0016_m_000003_0
2015-10-17 16:50:00,052 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:50:00,052 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0016_01_000005 taskAttempt attempt_1445062781478_0016_m_000003_0
2015-10-17 16:50:00,053 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0016_m_000003_0
2015-10-17 16:50:00,053 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:50:00,071 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:50:00,072 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0016_m_000003_0
2015-10-17 16:50:00,072 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:50:00,072 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 16:50:00,519 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.61898744
2015-10-17 16:50:00,841 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 16:50:01,047 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:50:01,049 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0016_01_000015
2015-10-17 16:50:01,049 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0016_01_000004
2015-10-17 16:50:01,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0016_m_000002_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:50:01,049 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:50:01,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0016_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:50:01,208 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000001_0 is : 1.0
2015-10-17 16:50:01,210 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0016_m_000001_0
2015-10-17 16:50:01,211 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:50:01,211 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0016_01_000003 taskAttempt attempt_1445062781478_0016_m_000001_0
2015-10-17 16:50:01,211 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0016_m_000001_0
2015-10-17 16:50:01,211 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:50:01,225 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:50:01,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0016_m_000001_0
2015-10-17 16:50:01,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:50:01,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 16:50:01,242 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.9484248
2015-10-17 16:50:01,308 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 0.9980085
2015-10-17 16:50:01,557 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 0.98183286
2015-10-17 16:50:01,642 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_0 is : 1.0
2015-10-17 16:50:01,644 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0016_m_000004_0
2015-10-17 16:50:01,645 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:50:01,645 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0016_01_000006 taskAttempt attempt_1445062781478_0016_m_000004_0
2015-10-17 16:50:01,645 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0016_m_000004_0
2015-10-17 16:50:01,645 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:50:01,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:50:01,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0016_m_000004_0
2015-10-17 16:50:01,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0016_m_000004_1
2015-10-17 16:50:01,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:50:01,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 16:50:01,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000004_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 16:50:01,661 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0016_01_000014 taskAttempt attempt_1445062781478_0016_m_000004_1
2015-10-17 16:50:01,661 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0016_m_000004_1
2015-10-17 16:50:01,662 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 16:50:01,908 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:50:02,050 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:50:02,053 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0016_01_000005
2015-10-17 16:50:02,053 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0016_01_000003
2015-10-17 16:50:02,053 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0016_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:50:02,053 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:50:02,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0016_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:50:02,139 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000004_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 16:50:02,140 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 16:50:02,144 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out1/_temporary/1/_temporary/attempt_1445062781478_0016_m_000004_1
2015-10-17 16:50:02,146 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000004_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 16:50:02,240 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000004_1 is : 0.17065723
2015-10-17 16:50:02,401 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:02,759 INFO [Socket Reader #1 for port 19061] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 19061: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 16:50:02,971 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:50:03,056 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0016_01_000006
2015-10-17 16:50:03,056 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:50:03,056 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0016_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:50:03,072 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000005_0 is : 1.0
2015-10-17 16:50:03,074 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0016_m_000005_0
2015-10-17 16:50:03,075 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:50:03,075 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0016_01_000007 taskAttempt attempt_1445062781478_0016_m_000005_0
2015-10-17 16:50:03,076 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0016_m_000005_0
2015-10-17 16:50:03,076 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:50:03,090 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:50:03,091 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0016_m_000005_0
2015-10-17 16:50:03,091 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:50:03,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 16:50:03,546 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_1 is : 0.61898744
2015-10-17 16:50:03,987 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:50:04,056 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:50:04,058 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0016_01_000014
2015-10-17 16:50:04,058 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:50:04,059 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0016_m_000004_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:50:04,250 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 0.9794696
2015-10-17 16:50:05,061 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0016_01_000007
2015-10-17 16:50:05,061 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:50:05,061 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0016_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:50:05,435 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 16:50:05,763 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:05,954 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_m_000000_0 is : 1.0
2015-10-17 16:50:05,956 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0016_m_000000_0
2015-10-17 16:50:05,957 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:50:05,957 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0016_01_000002 taskAttempt attempt_1445062781478_0016_m_000000_0
2015-10-17 16:50:05,958 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0016_m_000000_0
2015-10-17 16:50:05,958 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:50:05,976 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:50:05,976 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0016_m_000000_0
2015-10-17 16:50:05,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0016_m_000000_1
2015-10-17 16:50:05,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0016_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:50:05,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 16:50:05,978 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000000_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 16:50:05,978 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0016_01_000012 taskAttempt attempt_1445062781478_0016_m_000000_1
2015-10-17 16:50:05,979 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0016_m_000000_1
2015-10-17 16:50:05,979 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:50:05,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000000_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 16:50:05,994 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 16:50:05,996 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out1/_temporary/1/_temporary/attempt_1445062781478_0016_m_000000_1
2015-10-17 16:50:05,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0016_m_000000_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 16:50:06,051 INFO [Socket Reader #1 for port 19061] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 19061: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 16:50:06,061 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:50:06,493 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 16:50:07,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0016_01_000012
2015-10-17 16:50:07,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0016_01_000002
2015-10-17 16:50:07,066 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0016_m_000000_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:50:07,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 16:50:07,067 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0016_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:50:08,033 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:09,074 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:10,198 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:10,578 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:12,454 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:13,493 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:14,498 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:14,502 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:15,568 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:16,621 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:17,591 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:17,669 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:18,732 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:19,730 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:20,700 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:20,748 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:21,826 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:22,871 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:23,773 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:23,919 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:24,981 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:26,962 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:28,152 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:29,218 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:30,288 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:30,332 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:31,400 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:32,409 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:33,430 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:33,571 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:34,482 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:35,496 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:36,525 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:36,704 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:37,529 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:38,567 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:39,611 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:39,979 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:40,656 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:41,725 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:43,407 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:43,695 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:44,763 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:45,835 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:46,636 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:46,905 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:48,037 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:49,077 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:49,865 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:50,127 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:51,175 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:52,217 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:53,008 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:53,262 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:54,685 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:55,739 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:56,092 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:50:56,833 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:57,910 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:50:59,174 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.033333335
2015-10-17 16:51:01,698 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:03,419 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:03,755 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.06666667
2015-10-17 16:51:04,420 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:05,488 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:06,583 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:06,801 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.06666667
2015-10-17 16:51:07,677 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:08,700 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:09,739 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:09,938 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.06666667
2015-10-17 16:51:10,781 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:11,858 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:12,922 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:13,085 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.06666667
2015-10-17 16:51:13,966 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:15,003 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:16,050 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:16,189 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:51:17,081 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:18,108 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:19,159 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:19,349 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:51:20,200 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:21,265 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:22,312 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:22,425 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:51:23,393 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:24,450 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:25,547 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:51:27,610 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:28,617 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:29,681 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:30,145 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:51:30,729 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:31,786 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:33,248 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:51:42,173 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:43,204 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:44,244 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:51:44,248 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:45,313 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:46,361 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:47,435 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:47,676 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:51:48,515 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:49,595 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:50,646 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:50,940 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:51:51,687 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:52,723 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:53,746 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:54,254 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:51:54,827 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:55,849 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:58,000 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:51:58,018 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:52:01,596 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:02,626 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:03,671 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:03,879 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:52:06,075 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:07,125 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:08,155 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:52:08,609 INFO [IPC Server handler 28 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:10,452 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:11,503 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:12,509 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:52:12,575 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:13,677 INFO [IPC Server handler 15 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:14,689 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:15,593 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:52:15,734 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:16,798 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:17,828 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:18,674 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:52:18,906 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:19,988 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:21,093 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:21,786 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:52:22,128 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:23,171 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:24,882 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.10000001
2015-10-17 16:52:25,092 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:26,181 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:27,203 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:27,987 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.13333334
2015-10-17 16:52:28,236 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:29,321 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:30,649 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:31,095 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.13333334
2015-10-17 16:52:31,706 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:32,707 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:33,768 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:34,404 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.16666667
2015-10-17 16:52:34,840 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:35,908 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:36,930 INFO [IPC Server handler 24 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:37,871 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.16666667
2015-10-17 16:52:38,045 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:39,120 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:40,204 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:41,271 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.16666667
2015-10-17 16:52:42,233 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:43,671 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:44,440 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.16666667
2015-10-17 16:52:44,686 INFO [IPC Server handler 19 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:45,711 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:46,721 INFO [IPC Server handler 9 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:47,694 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.16666667
2015-10-17 16:52:47,740 INFO [IPC Server handler 23 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:48,784 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:49,830 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:50,900 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:51,130 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.16666667
2015-10-17 16:52:51,953 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:52,999 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:54,018 INFO [IPC Server handler 16 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:54,327 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.16666667
2015-10-17 16:52:55,077 INFO [IPC Server handler 13 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:56,142 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:57,203 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:57,642 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.16666667
2015-10-17 16:52:58,262 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:52:59,315 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:00,393 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:00,999 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.16666667
2015-10-17 16:53:01,455 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:02,584 INFO [IPC Server handler 2 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:03,697 INFO [IPC Server handler 20 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:04,579 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.20000002
2015-10-17 16:53:04,777 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:05,867 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:06,957 INFO [IPC Server handler 1 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:07,991 INFO [IPC Server handler 26 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:08,130 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.20000002
2015-10-17 16:53:09,105 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:10,141 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:11,198 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:11,455 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.23333333
2015-10-17 16:53:12,284 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:13,298 INFO [IPC Server handler 8 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:14,327 INFO [IPC Server handler 10 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:15,208 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.23333333
2015-10-17 16:53:15,362 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:16,424 INFO [IPC Server handler 3 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:17,457 INFO [IPC Server handler 11 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:18,520 INFO [IPC Server handler 14 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:18,867 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.23333333
2015-10-17 16:53:19,562 INFO [IPC Server handler 21 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:20,640 INFO [IPC Server handler 25 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:21,663 INFO [IPC Server handler 5 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:22,213 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.23333333
2015-10-17 16:53:23,094 INFO [IPC Server handler 22 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:24,109 INFO [IPC Server handler 18 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:25,140 INFO [IPC Server handler 12 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:25,627 INFO [IPC Server handler 4 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.23333333
2015-10-17 16:53:26,171 INFO [IPC Server handler 7 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:28,196 INFO [IPC Server handler 29 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:29,348 INFO [IPC Server handler 0 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0016_r_000000_0 is : 0.26666668
2015-10-17 16:53:29,768 INFO [IPC Server handler 6 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:30,811 INFO [IPC Server handler 17 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0016_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 16:53:31,853 INFO [IPC Server handler 27 on 19061] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents reques