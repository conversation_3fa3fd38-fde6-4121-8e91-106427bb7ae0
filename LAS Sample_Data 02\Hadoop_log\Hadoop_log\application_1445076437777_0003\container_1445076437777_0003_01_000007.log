2015-10-17 18:09:31,149 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 18:09:31,274 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 18:09:31,274 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 18:09:31,321 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 18:09:31,321 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445076437777_0003, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@5c46c7b0)
2015-10-17 18:09:31,524 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 18:09:31,993 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445076437777_0003
2015-10-17 18:09:33,227 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 18:09:34,587 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 18:09:34,696 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@1bf36742
2015-10-17 18:09:36,540 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:671088640+134217728
2015-10-17 18:09:36,681 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 18:09:36,681 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 18:09:36,681 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 18:09:36,681 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 18:09:36,681 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 18:09:36,712 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 18:09:47,415 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:09:47,415 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48241717; bufvoid = 104857600
2015-10-17 18:09:47,415 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17303312(69213248); length = 8911085/6553600
2015-10-17 18:09:47,415 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57310469 kvi 14327612(57310448)
2015-10-17 18:10:05,557 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 18:10:05,572 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57310469 kv 14327612(57310448) kvi 12124056(48496224)
2015-10-17 18:10:33,995 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:10:33,995 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57310469; bufend = 677160; bufvoid = 104857600
2015-10-17 18:10:33,995 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14327612(57310448); kvend = 5412172(21648688); length = 8915441/6553600
2015-10-17 18:10:33,995 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9745912 kvi 2436472(9745888)
2015-10-17 18:11:07,402 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 18:11:07,402 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9745912 kv 2436472(9745888) kvi 243380(973520)
2015-10-17 18:11:29,481 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:11:29,481 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9745912; bufend = 58001423; bufvoid = 104857600
2015-10-17 18:11:29,481 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2436472(9745888); kvend = 19743236(78972944); length = 8907637/6553600
2015-10-17 18:11:29,481 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67070175 kvi 16767536(67070144)
2015-10-17 18:11:58,576 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 18:11:58,685 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67070175 kv 16767536(67070144) kvi 14572400(58289600)
2015-10-17 18:12:14,123 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:12:14,123 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67070175; bufend = 10445315; bufvoid = 104857600
2015-10-17 18:12:14,123 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16767536(67070144); kvend = 7854204(31416816); length = 8913333/6553600
2015-10-17 18:12:14,123 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19514051 kvi 4878508(19514032)
2015-10-17 18:12:45,999 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 18:12:46,093 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 19514051 kv 4878508(19514032) kvi 2676468(10705872)
2015-10-17 18:13:37,532 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:13:37,532 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 19514051; bufend = 67765795; bufvoid = 104857600
2015-10-17 18:13:37,532 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 4878508(19514032); kvend = 22184324(88737296); length = 8908585/6553600
2015-10-17 18:13:37,532 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 76834531 kvi 19208628(76834512)
2015-10-17 18:14:04,470 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 18:14:04,548 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 76834531 kv 19208628(76834512) kvi 17011572(68046288)
2015-10-17 18:14:23,627 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 18:14:23,627 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 76834531; bufend = 20216843; bufvoid = 104857600
2015-10-17 18:14:23,627 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 19208628(76834512); kvend = 10297092(41188368); length = 8911537/6553600
2015-10-17 18:14:23,627 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29285595 kvi 7321392(29285568)
