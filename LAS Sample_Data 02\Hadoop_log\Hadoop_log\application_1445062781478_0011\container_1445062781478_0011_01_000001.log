2015-10-17 15:37:56,547 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445062781478_0011_000001
2015-10-17 15:37:56,899 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 15:37:56,900 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 11 cluster_timestamp: 1445062781478 } attemptId: 1 } keyId: 471522253)
2015-10-17 15:37:57,036 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 15:37:57,634 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 15:37:57,720 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 15:37:57,784 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 15:37:57,787 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 15:37:57,789 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 15:37:57,792 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 15:37:57,793 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 15:37:57,808 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 15:37:57,809 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 15:37:57,812 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 15:37:57,902 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:37:57,952 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:37:58,002 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:37:58,024 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 15:37:58,131 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 15:37:58,359 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 15:37:58,405 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 15:37:58,405 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 15:37:58,412 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445062781478_0011 to jobTokenSecretManager
2015-10-17 15:37:58,550 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445062781478_0011 because: not enabled; too many maps; too much input;
2015-10-17 15:37:58,568 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445062781478_0011 = 1256521728. Number of splits = 10
2015-10-17 15:37:58,569 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445062781478_0011 = 1
2015-10-17 15:37:58,569 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0011Job Transitioned from NEW to INITED
2015-10-17 15:37:58,570 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445062781478_0011.
2015-10-17 15:37:58,601 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 15:37:58,610 INFO [Socket Reader #1 for port 47455] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 47455
2015-10-17 15:37:58,630 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 15:37:58,631 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 15:37:58,631 INFO [IPC Server listener on 47455] org.apache.hadoop.ipc.Server: IPC Server listener on 47455: starting
2015-10-17 15:37:58,631 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-41.fareast.corp.microsoft.com/**************:47455
2015-10-17 15:37:58,721 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 15:37:58,725 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 15:37:58,735 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 15:37:58,740 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 15:37:58,740 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 15:37:58,743 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 15:37:58,743 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 15:37:58,752 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 47462
2015-10-17 15:37:58,752 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 15:37:58,782 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_47462_mapreduce____j3iclo\webapp
2015-10-17 15:37:58,926 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:47462
2015-10-17 15:37:58,926 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 47462
2015-10-17 15:37:59,314 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 15:37:59,321 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445062781478_0011
2015-10-17 15:37:59,325 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 15:37:59,332 INFO [Socket Reader #1 for port 47468] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 47468
2015-10-17 15:37:59,343 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 15:37:59,343 INFO [IPC Server listener on 47468] org.apache.hadoop.ipc.Server: IPC Server listener on 47468: starting
2015-10-17 15:37:59,361 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 15:37:59,361 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 15:37:59,361 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 15:37:59,405 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at MSRA-SA-41/**************:8030
2015-10-17 15:37:59,461 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 15:37:59,461 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 15:37:59,465 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 15:37:59,466 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 15:37:59,472 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0011Job Transitioned from INITED to SETUP
2015-10-17 15:37:59,474 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 15:37:59,481 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0011Job Transitioned from SETUP to RUNNING
2015-10-17 15:37:59,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:37:59,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:37:59,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:37:59,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:37:59,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:37:59,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,500 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:37:59,500 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,500 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,500 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:37:59,500 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,500 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,500 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:37:59,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:37:59,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:37:59,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:37:59,502 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:37:59,503 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:37:59,503 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:37:59,503 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:37:59,503 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:37:59,503 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:37:59,503 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:37:59,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:37:59,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:37:59,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:37:59,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:37:59,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:37:59,505 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 15:37:59,511 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 15:37:59,547 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445062781478_0011, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0011/job_1445062781478_0011_1.jhist
2015-10-17 15:38:00,464 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 15:38:00,533 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:32768, vCores:-3> knownNMs=5
2015-10-17 15:38:00,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:32768, vCores:-3>
2015-10-17 15:38:00,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:01,585 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 10
2015-10-17 15:38:01,588 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0011_01_000002 to attempt_1445062781478_0011_m_000000_0
2015-10-17 15:38:01,591 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0011_01_000003 to attempt_1445062781478_0011_m_000001_0
2015-10-17 15:38:01,592 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0011_01_000004 to attempt_1445062781478_0011_m_000002_0
2015-10-17 15:38:01,592 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0011_01_000005 to attempt_1445062781478_0011_m_000003_0
2015-10-17 15:38:01,593 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0011_01_000006 to attempt_1445062781478_0011_m_000004_0
2015-10-17 15:38:01,593 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0011_01_000007 to attempt_1445062781478_0011_m_000005_0
2015-10-17 15:38:01,594 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0011_01_000008 to attempt_1445062781478_0011_m_000006_0
2015-10-17 15:38:01,594 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0011_01_000009 to attempt_1445062781478_0011_m_000007_0
2015-10-17 15:38:01,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0011_01_000010 to attempt_1445062781478_0011_m_000008_0
2015-10-17 15:38:01,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0011_01_000011 to attempt_1445062781478_0011_m_000009_0
2015-10-17 15:38:01,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:22528, vCores:-13>
2015-10-17 15:38:01,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:01,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 15:38:01,691 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:01,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0011/job.jar
2015-10-17 15:38:01,728 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0011/job.xml
2015-10-17 15:38:01,730 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 15:38:01,730 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 15:38:01,731 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 15:38:01,828 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:01,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:01,836 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:01,837 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:01,839 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:01,839 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:01,840 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:01,841 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:01,842 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:01,843 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:01,844 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:01,845 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:01,846 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:01,847 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:01,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:01,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:01,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:01,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:01,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:01,855 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0011_01_000002 taskAttempt attempt_1445062781478_0011_m_000000_0
2015-10-17 15:38:01,855 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0011_01_000003 taskAttempt attempt_1445062781478_0011_m_000001_0
2015-10-17 15:38:01,855 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0011_01_000004 taskAttempt attempt_1445062781478_0011_m_000002_0
2015-10-17 15:38:01,856 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0011_01_000005 taskAttempt attempt_1445062781478_0011_m_000003_0
2015-10-17 15:38:01,856 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0011_01_000006 taskAttempt attempt_1445062781478_0011_m_000004_0
2015-10-17 15:38:01,856 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0011_01_000007 taskAttempt attempt_1445062781478_0011_m_000005_0
2015-10-17 15:38:01,857 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0011_01_000008 taskAttempt attempt_1445062781478_0011_m_000006_0
2015-10-17 15:38:01,857 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0011_01_000009 taskAttempt attempt_1445062781478_0011_m_000007_0
2015-10-17 15:38:01,857 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0011_01_000010 taskAttempt attempt_1445062781478_0011_m_000008_0
2015-10-17 15:38:01,858 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0011_01_000011 taskAttempt attempt_1445062781478_0011_m_000009_0
2015-10-17 15:38:01,860 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0011_m_000004_0
2015-10-17 15:38:01,860 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0011_m_000009_0
2015-10-17 15:38:01,861 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0011_m_000007_0
2015-10-17 15:38:01,861 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0011_m_000008_0
2015-10-17 15:38:01,860 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0011_m_000002_0
2015-10-17 15:38:01,860 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0011_m_000000_0
2015-10-17 15:38:01,860 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0011_m_000001_0
2015-10-17 15:38:01,861 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0011_m_000006_0
2015-10-17 15:38:01,861 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0011_m_000003_0
2015-10-17 15:38:01,861 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0011_m_000005_0
2015-10-17 15:38:01,862 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:38:01,903 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:38:01,906 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:38:01,907 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:38:01,909 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:38:01,911 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:38:01,912 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:38:01,914 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:38:01,917 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:38:01,918 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:38:02,024 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0011_m_000008_0 : 13562
2015-10-17 15:38:02,024 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0011_m_000007_0 : 13562
2015-10-17 15:38:02,024 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0011_m_000009_0 : 13562
2015-10-17 15:38:02,024 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0011_m_000006_0 : 13562
2015-10-17 15:38:02,027 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0011_m_000006_0] using containerId: [container_1445062781478_0011_01_000008 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:38:02,033 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:02,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0011_m_000008_0] using containerId: [container_1445062781478_0011_01_000010 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:38:02,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:02,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0011_m_000009_0] using containerId: [container_1445062781478_0011_01_000011 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:38:02,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:02,036 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0011_m_000007_0] using containerId: [container_1445062781478_0011_01_000009 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:38:02,036 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:02,037 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0011_m_000006
2015-10-17 15:38:02,037 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:02,037 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0011_m_000008
2015-10-17 15:38:02,038 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:02,038 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0011_m_000009
2015-10-17 15:38:02,038 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:02,039 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0011_m_000007
2015-10-17 15:38:02,039 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:02,040 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0011_m_000002_0 : 13562
2015-10-17 15:38:02,041 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0011_m_000002_0] using containerId: [container_1445062781478_0011_01_000004 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:38:02,041 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0011_m_000001_0 : 13562
2015-10-17 15:38:02,041 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:02,042 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0011_m_000001_0] using containerId: [container_1445062781478_0011_01_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:38:02,042 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:02,043 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0011_m_000003_0 : 13562
2015-10-17 15:38:02,043 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0011_m_000002
2015-10-17 15:38:02,043 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:02,043 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0011_m_000001
2015-10-17 15:38:02,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:02,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0011_m_000003_0] using containerId: [container_1445062781478_0011_01_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:38:02,045 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:02,045 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0011_m_000003
2015-10-17 15:38:02,045 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:02,046 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0011_m_000004_0 : 13562
2015-10-17 15:38:02,047 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0011_m_000004_0] using containerId: [container_1445062781478_0011_01_000006 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:38:02,047 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:02,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0011_m_000004
2015-10-17 15:38:02,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:02,049 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0011_m_000000_0 : 13562
2015-10-17 15:38:02,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0011_m_000000_0] using containerId: [container_1445062781478_0011_01_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:38:02,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:02,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0011_m_000000
2015-10-17 15:38:02,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:02,051 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0011_m_000005_0 : 13562
2015-10-17 15:38:02,052 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0011_m_000005_0] using containerId: [container_1445062781478_0011_01_000007 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:38:02,052 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:02,052 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0011_m_000005
2015-10-17 15:38:02,053 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:02,599 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:22528, vCores:-13> knownNMs=5
2015-10-17 15:38:04,349 INFO [Socket Reader #1 for port 47468] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0011 (auth:SIMPLE)
2015-10-17 15:38:04,352 INFO [Socket Reader #1 for port 47468] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0011 (auth:SIMPLE)
2015-10-17 15:38:04,375 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0011_m_000007 asked for a task
2015-10-17 15:38:04,375 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0011_m_000005 asked for a task
2015-10-17 15:38:04,375 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0011_m_000007 given task: attempt_1445062781478_0011_m_000005_0
2015-10-17 15:38:04,375 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0011_m_000005 given task: attempt_1445062781478_0011_m_000003_0
2015-10-17 15:38:04,385 INFO [Socket Reader #1 for port 47468] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0011 (auth:SIMPLE)
2015-10-17 15:38:04,397 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0011_m_000002 asked for a task
2015-10-17 15:38:04,397 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0011_m_000002 given task: attempt_1445062781478_0011_m_000000_0
2015-10-17 15:38:05,606 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:18432, vCores:-17>
2015-10-17 15:38:05,606 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:05,609 INFO [Socket Reader #1 for port 47468] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0011 (auth:SIMPLE)
2015-10-17 15:38:05,631 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0011_m_000003 asked for a task
2015-10-17 15:38:05,631 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0011_m_000003 given task: attempt_1445062781478_0011_m_000001_0
2015-10-17 15:38:05,673 INFO [Socket Reader #1 for port 47468] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0011 (auth:SIMPLE)
2015-10-17 15:38:05,676 INFO [Socket Reader #1 for port 47468] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0011 (auth:SIMPLE)
2015-10-17 15:38:05,702 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0011_m_000006 asked for a task
2015-10-17 15:38:05,702 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0011_m_000006 given task: attempt_1445062781478_0011_m_000004_0
2015-10-17 15:38:05,705 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0011_m_000004 asked for a task
2015-10-17 15:38:05,705 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0011_m_000004 given task: attempt_1445062781478_0011_m_000002_0
2015-10-17 15:38:06,006 INFO [Socket Reader #1 for port 47468] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0011 (auth:SIMPLE)
2015-10-17 15:38:06,032 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0011_m_000011 asked for a task
2015-10-17 15:38:06,033 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0011_m_000011 given task: attempt_1445062781478_0011_m_000009_0
2015-10-17 15:38:06,068 INFO [Socket Reader #1 for port 47468] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0011 (auth:SIMPLE)
2015-10-17 15:38:06,093 INFO [Socket Reader #1 for port 47468] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0011 (auth:SIMPLE)
2015-10-17 15:38:06,096 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0011_m_000009 asked for a task
2015-10-17 15:38:06,096 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0011_m_000009 given task: attempt_1445062781478_0011_m_000007_0
2015-10-17 15:38:06,120 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0011_m_000008 asked for a task
2015-10-17 15:38:06,121 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0011_m_000008 given task: attempt_1445062781478_0011_m_000006_0
2015-10-17 15:38:06,180 INFO [Socket Reader #1 for port 47468] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0011 (auth:SIMPLE)
2015-10-17 15:38:06,207 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0011_m_000010 asked for a task
2015-10-17 15:38:06,208 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0011_m_000010 given task: attempt_1445062781478_0011_m_000008_0
2015-10-17 15:38:06,609 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:15360, vCores:-20>
2015-10-17 15:38:06,609 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:07,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:13312, vCores:-22>
2015-10-17 15:38:07,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:08,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-25>
2015-10-17 15:38:08,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:09,617 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-28>
2015-10-17 15:38:09,617 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:10,619 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-27>
2015-10-17 15:38:10,619 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:11,574 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.09068313
2015-10-17 15:38:11,610 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.09088517
2015-10-17 15:38:11,639 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.08696926
2015-10-17 15:38:12,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-28>
2015-10-17 15:38:12,624 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:13,626 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-30>
2015-10-17 15:38:13,626 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:13,731 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.079341404
2015-10-17 15:38:13,748 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.23967324
2015-10-17 15:38:13,811 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.0794752
2015-10-17 15:38:13,853 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.08663876
2015-10-17 15:38:14,397 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.062167022
2015-10-17 15:38:14,399 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.061041567
2015-10-17 15:38:14,419 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.06243093
2015-10-17 15:38:14,625 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.10635664
2015-10-17 15:38:14,628 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-17 15:38:14,628 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:14,649 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.106493875
2015-10-17 15:38:14,669 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.10685723
2015-10-17 15:38:15,632 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:15,632 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:16,746 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.10681946
2015-10-17 15:38:16,761 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.295472
2015-10-17 15:38:16,825 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.106881365
2015-10-17 15:38:16,855 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.106964506
2015-10-17 15:38:17,427 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.10030612
2015-10-17 15:38:17,427 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.103239976
2015-10-17 15:38:17,449 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.102346756
2015-10-17 15:38:17,637 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:38:17,637 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:17,656 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.10635664
2015-10-17 15:38:17,677 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.106493875
2015-10-17 15:38:17,697 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.10685723
2015-10-17 15:38:19,747 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.10681946
2015-10-17 15:38:19,769 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.295472
2015-10-17 15:38:19,824 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.106881365
2015-10-17 15:38:19,866 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.106964506
2015-10-17 15:38:20,447 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.1066108
2015-10-17 15:38:20,447 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.10660437
2015-10-17 15:38:20,467 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.10680563
2015-10-17 15:38:20,682 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.10635664
2015-10-17 15:38:20,698 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.106493875
2015-10-17 15:38:20,716 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.10685723
2015-10-17 15:38:22,642 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:22,642 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:22,755 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.10681946
2015-10-17 15:38:22,781 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.295472
2015-10-17 15:38:22,836 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.106881365
2015-10-17 15:38:22,880 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.106964506
2015-10-17 15:38:23,467 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.1066108
2015-10-17 15:38:23,484 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.10660437
2015-10-17 15:38:23,493 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.10680563
2015-10-17 15:38:23,712 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.12171131
2015-10-17 15:38:23,726 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.1254422
2015-10-17 15:38:23,743 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.12669647
2015-10-17 15:38:25,770 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.1281273
2015-10-17 15:38:25,796 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.31369507
2015-10-17 15:38:25,848 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.12761278
2015-10-17 15:38:25,889 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.110805154
2015-10-17 15:38:26,508 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.12093705
2015-10-17 15:38:26,525 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.11859703
2015-10-17 15:38:26,532 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.120352514
2015-10-17 15:38:26,754 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.15326358
2015-10-17 15:38:26,766 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.152183
2015-10-17 15:38:26,785 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.1555351
2015-10-17 15:38:28,783 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.1527431
2015-10-17 15:38:28,816 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.35048062
2015-10-17 15:38:28,868 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.15726237
2015-10-17 15:38:28,916 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.12408431
2015-10-17 15:38:29,542 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.15714484
2015-10-17 15:38:29,559 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.15627116
2015-10-17 15:38:29,564 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.15622298
2015-10-17 15:38:29,786 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.19158794
2015-10-17 15:38:29,796 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.19209063
2015-10-17 15:38:29,814 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.19247705
2015-10-17 15:38:31,795 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.15744123
2015-10-17 15:38:31,823 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.37715548
2015-10-17 15:38:31,875 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.18097214
2015-10-17 15:38:31,915 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.13255043
2015-10-17 15:38:32,564 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.19211523
2015-10-17 15:38:32,579 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.19212553
2015-10-17 15:38:32,583 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.19242907
2015-10-17 15:38:32,807 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.19158794
2015-10-17 15:38:32,821 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.19209063
2015-10-17 15:38:32,833 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.19247705
2015-10-17 15:38:34,812 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.16186166
2015-10-17 15:38:34,833 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.38254938
2015-10-17 15:38:34,889 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.19258286
2015-10-17 15:38:34,927 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.1419942
2015-10-17 15:38:35,594 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.19211523
2015-10-17 15:38:35,604 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.19242907
2015-10-17 15:38:35,613 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.19212553
2015-10-17 15:38:35,828 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.19158794
2015-10-17 15:38:35,841 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.19209063
2015-10-17 15:38:35,853 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.19247705
2015-10-17 15:38:37,824 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.16870272
2015-10-17 15:38:37,840 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.3861577
2015-10-17 15:38:37,902 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.19258286
2015-10-17 15:38:37,934 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.14818479
2015-10-17 15:38:38,621 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.19211523
2015-10-17 15:38:38,630 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.19242907
2015-10-17 15:38:38,639 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.19212553
2015-10-17 15:38:38,857 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.20680812
2015-10-17 15:38:38,869 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.2172295
2015-10-17 15:38:38,880 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.22663447
2015-10-17 15:38:40,833 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.17163295
2015-10-17 15:38:40,848 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.38885322
2015-10-17 15:38:40,913 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.19258286
2015-10-17 15:38:40,941 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.15144187
2015-10-17 15:38:41,659 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.20094542
2015-10-17 15:38:41,665 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.20583165
2015-10-17 15:38:41,675 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.20390213
2015-10-17 15:38:41,899 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.23056613
2015-10-17 15:38:41,908 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.23584847
2015-10-17 15:38:41,920 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.2565981
2015-10-17 15:38:43,843 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.19255035
2015-10-17 15:38:43,864 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.4383594
2015-10-17 15:38:43,920 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.21006429
2015-10-17 15:38:43,957 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.16349196
2015-10-17 15:38:44,697 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.23155917
2015-10-17 15:38:44,700 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.23123392
2015-10-17 15:38:44,709 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.22927676
2015-10-17 15:38:44,933 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.25403136
2015-10-17 15:38:44,937 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.2540298
2015-10-17 15:38:44,945 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.27813601
2015-10-17 15:38:46,864 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.19255035
2015-10-17 15:38:46,871 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.47508562
2015-10-17 15:38:46,941 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.23294003
2015-10-17 15:38:46,965 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.17195699
2015-10-17 15:38:47,727 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.26308239
2015-10-17 15:38:47,735 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.26608133
2015-10-17 15:38:47,739 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.26320696
2015-10-17 15:38:47,962 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.27696857
2015-10-17 15:38:47,963 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.27765483
2015-10-17 15:38:47,972 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.27813601
2015-10-17 15:38:49,873 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.19255035
2015-10-17 15:38:49,874 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.5157738
2015-10-17 15:38:49,953 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.26836184
2015-10-17 15:38:49,982 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.18791856
2015-10-17 15:38:50,748 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.27776006
2015-10-17 15:38:50,755 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.2781602
2015-10-17 15:38:50,757 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.27772525
2015-10-17 15:38:50,984 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.27765483
2015-10-17 15:38:50,984 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.27696857
2015-10-17 15:38:50,991 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.27813601
2015-10-17 15:38:52,875 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.2071312
2015-10-17 15:38:52,893 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.5323719
2015-10-17 15:38:52,975 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.27811313
2015-10-17 15:38:52,984 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.19266446
2015-10-17 15:38:53,775 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.27776006
2015-10-17 15:38:53,779 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.2781602
2015-10-17 15:38:53,781 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.27772525
2015-10-17 15:38:54,006 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.27780753
2015-10-17 15:38:54,019 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.32828838
2015-10-17 15:38:54,023 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.28497067
2015-10-17 15:38:55,888 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.23449035
2015-10-17 15:38:55,906 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.5323719
2015-10-17 15:38:55,984 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.27811313
2015-10-17 15:38:56,000 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.19266446
2015-10-17 15:38:56,815 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.28917724
2015-10-17 15:38:56,818 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.28887984
2015-10-17 15:38:56,819 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.28725195
2015-10-17 15:38:57,043 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.29692492
2015-10-17 15:38:57,059 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.30663198
2015-10-17 15:38:57,062 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.34971544
2015-10-17 15:38:58,895 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.26830998
2015-10-17 15:38:58,924 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.59386617
2015-10-17 15:38:59,004 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.27811313
2015-10-17 15:38:59,020 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.20435055
2015-10-17 15:38:59,852 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.32696438
2015-10-17 15:38:59,853 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.32437798
2015-10-17 15:38:59,854 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.32327434
2015-10-17 15:39:00,075 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.34491253
2015-10-17 15:39:00,081 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.36390656
2015-10-17 15:39:00,098 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.3536914
2015-10-17 15:39:01,908 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.27825075
2015-10-17 15:39:01,940 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.62019265
2015-10-17 15:39:02,017 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.2976724
2015-10-17 15:39:02,034 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.21429864
2015-10-17 15:39:02,872 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.36046797
2015-10-17 15:39:02,872 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.3632276
2015-10-17 15:39:02,873 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.36058012
2015-10-17 15:39:03,096 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.3624012
2015-10-17 15:39:03,099 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.36390656
2015-10-17 15:39:03,118 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.36323506
2015-10-17 15:39:04,772 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.62019265
2015-10-17 15:39:04,918 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.27825075
2015-10-17 15:39:04,949 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.667
2015-10-17 15:39:05,028 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.3038585
2015-10-17 15:39:05,043 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.21853268
2015-10-17 15:39:05,894 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.36319977
2015-10-17 15:39:05,894 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.36388028
2015-10-17 15:39:05,894 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.36317363
2015-10-17 15:39:06,116 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.3624012
2015-10-17 15:39:06,117 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.37544397
2015-10-17 15:39:06,138 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.36323506
2015-10-17 15:39:07,922 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.27825075
2015-10-17 15:39:07,951 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.667
2015-10-17 15:39:08,029 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.33577597
2015-10-17 15:39:08,046 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.2302554
2015-10-17 15:39:08,915 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.36319977
2015-10-17 15:39:08,917 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.36317363
2015-10-17 15:39:08,924 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.36388028
2015-10-17 15:39:09,139 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.36965024
2015-10-17 15:39:09,149 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.43528605
2015-10-17 15:39:09,163 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.3901665
2015-10-17 15:39:10,934 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.29343957
2015-10-17 15:39:10,949 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.667
2015-10-17 15:39:11,028 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.3637686
2015-10-17 15:39:11,060 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.2455647
2015-10-17 15:39:11,938 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.3726046
2015-10-17 15:39:11,954 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.37973797
2015-10-17 15:39:11,955 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.37959167
2015-10-17 15:39:12,170 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.39668205
2015-10-17 15:39:12,177 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.44950968
2015-10-17 15:39:12,192 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.41556922
2015-10-17 15:39:13,938 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.33154386
2015-10-17 15:39:13,950 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.6856531
2015-10-17 15:39:14,031 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.3637686
2015-10-17 15:39:14,059 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.26294544
2015-10-17 15:39:14,963 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.4058004
2015-10-17 15:39:14,971 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.4123117
2015-10-17 15:39:14,985 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.41264024
2015-10-17 15:39:15,202 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.42836267
2015-10-17 15:39:15,205 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.44950968
2015-10-17 15:39:15,220 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.4413918
2015-10-17 15:39:16,956 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.77028006
2015-10-17 15:39:16,960 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.34568423
2015-10-17 15:39:17,048 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.3637686
2015-10-17 15:39:17,067 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.2783809
2015-10-17 15:39:17,981 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.43120337
2015-10-17 15:39:17,987 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.43445972
2015-10-17 15:39:18,001 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.4374781
2015-10-17 15:39:18,221 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.44789755
2015-10-17 15:39:18,221 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.44950968
2015-10-17 15:39:18,238 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.4486067
2015-10-17 15:39:19,968 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.860949
2015-10-17 15:39:19,968 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.3638923
2015-10-17 15:39:20,063 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.38300267
2015-10-17 15:39:20,075 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.2783809
2015-10-17 15:39:21,000 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.44859612
2015-10-17 15:39:21,003 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.448704
2015-10-17 15:39:21,021 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.44968578
2015-10-17 15:39:21,241 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.44789755
2015-10-17 15:39:21,241 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.47229117
2015-10-17 15:39:21,256 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.4486067
2015-10-17 15:39:22,975 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 0.9750354
2015-10-17 15:39:22,978 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.3638923
2015-10-17 15:39:23,085 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.44228736
2015-10-17 15:39:23,086 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.2783809
2015-10-17 15:39:24,019 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.44859612
2015-10-17 15:39:24,019 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.448704
2015-10-17 15:39:24,037 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.44968578
2015-10-17 15:39:24,241 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000009_0 is : 1.0
2015-10-17 15:39:24,243 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0011_m_000009_0
2015-10-17 15:39:24,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:39:24,244 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0011_01_000011 taskAttempt attempt_1445062781478_0011_m_000009_0
2015-10-17 15:39:24,244 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0011_m_000009_0
2015-10-17 15:39:24,245 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:39:24,261 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.5182623
2015-10-17 15:39:24,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:39:24,272 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0011_m_000009_0
2015-10-17 15:39:24,273 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:39:24,275 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 15:39:24,280 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.45005924
2015-10-17 15:39:24,283 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.46678588
2015-10-17 15:39:24,474 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0011_m_000006
2015-10-17 15:39:24,474 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:39:24,474 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0011_m_000006
2015-10-17 15:39:24,475 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:39:24,475 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:39:24,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:39:24,730 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 15:39:24,731 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:39:24,731 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:39:24,732 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 15:39:24,732 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:10240, vCores:-25> finalMapResourceLimit:<memory:9216, vCores:-23> finalReduceResourceLimit:<memory:1024, vCores:-2> netScheduledMapResource:<memory:11264, vCores:11> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-17 15:39:24,732 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-17 15:39:24,732 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 15:39:25,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0011: ask=1 release= 0 newContainers=1 finishedContainers=1 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:39:25,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0011_01_000011
2015-10-17 15:39:25,740 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:39:25,740 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0011_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:39:25,740 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0011_01_000012 to attempt_1445062781478_0011_m_000006_1
2015-10-17 15:39:25,740 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 15:39:25,740 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:39:25,741 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:39:25,741 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0011_01_000012 taskAttempt attempt_1445062781478_0011_m_000006_1
2015-10-17 15:39:25,741 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0011_m_000006_1
2015-10-17 15:39:25,741 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:39:25,757 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0011_m_000006_1 : 13562
2015-10-17 15:39:25,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0011_m_000006_1] using containerId: [container_1445062781478_0011_01_000012 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:39:25,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:39:25,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0011_m_000006
2015-10-17 15:39:25,981 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.3638923
2015-10-17 15:39:26,091 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.44950172
2015-10-17 15:39:26,096 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.2882277
2015-10-17 15:39:26,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:39:27,049 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.45411125
2015-10-17 15:39:27,052 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.45074397
2015-10-17 15:39:27,068 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.44968578
2015-10-17 15:39:27,292 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.5352021
2015-10-17 15:39:27,302 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.4907752
2015-10-17 15:39:27,317 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.470929
2015-10-17 15:39:28,803 INFO [Socket Reader #1 for port 47468] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0011 (auth:SIMPLE)
2015-10-17 15:39:28,828 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0011_m_000012 asked for a task
2015-10-17 15:39:28,828 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0011_m_000012 given task: attempt_1445062781478_0011_m_000006_1
2015-10-17 15:39:28,989 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.37192723
2015-10-17 15:39:29,103 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.44950172
2015-10-17 15:39:29,114 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.3041856
2015-10-17 15:39:30,083 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.4756677
2015-10-17 15:39:30,089 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.4784185
2015-10-17 15:39:30,099 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.47484598
2015-10-17 15:39:30,323 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.5352021
2015-10-17 15:39:30,334 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.52723664
2015-10-17 15:39:30,348 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.5127168
2015-10-17 15:39:32,003 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.39081845
2015-10-17 15:39:32,118 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.44950172
2015-10-17 15:39:32,125 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.32502994
2015-10-17 15:39:33,102 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.5067614
2015-10-17 15:39:33,105 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.50813025
2015-10-17 15:39:33,117 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.50904167
2015-10-17 15:39:33,357 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.5352021
2015-10-17 15:39:33,366 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.5343203
2015-10-17 15:39:33,376 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.53341997
2015-10-17 15:39:35,025 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.39798364
2015-10-17 15:39:35,126 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.32698333
2015-10-17 15:39:35,134 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.4637714
2015-10-17 15:39:36,051 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.01237266
2015-10-17 15:39:36,129 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.52984405
2015-10-17 15:39:36,145 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.53425497
2015-10-17 15:39:36,149 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.533576
2015-10-17 15:39:36,386 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.5808417
2015-10-17 15:39:36,394 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.5343203
2015-10-17 15:39:36,406 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.53341997
2015-10-17 15:39:38,040 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.41850245
2015-10-17 15:39:38,126 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.33089384
2015-10-17 15:39:38,149 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.4738664
2015-10-17 15:39:39,072 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.016607514
2015-10-17 15:39:39,148 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.5342037
2015-10-17 15:39:39,163 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.53425497
2015-10-17 15:39:39,166 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.5352028
2015-10-17 15:39:39,405 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.6209487
2015-10-17 15:39:39,410 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.55235684
2015-10-17 15:39:39,426 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.54014087
2015-10-17 15:39:39,476 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0011_m_000007
2015-10-17 15:39:39,476 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 15:39:39,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0011_m_000007
2015-10-17 15:39:39,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:39:39,477 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:39:39,477 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000007_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:39:39,764 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 15:39:39,765 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:39:41,059 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.42501315
2015-10-17 15:39:41,133 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.34522313
2015-10-17 15:39:41,163 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.49666637
2015-10-17 15:39:42,081 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.035509605
2015-10-17 15:39:42,173 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.5342037
2015-10-17 15:39:42,187 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.53425497
2015-10-17 15:39:42,188 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.5352028
2015-10-17 15:39:42,432 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.6209487
2015-10-17 15:39:42,434 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.5937642
2015-10-17 15:39:42,452 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.58265907
2015-10-17 15:39:44,071 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.4304316
2015-10-17 15:39:44,142 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.36404583
2015-10-17 15:39:44,181 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.51653254
2015-10-17 15:39:45,094 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.04852499
2015-10-17 15:39:45,200 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.54961467
2015-10-17 15:39:45,212 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.5507043
2015-10-17 15:39:45,213 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.5547362
2015-10-17 15:39:45,452 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.6199081
2015-10-17 15:39:45,463 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.6209487
2015-10-17 15:39:45,477 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.61190426
2015-10-17 15:39:47,080 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.44964966
2015-10-17 15:39:47,153 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.36404583
2015-10-17 15:39:47,189 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.53521925
2015-10-17 15:39:48,097 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.106964506
2015-10-17 15:39:48,236 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.5736758
2015-10-17 15:39:48,245 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.5784936
2015-10-17 15:39:48,247 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.5750809
2015-10-17 15:39:48,484 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.6199081
2015-10-17 15:39:48,493 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.6327201
2015-10-17 15:39:48,506 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.61898744
2015-10-17 15:39:50,079 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.44964966
2015-10-17 15:39:50,160 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.36404583
2015-10-17 15:39:50,189 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.53521925
2015-10-17 15:39:51,111 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.106964506
2015-10-17 15:39:51,270 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.59795403
2015-10-17 15:39:51,271 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.5969772
2015-10-17 15:39:51,286 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.60088533
2015-10-17 15:39:51,515 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.6199081
2015-10-17 15:39:51,522 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.6658388
2015-10-17 15:39:51,536 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.61898744
2015-10-17 15:39:51,598 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.6658388
2015-10-17 15:39:53,088 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.44964966
2015-10-17 15:39:53,178 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.40286964
2015-10-17 15:39:53,196 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.53521925
2015-10-17 15:39:54,115 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.106964506
2015-10-17 15:39:54,292 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.6171619
2015-10-17 15:39:54,303 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.6208445
2015-10-17 15:39:54,310 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.6197233
2015-10-17 15:39:54,542 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.64504886
2015-10-17 15:39:54,545 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.667
2015-10-17 15:39:54,560 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.6302427
2015-10-17 15:39:56,104 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.48496732
2015-10-17 15:39:56,191 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.42084545
2015-10-17 15:39:56,212 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.5840134
2015-10-17 15:39:56,631 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.64504886
2015-10-17 15:39:57,126 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.106964506
2015-10-17 15:39:57,312 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.6196791
2015-10-17 15:39:57,320 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.6208445
2015-10-17 15:39:57,332 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.6197233
2015-10-17 15:39:57,561 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.667
2015-10-17 15:39:57,574 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.667
2015-10-17 15:39:57,583 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.65932703
2015-10-17 15:39:58,245 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.65932703
2015-10-17 15:39:59,117 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.49536213
2015-10-17 15:39:59,188 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.44119853
2015-10-17 15:39:59,226 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.61747587
2015-10-17 15:40:00,129 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.15176594
2015-10-17 15:40:00,330 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.6196791
2015-10-17 15:40:00,337 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.6208445
2015-10-17 15:40:00,349 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.6197233
2015-10-17 15:40:00,580 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.667
2015-10-17 15:40:00,592 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.667
2015-10-17 15:40:00,601 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.667
2015-10-17 15:40:02,133 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.5126242
2015-10-17 15:40:02,197 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.44911548
2015-10-17 15:40:02,242 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.6207798
2015-10-17 15:40:03,149 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.17532343
2015-10-17 15:40:03,362 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.6379499
2015-10-17 15:40:03,366 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.63899136
2015-10-17 15:40:03,379 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.6445631
2015-10-17 15:40:03,606 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.667
2015-10-17 15:40:03,622 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.69770455
2015-10-17 15:40:03,630 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.667
2015-10-17 15:40:05,143 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.52760565
2015-10-17 15:40:05,204 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.44980705
2015-10-17 15:40:05,254 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.6207798
2015-10-17 15:40:05,898 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.6445631
2015-10-17 15:40:06,159 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.19266446
2015-10-17 15:40:06,388 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.6654912
2015-10-17 15:40:06,400 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.6644439
2015-10-17 15:40:06,412 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.667
2015-10-17 15:40:06,543 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.6654912
2015-10-17 15:40:06,636 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.6919308
2015-10-17 15:40:06,654 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.7348717
2015-10-17 15:40:06,658 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.6763529
2015-10-17 15:40:06,677 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.6644439
2015-10-17 15:40:08,149 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.5352825
2015-10-17 15:40:08,214 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.46196654
2015-10-17 15:40:08,271 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.6207798
2015-10-17 15:40:09,159 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.19266446
2015-10-17 15:40:09,407 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.667
2015-10-17 15:40:09,417 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.667
2015-10-17 15:40:09,429 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.667
2015-10-17 15:40:09,655 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.726009
2015-10-17 15:40:09,672 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.7691313
2015-10-17 15:40:09,674 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.709601
2015-10-17 15:40:11,164 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.5352825
2015-10-17 15:40:11,222 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.48298714
2015-10-17 15:40:11,285 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.655925
2015-10-17 15:40:12,161 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.19266446
2015-10-17 15:40:12,426 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.667
2015-10-17 15:40:12,435 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.667
2015-10-17 15:40:12,447 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.667
2015-10-17 15:40:12,672 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.7620085
2015-10-17 15:40:12,691 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.7448355
2015-10-17 15:40:12,691 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.80490553
2015-10-17 15:40:13,580 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.655925
2015-10-17 15:40:14,180 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.5603814
2015-10-17 15:40:14,228 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.49699035
2015-10-17 15:40:14,291 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.667
2015-10-17 15:40:15,176 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.21788055
2015-10-17 15:40:15,444 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.6800562
2015-10-17 15:40:15,451 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.6826211
2015-10-17 15:40:15,464 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.6870401
2015-10-17 15:40:15,690 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.7990417
2015-10-17 15:40:15,710 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.8417131
2015-10-17 15:40:15,710 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.78116524
2015-10-17 15:40:17,193 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.58525276
2015-10-17 15:40:17,237 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.51425296
2015-10-17 15:40:17,321 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.667
2015-10-17 15:40:18,192 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.26814502
2015-10-17 15:40:18,464 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.71709144
2015-10-17 15:40:18,467 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.71909183
2015-10-17 15:40:18,481 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.72314
2015-10-17 15:40:18,708 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.83602375
2015-10-17 15:40:18,731 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.8172147
2015-10-17 15:40:18,731 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.8789899
2015-10-17 15:40:20,212 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.6070729
2015-10-17 15:40:20,241 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.53543663
2015-10-17 15:40:20,331 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.667
2015-10-17 15:40:21,205 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.2783809
2015-10-17 15:40:21,484 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.7543961
2015-10-17 15:40:21,484 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.7559191
2015-10-17 15:40:21,500 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.75950634
2015-10-17 15:40:21,727 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.8733153
2015-10-17 15:40:21,751 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.91630477
2015-10-17 15:40:21,751 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.85328144
2015-10-17 15:40:23,223 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.61944807
2015-10-17 15:40:23,253 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.53543663
2015-10-17 15:40:23,334 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.6755245
2015-10-17 15:40:24,212 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.2783809
2015-10-17 15:40:24,503 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.7929754
2015-10-17 15:40:24,504 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.79187286
2015-10-17 15:40:24,516 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.7960567
2015-10-17 15:40:24,746 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.91069734
2015-10-17 15:40:24,768 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.9536185
2015-10-17 15:40:24,768 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.8894041
2015-10-17 15:40:26,221 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.620844
2015-10-17 15:40:26,254 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.53543663
2015-10-17 15:40:26,347 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.7159548
2015-10-17 15:40:27,221 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.2783809
2015-10-17 15:40:27,524 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.8287435
2015-10-17 15:40:27,526 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.82987404
2015-10-17 15:40:27,533 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.83255506
2015-10-17 15:40:27,763 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.9477836
2015-10-17 15:40:27,787 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.9253882
2015-10-17 15:40:27,787 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 0.9908032
2015-10-17 15:40:28,618 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000005_0 is : 1.0
2015-10-17 15:40:28,619 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0011_m_000005_0
2015-10-17 15:40:28,620 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:40:28,620 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0011_01_000007 taskAttempt attempt_1445062781478_0011_m_000005_0
2015-10-17 15:40:28,620 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0011_m_000005_0
2015-10-17 15:40:28,621 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:40:28,630 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:40:28,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0011_m_000005_0
2015-10-17 15:40:28,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:40:28,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 15:40:28,830 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 15:40:29,221 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.620844
2015-10-17 15:40:29,252 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.55490005
2015-10-17 15:40:29,346 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.76607764
2015-10-17 15:40:29,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0011_01_000007
2015-10-17 15:40:29,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:40:29,834 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0011_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:40:29,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 15:40:29,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0011_01_000013 to attempt_1445062781478_0011_r_000000_0
2015-10-17 15:40:29,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 15:40:29,842 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:40:29,843 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:40:29,843 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0011_01_000013 taskAttempt attempt_1445062781478_0011_r_000000_0
2015-10-17 15:40:29,843 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0011_r_000000_0
2015-10-17 15:40:29,843 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:40:29,854 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0011_r_000000_0 : 13562
2015-10-17 15:40:29,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0011_r_000000_0] using containerId: [container_1445062781478_0011_01_000013 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:40:29,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:40:29,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0011_r_000000
2015-10-17 15:40:29,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:40:30,227 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.34499955
2015-10-17 15:40:30,543 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.8655154
2015-10-17 15:40:30,544 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.86674535
2015-10-17 15:40:30,550 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.86890256
2015-10-17 15:40:30,781 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 0.98506016
2015-10-17 15:40:30,804 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.9614339
2015-10-17 15:40:30,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0011: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:40:31,772 INFO [Socket Reader #1 for port 47468] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0011 (auth:SIMPLE)
2015-10-17 15:40:31,787 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0011_r_000013 asked for a task
2015-10-17 15:40:31,787 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0011_r_000013 given task: attempt_1445062781478_0011_r_000000_0
2015-10-17 15:40:32,065 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000003_0 is : 1.0
2015-10-17 15:40:32,066 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0011_m_000003_0
2015-10-17 15:40:32,067 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:40:32,067 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0011_01_000005 taskAttempt attempt_1445062781478_0011_m_000003_0
2015-10-17 15:40:32,067 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0011_m_000003_0
2015-10-17 15:40:32,067 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:40:32,079 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:40:32,079 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0011_m_000003_0
2015-10-17 15:40:32,079 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:40:32,080 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 15:40:32,228 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.6575598
2015-10-17 15:40:32,275 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.5871442
2015-10-17 15:40:32,352 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.7930778
2015-10-17 15:40:32,708 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.6575598
2015-10-17 15:40:32,837 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 15:40:33,018 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 15:40:33,243 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.36404583
2015-10-17 15:40:33,561 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.9038297
2015-10-17 15:40:33,564 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.90268505
2015-10-17 15:40:33,566 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.9055817
2015-10-17 15:40:33,832 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 0.998171
2015-10-17 15:40:33,843 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0011_01_000005
2015-10-17 15:40:33,843 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:40:33,843 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0011_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:40:33,843 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0011_01_000014 to attempt_1445062781478_0011_m_000007_1
2015-10-17 15:40:33,844 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 15:40:33,844 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:40:33,845 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000007_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:40:33,845 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0011_01_000014 taskAttempt attempt_1445062781478_0011_m_000007_1
2015-10-17 15:40:33,846 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0011_m_000007_1
2015-10-17 15:40:33,846 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:40:33,864 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0011_m_000007_1 : 13562
2015-10-17 15:40:33,864 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0011_m_000007_1] using containerId: [container_1445062781478_0011_01_000014 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 15:40:33,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000007_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:40:33,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0011_m_000007
2015-10-17 15:40:34,026 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:40:34,097 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000000_0 is : 1.0
2015-10-17 15:40:34,099 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0011_m_000000_0
2015-10-17 15:40:34,100 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:40:34,100 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0011_01_000002 taskAttempt attempt_1445062781478_0011_m_000000_0
2015-10-17 15:40:34,100 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0011_m_000000_0
2015-10-17 15:40:34,101 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:40:34,116 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:40:34,116 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0011_m_000000_0
2015-10-17 15:40:34,116 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:40:34,117 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 15:40:34,844 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 15:40:34,847 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 15:40:35,029 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 15:40:35,239 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.667
2015-10-17 15:40:35,284 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.6133294
2015-10-17 15:40:35,362 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.8254299
2015-10-17 15:40:35,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0011_01_000002
2015-10-17 15:40:35,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 15:40:35,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0011_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:40:36,032 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:36,253 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.36404583
2015-10-17 15:40:36,593 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.9210699
2015-10-17 15:40:36,600 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.91984534
2015-10-17 15:40:36,609 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.9226719
2015-10-17 15:40:37,038 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:38,002 INFO [Socket Reader #1 for port 47468] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0011 (auth:SIMPLE)
2015-10-17 15:40:38,036 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0011_m_000014 asked for a task
2015-10-17 15:40:38,037 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0011_m_000014 given task: attempt_1445062781478_0011_m_000007_1
2015-10-17 15:40:38,041 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:38,260 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.667
2015-10-17 15:40:38,284 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.6210422
2015-10-17 15:40:38,368 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.8584731
2015-10-17 15:40:38,998 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.13333334
2015-10-17 15:40:39,043 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:39,259 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.36404583
2015-10-17 15:40:39,622 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.9390342
2015-10-17 15:40:39,629 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.9378089
2015-10-17 15:40:39,637 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.94068307
2015-10-17 15:40:40,045 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:41,047 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:41,269 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.667
2015-10-17 15:40:41,284 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.6210422
2015-10-17 15:40:41,379 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.898323
2015-10-17 15:40:42,036 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.13333334
2015-10-17 15:40:42,049 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:42,272 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.3984024
2015-10-17 15:40:42,665 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.9613528
2015-10-17 15:40:42,667 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.9600281
2015-10-17 15:40:42,673 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.9626008
2015-10-17 15:40:43,051 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:44,053 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:44,275 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.69067633
2015-10-17 15:40:44,287 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.62535584
2015-10-17 15:40:44,384 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.9275301
2015-10-17 15:40:45,056 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:45,075 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.13333334
2015-10-17 15:40:45,288 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.44980705
2015-10-17 15:40:45,707 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.9779973
2015-10-17 15:40:45,709 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.9766155
2015-10-17 15:40:45,711 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.97903967
2015-10-17 15:40:45,929 WARN [ResponseProcessor for block BP-1347369012-**************-1444972147527:blk_1073742514_1708] org.apache.hadoop.hdfs.DFSClient: DFSOutputStream ResponseProcessor exception  for block BP-1347369012-**************-1444972147527:blk_1073742514_1708
java.io.IOException: Bad response ERROR for block BP-1347369012-**************-1444972147527:blk_1073742514_1708 from datanode ***********:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer$ResponseProcessor.run(DFSOutputStream.java:898)
2015-10-17 15:40:45,935 WARN [DataStreamer for file /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0011/job_1445062781478_0011_1.jhist block BP-1347369012-**************-1444972147527:blk_1073742514_1708] org.apache.hadoop.hdfs.DFSClient: Error Recovery for block BP-1347369012-**************-1444972147527:blk_1073742514_1708 in pipeline **************:50010, ***********:50010: bad datanode ***********:50010
2015-10-17 15:40:46,059 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:46,097 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_1 is : 0.03805856
2015-10-17 15:40:47,062 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:47,287 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.7217595
2015-10-17 15:40:47,299 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.6520177
2015-10-17 15:40:47,396 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.9584847
2015-10-17 15:40:48,066 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:48,116 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.13333334
2015-10-17 15:40:48,317 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.44980705
2015-10-17 15:40:48,752 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 0.99431694
2015-10-17 15:40:48,752 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 0.99202824
2015-10-17 15:40:48,754 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 0.993435
2015-10-17 15:40:49,069 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:49,137 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_1 is : 0.058817852
2015-10-17 15:40:50,069 INFO [IPC Server handler 7 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000001_0 is : 1.0
2015-10-17 15:40:50,071 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:50,072 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0011_m_000001_0
2015-10-17 15:40:50,072 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:40:50,073 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0011_01_000003 taskAttempt attempt_1445062781478_0011_m_000001_0
2015-10-17 15:40:50,073 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0011_m_000001_0
2015-10-17 15:40:50,074 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:40:50,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:40:50,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0011_m_000001_0
2015-10-17 15:40:50,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:40:50,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 15:40:50,243 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000002_0 is : 1.0
2015-10-17 15:40:50,245 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0011_m_000002_0
2015-10-17 15:40:50,245 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:40:50,246 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0011_01_000004 taskAttempt attempt_1445062781478_0011_m_000002_0
2015-10-17 15:40:50,246 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0011_m_000002_0
2015-10-17 15:40:50,247 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:40:50,266 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:40:50,267 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0011_m_000002_0
2015-10-17 15:40:50,267 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:40:50,268 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 15:40:50,307 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.7490406
2015-10-17 15:40:50,307 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.66374123
2015-10-17 15:40:50,419 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 0.9850503
2015-10-17 15:40:50,543 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000004_0 is : 1.0
2015-10-17 15:40:50,545 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0011_m_000004_0
2015-10-17 15:40:50,546 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:40:50,546 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0011_01_000006 taskAttempt attempt_1445062781478_0011_m_000004_0
2015-10-17 15:40:50,547 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0011_m_000004_0
2015-10-17 15:40:50,547 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:40:50,566 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:40:50,567 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0011_m_000004_0
2015-10-17 15:40:50,567 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:40:50,568 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 15:40:50,646 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.66374123
2015-10-17 15:40:50,886 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 15:40:50,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0011_01_000003
2015-10-17 15:40:50,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0011_01_000004
2015-10-17 15:40:50,887 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0011_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:40:50,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 15:40:50,887 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0011_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:40:51,072 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 15:40:51,154 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.13333334
2015-10-17 15:40:51,335 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.44980705
2015-10-17 15:40:51,890 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0011_01_000006
2015-10-17 15:40:51,890 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 15:40:51,890 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0011_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:40:52,076 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:40:52,174 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_1 is : 0.0833192
2015-10-17 15:40:52,211 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000008_0 is : 1.0
2015-10-17 15:40:52,213 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0011_m_000008_0
2015-10-17 15:40:52,214 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:40:52,214 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0011_01_000010 taskAttempt attempt_1445062781478_0011_m_000008_0
2015-10-17 15:40:52,214 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0011_m_000008_0
2015-10-17 15:40:52,215 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:40:52,232 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:40:52,232 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0011_m_000008_0
2015-10-17 15:40:52,233 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:40:52,233 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 15:40:52,891 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 15:40:53,078 INFO [IPC Server handler 21 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 15:40:53,321 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.667
2015-10-17 15:40:53,322 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.7801125
2015-10-17 15:40:53,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0011_01_000010
2015-10-17 15:40:53,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 15:40:53,895 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0011_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:40:54,081 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:40:54,193 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.16666667
2015-10-17 15:40:54,359 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.44980705
2015-10-17 15:40:55,084 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:40:55,209 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_1 is : 0.09710924
2015-10-17 15:40:56,087 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:40:56,340 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.667
2015-10-17 15:40:56,344 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.8135109
2015-10-17 15:40:57,090 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:40:57,226 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.23333333
2015-10-17 15:40:57,376 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.49023053
2015-10-17 15:40:58,092 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:40:58,238 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_1 is : 0.10681946
2015-10-17 15:40:59,094 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:40:59,355 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.84389484
2015-10-17 15:40:59,355 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.667
2015-10-17 15:41:00,095 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:41:00,255 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.26666668
2015-10-17 15:41:00,389 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.53543663
2015-10-17 15:41:01,096 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:41:01,265 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_1 is : 0.10681946
2015-10-17 15:41:02,097 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:41:02,373 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.8778343
2015-10-17 15:41:02,373 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.6802496
2015-10-17 15:41:03,098 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:41:03,282 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.26666668
2015-10-17 15:41:03,398 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.53543663
2015-10-17 15:41:04,099 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:41:04,289 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_1 is : 0.10681946
2015-10-17 15:41:05,100 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:41:05,388 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.90833926
2015-10-17 15:41:05,389 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.7252275
2015-10-17 15:41:06,101 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:41:06,300 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.26666668
2015-10-17 15:41:06,403 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.53543663
2015-10-17 15:41:07,103 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:41:07,307 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_1 is : 0.14227784
2015-10-17 15:41:08,104 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:41:08,401 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.9420209
2015-10-17 15:41:08,402 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.7600696
2015-10-17 15:41:09,105 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:41:09,331 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.26666668
2015-10-17 15:41:09,413 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.53543663
2015-10-17 15:41:10,106 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:41:10,325 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_1 is : 0.18310882
2015-10-17 15:41:11,107 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:41:11,424 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 0.99267614
2015-10-17 15:41:11,424 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.8003783
2015-10-17 15:41:12,108 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:41:12,302 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000007_0 is : 1.0
2015-10-17 15:41:12,303 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0011_m_000007_0
2015-10-17 15:41:12,304 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:41:12,304 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0011_01_000009 taskAttempt attempt_1445062781478_0011_m_000007_0
2015-10-17 15:41:12,304 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0011_m_000007_0
2015-10-17 15:41:12,304 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:41:12,316 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:41:12,317 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0011_m_000007_0
2015-10-17 15:41:12,317 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0011_m_000007_1
2015-10-17 15:41:12,317 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:41:12,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 15:41:12,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000007_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 15:41:12,318 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0011_01_000014 taskAttempt attempt_1445062781478_0011_m_000007_1
2015-10-17 15:41:12,318 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0011_m_000007_1
2015-10-17 15:41:12,319 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:41:12,330 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000007_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 15:41:12,330 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 15:41:12,339 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out1/_temporary/1/_temporary/attempt_1445062781478_0011_m_000007_1
2015-10-17 15:41:12,341 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000007_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 15:41:12,347 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.26666668
2015-10-17 15:41:12,362 INFO [Socket Reader #1 for port 47468] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 47468: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 15:41:12,423 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.5677025
2015-10-17 15:41:12,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 15:41:13,109 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 15:41:13,922 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0011_01_000014
2015-10-17 15:41:13,922 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0011_01_000009
2015-10-17 15:41:13,922 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0011_m_000007_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:41:13,922 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 15:41:13,922 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0011_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:41:14,110 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:41:14,434 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.83441466
2015-10-17 15:41:15,111 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:41:15,376 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.3
2015-10-17 15:41:15,444 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.61973244
2015-10-17 15:41:16,112 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:41:17,113 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:41:17,456 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.86624163
2015-10-17 15:41:18,114 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:41:18,399 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.3
2015-10-17 15:41:18,450 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.6210422
2015-10-17 15:41:19,115 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:41:20,116 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:41:20,471 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.9148483
2015-10-17 15:41:21,117 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:41:21,422 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.3
2015-10-17 15:41:21,449 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.6210422
2015-10-17 15:41:22,118 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:41:23,121 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:41:23,482 INFO [IPC Server handler 4 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 0.95964396
2015-10-17 15:41:24,123 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:41:24,450 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_1 is : 0.6210422
2015-10-17 15:41:24,458 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.3
2015-10-17 15:41:25,125 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:41:25,699 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_m_000006_0 is : 1.0
2015-10-17 15:41:25,700 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0011_m_000006_0
2015-10-17 15:41:25,700 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:41:25,701 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0011_01_000008 taskAttempt attempt_1445062781478_0011_m_000006_0
2015-10-17 15:41:25,701 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0011_m_000006_0
2015-10-17 15:41:25,702 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:41:25,713 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:41:25,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0011_m_000006_0
2015-10-17 15:41:25,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0011_m_000006_1
2015-10-17 15:41:25,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:41:25,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 15:41:25,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000006_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 15:41:25,715 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0011_01_000012 taskAttempt attempt_1445062781478_0011_m_000006_1
2015-10-17 15:41:25,715 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0011_m_000006_1
2015-10-17 15:41:25,715 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:41:25,725 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000006_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 15:41:25,725 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 15:41:25,730 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out1/_temporary/1/_temporary/attempt_1445062781478_0011_m_000006_1
2015-10-17 15:41:25,731 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_m_000006_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 15:41:25,798 INFO [Socket Reader #1 for port 47468] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 47468: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 15:41:25,936 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 15:41:26,126 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 15:41:26,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0011_01_000008
2015-10-17 15:41:26,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0011_01_000012
2015-10-17 15:41:26,939 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0011_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:41:26,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 15:41:26,939 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0011_m_000006_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 15:41:27,129 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0011_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 15:41:27,356 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.3
2015-10-17 15:41:27,414 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.3
2015-10-17 15:41:27,499 INFO [IPC Server handler 23 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.6666667
2015-10-17 15:41:30,529 INFO [IPC Server handler 15 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.6702358
2015-10-17 15:41:33,550 INFO [IPC Server handler 16 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.6876459
2015-10-17 15:41:36,582 INFO [IPC Server handler 5 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.6954033
2015-10-17 15:41:39,613 INFO [IPC Server handler 0 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.6954033
2015-10-17 15:41:54,650 INFO [IPC Server handler 18 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.7208806
2015-10-17 15:41:57,679 INFO [IPC Server handler 10 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.7352575
2015-10-17 15:42:00,707 INFO [IPC Server handler 11 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.7476551
2015-10-17 15:42:03,731 INFO [IPC Server handler 28 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.7563094
2015-10-17 15:42:06,762 INFO [IPC Server handler 26 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.77515256
2015-10-17 15:42:09,788 INFO [IPC Server handler 19 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.7955633
2015-10-17 15:42:12,818 INFO [IPC Server handler 12 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.8061108
2015-10-17 15:42:15,847 INFO [IPC Server handler 24 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.82682127
2015-10-17 15:42:18,866 INFO [IPC Server handler 17 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.84576535
2015-10-17 15:42:21,893 INFO [IPC Server handler 14 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.865559
2015-10-17 15:42:24,913 INFO [IPC Server handler 25 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.8861815
2015-10-17 15:42:27,952 INFO [IPC Server handler 9 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.89767563
2015-10-17 15:42:30,980 INFO [IPC Server handler 27 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.91010404
2015-10-17 15:42:33,997 INFO [IPC Server handler 3 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.930416
2015-10-17 15:42:37,027 INFO [IPC Server handler 29 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.9498547
2015-10-17 15:42:40,045 INFO [IPC Server handler 2 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.97030914
2015-10-17 15:42:43,072 INFO [IPC Server handler 8 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.9893917
2015-10-17 15:42:46,108 INFO [IPC Server handler 6 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 0.9993825
2015-10-17 15:42:46,367 INFO [IPC Server handler 20 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445062781478_0011_r_000000_0
2015-10-17 15:42:46,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-17 15:42:46,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445062781478_0011_r_000000_0 given a go for committing the task output.
2015-10-17 15:42:46,368 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445062781478_0011_r_000000_0
2015-10-17 15:42:46,368 INFO [IPC Server handler 22 on 47468] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445062781478_0011_r_000000_0:true
2015-10-17 15:42:46,405 INFO [IPC Server handler 1 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0011_r_000000_0 is : 1.0
2015-10-17 15:42:46,406 INFO [IPC Server handler 13 on 47468] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0011_r_000000_0
2015-10-17 15:42:46,406 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 15:42:46,407 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0011_01_000013 taskAttempt attempt_1445062781478_0011_r_000000_0
2015-10-17 15:42:46,407 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0011_r_000000_0
2015-10-17 15:42:46,407 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 15:42:46,420 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0011_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 15:42:46,421 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0011_r_000000_0
2015-10-17 15:42:46,421 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0011_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 15:42:46,421 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 15:42:46,422 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0011Job Transitioned from RUNNING to COMMITTING
2015-10-17 15:42:46,422 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-17 15:42:46,467 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-17 15:42:46,468 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0011Job Transitioned from COMMITTING to SUCCEEDED
2015-10-17 15:42:46,469 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-17 15:42:46,469 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-17 15:42:46,469 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-17 15:42:46,469 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-17 15:42:46,470 INFO [Thread-101] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-17 15:42:46,470 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-17 15:42:46,470 INFO [Thread-101] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-17 15:42:46,545 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0011/job_1445062781478_0011_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0011-1445067474313-msrabi-pagerank-1445067766465-10-1-SUCCEEDED-default-1445067479468.jhist_tmp
2015-10-17 15:42:46,608 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0011-1445067474313-msrabi-pagerank-1445067766465-10-1-SUCCEEDED-default-1445067479468.jhist_tmp
2015-10-17 15:42:46,611 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0011/job_1445062781478_0011_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0011_conf.xml_tmp
2015-10-17 15:42:46,676 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0011_conf.xml_tmp
2015-10-17 15:42:46,680 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0011.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0011.summary
2015-10-17 15:42:46,683 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0011_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0011_conf.xml
2015-10-17 15:42:46,685 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0011-1445067474313-msrabi-pagerank-1445067766465-10-1-SUCCEEDED-default-1445067479468.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445062781478_0011-1445067474313-msrabi-pagerank-1445067766465-10-1-SUCCEEDED-default-1445067479468.jhist
2015-10-17 15:42:46,686 INFO [Thread-101] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-17 15:42:46,689 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-17 15:42:46,690 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-41.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445062781478_0011
2015-10-17 15:42:46,695 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-17 15:42:47,696 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 15:42:47,697 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0011
2015-10-17 15:42:47,706 INFO [Thread-101] org.apache.hadoop.ipc.Server: Stopping server on 47468
2015-10-17 15:42:47,707 INFO [IPC Server listener on 47468] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 47468
2015-10-17 15:42:47,708 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-17 15:42:47,708 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
