"""
Test script to demonstrate the Well Log App with sample data
"""

import pandas as pd
import numpy as np
from datetime import datetime

def create_sample_well_log_data():
    """Create sample well log data for testing the app"""
    
    # Generate depth array
    depth_start = 1500.0
    depth_end = 1600.0
    depth_step = 0.5
    depths = np.arange(depth_start, depth_end, depth_step)
    
    # Generate realistic well log curves
    np.random.seed(42)  # For reproducible results
    
    data = {
        'DEPTH_MD': depths,
        'GR': [],
        'RDEP': [],
        'RHOB': [],
        'NPHI': [],
        'CALI': [],
        'DTC': [],
        'PEF': []
    }
    
    for i, depth in enumerate(depths):
        # Gamma Ray (API) - typical range 0-200
        base_gr = 50 + 30 * np.sin((depth - depth_start) * 0.02) + np.random.normal(0, 10)
        data['GR'].append(max(0, min(200, base_gr)))
        
        # Deep Resistivity (ohm.m) - log-normal distribution
        base_res = np.exp(2 + 0.5 * np.sin((depth - depth_start) * 0.01) + np.random.normal(0, 0.3))
        data['RDEP'].append(max(0.1, base_res))
        
        # Bulk Density (g/cc) - typical range 1.8-2.8
        base_rhob = 2.3 + 0.2 * np.sin((depth - depth_start) * 0.015) + np.random.normal(0, 0.05)
        data['RHOB'].append(max(1.5, min(3.0, base_rhob)))
        
        # Neutron Porosity (v/v) - typical range 0-0.5
        base_nphi = 0.15 + 0.1 * np.sin((depth - depth_start) * 0.025) + np.random.normal(0, 0.02)
        data['NPHI'].append(max(0, min(0.5, base_nphi)))
        
        # Caliper (inches) - typical range 6-12
        base_cali = 8.5 + 1.0 * np.sin((depth - depth_start) * 0.03) + np.random.normal(0, 0.3)
        data['CALI'].append(max(6, min(15, base_cali)))
        
        # Delta Time Compressional (us/ft) - typical range 40-140
        base_dtc = 70 + 20 * np.sin((depth - depth_start) * 0.02) + np.random.normal(0, 5)
        data['DTC'].append(max(40, min(140, base_dtc)))
        
        # Photoelectric Factor - typical range 1-6
        base_pef = 2.8 + 0.5 * np.sin((depth - depth_start) * 0.018) + np.random.normal(0, 0.2)
        data['PEF'].append(max(1, min(6, base_pef)))
    
    # Create DataFrame
    df = pd.DataFrame(data)
    
    return df

def save_sample_data():
    """Generate and save sample well log data"""
    df = create_sample_well_log_data()
    
    filename = f"sample_well_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    df.to_csv(filename, index=False)
    
    print(f"✅ Sample well log data saved as: {filename}")
    print(f"📊 Data shape: {df.shape}")
    print(f"📏 Depth range: {df['DEPTH_MD'].min():.1f} - {df['DEPTH_MD'].max():.1f} m")
    print("\n📋 Column summary:")
    print(df.describe())
    
    return filename

if __name__ == "__main__":
    # Generate sample data
    filename = save_sample_data()
    
    print(f"\n🚀 To test the Well Log App:")
    print(f"1. Run: streamlit run well_log_app.py")
    print(f"2. Upload the generated file: {filename}")
    print(f"3. Explore the interactive plots!")
