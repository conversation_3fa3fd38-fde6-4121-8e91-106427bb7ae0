{% if offline %}
    {%- if inline -%}
        <style>
        {% if theme is not none %}
            {% if theme.value == 'flatly' %}
                {% include 'wrapper/assets/flatly.bootstrap.min.css' %}
            {% elif theme.value == 'united' %}
                {% include 'wrapper/assets/united.bootstrap.min.css' %}
            {% elif theme.value == 'simplex' %}
                {% include 'wrapper/assets/simplex.bootstrap.min.css' %}
            {% elif theme.value == 'cosmo' %}
                {% include 'wrapper/assets/cosmo.bootstrap.min.css' %}
            {% endif %}
        {% else %}
            {% include 'wrapper/assets/bootstrap.min.css' %}
        {% endif %}
        </style>
    {% else %}
        {% if theme is not none %}
            {% if theme.value == 'flatly' %}
                <link href="{{ assets_prefix }}/css/flatly.bootstrap.min.css" rel="stylesheet" />
            {% elif theme.value == 'united' %}
                <link href="{{ assets_prefix }}/css/united.bootstrap.min.css" rel="stylesheet" />
            {% elif theme.value == 'simplex' %}
                <link href="{{ assets_prefix }}/css/simplex.bootstrap.min.css" rel="stylesheet" />
            {% elif theme.value == 'cosmo' %}
                <link href="{{ assets_prefix }}/css/cosmo.bootstrap.min.css" rel="stylesheet" />
            {% endif %}
        {% else %}
            <link rel="stylesheet" href="{{ assets_prefix }}/css/bootstrap.min.css" />
        {% endif %}
    {% endif %}
{% else %}
    {% if theme is not none %}
        {% if theme.value == 'flatly' %}
            <link href="https://cdn.jsdelivr.net/npm/bootswatch@5.3.3/dist/flatly/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous" />
        {% elif theme.value == 'united' %}
            <link href="https://cdn.jsdelivr.net/npm/bootswatch@5.3.3/dist/united/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous" />
        {% elif theme.value == 'simplex' %}
            <link href="https://cdn.jsdelivr.net/npm/bootswatch@5.3.3/dist/simplex/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous" />
        {% elif theme.value == 'cosmo' %}
            <link href="https://cdn.jsdelivr.net/npm/bootswatch@5.3.3/dist/cosmo/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous" />
        {% endif %}
    {% else %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous" />
    {% endif %}
{% endif %}

{%- if inline -%}
    <style>{% include 'wrapper/assets/style.css' %}</style>
{% else %}
    <link rel="stylesheet" href="{{ assets_prefix }}/css/style.css" />
{% endif %}
