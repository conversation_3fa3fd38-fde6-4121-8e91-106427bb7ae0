2015-10-17 21:49:29,970 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 21:49:30,173 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 21:49:30,173 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 21:49:30,251 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 21:49:30,251 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445087491445_0005, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@6d9532fe)
2015-10-17 21:49:30,626 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 21:49:31,454 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445087491445_0005
2015-10-17 21:49:32,907 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 21:49:36,485 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 21:49:37,017 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@49a29f92
2015-10-17 21:49:46,454 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:805306368+134217728
2015-10-17 21:49:46,642 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 21:49:46,642 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 21:49:46,642 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 21:49:46,642 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 21:49:46,642 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 21:49:46,689 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 21:50:08,642 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:50:08,642 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34177122; bufvoid = 104857600
2015-10-17 21:50:08,642 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13787160(55148640); length = 12427237/6553600
2015-10-17 21:50:08,642 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44662873 kvi 11165712(44662848)
2015-10-17 21:50:43,143 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 21:50:43,472 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44662873 kv 11165712(44662848) kvi 8544288(34177152)
2015-10-17 21:50:54,956 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:50:54,956 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44662873; bufend = 78836823; bufvoid = 104857600
2015-10-17 21:50:54,956 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11165712(44662848); kvend = 24952084(99808336); length = 12428029/6553600
2015-10-17 21:50:54,956 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89322571 kvi 22330636(89322544)
