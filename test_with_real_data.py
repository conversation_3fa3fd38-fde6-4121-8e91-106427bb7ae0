"""
Test the Well Log App with real ONGC data
"""

import pandas as pd
import os

def prepare_real_data():
    """Prepare real well log data for the app"""
    
    # Check available data files
    data_dir = "litho_data"
    if not os.path.exists(data_dir):
        print("❌ litho_data directory not found")
        return None
    
    # Get first available CSV file
    csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
    if not csv_files:
        print("❌ No CSV files found in litho_data directory")
        return None
    
    # Use the first file
    input_file = os.path.join(data_dir, csv_files[0])
    print(f"📁 Using data file: {input_file}")
    
    # Read the data
    df = pd.read_csv(input_file)
    print(f"📊 Original data shape: {df.shape}")
    print(f"📋 Columns: {list(df.columns)}")
    
    # Check if required columns exist
    required_cols = ['DEPTH_MD', 'GR', 'RDEP', 'RHOB', 'NPHI', 'CALI', 'DTC', 'PEF']
    missing_cols = [col for col in required_cols if col not in df.columns]
    
    if missing_cols:
        print(f"❌ Missing required columns: {missing_cols}")
        return None
    
    # Select only required columns
    df_clean = df[required_cols].copy()
    
    # Basic data info
    print(f"\n📈 Data Summary:")
    print(f"Depth range: {df_clean['DEPTH_MD'].min():.1f} - {df_clean['DEPTH_MD'].max():.1f} m")
    print(f"Total points: {len(df_clean)}")
    
    # Check for missing values
    print(f"\n🔍 Missing Values:")
    for col in required_cols:
        missing_count = df_clean[col].isna().sum()
        missing_pct = (missing_count / len(df_clean)) * 100
        print(f"{col}: {missing_count} ({missing_pct:.1f}%)")
    
    # Save cleaned data
    output_file = f"cleaned_{csv_files[0]}"
    df_clean.to_csv(output_file, index=False)
    print(f"\n✅ Cleaned data saved as: {output_file}")
    
    return output_file

def main():
    """Main function"""
    print("🛢️ ONGC Well Log Data Preparation")
    print("=" * 50)
    
    # Prepare the data
    output_file = prepare_real_data()
    
    if output_file:
        print(f"\n🚀 To test with real data:")
        print(f"1. Run: streamlit run well_log_app.py")
        print(f"2. Upload the cleaned file: {output_file}")
        print(f"3. Explore your real well log data!")
        print(f"\n💡 The app will automatically handle missing values and data cleaning.")
    else:
        print(f"\n❌ Could not prepare data. Please check your data files.")

if __name__ == "__main__":
    main()
