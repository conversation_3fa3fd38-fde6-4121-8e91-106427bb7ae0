"""
Test script to demonstrate intelligent data handling capabilities
with various CSV formats including hidden_test.csv
"""

import pandas as pd
import sys
import os

# Add the current directory to Python path
sys.path.append('.')

# Import the intelligent data processing functions
try:
    from well_log_app import (
        smart_load_csv, 
        validate_and_process_data,
        detect_separator,
        map_columns_intelligently
    )
    print("✅ Successfully imported intelligent data processing functions")
except ImportError as e:
    print(f"❌ Failed to import functions: {e}")
    sys.exit(1)

def test_hidden_test_csv():
    """Test with the hidden_test.csv file"""
    print("\n🧪 Testing with hidden_test.csv")
    print("=" * 50)
    
    file_path = "../Dataset/CSV Data/hidden_test.csv"
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        # Read file content for separator detection
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test separator detection
        separator = detect_separator(content)
        print(f"📄 Detected separator: '{separator}' ({'Semicolon' if separator == ';' else 'Other'})")
        
        # Load the CSV
        df_raw = pd.read_csv(file_path, sep=separator)
        print(f"📊 Raw data loaded: {df_raw.shape}")
        print(f"📋 Original columns: {list(df_raw.columns)}")
        
        # Test intelligent column mapping
        df_mapped, mapped_columns, unmapped_columns = map_columns_intelligently(df_raw)
        print(f"\n🔄 Column Mapping Results:")
        print(f"   Mapped: {len(mapped_columns)} columns")
        print(f"   Unmapped: {len(unmapped_columns)} columns")
        
        for orig, standard in mapped_columns.items():
            print(f"   📍 {orig} → {standard}")
        
        # Test full processing
        df_processed, processing_info = validate_and_process_data(df_raw)
        print(f"\n🔧 Processing Results:")
        print(f"   Final shape: {df_processed.shape}")
        print(f"   Synthetic columns: {len(processing_info['synthetic_columns'])}")
        print(f"   Optional columns: {processing_info['optional_columns']}")
        
        # Show sample of processed data
        print(f"\n📋 Sample of processed data:")
        required_cols = ['DEPTH_MD', 'GR', 'RDEP', 'RHOB', 'NPHI']
        available_cols = [col for col in required_cols if col in df_processed.columns]
        print(df_processed[available_cols].head())
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing hidden_test.csv: {e}")
        return False

def test_volve_wells_csv():
    """Test with VolveWells.csv (different format)"""
    print("\n🧪 Testing with VolveWells.csv")
    print("=" * 50)
    
    file_path = "../Dataset/CSV Data/VolveWells.csv"
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        # Load and process
        df_raw = pd.read_csv(file_path)
        print(f"📊 Raw data loaded: {df_raw.shape}")
        print(f"📋 Original columns: {list(df_raw.columns)}")
        
        # Test processing
        df_processed, processing_info = validate_and_process_data(df_raw)
        print(f"\n🔧 Processing Results:")
        print(f"   Final shape: {df_processed.shape}")
        print(f"   Mapped columns: {len(processing_info['mapped_columns'])}")
        print(f"   Synthetic columns: {len(processing_info['synthetic_columns'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing VolveWells.csv: {e}")
        return False

def test_force2020_csv():
    """Test with force2020_data_unsupervised_learning.csv"""
    print("\n🧪 Testing with force2020_data_unsupervised_learning.csv")
    print("=" * 50)
    
    file_path = "../Dataset/CSV Data/force2020_data_unsupervised_learning.csv"
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        # Load and process
        df_raw = pd.read_csv(file_path)
        print(f"📊 Raw data loaded: {df_raw.shape}")
        print(f"📋 Original columns: {list(df_raw.columns)}")
        
        # Test processing
        df_processed, processing_info = validate_and_process_data(df_raw)
        print(f"\n🔧 Processing Results:")
        print(f"   Final shape: {df_processed.shape}")
        print(f"   Mapped columns: {len(processing_info['mapped_columns'])}")
        print(f"   Synthetic columns: {len(processing_info['synthetic_columns'])}")
        
        # Show mapping details
        print(f"\n🔄 Column Mappings:")
        for orig, standard in processing_info['mapped_columns'].items():
            print(f"   📍 {orig} → {standard}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing force2020 data: {e}")
        return False

def create_test_files():
    """Create test files with different formats for demonstration"""
    print("\n🧪 Creating Test Files with Different Formats")
    print("=" * 50)
    
    # Test file 1: Tab-separated with different column names
    test_data_1 = {
        'MD': [1500, 1501, 1502, 1503, 1504],
        'GAMMA': [45, 50, 55, 48, 52],
        'RT': [10, 15, 8, 12, 18],
        'DENSITY': [2.3, 2.4, 2.2, 2.35, 2.45],
        'NEUTRON': [0.15, 0.18, 0.12, 0.16, 0.20],
        'CAL': [8.5, 8.7, 8.3, 8.6, 8.8],
        'AC': [65, 68, 62, 66, 70],
        'PE': [2.8, 3.1, 2.6, 2.9, 3.2]
    }
    
    df1 = pd.DataFrame(test_data_1)
    df1.to_csv('test_tab_separated.csv', sep='\t', index=False)
    print("✅ Created test_tab_separated.csv (tab-separated)")
    
    # Test file 2: Pipe-separated with missing columns
    test_data_2 = {
        'DEPTH': [2000, 2001, 2002, 2003, 2004],
        'GR': [35, 40, 45, 38, 42],
        'RDEEP': [20, 25, 18, 22, 28],
        'DEN': [2.1, 2.2, 2.0, 2.15, 2.25]
        # Missing NPHI, CALI, DTC, PEF - will be generated synthetically
    }
    
    df2 = pd.DataFrame(test_data_2)
    df2.to_csv('test_pipe_separated.csv', sep='|', index=False)
    print("✅ Created test_pipe_separated.csv (pipe-separated, missing columns)")
    
    return ['test_tab_separated.csv', 'test_pipe_separated.csv']

def test_created_files(test_files):
    """Test the created files with different formats"""
    print("\n🧪 Testing Created Files")
    print("=" * 30)
    
    for filename in test_files:
        if os.path.exists(filename):
            print(f"\n📁 Testing {filename}")
            
            try:
                # Read file content for separator detection
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                separator = detect_separator(content)
                print(f"   Detected separator: '{separator}'")
                
                # Load and process
                df_raw = pd.read_csv(filename, sep=separator)
                df_processed, processing_info = validate_and_process_data(df_raw)
                
                print(f"   Original: {df_raw.shape} → Processed: {df_processed.shape}")
                print(f"   Mapped: {len(processing_info['mapped_columns'])}, Synthetic: {len(processing_info['synthetic_columns'])}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")

def main():
    """Main test function"""
    print("🧠 INTELLIGENT DATA HANDLING TEST")
    print("=" * 60)
    
    # Test with real datasets
    tests = [
        ("Hidden Test CSV", test_hidden_test_csv),
        ("Volve Wells CSV", test_volve_wells_csv),
        ("Force2020 CSV", test_force2020_csv)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed: {e}")
            results[test_name] = False
    
    # Create and test custom files
    test_files = create_test_files()
    test_created_files(test_files)
    
    # Summary
    print(f"\n📊 TEST RESULTS SUMMARY:")
    print("=" * 40)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"🚀 The intelligent data handling system is working perfectly!")
        print(f"📱 You can now upload ANY CSV format to the Streamlit app!")
    else:
        print(f"\n⚠️ Some tests failed - check the errors above")
    
    # Cleanup
    for filename in test_files:
        if os.path.exists(filename):
            os.remove(filename)
            print(f"🧹 Cleaned up {filename}")

if __name__ == "__main__":
    main()
