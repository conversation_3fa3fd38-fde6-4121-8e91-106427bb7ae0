# 📚 Custom Dataset Guide - Add Your Own Articles

This guide shows you how to add your own articles and documents to the ONGC Knowledge Management System for personalized semantic search.

## 🚀 Quick Start

### Method 1: Using the Web Interface (Easiest)
1. Run the Streamlit app: `streamlit run streamlit_app.py`
2. Go to the **"➕ Manage Dataset"** tab
3. Add articles directly through the web form
4. Upload text files or CSV files with your data

### Method 2: Using the Simple CLI Tool
```bash
# Interactive mode - easiest for single articles
python add_articles.py

# Add from a text file
python add_articles.py add "my_article.txt" "My Article Title" "Technical"

# Add all text files from a directory
python add_articles.py dir "my_documents_folder" ".txt" "Research"

# Show current articles
python add_articles.py list
```

### Method 3: Using the Advanced Dataset Manager
```bash
# Full-featured interactive manager
python custom_dataset_manager.py
```

## 📝 Adding Individual Articles

### Web Interface
1. Open the Streamlit app
2. Go to "➕ Manage Dataset" tab
3. Fill in the form:
   - **Title**: Your article title
   - **Content**: Full article text
   - **Category**: e.g., "Technical", "Research", "Procedures"
   - **Author**: Your name
   - **Tags**: Comma-separated keywords
4. Click "➕ Add Article"

### CLI (Interactive)
```bash
python add_articles.py
# Choose option 1, then follow prompts
```

## 📁 Adding Multiple Articles from Files

### From Text Files
If you have articles in separate `.txt` files:

```bash
# Single file
python add_articles.py add "article1.txt" "Drilling Procedures" "Technical"

# All files in a directory
python add_articles.py dir "my_articles_folder" ".txt" "Technical"
```

### From CSV File
If you have articles in a CSV file with columns like `title`, `content`, `category`:

1. Use the web interface "📁 Upload Files" section
2. Upload your CSV file
3. Map the columns (title, content, category, author)
4. Click "📊 Import CSV Data"

**CSV Format Example:**
```csv
title,content,category,author,tags
"Drilling Best Practices","Content about drilling...","Technical","John Doe","drilling,safety"
"Reservoir Analysis","Content about reservoirs...","Engineering","Jane Smith","reservoir,analysis"
```

## 🔧 Advanced Usage

### Using the Dataset Manager Directly
```python
from custom_dataset_manager import CustomDatasetManager

# Initialize manager
manager = CustomDatasetManager()

# Add an article
manager.add_article(
    title="My Technical Article",
    content="Full content of the article...",
    category="Technical",
    author="Your Name",
    tags=["keyword1", "keyword2"]
)

# Save changes
manager.save_articles()
```

### Batch Import from Directory
```python
# Add all .txt files from a directory
manager.add_articles_from_directory(
    directory="path/to/your/documents",
    file_extension=".txt",
    category="Imported Documents",
    author="Your Name"
)
```

### Import from CSV
```python
# Import from CSV file
manager.add_articles_from_csv(
    csv_file="your_articles.csv",
    title_col="title",
    content_col="content",
    category_col="category",
    author_col="author"
)
```

## 📊 Managing Your Dataset

### View Current Articles
```bash
# Show recent articles
python add_articles.py list

# Or use the web interface "📚 Browse Articles" tab
```

### Remove Articles
- Use the web interface "➕ Manage Dataset" tab
- Click the "🗑️ Remove" button next to any article

### Clear All Articles
- Web interface: "➕ Manage Dataset" → "🗑️ Clear All Articles"
- CLI: Use the dataset manager and choose option 0

## 🔍 Using Your Custom Data

Once you've added your articles:

1. **Restart the search engine** to load new data:
   ```bash
   # Web interface
   streamlit run streamlit_app.py
   
   # CLI interface
   python cli_app.py search --interactive
   ```

2. **Search your content**:
   - The system will now search through YOUR articles
   - Use natural language queries related to your content
   - Get semantic matches based on meaning, not just keywords

## 💡 Tips for Better Results

### Writing Good Articles
- **Clear Titles**: Use descriptive, specific titles
- **Rich Content**: Include detailed explanations and context
- **Good Categories**: Use consistent category names
- **Relevant Tags**: Add keywords that users might search for

### Organizing Your Data
- **Consistent Categories**: Use the same category names for similar content
- **Meaningful Tags**: Add tags that represent key concepts
- **Author Information**: Track who contributed each article
- **Source Tracking**: The system automatically tracks where articles came from

### Search Optimization
- **Use Natural Language**: Search like you're asking a question
- **Try Different Phrasings**: The AI understands synonyms and related concepts
- **Adjust Similarity Threshold**: Lower threshold = more results, higher = more precise

## 🔄 Data Format

Your articles are stored in JSON format in `data/technical_articles.json`:

```json
[
  {
    "id": 1,
    "title": "Your Article Title",
    "content": "Full article content...",
    "category": "Technical",
    "tags": ["tag1", "tag2"],
    "author": "Your Name",
    "date": "2024-01-01",
    "source": "manual",
    "uuid": "unique-identifier"
  }
]
```

## 🛠️ Troubleshooting

### Articles Not Appearing in Search
1. **Restart the application** after adding articles
2. **Check the data file**: Ensure `data/technical_articles.json` contains your articles
3. **Regenerate embeddings**: Delete `embeddings/article_embeddings.npy` and restart

### Search Not Finding Your Content
1. **Try different keywords**: Use terms that appear in your articles
2. **Lower similarity threshold**: In the web interface sidebar
3. **Check article content**: Ensure your articles have substantial content

### File Upload Issues
1. **Check file encoding**: Use UTF-8 encoding for text files
2. **File size limits**: Very large files might need to be split
3. **CSV format**: Ensure proper column headers and data format

## 📈 Next Steps

After adding your articles:
1. **Test searches** with queries relevant to your content
2. **Add more articles** as you create or find relevant content
3. **Organize categories** to match your domain
4. **Share with team** - they can add their articles too!

---

## 🎯 Example Workflow

Here's a complete example of adding your own technical documents:

```bash
# 1. Add a single article interactively
python add_articles.py
# Choose option 1, enter your article details

# 2. Add multiple files from a folder
python add_articles.py dir "my_technical_docs" ".txt" "Technical"

# 3. Check what was added
python add_articles.py list

# 4. Start searching your content
streamlit run streamlit_app.py
# Go to search and try queries related to your content
```

Your ONGC Knowledge Management System is now personalized with YOUR content! 🎉
