# ONGC Knowledge Management System Setup Script
Write-Host "🔍 ONGC Knowledge Management System Setup" -ForegroundColor Blue
Write-Host "==========================================" -ForegroundColor Blue

# Check Python installation
Write-Host "`nChecking Python installation..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python is not installed or not in PATH!" -ForegroundColor Red
    Write-Host "Please install Python 3.8 or higher from https://python.org" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Install dependencies
Write-Host "`n📦 Installing dependencies..." -ForegroundColor Yellow
python -m pip install --upgrade pip
python -m pip install -r requirements.txt

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install dependencies!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Run setup
Write-Host "`n🚀 Running setup..." -ForegroundColor Yellow
python setup.py

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Setup failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Run tests
Write-Host "`n🧪 Running tests..." -ForegroundColor Yellow
python test_search.py

if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️ Tests completed with warnings" -ForegroundColor Yellow
} else {
    Write-Host "✅ All tests passed!" -ForegroundColor Green
}

# User choice for running the application
Write-Host "`n✅ Setup complete!" -ForegroundColor Green
Write-Host "`nChoose how to run the application:" -ForegroundColor Cyan
Write-Host "1. Web Interface (Streamlit)" -ForegroundColor White
Write-Host "2. Command Line Interface" -ForegroundColor White
Write-Host "3. Exit" -ForegroundColor White

$choice = Read-Host "`nEnter your choice (1-3)"

switch ($choice) {
    "1" {
        Write-Host "Starting Streamlit web interface..." -ForegroundColor Green
        streamlit run streamlit_app.py
    }
    "2" {
        Write-Host "Starting CLI interface..." -ForegroundColor Green
        python cli_app.py search --interactive
    }
    default {
        Write-Host "Goodbye!" -ForegroundColor Blue
    }
}

Read-Host "Press Enter to exit"
