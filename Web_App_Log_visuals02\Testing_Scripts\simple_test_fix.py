"""
Simple test to verify the fix works
"""

import pandas as pd
import sys
import os

# Add the current directory to Python path
sys.path.append('.')

# Import the intelligent data processing functions
try:
    from well_log_app import validate_and_process_data, detect_separator
    print("✅ Successfully imported functions from well_log_app.py")
except ImportError as e:
    print(f"❌ Failed to import functions: {e}")
    sys.exit(1)

def test_processing_fix():
    """Test the processing fix with actual data"""
    print("\n🧪 Testing Processing Fix")
    print("=" * 40)
    
    # Try to load the demo file directly
    test_files = [
        "demo_hidden_test.csv",
        "../Dataset/CSV Data/hidden_test.csv"
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            print(f"\n📁 Testing: {file_path}")
            
            try:
                # Read file content for separator detection
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Test separator detection
                separator = detect_separator(content)
                print(f"   📄 Separator detected: '{separator}'")
                
                # Load the CSV directly
                df_raw = pd.read_csv(file_path, sep=separator)
                print(f"   📊 Raw data loaded: {df_raw.shape}")
                
                # Store original length (this is what was missing)
                original_length = len(df_raw)
                print(f"   📏 Original length: {original_length:,} rows")
                
                # Test the processing function
                df_processed, processing_info = validate_and_process_data(df_raw)
                print(f"   ✅ Processing successful: {df_processed.shape}")
                
                # Show results
                print(f"   🔄 Mapped columns: {len(processing_info['mapped_columns'])}")
                print(f"   🧪 Synthetic columns: {len(processing_info['synthetic_columns'])}")
                print(f"   📊 Final length: {len(df_processed):,} rows")
                print(f"   📉 Rows removed: {processing_info['rows_removed']}")
                
                # Check for required columns
                required_cols = ['DEPTH_MD', 'GR', 'RDEP', 'RHOB', 'NPHI', 'CALI', 'DTC', 'PEF']
                available_cols = [col for col in required_cols if col in df_processed.columns]
                print(f"   ✅ Required columns available: {len(available_cols)}/{len(required_cols)}")
                
                print(f"   🎉 {file_path} - PROCESSING SUCCESSFUL!")
                return True
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                import traceback
                traceback.print_exc()
                continue
    
    return False

def main():
    """Main test function"""
    print("🔧 SIMPLE FIX VERIFICATION")
    print("=" * 50)
    
    success = test_processing_fix()
    
    if success:
        print(f"\n🎉 FIX VERIFICATION SUCCESSFUL!")
        print(f"✅ The processing functions work correctly")
        print(f"✅ No 'original_length' error")
        print(f"🚀 The Streamlit app should now work with hidden_test.csv")
        print(f"📱 Try uploading the file at: http://localhost:8501")
    else:
        print(f"\n❌ Fix verification failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
