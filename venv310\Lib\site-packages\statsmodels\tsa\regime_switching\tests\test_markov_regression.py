"""
Tests for Markov Regression models

Author: <PERSON>: BSD-3
"""

import os
import warnings

import numpy as np
from numpy.testing import assert_allclose, assert_raises
import pandas as pd
import pytest

from statsmodels.tsa.regime_switching import (markov_switching,
                                              markov_regression)


current_path = os.path.dirname(os.path.abspath(__file__))


# See https://www.stata-press.com/data/r14/usmacro
fedfunds = [1.03, 0.99, 1.34, 1.5, 1.94, 2.36, 2.48, 2.69, 2.81, 2.93, 2.93,
            3.0, 3.23, 3.25, 1.86, 0.94, 1.32, 2.16, 2.57, 3.08, 3.58, 3.99,
            3.93, 3.7, 2.94, 2.3, 2.0, 1.73, 1.68, 2.4, 2.46, 2.61, 2.85,
            2.92, 2.97, 2.96, 3.33, 3.45, 3.46, 3.49, 3.46, 3.58, 3.97, 4.08,
            4.07, 4.17, 4.56, 4.91, 5.41, 5.56, 4.82, 3.99, 3.89, 4.17, 4.79,
            5.98, 5.94, 5.92, 6.57, 8.33, 8.98, 8.94, 8.57, 7.88, 6.7, 5.57,
            3.86, 4.56, 5.47, 4.75, 3.54, 4.3, 4.74, 5.14, 6.54, 7.82, 10.56,
            10.0, 9.32, 11.25, 12.09, 9.35, 6.3, 5.42, 6.16, 5.41, 4.83, 5.2,
            5.28, 4.87, 4.66, 5.16, 5.82, 6.51, 6.76, 7.28, 8.1, 9.58, 10.07,
            10.18, 10.95, 13.58, 15.05, 12.69, 9.84, 15.85, 16.57, 17.78,
            17.58, 13.59, 14.23, 14.51, 11.01, 9.29, 8.65, 8.8, 9.46, 9.43,
            9.69, 10.56, 11.39, 9.27, 8.48, 7.92, 7.9, 8.1, 7.83, 6.92, 6.21,
            6.27, 6.22, 6.65, 6.84, 6.92, 6.66, 7.16, 7.98, 8.47, 9.44, 9.73,
            9.08, 8.61, 8.25, 8.24, 8.16, 7.74, 6.43, 5.86, 5.64, 4.82, 4.02,
            3.77, 3.26, 3.04, 3.04, 3.0, 3.06, 2.99, 3.21, 3.94, 4.49, 5.17,
            5.81, 6.02, 5.8, 5.72, 5.36, 5.24, 5.31, 5.28, 5.28, 5.52, 5.53,
            5.51, 5.52, 5.5, 5.53, 4.86, 4.73, 4.75, 5.09, 5.31, 5.68, 6.27,
            6.52, 6.47, 5.59, 4.33, 3.5, 2.13, 1.73, 1.75, 1.74, 1.44, 1.25,
            1.25, 1.02, 1.0, 1.0, 1.01, 1.43, 1.95, 2.47, 2.94, 3.46, 3.98,
            4.46, 4.91, 5.25, 5.25, 5.26, 5.25, 5.07, 4.5, 3.18, 2.09, 1.94,
            0.51, 0.18, 0.18, 0.16, 0.12, 0.13, 0.19, 0.19, 0.19]

# See https://www.stata-press.com/data/r14/usmacro
ogap = [-0.53340107, 0.72974336, 2.93532324, 3.58194304, 4.15760183,
        4.28775644, 3.01683831, 2.64185619, 1.82473528, 2.37461162,
        2.39338565, 1.24197006, 1.1370815, -1.28657401, -4.46665335,
        -4.79258966, -3.06711817, -1.3212384, -0.54485309, 0.86588413,
        -0.2469136, -0.75004685, 0.7417022, -0.71350163, -1.5151515,
        -3.80444455, -4.02601957, -3.17873883, -2.48841596, -1.42372882,
        -0.61779928, -0.6430338, -0.73277968, -1.38330388, -1.31537247,
        -0.95626277, 0., -0.15248552, 0.93233085, 1.03888392,
        1.27174389, 0.63400578, 2.13007665, 2.44789481, 3.37605071,
        4.72771597, 6.20753956, 5.39234877, 5.0825758, 4.8605876,
        4.65116262, 3.52755141, 3.35122228, 3.09326482, 4.10191917,
        4.69641066, 4.38452244, 3.79841614, 4.38338947, 3.63766766,
        3.24129653, 1.84967709, 0.75554705, -0.02802691, -0.03673432,
        -1.90527546, -0.14918824, -0.42940569, -0.46382189, -0.97892815,
        -0.12142799, 1.37281513, 1.5143193, 2.47730422, 3.9762032,
        4.08987427, 2.62857127, 2.90107131, 0.97277576, 0.42547619,
        -1.60488391, -2.97784758, -4.98650694, -5.03382635, -4.25698328,
        -3.74993205, -2.39661908, -2.41223454, -2.66694117, -2.62232494,
        -2.29969597, -1.38809109, -0.67855304, -1.08100712, -1.82682908,
        0.92868561, 0.87040615, 1.32669306, 0.56407404, -0.13848817,
        -0.13089494, -0.58975571, -1.00534534, -3.55482054, -4.20365095,
        -2.97225475, -1.57762408, -2.77206445, -2.32418823, -4.01929235,
        -6.25393772, -6.46356869, -7.47437572, -8.06377602, -7.57157278,
        -6.14639282, -5.00167227, -3.74511886, -2.54788184, -1.64858043,
        -1.47994602, -1.44707143, -1.31824112, -1.20102882, -0.57691002,
        -0.64480144, -0.57239723, -0.93083948, -0.8392899, -1.19972074,
        -1.18918467, -0.87174636, -0.78151888, 0.10762761, -0.10596547,
        0.40488175, 0.17958413, 0.67704558, 0.99767941, 1.00495291,
        0.98304421, 0.47067845, 0.80427116, 0.45058677, -0.26300991,
        -1.84629929, -2.99437666, -2.90482664, -3.09490418, -3.32399321,
        -2.87384319, -2.47262239, -2.19618678, -1.91843009, -2.46574545,
        -2.58180451, -2.72212362, -2.17548561, -1.96046102, -1.3287729,
        -1.42521954, -1.04951096, -1.47037697, -1.87099183, -1.72912872,
        -1.76828432, -1.85885167, -0.9193368, -0.95776832, -0.62119246,
        -0.53508854, -0.04090983, 0.47511154, 0.41246772, 0.57928383,
        0.67604625, 1.1378212, 1.96481478, 2.05066752, 1.93714142,
        2.34412026, 3.16807413, 2.57455897, 3.59218717, 2.79711962,
        2.41787243, 1.19362748, 0.82524049, -0.36692095, -1.00542021,
        -0.89346135, -1.23166943, -1.56921482, -2.29188299, -2.56877398,
        -2.37549472, -1.4183135, -1.00017595, -1.03901041, -0.86736482,
        -0.63541794, -0.38296556, 0.11404825, 0.07249562, 0.30608681,
        0.27121997, 0.90333837, 0.595429, 0.08057959, 0.25154814,
        -0.27741581, -0.14053501, -0.06035376, -0.2722317, -1.5122633,
        -1.5272249, -2.5325017, -5.14671373, -6.88223982, -7.36753035,
        -7.43927145, -6.89403868, -6.8306222, -6.26507998, -5.93287086,
        -5.59370756]

# See https://www.stata-press.com/data/r14/usmacro
inf = [np.nan, np.nan, np.nan, np.nan, -0.2347243,
       0.37373397, 0.25006533, 1.04645514, 2.01665616, 2.58033299,
       3.41399837, 3.60986805, 3.46304512, 3.08529949, 3.45609665,
       3.27347994, 2.29982662, 1.91197193, 0.89083761, 0.390598,
       0.96842253, 1.47531354, 1.39343977, 1.82488036, 1.35991514,
       1.39598227, 1.50695646, 0.8690359, 1.20648873, 0.70517123,
       0.89477205, 1.30740857, 1.20212376, 1.30043352, 1.22895002,
       1.03573787, 1.36272156, 1.39236343, 1.48636675, 1.46398985,
       1.07421875, 1.26611042, 1.1639185, 1.64622331, 1.71658623,
       1.78565705, 2.41930342, 2.6897428, 3.27391338, 3.5685041,
       2.87078357, 2.56671929, 2.70717716, 2.99242783, 3.74010396,
       4.11855173, 4.47761202, 4.62397051, 4.87426901, 5.50198364,
       5.52285719, 5.83354473, 6.22577858, 6.03848171, 5.68597221,
       5.60000038, 4.81102371, 4.31496382, 4.27074528, 3.53535342,
       3.50587225, 3.22580624, 3.02948403, 3.33414626, 4.1129365,
       5.60817289, 6.83709764, 8.41692829, 9.91564655, 10.54788017,
       11.45758915, 12.04798317, 11.13530636, 9.53939915, 8.67963028,
       7.38337183, 6.34047985, 6.01503754, 5.58903217, 5.18573475,
       5.90339899, 6.79609919, 6.57417107, 6.59522104, 6.47466183,
       7.02936935, 8.02397346, 8.9289465, 9.78376389, 10.75433922,
       11.72252846, 12.64148235, 14.20953751, 14.42577076, 12.93487072,
       12.53929329, 11.26111889, 9.87392902, 10.85386753, 9.5831337,
       7.58190918, 6.90676928, 5.81573057, 4.44292784, 3.59408045,
       3.29905081, 2.52680969, 3.23384356, 4.62551022, 4.40519285,
       4.29570436, 4.1543026, 3.64175439, 3.60676312, 3.35249043,
       3.5137701, 3.1053853, 1.67858768, 1.66821122, 1.34587157,
       2.03802228, 3.69979739, 4.16317225, 4.40493536, 3.96511626,
       3.97994113, 4.1420536, 4.3066597, 4.67509222, 5.15961123,
       4.70588255, 4.62759781, 5.23231459, 4.58372736, 5.56420517,
       6.27646685, 5.25958157, 4.84686804, 3.85226536, 2.96485686,
       2.89388347, 3.07301927, 3.07467055, 3.12198234, 3.17306924,
       3.12524581, 2.8174715, 2.76977897, 2.53936958, 2.38237333,
       2.85493255, 2.60332823, 2.84049082, 3.09557867, 2.66420412,
       2.62607908, 2.78390908, 2.8270874, 2.8999064, 3.23162007,
       2.94453382, 2.30179024, 2.22504783, 1.89075232, 1.48277605,
       1.58312511, 1.59639311, 1.5253576, 1.68703699, 2.11280179,
       2.34625125, 2.61982656, 3.25799918, 3.29342604, 3.46889949,
       3.44350553, 3.40975904, 3.32491398, 2.67803454, 1.87507534,
       1.23194993, 1.31765401, 1.57628381, 2.25352097, 2.97640777,
       2.00593972, 2.21688938, 2.00165296, 1.81766617, 2.78586531,
       2.67522621, 3.38513398, 3.0353508, 2.92293549, 3.81956744,
       3.6745038, 3.69086194, 3.92426181, 3.34028482, 1.96539891,
       2.43147993, 2.66511655, 2.34880662, 4.03147316, 4.13719845,
       4.31058264, 5.25250196, 1.59580016, -0.1842365, -0.94229329,
       -1.60695589, 1.48749816, 2.33687115, 1.78588998, 1.22873163,
       1.21550024]

# See https://www.stata-press.com/data/r14/snp500
areturns = [1.60864139, 0.6581642, 0.91177338,
            1.88970506, 0.76378739, 0.10790635, 0.29509732,
            0.16913767, 1.30772412, 0.85901159, 0.92307973,
            0.9833895, 0.9116146, 2.58575296, 0.36441925,
            1.89720023, 0.65161127, 1.17255056, 0.53518051,
            0.00534112, 1.25064528, 2.00023437, 0.79801333,
            1.42980587, 0.02078664, 2.31948757, 2.78705025,
            1.36003578, 0.15257211, 0.30815724, 0.40030465,
            0.89941251, 0.36925647, 0.75660467, 0.87896836,
            1.07261622, 0.1137321, 1.32838523, 1.03085732,
            1.33930087, 0.66706187, 0.94959277, 1.07173061,
            0.80687243, 1.35347247, 1.56781077, 0.71599048,
            0.50293237, 0.33926481, 2.94415998, 0.72026408,
            0.28967711, 1.05362082, 0.3702977, 2.05277085,
            0.49342933, 0.03423685, 0.34392089, 1.01741159,
            1.43457139, 0.03759775, 1.54626679, 1.07742834,
            0.28664029, 0.72592038, 0.91093767, 0.06915179,
            0.88005662, 0.47802091, 1.2907486, 0.57604247,
            0.71046084, 0.81753206, 0.26241753, 2.57300162,
            0.16590172, 0.2918649, 0.96136051, 1.6711514,
            0.94229084, 1.83614326, 0.28854966, 0.35050908,
            0.04593768, 0.07599987, 0.09888303, 0.12907109,
            2.0099268, 0.23006552, 1.18803704, 0.99970037,
            1.32702613, 0.45646569, 1.43720019, 0.04425191,
            0.53156406, 0.45951003, 1.26583254, 0.26994073,
            0.1238014, 0.53068936, 0.21927625, 0.73882329,
            0.13153869, 0.97837049, 2.36890459, 2.29313374,
            0.75562358, 0.08656374, 2.4979558, 0.64189923,
            0.22916116, 2.27840376, 0.46641645, 2.02508688,
            1.25530422, 1.27711689, 0.07773363, 0.23380435,
            1.58663058, 0.19108967, 0.52218717, 0.18055375,
            1.18262017, 0.47418493, 0.88282752, 0.98944044,
            1.04560554, 0.65470523, 0.2604697, 0.14658713,
            0.77688956, 1.10911596, 0.69967973, 1.04578161,
            0.29641318, 0.98087156, 0.46531865, 0.11846001,
            0.44440377, 1.11066306, 0.02238905, 0.19865835,
            1.48028743, 0.27695858, 0.9391492, 1.70575404,
            2.94507742, 0.35386264, 0.72816408, 1.80369282,
            0.12440593, 1.04197288, 1.2957871, 1.35031664,
            0.55384284, 1.13915396, 0.29186234, 1.21344364,
            0.23005128, 0.85578758, 1.80613887, 1.55996382,
            1.46395147, 0.59826899, 0.65880769, 1.68974137,
            1.12778795, 4.19566727, 0.14379959, 2.09945345,
            0.29264972, 1.25936544, 0.84738803, 0.54094779,
            2.27655816, 1.48392296, 1.13808954, 1.16038692,
            0.46204364, 2.09433556, 1.16782069, 2.0192802,
            2.6190269, 1.63471925, 0.25279006, 2.64083171,
            1.64290273, 2.42852569, 1.54714262, 1.14975035,
            3.59362221, 1.16689992, 5.11030865, 1.81326246,
            0.93489766, 1.38605726, 0.53841805, 1.02298951,
            2.03038621, 2.8340385, 0.13691254, 3.18769765,
            0.23076122, 1.95332313, 1.63122225, 2.66484141,
            0.86377442, 1.1782372, 0.57231718, 1.11979997,
            2.07001758, 0.08726255, 1.71130466, 1.04979181,
            1.9825747, 3.43235064, 1.50204682, 1.75699294,
            2.56816769, 0.75786251, 0.93131924, 1.45494628,
            0.49975556, 0.32756457, 0.47183469, 3.3737793,
            2.25759649, 0.34138981, 3.09048033, 10.32189178,
            10.15319347, 0.12398402, 4.65263939, 7.62032652,
            7.04052448, 4.55579329, 3.52704573, 3.38968754,
            3.00466204, 0.46617937, 1.42808878, 1.00660408,
            4.65142584, 5.20996618, 4.80301046, 0.99780792,
            1.15280604, 1.87296033, 4.60985804, 5.41294718,
            6.06733084, 3.18375754, 10.0548315, 4.22182512,
            1.24640226, 2.66358495, 2.60049844, 0.00352026,
            1.02208447, 4.09924603, 1.27764511, 0.90124834,
            0.5303241, 3.84383249, 1.24640775, 1.39796948,
            2.34609175, 1.7742399, 3.56689548, 1.27681601,
            5.32056713, 3.19770503, 1.89575887, 0.59274858,
            0.64010525, 2.65920091, 0.81912726, 0.4868626,
            3.13063931, 1.3960743, 1.03451502, 1.28983963,
            3.27489519, 1.41772103, 2.00014663, 2.02787399,
            3.50289273, 1.65296888, 0.02450024, 0.04084374,
            0.17252181, 0.78132814, 0.20216605, 1.48436368,
            0.3301619, 1.12080252, 0.00699845, 3.87074757,
            0.84627002, 2.26680374, 2.07992935, 1.62452054,
            0.66078293, 2.26608515, 1.58540344, 0.98763937,
            0.25370923, 1.2576412, 1.07146478, 0.48786601,
            0.02327727, 1.29385257, 3.52217674, 1.05305433,
            5.13598871, 1.43351507, 2.12951326, 3.03700447,
            0.65843326, 4.28524971, 2.3428576, 4.72853422,
            0.58606911, 2.70345545, 0.8207835, 0.16228235,
            2.80714321, 1.97183621, 0.5928334, 3.61601782,
            1.82700455, 1.52638936, 0.72525144, 0.6499536,
            1.58741212, 0.72647524, 0.65064299, 0.43771812,
            2.68048692, 2.20902133, 0.0988697, 0.31138307,
            2.79860616, 1.13209391, 0.91427463, 0.69550049,
            0.68990183, 0.65359998, 1.04932129, 0.00310441,
            0.48663121, 1.68144464, 0.99051267, 0.22263506,
            0.97846323, 0.55040002, 2.56734443, 0.12510587,
            2.15363359, 1.18440747, 0.66974002, 0.48981813,
            2.08285856, 1.03952742, 1.00747502, 0.52523118,
            0.81593889, 0.22168602, 2.73786068, 1.21678591,
            0.235705, 0.56248677, 3.66057348, 0.35822684,
            0.97550339, 1.21677041, 4.03415823, 9.10342026,
            2.24355674, 3.6120553, 4.36456299, 0.83891636,
            1.07712805, 2.28685427, 4.04548168, 1.67408013,
            4.57762337, 2.47123241, 1.88890803, 1.62245703,
            0.02149973, 0.48483402, 4.40716505, 0.28621164,
            4.56798553, 1.6255945, 0.6124717, 2.72943926,
            0.80645156, 1.26738918, 0.91451788, 1.59959269,
            0.0356785, 1.93719864, 0.42164543, 0.87313241,
            0.52508104, 0.44771862, 1.38226497, 1.83891225,
            0.00711749, 0.26621303, 2.25254321, 0.27307722,
            0.26436633, 1.80608702, 2.29477572, 2.0931437,
            2.2915051, 0.82041657, 2.09074521, 1.87793779,
            2.15142703, 1.549685, 2.44940472, 0.45297864,
            0.35515305, 0.23224437, 1.77138305, 0.98827285,
            0.98435384, 0.80031335, 0.49445853, 0.36061874,
            2.15444446, 1.92558503, 0.75404048, 0.31921348,
            0.32092738, 0.48054051, 0.98650485, 1.1810472,
            0.28533801, 3.02953291, 0.16818592, 2.20164418,
            0.3911584, 0.6942575, 0.55016953, 0.06157291,
            0.19509397, 2.3744297, 0.73775989, 1.12842739,
            0.87197775, 0.30168825, 0.71310955, 0.27689508,
            1.13476491, 1.60331428, 1.56165123, 0.31513214,
            0.02698154, 0.49029687, 0.17265303, 0.36386153,
            0.56225872, 1.59077382, 1.84919345, 1.4230696,
            1.28607559, 0.57890779, 1.14760947, 0.22594096,
            0.43510813, 2.90668917, 1.49716794, 1.9549973,
            2.10786223, 0.71948445, 0.19396119, 0.86563414,
            0.63498968, 2.3593328, 0.18950517, 0.45737442,
            1.82937241, 1.72589195, 0.29414186, 0.74434268,
            1.22564518, 2.01444268, 2.32068515, 0.98414028,
            0.1174908, 0.22450124, 1.24669802, 0.70953292,
            0.21857196, 0.11119327, 0.60500813, 2.04446197,
            1.146896, 0.54849964, 0.23402978, 0.32219616,
            2.7076292, 1.57800817, 2.08260155, 1.81090641,
            0.45189673, 1.01260054, 0.65379494, 0.94736898,
            0.37556711, 0.44287458, 0.34578958, 1.48449266,
            1.95924711, 0.09717447]

# See https://www.stata-press.com/data/r14/mumpspc
# Note that this has already been seasonally differenced at period 12
mumpspc = [0.29791319, 0.41467956, 1.13061404, 1.23267496,
           1.55659747, 1.41078568, 0.45335022, 0.1419628,
           0.03802268, 0.04621375, 0.01261204, 0.04653099,
           0.10195512, 0.18079406, -0.1898452, -0.24501109,
           -0.71440864, -0.82188988, -0.32300544, -0.07680188,
           -0.0183593, -0.02145147, -0.14442876, -0.13897884,
           -0.41970083, -0.53978181, -0.81733, -0.77516699,
           -0.6827361, -0.27539611, 0.01427381, -0.02352227,
           0.00223821, -0.00509738, 0.03753691, 0.05826023,
           0.34700248, 0.53648567, 0.56336415, 0.73740566,
           0.68290168, 0.80702746, 0.47288245, 0.22873914,
           0.1323263, 0.18721257, 0.38872179, 0.5571546,
           0.62545192, 0.51162982, 1.28496778, 0.91240239,
           0.44763446, -0.34558165, -0.32126725, -0.13707247,
           -0.11812115, -0.14246191, -0.33914241, -0.59595251,
           -0.76603931, -0.95292002, -1.69234133, -1.44532502,
           -0.8163048, -0.27210402, -0.05841839, 0.02669862,
           0.06060357, 0.04068814, 0.17806116, 0.25716701,
           0.58398741, 0.95062274, 2.00101161, 2.05761814,
           1.74057662, 0.76864243, 0.3566184, 0.01938879,
           0.01129906, -0.00691494, -0.11471844, -0.12220788,
           -0.46378085, -0.76668882, -1.8203615, -1.80084801,
           -1.58501005, -0.5208298, -0.27426577, -0.01387694,
           -0.04243414, -0.07133579, -0.10209171, -0.04366681,
           -0.06109473, -0.03943163, 0.3148942, 0.57496029,
           0.60446811, 0.73262405, 0.37140131, 0.18555129,
           0.08227628, 0.11913572, 0.22764499, 0.35582894,
           0.60779673, 0.85310715, 1.23990095, 0.89678788,
           0.23346186, -0.24769557, -0.28325707, -0.13954946,
           -0.09492368, -0.07607545, -0.23001991, -0.42238122,
           -0.68010765, -0.90599316, -1.69077659, -1.67265296,
           -1.00972712, -0.67655402, 0.01419702, -0.00304723,
           0.06103691, 0.09834027, 0.18685167, 0.29223168,
           0.52865916, 0.54262394, 0.64842945, 0.95841271,
           1.24009287, 1.16617942, 0.80071652, 0.3447271,
           0.1351914, 0.04118001, 0.1700764, 0.39442945,
           0.35222113, 0.21554053, 0.4189862, 0.01172769,
           -0.86072814, -1.04859877, -0.81989408, -0.35956979,
           -0.13597609, -0.10660569, -0.25517979, -0.39934713,
           -0.48581338, -0.33558851, -0.32364452, 0.02615488,
           0.53735149, 0.43695128, 0.12556195, 0.04231615,
           0.00691247, -0.03409019, -0.05299731, -0.1705423,
           -0.23371273, -0.13540632, -0.13686514, -0.28611076,
           -0.2569176, -0.15721166, -0.12167645, -0.0396246,
           -0.03912748, -0.03107409, 0.02763657, -0.03745994,
           -0.0960384, -0.16005671, -0.23481375, -0.2919997,
           -0.28406811, -0.23517478, -0.10721764, -0.05092888,
           -0.04520934, 0.01234692, -0.03137775, -0.01226076,
           0.00540099, 0.0410589, -0.06418979, -0.23792684,
           -0.19889355, 0.15362859, 0.19808075, 0.09901999,
           0.08383148, 0.1328882, 0.1155429, 0.06566355,
           0.13103351, -0.00214756, 0.11389524, 0.60455656,
           0.43063915, -0.11312306, 0.00848174, -0.04416773,
           -0.03458966, -0.11635408, -0.09985384, -0.10910749,
           -0.03021795, 0.00818002, -0.20921308, -0.42517149,
           -0.26740992, 0.21723568, 0.19341183, 0.03723881,
           0.0800474, 0.1313054, 0.17315492, 0.60613275,
           0.88496959, 1.29391515, 1.67872524, 1.1444242,
           0.56303668, 0.21097398, -0.29172775, -0.07173294,
           -0.10594339, -0.13427913, -0.23306128, -0.63841069,
           -1.01829767, -1.37716746, -1.74518943, -1.48689389,
           -1.00245714, -0.67613804, -0.09916437, 0.01034598,
           0.00059676, -0.02620511, 0.07644644, 0.21421635,
           0.36779583, 0.44090557, 0.65572244, 0.69319898,
           1.03741217, 1.03150916, 0.48106751, 0.19878693,
           0.08993446, 0.10016203, 0.08885416, 0.01304582,
           0.01628131, -0.16743767, -0.3889482, -0.25320077,
           -0.41278255, -0.64387393, -0.24642634, -0.09595281,
           0.00029226, -0.03017606, -0.09989822, -0.10608336,
           -0.12089968, -0.02303368, -0.07865107, -0.07976627,
           -0.27282, -0.00616729, 0.12162459, 0.01441428,
           0.01936977, 0.04224043, 0.10971794, 0.31981739,
           0.37371701, 0.21740788, 0.66436541, 0.8377074,
           1.11139965, 0.89899027, 0.63889956, 0.26021931,
           0.10602421, 0.05764158, 0.03996068, 0.13342732,
           -0.01258349, 0.20526713, -0.05639255, -0.51611507,
           -1.10225511, -1.04906142, -0.82814342, -0.32945809,
           -0.16659749, -0.13606755, -0.156371, -0.44539213,
           -0.54849428, -0.57765388, -0.46875834, -0.20867264,
           0.11628377, 0.30508852, 0.18076879, 0.15996796,
           0.09090945, 0.13049443, 0.37585843, 0.47701722,
           0.8886351, 1.12534606, 1.0532701, 1.1787746,
           1.19929063, 0.67156017, 0.26693404, 0.08880523,
           -0.0367229, 0.01958427, -0.2178995, -0.35959432,
           -0.61479795, -1.12488365, -1.24093127, -1.37260103,
           -1.34592342, -1.1085875, -0.48515847, -0.22466549,
           -0.01377375, -0.15326615, -0.20697775, -0.21839607,
           -0.37820193, -0.18108195, -0.23839343, 0.00777894,
           -0.01658171, 0.14208788, 0.21352491, 0.08116969,
           0.0220954, 0.05151662, 0.15160444, 0.46347663,
           0.59711337, 0.69609326, 0.85816896, 0.44160861,
           0.29913878, 0.35687125, 0.02410281, -0.00206721,
           0.04784113, 0.01441422, 0.01972398, -0.19168586,
           -0.31085777, -0.38792318, -0.59203249, -0.4652282,
           -0.36413753, -0.41189915, -0.27989927, -0.06170946,
           -0.09512204, -0.05406281, -0.04524729, -0.19567066,
           -0.19209856, -0.30510414, -0.21937585, -0.34253049,
           -0.08848315, 0.0628857, 0.12370691, 0.08033729,
           0.02536885, 0.06512444, -0.00683796, 0.01617461,
           0.09414208, 0.17485267, 0.01436073, 0.15278709,
           0.21909434, -0.13190985, 0.1297549, 0.00458425,
           0.00097814, 0.0419029, 0.09299085, 0.30784416,
           0.3420583, 0.31633973, 0.6052171, 0.59994769,
           0.19161701, 0.14463156, -0.00356764, 0.03013593,
           -0.00030272, -0.04639405, -0.11171955, -0.26541206,
           -0.46245131, -0.59785151, -0.93805957, -1.02102923,
           -0.85468853, -0.57457525, -0.43376198, -0.22778665,
           -0.08325937, -0.07688884, -0.10757375, -0.04266521,
           -0.07971251, 0.19849321, 0.46367952, 0.45219129,
           0.5286305, 0.82308269, 0.62806904, 0.44585282,
           0.2649036, 0.18073915, 0.24439827, 0.33583486,
           0.36763605, 0.31510991, 0.44708037, 0.27008474,
           0.06621343, -0.20664448, -0.34370041, -0.30381745,
           -0.18254732, -0.16462031, -0.20288868, -0.47805107,
           -0.42589119, -0.52396262, -0.80304122, -0.54068702,
           -0.32430774, -0.41455108, -0.18256193, -0.11230741,
           -0.05113308, -0.00785848, -0.00410898, 0.02002721,
           0.04911622, 0.11129829, 0.03739616, 0.23160917,
           0.09051466, 0.0703001, 0.15306205, 0.092351,
           0.04038295, -0.00022292, -0.0345473, -0.104352,
           -0.14002147, -0.25555477, -0.15546834, -0.12915748,
           -0.00736588, 0.18039131, 0.03981721, 0.05406788,
           -0.00028329, 0.12522104, 0.09731361, 0.29498664,
           0.20997131, 0.16853192, 0.07126871, 0.02766478,
           -0.13036358, -0.26429421, -0.18460721, -0.17133695,
           -0.06757163, -0.16766661, -0.17020702, -0.26582304,
           -0.23111637, -0.16535208, -0.13117793, -0.28425765,
           -0.30206084, -0.16778651, -0.0795947, -0.0456669,
           -0.01921733, -0.02716412, 0.01525059, 0.01458484,
           0.00587094, 0.01239279, -0.03418982, -0.09835899,
           0.05628902, 0.00924054]


class MarkovRegression:
    @classmethod
    def setup_class(cls, true, endog, atol=1e-5, rtol=1e-7, **kwargs):
        cls.model = markov_regression.MarkovRegression(endog, **kwargs)
        cls.true = true
        cls.result = cls.model.smooth(cls.true['params'])
        cls.atol = atol
        cls.rtol = rtol

    @pytest.mark.smoke
    def test_summary(self):
        self.result.summary()

    def test_llf(self):
        assert_allclose(self.result.llf, self.true['llf'], atol=self.atol,
                        rtol=self.rtol)

    def test_fit(self, **kwargs):
        # Test fitting against Stata
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            res = self.model.fit(disp=False, **kwargs)
        assert_allclose(res.llf, self.true['llf_fit'], atol=self.atol,
                        rtol=self.rtol)

    @pytest.mark.smoke
    def test_fit_em(self, **kwargs):
        # Test EM fitting (smoke test)
        res_em = self.model._fit_em(**kwargs)
        assert_allclose(res_em.llf, self.true['llf_fit_em'], atol=self.atol,
                        rtol=self.rtol)


fedfunds_const_filtered_joint_probabilities = np.array([
         [[9.81875427e-01,   9.99977639e-01,   9.99982269e-01,
           9.99977917e-01,   9.99961064e-01,   9.99932206e-01,
           9.99919386e-01,   9.99894144e-01,   9.99875287e-01,
           9.99853807e-01,   9.99852600e-01,   9.99839056e-01,
           9.99783848e-01,   9.99774884e-01,   9.99954588e-01,
           9.99988082e-01,   9.99982757e-01,   9.99948590e-01,
           9.99910525e-01,   9.99825195e-01,   9.99660841e-01,
           9.99411534e-01,   9.99435921e-01,   9.99578212e-01,
           9.99835797e-01,   9.99931424e-01,   9.99955815e-01,
           9.99969158e-01,   9.99971638e-01,   9.99929247e-01,
           9.99921198e-01,   9.99904441e-01,   9.99869417e-01,
           9.99855264e-01,   9.99845124e-01,   9.99846496e-01,
           9.99755048e-01,   9.99708382e-01,   9.99701905e-01,
           9.99690188e-01,   9.99700859e-01,   9.99653041e-01,
           9.99426220e-01,   9.99320494e-01,   9.99321052e-01,
           9.99230758e-01,   9.98727025e-01,   9.97930031e-01,
           9.95880407e-01,   9.94433855e-01,   9.97574246e-01,
           9.99263821e-01,   9.99459141e-01,   9.99242937e-01,
           9.98291614e-01,   9.91580774e-01,   9.89101309e-01,
           9.88455096e-01,   9.73457493e-01,   7.07858812e-01,
           9.84830417e-02,   5.48777905e-03,   6.48064326e-04,
           9.48013715e-04,   1.00281851e-02,   1.69325148e-01,
           8.57820523e-01,   9.93211456e-01,   9.94962561e-01,
           9.97835285e-01,   9.99559280e-01,   9.99122588e-01,
           9.98381004e-01,   9.97159054e-01,   9.81694661e-01,
           8.52237392e-01,   2.98185003e-02,   3.80317793e-04,
           3.03223798e-05,   3.72338821e-06,   9.81252727e-08,
           1.16279423e-06,   2.19178686e-03,   2.54688430e-01,
           5.45485941e-01,   8.49963514e-01,   9.79591871e-01,
           9.94535520e-01,   9.95920695e-01,   9.97652340e-01,
           9.98379085e-01,   9.97097840e-01,   9.92715379e-01,
           9.78668900e-01,   9.54978984e-01,   8.70032440e-01,
           4.73445558e-01,   1.87649267e-02,   2.26217560e-04,
           7.75338336e-06,   1.77410820e-06,   2.00686803e-08,
           9.02595322e-11,   2.90324859e-10,   2.76383741e-07,
           4.31669137e-09,   6.21472832e-13,   4.82334762e-14,
           1.27598134e-14,   3.17409414e-12,   2.61276609e-10,
           7.78226008e-11,   5.39702646e-09,   5.20281165e-06,
           1.16430050e-04,   2.28275630e-04,   8.20520602e-05,
           3.54392208e-05,   2.52690630e-05,   5.71223049e-06,
           6.04760361e-07,   3.25802367e-06,   1.49189117e-04,
           9.03616681e-04,   2.05169327e-03,   1.79174202e-03,
           2.00293091e-03,   8.80162479e-03,   6.98800266e-02,
           2.10894169e-01,   3.78257683e-01,   4.16997707e-01,
           3.74555143e-01,   3.09162817e-01,   3.19673213e-01,
           2.13371614e-01,   5.00408396e-02,   5.65131400e-03,
           2.67174854e-04,   2.77281533e-05,   3.81188091e-05,
           1.64157087e-04,   4.99719207e-04,   8.42437704e-04,
           9.94072768e-04,   1.92236077e-03,   1.77274131e-02,
           1.80694204e-01,   5.59064015e-01,   9.22767369e-01,
           9.95149639e-01,   9.99418123e-01,   9.99755674e-01,
           9.99825956e-01,   9.99829396e-01,   9.99837748e-01,
           9.99825597e-01,   9.99839509e-01,   9.99789371e-01,
           9.99458023e-01,   9.98857894e-01,   9.97136330e-01,
           9.92814933e-01,   9.88571000e-01,   9.89824082e-01,
           9.91277010e-01,   9.94688346e-01,   9.96090383e-01,
           9.96010189e-01,   9.96126138e-01,   9.96152552e-01,
           9.94805484e-01,   9.94365920e-01,   9.94404335e-01,
           9.94350015e-01,   9.94473588e-01,   9.94298374e-01,
           9.97447776e-01,   9.98217589e-01,   9.98253594e-01,
           9.97319145e-01,   9.96244465e-01,   9.93650638e-01,
           9.84885276e-01,   9.71807970e-01,   9.63220269e-01,
           9.84748123e-01,   9.97795750e-01,   9.99604178e-01,
           9.99934302e-01,   9.99968719e-01,   9.99969041e-01,
           9.99969388e-01,   9.99978903e-01,   9.99983737e-01,
           9.99983963e-01,   9.99987949e-01,   9.99988448e-01,
           9.99988464e-01,   9.99988318e-01,   9.99980100e-01,
           9.99960662e-01,   9.99921917e-01,   9.99854499e-01,
           9.99711016e-01,   9.99422933e-01,   9.98897716e-01,
           9.97954476e-01,   9.96644185e-01,   9.96382036e-01,
           9.96295075e-01,   9.96325086e-01,   9.97066095e-01,
           9.98599288e-01,   9.99729020e-01,   9.99942853e-01,
           9.99959712e-01,   9.99992291e-01,   9.99995977e-01,
           9.99996082e-01,   9.99996179e-01,   9.99996370e-01,
           9.99996334e-01,   9.99996045e-01,   9.99996030e-01,
           9.99996030e-01],
          [1.79021167e-02,   1.14091306e-05,   5.61557959e-07,
           8.80398735e-07,   1.08717798e-06,   1.94073468e-06,
           3.37670187e-06,   3.96039606e-06,   5.22475895e-06,
           6.12683114e-06,   7.18211108e-06,   7.18979687e-06,
           7.88353588e-06,   1.06791533e-05,   1.09974873e-05,
           1.76468607e-06,   5.20603180e-07,   8.57457507e-07,
           2.59206830e-06,   4.45469808e-06,   8.73353814e-06,
           1.69387908e-05,   2.93063475e-05,   2.74245464e-05,
           2.02263872e-05,   7.38324620e-06,   3.13785014e-06,
           2.10478420e-06,   1.47354829e-06,   1.37871330e-06,
           3.55723955e-06,   3.85823009e-06,   4.70191999e-06,
           6.45467678e-06,   7.09050564e-06,   7.57792773e-06,
           7.48186721e-06,   1.21760845e-05,   1.43286436e-05,
           1.45502695e-05,   1.51400501e-05,   1.45617528e-05,
           1.70401707e-05,   2.85443698e-05,   3.33783376e-05,
           3.30986931e-05,   3.77268239e-05,   6.32867451e-05,
           1.02678523e-04,   2.05654391e-04,   2.75680178e-04,
           1.10405281e-04,   3.20904634e-05,   2.60815610e-05,
           3.74458003e-05,   8.51014220e-05,   4.26242634e-04,
           5.36412357e-04,   5.55614723e-04,   9.68406481e-04,
           2.07440399e-03,   2.51696579e-03,   4.11814576e-03,
           1.01505143e-02,   4.58175094e-02,   1.46789923e-01,
           9.51602906e-02,   2.51277827e-03,   2.19080166e-04,
           2.47729179e-04,   9.84424301e-05,   1.75415066e-05,
           4.40579693e-05,   8.06554966e-05,   1.39332709e-04,
           8.08543683e-04,   2.63400428e-04,   6.28778860e-04,
           1.53926554e-03,   1.21448448e-04,   4.01921637e-05,
           1.47981180e-03,   7.57754547e-02,   1.54441814e-01,
           4.03957090e-02,   3.08060743e-02,   6.79972580e-03,
           7.03557513e-04,   2.44292397e-04,   1.96941023e-04,
           1.10340666e-04,   7.73495785e-05,   1.44199620e-04,
           3.60905335e-04,   1.04887049e-03,   2.05193714e-03,
           3.56087416e-03,   1.05497090e-03,   5.73656986e-04,
           4.96641901e-04,   1.80265024e-04,   5.65192629e-06,
           8.15973913e-07,   1.82423614e-05,   7.76851662e-04,
           2.84604839e-07,   1.10296978e-07,   2.24235284e-08,
           2.91783631e-08,   5.57802135e-06,   2.40181297e-06,
           1.66126826e-06,   1.66577067e-04,   1.60124567e-03,
           3.71040697e-03,   3.04702430e-03,   1.28036894e-03,
           1.33199390e-03,   9.46259530e-04,   3.01196207e-04,
           1.01009606e-04,   1.64390413e-03,   4.63667164e-03,
           9.63523286e-03,   9.87732797e-03,   7.60992378e-03,
           1.08213202e-02,   3.47413597e-02,   7.87085339e-02,
           6.19641042e-02,   5.16881745e-02,   2.83502897e-02,
           2.39198441e-02,   2.39310293e-02,   3.28190388e-02,
           2.00980320e-02,   8.42451713e-03,   4.66668659e-03,
           1.31406606e-03,   8.97753552e-04,   2.11004086e-03,
           3.91003494e-03,   6.26373002e-03,   6.34371628e-03,
           7.04224256e-03,   1.21673274e-02,   6.36067034e-02,
           1.04652728e-01,   7.17968006e-02,   2.76866443e-02,
           2.66003442e-03,   1.12494156e-04,   2.40738814e-05,
           1.12943610e-05,   8.34520559e-06,   8.32013406e-06,
           7.89300655e-06,   8.53818448e-06,   7.79122759e-06,
           1.03973423e-05,   2.72411101e-05,   5.70666949e-05,
           1.43281927e-04,   3.59485298e-04,   5.68122941e-04,
           4.93097756e-04,   4.23244829e-04,   2.50908844e-04,
           1.87544775e-04,   1.94954279e-04,   1.88610947e-04,
           1.87324551e-04,   2.56591337e-04,   2.75680269e-04,
           2.72701680e-04,   2.75688894e-04,   2.69118592e-04,
           2.79368706e-04,   1.16602211e-04,   8.54105771e-05,
           8.50836389e-05,   1.32948236e-04,   1.85246543e-04,
           3.13229937e-04,   7.48658490e-04,   1.39370122e-03,
           1.85235809e-03,   6.94881963e-04,   7.74820062e-05,
           1.63275933e-05,   2.53162706e-06,   1.47419297e-06,
           1.51187760e-06,   1.49217514e-06,   1.00525815e-06,
           7.82384697e-07,   7.82207824e-07,   5.77845546e-07,
           5.62709744e-07,   5.62700867e-07,   5.70153586e-07,
           9.91143211e-07,   1.96623986e-06,   3.90276412e-06,
           7.25964578e-06,   1.44416500e-05,   2.88341084e-05,
           5.49895700e-05,   1.01928727e-04,   1.66788149e-04,
           1.76920424e-04,   1.80878376e-04,   1.79263902e-04,
           1.41441142e-04,   6.46353467e-05,   1.05822848e-05,
           2.38770925e-06,   1.94345661e-06,   2.95614088e-07,
           1.91116221e-07,   1.91095610e-07,   1.86129447e-07,
           1.76579974e-07,   1.78918351e-07,   1.93625709e-07,
           1.93628651e-07]],

         [[1.12025955e-05,   1.08238349e-05,   1.71596282e-05,
           2.11831999e-05,   3.78067714e-05,   6.57213848e-05,
           7.69689076e-05,   1.01479702e-04,   1.18846167e-04,
           1.39184231e-04,   1.39184063e-04,   1.52618987e-04,
           2.06583251e-04,   2.12093282e-04,   3.40270257e-05,
           1.01343628e-05,   1.67136918e-05,   5.05076133e-05,
           8.66507075e-05,   1.69568562e-04,   3.27466124e-04,
           5.61681158e-04,   5.19031075e-04,   3.83481719e-04,
           1.41026283e-04,   6.07294396e-05,   4.09142321e-05,
           2.86746604e-05,   2.68478873e-05,   6.92750449e-05,
           7.49687747e-05,   9.13357640e-05,   1.25272004e-04,
           1.37363965e-04,   1.46709304e-04,   1.44790612e-04,
           2.35646550e-04,   2.75965631e-04,   2.79621160e-04,
           2.90883100e-04,   2.79620868e-04,   3.27463569e-04,
           5.47092221e-04,   6.32282164e-04,   6.24012551e-04,
           7.11758349e-04,   1.18880242e-03,   1.88316198e-03,
           3.62983774e-03,   4.41594500e-03,   1.67213822e-03,
           5.61598141e-04,   4.92415886e-04,   7.11767023e-04,
           1.60853614e-03,   7.65465286e-03,   7.24380556e-03,
           7.05094383e-03,   1.63405802e-02,   1.20569790e-01,
           3.94742135e-02,   2.08678283e-03,   1.51404348e-04,
           8.92900957e-05,   1.99758310e-04,   7.61880868e-04,
           4.06265456e-04,   1.18223714e-03,   3.92458604e-03,
           1.52531964e-03,   3.10635338e-04,   8.44534873e-04,
           1.50619244e-03,   2.54719705e-03,   1.58406633e-02,
           7.41723708e-02,   9.56881006e-02,   5.83874154e-04,
           1.90161316e-05,   2.96375310e-05,   2.36039059e-06,
           7.58604311e-07,   2.57849849e-05,   9.40604577e-04,
           5.33706193e-03,   3.09799210e-03,   1.66375727e-03,
           2.74932130e-03,   3.05895259e-03,   1.78605741e-03,
           1.35561648e-03,   2.61499931e-03,   6.20778364e-03,
           1.51802588e-02,   2.05865429e-02,   3.71923130e-02,
           5.95732628e-02,   1.65718056e-02,   3.80823607e-04,
           1.50863835e-05,   9.51367568e-06,   3.43305910e-06,
           1.06949636e-07,   1.53871486e-08,   3.43715817e-07,
           1.46644282e-05,   5.44780299e-09,   2.07973264e-09,
           4.22811016e-10,   5.50174068e-10,   1.05177578e-07,
           4.52926927e-08,   3.13205860e-08,   3.13648669e-06,
           3.02223414e-05,   7.21922046e-05,   6.18725853e-05,
           2.56885381e-05,   2.57933905e-05,   1.83306498e-05,
           5.78811168e-06,   1.91303926e-06,   3.09596995e-05,
           8.97107118e-05,   1.98397776e-04,   2.25453416e-04,
           1.76630376e-04,   2.34227583e-04,   7.30232210e-04,
           2.38495171e-03,   4.00510154e-03,   7.77726570e-03,
           8.97115089e-03,   8.22739679e-03,   6.04111865e-03,
           7.78825566e-03,   5.37640212e-03,   1.15742054e-03,
           1.96231469e-04,   2.98340104e-05,   1.74288946e-05,
           4.04251152e-05,   7.66081747e-05,   1.27458564e-04,
           1.35365069e-04,   1.50582215e-04,   2.47483336e-04,
           1.19104299e-03,   2.75836722e-03,   1.54674662e-03,
           5.81818376e-04,   4.20436146e-04,   2.14900000e-04,
           1.60869708e-04,   1.60870261e-04,   1.52618788e-04,
           1.65161879e-04,   1.50622869e-04,   2.01215675e-04,
           5.25921364e-04,   1.08428049e-03,   2.64975788e-03,
           6.12720308e-03,   8.04408524e-03,   6.02884531e-03,
           5.43410929e-03,   3.39451560e-03,   2.90252080e-03,
           3.18247731e-03,   3.05958361e-03,   3.05966473e-03,
           4.19096979e-03,   4.24463587e-03,   4.13448594e-03,
           4.18905097e-03,   4.08069293e-03,   4.24434753e-03,
           1.76233516e-03,   1.48624883e-03,   1.52595908e-03,
           2.38530006e-03,   3.18322588e-03,   5.16768050e-03,
           1.11378320e-02,   1.52736098e-02,   1.41741690e-02,
           4.54911025e-03,   8.77392678e-04,   2.94712782e-04,
           4.85508787e-05,   2.86746478e-05,   2.94397372e-05,
           2.90546890e-05,   1.95742396e-05,   1.52422076e-05,
           1.52422111e-05,   1.12600205e-05,   1.09674004e-05,
           1.09674006e-05,   1.11127487e-05,   1.93182406e-05,
           3.83078048e-05,   7.59623818e-05,   1.41028921e-04,
           2.79623709e-04,   5.54340948e-04,   1.04233041e-03,
           1.88320811e-03,   2.94262269e-03,   2.94184869e-03,
           2.98057651e-03,   2.94168054e-03,   2.32272136e-03,
           1.09836589e-03,   1.93411390e-04,   4.60605812e-05,
           3.78067203e-05,   5.75348228e-06,   3.72600447e-06,
           3.72600486e-06,   3.62917375e-06,   3.44299546e-06,
           3.48862495e-06,   3.77538503e-06,   3.77538498e-06,
           3.77538498e-06],
          [2.11253415e-04,   1.27726353e-07,   9.96660517e-09,
           1.92893528e-08,   4.25132434e-08,   1.31928738e-07,
           2.68831493e-07,   4.15719953e-07,   6.42307057e-07,
           8.82117691e-07,   1.03405286e-06,   1.13509493e-06,
           1.68479526e-06,   2.34314197e-06,   3.87056416e-07,
           1.84972077e-08,   8.99961200e-09,   4.47949311e-08,
           2.32324230e-07,   7.81405794e-07,   2.95897306e-06,
           9.84611707e-06,   1.57411766e-05,   1.08818924e-05,
           2.95071175e-06,   4.63781583e-07,   1.32789240e-07,
           6.24246828e-08,   4.09188950e-08,   9.87912777e-08,
           2.75844825e-07,   3.64508436e-07,   6.09288617e-07,
           9.17164574e-07,   1.07606678e-06,   1.13499677e-06,
           1.82395688e-06,   3.47637333e-06,   4.14516370e-06,
           4.37886128e-06,   4.37989719e-06,   4.93360580e-06,
           9.64763432e-06,   1.86794329e-05,   2.15570874e-05,
           2.43845482e-05,   4.64462028e-05,   1.23520043e-04,
           3.87076429e-04,   9.44545390e-04,   4.77935817e-04,
           6.41759692e-05,   1.63523468e-05,   1.92148699e-05,
           6.24041695e-05,   6.79471603e-04,   3.22864270e-03,
           3.95754786e-03,   9.64631226e-03,   1.70602991e-01,
           8.59968341e-01,   9.89908472e-01,   9.95082386e-01,
           9.88812182e-01,   9.43954547e-01,   6.83123048e-01,
           4.66129206e-02,   3.09352861e-03,   8.93773079e-04,
           3.91666349e-04,   3.16418394e-05,   1.53356713e-05,
           6.87457292e-05,   2.13092992e-04,   2.32534345e-03,
           7.27816933e-02,   8.74229999e-01,   9.98407029e-01,
           9.98411396e-01,   9.99845191e-01,   9.99957349e-01,
           9.98518267e-01,   9.22006973e-01,   5.89929152e-01,
           4.08781288e-01,   1.16132419e-01,   1.19446461e-02,
           2.01160124e-03,   7.76059520e-04,   3.64661127e-04,
           1.54958020e-04,   2.09811287e-04,   9.32637836e-04,
           5.78993623e-03,   2.33856026e-02,   9.07233098e-02,
           4.63420305e-01,   9.63608297e-01,   9.98819302e-01,
           9.99480518e-01,   9.99808447e-01,   9.99990895e-01,
           9.99999077e-01,   9.99981742e-01,   9.99222528e-01,
           9.99985047e-01,   9.99999884e-01,   9.99999975e-01,
           9.99999970e-01,   9.99994421e-01,   9.99997493e-01,
           9.99998293e-01,   9.99833386e-01,   9.98390415e-01,
           9.96142941e-01,   9.96652508e-01,   9.98575706e-01,
           9.98606878e-01,   9.99002678e-01,   9.99674761e-01,
           9.99892598e-01,   9.98350925e-01,   9.95183180e-01,
           9.89371440e-01,   9.87872581e-01,   9.90372881e-01,
           9.86999118e-01,   9.56222788e-01,   8.50681207e-01,
           7.24756775e-01,   5.66049041e-01,   5.46874738e-01,
           5.92553862e-01,   6.58678757e-01,   6.41466629e-01,
           7.58742098e-01,   9.36158241e-01,   9.88524579e-01,
           9.98222528e-01,   9.99044684e-01,   9.97834411e-01,
           9.95885383e-01,   9.93159943e-01,   9.92686387e-01,
           9.91828320e-01,   9.85759730e-01,   9.18418400e-01,
           7.13462025e-01,   3.66380818e-01,   4.79992398e-02,
           1.60850862e-03,   4.89463014e-05,   5.35211883e-06,
           1.87952723e-06,   1.38874976e-06,   1.31354781e-06,
           1.34854399e-06,   1.33034183e-06,   1.62179619e-06,
           5.65868721e-06,   3.05843904e-05,   1.56845284e-04,
           9.14581641e-04,   3.02542940e-03,   3.57894927e-03,
           2.79578295e-03,   1.49389314e-03,   7.56187782e-04,
           6.19788616e-04,   6.19323917e-04,   5.99172642e-04,
           8.16221729e-04,   1.13285230e-03,   1.18549926e-03,
           1.18823205e-03,   1.17003007e-03,   1.18816020e-03,
           5.10520503e-04,   1.79560242e-04,   1.35036369e-04,
           2.10470876e-04,   4.39360698e-04,   9.96435388e-04,
           3.66366176e-03,   1.21697619e-02,   2.12118606e-02,
           8.85040895e-03,   6.31975856e-04,   2.36270150e-05,
           8.19945322e-07,   7.50841896e-08,   4.48888835e-08,
           4.54342381e-08,   3.02100160e-08,   1.58478237e-08,
           1.23342395e-08,   9.10969133e-09,   6.55477183e-09,
           6.38307937e-09,   6.46757155e-09,   1.13921361e-08,
           3.92715328e-08,   1.54492028e-07,   5.69351898e-07,
           2.10015832e-06,   8.28479101e-06,   3.11191703e-05,
           1.07326014e-04,   3.11263455e-04,   5.09327027e-04,
           5.47428437e-04,   5.52354866e-04,   4.31919788e-04,
           1.60904664e-04,   1.29332277e-05,   5.04162769e-07,
           9.33695004e-08,   1.15650115e-08,   1.13921931e-09,
           7.36511886e-10,   7.17294041e-10,   6.62811758e-10,
           6.37139329e-10,   6.98642410e-10,   7.56071871e-10,
           7.56083358e-10]]])


class TestFedFundsConst(MarkovRegression):
    # Results from Stata, see http://www.stata.com/manuals14/tsmswitch.pdf
    @classmethod
    def setup_class(cls):
        path = os.path.join(current_path, 'results',
                            'results_predict_fedfunds.csv')
        results = pd.read_csv(path)
        true = {
            'params': np.r_[.9820939, .0503587, 3.70877, 9.556793,
                            2.107562**2],
            'llf': -508.63592,
            'llf_fit': -508.63592,
            'llf_fit_em': -508.65852,
            'bse_oim': np.r_[.0104002, .0268434, .1767083, .2999889, np.nan],
            'smoothed0': results['const_sm1'],
            'smoothed1': results['const_sm2'],
            'predict0': results['const_yhat1'],
            'predict1': results['const_yhat2'],
            'predict_predicted': results['const_pyhat'],
            'predict_filtered': results['const_fyhat'],
            'predict_smoothed': results['const_syhat'],
        }
        super().setup_class(true, fedfunds, k_regimes=2)

    def test_filter_output(self, **kwargs):
        res = self.result
        assert_allclose(res.filtered_joint_probabilities,
                        fedfunds_const_filtered_joint_probabilities)

    def test_smoothed_marginal_probabilities(self):
        assert_allclose(self.result.smoothed_marginal_probabilities[:, 0],
                        self.true['smoothed0'], atol=1e-6)
        assert_allclose(self.result.smoothed_marginal_probabilities[:, 1],
                        self.true['smoothed1'], atol=1e-6)

    def test_predict(self):
        # Predictions conditional on regime (the same no matter which
        # probabilities are selected)
        for name in ['predicted', 'filtered', 'smoothed', None]:
            actual = self.model.predict(
                self.true['params'], probabilities=name, conditional=True)
            assert_allclose(actual[0],
                            self.true['predict0'], atol=1e-6)
            assert_allclose(actual[1],
                            self.true['predict1'], atol=1e-6)

        # Predicted
        actual = self.model.predict(
            self.true['params'], probabilities='predicted')
        assert_allclose(actual, self.true['predict_predicted'], atol=1e-5)

        # Filtered
        actual = self.model.predict(
            self.true['params'], probabilities='filtered')
        assert_allclose(self.model.predict(self.true['params'],
                                           probabilities='filtered'),
                        self.true['predict_filtered'], atol=1e-5)

        # Smoothed
        actual = self.model.predict(
            self.true['params'], probabilities='smoothed')
        assert_allclose(actual, self.true['predict_smoothed'], atol=1e-6)
        actual = self.model.predict(
            self.true['params'], probabilities=None)
        assert_allclose(actual, self.true['predict_smoothed'], atol=1e-6)

    def test_bse(self):
        # Cannot compare last element of bse because we estimate sigma^2 rather
        # than sigma^2
        bse = self.result.cov_params_approx.diagonal()**0.5
        assert_allclose(bse[:-1], self.true['bse_oim'][:-1], atol=1e-7)


fedfunds_const_short_filtered_joint_probabilities = np.array([
         [[9.81370301e-01,   9.99956215e-01,   9.99995966e-01,
           9.99996082e-01,   9.99996179e-01,   9.99996370e-01,
           9.99996334e-01,   9.99996045e-01,   9.99996030e-01,
           9.99996030e-01],
          [1.78929069e-02,   3.78065881e-05,   3.06546640e-07,
           1.91118379e-07,   1.91095611e-07,   1.86129447e-07,
           1.76579974e-07,   1.78918351e-07,   1.93625709e-07,
           1.93628651e-07]],

         [[3.71038873e-05,   5.75327472e-06,   3.72600443e-06,
           3.72600486e-06,   3.62917375e-06,   3.44299546e-06,
           3.48862495e-06,   3.77538503e-06,   3.77538498e-06,
           3.77538498e-06],
          [6.99688113e-04,   2.24977302e-07,   1.18135050e-09,
           7.36520203e-10,   7.17294043e-10,   6.62811758e-10,
           6.37139329e-10,   6.98642410e-10,   7.56071871e-10,
           7.56083358e-10]]])


fedfunds_const_short_predicted_joint_probabilities = np.array([
         [[[7.11514435e-01,   9.63797786e-01,   9.82050899e-01,
            9.82089938e-01,   9.82090052e-01,   9.82090147e-01,
            9.82090335e-01,   9.82090300e-01,   9.82090016e-01,
            9.82090001e-01],
           [1.29727398e-02,   1.75725147e-02,   3.71296195e-05,
            3.01057585e-07,   1.87696195e-07,   1.87673833e-07,
            1.82796594e-07,   1.73418115e-07,   1.75714621e-07,
            1.90158628e-07]],

          [[6.65201476e-04,   1.86850353e-06,   2.89727435e-07,
            1.87636739e-07,   1.87636761e-07,   1.82760472e-07,
            1.73384775e-07,   1.75682617e-07,   1.90123482e-07,
            1.90123479e-07],
           [1.25440648e-02,   3.52353838e-05,   1.13295645e-08,
            5.94912755e-11,   3.70902000e-11,   3.61219955e-11,
            3.33783385e-11,   3.20855083e-11,   3.51827235e-11,
            3.80747965e-11]]],


         [[[1.29727398e-02,   1.75725147e-02,   1.79053160e-02,
            1.79060278e-02,   1.79060298e-02,   1.79060316e-02,
            1.79060350e-02,   1.79060344e-02,   1.79060292e-02,
            1.79060289e-02],
           [2.36526442e-04,   3.20392181e-04,   6.76968547e-07,
            5.48905479e-09,   3.42218481e-09,   3.42177711e-09,
            3.33285249e-09,   3.16185867e-09,   3.20372988e-09,
            3.46708131e-09]],

          [[1.25440648e-02,   3.52353838e-05,   5.46354728e-06,
            3.53836769e-06,   3.53836810e-06,   3.44641328e-06,
            3.26961068e-06,   3.31294233e-06,   3.58526155e-06,
            3.58526150e-06],
           [2.36550228e-01,   6.64452729e-04,   2.13647738e-07,
            1.12185923e-09,   6.99430003e-10,   6.81172047e-10,
            6.29433420e-10,   6.05053821e-10,   6.63459686e-10,
            7.17997074e-10]]]])

fedfunds_const_short_smoothed_joint_probabilities = np.array([
         [[9.82056759e-01,   9.99961887e-01,   9.99999502e-01,
           9.99999618e-01,   9.99999623e-01,   9.99999637e-01,
           9.99999644e-01,   9.99999627e-01,   9.99999612e-01,
           9.99996030e-01],
          [1.79054228e-02,   3.78068025e-05,   3.06547724e-07,
           1.91119055e-07,   1.91096269e-07,   1.86130055e-07,
           1.76580558e-07,   1.78918992e-07,   1.93626403e-07,
           1.93628651e-07]],

         [[1.90448249e-06,   2.95069837e-07,   1.91096241e-07,
           1.91095282e-07,   1.86127261e-07,   1.76579242e-07,
           1.78922146e-07,   1.93629492e-07,   1.94345814e-07,
           3.77538498e-06],
          [3.59138585e-05,   1.15384749e-08,   6.05881299e-11,
           3.77738466e-11,   3.67874300e-11,   3.39933060e-11,
           3.26771544e-11,   3.58315175e-11,   3.89203762e-11,
           7.56083358e-10]]])


class TestFedFundsConstShort(MarkovRegression):
    # This is just a set of regression tests
    @classmethod
    def setup_class(cls):
        true = {
            'params': np.r_[.9820939, .0503587, 3.70877, 9.556793,
                            2.107562**2],
            'llf': -29.909297,
            'llf_fit': -7.8553370,
            'llf_fit_em': -7.8554974
        }
        super().setup_class(true, fedfunds[-10:], k_regimes=2)

    def test_filter_output(self, **kwargs):
        res = self.result

        # Filtered
        assert_allclose(res.filtered_joint_probabilities,
                        fedfunds_const_short_filtered_joint_probabilities)

        # Predicted
        desired = fedfunds_const_short_predicted_joint_probabilities
        if desired.ndim > res.predicted_joint_probabilities.ndim:
            desired = desired.sum(axis=-2)
        assert_allclose(res.predicted_joint_probabilities, desired)

    def test_smoother_output(self, **kwargs):
        res = self.result

        # Filtered
        assert_allclose(res.filtered_joint_probabilities,
                        fedfunds_const_short_filtered_joint_probabilities)

        # Predicted
        desired = fedfunds_const_short_predicted_joint_probabilities
        if desired.ndim > res.predicted_joint_probabilities.ndim:
            desired = desired.sum(axis=-2)
        assert_allclose(res.predicted_joint_probabilities, desired)

        # Smoothed, last entry
        assert_allclose(res.smoothed_joint_probabilities,
                        fedfunds_const_short_smoothed_joint_probabilities)

    def test_hamilton_filter_order_zero(self):
        k_regimes = 3
        nobs = 4
        initial_probabilities = np.ones(k_regimes) / k_regimes

        # We do not actually transition between the 3 regimes.
        regime_transition = np.eye(k_regimes)[:, :, np.newaxis]

        # Regime i correponds to a sequence of iid draws from discrete
        # random variables that are equally likely to be -1 and i for i=0,
        # 1, 2.  We observe the sequence -1, -1, 1, -1.  The first two
        # observations tell us nothing, but the third lets us know that we
        # are in regime 1 the whole time.
        conditional_likelihoods = np.ones((k_regimes, nobs)) / 2
        conditional_likelihoods[:, 2] = [0, 1, 0]

        expected_marginals = np.empty((k_regimes, nobs))
        expected_marginals[:, :2] = [[1/3], [1/3], [1/3]]
        expected_marginals[:, 2:] = [[0], [1], [0]]

        cy_results = markov_switching.cy_hamilton_filter_log(
            initial_probabilities, regime_transition,
            np.log(conditional_likelihoods + 1e-20), model_order=0)
        assert_allclose(cy_results[0], expected_marginals, atol=1e-15)

    def test_hamilton_filter_order_zero_with_tvtp(self):
        k_regimes = 3
        nobs = 8
        initial_probabilities = np.ones(k_regimes) / k_regimes

        # We do not actually transition between the 3 regimes except from
        # t=3 to t=4 where we reset to regimes 1 and 2 being equally
        # likely.
        regime_transition = np.zeros((k_regimes, k_regimes, nobs))
        regime_transition[...] = np.eye(k_regimes)[:, :, np.newaxis]
        regime_transition[..., 4] = [[0,     0,   0],
                                     [1/2, 1/2, 1/2],
                                     [1/2, 1/2, 1/2]]

        # Regime i correponds to a sequence of iid draws from discrete
        # random variables that are equally likely to be -1, i, and i + 1
        # for i = 0, 1, 2.  We observe the sequence:
        #
        #     t =  0, 1, 2,  3,  4, 5, 6,  7
        #   X_t = -1, 1, 2, -1, -1, 2, 3, -1
        #
        # The first observations tell us nothing, the second tells us that
        # regime 0 and 1 are equally likely, and the third tells us that we
        # must be in regime 1 for t = 0, 1, 2, 3.  At t=4 we transition to
        # state 1 or 2.  In this case, neither a -1 or 2 changes our view
        # that these states are equally likely, but a 3 tells we must be in
        # state 2 for the second four timestamps.
        conditional_likelihoods = np.empty((k_regimes, nobs))
        conditional_likelihoods[:, 0] = [1/3, 1/3, 1/3]
        conditional_likelihoods[:, 1] = [1/3, 1/3, 0]
        conditional_likelihoods[:, 2] = [0, 1/3, 1/3]
        conditional_likelihoods[:, 3:5] = [[1/3], [1/3], [1/3]]
        conditional_likelihoods[:, 5] = [0, 1/3, 1/3]
        conditional_likelihoods[:, 6] = [0, 0, 1/3]
        conditional_likelihoods[:, 7] = [1/3, 1/3, 1/3]

        expected_marginals = np.empty((k_regimes, nobs))
        expected_marginals[:, 0] = [1/3, 1/3, 1/3]
        expected_marginals[:, 1] = [1/2, 1/2, 0]
        expected_marginals[:, 2:4] = [[0], [1], [0]]
        expected_marginals[:, 4:6] = [[0], [1/2], [1/2]]
        expected_marginals[:, 6:8] = [[0], [0], [1]]

        cy_results = markov_switching.cy_hamilton_filter_log(
            initial_probabilities, regime_transition,
            np.log(conditional_likelihoods + 1e-20), model_order=0)
        assert_allclose(cy_results[0], expected_marginals, atol=1e-15)

    def test_hamilton_filter_shape_checks(self):
        k_regimes = 3
        nobs = 8
        order = 3

        initial_probabilities = np.ones(k_regimes) / k_regimes
        regime_transition = np.ones((k_regimes, k_regimes, nobs)) / k_regimes
        conditional_loglikelihoods = np.ones(order * (k_regimes,) + (nobs,))

        with assert_raises(ValueError):
            markov_switching.cy_hamilton_filter_log(
                initial_probabilities, regime_transition,
                conditional_loglikelihoods, model_order=order)


class TestFedFundsConstL1(MarkovRegression):
    # Results from Stata, see http://www.stata.com/manuals14/tsmswitch.pdf
    @classmethod
    def setup_class(cls):
        true = {
            'params': np.r_[.6378175, .1306295, .724457, -.0988764,
                            .7631424, 1.061174, .6915759**2],
            'llf': -264.71069,
            'llf_fit': -264.71069,
            'llf_fit_em': -264.71153,
            'bse_oim': np.r_[.1202616, .0495924, .2886657, .1183838, .0337234,
                             .0185031, np.nan]
        }
        super().setup_class(
            true, fedfunds[1:], k_regimes=2, exog=fedfunds[:-1]
        )

    def test_bse(self):
        # Cannot compare last element of bse because we estimate sigma^2 rather
        # than sigma^2
        bse = self.result.cov_params_approx.diagonal()**0.5
        assert_allclose(bse[:-1], self.true['bse_oim'][:-1], atol=1e-6)


class TestFedFundsConstL1Exog(MarkovRegression):
    # Results from Stata, see http://www.stata.com/manuals14/tsmswitch.pdf
    @classmethod
    def setup_class(cls):
        path = os.path.join(current_path, 'results',
                            'results_predict_fedfunds.csv')
        results = pd.read_csv(path)

        true = {
            'params': np.r_[.7279288, .2114578, .6554954, -.0944924,
                            .8314458, .9292574, .1355425, .0343072,
                            -.0273928, .2125275, .5764495**2],
            'llf': -229.25614,
            'llf_fit': -229.25614,
            'llf_fit_em': -229.25624,
            'bse_oim': np.r_[.0929915, .0641179, .1373889, .1279231, .0333236,
                             .0270852, .0294113, .0240138, .0408057, .0297351,
                             np.nan],
            'predict0': results.iloc[4:]['constL1exog_syhat1'],
            'predict1': results.iloc[4:]['constL1exog_syhat2'],
            'predict_smoothed': results.iloc[4:]['constL1exog_syhat'],
        }
        super().setup_class(
            true, fedfunds[4:], k_regimes=2,
            exog=np.c_[fedfunds[3:-1], ogap[4:], inf[4:]])

    def test_fit(self, **kwargs):
        kwargs.setdefault('em_iter', 10)
        kwargs.setdefault('maxiter', 100)
        super().test_fit(**kwargs)

    def test_predict(self):
        # Predictions conditional on regime (the same no matter which
        # probabilities are selected)
        for name in ['predicted', 'filtered', 'smoothed', None]:
            actual = self.model.predict(
                self.true['params'], probabilities=name, conditional=True)
            assert_allclose(actual[0],
                            self.true['predict0'], atol=1e-5)
            assert_allclose(actual[1],
                            self.true['predict1'], atol=1e-5)

        # Smoothed
        actual = self.model.predict(
            self.true['params'], probabilities='smoothed')
        assert_allclose(actual, self.true['predict_smoothed'], atol=1e-5)
        actual = self.model.predict(
            self.true['params'], probabilities=None)
        assert_allclose(actual, self.true['predict_smoothed'], atol=1e-5)

        actual = self.result.predict(probabilities='smoothed')
        assert_allclose(actual, self.true['predict_smoothed'], atol=1e-5)
        actual = self.result.predict(probabilities=None)
        assert_allclose(actual, self.true['predict_smoothed'], atol=1e-5)

    def test_bse(self):
        # Cannot compare last element of bse because we estimate sigma^2 rather
        # than sigma^2
        bse = self.result.cov_params_approx.diagonal()**0.5
        assert_allclose(bse[:-1], self.true['bse_oim'][:-1], atol=1e-7)


class TestFedFundsConstL1Exog3(MarkovRegression):
    # Results from Stata, see http://www.stata.com/manuals14/tsmswitch.pdf
    @classmethod
    def setup_class(cls):
        true = {
            'params': np.r_[.7253684, .1641252, .6178282,
                            .2564055, .7994204, .3821718,
                            .5261292, -.0034106, .6015991,
                            .8464551, .9690088, .4178913,
                            .1201952, .0464136, .1075357,
                            -.0425603, .1298906, .9099168,
                            .438375**2],
            'llf': -189.89493,
            'llf_fit': -182.27188,
            'llf_fit_em': -226.88581
        }
        super().setup_class(
            true, fedfunds[4:], k_regimes=3,
            exog=np.c_[fedfunds[3:-1], ogap[4:], inf[4:]])

    def test_fit(self, **kwargs):
        kwargs['search_reps'] = 20
        np.random.seed(1234)
        super().test_fit(**kwargs)


class TestAreturnsConstL1Variance(MarkovRegression):
    # Results from Stata, see http://www.stata.com/manuals14/tsmswitch.pdf
    @classmethod
    def setup_class(cls):
        true = {
            'params': np.r_[.7530865, .6825357, .7641424, 1.972771, .0790744,
                            .527953, .5895792**2, 1.605333**2],
            'llf': -745.7977,
            'llf_fit': -745.7977,
            'llf_fit_em': -745.83654,
            'bse_oim': np.r_[.0634387, .0662574, .0782852, .2784204, .0301862,
                             .0857841, np.nan, np.nan]
        }
        super().setup_class(
            true, areturns[1:], k_regimes=2, exog=areturns[:-1],
            switching_variance=True)

    def test_fit(self, **kwargs):
        kwargs.setdefault('em_iter', 10)
        kwargs.setdefault('maxiter', 100)
        super().test_fit(**kwargs)

    def test_bse(self):
        # Cannot compare last two element of bse because we estimate sigma^2
        # rather than sigma
        bse = self.result.cov_params_approx.diagonal()**0.5
        assert_allclose(bse[:-2], self.true['bse_oim'][:-2], atol=1e-7)


class TestMumpspcNoconstL1Variance(MarkovRegression):
    # Results from Stata, see http://www.stata.com/manuals14/tsmswitch.pdf
    @classmethod
    def setup_class(cls):
        true = {
            'params': np.r_[.762733, .1473767, .420275, .9847369, .0562405**2,
                            .2611362**2],
            'llf': 131.7225,
            'llf_fit': 131.7225,
            'llf_fit_em': 131.7175
        }
        super().setup_class(
            true, mumpspc[1:], k_regimes=2, trend='n', exog=mumpspc[:-1],
            switching_variance=True, atol=1e-4)


gh5380_series = [
    -2.28833689e-04, -4.83175915e-05, -4.93236435e-04, -2.92251109e-04,
    -4.03295179e-04, -2.01009784e-04, 2.85109215e-04, 1.85710827e-04,
    -2.04503930e-04, -9.06050944e-05, 1.06177818e-03, -4.97251569e-04,
    7.16669183e-04, 1.81161945e-04, -6.87139289e-04, -4.85979721e-05,
    -1.68387973e-05, -4.43257154e-04, 5.37457383e-04, -8.17822909e-04,
    6.06814040e-04, -1.05481968e-05, -1.15698730e-04, -8.57638924e-04,
    8.98143953e-05, 1.70216877e-04, -2.84874742e-04, -2.81999910e-05,
    -3.43787510e-04, -5.74808548e-04, -6.47844682e-05, 1.08667397e-04,
    -1.28790034e-04, 7.07615608e-05, 1.17633886e-04, 8.98163443e-07,
    1.03860559e-05, -2.50298430e-06, -9.09673045e-07, -1.94645227e-05,
    -5.21373877e-05, -1.38431813e-04, 1.11092157e-04, 3.09541647e-05,
    -1.57756488e-04, 4.93176285e-05, -4.87488913e-05, 2.08097182e-04,
    -7.08581132e-05, -8.12182224e-06, 8.03090891e-05, -1.02981093e-05,
    2.07550066e-05, 3.75383116e-05, -3.96307514e-05, -1.12007982e-05,
    -4.00958429e-05, 2.01206738e-05, -2.95802891e-05, 1.26422639e-04,
    -3.95617289e-05, 1.94523738e-04, 4.63964270e-05, 2.07712951e-05,
    2.92417981e-04, 2.15111259e-04, 1.23762736e-04, 7.78305555e-05,
    -3.44022033e-04, -2.84249114e-05, -1.08351559e-04, -1.76944408e-04,
    7.99332875e-05, 5.09785758e-04, 3.24506365e-04, -2.47943721e-04,
    2.22346033e-04, -6.66164024e-05, 2.00911206e-04, 4.58750048e-04,
    -1.55390954e-04, 3.67913831e-04, 1.08274314e-04, -1.27400351e-04,
    1.50654063e-04, 2.69976025e-04, 4.51532804e-05, -2.21579419e-04,
    1.54792373e-04, 1.98397630e-04, 3.96943388e-04, 6.18663277e-04,
    8.86151537e-04, 6.16949774e-04, 5.44538857e-03, 7.33282114e-05,
    3.93393013e-03, 6.66556165e-04, 6.18815111e-03, -6.40775088e-03,
    -1.98864768e-03, -3.42828364e-05, -7.92023127e-04, -1.64656177e-03,
    -3.31112273e-03, -2.63499766e-03, -2.55118821e-04, -3.63858500e-04,
    -6.58065806e-04, -3.38492405e-04, -1.10458161e-03, -2.08228620e-07,
    -7.21562092e-05, 7.92946105e-05, 1.25472212e-05, -1.27190212e-04,
    3.94913196e-05, -1.87353437e-05, -6.57929565e-06, 1.61597869e-05,
    1.90031845e-05, 8.76129739e-03, -8.34566734e-03, -5.82053353e-05,
    1.28770725e-04, 2.38160836e-04, -1.84590083e-05, 3.10903734e-05,
    1.89366264e-05, 7.61985181e-05, 3.20208326e-04, -6.90388272e-05,
    2.09928647e-05, -3.14720707e-05, 1.32614437e-04, 1.10385710e-04,
    -4.91256074e-05, -1.16967974e-04, 4.88977021e-05, 6.19231048e-05,
    2.95994863e-04, 1.91592526e-03, -2.22511393e-04, -8.04871984e-04,
    -6.66886069e-04, -3.80754506e-04, -4.06613941e-05, -4.66164600e-04,
    -1.48077847e-04, 1.31787219e-04, 5.12392307e-05, -2.61346302e-05,
    -1.46071993e-04, -3.17839957e-04, -2.52143621e-04, -1.73768102e-04,
    -6.88107229e-05, -2.14130430e-05, 5.25348170e-07, -9.80972737e-05,
    -6.90120785e-05, -1.28208365e-04, 2.09816018e-05, 1.02199123e-05,
    2.08633684e-05, 1.05609818e-05, 1.35835269e-05, 1.50357742e-04,
    5.72303827e-05, 1.27501839e-04, 1.20706157e-04, 8.27340411e-06,
    3.77430540e-05, 8.10426523e-05, 9.99617511e-05, 1.21103273e-04,
    1.10255996e-04, 6.89558159e-05, 3.22442426e-05, 8.49416109e-05,
    4.05396296e-04, 1.69699170e-04, 1.10892701e-04, 7.21186150e-05,
    1.24978570e-04, -2.91922347e-04, 5.50447651e-05, -1.68467417e-04,
    -7.06084859e-05, -3.73250482e-04, -1.49829854e-04, -7.23752254e-05,
    -9.80670951e-05, -3.00179050e-05, 4.15704679e-05, 2.87503307e-05,
    -5.09988933e-05, -9.55791344e-06, -2.74369249e-05, -2.23330122e-05,
    9.74727035e-06, -8.58146574e-06, 2.97704340e-05, -1.12990428e-05,
    -5.90507361e-05, 9.50772649e-07, -7.35502474e-07, 5.66683332e-07,
    -9.81320792e-06, -6.60845729e-07, -3.19848073e-05, -1.10779191e-04]


@pytest.mark.smoke
def test_avoid_underflow():
    # See GH5380 for example and dataset demonstrating underflow. This test is
    # the "Example 1" given there.

    # The underlying problem was an underflow error in computing the smoothed
    # states, which resulted in NaN values in e.g. smoothed_joint_probabilities
    # This causes NaNs in the EM algorithm (e.g. _em_regime_transition), and
    # so the _fit_em method would return NaN parameters, as would the fit
    # method.

    # This test runs the smoother at the last set of good parameter values
    # (i.e. these parameters were generated by _fit_em and did not have any
    # NaN values in them, but the smoothed_joint_probabilities computed at
    # those parameters have NaNs in them, so that the next set of parameters
    # would have NaNs). The test then checks that the underflow problem has
    # been resolved, i.e. that there are no NaNs in the predicted, filtered,
    # and smoothed joint probabilities.
    m = markov_regression.MarkovRegression(
        gh5380_series, k_regimes=2, switching_variance=True)
    # To get these params, the following command was used:
    # params = m._fit_em(maxiter=1).params
    params = np.array([
        6.97337611e-01, 6.26116329e-01, -6.41266551e-06, 3.81141202e-06,
        4.72462327e-08, 4.45291473e-06])
    res = m.smooth(params)

    assert not np.any(np.isnan(res.predicted_joint_probabilities))
    assert not np.any(np.isnan(res.filtered_joint_probabilities))
    assert not np.any(np.isnan(res.smoothed_joint_probabilities))


def test_exog_tvtp():
    exog = np.ones_like(fedfunds)

    mod1 = markov_regression.MarkovRegression(fedfunds, k_regimes=2)
    mod2 = markov_regression.MarkovRegression(fedfunds, k_regimes=2,
                                              exog_tvtp=exog)

    params = np.r_[0.98209618, 0.05036498, 3.70877542, 9.55676298, 4.44181911]
    params_tvtp = params.copy()
    params_tvtp[0] = np.squeeze(
        mod2._untransform_logistic(np.r_[0.], np.r_[1 - params[0]])
    )
    params_tvtp[1] = np.squeeze(
        mod2._untransform_logistic(np.r_[0.], np.r_[1 - params[1]])
    )

    res1 = mod1.smooth(params)
    res2 = mod2.smooth(params_tvtp)

    assert_allclose(res2.llf_obs, res1.llf_obs)
    assert_allclose(res2.regime_transition - res1.regime_transition, 0,
                    atol=1e-15)
    assert_allclose(res2.predicted_joint_probabilities,
                    res1.predicted_joint_probabilities)
    assert_allclose(res2.filtered_joint_probabilities,
                    res1.filtered_joint_probabilities)
    assert_allclose(res2.smoothed_joint_probabilities,
                    res1.smoothed_joint_probabilities)
