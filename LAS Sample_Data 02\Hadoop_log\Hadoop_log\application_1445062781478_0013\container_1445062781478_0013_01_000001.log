2015-10-17 15:37:58,425 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445062781478_0013_000001
2015-10-17 15:37:59,190 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 15:37:59,190 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 13 cluster_timestamp: 1445062781478 } attemptId: 1 } keyId: 471522253)
2015-10-17 15:37:59,503 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 15:38:00,581 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 15:38:00,675 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 15:38:00,722 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 15:38:00,722 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 15:38:00,722 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 15:38:00,722 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 15:38:00,722 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 15:38:00,737 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 15:38:00,737 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 15:38:00,737 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 15:38:00,831 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:38:00,878 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:38:00,909 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 15:38:00,940 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 15:38:01,018 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 15:38:01,409 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 15:38:01,518 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 15:38:01,518 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 15:38:01,534 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445062781478_0013 to jobTokenSecretManager
2015-10-17 15:38:01,768 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445062781478_0013 because: not enabled; too many maps; too much input;
2015-10-17 15:38:01,784 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445062781478_0013 = 1256521728. Number of splits = 10
2015-10-17 15:38:01,784 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445062781478_0013 = 1
2015-10-17 15:38:01,784 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0013Job Transitioned from NEW to INITED
2015-10-17 15:38:01,784 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445062781478_0013.
2015-10-17 15:38:01,831 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 15:38:01,847 INFO [Socket Reader #1 for port 49460] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 49460
2015-10-17 15:38:01,878 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 15:38:01,878 INFO [IPC Server listener on 49460] org.apache.hadoop.ipc.Server: IPC Server listener on 49460: starting
2015-10-17 15:38:01,878 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 15:38:01,893 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at 04DN8IQ.fareast.corp.microsoft.com/***********:49460
2015-10-17 15:38:01,987 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 15:38:02,003 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 15:38:02,018 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 15:38:02,018 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 15:38:02,018 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 15:38:02,018 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 15:38:02,018 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 15:38:02,034 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 49467
2015-10-17 15:38:02,034 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 15:38:02,097 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_49467_mapreduce____m1z7u1\webapp
2015-10-17 15:38:02,409 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:49467
2015-10-17 15:38:02,409 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 49467
2015-10-17 15:38:02,956 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 15:38:02,972 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445062781478_0013
2015-10-17 15:38:02,972 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 15:38:02,972 INFO [Socket Reader #1 for port 49470] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 49470
2015-10-17 15:38:02,987 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 15:38:02,987 INFO [IPC Server listener on 49470] org.apache.hadoop.ipc.Server: IPC Server listener on 49470: starting
2015-10-17 15:38:03,003 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 15:38:03,003 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 15:38:03,003 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 15:38:03,065 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 15:38:03,175 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 15:38:03,175 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 15:38:03,175 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 15:38:03,190 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 15:38:03,190 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0013Job Transitioned from INITED to SETUP
2015-10-17 15:38:03,190 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 15:38:03,206 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0013Job Transitioned from SETUP to RUNNING
2015-10-17 15:38:03,237 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,253 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:03,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 15:38:03,269 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 15:38:03,284 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 15:38:03,315 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445062781478_0013, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0013/job_1445062781478_0013_1.jhist
2015-10-17 15:38:04,175 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 15:38:04,237 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:22528, vCores:-13> knownNMs=5
2015-10-17 15:38:04,237 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:22528, vCores:-13>
2015-10-17 15:38:04,237 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:05,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 4
2015-10-17 15:38:05,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_01_000002 to attempt_1445062781478_0013_m_000000_0
2015-10-17 15:38:05,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_01_000003 to attempt_1445062781478_0013_m_000001_0
2015-10-17 15:38:05,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_01_000004 to attempt_1445062781478_0013_m_000002_0
2015-10-17 15:38:05,269 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_01_000005 to attempt_1445062781478_0013_m_000003_0
2015-10-17 15:38:05,269 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:18432, vCores:-17>
2015-10-17 15:38:05,269 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:05,269 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:4 RackLocal:0
2015-10-17 15:38:05,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:05,347 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0013/job.jar
2015-10-17 15:38:05,347 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0013/job.xml
2015-10-17 15:38:05,347 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 15:38:05,347 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 15:38:05,347 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 15:38:05,409 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:05,425 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:05,425 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:05,425 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:05,425 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:05,425 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:05,425 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:05,425 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_01_000002 taskAttempt attempt_1445062781478_0013_m_000000_0
2015-10-17 15:38:05,425 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_01_000003 taskAttempt attempt_1445062781478_0013_m_000001_0
2015-10-17 15:38:05,425 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_01_000004 taskAttempt attempt_1445062781478_0013_m_000002_0
2015-10-17 15:38:05,425 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_01_000005 taskAttempt attempt_1445062781478_0013_m_000003_0
2015-10-17 15:38:05,425 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000000_0
2015-10-17 15:38:05,425 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000002_0
2015-10-17 15:38:05,425 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000003_0
2015-10-17 15:38:05,425 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000001_0
2015-10-17 15:38:05,425 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:38:05,456 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:38:05,456 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:38:05,456 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 15:38:05,534 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000002_0 : 13562
2015-10-17 15:38:05,534 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000003_0 : 13562
2015-10-17 15:38:05,534 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000001_0 : 13562
2015-10-17 15:38:05,534 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000000_0 : 13562
2015-10-17 15:38:05,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000000_0] using containerId: [container_1445062781478_0013_01_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000002_0] using containerId: [container_1445062781478_0013_01_000004 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000001_0] using containerId: [container_1445062781478_0013_01_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000003_0] using containerId: [container_1445062781478_0013_01_000005 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000000
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000002
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000001
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000003
2015-10-17 15:38:05,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:06,284 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:17408, vCores:-18> knownNMs=5
2015-10-17 15:38:06,284 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:38:06,284 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:06,284 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_01_000006 to attempt_1445062781478_0013_m_000004_0
2015-10-17 15:38:06,284 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:17408, vCores:-18>
2015-10-17 15:38:06,284 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:06,284 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:4 RackLocal:1
2015-10-17 15:38:06,284 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:06,284 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:06,284 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_01_000006 taskAttempt attempt_1445062781478_0013_m_000004_0
2015-10-17 15:38:06,284 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000004_0
2015-10-17 15:38:06,284 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64484
2015-10-17 15:38:06,315 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000004_0 : 13562
2015-10-17 15:38:06,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000004_0] using containerId: [container_1445062781478_0013_01_000006 on NM: [04DN8IQ.fareast.corp.microsoft.com:64484]
2015-10-17 15:38:06,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:06,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000004
2015-10-17 15:38:06,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:07,300 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:15360, vCores:-20> knownNMs=5
2015-10-17 15:38:07,300 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 15:38:07,300 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:07,300 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_01_000007 to attempt_1445062781478_0013_m_000005_0
2015-10-17 15:38:07,300 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:07,300 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:07,315 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_01_000007 taskAttempt attempt_1445062781478_0013_m_000005_0
2015-10-17 15:38:07,315 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000005_0
2015-10-17 15:38:07,315 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:38:07,315 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:07,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_01_000008 to attempt_1445062781478_0013_m_000006_0
2015-10-17 15:38:07,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:15360, vCores:-20>
2015-10-17 15:38:07,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:07,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:4 RackLocal:3
2015-10-17 15:38:07,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:07,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:07,315 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_01_000008 taskAttempt attempt_1445062781478_0013_m_000006_0
2015-10-17 15:38:07,315 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000006_0
2015-10-17 15:38:07,315 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 15:38:07,362 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000005_0 : 13562
2015-10-17 15:38:07,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000005_0] using containerId: [container_1445062781478_0013_01_000007 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:51951]
2015-10-17 15:38:07,362 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000006_0 : 13562
2015-10-17 15:38:07,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:07,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000006_0] using containerId: [container_1445062781478_0013_01_000008 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 15:38:07,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:07,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000005
2015-10-17 15:38:07,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:07,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000006
2015-10-17 15:38:07,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:08,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:12288, vCores:-23> knownNMs=5
2015-10-17 15:38:08,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 15:38:08,315 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:08,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_01_000009 to attempt_1445062781478_0013_m_000007_0
2015-10-17 15:38:08,315 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:08,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:08,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_01_000010 to attempt_1445062781478_0013_m_000008_0
2015-10-17 15:38:08,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:12288, vCores:-23>
2015-10-17 15:38:08,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:08,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:4 RackLocal:5
2015-10-17 15:38:08,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:08,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:08,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:08,331 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_01_000009 taskAttempt attempt_1445062781478_0013_m_000007_0
2015-10-17 15:38:08,331 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_01_000010 taskAttempt attempt_1445062781478_0013_m_000008_0
2015-10-17 15:38:08,331 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000007_0
2015-10-17 15:38:08,331 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000008_0
2015-10-17 15:38:08,331 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 15:38:08,331 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64484
2015-10-17 15:38:08,378 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000008_0 : 13562
2015-10-17 15:38:08,378 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000008_0] using containerId: [container_1445062781478_0013_01_000010 on NM: [04DN8IQ.fareast.corp.microsoft.com:64484]
2015-10-17 15:38:08,378 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:08,378 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000008
2015-10-17 15:38:08,378 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:08,565 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000007_0 : 13562
2015-10-17 15:38:08,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000007_0] using containerId: [container_1445062781478_0013_01_000009 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 15:38:08,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:08,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000007
2015-10-17 15:38:08,565 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:09,081 INFO [Socket Reader #1 for port 49470] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:38:09,112 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000006 asked for a task
2015-10-17 15:38:09,112 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000006 given task: attempt_1445062781478_0013_m_000004_0
2015-10-17 15:38:09,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 0 newContainers=3 finishedContainers=0 resourcelimit=<memory:8192, vCores:-27> knownNMs=5
2015-10-17 15:38:09,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 3
2015-10-17 15:38:09,362 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0013_01_000011 to attempt_1445062781478_0013_m_000009_0
2015-10-17 15:38:09,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Releasing unassigned and invalid container Container: [ContainerId: container_1445062781478_0013_01_000012, NodeId: MININT-FNANLI5.fareast.corp.microsoft.com:64642, NodeHttpAddress: MININT-FNANLI5.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 20, Token: Token { kind: ContainerToken, service: *************:64642 }, ]. RM may have assignment issues
2015-10-17 15:38:09,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 15:38:09,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Releasing unassigned and invalid container Container: [ContainerId: container_1445062781478_0013_01_000013, NodeId: 04DN8IQ.fareast.corp.microsoft.com:64484, NodeHttpAddress: 04DN8IQ.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 20, Token: Token { kind: ContainerToken, service: ***********:64484 }, ]. RM may have assignment issues
2015-10-17 15:38:09,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-27>
2015-10-17 15:38:09,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:09,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:12 ContRel:2 HostLocal:4 RackLocal:6
2015-10-17 15:38:09,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 15:38:09,362 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0013_01_000011 taskAttempt attempt_1445062781478_0013_m_000009_0
2015-10-17 15:38:09,362 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0013_m_000009_0
2015-10-17 15:38:09,362 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 15:38:09,487 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0013_m_000009_0 : 13562
2015-10-17 15:38:09,487 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0013_m_000009_0] using containerId: [container_1445062781478_0013_01_000011 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:51951]
2015-10-17 15:38:09,487 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0013_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 15:38:09,487 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0013_m_000009
2015-10-17 15:38:09,487 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0013_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 15:38:09,816 INFO [Socket Reader #1 for port 49470] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:38:09,831 INFO [Socket Reader #1 for port 49470] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:38:09,847 INFO [Socket Reader #1 for port 49470] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:38:09,847 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000004 asked for a task
2015-10-17 15:38:09,847 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000005 asked for a task
2015-10-17 15:38:09,847 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000004 given task: attempt_1445062781478_0013_m_000002_0
2015-10-17 15:38:09,847 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000005 given task: attempt_1445062781478_0013_m_000003_0
2015-10-17 15:38:09,894 INFO [IPC Server handler 6 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000003 asked for a task
2015-10-17 15:38:09,894 INFO [IPC Server handler 6 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000003 given task: attempt_1445062781478_0013_m_000001_0
2015-10-17 15:38:09,909 INFO [Socket Reader #1 for port 49470] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:38:09,941 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000002 asked for a task
2015-10-17 15:38:09,941 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000002 given task: attempt_1445062781478_0013_m_000000_0
2015-10-17 15:38:10,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=4 release= 2 newContainers=1 finishedContainers=2 resourcelimit=<memory:9216, vCores:-26> knownNMs=5
2015-10-17 15:38:10,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_01_000013
2015-10-17 15:38:10,409 ERROR [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Container complete event for unknown container id container_1445062781478_0013_01_000013
2015-10-17 15:38:10,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_01_000012
2015-10-17 15:38:10,409 ERROR [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Container complete event for unknown container id container_1445062781478_0013_01_000012
2015-10-17 15:38:10,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 15:38:10,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Cannot assign container Container: [ContainerId: container_1445062781478_0013_01_000014, NodeId: MININT-75DGDAM1.fareast.corp.microsoft.com:51951, NodeHttpAddress: MININT-75DGDAM1.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 20, Token: Token { kind: ContainerToken, service: ************:51951 }, ] for a map as either  container memory less than required <memory:1024, vCores:1> or no pending map tasks - maps.isEmpty=true
2015-10-17 15:38:10,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-26>
2015-10-17 15:38:10,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:10,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:13 ContRel:3 HostLocal:4 RackLocal:6
2015-10-17 15:38:11,081 INFO [Socket Reader #1 for port 49470] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:38:11,097 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000007 asked for a task
2015-10-17 15:38:11,097 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000007 given task: attempt_1445062781478_0013_m_000005_0
2015-10-17 15:38:11,425 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0013: ask=0 release= 1 newContainers=0 finishedContainers=1 resourcelimit=<memory:8192, vCores:-27> knownNMs=5
2015-10-17 15:38:11,425 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0013_01_000014
2015-10-17 15:38:11,425 ERROR [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Container complete event for unknown container id container_1445062781478_0013_01_000014
2015-10-17 15:38:11,425 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-27>
2015-10-17 15:38:11,425 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:12,425 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-28>
2015-10-17 15:38:12,425 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:12,612 INFO [Socket Reader #1 for port 49470] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:38:12,659 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000010 asked for a task
2015-10-17 15:38:12,659 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000010 given task: attempt_1445062781478_0013_m_000008_0
2015-10-17 15:38:12,987 INFO [Socket Reader #1 for port 49470] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:38:13,019 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000011 asked for a task
2015-10-17 15:38:13,019 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000011 given task: attempt_1445062781478_0013_m_000009_0
2015-10-17 15:38:13,487 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-30>
2015-10-17 15:38:13,487 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:14,503 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-17 15:38:14,503 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:15,519 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:15,519 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:15,550 INFO [Socket Reader #1 for port 49470] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:38:15,675 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000008 asked for a task
2015-10-17 15:38:15,675 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000008 given task: attempt_1445062781478_0013_m_000006_0
2015-10-17 15:38:17,284 INFO [IPC Server handler 20 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.094373755
2015-10-17 15:38:17,284 INFO [IPC Server handler 14 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.075882845
2015-10-17 15:38:17,284 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.06806425
2015-10-17 15:38:17,409 INFO [Socket Reader #1 for port 49470] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0013 (auth:SIMPLE)
2015-10-17 15:38:17,456 INFO [IPC Server handler 6 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.10196821
2015-10-17 15:38:17,503 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0013_m_000009 asked for a task
2015-10-17 15:38:17,503 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0013_m_000009 given task: attempt_1445062781478_0013_m_000007_0
2015-10-17 15:38:17,738 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 15:38:17,738 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:20,394 INFO [IPC Server handler 10 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.1066108
2015-10-17 15:38:20,394 INFO [IPC Server handler 14 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.093819976
2015-10-17 15:38:20,394 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.09737582
2015-10-17 15:38:20,488 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.10635664
2015-10-17 15:38:20,800 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.019444423
2015-10-17 15:38:21,878 INFO [IPC Server handler 3 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.017851802
2015-10-17 15:38:22,988 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 15:38:22,988 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 15:38:23,534 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.10635664
2015-10-17 15:38:23,769 INFO [IPC Server handler 3 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.106493875
2015-10-17 15:38:23,769 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.1066108
2015-10-17 15:38:23,769 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.10660437
2015-10-17 15:38:24,831 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.04149922
2015-10-17 15:38:25,285 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.03928281
2015-10-17 15:38:26,269 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.020515375
2015-10-17 15:38:26,706 INFO [IPC Server handler 6 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.10635664
2015-10-17 15:38:26,816 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.106493875
2015-10-17 15:38:26,816 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.1066108
2015-10-17 15:38:26,831 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.10660437
2015-10-17 15:38:28,425 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.046246707
2015-10-17 15:38:29,160 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.058621403
2015-10-17 15:38:29,410 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.022469634
2015-10-17 15:38:29,800 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.12311611
2015-10-17 15:38:29,894 INFO [IPC Server handler 3 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.10660437
2015-10-17 15:38:29,894 INFO [IPC Server handler 13 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.106493875
2015-10-17 15:38:29,894 INFO [IPC Server handler 3 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.123257026
2015-10-17 15:38:30,410 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.018235836
2015-10-17 15:38:31,191 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.01237338
2015-10-17 15:38:31,456 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.04982748
2015-10-17 15:38:32,363 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.060894996
2015-10-17 15:38:32,472 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.0814192
2015-10-17 15:38:32,941 INFO [IPC Server handler 17 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.17096159
2015-10-17 15:38:33,019 INFO [IPC Server handler 12 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.11333601
2015-10-17 15:38:33,050 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.12668905
2015-10-17 15:38:33,050 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.106493875
2015-10-17 15:38:33,253 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.023447443
2015-10-17 15:38:33,660 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.025727065
2015-10-17 15:38:34,222 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.015303903
2015-10-17 15:38:34,488 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.051782984
2015-10-17 15:38:35,832 INFO [IPC Server handler 10 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.079208985
2015-10-17 15:38:35,957 INFO [IPC Server handler 13 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.18010126
2015-10-17 15:38:36,082 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.13483094
2015-10-17 15:38:36,082 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.11203393
2015-10-17 15:38:36,082 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.11919787
2015-10-17 15:38:36,238 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.09444484
2015-10-17 15:38:36,457 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.027028728
2015-10-17 15:38:36,925 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.030610386
2015-10-17 15:38:37,269 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.01921344
2015-10-17 15:38:37,519 INFO [IPC Server handler 10 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.054061987
2015-10-17 15:38:39,082 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.18563919
2015-10-17 15:38:39,285 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.14101847
2015-10-17 15:38:39,285 INFO [IPC Server handler 10 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.14004312
2015-10-17 15:38:39,285 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.12732336
2015-10-17 15:38:39,441 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.105309054
2015-10-17 15:38:39,722 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.029961118
2015-10-17 15:38:39,722 INFO [IPC Server handler 22 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.09965749
2015-10-17 15:38:40,129 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.03745183
2015-10-17 15:38:40,316 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.02214112
2015-10-17 15:38:40,550 INFO [IPC Server handler 22 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.05601466
2015-10-17 15:38:42,129 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.19158794
2015-10-17 15:38:42,379 INFO [IPC Server handler 22 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.15502384
2015-10-17 15:38:42,379 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.14883612
2015-10-17 15:38:42,379 INFO [IPC Server handler 23 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.13670984
2015-10-17 15:38:42,675 INFO [IPC Server handler 9 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.14041822
2015-10-17 15:38:42,847 INFO [IPC Server handler 15 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.10685723
2015-10-17 15:38:42,879 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.03419334
2015-10-17 15:38:43,222 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.042661197
2015-10-17 15:38:43,347 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.026058549
2015-10-17 15:38:43,582 INFO [IPC Server handler 9 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.058295794
2015-10-17 15:38:45,222 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.21299656
2015-10-17 15:38:45,441 INFO [IPC Server handler 9 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.16642052
2015-10-17 15:38:45,441 INFO [IPC Server handler 15 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.15306766
2015-10-17 15:38:45,785 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.17488812
2015-10-17 15:38:45,910 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.15752049
2015-10-17 15:38:46,051 INFO [IPC Server handler 12 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.03973183
2015-10-17 15:38:46,363 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.050804116
2015-10-17 15:38:46,394 INFO [IPC Server handler 10 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.02963486
2015-10-17 15:38:46,597 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.062203903
2015-10-17 15:38:46,910 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.10685723
2015-10-17 15:38:48,816 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.19209063
2015-10-17 15:38:48,816 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.19211523
2015-10-17 15:38:49,441 INFO [IPC Server handler 9 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.03321461
2015-10-17 15:38:49,629 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.06415912
2015-10-17 15:38:49,629 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.16088612
2015-10-17 15:38:49,629 INFO [IPC Server handler 1 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.1800219
2015-10-17 15:38:49,785 INFO [IPC Server handler 6 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.04136009
2015-10-17 15:38:49,863 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.054712642
2015-10-17 15:38:50,676 INFO [IPC Server handler 6 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.10685723
2015-10-17 15:38:51,879 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.19209063
2015-10-17 15:38:51,894 INFO [IPC Server handler 28 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.19211523
2015-10-17 15:38:52,457 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.03582221
2015-10-17 15:38:52,660 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.06871816
2015-10-17 15:38:52,722 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.19212553
2015-10-17 15:38:52,785 INFO [IPC Server handler 28 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.19352835
2015-10-17 15:38:52,863 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.2331883
2015-10-17 15:38:52,941 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.04658845
2015-10-17 15:38:52,957 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.06480621
2015-10-17 15:38:53,863 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.10685723
2015-10-17 15:38:54,973 INFO [IPC Server handler 20 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.19209063
2015-10-17 15:38:55,098 INFO [IPC Server handler 14 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.19793084
2015-10-17 15:38:55,504 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.039403982
2015-10-17 15:38:55,676 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.07197341
2015-10-17 15:38:55,801 INFO [IPC Server handler 28 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.19212553
2015-10-17 15:38:55,941 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.27696857
2015-10-17 15:38:56,035 INFO [IPC Server handler 14 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.214232
2015-10-17 15:38:56,144 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.052433196
2015-10-17 15:38:56,191 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.07001955
2015-10-17 15:38:57,051 INFO [IPC Server handler 14 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.1136615
2015-10-17 15:38:58,144 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.21183048
2015-10-17 15:38:58,207 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.25022107
2015-10-17 15:38:58,519 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.043314278
2015-10-17 15:38:58,707 INFO [IPC Server handler 1 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.07686013
2015-10-17 15:38:58,879 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.19212553
2015-10-17 15:38:59,019 INFO [IPC Server handler 14 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.27696857
2015-10-17 15:38:59,207 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.2250318
2015-10-17 15:38:59,394 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.059597574
2015-10-17 15:38:59,394 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.076856926
2015-10-17 15:39:00,941 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.119198576
2015-10-17 15:39:01,207 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.23448932
2015-10-17 15:39:01,254 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.26152185
2015-10-17 15:39:01,551 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.048522387
2015-10-17 15:39:01,738 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.081419446
2015-10-17 15:39:01,926 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.19573432
2015-10-17 15:39:02,129 INFO [IPC Server handler 12 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.2872527
2015-10-17 15:39:02,379 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.24753028
2015-10-17 15:39:02,645 INFO [IPC Server handler 23 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.08337172
2015-10-17 15:39:02,645 INFO [IPC Server handler 15 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.061225235
2015-10-17 15:39:04,223 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.12603655
2015-10-17 15:39:04,332 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.24588789
2015-10-17 15:39:04,426 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.2765037
2015-10-17 15:39:04,598 INFO [IPC Server handler 22 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.05275878
2015-10-17 15:39:04,770 INFO [IPC Server handler 1 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.08352426
2015-10-17 15:39:05,020 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.20810862
2015-10-17 15:39:05,207 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.31884363
2015-10-17 15:39:05,926 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.26373842
2015-10-17 15:39:05,926 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.06546096
2015-10-17 15:39:05,973 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.09151366
2015-10-17 15:39:07,379 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.12766632
2015-10-17 15:39:07,426 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.25175232
2015-10-17 15:39:07,520 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.27776006
2015-10-17 15:39:07,645 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.05764207
2015-10-17 15:39:07,832 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.086956725
2015-10-17 15:39:08,145 INFO [IPC Server handler 12 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.21918426
2015-10-17 15:39:08,316 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.34033573
2015-10-17 15:39:09,082 INFO [IPC Server handler 20 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.27543733
2015-10-17 15:39:09,317 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.06806611
2015-10-17 15:39:09,504 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.09835483
2015-10-17 15:39:10,582 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.26054576
2015-10-17 15:39:10,613 INFO [IPC Server handler 22 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.28716508
2015-10-17 15:39:10,692 INFO [IPC Server handler 9 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.06301557
2015-10-17 15:39:10,863 INFO [IPC Server handler 28 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.13320138
2015-10-17 15:39:10,879 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.09086504
2015-10-17 15:39:11,270 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.22993122
2015-10-17 15:39:11,410 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.35010943
2015-10-17 15:39:12,410 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.286241
2015-10-17 15:39:12,629 INFO [IPC Server handler 9 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.07295211
2015-10-17 15:39:13,035 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.10258675
2015-10-17 15:39:13,738 INFO [IPC Server handler 23 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.06643759
2015-10-17 15:39:13,910 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.09412231
2015-10-17 15:39:13,942 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.2773904
2015-10-17 15:39:13,973 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.31362966
2015-10-17 15:39:14,004 INFO [IPC Server handler 20 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.13548382
2015-10-17 15:39:14,363 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.2546826
2015-10-17 15:39:14,770 INFO [IPC Server handler 15 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.3624012
2015-10-17 15:39:16,098 INFO [IPC Server handler 14 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.07718326
2015-10-17 15:39:16,332 INFO [IPC Server handler 17 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.29973805
2015-10-17 15:39:16,442 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.10681946
2015-10-17 15:39:16,895 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.07067099
2015-10-17 15:39:16,989 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.09672714
2015-10-17 15:39:17,067 INFO [IPC Server handler 14 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.27765483
2015-10-17 15:39:17,129 INFO [IPC Server handler 12 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.3422894
2015-10-17 15:39:17,160 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.14069442
2015-10-17 15:39:17,426 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.27349794
2015-10-17 15:39:17,910 INFO [IPC Server handler 28 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.3624012
2015-10-17 15:39:19,489 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.31594294
2015-10-17 15:39:19,535 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.08174464
2015-10-17 15:39:19,926 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.0755542
2015-10-17 15:39:20,020 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.100959904
2015-10-17 15:39:20,114 INFO [IPC Server handler 12 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.28562176
2015-10-17 15:39:20,114 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.10681946
2015-10-17 15:39:20,192 INFO [IPC Server handler 17 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.36319977
2015-10-17 15:39:20,379 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.14622976
2015-10-17 15:39:20,473 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.27772525
2015-10-17 15:39:21,082 INFO [IPC Server handler 14 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.4170679
2015-10-17 15:39:22,989 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.3285468
2015-10-17 15:39:22,989 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.07980798
2015-10-17 15:39:23,082 INFO [IPC Server handler 20 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.10291381
2015-10-17 15:39:23,160 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.31754074
2015-10-17 15:39:23,239 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.36319977
2015-10-17 15:39:23,535 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.10779751
2015-10-17 15:39:23,535 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.092818365
2015-10-17 15:39:23,582 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.27772525
2015-10-17 15:39:24,176 INFO [IPC Server handler 12 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.44789755
2015-10-17 15:39:24,286 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.15209244
2015-10-17 15:39:26,067 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.084349364
2015-10-17 15:39:26,114 INFO [IPC Server handler 12 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.10519451
2015-10-17 15:39:26,129 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.3420474
2015-10-17 15:39:26,332 INFO [IPC Server handler 13 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.36319977
2015-10-17 15:39:26,504 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.33219588
2015-10-17 15:39:26,629 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.28204134
2015-10-17 15:39:26,864 INFO [IPC Server handler 23 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.10975322
2015-10-17 15:39:26,926 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.0947728
2015-10-17 15:39:28,036 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.15990855
2015-10-17 15:39:28,098 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.44789755
2015-10-17 15:39:29,129 INFO [IPC Server handler 20 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.08760695
2015-10-17 15:39:29,176 INFO [IPC Server handler 17 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.10682103
2015-10-17 15:39:29,442 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.34834456
2015-10-17 15:39:29,442 INFO [IPC Server handler 3 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.37577674
2015-10-17 15:39:29,598 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.36323506
2015-10-17 15:39:29,723 INFO [IPC Server handler 22 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.29441723
2015-10-17 15:39:30,832 INFO [IPC Server handler 23 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.11887241
2015-10-17 15:39:31,161 INFO [IPC Server handler 17 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.44789755
2015-10-17 15:39:31,364 INFO [IPC Server handler 13 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.10128656
2015-10-17 15:39:32,208 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.093140826
2015-10-17 15:39:32,270 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.110731624
2015-10-17 15:39:32,770 INFO [IPC Server handler 9 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.31037566
2015-10-17 15:39:32,786 INFO [IPC Server handler 23 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.39309877
2015-10-17 15:39:32,817 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.16870002
2015-10-17 15:39:32,942 INFO [IPC Server handler 1 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.36323506
2015-10-17 15:39:33,036 INFO [IPC Server handler 28 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.35374844
2015-10-17 15:39:34,145 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.13189735
2015-10-17 15:39:34,270 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.45856157
2015-10-17 15:39:35,411 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.09444665
2015-10-17 15:39:35,458 INFO [IPC Server handler 3 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.113010995
2015-10-17 15:39:35,864 INFO [IPC Server handler 1 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.31493258
2015-10-17 15:39:35,864 INFO [IPC Server handler 28 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.409383
2015-10-17 15:39:36,004 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.36323506
2015-10-17 15:39:36,004 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.17261034
2015-10-17 15:39:36,301 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.3807513
2015-10-17 15:39:36,926 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.105194785
2015-10-17 15:39:37,317 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.47810328
2015-10-17 15:39:37,473 INFO [IPC Server handler 3 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.14036538
2015-10-17 15:39:39,005 INFO [IPC Server handler 10 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.41980344
2015-10-17 15:39:39,005 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.33773223
2015-10-17 15:39:39,036 INFO [IPC Server handler 15 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.11789677
2015-10-17 15:39:39,083 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.100959316
2015-10-17 15:39:39,161 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.3696962
2015-10-17 15:39:39,567 INFO [IPC Server handler 3 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.39155456
2015-10-17 15:39:39,958 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.17651798
2015-10-17 15:39:40,130 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.10845062
2015-10-17 15:39:40,739 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.48622927
2015-10-17 15:39:40,739 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.1527431
2015-10-17 15:39:42,583 INFO [IPC Server handler 3 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.12147742
2015-10-17 15:39:42,801 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.4407631
2015-10-17 15:39:42,801 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.34588373
2015-10-17 15:39:42,801 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.106881365
2015-10-17 15:39:42,848 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.40415826
2015-10-17 15:39:43,176 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.18368445
2015-10-17 15:39:43,270 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.40221828
2015-10-17 15:39:43,301 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.11333614
2015-10-17 15:39:43,848 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.5106718
2015-10-17 15:39:44,005 INFO [IPC Server handler 22 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.15860437
2015-10-17 15:39:45,895 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.448704
2015-10-17 15:39:45,895 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.36317363
2015-10-17 15:39:45,958 INFO [IPC Server handler 22 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.12669009
2015-10-17 15:39:46,302 INFO [IPC Server handler 15 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.106881365
2015-10-17 15:39:46,317 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.429902
2015-10-17 15:39:46,427 INFO [IPC Server handler 17 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.4149604
2015-10-17 15:39:46,880 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.52494603
2015-10-17 15:39:47,177 INFO [IPC Server handler 15 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.1690282
2015-10-17 15:39:47,177 INFO [IPC Server handler 10 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.11984887
2015-10-17 15:39:47,239 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.19182543
2015-10-17 15:39:49,020 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.448704
2015-10-17 15:39:49,020 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.36317363
2015-10-17 15:39:49,395 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.13287796
2015-10-17 15:39:49,395 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.4486067
2015-10-17 15:39:49,958 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.106881365
2015-10-17 15:39:49,989 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.53341997
2015-10-17 15:39:50,317 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.4248713
2015-10-17 15:39:50,364 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.1283174
2015-10-17 15:39:50,364 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.17456204
2015-10-17 15:39:52,020 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.19247705
2015-10-17 15:39:52,067 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.4712515
2015-10-17 15:39:52,067 INFO [IPC Server handler 22 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.3729065
2015-10-17 15:39:52,473 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.4486067
2015-10-17 15:39:52,864 INFO [IPC Server handler 3 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.1482432
2015-10-17 15:39:53,052 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.53341997
2015-10-17 15:39:53,395 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.106881365
2015-10-17 15:39:54,020 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.18140283
2015-10-17 15:39:55,005 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.13613375
2015-10-17 15:39:55,817 INFO [IPC Server handler 3 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.5280709
2015-10-17 15:39:55,817 INFO [IPC Server handler 13 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.4486067
2015-10-17 15:39:55,817 INFO [IPC Server handler 3 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.43806094
2015-10-17 15:39:56,083 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.55977315
2015-10-17 15:39:56,177 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.15079007
2015-10-17 15:39:56,692 INFO [IPC Server handler 17 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.106881365
2015-10-17 15:39:57,130 INFO [IPC Server handler 22 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.19247656
2015-10-17 15:39:58,224 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.14101727
2015-10-17 15:39:58,739 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.46986422
2015-10-17 15:39:58,895 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.44859612
2015-10-17 15:39:58,911 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.470611
2015-10-17 15:39:58,911 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.53425497
2015-10-17 15:39:59,145 INFO [IPC Server handler 22 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.5725231
2015-10-17 15:39:59,474 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.15860605
2015-10-17 15:39:59,645 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.19247705
2015-10-17 15:39:59,770 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.109750986
2015-10-17 15:40:00,333 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.19255035
2015-10-17 15:40:01,411 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.14590457
2015-10-17 15:40:01,927 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.5323719
2015-10-17 15:40:01,958 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.53425497
2015-10-17 15:40:01,958 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.48557225
2015-10-17 15:40:01,958 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.44859612
2015-10-17 15:40:02,208 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.5885097
2015-10-17 15:40:02,521 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.16153675
2015-10-17 15:40:02,833 INFO [IPC Server handler 20 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.11724175
2015-10-17 15:40:02,896 INFO [IPC Server handler 12 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.19996932
2015-10-17 15:40:03,489 INFO [IPC Server handler 23 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.19475694
2015-10-17 15:40:04,552 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.15078904
2015-10-17 15:40:05,036 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.50024885
2015-10-17 15:40:05,036 INFO [IPC Server handler 22 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.5438912
2015-10-17 15:40:05,036 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.45497882
2015-10-17 15:40:05,083 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.5323719
2015-10-17 15:40:05,302 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.6044661
2015-10-17 15:40:05,599 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.16609815
2015-10-17 15:40:05,896 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.12473284
2015-10-17 15:40:06,146 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.20713283
2015-10-17 15:40:07,052 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.19931445
2015-10-17 15:40:07,896 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.15958306
2015-10-17 15:40:08,130 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.5240239
2015-10-17 15:40:08,130 INFO [IPC Server handler 23 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.57682145
2015-10-17 15:40:08,130 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.48233566
2015-10-17 15:40:08,224 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.5323719
2015-10-17 15:40:08,427 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.61898744
2015-10-17 15:40:08,739 INFO [IPC Server handler 17 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.17000598
2015-10-17 15:40:08,927 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.1335285
2015-10-17 15:40:10,458 INFO [IPC Server handler 1 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.20387407
2015-10-17 15:40:11,177 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.6049089
2015-10-17 15:40:11,177 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.5343203
2015-10-17 15:40:11,177 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.50871557
2015-10-17 15:40:11,255 INFO [IPC Server handler 9 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.16739772
2015-10-17 15:40:11,333 INFO [IPC Server handler 10 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.5323719
2015-10-17 15:40:11,458 INFO [IPC Server handler 15 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.61898744
2015-10-17 15:40:11,786 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.17423928
2015-10-17 15:40:11,989 INFO [IPC Server handler 14 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.13841358
2015-10-17 15:40:12,630 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.21234466
2015-10-17 15:40:14,208 INFO [IPC Server handler 9 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.6197233
2015-10-17 15:40:14,208 INFO [IPC Server handler 10 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.5342037
2015-10-17 15:40:14,239 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.5343203
2015-10-17 15:40:14,271 INFO [IPC Server handler 15 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.21169062
2015-10-17 15:40:14,505 INFO [IPC Server handler 1 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.6288942
2015-10-17 15:40:14,739 INFO [IPC Server handler 17 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.175868
2015-10-17 15:40:14,880 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.17782298
2015-10-17 15:40:15,052 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.14394844
2015-10-17 15:40:15,193 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.54728025
2015-10-17 15:40:15,818 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.21788183
2015-10-17 15:40:17,255 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.6197233
2015-10-17 15:40:17,255 INFO [IPC Server handler 10 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.5342037
2015-10-17 15:40:17,302 INFO [IPC Server handler 15 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.54009247
2015-10-17 15:40:17,615 INFO [IPC Server handler 28 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.6448525
2015-10-17 15:40:17,927 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.21787995
2015-10-17 15:40:17,943 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.18303266
2015-10-17 15:40:18,099 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.14981201
2015-10-17 15:40:18,927 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.22113727
2015-10-17 15:40:19,240 INFO [IPC Server handler 9 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.5625815
2015-10-17 15:40:19,240 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.17944862
2015-10-17 15:40:20,365 INFO [IPC Server handler 1 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.5342037
2015-10-17 15:40:20,365 INFO [IPC Server handler 28 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.6197233
2015-10-17 15:40:20,646 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.5692919
2015-10-17 15:40:20,646 INFO [IPC Server handler 17 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.65332043
2015-10-17 15:40:20,990 INFO [IPC Server handler 20 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.18922195
2015-10-17 15:40:21,146 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.1559977
2015-10-17 15:40:21,349 INFO [IPC Server handler 1 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.2237422
2015-10-17 15:40:22,177 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.22537044
2015-10-17 15:40:22,521 INFO [IPC Server handler 17 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.5832832
2015-10-17 15:40:22,646 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.18466029
2015-10-17 15:40:23,443 INFO [IPC Server handler 17 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.66103935
2015-10-17 15:40:23,443 INFO [IPC Server handler 0 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.5767812
2015-10-17 15:40:23,927 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.66103935
2015-10-17 15:40:24,021 INFO [IPC Server handler 20 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.662113
2015-10-17 15:40:24,115 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.19242907
2015-10-17 15:40:24,146 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.662113
2015-10-17 15:40:24,240 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.16674583
2015-10-17 15:40:24,599 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.5988948
2015-10-17 15:40:24,865 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.22895211
2015-10-17 15:40:25,271 INFO [IPC Server handler 23 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.2283341
2015-10-17 15:40:25,615 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.5949841
2015-10-17 15:40:26,521 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.667
2015-10-17 15:40:26,521 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.19266446
2015-10-17 15:40:26,521 INFO [IPC Server handler 21 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.6196791
2015-10-17 15:40:27,130 INFO [IPC Server handler 13 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.667
2015-10-17 15:40:27,177 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.19242907
2015-10-17 15:40:27,302 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.17130539
2015-10-17 15:40:27,677 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.6199081
2015-10-17 15:40:28,130 INFO [IPC Server handler 14 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.2393729
2015-10-17 15:40:29,333 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.60128975
2015-10-17 15:40:29,599 INFO [IPC Server handler 16 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.667
2015-10-17 15:40:29,599 INFO [IPC Server handler 27 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.6196791
2015-10-17 15:40:29,708 INFO [IPC Server handler 20 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.19266446
2015-10-17 15:40:29,802 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.2312347
2015-10-17 15:40:30,209 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.667
2015-10-17 15:40:30,443 INFO [IPC Server handler 28 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.19242907
2015-10-17 15:40:30,552 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.1787962
2015-10-17 15:40:30,771 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.6199081
2015-10-17 15:40:31,349 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.2458648
2015-10-17 15:40:32,646 INFO [IPC Server handler 20 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.61388814
2015-10-17 15:40:32,693 INFO [IPC Server handler 4 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.6196791
2015-10-17 15:40:32,693 INFO [IPC Server handler 24 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.6733533
2015-10-17 15:40:33,318 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.6709045
2015-10-17 15:40:33,380 INFO [IPC Server handler 22 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.19410524
2015-10-17 15:40:33,724 INFO [IPC Server handler 3 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.19736192
2015-10-17 15:40:33,740 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.1849622
2015-10-17 15:40:33,834 INFO [IPC Server handler 12 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.6199081
2015-10-17 15:40:34,521 INFO [IPC Server handler 17 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.25012162
2015-10-17 15:40:35,943 INFO [IPC Server handler 14 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.62378913
2015-10-17 15:40:36,052 INFO [IPC Server handler 6 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.71774083
2015-10-17 15:40:36,052 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.63770753
2015-10-17 15:40:36,099 INFO [IPC Server handler 13 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.24067983
2015-10-17 15:40:36,365 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.7120226
2015-10-17 15:40:36,584 INFO [IPC Server handler 26 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.19736312
2015-10-17 15:40:36,834 INFO [IPC Server handler 12 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.18824291
2015-10-17 15:40:36,834 INFO [IPC Server handler 14 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.20029396
2015-10-17 15:40:36,880 INFO [IPC Server handler 6 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.6497366
2015-10-17 15:40:38,146 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.25468245
2015-10-17 15:40:38,943 INFO [IPC Server handler 5 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.63770753
2015-10-17 15:40:38,959 INFO [IPC Server handler 13 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.6497366
2015-10-17 15:40:39,068 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.667
2015-10-17 15:40:39,068 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.7797339
2015-10-17 15:40:39,162 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.63279176
2015-10-17 15:40:39,271 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.24588937
2015-10-17 15:40:39,443 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.7452539
2015-10-17 15:40:39,974 INFO [IPC Server handler 29 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.1911733
2015-10-17 15:40:39,974 INFO [IPC Server handler 19 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.20061994
2015-10-17 15:40:40,224 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.667
2015-10-17 15:40:42,209 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.667
2015-10-17 15:40:42,209 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.82012177
2015-10-17 15:40:42,318 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.2019224
2015-10-17 15:40:42,427 INFO [IPC Server handler 9 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000005_0 is : 0.24979839
2015-10-17 15:40:42,443 INFO [IPC Server handler 23 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.25859123
2015-10-17 15:40:42,506 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.78359985
2015-10-17 15:40:42,740 INFO [IPC Server handler 7 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000009_0 is : 0.6417869
2015-10-17 15:40:43,287 INFO [IPC Server handler 18 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000008_0 is : 0.19258286
2015-10-17 15:40:43,287 INFO [IPC Server handler 9 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000004_0 is : 0.20387465
2015-10-17 15:40:43,318 INFO [IPC Server handler 23 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000003_0 is : 0.667
2015-10-17 15:40:45,287 INFO [IPC Server handler 11 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000001_0 is : 0.866238
2015-10-17 15:40:45,287 INFO [IPC Server handler 25 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000002_0 is : 0.667
2015-10-17 15:40:45,506 INFO [IPC Server handler 22 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000006_0 is : 0.21234201
2015-10-17 15:40:45,553 INFO [IPC Server handler 8 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000000_0 is : 0.81333214
2015-10-17 15:40:45,631 INFO [IPC Server handler 2 on 49470] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0013_m_000007_0 is : 0.2621727
