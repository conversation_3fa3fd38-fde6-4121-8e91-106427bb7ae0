2015-10-17 21:48:16,337 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 21:48:16,509 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 21:48:16,509 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 21:48:16,540 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 21:48:16,540 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445087491445_0005, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@7f0eb4b4)
2015-10-17 21:48:16,712 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 21:48:17,259 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445087491445_0005
2015-10-17 21:48:17,587 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 21:48:18,399 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 21:48:18,415 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@6ad3381f
2015-10-17 21:48:18,884 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:402653184+134217728
2015-10-17 21:48:18,993 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 21:48:18,993 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 21:48:18,993 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 21:48:18,993 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 21:48:18,993 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 21:48:18,993 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 21:48:25,947 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:48:25,947 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34171787; bufvoid = 104857600
2015-10-17 21:48:25,947 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13785828(55143312); length = 12428569/6553600
2015-10-17 21:48:25,947 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44657541 kvi 11164380(44657520)
2015-10-17 21:48:35,603 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 21:48:35,603 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44657541 kv 11164380(44657520) kvi 8542952(34171808)
2015-10-17 21:48:40,228 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:48:40,228 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44657541; bufend = 78831461; bufvoid = 104857600
2015-10-17 21:48:40,228 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11164380(44657520); kvend = 24950744(99802976); length = 12428037/6553600
2015-10-17 21:48:40,228 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89317210 kvi 22329296(89317184)
2015-10-17 21:48:50,260 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 21:48:50,260 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 89317210 kv 22329296(89317184) kvi 19707872(78831488)
2015-10-17 21:48:52,791 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:48:52,791 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 89317210; bufend = 18635516; bufvoid = 104857600
2015-10-17 21:48:52,791 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 22329296(89317184); kvend = 9901760(39607040); length = 12427537/6553600
2015-10-17 21:48:52,791 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29121270 kvi 7280312(29121248)
2015-10-17 21:49:02,089 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 21:49:02,089 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29121270 kv 7280312(29121248) kvi 4658884(18635536)
2015-10-17 21:49:04,933 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:49:04,933 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29121270; bufend = 63298571; bufvoid = 104857600
2015-10-17 21:49:04,933 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7280312(29121248); kvend = 21067524(84270096); length = 12427189/6553600
2015-10-17 21:49:04,933 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 73784325 kvi 18446076(73784304)
2015-10-17 21:49:15,308 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 21:49:15,308 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 73784325 kv 18446076(73784304) kvi 15824648(63298592)
2015-10-17 21:49:18,058 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:49:18,058 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 73784325; bufend = 3101826; bufvoid = 104857600
2015-10-17 21:49:18,058 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 18446076(73784304); kvend = 6018336(24073344); length = 12427741/6553600
2015-10-17 21:49:18,058 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 13587577 kvi 3396888(13587552)
2015-10-17 21:49:27,668 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 21:49:27,668 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 13587577 kv 3396888(13587552) kvi 775464(3101856)
2015-10-17 21:49:30,230 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:49:30,230 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 13587577; bufend = 47763869; bufvoid = 104857600
2015-10-17 21:49:30,230 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 3396888(13587552); kvend = 17183848(68735392); length = 12427441/6553600
2015-10-17 21:49:30,230 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 58249622 kvi 14562400(58249600)
2015-10-17 21:49:39,731 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-17 21:49:49,528 WARN [main] org.apache.hadoop.hdfs.BlockReaderFactory: I/O error constructing remote block reader.
java.net.ConnectException: Connection timed out: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:206)
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:530)
	at org.apache.hadoop.hdfs.DFSClient.newConnectedPeer(DFSClient.java:3101)
	at org.apache.hadoop.hdfs.BlockReaderFactory.nextTcpPeer(BlockReaderFactory.java:755)
	at org.apache.hadoop.hdfs.BlockReaderFactory.getRemoteBlockReaderFromTcp(BlockReaderFactory.java:670)
	at org.apache.hadoop.hdfs.BlockReaderFactory.build(BlockReaderFactory.java:337)
	at org.apache.hadoop.hdfs.DFSInputStream.blockSeekTo(DFSInputStream.java:576)
	at org.apache.hadoop.hdfs.DFSInputStream.readWithStrategy(DFSInputStream.java:800)
	at org.apache.hadoop.hdfs.DFSInputStream.read(DFSInputStream.java:847)
	at java.io.DataInputStream.read(DataInputStream.java:100)
	at org.apache.hadoop.util.LineReader.fillBuffer(LineReader.java:180)
	at org.apache.hadoop.util.LineReader.readDefaultLine(LineReader.java:216)
	at org.apache.hadoop.util.LineReader.readLine(LineReader.java:174)
	at org.apache.hadoop.mapreduce.lib.input.LineRecordReader.nextKeyValue(LineRecordReader.java:185)
	at org.apache.hadoop.mapred.MapTask$NewTrackingRecordReader.nextKeyValue(MapTask.java:553)
	at org.apache.hadoop.mapreduce.task.MapContextImpl.nextKeyValue(MapContextImpl.java:80)
	at org.apache.hadoop.mapreduce.lib.map.WrappedMapper$Context.nextKeyValue(WrappedMapper.java:91)
	at org.apache.hadoop.mapreduce.Mapper.run(Mapper.java:144)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:784)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:422)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
2015-10-17 21:49:49,528 WARN [main] org.apache.hadoop.hdfs.DFSClient: Failed to connect to /10.86.169.121:50010 for block, add to deadNodes and continue. java.net.ConnectException: Connection timed out: no further information
java.net.ConnectException: Connection timed out: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:206)
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:530)
	at org.apache.hadoop.hdfs.DFSClient.newConnectedPeer(DFSClient.java:3101)
	at org.apache.hadoop.hdfs.BlockReaderFactory.nextTcpPeer(BlockReaderFactory.java:755)
	at org.apache.hadoop.hdfs.BlockReaderFactory.getRemoteBlockReaderFromTcp(BlockReaderFactory.java:670)
	at org.apache.hadoop.hdfs.BlockReaderFactory.build(BlockReaderFactory.java:337)
	at org.apache.hadoop.hdfs.DFSInputStream.blockSeekTo(DFSInputStream.java:576)
	at org.apache.hadoop.hdfs.DFSInputStream.readWithStrategy(DFSInputStream.java:800)
	at org.apache.hadoop.hdfs.DFSInputStream.read(DFSInputStream.java:847)
	at java.io.DataInputStream.read(DataInputStream.java:100)
	at org.apache.hadoop.util.LineReader.fillBuffer(LineReader.java:180)
	at org.apache.hadoop.util.LineReader.readDefaultLine(LineReader.java:216)
	at org.apache.hadoop.util.LineReader.readLine(LineReader.java:174)
	at org.apache.hadoop.mapreduce.lib.input.LineRecordReader.nextKeyValue(LineRecordReader.java:185)
	at org.apache.hadoop.mapred.MapTask$NewTrackingRecordReader.nextKeyValue(MapTask.java:553)
	at org.apache.hadoop.mapreduce.task.MapContextImpl.nextKeyValue(MapContextImpl.java:80)
	at org.apache.hadoop.mapreduce.lib.map.WrappedMapper$Context.nextKeyValue(WrappedMapper.java:91)
	at org.apache.hadoop.mapreduce.Mapper.run(Mapper.java:144)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:784)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:422)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
2015-10-17 21:49:49,731 INFO [main] org.apache.hadoop.hdfs.DFSClient: Successfully connected to /10.190.173.170:50010 for BP-1347369012-10.190.173.170-1444972147527:blk_1073742826_2022
2015-10-17 21:49:49,731 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 58249622 kv 14562400(58249600) kvi 12516560(50066240)
2015-10-17 21:49:49,731 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-17 21:49:49,731 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:49:49,731 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 58249622; bufend = 63876500; bufvoid = 104857600
2015-10-17 21:49:49,731 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14562400(58249600); kvend = 12515968(50063872); length = 2046433/6553600
2015-10-17 21:49:50,872 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-17 21:49:50,997 INFO [main] org.apache.hadoop.mapred.Merger: Merging 7 sorted segments
2015-10-17 21:49:51,013 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 7 segments left of total size: 228411640 bytes
2015-10-17 21:50:12,545 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445087491445_0005_m_000004_0 is done. And is in the process of committing
2015-10-17 21:50:12,592 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445087491445_0005_m_000004_0' done.
