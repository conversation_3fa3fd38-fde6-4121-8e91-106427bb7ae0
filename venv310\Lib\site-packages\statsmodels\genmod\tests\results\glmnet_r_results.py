import numpy as np

rslt_binomial_0 = np.array([
    0, 6.618737, 0.004032037, 0.01433665, 0.01265635, 0.006173346, 0.01067706])

rslt_binomial_1 = np.array([
    0, 1.029661, 0.02180239, 0.07769613, 0.06756466, 0.03156418, 0.05851878])

rslt_binomial_2 = np.array([
    0, 0.1601819, 0.07111087, 0.2544921, 0.2110318, 0.08577924, 0.1984383])

rslt_binomial_3 = np.array([
    0.5, 0.05343991, 0.004990061, 0.2838563, 0.2167881, 0.02370156, 0.2096612])

rslt_binomial_4 = np.array([
    0.5, 0.02313286, 0.0708914, 0.3791042, 0.2938332, 0.07506391, 0.2982251])

rslt_binomial_5 = np.array([
    0.5, 0.009124078, 0.106681, 0.4327268, 0.3362166, 0.1019452, 0.3479955])

rslt_binomial_6 = np.array([
    1, 0.02932512, 0, 0.3085764, 0.2300801, 0.01143652, 0.2291531])

rslt_binomial_7 = np.array([
    1, 0.01269414, 0.07022348, 0.396642, 0.3044255, 0.07151663, 0.31301])

rslt_binomial_8 = np.array([
    1, 0.005494992, 0.1049623, 0.4385186, 0.3391729, 0.09907393, 0.3527401])

rslt_poisson_0 = np.array([
    0, 23.5349, 0.009251658, 0.003730997, 0.01266164, 0.003439135, 0.0141719])

rslt_poisson_1 = np.array([
    0, 3.661269, 0.04842557, 0.02095708, 0.06550316, 0.02029514, 0.07300782])

rslt_poisson_2 = np.array([
    0, 0.5695749, 0.1440462, 0.07208017, 0.182649, 0.07511376, 0.2018242])

rslt_poisson_3 = np.array([
    0.5, 0.1577593, 0.1247603, 0.02857521, 0.185693, 0.03840622, 0.2200925])

rslt_poisson_4 = np.array([
    0.5, 0.05669575, 0.187629, 0.08842012, 0.2348627, 0.09736964, 0.2628845])

rslt_poisson_5 = np.array([
    0.5, 0.0185653, 0.2118078, 0.1121067, 0.2534181, 0.1204543, 0.2784761])

rslt_poisson_6 = np.array([
    1, 0.07887965, 0.1339927, 0.0322772, 0.1969884, 0.0439019, 0.2339252])

rslt_poisson_7 = np.array([
    1, 0.02834788, 0.1927163, 0.09160406, 0.2398164, 0.1010126, 0.2682158])

rslt_poisson_8 = np.array([
    1, 0.0101877, 0.2126847, 0.1123439, 0.2544153, 0.1208601, 0.2796794])
