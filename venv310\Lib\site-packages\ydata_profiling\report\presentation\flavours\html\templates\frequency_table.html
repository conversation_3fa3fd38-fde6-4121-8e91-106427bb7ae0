<div class="table-responsive">
    <table class="freq table table-hover table-striped {% if idx is defined %}table-{{ idx }}{% endif %}">
        <thead>
            <tr>
                <td>Value</td>
                <td>Count</td>
                <td>Frequency (%)</td>
            </tr>
        </thead>
        <tbody>
            {% if rows | length > 0 %}
                {% for row in rows %}
                    <tr class="{{ row['extra_class'] }}">
                        {% if row['extra_class'] | length == 0 and redact %}
                            <td title="Redacted value">
                                <div class="text-placeholder"></div>
                            </td>
                        {% else %}
                            <td title="{{ row['label'] | fmt }}">
                                {{ row['label'] | fmt }}
                            </td>
                        {% endif %}
                        <td>{{ row['count'] }}</td>
                        <td>
                            <div class="bar" style="width:{{ row['width'] | fmt_percent(edge_cases=False) }}">
                                {% if row['width'] > 0.40 %}
                                    {{ row['percentage'] | fmt_percent }}
                                {% else %}
                                    &nbsp;
                                {% endif %}
                            </div>
                            {% if row['width'] <= 0.40 %}
                                {{ row['percentage'] | fmt_percent }}
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="3">No values found.</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>