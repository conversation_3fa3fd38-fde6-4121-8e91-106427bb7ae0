2015-10-17 22:26:37,008 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445087491445_0009_000001
2015-10-17 22:26:37,430 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 22:26:37,430 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 9 cluster_timestamp: 1445087491445 } attemptId: 1 } keyId: -1547346236)
2015-10-17 22:26:37,680 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 22:26:39,008 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 22:26:39,133 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 22:26:39,180 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 22:26:39,180 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 22:26:39,180 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 22:26:39,180 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 22:26:39,180 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 22:26:39,195 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 22:26:39,195 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 22:26:39,195 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 22:26:39,273 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 22:26:39,305 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 22:26:39,351 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 22:26:39,367 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 22:26:39,430 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 22:26:39,742 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 22:26:39,820 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 22:26:39,820 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 22:26:39,836 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445087491445_0009 to jobTokenSecretManager
2015-10-17 22:26:40,321 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445087491445_0009 because: not enabled; too many maps; too much input;
2015-10-17 22:26:40,352 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445087491445_0009 = 1313861632. Number of splits = 10
2015-10-17 22:26:40,352 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445087491445_0009 = 1
2015-10-17 22:26:40,352 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0009Job Transitioned from NEW to INITED
2015-10-17 22:26:40,352 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445087491445_0009.
2015-10-17 22:26:40,415 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 22:26:40,446 INFO [Socket Reader #1 for port 11411] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 11411
2015-10-17 22:26:40,477 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 22:26:40,477 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 22:26:40,477 INFO [IPC Server listener on 11411] org.apache.hadoop.ipc.Server: IPC Server listener on 11411: starting
2015-10-17 22:26:40,477 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-39.fareast.corp.microsoft.com/**************:11411
2015-10-17 22:26:40,602 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 22:26:40,602 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 22:26:40,618 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 22:26:40,633 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 22:26:40,633 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 22:26:40,633 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 22:26:40,633 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 22:26:40,649 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 11418
2015-10-17 22:26:40,649 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 22:26:40,696 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_11418_mapreduce____.rgdnky\webapp
2015-10-17 22:26:40,993 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:11418
2015-10-17 22:26:40,993 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 11418
2015-10-17 22:26:41,446 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 22:26:41,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445087491445_0009
2015-10-17 22:26:41,462 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 22:26:41,462 INFO [Socket Reader #1 for port 11421] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 11421
2015-10-17 22:26:41,477 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 22:26:41,477 INFO [IPC Server listener on 11421] org.apache.hadoop.ipc.Server: IPC Server listener on 11421: starting
2015-10-17 22:26:41,524 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 22:26:41,524 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 22:26:41,524 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 22:26:41,587 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 22:26:41,712 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 22:26:41,712 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 22:26:41,712 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 22:26:41,712 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 22:26:41,727 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0009Job Transitioned from INITED to SETUP
2015-10-17 22:26:41,727 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 22:26:41,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0009Job Transitioned from SETUP to RUNNING
2015-10-17 22:26:41,790 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,790 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,790 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:41,790 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:41,805 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:41,821 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 22:26:41,837 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 22:26:41,868 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445087491445_0009, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0009/job_1445087491445_0009_1.jhist
2015-10-17 22:26:42,712 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 22:26:42,774 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=6 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:47104, vCores:-4> knownNMs=7
2015-10-17 22:26:42,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:47104, vCores:-4>
2015-10-17 22:26:42,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:43,852 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 10
2015-10-17 22:26:43,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000002 to attempt_1445087491445_0009_m_000002_0
2015-10-17 22:26:43,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000003 to attempt_1445087491445_0009_m_000004_0
2015-10-17 22:26:43,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000004 to attempt_1445087491445_0009_m_000007_0
2015-10-17 22:26:43,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000005 to attempt_1445087491445_0009_m_000000_0
2015-10-17 22:26:43,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000006 to attempt_1445087491445_0009_m_000003_0
2015-10-17 22:26:43,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000007 to attempt_1445087491445_0009_m_000006_0
2015-10-17 22:26:43,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000009 to attempt_1445087491445_0009_m_000001_0
2015-10-17 22:26:43,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000010 to attempt_1445087491445_0009_m_000005_0
2015-10-17 22:26:43,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000011 to attempt_1445087491445_0009_m_000008_0
2015-10-17 22:26:43,868 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:43,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000008 to attempt_1445087491445_0009_m_000009_0
2015-10-17 22:26:43,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:36864, vCores:-14>
2015-10-17 22:26:43,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:43,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 22:26:43,946 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:43,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0009/job.jar
2015-10-17 22:26:43,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0009/job.xml
2015-10-17 22:26:43,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 22:26:43,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 22:26:43,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 22:26:44,040 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:44,040 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,040 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:44,055 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000002 taskAttempt attempt_1445087491445_0009_m_000002_0
2015-10-17 22:26:44,055 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000003 taskAttempt attempt_1445087491445_0009_m_000004_0
2015-10-17 22:26:44,055 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000004 taskAttempt attempt_1445087491445_0009_m_000007_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000006 taskAttempt attempt_1445087491445_0009_m_000003_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000007 taskAttempt attempt_1445087491445_0009_m_000006_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000005 taskAttempt attempt_1445087491445_0009_m_000000_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000009 taskAttempt attempt_1445087491445_0009_m_000001_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000010 taskAttempt attempt_1445087491445_0009_m_000005_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000011 taskAttempt attempt_1445087491445_0009_m_000008_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000008 taskAttempt attempt_1445087491445_0009_m_000009_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000000_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000008_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000001_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000009_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000007_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000004_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000005_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000006_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000003_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000002_0
2015-10-17 22:26:44,071 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:26:44,102 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:26:44,118 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:26:44,118 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:26:44,118 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:26:44,118 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:26:44,118 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:26:44,118 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:26:44,118 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:26:44,118 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:26:44,229 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000005_0 : 13562
2015-10-17 22:26:44,229 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000001_0 : 13562
2015-10-17 22:26:44,229 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000008_0 : 13562
2015-10-17 22:26:44,229 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000007_0 : 13562
2015-10-17 22:26:44,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000008_0] using containerId: [container_1445087491445_0009_01_000011 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:26:44,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:44,229 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000002_0 : 13562
2015-10-17 22:26:44,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000001_0] using containerId: [container_1445087491445_0009_01_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000005_0] using containerId: [container_1445087491445_0009_01_000010 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000007_0] using containerId: [container_1445087491445_0009_01_000004 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000008
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000002_0] using containerId: [container_1445087491445_0009_01_000002 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000001
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000005
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000007
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000002
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:44,244 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000004_0 : 13562
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000004_0] using containerId: [container_1445087491445_0009_01_000003 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000004
2015-10-17 22:26:44,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:44,307 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000006_0 : 13562
2015-10-17 22:26:44,307 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000006_0] using containerId: [container_1445087491445_0009_01_000007 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 22:26:44,307 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:44,307 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000006
2015-10-17 22:26:44,307 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:44,354 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000000_0 : 13562
2015-10-17 22:26:44,354 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000000_0] using containerId: [container_1445087491445_0009_01_000005 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 22:26:44,354 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:44,354 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000000
2015-10-17 22:26:44,354 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:44,385 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000003_0 : 13562
2015-10-17 22:26:44,385 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000003_0] using containerId: [container_1445087491445_0009_01_000006 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 22:26:44,385 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:44,385 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000003
2015-10-17 22:26:44,385 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:44,651 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000009_0 : 13562
2015-10-17 22:26:44,651 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000009_0] using containerId: [container_1445087491445_0009_01_000008 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:55135]
2015-10-17 22:26:44,651 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:44,651 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000009
2015-10-17 22:26:44,651 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:44,869 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=6 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:36864, vCores:-14> knownNMs=7
2015-10-17 22:26:45,869 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:20480, vCores:-30>
2015-10-17 22:26:45,869 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:46,869 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:19456, vCores:-31>
2015-10-17 22:26:46,869 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:47,760 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:26:47,776 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:26:47,807 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000009 asked for a task
2015-10-17 22:26:47,807 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000011 asked for a task
2015-10-17 22:26:47,807 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000009 given task: attempt_1445087491445_0009_m_000001_0
2015-10-17 22:26:47,807 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000011 given task: attempt_1445087491445_0009_m_000008_0
2015-10-17 22:26:48,088 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:26:48,119 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000010 asked for a task
2015-10-17 22:26:48,119 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000010 given task: attempt_1445087491445_0009_m_000005_0
2015-10-17 22:26:49,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:18432, vCores:-32>
2015-10-17 22:26:49,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:50,866 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:26:50,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:17408, vCores:-33>
2015-10-17 22:26:50,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:50,928 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:26:50,960 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:26:50,991 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:26:50,991 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000006 asked for a task
2015-10-17 22:26:50,991 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000006 given task: attempt_1445087491445_0009_m_000003_0
2015-10-17 22:26:51,007 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:26:51,007 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:26:51,022 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000007 asked for a task
2015-10-17 22:26:51,022 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000007 given task: attempt_1445087491445_0009_m_000006_0
2015-10-17 22:26:51,053 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000003 asked for a task
2015-10-17 22:26:51,053 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000003 given task: attempt_1445087491445_0009_m_000004_0
2015-10-17 22:26:51,085 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000005 asked for a task
2015-10-17 22:26:51,085 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000005 given task: attempt_1445087491445_0009_m_000000_0
2015-10-17 22:26:51,132 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000002 asked for a task
2015-10-17 22:26:51,132 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000002 given task: attempt_1445087491445_0009_m_000002_0
2015-10-17 22:26:51,210 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000004 asked for a task
2015-10-17 22:26:51,210 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000004 given task: attempt_1445087491445_0009_m_000007_0
2015-10-17 22:26:51,366 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:26:51,413 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000008 asked for a task
2015-10-17 22:26:51,413 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000008 given task: attempt_1445087491445_0009_m_000009_0
2015-10-17 22:26:52,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:26:52,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:55,288 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.13102318
2015-10-17 22:26:55,304 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.13102192
2015-10-17 22:26:55,663 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.13104132
2015-10-17 22:26:58,335 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.13102318
2015-10-17 22:26:58,351 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.13102192
2015-10-17 22:26:58,679 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.13104132
2015-10-17 22:27:01,366 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.13343063
2015-10-17 22:27:01,366 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.19681121
2015-10-17 22:27:01,710 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.13104132
2015-10-17 22:27:04,179 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.109372765
2015-10-17 22:27:04,382 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.23921506
2015-10-17 22:27:04,382 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.23921879
2015-10-17 22:27:04,742 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.23922269
2015-10-17 22:27:05,304 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.042253315
2015-10-17 22:27:05,601 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.04175504
2015-10-17 22:27:05,945 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.050825387
2015-10-17 22:27:07,257 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.09810516
2015-10-17 22:27:07,382 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.10373433
2015-10-17 22:27:07,414 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.23921879
2015-10-17 22:27:07,414 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.23921506
2015-10-17 22:27:07,460 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.10207153
2015-10-17 22:27:07,757 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.23922269
2015-10-17 22:27:08,257 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.16604526
2015-10-17 22:27:09,132 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.11860685
2015-10-17 22:27:09,679 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.119372874
2015-10-17 22:27:10,336 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.12748064
2015-10-17 22:27:10,429 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.33177033
2015-10-17 22:27:10,429 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.25290897
2015-10-17 22:27:10,789 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.23922269
2015-10-17 22:27:10,929 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.13103712
2015-10-17 22:27:11,086 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.13104042
2015-10-17 22:27:11,132 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.131014
2015-10-17 22:27:11,851 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.16604526
2015-10-17 22:27:12,554 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.13101934
2015-10-17 22:27:13,445 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.34743196
2015-10-17 22:27:13,445 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.3474145
2015-10-17 22:27:13,461 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.13101135
2015-10-17 22:27:13,758 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.13102706
2015-10-17 22:27:13,804 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.3474054
2015-10-17 22:27:14,383 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.13103712
2015-10-17 22:27:14,726 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.13104042
2015-10-17 22:27:14,726 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.131014
2015-10-17 22:27:15,195 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.16604526
2015-10-17 22:27:16,383 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.13101934
2015-10-17 22:27:16,476 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.34743196
2015-10-17 22:27:16,476 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.3474145
2015-10-17 22:27:16,820 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.3474054
2015-10-17 22:27:17,289 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.13102706
2015-10-17 22:27:17,336 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.13101135
2015-10-17 22:27:17,898 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.13103712
2015-10-17 22:27:18,273 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.16604526
2015-10-17 22:27:18,273 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.13104042
2015-10-17 22:27:18,320 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.131014
2015-10-17 22:27:19,492 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.43118286
2015-10-17 22:27:19,508 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.455629
2015-10-17 22:27:19,852 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.3474054
2015-10-17 22:27:19,898 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.13101934
2015-10-17 22:27:20,836 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.13102706
2015-10-17 22:27:21,133 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.13101135
2015-10-17 22:27:21,367 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.16604526
2015-10-17 22:27:21,367 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.13103712
2015-10-17 22:27:21,852 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.131014
2015-10-17 22:27:22,008 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.13104042
2015-10-17 22:27:22,508 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.45562187
2015-10-17 22:27:22,524 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.455629
2015-10-17 22:27:22,883 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.45560944
2015-10-17 22:27:23,305 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.13101934
2015-10-17 22:27:24,524 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.16604526
2015-10-17 22:27:24,633 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.13102706
2015-10-17 22:27:24,742 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.13101135
2015-10-17 22:27:24,758 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.13103712
2015-10-17 22:27:25,289 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.131014
2015-10-17 22:27:25,524 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.13104042
2015-10-17 22:27:25,539 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.45562187
2015-10-17 22:27:25,539 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.455629
2015-10-17 22:27:25,899 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.45560944
2015-10-17 22:27:26,899 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.13101934
2015-10-17 22:27:27,649 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.1717106
2015-10-17 22:27:28,117 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.13103712
2015-10-17 22:27:28,258 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.13102706
2015-10-17 22:27:28,430 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.13101135
2015-10-17 22:27:28,555 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.56384325
2015-10-17 22:27:28,555 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.5638294
2015-10-17 22:27:28,774 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.131014
2015-10-17 22:27:28,914 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.45560944
2015-10-17 22:27:29,024 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.13104042
2015-10-17 22:27:30,368 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.13101934
2015-10-17 22:27:31,024 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.2183451
2015-10-17 22:27:31,571 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.56384325
2015-10-17 22:27:31,586 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.5638294
2015-10-17 22:27:31,618 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.13103712
2015-10-17 22:27:31,821 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.13102706
2015-10-17 22:27:31,930 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.56381226
2015-10-17 22:27:31,961 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.13101135
2015-10-17 22:27:32,290 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.131014
2015-10-17 22:27:32,477 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.13104042
2015-10-17 22:27:33,836 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.13101934
2015-10-17 22:27:34,508 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.26127356
2015-10-17 22:27:34,586 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.56384325
2015-10-17 22:27:34,602 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.5638294
2015-10-17 22:27:34,962 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.56381226
2015-10-17 22:27:35,165 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.13103712
2015-10-17 22:27:35,399 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.13102706
2015-10-17 22:27:35,696 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.13101135
2015-10-17 22:27:35,946 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.131014
2015-10-17 22:27:35,962 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.5638294
2015-10-17 22:27:36,118 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.13104042
2015-10-17 22:27:37,180 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.56384325
2015-10-17 22:27:37,602 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.667
2015-10-17 22:27:37,618 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.667
2015-10-17 22:27:37,759 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.13101934
2015-10-17 22:27:37,977 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.62454885
2015-10-17 22:27:38,055 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.3031575
2015-10-17 22:27:38,462 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.62454885
2015-10-17 22:27:38,634 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.13103712
2015-10-17 22:27:38,977 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.13102706
2015-10-17 22:27:39,134 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.13101135
2015-10-17 22:27:39,415 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.13200739
2015-10-17 22:27:39,680 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.13502702
2015-10-17 22:27:40,634 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.667
2015-10-17 22:27:40,634 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.667
2015-10-17 22:27:40,993 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.667
2015-10-17 22:27:41,118 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.1388899
2015-10-17 22:27:41,509 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.3031575
2015-10-17 22:27:42,056 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.1862889
2015-10-17 22:27:42,368 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.1732117
2015-10-17 22:27:42,556 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.1661052
2015-10-17 22:27:42,962 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.20416516
2015-10-17 22:27:43,181 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.2048532
2015-10-17 22:27:43,649 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.667
2015-10-17 22:27:43,649 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.667
2015-10-17 22:27:44,009 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.667
2015-10-17 22:27:44,462 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.20565423
2015-10-17 22:27:45,040 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.3031575
2015-10-17 22:27:45,884 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.23924637
2015-10-17 22:27:46,071 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.23604141
2015-10-17 22:27:46,274 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.22934361
2015-10-17 22:27:46,524 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.23919508
2015-10-17 22:27:46,665 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.69077116
2015-10-17 22:27:46,665 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.7108953
2015-10-17 22:27:46,821 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.23924798
2015-10-17 22:27:47,025 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.6747828
2015-10-17 22:27:47,821 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.23921585
2015-10-17 22:27:48,540 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.3031575
2015-10-17 22:27:49,618 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.23922287
2015-10-17 22:27:49,681 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.7388866
2015-10-17 22:27:49,681 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.76185167
2015-10-17 22:27:49,696 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.23923388
2015-10-17 22:27:49,821 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.23924637
2015-10-17 22:27:50,040 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.7224159
2015-10-17 22:27:50,275 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.23919508
2015-10-17 22:27:50,447 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.23924798
2015-10-17 22:27:51,493 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.23921585
2015-10-17 22:27:51,837 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.3031575
2015-10-17 22:27:52,712 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.81022054
2015-10-17 22:27:52,712 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.7851321
2015-10-17 22:27:53,056 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.7675823
2015-10-17 22:27:53,228 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.23924637
2015-10-17 22:27:53,259 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.23922287
2015-10-17 22:27:53,306 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.23923388
2015-10-17 22:27:53,744 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.23919508
2015-10-17 22:27:53,884 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.23924798
2015-10-17 22:27:54,978 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.23921585
2015-10-17 22:27:55,306 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.3031575
2015-10-17 22:27:55,728 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.8284174
2015-10-17 22:27:55,728 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.854038
2015-10-17 22:27:56,087 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.809511
2015-10-17 22:27:56,759 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.23924637
2015-10-17 22:27:56,806 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.23923388
2015-10-17 22:27:56,853 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.23922287
2015-10-17 22:27:57,134 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.23919508
2015-10-17 22:27:57,462 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.23924798
2015-10-17 22:27:58,447 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.23921585
2015-10-17 22:27:58,744 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.87161446
2015-10-17 22:27:58,744 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.89747727
2015-10-17 22:27:58,869 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.3031575
2015-10-17 22:27:59,103 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.8511998
2015-10-17 22:28:00,259 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.23923388
2015-10-17 22:28:00,322 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.23922287
2015-10-17 22:28:00,353 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.23924637
2015-10-17 22:28:00,619 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.23919508
2015-10-17 22:28:00,978 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.23924798
2015-10-17 22:28:01,759 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.91490984
2015-10-17 22:28:01,759 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.9412771
2015-10-17 22:28:01,884 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.23921585
2015-10-17 22:28:02,119 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.8929642
2015-10-17 22:28:02,197 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.3172706
2015-10-17 22:28:03,744 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.23923388
2015-10-17 22:28:03,869 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.23922287
2015-10-17 22:28:04,119 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.23924637
2015-10-17 22:28:04,213 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.23919508
2015-10-17 22:28:04,525 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.23924798
2015-10-17 22:28:04,775 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 0.9850173
2015-10-17 22:28:04,775 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 0.9581645
2015-10-17 22:28:05,135 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.9345716
2015-10-17 22:28:05,306 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.23921585
2015-10-17 22:28:05,760 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.42101648
2015-10-17 22:28:07,228 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.23923388
2015-10-17 22:28:07,369 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.23922287
2015-10-17 22:28:07,666 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.23919508
2015-10-17 22:28:07,713 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.23924637
2015-10-17 22:28:07,807 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 1.0
2015-10-17 22:28:07,807 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 1.0
2015-10-17 22:28:07,822 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000008_0 is : 1.0
2015-10-17 22:28:07,822 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0009_m_000008_0
2015-10-17 22:28:07,822 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:28:07,822 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000011 taskAttempt attempt_1445087491445_0009_m_000008_0
2015-10-17 22:28:07,822 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000008_0
2015-10-17 22:28:07,822 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:28:07,838 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:28:07,838 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0009_m_000008_0
2015-10-17 22:28:07,838 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:28:07,853 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 22:28:07,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 22:28:07,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:07,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 22:28:07,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-17 22:28:07,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 22:28:08,057 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.23924798
2015-10-17 22:28:08,166 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 0.97594666
2015-10-17 22:28:08,635 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0009_m_000004
2015-10-17 22:28:08,635 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 22:28:08,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0009_m_000004
2015-10-17 22:28:08,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:28:08,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:28:08,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000004_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:28:08,744 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.23921585
2015-10-17 22:28:08,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 22:28:08,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:16384, vCores:-34> knownNMs=7
2015-10-17 22:28:08,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000011
2015-10-17 22:28:08,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 22:28:08,885 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:28:09,291 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.4402952
2015-10-17 22:28:10,716 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.23923388
2015-10-17 22:28:10,872 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.23922287
2015-10-17 22:28:11,122 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.23919508
2015-10-17 22:28:11,341 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.23924637
2015-10-17 22:28:11,497 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.23924798
2015-10-17 22:28:12,294 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.23921585
2015-10-17 22:28:12,325 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 1.0
2015-10-17 22:28:12,325 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 1.0
2015-10-17 22:28:12,747 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.4402952
2015-10-17 22:28:14,091 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.23923388
2015-10-17 22:28:14,247 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.25241086
2015-10-17 22:28:14,435 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.26515228
2015-10-17 22:28:14,747 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.25998592
2015-10-17 22:28:14,982 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.27998164
2015-10-17 22:28:15,341 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 1.0
2015-10-17 22:28:15,669 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.2785388
2015-10-17 22:28:16,185 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.4402952
2015-10-17 22:28:17,419 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000001_0 is : 1.0
2015-10-17 22:28:17,419 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0009_m_000001_0
2015-10-17 22:28:17,435 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:28:17,435 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000009 taskAttempt attempt_1445087491445_0009_m_000001_0
2015-10-17 22:28:17,435 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000001_0
2015-10-17 22:28:17,435 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:28:17,435 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:28:17,435 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0009_m_000001_0
2015-10-17 22:28:17,435 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:28:17,435 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 22:28:17,513 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.3003566
2015-10-17 22:28:17,607 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.32201508
2015-10-17 22:28:17,888 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 22:28:17,935 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.33320394
2015-10-17 22:28:18,513 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.32936507
2015-10-17 22:28:18,576 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000005_0 is : 1.0
2015-10-17 22:28:18,576 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0009_m_000005_0
2015-10-17 22:28:18,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:28:18,576 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000010 taskAttempt attempt_1445087491445_0009_m_000005_0
2015-10-17 22:28:18,576 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000005_0
2015-10-17 22:28:18,576 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:28:18,591 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:28:18,591 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0009_m_000005_0
2015-10-17 22:28:18,591 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:28:18,591 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 22:28:18,810 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.3468204
2015-10-17 22:28:18,888 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 22:28:18,888 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000009
2015-10-17 22:28:18,888 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000010
2015-10-17 22:28:18,888 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:28:18,888 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:7 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 22:28:18,888 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:28:19,201 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.34068984
2015-10-17 22:28:19,654 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.4402952
2015-10-17 22:28:21,029 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.34743717
2015-10-17 22:28:21,107 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.3473985
2015-10-17 22:28:21,326 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.3474061
2015-10-17 22:28:21,920 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.34743145
2015-10-17 22:28:22,466 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.34742972
2015-10-17 22:28:22,748 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.3474062
2015-10-17 22:28:23,091 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.4402952
2015-10-17 22:28:24,513 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.34743717
2015-10-17 22:28:24,701 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.3473985
2015-10-17 22:28:24,982 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.3474061
2015-10-17 22:28:25,467 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.34743145
2015-10-17 22:28:25,967 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.34742972
2015-10-17 22:28:26,560 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.3474062
2015-10-17 22:28:26,592 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.4402952
2015-10-17 22:28:28,107 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.34743717
2015-10-17 22:28:28,389 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.3473985
2015-10-17 22:28:28,404 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.3474061
2015-10-17 22:28:28,904 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.34743145
2015-10-17 22:28:29,389 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.34742972
2015-10-17 22:28:29,936 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.4402952
2015-10-17 22:28:30,139 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.3474062
2015-10-17 22:28:31,639 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.34743717
2015-10-17 22:28:31,920 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.3473985
2015-10-17 22:28:31,936 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.3474061
2015-10-17 22:28:32,436 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.34743145
2015-10-17 22:28:33,061 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.34742972
2015-10-17 22:28:33,420 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.44825658
2015-10-17 22:28:33,561 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.3474062
2015-10-17 22:28:35,123 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.34743717
2015-10-17 22:28:35,420 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.3474061
2015-10-17 22:28:35,451 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.3473985
2015-10-17 22:28:35,829 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.34743145
2015-10-17 22:28:36,501 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.34742972
2015-10-17 22:28:36,704 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.5328732
2015-10-17 22:28:37,173 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.3474062
2015-10-17 22:28:38,611 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.34743717
2015-10-17 22:28:38,876 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.3473985
2015-10-17 22:28:39,017 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.3474061
2015-10-17 22:28:39,580 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.34743145
2015-10-17 22:28:39,939 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.34742972
2015-10-17 22:28:40,126 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.5773621
2015-10-17 22:28:40,642 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.3474062
2015-10-17 22:28:42,095 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.34743717
2015-10-17 22:28:42,236 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.3473985
2015-10-17 22:28:42,830 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.3474061
2015-10-17 22:28:43,017 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.34743145
2015-10-17 22:28:43,408 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.34742972
2015-10-17 22:28:43,736 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.5773621
2015-10-17 22:28:44,017 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.3474062
2015-10-17 22:28:45,549 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.34743717
2015-10-17 22:28:45,642 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.3473985
2015-10-17 22:28:46,299 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.3474061
2015-10-17 22:28:46,642 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.34743145
2015-10-17 22:28:46,908 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.38012215
2015-10-17 22:28:47,252 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.5773621
2015-10-17 22:28:47,424 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.38182116
2015-10-17 22:28:48,924 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.3704131
2015-10-17 22:28:49,049 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.41465092
2015-10-17 22:28:49,783 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.40201107
2015-10-17 22:28:50,080 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.4144641
2015-10-17 22:28:50,471 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.45093328
2015-10-17 22:28:50,643 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.5773621
2015-10-17 22:28:51,002 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.44835186
2015-10-17 22:28:52,549 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.43779153
2015-10-17 22:28:52,614 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.45559394
2015-10-17 22:28:53,239 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.45561612
2015-10-17 22:28:53,646 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.4556257
2015-10-17 22:28:53,864 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.45565325
2015-10-17 22:28:54,099 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.5773621
2015-10-17 22:28:54,599 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.45563135
2015-10-17 22:28:56,042 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.45559394
2015-10-17 22:28:56,246 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.455643
2015-10-17 22:28:56,871 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.45561612
2015-10-17 22:28:57,436 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.4556257
2015-10-17 22:28:57,608 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.5773621
2015-10-17 22:28:57,749 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.45565325
2015-10-17 22:28:58,171 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.45563135
2015-10-17 22:28:59,467 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.45559394
2015-10-17 22:28:59,842 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.455643
2015-10-17 22:29:00,405 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.45561612
2015-10-17 22:29:00,858 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.4556257
2015-10-17 22:29:01,202 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.5773621
2015-10-17 22:29:01,233 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.45565325
2015-10-17 22:29:01,624 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.45563135
2015-10-17 22:29:02,889 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.45559394
2015-10-17 22:29:03,311 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.455643
2015-10-17 22:29:03,827 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.45561612
2015-10-17 22:29:04,280 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.4556257
2015-10-17 22:29:04,530 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.58859986
2015-10-17 22:29:04,702 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.45565325
2015-10-17 22:29:05,140 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.45563135
2015-10-17 22:29:06,358 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.45559394
2015-10-17 22:29:06,827 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.455643
2015-10-17 22:29:07,280 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.45561612
2015-10-17 22:29:07,796 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.4556257
2015-10-17 22:29:07,843 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.64679456
2015-10-17 22:29:08,327 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.45565325
2015-10-17 22:29:08,655 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_0 is : 0.45563135
2015-10-17 22:29:08,655 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.64679456
2015-10-17 22:29:08,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0009_m_000000_0 because it is running on unusable node:MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:29:08,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0009_m_000003_0 because it is running on unusable node:MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:29:08,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0009_m_000006_0 because it is running on unusable node:MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:29:08,905 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000000_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 22:29:08,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000007
2015-10-17 22:29:08,905 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000003_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 22:29:08,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000006
2015-10-17 22:29:08,905 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 22:29:08,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000005
2015-10-17 22:29:08,905 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000005 taskAttempt attempt_1445087491445_0009_m_000000_0
2015-10-17 22:29:08,905 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000006_0: Container released on a *lost* node
2015-10-17 22:29:08,905 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000006 taskAttempt attempt_1445087491445_0009_m_000003_0
2015-10-17 22:29:08,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:4 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 22:29:08,905 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000003_0
2015-10-17 22:29:08,905 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000003_0: Container released on a *lost* node
2015-10-17 22:29:08,905 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000000_0
2015-10-17 22:29:08,905 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000007 taskAttempt attempt_1445087491445_0009_m_000006_0
2015-10-17 22:29:08,905 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000000_0: Container released on a *lost* node
2015-10-17 22:29:08,905 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000006_0
2015-10-17 22:29:08,905 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:29:08,905 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:29:08,905 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:29:09,468 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000003_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 22:29:09,562 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 22:29:09,562 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445087491445_0009_m_000003_0
2015-10-17 22:29:09,577 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000003_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 22:29:09,577 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:29:09,577 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:29:09,577 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000003_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:29:09,593 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000000_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 22:29:09,593 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 22:29:09,593 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445087491445_0009_m_000000_0
2015-10-17 22:29:09,593 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000000_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 22:29:09,593 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:29:09,593 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:29:09,593 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:29:09,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 22:29:09,718 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 22:29:09,718 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445087491445_0009_m_000006_0
2015-10-17 22:29:09,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 22:29:09,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:29:09,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:29:09,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:29:09,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:4 ScheduledReds:1 AssignedMaps:4 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 22:29:09,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:15360, vCores:-27> knownNMs=6
2015-10-17 22:29:09,952 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_0 is : 0.45559394
2015-10-17 22:29:10,327 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.455643
2015-10-17 22:29:10,718 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.45561612
2015-10-17 22:29:11,249 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.667
2015-10-17 22:29:11,296 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.4556257
2015-10-17 22:29:11,530 INFO [Socket Reader #1 for port 11421] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 11421: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:29:11,859 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.45565325
2015-10-17 22:29:12,906 INFO [Socket Reader #1 for port 11421] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 11421: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:29:13,624 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_0 is : 0.455643
2015-10-17 22:29:13,812 INFO [Socket Reader #1 for port 11421] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 11421: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:29:14,296 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.45561612
2015-10-17 22:29:14,671 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.667
2015-10-17 22:29:14,827 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.4556257
2015-10-17 22:29:15,343 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.45565325
2015-10-17 22:29:17,749 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.45561612
2015-10-17 22:29:18,046 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.667
2015-10-17 22:29:18,140 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.4556257
2015-10-17 22:29:18,703 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.46472543
2015-10-17 22:29:21,390 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.47342777
2015-10-17 22:29:21,468 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.667
2015-10-17 22:29:21,593 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.4805756
2015-10-17 22:29:22,187 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.54324454
2015-10-17 22:29:24,906 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.5414241
2015-10-17 22:29:25,000 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.667
2015-10-17 22:29:25,172 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.548191
2015-10-17 22:29:25,656 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.5638328
2015-10-17 22:29:28,344 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.56381613
2015-10-17 22:29:28,500 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.667
2015-10-17 22:29:28,953 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.56380385
2015-10-17 22:29:29,281 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.5638328
2015-10-17 22:29:31,859 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.6670524
2015-10-17 22:29:31,922 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.56381613
2015-10-17 22:29:32,547 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.56380385
2015-10-17 22:29:32,719 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.5638328
2015-10-17 22:29:35,266 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.6869944
2015-10-17 22:29:35,438 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.56381613
2015-10-17 22:29:36,016 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.56380385
2015-10-17 22:29:36,203 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.5638328
2015-10-17 22:29:38,594 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.70886743
2015-10-17 22:29:39,047 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.56381613
2015-10-17 22:29:39,641 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.5638328
2015-10-17 22:29:39,672 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.56380385
2015-10-17 22:29:41,954 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.7274369
2015-10-17 22:29:42,563 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.56381613
2015-10-17 22:29:43,126 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.5638328
2015-10-17 22:29:43,251 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.56380385
2015-10-17 22:29:45,469 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.74815685
2015-10-17 22:29:46,016 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.56381613
2015-10-17 22:29:46,673 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.5638328
2015-10-17 22:29:46,813 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.56380385
2015-10-17 22:29:48,876 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.76774925
2015-10-17 22:29:49,704 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.56381613
2015-10-17 22:29:50,313 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.5638328
2015-10-17 22:29:50,329 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.56380385
2015-10-17 22:29:52,473 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.78740394
2015-10-17 22:29:53,004 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.56381613
2015-10-17 22:29:53,785 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.63214415
2015-10-17 22:29:53,973 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.56380385
2015-10-17 22:29:55,973 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.80976504
2015-10-17 22:29:56,410 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.63214415
2015-10-17 22:29:56,426 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.613724
2015-10-17 22:29:57,270 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.667
2015-10-17 22:29:57,582 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.6328002
2015-10-17 22:29:59,410 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.8322731
2015-10-17 22:29:59,879 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.66699964
2015-10-17 22:29:59,942 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.66699964
2015-10-17 22:30:00,676 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.667
2015-10-17 22:30:01,082 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.667
2015-10-17 22:30:01,082 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.667
2015-10-17 22:30:03,351 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.667
2015-10-17 22:30:03,382 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.85390675
2015-10-17 22:30:04,148 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.667
2015-10-17 22:30:04,617 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.667
2015-10-17 22:30:06,851 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.8753015
2015-10-17 22:30:06,867 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.667
2015-10-17 22:30:07,648 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.667
2015-10-17 22:30:08,195 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.667
2015-10-17 22:30:10,226 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.8960703
2015-10-17 22:30:10,351 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.667
2015-10-17 22:30:11,117 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.667
2015-10-17 22:30:11,805 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.667
2015-10-17 22:30:13,805 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.9171938
2015-10-17 22:30:13,930 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.667
2015-10-17 22:30:14,539 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.667
2015-10-17 22:30:15,227 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.667
2015-10-17 22:30:17,261 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.9376461
2015-10-17 22:30:17,402 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.667
2015-10-17 22:30:17,948 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.667
2015-10-17 22:30:18,699 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.667
2015-10-17 22:30:20,667 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.9569434
2015-10-17 22:30:20,777 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.667
2015-10-17 22:30:21,433 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.667
2015-10-17 22:30:22,277 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.667
2015-10-17 22:30:24,058 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 0.977808
2015-10-17 22:30:24,230 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.667
2015-10-17 22:30:24,808 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.667
2015-10-17 22:30:25,746 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.667
2015-10-17 22:30:27,496 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 1.0
2015-10-17 22:30:27,777 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.667
2015-10-17 22:30:28,340 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.66961926
2015-10-17 22:30:28,668 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_0 is : 1.0
2015-10-17 22:30:28,668 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0009_m_000009_0
2015-10-17 22:30:28,668 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:30:28,668 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000008 taskAttempt attempt_1445087491445_0009_m_000009_0
2015-10-17 22:30:28,668 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000009_0
2015-10-17 22:30:28,668 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:30:28,933 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:30:28,933 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0009_m_000009_0
2015-10-17 22:30:28,933 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:30:28,933 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 22:30:29,261 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.667
2015-10-17 22:30:29,918 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:4 ScheduledReds:1 AssignedMaps:4 AssignedReds:0 CompletedMaps:4 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 22:30:30,918 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000008
2015-10-17 22:30:30,918 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:30:30,918 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:30:30,918 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 22:30:30,918 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000012 to attempt_1445087491445_0009_r_000000_0
2015-10-17 22:30:30,918 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 22:30:30,918 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:30:30,918 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:30:30,918 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000012 taskAttempt attempt_1445087491445_0009_r_000000_0
2015-10-17 22:30:30,918 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_r_000000_0
2015-10-17 22:30:30,918 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:30:31,121 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.667
2015-10-17 22:30:31,168 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_r_000000_0 : 13562
2015-10-17 22:30:31,168 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_r_000000_0] using containerId: [container_1445087491445_0009_01_000012 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:55135]
2015-10-17 22:30:31,168 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:30:31,168 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_r_000000
2015-10-17 22:30:31,168 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:30:31,558 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.68293726
2015-10-17 22:30:31,918 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-26> knownNMs=6
2015-10-17 22:30:32,605 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.66925335
2015-10-17 22:30:34,574 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.67209977
2015-10-17 22:30:34,965 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.6946823
2015-10-17 22:30:36,152 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.6814836
2015-10-17 22:30:38,027 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.68352216
2015-10-17 22:30:38,465 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.7062586
2015-10-17 22:30:39,215 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:30:39,387 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_r_000012 asked for a task
2015-10-17 22:30:39,387 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_r_000012 given task: attempt_1445087491445_0009_r_000000_0
2015-10-17 22:30:39,793 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.6930805
2015-10-17 22:30:41,418 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.69466066
2015-10-17 22:30:41,934 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.7193755
2015-10-17 22:30:43,309 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.7050213
2015-10-17 22:30:44,043 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 22:30:44,793 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.7049871
2015-10-17 22:30:45,059 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:45,309 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.7302481
2015-10-17 22:30:46,106 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:46,762 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.71667147
2015-10-17 22:30:47,137 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:48,168 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:48,200 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.71560484
2015-10-17 22:30:48,778 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.7411264
2015-10-17 22:30:49,215 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:49,544 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.0
2015-10-17 22:30:50,200 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.7287034
2015-10-17 22:30:50,247 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:51,278 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:51,590 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.72721595
2015-10-17 22:30:52,122 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.7530733
2015-10-17 22:30:52,309 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:53,106 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:30:53,356 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:53,700 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.7405231
2015-10-17 22:30:54,387 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:55,012 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.7389069
2015-10-17 22:30:55,419 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:55,747 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.7651939
2015-10-17 22:30:56,934 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:57,294 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.752685
2015-10-17 22:30:57,559 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:30:57,981 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:58,481 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.7497583
2015-10-17 22:30:59,075 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:59,200 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.77771735
2015-10-17 22:31:00,153 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:00,763 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.76602906
2015-10-17 22:31:00,888 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:01,216 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:01,857 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.7610232
2015-10-17 22:31:02,294 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:02,763 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.79019064
2015-10-17 22:31:03,388 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:04,138 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:04,185 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.778132
2015-10-17 22:31:04,450 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:05,294 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.77267253
2015-10-17 22:31:05,513 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:06,154 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.8021014
2015-10-17 22:31:06,607 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:07,357 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:07,669 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:07,685 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.7903661
2015-10-17 22:31:08,716 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.78389645
2015-10-17 22:31:08,747 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:09,779 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.8154664
2015-10-17 22:31:09,826 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:10,607 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:10,904 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:11,169 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.8026871
2015-10-17 22:31:12,029 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:12,154 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.7951346
2015-10-17 22:31:13,107 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:13,185 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.8268677
2015-10-17 22:31:13,826 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:14,201 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:14,623 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.81479216
2015-10-17 22:31:15,279 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:15,560 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.806757
2015-10-17 22:31:16,326 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:16,810 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.83908486
2015-10-17 22:31:17,029 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:17,404 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:18,185 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.82702893
2015-10-17 22:31:18,670 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:19,013 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.81826127
2015-10-17 22:31:19,701 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:20,232 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:20,295 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.85096896
2015-10-17 22:31:20,764 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:21,545 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.8390853
2015-10-17 22:31:21,826 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:21,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:31:21,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000013 to attempt_1445087491445_0009_m_000004_1
2015-10-17 22:31:21,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 22:31:21,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:31:21,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000004_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:31:21,920 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000013 taskAttempt attempt_1445087491445_0009_m_000004_1
2015-10-17 22:31:21,920 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000004_1
2015-10-17 22:31:21,920 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:31:21,935 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000004_1 : 13562
2015-10-17 22:31:21,935 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000004_1] using containerId: [container_1445087491445_0009_01_000013 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:31:21,935 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000004_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:31:21,935 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000004
2015-10-17 22:31:22,514 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.82964045
2015-10-17 22:31:22,889 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:22,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-26> knownNMs=6
2015-10-17 22:31:23,357 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:23,748 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.86303383
2015-10-17 22:31:23,936 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:24,279 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:31:24,311 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000013 asked for a task
2015-10-17 22:31:24,311 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000013 given task: attempt_1445087491445_0009_m_000004_1
2015-10-17 22:31:24,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:31:24,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000014 to attempt_1445087491445_0009_m_000003_1
2015-10-17 22:31:24,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:11 RackLocal:1
2015-10-17 22:31:24,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:31:24,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000003_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:31:24,920 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000014 taskAttempt attempt_1445087491445_0009_m_000003_1
2015-10-17 22:31:24,920 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000003_1
2015-10-17 22:31:24,920 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:31:24,936 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000003_1 : 13562
2015-10-17 22:31:24,936 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000003_1] using containerId: [container_1445087491445_0009_01_000014 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:31:24,936 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000003_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:31:24,936 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000003
2015-10-17 22:31:24,951 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.85015327
2015-10-17 22:31:24,998 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:25,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-26> knownNMs=6
2015-10-17 22:31:25,998 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.84108496
2015-10-17 22:31:26,108 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:26,670 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:26,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:31:26,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000015 to attempt_1445087491445_0009_m_000000_1
2015-10-17 22:31:26,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:12 RackLocal:1
2015-10-17 22:31:26,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:31:26,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:31:26,920 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000015 taskAttempt attempt_1445087491445_0009_m_000000_1
2015-10-17 22:31:26,920 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000000_1
2015-10-17 22:31:26,920 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:31:26,936 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000000_1 : 13562
2015-10-17 22:31:26,936 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000000_1] using containerId: [container_1445087491445_0009_01_000015 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:31:26,936 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:31:26,936 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000000
2015-10-17 22:31:27,170 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.87519795
2015-10-17 22:31:27,186 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:27,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-26> knownNMs=6
2015-10-17 22:31:28,264 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:28,358 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:31:28,389 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000014 asked for a task
2015-10-17 22:31:28,389 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000014 given task: attempt_1445087491445_0009_m_000003_1
2015-10-17 22:31:28,389 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.8628427
2015-10-17 22:31:29,311 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:29,420 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.8527071
2015-10-17 22:31:29,858 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:30,389 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:30,623 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.88768715
2015-10-17 22:31:31,248 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:31:31,280 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000015 asked for a task
2015-10-17 22:31:31,280 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000015 given task: attempt_1445087491445_0009_m_000000_1
2015-10-17 22:31:31,451 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:31,920 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.87544346
2015-10-17 22:31:32,623 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:32,639 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_1 is : 0.13104042
2015-10-17 22:31:32,873 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.86451286
2015-10-17 22:31:32,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:31:32,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000016 to attempt_1445087491445_0009_m_000006_1
2015-10-17 22:31:32,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:13 RackLocal:1
2015-10-17 22:31:32,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:31:32,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:31:32,920 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000016 taskAttempt attempt_1445087491445_0009_m_000006_1
2015-10-17 22:31:32,920 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000006_1
2015-10-17 22:31:32,920 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:31:32,936 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000006_1 : 13562
2015-10-17 22:31:32,936 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000006_1] using containerId: [container_1445087491445_0009_01_000016 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:31:32,936 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:31:32,936 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000006
2015-10-17 22:31:32,983 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:33,702 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:33,920 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-26> knownNMs=6
2015-10-17 22:31:33,998 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.8997959
2015-10-17 22:31:34,748 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:35,545 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.8878564
2015-10-17 22:31:35,670 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_1 is : 0.13104042
2015-10-17 22:31:35,795 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:36,233 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:36,436 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.8758426
2015-10-17 22:31:36,842 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:31:36,874 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:36,874 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000016 asked for a task
2015-10-17 22:31:36,874 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000016 given task: attempt_1445087491445_0009_m_000006_1
2015-10-17 22:31:37,405 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.13102706
2015-10-17 22:31:37,467 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.9109009
2015-10-17 22:31:37,920 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:38,686 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_1 is : 0.13104042
2015-10-17 22:31:38,967 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:39,014 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.8996593
2015-10-17 22:31:39,546 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:39,936 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.13101934
2015-10-17 22:31:39,936 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.8869598
2015-10-17 22:31:40,030 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:40,421 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.13102706
2015-10-17 22:31:40,905 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.9228571
2015-10-17 22:31:41,077 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:41,702 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_1 is : 0.18379454
2015-10-17 22:31:42,139 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:42,421 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.9115582
2015-10-17 22:31:42,780 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:42,952 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.13101934
2015-10-17 22:31:43,202 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:43,389 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.89885503
2015-10-17 22:31:43,436 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.13102706
2015-10-17 22:31:44,030 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0009_m_000006
2015-10-17 22:31:44,030 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 22:31:44,030 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0009_m_000006
2015-10-17 22:31:44,030 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:31:44,030 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:31:44,030 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:31:44,358 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.9350748
2015-10-17 22:31:44,671 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.13101135
2015-10-17 22:31:44,671 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:44,718 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_1 is : 0.23924798
2015-10-17 22:31:44,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:13 RackLocal:1
2015-10-17 22:31:44,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-26> knownNMs=6
2015-10-17 22:31:45,780 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.92367876
2015-10-17 22:31:45,796 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:45,968 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.13547046
2015-10-17 22:31:46,061 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:46,452 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.23922287
2015-10-17 22:31:46,890 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.910386
2015-10-17 22:31:46,905 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:47,687 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.13101135
2015-10-17 22:31:47,733 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_1 is : 0.23924798
2015-10-17 22:31:47,890 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.9472054
2015-10-17 22:31:47,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:31:47,921 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:31:47,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000017 to attempt_1445087491445_0009_m_000006_2
2015-10-17 22:31:47,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:31:47,921 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:31:47,921 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:31:47,921 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000017 taskAttempt attempt_1445087491445_0009_m_000006_2
2015-10-17 22:31:47,921 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000006_2
2015-10-17 22:31:47,921 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 22:31:47,937 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000006_2 : 13562
2015-10-17 22:31:47,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000006_2] using containerId: [container_1445087491445_0009_01_000017 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 22:31:47,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:31:47,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000006
2015-10-17 22:31:47,952 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:48,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-26> knownNMs=6
2015-10-17 22:31:48,986 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.23921585
2015-10-17 22:31:49,033 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:49,268 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:49,268 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.93695515
2015-10-17 22:31:49,471 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.23922287
2015-10-17 22:31:50,096 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:50,283 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.92165315
2015-10-17 22:31:50,705 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.23923388
2015-10-17 22:31:50,752 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_1 is : 0.34099868
2015-10-17 22:31:51,190 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:51,377 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.9592246
2015-10-17 22:31:51,627 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:31:51,643 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000017 asked for a task
2015-10-17 22:31:51,643 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000017 given task: attempt_1445087491445_0009_m_000006_2
2015-10-17 22:31:52,002 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.23921585
2015-10-17 22:31:52,205 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:52,377 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:52,487 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.23922287
2015-10-17 22:31:52,705 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.9484003
2015-10-17 22:31:53,315 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:53,690 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.93298066
2015-10-17 22:31:53,721 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.23923388
2015-10-17 22:31:53,768 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_1 is : 0.34742972
2015-10-17 22:31:54,440 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:54,674 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.97135603
2015-10-17 22:31:55,034 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.26087394
2015-10-17 22:31:55,440 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:55,518 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.3473985
2015-10-17 22:31:55,518 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:56,096 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.96036077
2015-10-17 22:31:56,549 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:56,737 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.23923388
2015-10-17 22:31:56,784 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_1 is : 0.34742972
2015-10-17 22:31:57,190 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.944849
2015-10-17 22:31:57,643 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:58,049 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.3474062
2015-10-17 22:31:58,159 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.9832009
2015-10-17 22:31:58,534 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.3473985
2015-10-17 22:31:58,627 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:31:58,706 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:59,018 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.13101135
2015-10-17 22:31:59,581 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.9723383
2015-10-17 22:31:59,753 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.34743717
2015-10-17 22:31:59,799 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:59,815 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_1 is : 0.45565325
2015-10-17 22:32:00,799 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.95619273
2015-10-17 22:32:00,862 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:01,065 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.3474062
2015-10-17 22:32:01,549 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.3473985
2015-10-17 22:32:01,549 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 0.99464595
2015-10-17 22:32:01,724 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:32:01,943 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:02,037 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.13101135
2015-10-17 22:32:02,771 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.34743717
2015-10-17 22:32:02,834 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_1 is : 0.45565325
2015-10-17 22:32:03,021 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.98387265
2015-10-17 22:32:03,084 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:04,084 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.3474062
2015-10-17 22:32:04,099 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:04,240 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.9678114
2015-10-17 22:32:04,334 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000004_0 is : 1.0
2015-10-17 22:32:04,349 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0009_m_000004_0
2015-10-17 22:32:04,349 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:32:04,349 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000003 taskAttempt attempt_1445087491445_0009_m_000004_0
2015-10-17 22:32:04,349 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000004_0
2015-10-17 22:32:04,349 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:32:04,568 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.45559394
2015-10-17 22:32:04,818 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:32:04,818 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0009_m_000004_0
2015-10-17 22:32:04,818 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0009_m_000004_1
2015-10-17 22:32:04,818 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:32:04,818 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 22:32:04,818 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000004_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 22:32:04,818 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000013 taskAttempt attempt_1445087491445_0009_m_000004_1
2015-10-17 22:32:04,818 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000004_1
2015-10-17 22:32:04,818 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:32:04,834 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:32:04,834 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000004_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 22:32:04,834 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 22:32:04,850 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445087491445_0009_m_000004_1
2015-10-17 22:32:04,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000004_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 22:32:04,865 INFO [Socket Reader #1 for port 11421] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 11421: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:32:04,928 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:32:05,053 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.13101135
2015-10-17 22:32:05,162 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:05,787 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.34743717
2015-10-17 22:32:05,928 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000013
2015-10-17 22:32:05,928 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:32:05,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000004_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:32:06,303 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 22:32:06,475 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 0.9975033
2015-10-17 22:32:07,100 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.45563135
2015-10-17 22:32:07,334 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 22:32:07,584 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.45559394
2015-10-17 22:32:07,631 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.9795327
2015-10-17 22:32:07,928 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000003
2015-10-17 22:32:07,928 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:32:07,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:32:07,943 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:32:08,068 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.20778148
2015-10-17 22:32:08,365 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 22:32:08,631 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000007_0 is : 1.0
2015-10-17 22:32:08,631 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0009_m_000007_0
2015-10-17 22:32:08,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:32:08,631 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000004 taskAttempt attempt_1445087491445_0009_m_000007_0
2015-10-17 22:32:08,631 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000007_0
2015-10-17 22:32:08,631 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:32:08,803 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.455643
2015-10-17 22:32:09,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:32:09,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0009_m_000007_0
2015-10-17 22:32:09,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:32:09,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 22:32:09,475 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 22:32:09,928 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:32:10,115 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.45563135
2015-10-17 22:32:10,537 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:32:10,600 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.45559394
2015-10-17 22:32:10,928 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000004
2015-10-17 22:32:10,928 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:32:10,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:32:11,006 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:32:11,068 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.23923388
2015-10-17 22:32:11,131 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 0.9917287
2015-10-17 22:32:11,600 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:32:11,834 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.455643
2015-10-17 22:32:12,631 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:32:13,147 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.45563135
2015-10-17 22:32:13,615 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.5637838
2015-10-17 22:32:13,694 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:32:13,897 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000002_0 is : 1.0
2015-10-17 22:32:13,912 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0009_m_000002_0
2015-10-17 22:32:13,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:32:13,912 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000002 taskAttempt attempt_1445087491445_0009_m_000002_0
2015-10-17 22:32:13,912 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000002_0
2015-10-17 22:32:13,912 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:32:13,990 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:32:13,990 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0009_m_000002_0
2015-10-17 22:32:13,990 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:32:14,006 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 22:32:14,069 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.23923388
2015-10-17 22:32:14,131 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:32:14,756 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:32:14,866 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.455643
2015-10-17 22:32:14,928 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:32:15,834 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:16,053 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000002
2015-10-17 22:32:16,053 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:32:16,053 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:32:16,162 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.56380075
2015-10-17 22:32:16,647 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.5637838
2015-10-17 22:32:16,866 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:17,084 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.23923388
2015-10-17 22:32:17,225 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:32:17,944 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:18,069 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.5638263
2015-10-17 22:32:19,006 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:19,178 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.56380075
2015-10-17 22:32:19,663 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.5637838
2015-10-17 22:32:20,069 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:20,084 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.34743717
2015-10-17 22:32:20,303 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:32:21,085 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.5638263
2015-10-17 22:32:21,131 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:22,194 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:22,194 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.56380075
2015-10-17 22:32:22,569 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.5637838
2015-10-17 22:32:22,678 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.667
2015-10-17 22:32:23,085 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.34743717
2015-10-17 22:32:23,241 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:23,397 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:32:24,100 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.5638263
2015-10-17 22:32:24,147 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.56380075
2015-10-17 22:32:24,303 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:25,210 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.667
2015-10-17 22:32:25,350 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:25,694 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.667
2015-10-17 22:32:25,928 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.5638263
2015-10-17 22:32:26,100 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.34743717
2015-10-17 22:32:26,413 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:26,475 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:32:27,116 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.667
2015-10-17 22:32:27,444 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:28,225 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.667
2015-10-17 22:32:28,538 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:28,710 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.667
2015-10-17 22:32:29,100 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.36226818
2015-10-17 22:32:29,632 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:29,741 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:32:30,147 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.667
2015-10-17 22:32:30,679 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:31,257 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.667
2015-10-17 22:32:31,726 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:31,741 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.6727311
2015-10-17 22:32:32,116 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.455643
2015-10-17 22:32:32,772 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:32,976 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.033333335
2015-10-17 22:32:33,179 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.667
2015-10-17 22:32:33,819 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:34,288 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.6778903
2015-10-17 22:32:34,773 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.7059416
2015-10-17 22:32:34,882 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:35,116 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.455643
2015-10-17 22:32:35,991 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:36,195 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.6782258
2015-10-17 22:32:36,210 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:32:37,054 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:37,304 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.70261264
2015-10-17 22:32:37,804 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.7276418
2015-10-17 22:32:38,116 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.455643
2015-10-17 22:32:38,116 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:39,195 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:39,226 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.69607174
2015-10-17 22:32:39,398 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:32:40,257 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:40,335 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.7202607
2015-10-17 22:32:40,820 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.7463114
2015-10-17 22:32:41,132 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.5444585
2015-10-17 22:32:41,304 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:42,242 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.7184479
2015-10-17 22:32:42,335 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:42,554 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:32:43,351 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.745195
2015-10-17 22:32:43,398 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:43,851 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.7735388
2015-10-17 22:32:44,132 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.5638263
2015-10-17 22:32:44,445 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:45,273 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.752112
2015-10-17 22:32:45,492 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:46,023 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:32:46,367 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.7823597
2015-10-17 22:32:46,539 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:46,867 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.81214195
2015-10-17 22:32:47,132 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.5638263
2015-10-17 22:32:47,617 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:48,289 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.7917229
2015-10-17 22:32:48,664 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:49,101 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:32:49,383 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.8214684
2015-10-17 22:32:49,711 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:49,883 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.8519503
2015-10-17 22:32:50,148 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.5638263
2015-10-17 22:32:50,758 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:51,304 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.8323437
2015-10-17 22:32:51,805 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:52,164 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:32:52,398 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.8608197
2015-10-17 22:32:52,836 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:52,914 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.8913835
2015-10-17 22:32:53,164 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.6386284
2015-10-17 22:32:53,914 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:54,273 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.6386284
2015-10-17 22:32:54,336 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.871161
2015-10-17 22:32:54,961 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:55,242 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:32:55,430 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.89878494
2015-10-17 22:32:55,930 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.92996055
2015-10-17 22:32:56,023 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:56,180 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.667
2015-10-17 22:32:57,039 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:57,352 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.9105383
2015-10-17 22:32:58,086 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:58,352 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:32:58,445 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.9388217
2015-10-17 22:32:58,945 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 0.9712305
2015-10-17 22:32:59,102 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:59,180 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.667
2015-10-17 22:33:00,149 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:00,367 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.95270675
2015-10-17 22:33:01,149 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000003_1 is : 1.0
2015-10-17 22:33:01,149 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0009_m_000003_1
2015-10-17 22:33:01,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000003_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:33:01,149 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000014 taskAttempt attempt_1445087491445_0009_m_000003_1
2015-10-17 22:33:01,149 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000003_1
2015-10-17 22:33:01,149 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:33:01,164 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000003_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:33:01,164 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0009_m_000003_1
2015-10-17 22:33:01,164 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:33:01,164 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 22:33:01,227 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:01,446 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:33:01,461 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 0.97935355
2015-10-17 22:33:02,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:33:02,180 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_2 is : 0.667
2015-10-17 22:33:02,274 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 22:33:03,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000014
2015-10-17 22:33:03,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:33:03,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000003_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:33:03,258 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000000_1 is : 1.0
2015-10-17 22:33:03,258 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0009_m_000000_1
2015-10-17 22:33:03,258 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000000_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:33:03,258 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000015 taskAttempt attempt_1445087491445_0009_m_000000_1
2015-10-17 22:33:03,258 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000000_1
2015-10-17 22:33:03,258 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:33:03,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000000_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:33:03,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0009_m_000000_1
2015-10-17 22:33:03,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:33:03,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 22:33:03,305 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 22:33:03,383 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 0.9908067
2015-10-17 22:33:04,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:33:04,211 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000006_1 is : 1.0
2015-10-17 22:33:04,211 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0009_m_000006_1
2015-10-17 22:33:04,211 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:33:04,211 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000016 taskAttempt attempt_1445087491445_0009_m_000006_1
2015-10-17 22:33:04,211 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000006_1
2015-10-17 22:33:04,211 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:33:04,227 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:33:04,227 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0009_m_000006_1
2015-10-17 22:33:04,227 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0009_m_000006_2
2015-10-17 22:33:04,227 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:33:04,227 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 22:33:04,227 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_2 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 22:33:04,227 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000017 taskAttempt attempt_1445087491445_0009_m_000006_2
2015-10-17 22:33:04,227 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000006_2
2015-10-17 22:33:04,227 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 22:33:04,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_2 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 22:33:04,243 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 22:33:04,243 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445087491445_0009_m_000006_2
2015-10-17 22:33:04,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000006_2 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 22:33:04,368 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 22:33:04,539 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:33:05,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:33:05,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000015
2015-10-17 22:33:05,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:33:05,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000000_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:33:05,414 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:06,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000016
2015-10-17 22:33:06,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:33:06,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000006_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:33:06,493 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:07,540 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:07,774 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:33:08,133 INFO [Socket Reader #1 for port 11421] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 11421: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:33:08,602 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:09,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000017
2015-10-17 22:33:09,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:33:09,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000006_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:33:09,680 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:10,727 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:11,024 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:33:11,774 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:12,805 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:13,899 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:14,149 INFO [IPC Server handler 21 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:33:14,915 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:15,977 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:17,009 INFO [IPC Server handler 14 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:17,337 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:33:18,056 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:19,134 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:20,181 INFO [IPC Server handler 29 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:20,368 INFO [IPC Server handler 24 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:33:21,228 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:22,259 INFO [IPC Server handler 28 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:23,337 INFO [IPC Server handler 1 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:23,478 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:33:24,400 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:25,462 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:26,478 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:26,556 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:33:27,493 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:28,572 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:29,650 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:29,665 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_r_000000_0 is : 0.06666667
2015-10-17 22:33:30,744 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0009_r_000000_0. startIndex 13 maxEvents 10000
2015-10-17 22:33:31,853 INFO [Socket Reader #1 for port 11421] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 11421: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:43:41,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_r_000000_0: AttemptID:attempt_1445087491445_0009_r_000000_0 Timed out after 600 secs
2015-10-17 22:43:41,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_r_000000_0 TaskAttempt Transitioned from RUNNING to FAIL_CONTAINER_CLEANUP
2015-10-17 22:43:41,590 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000012 taskAttempt attempt_1445087491445_0009_r_000000_0
2015-10-17 22:43:41,590 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_r_000000_0
2015-10-17 22:43:41,590 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:44:01,590 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 0 time(s); maxRetries=45
2015-10-17 22:44:21,591 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 1 time(s); maxRetries=45
2015-10-17 22:44:41,592 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 2 time(s); maxRetries=45
2015-10-17 22:44:52,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: TaskAttempt killed because it ran on unusable node MININT-FNANLI5.fareast.corp.microsoft.com:55135. AttemptId:attempt_1445087491445_0009_m_000009_0
2015-10-17 22:44:52,093 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0009_r_000000_0 because it is running on unusable node:MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:44:52,093 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000012
2015-10-17 22:44:52,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_0 TaskAttempt Transitioned from SUCCEEDED to KILLED
2015-10-17 22:44:52,093 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:44:52,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:44:52,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:44:52,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000009 Task Transitioned from SUCCEEDED to SCHEDULED
2015-10-17 22:44:52,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_r_000000_0: Container released on a *lost* node
2015-10-17 22:44:52,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:44:53,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:9 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:13 RackLocal:2
2015-10-17 22:44:53,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:18432, vCores:-2> knownNMs=3
2015-10-17 22:44:54,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:44:54,096 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:44:54,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000018 to attempt_1445087491445_0009_m_000009_1
2015-10-17 22:44:54,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:9 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:13 RackLocal:3
2015-10-17 22:44:54,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:44:54,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:44:54,096 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000018 taskAttempt attempt_1445087491445_0009_m_000009_1
2015-10-17 22:44:54,096 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000009_1
2015-10-17 22:44:54,096 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:44:54,143 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000009_1 : 13562
2015-10-17 22:44:54,143 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000009_1] using containerId: [container_1445087491445_0009_01_000018 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 22:44:54,143 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:44:54,143 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000009
2015-10-17 22:44:54,143 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:44:55,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:14336, vCores:-6> knownNMs=3
2015-10-17 22:44:57,424 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:44:57,440 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000018 asked for a task
2015-10-17 22:44:57,440 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000018 given task: attempt_1445087491445_0009_m_000009_1
2015-10-17 22:45:01,596 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 3 time(s); maxRetries=45
2015-10-17 22:45:04,909 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0009_m_000009
2015-10-17 22:45:04,909 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 22:45:04,909 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0009_m_000009
2015-10-17 22:45:04,909 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:45:04,909 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:45:04,909 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:45:05,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:9 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:13 RackLocal:3
2015-10-17 22:45:05,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-7> knownNMs=3
2015-10-17 22:45:05,143 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.16604526
2015-10-17 22:45:06,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:45:06,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0009_01_000019 to attempt_1445087491445_0009_m_000009_2
2015-10-17 22:45:06,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:9 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:14 RackLocal:3
2015-10-17 22:45:06,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:45:06,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:45:06,096 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0009_01_000019 taskAttempt attempt_1445087491445_0009_m_000009_2
2015-10-17 22:45:06,096 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0009_m_000009_2
2015-10-17 22:45:06,096 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:45:06,112 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0009_m_000009_2 : 13562
2015-10-17 22:45:06,112 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0009_m_000009_2] using containerId: [container_1445087491445_0009_01_000019 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:45:06,112 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:45:06,112 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0009_m_000009
2015-10-17 22:45:07,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0009: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-9> knownNMs=3
2015-10-17 22:45:08,018 INFO [Socket Reader #1 for port 11421] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0009 (auth:SIMPLE)
2015-10-17 22:45:08,034 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0009_m_000019 asked for a task
2015-10-17 22:45:08,034 INFO [IPC Server handler 27 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0009_m_000019 given task: attempt_1445087491445_0009_m_000009_2
2015-10-17 22:45:08,174 INFO [IPC Server handler 19 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.16604526
2015-10-17 22:45:11,206 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.16604526
2015-10-17 22:45:14,237 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.16604526
2015-10-17 22:45:15,206 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.16604526
2015-10-17 22:45:17,268 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.26746505
2015-10-17 22:45:18,222 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.16604526
2015-10-17 22:45:20,331 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.3031575
2015-10-17 22:45:21,237 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.16604526
2015-10-17 22:45:21,597 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 4 time(s); maxRetries=45
2015-10-17 22:45:23,362 INFO [IPC Server handler 15 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.3031575
2015-10-17 22:45:24,269 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.3031575
2015-10-17 22:45:26,394 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.3031575
2015-10-17 22:45:27,285 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.3031575
2015-10-17 22:45:29,426 INFO [IPC Server handler 4 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.39448786
2015-10-17 22:45:30,306 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.3031575
2015-10-17 22:45:32,462 INFO [IPC Server handler 5 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.4402952
2015-10-17 22:45:33,338 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.40611345
2015-10-17 22:45:35,495 INFO [IPC Server handler 12 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.4402952
2015-10-17 22:45:36,355 INFO [IPC Server handler 22 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.4402952
2015-10-17 22:45:38,511 INFO [IPC Server handler 8 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.4402952
2015-10-17 22:45:39,386 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.4402952
2015-10-17 22:45:41,542 INFO [IPC Server handler 17 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.5259578
2015-10-17 22:45:41,683 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 5 time(s); maxRetries=45
2015-10-17 22:45:42,418 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.4402952
2015-10-17 22:45:44,621 INFO [IPC Server handler 7 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.5773621
2015-10-17 22:45:45,449 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.5773621
2015-10-17 22:45:47,652 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.5773621
2015-10-17 22:45:48,465 INFO [IPC Server handler 16 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.5773621
2015-10-17 22:45:50,683 INFO [IPC Server handler 18 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.5773621
2015-10-17 22:45:51,496 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.5773621
2015-10-17 22:45:53,730 INFO [IPC Server handler 3 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.5789328
2015-10-17 22:45:54,527 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.59416187
2015-10-17 22:45:55,418 INFO [IPC Server handler 23 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.59416187
2015-10-17 22:45:56,090 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.5789328
2015-10-17 22:45:56,793 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.667
2015-10-17 22:45:57,543 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.667
2015-10-17 22:45:59,824 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.667
2015-10-17 22:46:00,575 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.667
2015-10-17 22:46:01,684 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 6 time(s); maxRetries=45
2015-10-17 22:46:02,856 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.667
2015-10-17 22:46:03,590 INFO [IPC Server handler 2 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.667
2015-10-17 22:46:05,872 INFO [IPC Server handler 13 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.68677294
2015-10-17 22:46:06,606 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.70589787
2015-10-17 22:46:08,903 INFO [IPC Server handler 26 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.7415892
2015-10-17 22:46:09,622 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.7564796
2015-10-17 22:46:11,950 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.7964628
2015-10-17 22:46:12,637 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.8077641
2015-10-17 22:46:14,966 INFO [IPC Server handler 0 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.85012746
2015-10-17 22:46:15,653 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.859277
2015-10-17 22:46:17,997 INFO [IPC Server handler 6 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.9031483
2015-10-17 22:46:18,685 INFO [IPC Server handler 10 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.9088849
2015-10-17 22:46:21,028 INFO [IPC Server handler 20 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 0.95702505
2015-10-17 22:46:21,685 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 7 time(s); maxRetries=45
2015-10-17 22:46:21,700 INFO [IPC Server handler 25 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_2 is : 0.95674455
2015-10-17 22:46:23,529 INFO [IPC Server handler 11 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0009_m_000009_1 is : 1.0
2015-10-17 22:46:23,529 INFO [IPC Server handler 9 on 11421] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0009_m_000009_1
2015-10-17 22:46:23,529 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:46:23,529 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000018 taskAttempt attempt_1445087491445_0009_m_000009_1
2015-10-17 22:46:23,529 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000009_1
2015-10-17 22:46:23,529 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:46:23,560 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:46:23,560 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0009_m_000009_1
2015-10-17 22:46:23,560 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0009_m_000009_2
2015-10-17 22:46:23,560 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0009_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:46:23,560 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 22:46:23,560 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_2 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 22:46:23,560 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0009_01_000019 taskAttempt attempt_1445087491445_0009_m_000009_2
2015-10-17 22:46:23,560 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0009_m_000009_2
2015-10-17 22:46:23,560 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:46:23,575 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_2 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 22:46:23,575 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 22:46:23,591 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445087491445_0009_m_000009_2
2015-10-17 22:46:23,591 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0009_m_000009_2 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 22:46:23,622 INFO [Socket Reader #1 for port 11421] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 11421: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:46:24,107 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:10 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:14 RackLocal:3
2015-10-17 22:46:25,107 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000018
2015-10-17 22:46:25,107 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0009_01_000019
2015-10-17 22:46:25,107 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:14 RackLocal:3
2015-10-17 22:46:25,107 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000009_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:46:25,107 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0009_m_000009_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:46:41,693 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 8 time(s); maxRetries=45
2015-10-17 22:47:01,698 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 9 time(s); maxRetries=45
2015-10-17 22:47:21,700 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 10 time(s); maxRetries=45
2015-10-17 22:47:41,701 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 11 time(s); maxRetries=45
2015-10-17 22:48:01,702 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 12 time(s); maxRetries=45
2015-10-17 22:48:21,706 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 13 time(s); maxRetries=45
2015-10-17 22:48:41,716 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 14 time(s); maxRetries=45
2015-10-17 22:49:01,724 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 15 time(s); maxRetries=45
2015-10-17 22:49:21,739 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 16 time(s); maxRetries=45
2015-10-17 22:49:41,739 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 17 time(s); maxRetries=45
2015-10-17 22:50:01,742 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 18 time(s); maxRetries=45
2015-10-17 22:50:21,746 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 19 time(s); maxRetries=45
2015-10-17 22:50:41,751 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 20 time(s); maxRetries=45
2015-10-17 22:51:01,756 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 21 time(s); maxRetries=45
2015-10-17 22:51:21,757 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 22 time(s); maxRetries=45
2015-10-17 22:51:41,758 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 23 time(s); maxRetries=45
2015-10-17 22:52:01,759 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 24 time(s); maxRetries=45
2015-10-17 22:52:21,760 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 25 time(s); maxRetries=45
2015-10-17 22:52:41,768 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 26 time(s); maxRetries=45
2015-10-17 22:53:01,773 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 27 time(s); maxRetries=45
2015-10-17 22:53:21,300 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 0 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:53:40,816 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 1 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:54:00,322 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 2 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:54:19,870 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 3 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:54:39,418 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 4 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:54:58,962 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 5 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:55:18,494 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 6 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:55:38,010 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 7 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:55:57,558 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 8 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:56:17,061 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 9 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:57:05,136 INFO [ContainerLauncher #7] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 0 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
