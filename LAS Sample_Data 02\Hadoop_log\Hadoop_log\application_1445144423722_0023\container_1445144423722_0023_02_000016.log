2015-10-18 18:19:41,867 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 18:19:41,976 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 18:19:41,976 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-18 18:19:42,007 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-18 18:19:42,007 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445144423722_0023, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@1623b78d)
2015-10-18 18:19:42,132 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-18 18:19:42,695 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445144423722_0023
2015-10-18 18:19:43,101 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-18 18:19:43,742 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-18 18:19:43,757 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@db57326
2015-10-18 18:19:44,007 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:939524096+134217728
2015-10-18 18:19:44,070 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-18 18:19:44,070 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-18 18:19:44,070 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-18 18:19:44,070 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-18 18:19:44,070 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-18 18:19:44,070 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-18 18:19:46,617 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:19:46,617 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48252774; bufvoid = 104857600
2015-10-18 18:19:46,617 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17306072(69224288); length = 8908325/6553600
2015-10-18 18:19:46,617 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57321526 kvi 14330376(57321504)
2015-10-18 18:19:57,026 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-18 18:19:57,026 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57321526 kv 14330376(57321504) kvi 12128560(48514240)
2015-10-18 18:19:59,277 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:19:59,277 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57321526; bufend = 695920; bufvoid = 104857600
2015-10-18 18:19:59,277 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14330376(57321504); kvend = 5416856(21667424); length = 8913521/6553600
2015-10-18 18:19:59,277 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9764656 kvi 2441160(9764640)
2015-10-18 18:20:08,652 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-18 18:20:08,652 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9764656 kv 2441160(9764640) kvi 236684(946736)
2015-10-18 18:20:11,011 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:20:11,011 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9764656; bufend = 58004947; bufvoid = 104857600
2015-10-18 18:20:11,011 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2441160(9764640); kvend = 19744112(78976448); length = 8911449/6553600
2015-10-18 18:20:11,011 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67073683 kvi 16768416(67073664)
2015-10-18 18:20:19,527 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-18 18:20:19,527 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67073683 kv 16768416(67073664) kvi 14561752(58247008)
2015-10-18 18:20:21,090 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:20:21,090 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67073683; bufend = 10426966; bufvoid = 104857600
2015-10-18 18:20:21,090 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16768416(67073664); kvend = 7849620(31398480); length = 8918797/6553600
2015-10-18 18:20:21,090 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19495718 kvi 4873924(19495696)
2015-10-18 18:20:29,434 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-18 18:20:29,434 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 19495718 kv 4873924(19495696) kvi 2677448(10709792)
2015-10-18 18:20:30,997 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:20:30,997 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 19495718; bufend = 67755457; bufvoid = 104857600
2015-10-18 18:20:30,997 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 4873924(19495696); kvend = 22181748(88726992); length = 8906577/6553600
2015-10-18 18:20:30,997 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 76824209 kvi 19206048(76824192)
2015-10-18 18:20:39,231 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-18 18:20:39,231 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 76824209 kv 19206048(76824192) kvi 16996444(67985776)
2015-10-18 18:20:41,845 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:20:41,845 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 76824209; bufend = 20191510; bufvoid = 104857600
2015-10-18 18:20:41,845 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 19206048(76824192); kvend = 10290756(41163024); length = 8915293/6553600
2015-10-18 18:20:41,845 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29260262 kvi 7315060(29260240)
2015-10-18 18:20:54,517 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-18 18:20:54,517 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29260262 kv 7315060(29260240) kvi 5114312(20457248)
2015-10-18 18:20:56,770 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:20:56,770 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29260262; bufend = 77519736; bufvoid = 104857600
2015-10-18 18:20:56,770 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7315060(29260240); kvend = 24622816(98491264); length = 8906645/6553600
2015-10-18 18:20:56,770 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 86588488 kvi 21647116(86588464)
2015-10-18 18:21:08,302 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-18 18:21:08,318 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 86588488 kv 21647116(86588464) kvi 19453336(77813344)
2015-10-18 18:21:09,755 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-18 18:21:09,755 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-18 18:21:09,755 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 86588488; bufend = 19607110; bufvoid = 104857597
2015-10-18 18:21:09,755 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 21647116(86588464); kvend = 14652112(58608448); length = 6995005/6553600
2015-10-18 18:21:18,084 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 7
2015-10-18 18:21:18,099 INFO [main] org.apache.hadoop.mapred.Merger: Merging 8 sorted segments
2015-10-18 18:21:18,115 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 8 segments left of total size: 288286633 bytes
2015-10-18 18:21:43,431 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445144423722_0023_m_000007_1001 is done. And is in the process of committing
2015-10-18 18:21:43,494 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445144423722_0023_m_000007_1001' done.
