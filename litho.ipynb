{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8f1845e9", "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import lasio as las\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "from sklearn.svm import SVC\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score"]}, {"cell_type": "code", "execution_count": 2, "id": "bbe78c78", "metadata": {}, "outputs": [], "source": ["# defining a function that extract LAS files from a folder\n", "\n", "def load_las_files(folder_path):\n", "    wells = []\n", "    for filename in os.listdir(folder_path):\n", "        if filename.endswith(\".las\"):\n", "            las_path = os.path.join(folder_path, filename)\n", "            well = las.read(las_path)\n", "            wells.append(well)\n", "    return wells"]}, {"cell_type": "code", "execution_count": 3, "id": "2f5153fe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 4 LAS files.\n"]}], "source": ["folder_path = \"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/LAS Data\"\n", "\n", "# Call the function with the folder path\n", "wells = load_las_files(folder_path)\n", "\n", "print(f\"Loaded {len(wells)} LAS files.\")"]}, {"cell_type": "code", "execution_count": 4, "id": "922908ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['DEPT', 'BHT', 'CAL', 'CHT', 'C<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>Q<PERSON>', 'DEPTH', 'GR', 'MBVI', 'MBVM', 'MCBW', 'MPHE', 'MPHS', 'MPRM', 'PEQH', 'PORZ', 'PORZ<PERSON>', 'TEN', 'TTEN', 'WTBH', 'ZCORQ<PERSON>', 'ZDE<PERSON>', 'ZDENQ<PERSON>', 'ZDNC', 'ZDNCQ<PERSON>']\n", "['DEPT', 'BHT', 'CAL', 'CHT', 'C<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>Q<PERSON>', 'DEPTH', 'GR', 'MBVI', 'MBVM', 'MCBW', 'MPHE', 'MPHS', 'MPRM', 'PEQH', 'PORZ', 'PORZ<PERSON>', 'TEN', 'TTEN', 'WTBH', 'ZCORQ<PERSON>', 'ZDE<PERSON>', 'ZDENQ<PERSON>', 'ZDNC', 'ZDNCQ<PERSON>']\n", "['DEPT', 'GR', 'DT', 'RHOB', 'DRHO', 'NPHI']\n", "['DEPT', 'GR', 'DT', 'RHOB', 'DRHO', 'NPHI']\n"]}], "source": ["for well in wells:\n", "    print(well.keys())"]}, {"cell_type": "code", "execution_count": 5, "id": "0bec141b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 98136 entries, 0 to 98135\n", "Data columns (total 31 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   DEPT    98136 non-null  float64\n", " 1   BHT     1862 non-null   float64\n", " 2   CAL     1836 non-null   float64\n", " 3   CHT     1850 non-null   float64\n", " 4   CN      1842 non-null   float64\n", " 5   CNC     1788 non-null   float64\n", " 6   CNCQH   1796 non-null   float64\n", " 7   CNQH    1852 non-null   float64\n", " 8   DEPTH   1862 non-null   float64\n", " 9   GR      97794 non-null  float64\n", " 10  MBVI    1728 non-null   float64\n", " 11  MBVM    1728 non-null   float64\n", " 12  MCBW    1728 non-null   float64\n", " 13  MPHE    1728 non-null   float64\n", " 14  MPHS    1728 non-null   float64\n", " 15  MPRM    1728 non-null   float64\n", " 16  PEQH    1822 non-null   float64\n", " 17  PORZ    1816 non-null   float64\n", " 18  PORZC   1816 non-null   float64\n", " 19  TEN     1850 non-null   float64\n", " 20  TTEN    1850 non-null   float64\n", " 21  WTBH    1848 non-null   float64\n", " 22  ZCORQH  1822 non-null   float64\n", " 23  ZDEN    1816 non-null   float64\n", " 24  ZDENQH  1822 non-null   float64\n", " 25  ZDNC    1816 non-null   float64\n", " 26  ZDNCQH  1822 non-null   float64\n", " 27  DT      43418 non-null  float64\n", " 28  RHOB    4150 non-null   float64\n", " 29  DRHO    4150 non-null   float64\n", " 30  NPHI    6190 non-null   float64\n", "dtypes: float64(31)\n", "memory usage: 23.2 MB\n", "None\n", "       DEPT  BHT  CAL  CHT  CN  CNC  CNCQH  CNQH  DEPTH  GR  ...    WTBH  \\\n", "0  2772.750  NaN  NaN  NaN NaN  NaN    NaN   NaN    NaN NaN  ...     NaN   \n", "1  2772.875  NaN  NaN  NaN NaN  NaN    NaN   NaN    NaN NaN  ...     NaN   \n", "2  2773.000  NaN  NaN  NaN NaN  NaN    NaN   NaN    NaN NaN  ...  87.943   \n", "3  2773.125  NaN  NaN  NaN NaN  NaN    NaN   NaN    NaN NaN  ...  88.166   \n", "4  2773.250  NaN  NaN  NaN NaN  NaN    NaN   NaN    NaN NaN  ...  88.252   \n", "\n", "   ZCORQH  ZDEN  ZDENQH  ZDNC  ZDNCQH  DT  RHOB  DRHO  NPHI  \n", "0     NaN   NaN     NaN   NaN     NaN NaN   NaN   NaN   NaN  \n", "1     NaN   NaN     NaN   NaN     NaN NaN   NaN   NaN   NaN  \n", "2     NaN   NaN     NaN   NaN     NaN NaN   NaN   NaN   NaN  \n", "3     NaN   NaN     NaN   NaN     NaN NaN   NaN   NaN   NaN  \n", "4     NaN   NaN     NaN   NaN     NaN NaN   NaN   NaN   NaN  \n", "\n", "[5 rows x 31 columns]\n"]}], "source": ["data = []\n", "for well in wells:\n", "    df = well.df()  # Convert LAS data to a DataFrame\n", "\n", "    # Ensure DEPT is not used as the index\n", "    if df.index.name == 'DEPT':\n", "        df = df.reset_index()\n", "\n", "    # Ensure consistent data type for DEPT\n", "    df['DEPT'] = df['DEPT'].astype(float)\n", "\n", "    data.append(df)\n", "\n", "# Concatenate all DataFrames\n", "data = pd.concat(data, axis=0, ignore_index=True)\n", "\n", "print(data.info())\n", "print(data.head())"]}, {"cell_type": "code", "execution_count": 6, "id": "72e31dd1", "metadata": {}, "outputs": [{"data": {"text/plain": ["DEPT          0\n", "BHT       96274\n", "CAL       96300\n", "CHT       96286\n", "CN        96294\n", "CNC       96348\n", "CNCQH     96340\n", "CNQH      96284\n", "DEPTH     96274\n", "GR          342\n", "MBVI      96408\n", "MBVM      96408\n", "MCBW      96408\n", "MPHE      96408\n", "MPHS      96408\n", "MPRM      96408\n", "PEQH      96314\n", "PORZ      96320\n", "PORZC     96320\n", "TEN       96286\n", "TTEN      96286\n", "WTBH      96288\n", "ZCORQH    96314\n", "ZDEN      96320\n", "ZDENQH    96314\n", "ZDNC      96320\n", "ZDNCQH    96314\n", "DT        54718\n", "RHOB      93986\n", "DRHO      93986\n", "NPHI      91946\n", "dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["data.isnull().sum()"]}, {"cell_type": "code", "execution_count": 9, "id": "8f754145", "metadata": {}, "outputs": [], "source": ["# remove the nulls\n", "data.dropna(inplace=True)\n", "\n", "# making GR as a int\n", "data['GR']=data['GR'].astype(int)"]}, {"cell_type": "code", "execution_count": 10, "id": "b297b347", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPT</th>\n", "      <th>BHT</th>\n", "      <th>CAL</th>\n", "      <th>CHT</th>\n", "      <th>CN</th>\n", "      <th>CNC</th>\n", "      <th>CNCQH</th>\n", "      <th>CNQH</th>\n", "      <th>DEPTH</th>\n", "      <th>GR</th>\n", "      <th>...</th>\n", "      <th>WTBH</th>\n", "      <th>ZCORQH</th>\n", "      <th>ZDEN</th>\n", "      <th>ZDENQH</th>\n", "      <th>ZDNC</th>\n", "      <th>ZDNCQH</th>\n", "      <th>DT</th>\n", "      <th>RHOB</th>\n", "      <th>DRHO</th>\n", "      <th>NPHI</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "<p>0 rows × 31 columns</p>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [DEPT, BHT, CAL, CHT, CN, CNC, CNCQH, CNQH, DEPTH, GR, MBVI, MBVM, MCBW, MPHE, MPHS, MPRM, PEQH, PORZ, PORZC, TEN, TTEN, WTBH, ZCORQH, ZDEN, ZDENQH, ZDNC, ZDNCQH, DT, RHOB, DRHO, NPHI]\n", "Index: []\n", "\n", "[0 rows x 31 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": null, "id": "2b47da3e", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "Could not interpret value `GAMMA` for `x`. An entry with this name does not appear in `data`.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[11], line 4\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m#  <PERSON> vs. <PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m      3\u001b[0m plt\u001b[38;5;241m.\u001b[39mfigure(figsize\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m10\u001b[39m, \u001b[38;5;241m6\u001b[39m))\n\u001b[1;32m----> 4\u001b[0m \u001b[43msns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mscatterplot\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mGAMMA\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mPOROSI<PERSON>\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mhue\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mLITH\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      5\u001b[0m plt\u001b[38;5;241m.\u001b[39mtitle(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mGamma Ray vs. Porosity\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m      6\u001b[0m plt\u001b[38;5;241m.\u001b[39mxlabel(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mGamma Ray (API)\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\seaborn\\relational.py:615\u001b[0m, in \u001b[0;36mscatterplot\u001b[1;34m(data, x, y, hue, size, style, palette, hue_order, hue_norm, sizes, size_order, size_norm, markers, style_order, legend, ax, **kwargs)\u001b[0m\n\u001b[0;32m    606\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mscatterplot\u001b[39m(\n\u001b[0;32m    607\u001b[0m     data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m,\n\u001b[0;32m    608\u001b[0m     x\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, y\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, hue\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01m<PERSON>one\u001b[39;00m, size\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, style\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    612\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs\n\u001b[0;32m    613\u001b[0m ):\n\u001b[1;32m--> 615\u001b[0m     p \u001b[38;5;241m=\u001b[39m \u001b[43m_ScatterPlotter\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    616\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    617\u001b[0m \u001b[43m        \u001b[49m\u001b[43mvariables\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mdict\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mhue\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mhue\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msize\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msize\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstyle\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstyle\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    618\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlegend\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlegend\u001b[49m\n\u001b[0;32m    619\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    621\u001b[0m     p\u001b[38;5;241m.\u001b[39mmap_hue(palette\u001b[38;5;241m=\u001b[39mpalette, order\u001b[38;5;241m=\u001b[39mhue_order, norm\u001b[38;5;241m=\u001b[39mhue_norm)\n\u001b[0;32m    622\u001b[0m     p\u001b[38;5;241m.\u001b[39mmap_size(sizes\u001b[38;5;241m=\u001b[39msizes, order\u001b[38;5;241m=\u001b[39msize_order, norm\u001b[38;5;241m=\u001b[39msize_norm)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\seaborn\\relational.py:396\u001b[0m, in \u001b[0;36m_ScatterPlotter.__init__\u001b[1;34m(self, data, variables, legend)\u001b[0m\n\u001b[0;32m    387\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39m, data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, variables\u001b[38;5;241m=\u001b[39m{}, legend\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[0;32m    388\u001b[0m \n\u001b[0;32m    389\u001b[0m     \u001b[38;5;66;03m# TODO this is messy, we want the mapping to be agnostic about\u001b[39;00m\n\u001b[0;32m    390\u001b[0m     \u001b[38;5;66;03m# the kind of plot to draw, but for the time being we need to set\u001b[39;00m\n\u001b[0;32m    391\u001b[0m     \u001b[38;5;66;03m# this information so the SizeMapping can use it\u001b[39;00m\n\u001b[0;32m    392\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_default_size_range \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m    393\u001b[0m         np\u001b[38;5;241m.\u001b[39mr_[\u001b[38;5;241m.5\u001b[39m, \u001b[38;5;241m2\u001b[39m] \u001b[38;5;241m*\u001b[39m np\u001b[38;5;241m.\u001b[39msquare(mpl\u001b[38;5;241m.\u001b[39mrcParams[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlines.markersize\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n\u001b[0;32m    394\u001b[0m     )\n\u001b[1;32m--> 396\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvariables\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvariables\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    398\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlegend \u001b[38;5;241m=\u001b[39m legend\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\seaborn\\_base.py:634\u001b[0m, in \u001b[0;36mVectorPlotter.__init__\u001b[1;34m(self, data, variables)\u001b[0m\n\u001b[0;32m    629\u001b[0m \u001b[38;5;66;03m# var_ordered is relevant only for categorical axis variables, and may\u001b[39;00m\n\u001b[0;32m    630\u001b[0m \u001b[38;5;66;03m# be better handled by an internal axis information object that tracks\u001b[39;00m\n\u001b[0;32m    631\u001b[0m \u001b[38;5;66;03m# such information and is set up by the scale_* methods. The analogous\u001b[39;00m\n\u001b[0;32m    632\u001b[0m \u001b[38;5;66;03m# information for numeric axes would be information about log scales.\u001b[39;00m\n\u001b[0;32m    633\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_var_ordered \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mx\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28;01mFalse\u001b[39;00m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124my\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28;01mFalse\u001b[39;00m}  \u001b[38;5;66;03m# alt., used DefaultDict\u001b[39;00m\n\u001b[1;32m--> 634\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43massign_variables\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvariables\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    636\u001b[0m \u001b[38;5;66;03m# TODO Lots of tests assume that these are called to initialize the\u001b[39;00m\n\u001b[0;32m    637\u001b[0m \u001b[38;5;66;03m# mappings to default values on class initialization. I'd prefer to\u001b[39;00m\n\u001b[0;32m    638\u001b[0m \u001b[38;5;66;03m# move away from that and only have a mapping when explicitly called.\u001b[39;00m\n\u001b[0;32m    639\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m var \u001b[38;5;129;01min\u001b[39;00m [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhue\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msize\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstyle\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\seaborn\\_base.py:679\u001b[0m, in \u001b[0;36mVectorPlotter.assign_variables\u001b[1;34m(self, data, variables)\u001b[0m\n\u001b[0;32m    674\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    675\u001b[0m     \u001b[38;5;66;03m# When dealing with long-form input, use the newer PlotData\u001b[39;00m\n\u001b[0;32m    676\u001b[0m     \u001b[38;5;66;03m# object (internal but introduced for the objects interface)\u001b[39;00m\n\u001b[0;32m    677\u001b[0m     \u001b[38;5;66;03m# to centralize / standardize data consumption logic.\u001b[39;00m\n\u001b[0;32m    678\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minput_format \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlong\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m--> 679\u001b[0m     plot_data \u001b[38;5;241m=\u001b[39m \u001b[43mPlotData\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvariables\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    680\u001b[0m     frame \u001b[38;5;241m=\u001b[39m plot_data\u001b[38;5;241m.\u001b[39mframe\n\u001b[0;32m    681\u001b[0m     names \u001b[38;5;241m=\u001b[39m plot_data\u001b[38;5;241m.\u001b[39mnames\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\seaborn\\_core\\data.py:58\u001b[0m, in \u001b[0;36mPlotData.__init__\u001b[1;34m(self, data, variables)\u001b[0m\n\u001b[0;32m     51\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\n\u001b[0;32m     52\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m     53\u001b[0m     data: DataSource,\n\u001b[0;32m     54\u001b[0m     variables: \u001b[38;5;28mdict\u001b[39m[\u001b[38;5;28mstr\u001b[39m, VariableSpec],\n\u001b[0;32m     55\u001b[0m ):\n\u001b[0;32m     57\u001b[0m     data \u001b[38;5;241m=\u001b[39m handle_data_source(data)\n\u001b[1;32m---> 58\u001b[0m     frame, names, ids \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_assign_variables\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvariables\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     60\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mframe \u001b[38;5;241m=\u001b[39m frame\n\u001b[0;32m     61\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnames \u001b[38;5;241m=\u001b[39m names\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\seaborn\\_core\\data.py:232\u001b[0m, in \u001b[0;36mPlotData._assign_variables\u001b[1;34m(self, data, variables)\u001b[0m\n\u001b[0;32m    230\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    231\u001b[0m         err \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAn entry with this name does not appear in `data`.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m--> 232\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValue<PERSON>rror\u001b[39;00m(err)\n\u001b[0;32m    234\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    235\u001b[0m \n\u001b[0;32m    236\u001b[0m     \u001b[38;5;66;03m# Otherwise, assume the value somehow represents data\u001b[39;00m\n\u001b[0;32m    237\u001b[0m \n\u001b[0;32m    238\u001b[0m     \u001b[38;5;66;03m# Ignore empty data structures\u001b[39;00m\n\u001b[0;32m    239\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(val, Sized) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(val) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n", "\u001b[1;31mValueError\u001b[0m: Could not interpret value `GAMMA` for `x`. An entry with this name does not appear in `data`."]}, {"data": {"text/plain": ["<Figure size 1000x600 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#  Gamma Ray vs. <PERSON><PERSON><PERSON>\n", "\n", "plt.figure(figsize=(10, 6))\n", "sns.scatterplot(x='GR', y='NPHI', hue='LITH', data=data)\n", "plt.title('Gamma Ray vs. <PERSON><PERSON><PERSON>')\n", "plt.xlabel('Gamma Ray (API)')\n", "plt.ylabel('Porosity')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "1e4ee4e6", "metadata": {}, "outputs": [], "source": ["# Porosity vs Permeability\n", "\n", "plt.figure(figsize=(10, 6))\n", "sns.scatterplot(x='POROSITY', y='PERM', hue='LITH', data=data)\n", "plt.title('Porosity vs Permeability')\n", "plt.xlabel('Porosity')\n", "plt.ylabel('Permeability')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "42b7bd5f", "metadata": {}, "outputs": [], "source": ["# getting LITH count to determine if there is Imbalance in the data\n", "lith_counts = data['LITH'].value_counts()\n", "lith_counts"]}, {"cell_type": "code", "execution_count": null, "id": "67c90608", "metadata": {}, "outputs": [], "source": ["# plotting it\n", "plt.figure(figsize=(10, 6))\n", "sns.barplot(x=lith_counts.index, y=lith_counts.values,palette='colorblind')\n", "plt.title('LITH Count')\n", "plt.xlabel('LITH')\n", "plt.ylabel('Count')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "816026ab", "metadata": {}, "outputs": [], "source": ["# Separate features and target\n", "X = data.drop(columns=['LITH'])  # Features\n", "y = data['LITH']  # Target"]}, {"cell_type": "code", "execution_count": null, "id": "e8b62a78", "metadata": {}, "outputs": [], "source": ["print(X) # Checking for data leakage"]}, {"cell_type": "code", "execution_count": null, "id": "156f6391", "metadata": {}, "outputs": [], "source": ["# Split the data into training and testing sets (e.g., 80% training, 20% testing)\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)"]}, {"cell_type": "code", "execution_count": null, "id": "0f504423", "metadata": {}, "outputs": [], "source": ["# Feature Scaling\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)"]}, {"cell_type": "code", "execution_count": null, "id": "7f6f0772", "metadata": {}, "outputs": [], "source": ["rf_model=RandomForestClassifier(random_state=42)\n", "rf_model.fit(X_train_scaled, y_train)"]}, {"cell_type": "code", "execution_count": null, "id": "44e953d9", "metadata": {}, "outputs": [], "source": ["y_pred = rf_model.predict(X_test_scaled)"]}, {"cell_type": "code", "execution_count": null, "id": "45c5678e", "metadata": {}, "outputs": [], "source": ["class_report = classification_report(y_test, y_pred)\n", "print(\"Classification Report:\")\n", "print(class_report)"]}, {"cell_type": "code", "execution_count": null, "id": "b9e25fd8", "metadata": {}, "outputs": [], "source": ["#confustion matrix\n", "\n", "sns.heatmap(confusion_matrix(y_test, y_pred), annot=True, fmt='d', cmap='Blues')\n", "plt.title('SVM Confusion Matrix')\n", "plt.xlabel('Predicted')\n", "plt.ylabel('True')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "f923ea3f", "metadata": {}, "outputs": [], "source": ["# #Observations:\n", "# the model acheived 100% accuracy on the test set\n", "# This is highly unusual for real-world datasets and suggests there might be an issue with the data or the model's evaluation process.\n", "# Let's try using an SVM (Support Vector Machine) to see if the issue persists. If the SVM also achieves 100% accuracy, it strongly indicates a problem with the dataset."]}, {"cell_type": "code", "execution_count": null, "id": "3667c6d2", "metadata": {}, "outputs": [], "source": ["# Split the data into training and testing sets (e.g., 80% training, 20% testing)\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)"]}, {"cell_type": "code", "execution_count": null, "id": "82338082", "metadata": {}, "outputs": [], "source": ["# Feature Scaling\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)"]}, {"cell_type": "code", "execution_count": null, "id": "fea1c5b2", "metadata": {}, "outputs": [], "source": ["# Train SVM model\n", "svm_model = SVC(kernel='rbf', random_state=42)\n", "svm_model.fit(X_train_scaled, y_train)"]}, {"cell_type": "code", "execution_count": null, "id": "3fe7aafc", "metadata": {}, "outputs": [], "source": ["# Evaluate SVM model\n", "y_pred_svm = svm_model.predict(X_test_scaled)\n", "print(\"SVM Classification Report:\\n\", classification_report(y_test, y_pred_svm))\n", "print(\"SVM Confusion Matrix:\\n\", confusion_matrix(y_test, y_pred_svm))"]}, {"cell_type": "code", "execution_count": null, "id": "a918630d", "metadata": {}, "outputs": [], "source": ["#confusion matrix\n", "sns.heatmap(confusion_matrix(y_test, y_pred_svm), annot=True, fmt='d', cmap='Blues')\n", "plt.title('SVM Confusion Matrix')\n", "plt.xlabel('Predicted')\n", "plt.ylabel('True')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "9082b089", "metadata": {}, "outputs": [], "source": ["# Observations:\n", "# Random Forest model achieving 100% accuracy while SVM achieves around 98% strongly suggests one or more of the following issues: ##### 1- Overfitting in Random Forest ##### 2- Feature Dominance ##### 3- Data Leakage ##### 4- Dataset Might Be Too Easy\n", "# First let's check feature importance and correlation to determine which feature is dominent."]}, {"cell_type": "code", "execution_count": null, "id": "708bfff6", "metadata": {}, "outputs": [], "source": ["# Feature importance\n", "importances = rf_model.feature_importances_\n", "feature_names = X.columns\n", "feature_importance_df = pd.DataFrame({'Feature': feature_names, 'Importance': importances})\n", "print(feature_importance_df.sort_values(by='Importance', ascending=False))\n", "\n", "# plotting it for better visual\n", "\n", "sns.barplot(x='Importance', y='Feature', data=feature_importance_df.sort_values(by='Importance', ascending=False))\n", "plt.title('Feature Importance')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "be9d4b30", "metadata": {}, "outputs": [], "source": ["# data correlation\n", "corr_matrix = data.corr()\n", "plt.figure(figsize=(10, 6))\n", "sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', fmt=\".2f\")\n", "plt.title('Correlation Matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "f0729582", "metadata": {}, "outputs": [], "source": ["# Observaion\n", "# It seem evident that the Porosity feature is dominant\n", "# and there's high correlation between porosity and the target (Lithology)\n", "# Let's try ommitting the porosity and check model performance."]}, {"cell_type": "code", "execution_count": null, "id": "0a35da2f", "metadata": {}, "outputs": [], "source": ["X_reduced = X.drop(columns=['POROSITY'])\n", "\n", "X_train_reduced, X_test_reduced, y_train, y_test = train_test_split(X_reduced, y, test_size=0.2, random_state=42, shuffle=True)\n", " # made sure the data is shuffled to avoid data memorization\n", "\n", "\n", "# Feature Scaling\n", "scaler = StandardScaler()\n", "X_train_scaled1 = scaler.fit_transform(X_train_reduced)\n", "X_test_scaled1 = scaler.transform(X_test_reduced)\n", "\n", "\n", "SVM_model_reduced = SVC(kernel='rbf',random_state=42, class_weight='balanced')\n", "SVM_model_reduced.fit(X_train_scaled1, y_train)\n", "\n", "SVM_y_pred_reduced = SVM_model_reduced.predict(X_test_scaled1)\n", "\n", "print(\"Reduced Feature RF Classification Report:\\n\", classification_report(y_test, SVM_y_pred_reduced))"]}, {"cell_type": "code", "execution_count": null, "id": "85440d91", "metadata": {}, "outputs": [], "source": ["#confusion matrix\n", "sns.heatmap(confusion_matrix(y_test, SVM_y_pred_reduced), annot=True, fmt='d', cmap='Blues')\n", "plt.title('SVM Confusion Matrix')\n", "plt.xlabel('Predicted')\n", "plt.ylabel('True')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "813c9163", "metadata": {}, "outputs": [], "source": ["# Observations:\n", "# As it seems the model acheived good results on class 1 and 2, this may due to the inbalance mentioned earlier that was not addressed, or due to features not scaled.\n", "# As shown above the model had improved.\n", "# BUT It's facing some issues with Class 0 persicion and Class 2 recall.\n", "# that's indicating that omitting Porosity had a negative impact on the model."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}