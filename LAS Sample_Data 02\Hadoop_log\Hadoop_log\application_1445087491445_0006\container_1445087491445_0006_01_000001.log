2015-10-17 22:26:39,297 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445087491445_0006_000001
2015-10-17 22:26:40,766 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 22:26:40,766 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 6 cluster_timestamp: 1445087491445 } attemptId: 1 } keyId: -1547346236)
2015-10-17 22:26:41,000 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 22:26:42,078 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 22:26:42,203 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 22:26:42,250 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 22:26:42,250 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 22:26:42,250 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 22:26:42,250 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 22:26:42,250 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 22:26:42,266 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 22:26:42,266 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 22:26:42,266 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 22:26:42,359 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 22:26:42,391 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 22:26:42,438 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 22:26:42,453 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 22:26:42,531 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 22:26:43,109 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 22:26:43,188 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 22:26:43,188 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 22:26:43,203 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445087491445_0006 to jobTokenSecretManager
2015-10-17 22:26:43,453 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445087491445_0006 because: not enabled; too many maps; too much input;
2015-10-17 22:26:43,469 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445087491445_0006 = 1313861632. Number of splits = 10
2015-10-17 22:26:43,469 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445087491445_0006 = 1
2015-10-17 22:26:43,469 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0006Job Transitioned from NEW to INITED
2015-10-17 22:26:43,484 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445087491445_0006.
2015-10-17 22:26:43,516 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 22:26:43,578 INFO [Socket Reader #1 for port 55199] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 55199
2015-10-17 22:26:43,578 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 22:26:43,578 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MININT-FNANLI5.fareast.corp.microsoft.com/*************:55199
2015-10-17 22:26:43,641 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 22:26:43,656 INFO [IPC Server listener on 55199] org.apache.hadoop.ipc.Server: IPC Server listener on 55199: starting
2015-10-17 22:26:43,734 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 22:26:43,734 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 22:26:43,750 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 22:26:43,781 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 22:26:43,781 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 22:26:43,781 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 22:26:43,781 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 22:26:43,797 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 55213
2015-10-17 22:26:43,797 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 22:26:43,906 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_55213_mapreduce____.g87ojf\webapp
2015-10-17 22:26:44,485 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:55213
2015-10-17 22:26:44,485 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 55213
2015-10-17 22:26:45,219 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 22:26:45,219 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 22:26:45,250 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445087491445_0006
2015-10-17 22:26:45,250 INFO [IPC Server listener on 55219] org.apache.hadoop.ipc.Server: IPC Server listener on 55219: starting
2015-10-17 22:26:45,250 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 22:26:45,250 INFO [Socket Reader #1 for port 55219] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 55219
2015-10-17 22:26:45,266 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 22:26:45,266 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 22:26:45,266 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 22:26:45,563 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 22:26:45,875 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 22:26:45,875 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 22:26:45,875 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 22:26:45,875 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 22:26:46,110 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0006Job Transitioned from INITED to SETUP
2015-10-17 22:26:46,235 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 22:26:46,344 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0006Job Transitioned from SETUP to RUNNING
2015-10-17 22:26:46,391 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,391 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445087491445_0006, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0006/job_1445087491445_0006_1.jhist
2015-10-17 22:26:46,391 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,406 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:46,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,469 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:46,531 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:46,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:46,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:46,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:46,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:46,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:46,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:46,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:46,547 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 22:26:46,563 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 22:26:47,172 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 22:26:47,250 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0006: ask=6 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:19456, vCores:-31> knownNMs=7
2015-10-17 22:26:47,313 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:19456, vCores:-31>
2015-10-17 22:26:47,313 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:48,328 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:19456, vCores:-31>
2015-10-17 22:26:48,328 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:49,329 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:19456, vCores:-31>
2015-10-17 22:26:49,329 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:50,344 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:18432, vCores:-32>
2015-10-17 22:26:50,344 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:51,375 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:17408, vCores:-33>
2015-10-17 22:26:51,375 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:52,391 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:26:52,391 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:53,407 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:26:53,407 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:54,469 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:26:54,469 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:55,485 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:26:55,485 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:56,516 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:26:56,516 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:57,563 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:26:57,563 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:58,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:26:58,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:59,798 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:26:59,798 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:00,938 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:00,938 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:02,063 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:02,063 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:03,189 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:03,189 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:04,267 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:04,267 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:05,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:05,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:06,532 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:06,532 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:07,642 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:07,642 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:08,798 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:08,798 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:09,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:09,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:11,142 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:11,142 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:12,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:12,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:13,330 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:13,330 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:14,345 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:14,345 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:15,361 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:15,361 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:16,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:16,408 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:17,486 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:17,486 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:18,549 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:18,549 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:19,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:19,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:20,705 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:20,705 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:21,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:21,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:22,908 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:22,908 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:23,971 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:23,971 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:25,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:25,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:26,174 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:26,174 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:27,237 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:27,237 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:28,299 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:28,299 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:29,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:29,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:30,455 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:30,455 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:31,549 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:31,549 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:32,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:32,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:33,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:33,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:34,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:34,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:35,846 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:35,846 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:36,909 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:36,909 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:37,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:37,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:39,018 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:39,018 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:40,050 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:40,050 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:41,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:41,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:42,112 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:42,112 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:43,159 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:43,159 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:44,175 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:44,175 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:45,206 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:45,206 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:46,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:46,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:47,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:47,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:48,300 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:48,300 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:49,347 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:49,347 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:50,394 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:50,394 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:51,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:51,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:52,503 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:52,503 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:53,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:53,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:54,707 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:54,707 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:55,754 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:55,754 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:56,800 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:56,800 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:57,879 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:57,879 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:27:59,004 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:27:59,004 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:00,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:00,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:01,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:01,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:02,144 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:02,144 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:03,223 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:03,223 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:04,285 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:04,285 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:05,363 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:05,363 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:06,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:06,442 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:07,473 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:07,473 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:08,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:28:08,551 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0006_01_000002 to attempt_1445087491445_0006_m_000001_0
2015-10-17 22:28:08,551 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:08,551 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:08,551 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:1 RackLocal:0
2015-10-17 22:28:08,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:28:09,004 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0006/job.jar
2015-10-17 22:28:09,082 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0006/job.xml
2015-10-17 22:28:09,082 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 22:28:09,082 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 22:28:09,082 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 22:28:09,192 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:28:09,285 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0006_01_000002 taskAttempt attempt_1445087491445_0006_m_000001_0
2015-10-17 22:28:09,285 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0006_m_000001_0
2015-10-17 22:28:09,301 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 22:28:09,707 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0006_m_000001_0 : 13562
2015-10-17 22:28:09,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0006_m_000001_0] using containerId: [container_1445087491445_0006_01_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 22:28:09,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:28:09,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0006_m_000001
2015-10-17 22:28:09,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:28:09,785 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0006: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:16384, vCores:-34> knownNMs=7
2015-10-17 22:28:09,785 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:28:09,785 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0006_01_000003 to attempt_1445087491445_0006_m_000000_0
2015-10-17 22:28:09,785 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:09,785 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:09,785 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:2 RackLocal:0
2015-10-17 22:28:09,785 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:28:09,785 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:28:09,879 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0006_01_000003 taskAttempt attempt_1445087491445_0006_m_000000_0
2015-10-17 22:28:09,879 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0006_m_000000_0
2015-10-17 22:28:09,879 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:28:10,270 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0006_m_000000_0 : 13562
2015-10-17 22:28:10,270 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0006_m_000000_0] using containerId: [container_1445087491445_0006_01_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:28:10,270 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:28:10,270 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0006_m_000000
2015-10-17 22:28:10,270 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:28:10,848 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0006: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-34> knownNMs=7
2015-10-17 22:28:10,848 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:10,848 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:11,895 INFO [Socket Reader #1 for port 55219] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0006 (auth:SIMPLE)
2015-10-17 22:28:11,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:11,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:12,223 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0006_m_000002 asked for a task
2015-10-17 22:28:12,223 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0006_m_000002 given task: attempt_1445087491445_0006_m_000001_0
2015-10-17 22:28:12,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:28:12,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0006_01_000004 to attempt_1445087491445_0006_m_000002_0
2015-10-17 22:28:12,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:12,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:12,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:3 RackLocal:0
2015-10-17 22:28:12,942 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:28:12,942 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:28:13,051 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0006_01_000004 taskAttempt attempt_1445087491445_0006_m_000002_0
2015-10-17 22:28:13,051 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0006_m_000002_0
2015-10-17 22:28:13,051 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:28:13,145 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0006_m_000002_0 : 13562
2015-10-17 22:28:13,145 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0006_m_000002_0] using containerId: [container_1445087491445_0006_01_000004 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:28:13,145 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:28:13,145 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0006_m_000002
2015-10-17 22:28:13,145 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:28:13,973 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0006: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-34> knownNMs=7
2015-10-17 22:28:13,973 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:13,973 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:15,004 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:15,004 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:16,036 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:28:16,036 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0006_01_000005 to attempt_1445087491445_0006_m_000003_0
2015-10-17 22:28:16,036 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:16,036 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:16,036 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:4 RackLocal:0
2015-10-17 22:28:16,036 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:28:16,036 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:28:16,130 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0006_01_000005 taskAttempt attempt_1445087491445_0006_m_000003_0
2015-10-17 22:28:16,130 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0006_m_000003_0
2015-10-17 22:28:16,130 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:28:16,286 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0006_m_000003_0 : 13562
2015-10-17 22:28:16,286 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0006_m_000003_0] using containerId: [container_1445087491445_0006_01_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:28:16,286 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:28:16,286 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0006_m_000003
2015-10-17 22:28:16,286 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:28:17,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0006: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-34> knownNMs=7
2015-10-17 22:28:17,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:17,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:18,036 INFO [Socket Reader #1 for port 55219] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0006 (auth:SIMPLE)
2015-10-17 22:28:18,067 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0006_m_000003 asked for a task
2015-10-17 22:28:18,067 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0006_m_000003 given task: attempt_1445087491445_0006_m_000000_0
2015-10-17 22:28:18,098 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:28:18,098 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0006_01_000006 to attempt_1445087491445_0006_m_000004_0
2015-10-17 22:28:18,098 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:18,098 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:18,098 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:5 RackLocal:0
2015-10-17 22:28:18,098 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:28:18,098 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:28:18,208 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0006_01_000006 taskAttempt attempt_1445087491445_0006_m_000004_0
2015-10-17 22:28:18,208 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0006_m_000004_0
2015-10-17 22:28:18,208 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:28:18,348 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0006_m_000004_0 : 13562
2015-10-17 22:28:18,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0006_m_000004_0] using containerId: [container_1445087491445_0006_01_000006 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:28:18,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:28:18,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0006_m_000004
2015-10-17 22:28:18,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:28:19,020 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.13102192
2015-10-17 22:28:19,067 INFO [Socket Reader #1 for port 55219] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0006 (auth:SIMPLE)
2015-10-17 22:28:19,098 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0006_m_000004 asked for a task
2015-10-17 22:28:19,098 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0006_m_000004 given task: attempt_1445087491445_0006_m_000002_0
2015-10-17 22:28:19,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0006: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:16384, vCores:-34> knownNMs=7
2015-10-17 22:28:19,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 22:28:19,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0006_01_000007 to attempt_1445087491445_0006_m_000005_0
2015-10-17 22:28:19,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0006_01_000008 to attempt_1445087491445_0006_m_000006_0
2015-10-17 22:28:19,145 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:28:19,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:19,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:19,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:7 RackLocal:0
2015-10-17 22:28:19,145 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:28:19,145 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:28:19,145 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:28:19,270 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0006_01_000007 taskAttempt attempt_1445087491445_0006_m_000005_0
2015-10-17 22:28:19,270 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0006_m_000005_0
2015-10-17 22:28:19,270 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:28:19,380 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0006_01_000008 taskAttempt attempt_1445087491445_0006_m_000006_0
2015-10-17 22:28:19,380 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0006_m_000006_0
2015-10-17 22:28:19,380 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:28:19,380 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0006_m_000005_0 : 13562
2015-10-17 22:28:19,380 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0006_m_000005_0] using containerId: [container_1445087491445_0006_01_000007 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:28:19,380 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:28:19,380 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0006_m_000005
2015-10-17 22:28:19,380 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:28:19,520 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0006_m_000006_0 : 13562
2015-10-17 22:28:19,520 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0006_m_000006_0] using containerId: [container_1445087491445_0006_01_000008 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:28:19,520 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:28:19,520 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0006_m_000006
2015-10-17 22:28:19,520 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:28:19,786 INFO [Socket Reader #1 for port 55219] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0006 (auth:SIMPLE)
2015-10-17 22:28:19,817 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0006_m_000005 asked for a task
2015-10-17 22:28:19,817 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0006_m_000005 given task: attempt_1445087491445_0006_m_000003_0
2015-10-17 22:28:20,177 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0006: ask=5 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:16384, vCores:-34> knownNMs=7
2015-10-17 22:28:20,177 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:28:20,177 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0006_01_000009 to attempt_1445087491445_0006_m_000007_0
2015-10-17 22:28:20,177 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:20,177 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:20,177 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:8 RackLocal:0
2015-10-17 22:28:20,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:28:20,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:28:20,270 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0006_01_000009 taskAttempt attempt_1445087491445_0006_m_000007_0
2015-10-17 22:28:20,270 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0006_m_000007_0
2015-10-17 22:28:20,270 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:28:20,395 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0006_m_000007_0 : 13562
2015-10-17 22:28:20,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0006_m_000007_0] using containerId: [container_1445087491445_0006_01_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:28:20,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:28:20,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0006_m_000007
2015-10-17 22:28:20,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:28:21,239 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0006: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-34> knownNMs=7
2015-10-17 22:28:21,239 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:21,239 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:21,802 INFO [Socket Reader #1 for port 55219] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0006 (auth:SIMPLE)
2015-10-17 22:28:21,880 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0006_m_000006 asked for a task
2015-10-17 22:28:21,880 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0006_m_000006 given task: attempt_1445087491445_0006_m_000004_0
2015-10-17 22:28:22,083 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.13102192
2015-10-17 22:28:22,317 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:28:22,317 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0006_01_000010 to attempt_1445087491445_0006_m_000008_0
2015-10-17 22:28:22,317 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:22,317 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:22,317 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:9 RackLocal:0
2015-10-17 22:28:22,317 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:28:22,317 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:28:22,395 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0006_01_000010 taskAttempt attempt_1445087491445_0006_m_000008_0
2015-10-17 22:28:22,395 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0006_m_000008_0
2015-10-17 22:28:22,395 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:28:22,708 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0006_m_000008_0 : 13562
2015-10-17 22:28:22,708 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0006_m_000008_0] using containerId: [container_1445087491445_0006_01_000010 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:28:22,708 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:28:22,708 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0006_m_000008
2015-10-17 22:28:22,708 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:28:23,333 INFO [Socket Reader #1 for port 55219] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0006 (auth:SIMPLE)
2015-10-17 22:28:23,411 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0006: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-34> knownNMs=7
2015-10-17 22:28:23,411 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:23,411 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:23,427 INFO [IPC Server handler 11 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0006_m_000008 asked for a task
2015-10-17 22:28:23,427 INFO [IPC Server handler 11 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0006_m_000008 given task: attempt_1445087491445_0006_m_000006_0
2015-10-17 22:28:23,552 INFO [Socket Reader #1 for port 55219] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0006 (auth:SIMPLE)
2015-10-17 22:28:23,646 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0006_m_000007 asked for a task
2015-10-17 22:28:23,646 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0006_m_000007 given task: attempt_1445087491445_0006_m_000005_0
2015-10-17 22:28:24,489 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:24,489 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:24,661 INFO [Socket Reader #1 for port 55219] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0006 (auth:SIMPLE)
2015-10-17 22:28:24,755 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0006_m_000009 asked for a task
2015-10-17 22:28:24,755 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0006_m_000009 given task: attempt_1445087491445_0006_m_000007_0
2015-10-17 22:28:25,099 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.13417642
2015-10-17 22:28:25,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:25,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:26,271 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.13101934
2015-10-17 22:28:26,583 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:26,583 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:26,896 INFO [Socket Reader #1 for port 55219] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0006 (auth:SIMPLE)
2015-10-17 22:28:26,974 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0006_m_000010 asked for a task
2015-10-17 22:28:26,974 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0006_m_000010 given task: attempt_1445087491445_0006_m_000008_0
2015-10-17 22:28:27,505 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.131014
2015-10-17 22:28:27,630 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:27,630 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:27,958 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.13102706
2015-10-17 22:28:28,130 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.23921879
2015-10-17 22:28:28,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:28,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:29,318 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.13101934
2015-10-17 22:28:29,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:29,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:30,583 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.131014
2015-10-17 22:28:30,677 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.13104042
2015-10-17 22:28:30,943 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:30,943 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:31,052 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.13102706
2015-10-17 22:28:31,208 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.23921879
2015-10-17 22:28:31,880 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.13101135
2015-10-17 22:28:31,990 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:31,990 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:32,130 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.13104132
2015-10-17 22:28:32,349 INFO [IPC Server handler 11 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.13101934
2015-10-17 22:28:32,974 INFO [IPC Server handler 13 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.13103712
2015-10-17 22:28:33,021 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:33,021 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:33,662 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.131014
2015-10-17 22:28:33,771 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.13104042
2015-10-17 22:28:34,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:28:34,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0006_01_000011 to attempt_1445087491445_0006_m_000009_0
2015-10-17 22:28:34,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:34,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:28:34,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:28:34,115 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:28:34,115 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:28:34,146 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.13102706
2015-10-17 22:28:34,240 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0006_01_000011 taskAttempt attempt_1445087491445_0006_m_000009_0
2015-10-17 22:28:34,240 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0006_m_000009_0
2015-10-17 22:28:34,240 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 22:28:34,271 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.23921879
2015-10-17 22:28:34,646 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0006_m_000009_0 : 13562
2015-10-17 22:28:34,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0006_m_000009_0] using containerId: [container_1445087491445_0006_01_000011 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 22:28:34,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:28:34,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0006_m_000009
2015-10-17 22:28:34,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:28:34,974 INFO [IPC Server handler 13 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.13101135
2015-10-17 22:28:35,146 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.13104132
2015-10-17 22:28:35,224 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0006: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-34> knownNMs=7
2015-10-17 22:28:35,380 INFO [IPC Server handler 11 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.13102318
2015-10-17 22:28:35,412 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.19402665
2015-10-17 22:28:36,037 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.13103712
2015-10-17 22:28:36,771 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.23919508
2015-10-17 22:28:36,912 INFO [IPC Server handler 13 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.13222642
2015-10-17 22:28:37,318 INFO [IPC Server handler 11 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.23922287
2015-10-17 22:28:37,381 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.2757474
2015-10-17 22:28:38,115 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.19390643
2015-10-17 22:28:38,287 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.21429679
2015-10-17 22:28:38,490 INFO [Socket Reader #1 for port 55219] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0006 (auth:SIMPLE)
2015-10-17 22:28:38,506 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.13102318
2015-10-17 22:28:38,521 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.23921585
2015-10-17 22:28:38,599 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0006_m_000011 asked for a task
2015-10-17 22:28:38,599 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0006_m_000011 given task: attempt_1445087491445_0006_m_000009_0
2015-10-17 22:28:39,193 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.23924637
2015-10-17 22:28:39,818 INFO [IPC Server handler 13 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.23919508
2015-10-17 22:28:39,990 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.23924798
2015-10-17 22:28:40,365 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.23922287
2015-10-17 22:28:40,412 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.34743196
2015-10-17 22:28:41,193 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.23923388
2015-10-17 22:28:41,334 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.23922269
2015-10-17 22:28:41,568 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.23921506
2015-10-17 22:28:41,568 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.23921585
2015-10-17 22:28:42,256 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.23924637
2015-10-17 22:28:42,896 INFO [IPC Server handler 13 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.23919508
2015-10-17 22:28:43,037 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.23924798
2015-10-17 22:28:43,428 INFO [IPC Server handler 11 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.23922287
2015-10-17 22:28:43,506 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.34743196
2015-10-17 22:28:44,287 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.23923388
2015-10-17 22:28:44,396 INFO [IPC Server handler 11 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.23922269
2015-10-17 22:28:44,600 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.23921506
2015-10-17 22:28:44,600 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.3189278
2015-10-17 22:28:45,318 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.23924637
2015-10-17 22:28:45,912 INFO [IPC Server handler 13 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.3474061
2015-10-17 22:28:46,068 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.2408859
2015-10-17 22:28:46,178 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.16604526
2015-10-17 22:28:46,475 INFO [IPC Server handler 11 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.3473985
2015-10-17 22:28:46,522 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.34743196
2015-10-17 22:28:47,319 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.3416092
2015-10-17 22:28:47,444 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.3335664
2015-10-17 22:28:47,615 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.23921506
2015-10-17 22:28:47,615 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.3474062
2015-10-17 22:28:48,365 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.34743145
2015-10-17 22:28:48,944 INFO [IPC Server handler 13 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.3474061
2015-10-17 22:28:49,162 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.34742972
2015-10-17 22:28:49,178 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.16604526
2015-10-17 22:28:49,506 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.3473985
2015-10-17 22:28:49,569 INFO [IPC Server handler 11 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.34743196
2015-10-17 22:28:50,428 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.34743717
2015-10-17 22:28:50,537 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.3474054
2015-10-17 22:28:50,725 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.3474145
2015-10-17 22:28:50,725 INFO [IPC Server handler 13 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.3474062
2015-10-17 22:28:51,397 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.34743145
2015-10-17 22:28:52,006 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.3474061
2015-10-17 22:28:52,225 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.34742972
2015-10-17 22:28:52,241 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.16604526
2015-10-17 22:28:52,584 INFO [IPC Server handler 11 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.3473985
2015-10-17 22:28:52,663 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.44368923
2015-10-17 22:28:53,538 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.34743717
2015-10-17 22:28:53,631 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.3474054
2015-10-17 22:28:53,819 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.3474145
2015-10-17 22:28:53,819 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.44844714
2015-10-17 22:28:54,428 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.34743145
2015-10-17 22:28:55,116 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.45561612
2015-10-17 22:28:55,335 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.39145374
2015-10-17 22:28:55,335 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.24667773
2015-10-17 22:28:55,710 INFO [IPC Server handler 11 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.45559394
2015-10-17 22:28:55,756 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.455629
2015-10-17 22:28:56,616 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.455643
2015-10-17 22:28:56,678 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.4552903
2015-10-17 22:28:56,897 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.45563135
2015-10-17 22:28:56,897 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.4354415
2015-10-17 22:28:57,522 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.4556257
2015-10-17 22:28:58,178 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.45561612
2015-10-17 22:28:58,444 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.3031575
2015-10-17 22:28:58,444 INFO [IPC Server handler 19 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.45565325
2015-10-17 22:28:58,772 INFO [IPC Server handler 11 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.45559394
2015-10-17 22:28:58,850 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.455629
2015-10-17 22:28:59,678 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.455643
2015-10-17 22:28:59,757 INFO [IPC Server handler 11 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.45560944
2015-10-17 22:28:59,944 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.45563135
2015-10-17 22:28:59,944 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.45562187
2015-10-17 22:29:00,569 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.4556257
2015-10-17 22:29:01,225 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.45561612
2015-10-17 22:29:01,460 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.3031575
2015-10-17 22:29:01,460 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.45565325
2015-10-17 22:29:01,850 INFO [IPC Server handler 21 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.45559394
2015-10-17 22:29:01,866 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.455629
2015-10-17 22:29:02,694 INFO [IPC Server handler 18 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.455643
2015-10-17 22:29:02,788 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.45560944
2015-10-17 22:29:03,007 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.45562187
2015-10-17 22:29:03,007 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.5544425
2015-10-17 22:29:03,601 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.45667723
2015-10-17 22:29:04,241 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.56381613
2015-10-17 22:29:04,476 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.3031575
2015-10-17 22:29:04,491 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.52676946
2015-10-17 22:29:04,866 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.5637838
2015-10-17 22:29:04,882 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.455629
2015-10-17 22:29:05,726 INFO [IPC Server handler 18 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.5638263
2015-10-17 22:29:05,804 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.56381226
2015-10-17 22:29:06,023 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.56380075
2015-10-17 22:29:06,038 INFO [IPC Server handler 13 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.54923975
2015-10-17 22:29:06,632 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.56380385
2015-10-17 22:29:07,288 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.56381613
2015-10-17 22:29:07,491 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.32642305
2015-10-17 22:29:07,523 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.5638328
2015-10-17 22:29:07,882 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.5638294
2015-10-17 22:29:07,944 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.5637838
2015-10-17 22:29:08,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:17408, vCores:-26>
2015-10-17 22:29:08,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:29:08,741 INFO [IPC Server handler 18 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.5638263
2015-10-17 22:29:08,866 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.56381226
2015-10-17 22:29:09,085 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.56380075
2015-10-17 22:29:09,116 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.56384325
2015-10-17 22:29:09,320 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:15360, vCores:-27>
2015-10-17 22:29:09,320 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:29:09,695 INFO [IPC Server handler 19 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.56380385
2015-10-17 22:29:10,382 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.56381613
2015-10-17 22:29:10,554 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.4402952
2015-10-17 22:29:10,570 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.5638328
2015-10-17 22:29:10,929 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.5638294
2015-10-17 22:29:10,992 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.5637838
2015-10-17 22:29:11,804 INFO [IPC Server handler 18 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.5638263
2015-10-17 22:29:11,929 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.56381226
2015-10-17 22:29:12,023 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.56380075
2015-10-17 22:29:12,148 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.667
2015-10-17 22:29:12,179 INFO [IPC Server handler 13 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.56384325
2015-10-17 22:29:12,429 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.5637838
2015-10-17 22:29:12,788 INFO [IPC Server handler 19 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.6227535
2015-10-17 22:29:12,992 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.56381613
2015-10-17 22:29:13,304 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.6227535
2015-10-17 22:29:13,414 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.667
2015-10-17 22:29:13,601 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.4402952
2015-10-17 22:29:13,601 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.657805
2015-10-17 22:29:13,664 INFO [IPC Server handler 19 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.5638263
2015-10-17 22:29:13,789 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.657805
2015-10-17 22:29:13,929 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.5638294
2015-10-17 22:29:14,039 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.667
2015-10-17 22:29:14,835 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.667
2015-10-17 22:29:14,976 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.63475233
2015-10-17 22:29:15,210 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.667
2015-10-17 22:29:15,210 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.56384325
2015-10-17 22:29:15,757 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.63475233
2015-10-17 22:29:15,820 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.667
2015-10-17 22:29:16,445 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.667
2015-10-17 22:29:16,601 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.4402952
2015-10-17 22:29:16,632 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.667
2015-10-17 22:29:16,929 INFO [IPC Server handler 18 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.62010074
2015-10-17 22:29:17,086 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.667
2015-10-17 22:29:17,882 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.667
2015-10-17 22:29:17,914 INFO [IPC Server handler 18 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.62010074
2015-10-17 22:29:18,023 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.667
2015-10-17 22:29:18,132 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.56384325
2015-10-17 22:29:18,226 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.667
2015-10-17 22:29:18,242 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.667
2015-10-17 22:29:18,836 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.667
2015-10-17 22:29:19,461 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.667
2015-10-17 22:29:19,617 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.54844844
2015-10-17 22:29:19,648 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.667
2015-10-17 22:29:19,945 INFO [IPC Server handler 18 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.667
2015-10-17 22:29:20,179 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.667
2015-10-17 22:29:20,929 INFO [IPC Server handler 18 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.667
2015-10-17 22:29:21,054 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.667
2015-10-17 22:29:21,289 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.667
2015-10-17 22:29:21,289 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.66717815
2015-10-17 22:29:21,867 INFO [IPC Server handler 19 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.667
2015-10-17 22:29:22,492 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.667
2015-10-17 22:29:22,648 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.5773621
2015-10-17 22:29:22,711 INFO [IPC Server handler 19 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.667
2015-10-17 22:29:22,992 INFO [IPC Server handler 18 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.667
2015-10-17 22:29:23,226 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.69119686
2015-10-17 22:29:23,992 INFO [IPC Server handler 18 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.6868141
2015-10-17 22:29:24,164 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.667
2015-10-17 22:29:24,383 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.667
2015-10-17 22:29:24,383 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.70905095
2015-10-17 22:29:24,898 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.705948
2015-10-17 22:29:25,523 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.70202893
2015-10-17 22:29:25,664 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.5773621
2015-10-17 22:29:25,742 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.70591927
2015-10-17 22:29:26,055 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.667
2015-10-17 22:29:26,367 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.73536325
2015-10-17 22:29:27,039 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.7296028
2015-10-17 22:29:27,211 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.6929806
2015-10-17 22:29:27,430 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.6757626
2015-10-17 22:29:27,492 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.7543603
2015-10-17 22:29:27,930 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.7486199
2015-10-17 22:29:28,602 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.7458978
2015-10-17 22:29:28,664 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.58934253
2015-10-17 22:29:28,758 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.74725246
2015-10-17 22:29:29,071 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.667
2015-10-17 22:29:29,446 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.77944857
2015-10-17 22:29:29,446 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.58934253
2015-10-17 22:29:30,086 INFO [IPC Server handler 18 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.7724644
2015-10-17 22:29:30,227 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.73594785
2015-10-17 22:29:30,477 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.7183086
2015-10-17 22:29:30,555 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.79863995
2015-10-17 22:29:30,977 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.79172134
2015-10-17 22:29:31,618 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.79083806
2015-10-17 22:29:31,696 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.667
2015-10-17 22:29:31,774 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.78873765
2015-10-17 22:29:32,071 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.670927
2015-10-17 22:29:32,461 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.8235718
2015-10-17 22:29:33,102 INFO [IPC Server handler 18 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.8150363
2015-10-17 22:29:33,258 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.77889305
2015-10-17 22:29:33,508 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.7608768
2015-10-17 22:29:33,586 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.8426975
2015-10-17 22:29:34,008 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.8349139
2015-10-17 22:29:34,680 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.8333318
2015-10-17 22:29:34,696 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.667
2015-10-17 22:29:34,805 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.83014905
2015-10-17 22:29:35,071 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.7262417
2015-10-17 22:29:35,477 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.8666164
2015-10-17 22:29:36,133 INFO [IPC Server handler 19 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.8571434
2015-10-17 22:29:36,274 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.82182837
2015-10-17 22:29:36,524 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.80338115
2015-10-17 22:29:36,602 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.8861301
2015-10-17 22:29:37,024 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.8775661
2015-10-17 22:29:37,696 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.6787971
2015-10-17 22:29:37,696 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.87762654
2015-10-17 22:29:37,837 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.8713921
2015-10-17 22:29:38,087 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.7724929
2015-10-17 22:29:38,493 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.90958476
2015-10-17 22:29:39,212 INFO [IPC Server handler 19 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.89918596
2015-10-17 22:29:39,337 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.8637924
2015-10-17 22:29:39,618 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.84550613
2015-10-17 22:29:39,665 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.9293835
2015-10-17 22:29:40,087 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.92010224
2015-10-17 22:29:40,759 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.72824913
2015-10-17 22:29:40,790 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.9173723
2015-10-17 22:29:40,915 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.9075222
2015-10-17 22:29:41,118 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.8077528
2015-10-17 22:29:41,618 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.9416506
2015-10-17 22:29:42,274 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.9263202
2015-10-17 22:29:42,399 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.8897698
2015-10-17 22:29:42,665 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.8703815
2015-10-17 22:29:42,743 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.95418584
2015-10-17 22:29:43,134 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.94235253
2015-10-17 22:29:43,774 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.76314384
2015-10-17 22:29:43,806 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.9421468
2015-10-17 22:29:43,946 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.9310911
2015-10-17 22:29:44,165 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.8573567
2015-10-17 22:29:44,634 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.97139823
2015-10-17 22:29:45,306 INFO [IPC Server handler 19 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.95992684
2015-10-17 22:29:45,431 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.9246249
2015-10-17 22:29:45,696 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.9069311
2015-10-17 22:29:45,774 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 0.9915848
2015-10-17 22:29:46,165 INFO [IPC Server handler 25 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.9809897
2015-10-17 22:29:46,478 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000000_0 is : 1.0
2015-10-17 22:29:46,493 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0006_m_000000_0
2015-10-17 22:29:46,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:29:46,493 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0006_01_000003 taskAttempt attempt_1445087491445_0006_m_000000_0
2015-10-17 22:29:46,493 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0006_m_000000_0
2015-10-17 22:29:46,493 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:29:46,806 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.80969405
2015-10-17 22:29:46,837 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.97897536
2015-10-17 22:29:46,978 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.9639814
2015-10-17 22:29:47,212 INFO [IPC Server handler 25 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.90702766
2015-10-17 22:29:47,587 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:29:47,603 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0006_m_000000_0
2015-10-17 22:29:47,603 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:29:47,603 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 22:29:47,759 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 0.9974556
2015-10-17 22:29:47,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:47,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:15360, vCores:-27>
2015-10-17 22:29:47,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 22:29:47,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-17 22:29:47,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:48,353 INFO [IPC Server handler 19 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.9795548
2015-10-17 22:29:48,353 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000003_0 is : 1.0
2015-10-17 22:29:48,353 INFO [IPC Server handler 18 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0006_m_000003_0
2015-10-17 22:29:48,353 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:29:48,368 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0006_01_000005 taskAttempt attempt_1445087491445_0006_m_000003_0
2015-10-17 22:29:48,368 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0006_m_000003_0
2015-10-17 22:29:48,368 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:29:48,478 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.94322014
2015-10-17 22:29:48,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:29:48,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0006_m_000003_0
2015-10-17 22:29:48,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:29:48,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 22:29:48,743 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.92345417
2015-10-17 22:29:49,025 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:49,040 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0006: ask=1 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:14336, vCores:-27> knownNMs=6
2015-10-17 22:29:49,040 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0006_01_000003
2015-10-17 22:29:49,040 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:49,040 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0006_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:29:49,212 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 0.9936643
2015-10-17 22:29:49,806 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.8693063
2015-10-17 22:29:49,900 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 0.99287283
2015-10-17 22:29:50,072 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.9773834
2015-10-17 22:29:50,087 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0006_01_000005
2015-10-17 22:29:50,087 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:50,087 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0006_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:29:50,259 INFO [IPC Server handler 25 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.9499644
2015-10-17 22:29:50,368 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000007_0 is : 1.0
2015-10-17 22:29:50,447 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0006_m_000007_0
2015-10-17 22:29:50,447 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:29:50,447 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0006_01_000009 taskAttempt attempt_1445087491445_0006_m_000007_0
2015-10-17 22:29:50,447 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0006_m_000007_0
2015-10-17 22:29:50,447 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:29:50,837 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000002_0 is : 1.0
2015-10-17 22:29:50,884 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0006_m_000002_0
2015-10-17 22:29:50,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:29:50,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:29:50,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0006_m_000007_0
2015-10-17 22:29:50,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:29:50,884 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 22:29:50,900 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0006_01_000004 taskAttempt attempt_1445087491445_0006_m_000002_0
2015-10-17 22:29:50,900 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0006_m_000002_0
2015-10-17 22:29:50,900 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:29:51,181 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:51,212 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:29:51,212 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 22:29:51,212 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0006_01_000012 to attempt_1445087491445_0006_r_000000_0
2015-10-17 22:29:51,212 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:51,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:29:51,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:29:51,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:29:51,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0006_m_000002_0
2015-10-17 22:29:51,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:29:51,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 22:29:51,259 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0006_01_000012 taskAttempt attempt_1445087491445_0006_r_000000_0
2015-10-17 22:29:51,259 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0006_r_000000_0
2015-10-17 22:29:51,259 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 22:29:51,478 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 0.99740165
2015-10-17 22:29:51,525 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0006_r_000000_0 : 13562
2015-10-17 22:29:51,525 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0006_r_000000_0] using containerId: [container_1445087491445_0006_01_000012 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 22:29:51,525 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:29:51,525 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0006_r_000000
2015-10-17 22:29:51,525 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:29:51,572 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.96383727
2015-10-17 22:29:51,806 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.94493926
2015-10-17 22:29:52,025 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000006_0 is : 1.0
2015-10-17 22:29:52,056 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0006_m_000006_0
2015-10-17 22:29:52,056 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:29:52,056 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0006_01_000008 taskAttempt attempt_1445087491445_0006_m_000006_0
2015-10-17 22:29:52,056 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0006_m_000006_0
2015-10-17 22:29:52,056 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:29:52,368 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:52,384 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0006: ask=1 release= 0 newContainers=0 finishedContainers=2 resourcelimit=<memory:16384, vCores:-26> knownNMs=6
2015-10-17 22:29:52,384 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0006_01_000009
2015-10-17 22:29:52,384 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0006_01_000004
2015-10-17 22:29:52,384 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:52,384 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:29:52,384 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0006_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:29:52,384 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0006_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:29:52,384 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0006_m_000006_0
2015-10-17 22:29:52,384 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:29:52,384 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 22:29:52,853 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.9309207
2015-10-17 22:29:53,150 INFO [IPC Server handler 25 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 0.996239
2015-10-17 22:29:53,290 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 0.9977433
2015-10-17 22:29:53,431 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:53,462 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0006_01_000008
2015-10-17 22:29:53,462 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:53,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0006_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:29:53,712 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000004_0 is : 1.0
2015-10-17 22:29:53,712 INFO [IPC Server handler 4 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0006_m_000004_0
2015-10-17 22:29:53,712 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:29:53,712 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0006_01_000006 taskAttempt attempt_1445087491445_0006_m_000004_0
2015-10-17 22:29:53,712 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0006_m_000004_0
2015-10-17 22:29:53,712 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:29:53,994 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:29:53,994 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0006_m_000004_0
2015-10-17 22:29:53,994 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:29:53,994 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 22:29:54,072 INFO [Socket Reader #1 for port 55219] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0006 (auth:SIMPLE)
2015-10-17 22:29:54,103 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0006_r_000012 asked for a task
2015-10-17 22:29:54,103 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0006_r_000012 given task: attempt_1445087491445_0006_r_000000_0
2015-10-17 22:29:54,509 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:54,619 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 0.9982172
2015-10-17 22:29:54,837 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.97855484
2015-10-17 22:29:54,947 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 22:29:55,072 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000005_0 is : 1.0
2015-10-17 22:29:55,087 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0006_m_000005_0
2015-10-17 22:29:55,087 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:29:55,087 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0006_01_000007 taskAttempt attempt_1445087491445_0006_m_000005_0
2015-10-17 22:29:55,087 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0006_m_000005_0
2015-10-17 22:29:55,087 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:29:55,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:29:55,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0006_m_000005_0
2015-10-17 22:29:55,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:29:55,244 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 22:29:55,541 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:55,541 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0006_01_000006
2015-10-17 22:29:55,541 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:55,541 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0006_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:29:55,869 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 0.9740453
2015-10-17 22:29:55,962 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 22:29:56,306 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 1.0
2015-10-17 22:29:56,572 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0006_01_000007
2015-10-17 22:29:56,572 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:29:56,572 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0006_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:29:56,947 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:29:57,884 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 0.99374485
2015-10-17 22:29:57,994 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:29:58,869 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 1.0
2015-10-17 22:29:58,978 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:29:59,150 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000009_0 is : 1.0
2015-10-17 22:29:59,150 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0006_m_000009_0
2015-10-17 22:29:59,150 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:29:59,150 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0006_01_000011 taskAttempt attempt_1445087491445_0006_m_000009_0
2015-10-17 22:29:59,150 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0006_m_000009_0
2015-10-17 22:29:59,150 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 22:29:59,275 INFO [IPC Server handler 25 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000008_0 is : 1.0
2015-10-17 22:29:59,275 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:29:59,275 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0006_m_000009_0
2015-10-17 22:29:59,275 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:29:59,275 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 22:29:59,275 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0006_m_000008_0
2015-10-17 22:29:59,275 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:29:59,275 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0006_01_000010 taskAttempt attempt_1445087491445_0006_m_000008_0
2015-10-17 22:29:59,275 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0006_m_000008_0
2015-10-17 22:29:59,275 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:29:59,416 INFO [IPC Server handler 29 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 1.0
2015-10-17 22:29:59,416 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:29:59,416 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0006_m_000008_0
2015-10-17 22:29:59,416 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:29:59,416 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 22:29:59,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:30:00,025 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:30:00,681 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0006_01_000011
2015-10-17 22:30:00,681 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0006_01_000010
2015-10-17 22:30:00,681 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:30:00,681 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0006_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:30:00,681 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0006_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:30:00,900 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.06666667
2015-10-17 22:30:01,010 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:30:02,010 INFO [IPC Server handler 16 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:30:03,072 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:30:04,010 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.13333334
2015-10-17 22:30:04,307 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:30:05,432 INFO [IPC Server handler 25 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:30:06,494 INFO [IPC Server handler 25 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:30:07,010 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.16666667
2015-10-17 22:30:07,525 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:30:08,572 INFO [IPC Server handler 25 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:30:09,619 INFO [IPC Server handler 25 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:30:10,104 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.20000002
2015-10-17 22:30:10,682 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:30:11,698 INFO [IPC Server handler 25 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:30:12,526 INFO [IPC Server handler 25 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_m_000001_0 is : 1.0
2015-10-17 22:30:12,573 INFO [IPC Server handler 29 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0006_m_000001_0
2015-10-17 22:30:12,573 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:30:12,573 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0006_01_000002 taskAttempt attempt_1445087491445_0006_m_000001_0
2015-10-17 22:30:12,573 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0006_m_000001_0
2015-10-17 22:30:12,573 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 22:30:12,854 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:30:12,901 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0006_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:30:12,901 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0006_m_000001_0
2015-10-17 22:30:12,901 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0006_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:30:12,901 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 22:30:13,120 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.23333333
2015-10-17 22:30:13,557 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:30:13,885 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:30:14,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0006_01_000002
2015-10-17 22:30:14,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 22:30:14,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0006_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:30:14,932 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:15,948 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:16,151 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.23333333
2015-10-17 22:30:16,979 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:17,979 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:19,010 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:19,167 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.26666668
2015-10-17 22:30:20,042 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:21,057 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:22,073 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:22,182 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.26666668
2015-10-17 22:30:23,089 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:24,136 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:25,167 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:25,183 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.26666668
2015-10-17 22:30:26,198 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:27,230 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:28,214 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.26666668
2015-10-17 22:30:28,261 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:29,277 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:30,308 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:31,245 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.26666668
2015-10-17 22:30:31,370 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:32,402 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:33,402 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:34,261 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.26666668
2015-10-17 22:30:34,433 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:35,449 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:36,465 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:37,277 INFO [IPC Server handler 2 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.26666668
2015-10-17 22:30:37,480 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:38,511 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:39,574 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:40,293 INFO [IPC Server handler 25 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.3
2015-10-17 22:30:40,605 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:41,637 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:42,652 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:43,309 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.3
2015-10-17 22:30:43,684 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:44,762 INFO [IPC Server handler 9 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:45,778 INFO [IPC Server handler 27 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:46,340 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.3
2015-10-17 22:30:46,824 INFO [IPC Server handler 22 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:47,840 INFO [IPC Server handler 3 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:48,918 INFO [IPC Server handler 22 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:49,371 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.3
2015-10-17 22:30:49,934 INFO [IPC Server handler 22 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0006_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:30:50,028 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.3
2015-10-17 22:30:52,434 INFO [IPC Server handler 25 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.33333334
2015-10-17 22:30:55,450 INFO [IPC Server handler 10 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.33333334
2015-10-17 22:31:42,281 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.33333334
2015-10-17 22:31:47,796 INFO [IPC Server handler 22 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.67160636
2015-10-17 22:31:50,875 INFO [IPC Server handler 29 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.6751038
2015-10-17 22:31:53,953 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.67817634
2015-10-17 22:31:57,031 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.6809511
2015-10-17 22:32:00,125 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.683979
2015-10-17 22:32:03,235 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.6871833
2015-10-17 22:32:06,329 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.68992937
2015-10-17 22:32:09,407 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.6937748
2015-10-17 22:32:12,501 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.69816136
2015-10-17 22:32:15,548 INFO [IPC Server handler 13 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.7005413
2015-10-17 22:32:18,658 INFO [IPC Server handler 0 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.701434
2015-10-17 22:32:21,736 INFO [IPC Server handler 23 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.7020172
2015-10-17 22:32:24,814 INFO [IPC Server handler 25 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.70300436
2015-10-17 22:32:27,892 INFO [IPC Server handler 26 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.7041902
2015-10-17 22:32:30,971 INFO [IPC Server handler 19 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.70529884
2015-10-17 22:32:34,018 INFO [IPC Server handler 5 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.70646715
2015-10-17 22:32:37,112 INFO [IPC Server handler 22 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.70785415
2015-10-17 22:32:40,190 INFO [IPC Server handler 29 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.7094481
2015-10-17 22:32:43,268 INFO [IPC Server handler 20 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.71064603
2015-10-17 22:32:46,331 INFO [IPC Server handler 14 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.7113589
2015-10-17 22:32:49,394 INFO [IPC Server handler 8 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.7121764
2015-10-17 22:32:52,472 INFO [IPC Server handler 7 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.713155
2015-10-17 22:32:55,519 INFO [IPC Server handler 3 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.7142421
2015-10-17 22:32:58,582 INFO [IPC Server handler 27 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.71514195
2015-10-17 22:33:01,629 INFO [IPC Server handler 21 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.716214
2015-10-17 22:33:04,738 INFO [IPC Server handler 6 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.71710694
2015-10-17 22:33:07,785 INFO [IPC Server handler 13 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.7179947
2015-10-17 22:33:10,848 INFO [IPC Server handler 13 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.719243
2015-10-17 22:33:13,910 INFO [IPC Server handler 15 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.720268
2015-10-17 22:33:16,958 INFO [IPC Server handler 24 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.7212119
2015-10-17 22:33:20,036 INFO [IPC Server handler 1 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.72195596
2015-10-17 22:33:23,130 INFO [IPC Server handler 12 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.7227691
2015-10-17 22:33:26,208 INFO [IPC Server handler 28 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.72361046
2015-10-17 22:33:29,255 INFO [IPC Server handler 17 on 55219] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0006_r_000000_0 is : 0.7242458
