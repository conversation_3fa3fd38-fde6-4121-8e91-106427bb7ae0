2015-10-17 23:07:56,717 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445094324383_0003_000001
2015-10-17 23:07:57,357 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 23:07:57,357 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 3 cluster_timestamp: 1445094324383 } attemptId: 1 } keyId: -411019364)
2015-10-17 23:07:57,685 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 23:07:58,654 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 23:07:58,732 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 23:07:58,779 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 23:07:58,779 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 23:07:58,779 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 23:07:58,779 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 23:07:58,779 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 23:07:58,795 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 23:07:58,795 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 23:07:58,795 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 23:07:58,842 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 23:07:58,857 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 23:07:58,889 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 23:07:58,904 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 23:07:58,967 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 23:07:59,295 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 23:07:59,404 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 23:07:59,404 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 23:07:59,420 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445094324383_0003 to jobTokenSecretManager
2015-10-17 23:07:59,639 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445094324383_0003 because: not enabled; too many maps; too much input;
2015-10-17 23:07:59,654 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445094324383_0003 = 1313861632. Number of splits = 10
2015-10-17 23:07:59,654 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445094324383_0003 = 1
2015-10-17 23:07:59,654 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445094324383_0003Job Transitioned from NEW to INITED
2015-10-17 23:07:59,654 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445094324383_0003.
2015-10-17 23:07:59,701 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 23:07:59,717 INFO [Socket Reader #1 for port 58612] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 58612
2015-10-17 23:07:59,748 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 23:07:59,748 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 23:07:59,748 INFO [IPC Server listener on 58612] org.apache.hadoop.ipc.Server: IPC Server listener on 58612: starting
2015-10-17 23:07:59,748 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at 04DN8IQ.fareast.corp.microsoft.com/***********:58612
2015-10-17 23:07:59,857 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 23:07:59,857 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 23:07:59,873 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 23:07:59,873 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 23:07:59,873 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 23:07:59,889 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 23:07:59,889 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 23:07:59,904 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 58619
2015-10-17 23:07:59,904 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 23:07:59,951 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_58619_mapreduce____.x0un5u\webapp
2015-10-17 23:08:00,170 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:58619
2015-10-17 23:08:00,170 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 58619
2015-10-17 23:08:00,623 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 23:08:00,623 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445094324383_0003
2015-10-17 23:08:00,623 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 23:08:00,639 INFO [Socket Reader #1 for port 58622] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 58622
2015-10-17 23:08:00,639 INFO [IPC Server listener on 58622] org.apache.hadoop.ipc.Server: IPC Server listener on 58622: starting
2015-10-17 23:08:00,639 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 23:08:00,670 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 23:08:00,670 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 23:08:00,670 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 23:08:00,717 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 23:08:00,826 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 23:08:00,826 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 23:08:00,842 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 23:08:00,842 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 23:08:00,857 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445094324383_0003Job Transitioned from INITED to SETUP
2015-10-17 23:08:00,857 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 23:08:00,873 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445094324383_0003Job Transitioned from SETUP to RUNNING
2015-10-17 23:08:00,889 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,904 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:00,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:00,920 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 23:08:00,935 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 23:08:00,951 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445094324383_0003, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445094324383_0003/job_1445094324383_0003_1.jhist
2015-10-17 23:08:01,826 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 23:08:01,873 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=6 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:15360, vCores:-12> knownNMs=4
2015-10-17 23:08:01,889 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:15360, vCores:-12>
2015-10-17 23:08:01,889 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:02,936 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 7
2015-10-17 23:08:02,936 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000002 to attempt_1445094324383_0003_m_000001_0
2015-10-17 23:08:02,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000003 to attempt_1445094324383_0003_m_000005_0
2015-10-17 23:08:02,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000004 to attempt_1445094324383_0003_m_000008_0
2015-10-17 23:08:02,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000005 to attempt_1445094324383_0003_m_000009_0
2015-10-17 23:08:02,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000006 to attempt_1445094324383_0003_m_000002_0
2015-10-17 23:08:02,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000007 to attempt_1445094324383_0003_m_000004_0
2015-10-17 23:08:02,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000008 to attempt_1445094324383_0003_m_000007_0
2015-10-17 23:08:02,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-20>
2015-10-17 23:08:02,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:02,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:7 RackLocal:0
2015-10-17 23:08:03,014 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:03,061 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445094324383_0003/job.jar
2015-10-17 23:08:03,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445094324383_0003/job.xml
2015-10-17 23:08:03,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 23:08:03,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 23:08:03,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 23:08:03,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:08:03,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:03,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:08:03,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:03,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:08:03,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:03,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:08:03,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:03,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:08:03,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:03,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:08:03,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:03,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:08:03,170 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000003 taskAttempt attempt_1445094324383_0003_m_000005_0
2015-10-17 23:08:03,170 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000002 taskAttempt attempt_1445094324383_0003_m_000001_0
2015-10-17 23:08:03,170 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000004 taskAttempt attempt_1445094324383_0003_m_000008_0
2015-10-17 23:08:03,170 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000007 taskAttempt attempt_1445094324383_0003_m_000004_0
2015-10-17 23:08:03,170 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000006 taskAttempt attempt_1445094324383_0003_m_000002_0
2015-10-17 23:08:03,170 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000005 taskAttempt attempt_1445094324383_0003_m_000009_0
2015-10-17 23:08:03,170 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000008 taskAttempt attempt_1445094324383_0003_m_000007_0
2015-10-17 23:08:03,170 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000009_0
2015-10-17 23:08:03,170 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000001_0
2015-10-17 23:08:03,170 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000007_0
2015-10-17 23:08:03,170 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000005_0
2015-10-17 23:08:03,170 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000004_0
2015-10-17 23:08:03,170 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000002_0
2015-10-17 23:08:03,170 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000008_0
2015-10-17 23:08:03,170 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:08:03,201 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:08:03,201 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 23:08:03,201 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 23:08:03,201 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:08:03,201 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 23:08:03,217 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:08:03,295 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000001_0 : 13562
2015-10-17 23:08:03,295 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000009_0 : 13562
2015-10-17 23:08:03,295 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000005_0 : 13562
2015-10-17 23:08:03,295 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000008_0 : 13562
2015-10-17 23:08:03,311 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000007_0 : 13562
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000008_0] using containerId: [container_1445094324383_0003_01_000004 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000009_0] using containerId: [container_1445094324383_0003_01_000005 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000001_0] using containerId: [container_1445094324383_0003_01_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:08:03,311 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000002_0 : 13562
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000007_0] using containerId: [container_1445094324383_0003_01_000008 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000005_0] using containerId: [container_1445094324383_0003_01_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000008
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000009
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000001
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000002_0] using containerId: [container_1445094324383_0003_01_000006 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000007
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000005
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000002
2015-10-17 23:08:03,311 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:08:03,326 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000004_0 : 13562
2015-10-17 23:08:03,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000004_0] using containerId: [container_1445094324383_0003_01_000007 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 23:08:03,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:08:03,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000004
2015-10-17 23:08:03,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:08:03,967 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-20> knownNMs=4
2015-10-17 23:08:03,967 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-20>
2015-10-17 23:08:03,967 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:05,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:08:05,045 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:05,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000009 to attempt_1445094324383_0003_m_000000_0
2015-10-17 23:08:05,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-21>
2015-10-17 23:08:05,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:05,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:7 RackLocal:1
2015-10-17 23:08:05,045 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:05,045 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:08:05,217 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000009 taskAttempt attempt_1445094324383_0003_m_000000_0
2015-10-17 23:08:05,217 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000000_0
2015-10-17 23:08:05,217 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-17 23:08:05,576 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000000_0 : 13562
2015-10-17 23:08:05,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000000_0] using containerId: [container_1445094324383_0003_01_000009 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:52368]
2015-10-17 23:08:05,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:08:05,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000000
2015-10-17 23:08:05,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:08:06,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:3072, vCores:-24> knownNMs=4
2015-10-17 23:08:06,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 23:08:06,108 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:06,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000010 to attempt_1445094324383_0003_m_000003_0
2015-10-17 23:08:06,108 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:06,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000011 to attempt_1445094324383_0003_m_000006_0
2015-10-17 23:08:06,108 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:06,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-17 23:08:06,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:06,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-17 23:08:06,108 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:08:06,108 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:06,108 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:08:06,170 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000010 taskAttempt attempt_1445094324383_0003_m_000003_0
2015-10-17 23:08:06,170 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000003_0
2015-10-17 23:08:06,170 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:08:06,233 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000011 taskAttempt attempt_1445094324383_0003_m_000006_0
2015-10-17 23:08:06,233 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000006_0
2015-10-17 23:08:06,233 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-17 23:08:06,248 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000003_0 : 13562
2015-10-17 23:08:06,248 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000003_0] using containerId: [container_1445094324383_0003_01_000010 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 23:08:06,248 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:08:06,248 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000003
2015-10-17 23:08:06,248 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:08:06,326 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000006_0 : 13562
2015-10-17 23:08:06,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000006_0] using containerId: [container_1445094324383_0003_01_000011 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:52368]
2015-10-17 23:08:06,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:08:06,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000006
2015-10-17 23:08:06,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:08:07,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-17 23:08:07,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:08:07,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Cannot assign container Container: [ContainerId: container_1445094324383_0003_01_000012, NodeId: MININT-FNANLI5.fareast.corp.microsoft.com:52368, NodeHttpAddress: MININT-FNANLI5.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 20, Token: Token { kind: ContainerToken, service: *************:52368 }, ] for a map as either  container memory less than required <memory:1024, vCores:1> or no pending map tasks - maps.isEmpty=true
2015-10-17 23:08:07,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-17 23:08:07,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:07,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:7 RackLocal:3
2015-10-17 23:08:07,233 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:08:07,248 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:08:07,264 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:08:07,264 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:08:07,279 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000003 asked for a task
2015-10-17 23:08:07,279 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000003 given task: attempt_1445094324383_0003_m_000005_0
2015-10-17 23:08:07,279 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000005 asked for a task
2015-10-17 23:08:07,279 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000004 asked for a task
2015-10-17 23:08:07,279 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000005 given task: attempt_1445094324383_0003_m_000009_0
2015-10-17 23:08:07,279 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000004 given task: attempt_1445094324383_0003_m_000008_0
2015-10-17 23:08:07,295 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000002 asked for a task
2015-10-17 23:08:07,295 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000002 given task: attempt_1445094324383_0003_m_000001_0
2015-10-17 23:08:08,295 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=0 release= 1 newContainers=0 finishedContainers=1 resourcelimit=<memory:3072, vCores:-24> knownNMs=4
2015-10-17 23:08:08,311 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000012
2015-10-17 23:08:08,311 ERROR [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Container complete event for unknown container id container_1445094324383_0003_01_000012
2015-10-17 23:08:08,311 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-17 23:08:08,311 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:09,514 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:08:09,576 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000010 asked for a task
2015-10-17 23:08:09,576 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000010 given task: attempt_1445094324383_0003_m_000003_0
2015-10-17 23:08:11,514 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:08:11,545 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-17 23:08:11,545 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:11,670 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:08:11,686 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000006 asked for a task
2015-10-17 23:08:11,686 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000006 given task: attempt_1445094324383_0003_m_000002_0
2015-10-17 23:08:11,873 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000007 asked for a task
2015-10-17 23:08:11,873 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000007 given task: attempt_1445094324383_0003_m_000004_0
2015-10-17 23:08:13,858 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:08:13,920 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:08:14,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-17 23:08:14,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:14,233 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000011 asked for a task
2015-10-17 23:08:14,233 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000011 given task: attempt_1445094324383_0003_m_000006_0
2015-10-17 23:08:14,545 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:08:14,577 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000008 asked for a task
2015-10-17 23:08:14,577 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000008 given task: attempt_1445094324383_0003_m_000007_0
2015-10-17 23:08:14,827 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.13102192
2015-10-17 23:08:14,873 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.16604526
2015-10-17 23:08:14,920 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.13104132
2015-10-17 23:08:14,920 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.13102318
2015-10-17 23:08:15,217 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000009 asked for a task
2015-10-17 23:08:15,217 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000009 given task: attempt_1445094324383_0003_m_000000_0
2015-10-17 23:08:16,280 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:16,280 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:17,170 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.13102706
2015-10-17 23:08:17,842 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.13102192
2015-10-17 23:08:17,889 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.16604526
2015-10-17 23:08:17,936 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.13104132
2015-10-17 23:08:17,936 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.13102318
2015-10-17 23:08:20,170 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.13102706
2015-10-17 23:08:20,858 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.13102192
2015-10-17 23:08:20,905 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.16604526
2015-10-17 23:08:20,952 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.13104132
2015-10-17 23:08:20,967 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.13102318
2015-10-17 23:08:23,202 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.13102706
2015-10-17 23:08:23,874 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.23841035
2015-10-17 23:08:23,936 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.24754988
2015-10-17 23:08:23,983 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.23260549
2015-10-17 23:08:23,983 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.21503398
2015-10-17 23:08:24,233 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.06328928
2015-10-17 23:08:24,264 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.06718555
2015-10-17 23:08:24,952 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.058544274
2015-10-17 23:08:26,264 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.23922287
2015-10-17 23:08:26,983 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.23921879
2015-10-17 23:08:26,983 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.3031575
2015-10-17 23:08:26,999 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.23922269
2015-10-17 23:08:27,014 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.23921506
2015-10-17 23:08:28,171 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.12991059
2015-10-17 23:08:28,343 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.131014
2015-10-17 23:08:28,436 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.11773201
2015-10-17 23:08:29,311 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.23922287
2015-10-17 23:08:29,999 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.23921879
2015-10-17 23:08:30,014 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.3031575
2015-10-17 23:08:30,030 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.23922269
2015-10-17 23:08:30,030 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.23921506
2015-10-17 23:08:31,374 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.13104042
2015-10-17 23:08:31,608 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.131014
2015-10-17 23:08:31,749 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.13103712
2015-10-17 23:08:32,358 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.23922287
2015-10-17 23:08:33,046 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.3031575
2015-10-17 23:08:33,046 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.23921879
2015-10-17 23:08:33,046 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.23922269
2015-10-17 23:08:33,046 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.23921506
2015-10-17 23:08:34,733 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.13104042
2015-10-17 23:08:34,811 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.08662571
2015-10-17 23:08:34,968 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.131014
2015-10-17 23:08:35,030 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.13103712
2015-10-17 23:08:35,390 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.2655877
2015-10-17 23:08:36,077 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.34743196
2015-10-17 23:08:36,077 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.3474145
2015-10-17 23:08:36,077 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.3384976
2015-10-17 23:08:36,077 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.42679134
2015-10-17 23:08:38,202 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.13101135
2015-10-17 23:08:38,265 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.13104042
2015-10-17 23:08:38,405 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.131014
2015-10-17 23:08:38,499 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.3473985
2015-10-17 23:08:38,499 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.13103712
2015-10-17 23:08:39,140 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.4402952
2015-10-17 23:08:39,140 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.3474054
2015-10-17 23:08:39,140 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.34743196
2015-10-17 23:08:39,140 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.3474145
2015-10-17 23:08:41,234 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.093806654
2015-10-17 23:08:41,546 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.3473985
2015-10-17 23:08:41,671 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.13101135
2015-10-17 23:08:41,734 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.13104042
2015-10-17 23:08:41,968 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.131014
2015-10-17 23:08:41,999 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.13103712
2015-10-17 23:08:42,155 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.4402952
2015-10-17 23:08:42,155 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.3474054
2015-10-17 23:08:42,171 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.3474145
2015-10-17 23:08:42,171 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.34743196
2015-10-17 23:08:44,593 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.3473985
2015-10-17 23:08:45,046 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.13101934
2015-10-17 23:08:45,156 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.46877417
2015-10-17 23:08:45,156 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.34945512
2015-10-17 23:08:45,171 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.13101135
2015-10-17 23:08:45,187 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.41278237
2015-10-17 23:08:45,202 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.42076877
2015-10-17 23:08:45,452 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.13104042
2015-10-17 23:08:45,718 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.131014
2015-10-17 23:08:45,718 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.13103712
2015-10-17 23:08:47,593 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.45559394
2015-10-17 23:08:48,187 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.5773621
2015-10-17 23:08:48,187 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.45560944
2015-10-17 23:08:48,234 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.45562187
2015-10-17 23:08:48,234 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.455629
2015-10-17 23:08:48,828 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.13101934
2015-10-17 23:08:48,859 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.13101135
2015-10-17 23:08:48,953 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.13104042
2015-10-17 23:08:49,171 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.131014
2015-10-17 23:08:49,265 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.13103712
2015-10-17 23:08:50,609 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.45559394
2015-10-17 23:08:51,187 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.45560944
2015-10-17 23:08:51,187 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.5773621
2015-10-17 23:08:51,234 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.45562187
2015-10-17 23:08:51,265 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.455629
2015-10-17 23:08:52,500 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.13104042
2015-10-17 23:08:52,671 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.13101934
2015-10-17 23:08:52,718 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.131014
2015-10-17 23:08:52,750 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.13101135
2015-10-17 23:08:52,921 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.13103712
2015-10-17 23:08:53,671 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.45559394
2015-10-17 23:08:54,203 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.5773621
2015-10-17 23:08:54,218 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.45560944
2015-10-17 23:08:54,265 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.45562187
2015-10-17 23:08:54,265 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.455629
2015-10-17 23:08:56,125 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.18552956
2015-10-17 23:08:56,125 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.15486628
2015-10-17 23:08:56,484 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.15299791
2015-10-17 23:08:56,515 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.13101135
2015-10-17 23:08:56,578 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.13101934
2015-10-17 23:08:56,687 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.52467406
2015-10-17 23:08:57,218 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.45560944
2015-10-17 23:08:57,234 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.6166649
2015-10-17 23:08:57,265 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.5503179
2015-10-17 23:08:57,312 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.5638294
2015-10-17 23:08:58,109 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.6166649
2015-10-17 23:08:59,703 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.5637838
2015-10-17 23:08:59,953 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.2230899
2015-10-17 23:08:59,953 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.23919508
2015-10-17 23:09:00,265 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.13101135
2015-10-17 23:09:00,265 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.22280616
2015-10-17 23:09:00,265 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.667
2015-10-17 23:09:00,265 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.56381226
2015-10-17 23:09:00,312 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.5638294
2015-10-17 23:09:00,328 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.56384325
2015-10-17 23:09:00,531 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.13101934
2015-10-17 23:09:02,719 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.5637838
2015-10-17 23:09:03,297 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.667
2015-10-17 23:09:03,297 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.56381226
2015-10-17 23:09:03,328 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.5638294
2015-10-17 23:09:03,344 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.56384325
2015-10-17 23:09:03,547 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.23924798
2015-10-17 23:09:03,562 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.23919508
2015-10-17 23:09:03,828 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.23924637
2015-10-17 23:09:04,531 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.13101135
2015-10-17 23:09:04,578 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.13101934
2015-10-17 23:09:05,719 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.5637838
2015-10-17 23:09:06,297 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.673078
2015-10-17 23:09:06,312 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.56381226
2015-10-17 23:09:06,344 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.56384325
2015-10-17 23:09:06,359 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.6485155
2015-10-17 23:09:07,047 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.23919508
2015-10-17 23:09:07,078 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.23924798
2015-10-17 23:09:07,250 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.6485155
2015-10-17 23:09:07,406 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.23924637
2015-10-17 23:09:07,984 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.5637838
2015-10-17 23:09:08,234 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.13101135
2015-10-17 23:09:08,234 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.13101934
2015-10-17 23:09:08,734 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.667
2015-10-17 23:09:09,281 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.56384325
2015-10-17 23:09:09,313 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.7255433
2015-10-17 23:09:09,328 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.63409734
2015-10-17 23:09:09,359 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.667
2015-10-17 23:09:09,375 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.667
2015-10-17 23:09:10,391 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.63409734
2015-10-17 23:09:10,406 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.23919508
2015-10-17 23:09:10,625 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.23924798
2015-10-17 23:09:10,953 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.23924637
2015-10-17 23:09:11,797 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.667
2015-10-17 23:09:11,938 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.13101934
2015-10-17 23:09:12,172 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.13101135
2015-10-17 23:09:12,359 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.7829872
2015-10-17 23:09:12,359 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.667
2015-10-17 23:09:12,422 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.667
2015-10-17 23:09:12,422 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.667
2015-10-17 23:09:13,766 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.23919508
2015-10-17 23:09:13,985 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.23924798
2015-10-17 23:09:14,281 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.23924637
2015-10-17 23:09:14,844 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.667
2015-10-17 23:09:15,438 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.667
2015-10-17 23:09:15,438 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.8507207
2015-10-17 23:09:15,485 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.667
2015-10-17 23:09:15,500 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.667
2015-10-17 23:09:15,891 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.13101934
2015-10-17 23:09:16,047 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.13101135
2015-10-17 23:09:17,063 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.23919508
2015-10-17 23:09:17,266 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.23924798
2015-10-17 23:09:17,532 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.23924637
2015-10-17 23:09:17,953 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.670998
2015-10-17 23:09:18,610 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.667
2015-10-17 23:09:18,610 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.9176163
2015-10-17 23:09:18,625 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.7018126
2015-10-17 23:09:18,641 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.667
2015-10-17 23:09:19,688 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.13101934
2015-10-17 23:09:19,828 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.18631393
2015-10-17 23:09:20,547 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.23919508
2015-10-17 23:09:20,750 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.23924798
2015-10-17 23:09:21,078 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.71878093
2015-10-17 23:09:21,172 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.23924637
2015-10-17 23:09:21,782 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 0.9768887
2015-10-17 23:09:21,782 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.7493051
2015-10-17 23:09:21,782 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.7037211
2015-10-17 23:09:21,782 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.691497
2015-10-17 23:09:23,000 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000009_0 is : 1.0
2015-10-17 23:09:23,016 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0003_m_000009_0
2015-10-17 23:09:23,016 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:09:23,016 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000005 taskAttempt attempt_1445094324383_0003_m_000009_0
2015-10-17 23:09:23,016 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000009_0
2015-10-17 23:09:23,016 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:09:23,219 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:09:23,235 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0003_m_000009_0
2015-10-17 23:09:23,235 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:09:23,250 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 23:09:23,641 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.13101934
2015-10-17 23:09:23,672 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.23923388
2015-10-17 23:09:23,704 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:7 RackLocal:3
2015-10-17 23:09:23,704 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:23,704 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 23:09:23,704 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-17 23:09:23,704 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:7 RackLocal:3
2015-10-17 23:09:23,969 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445094324383_0003_m_000000
2015-10-17 23:09:23,969 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 23:09:23,969 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445094324383_0003_m_000000
2015-10-17 23:09:23,969 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:09:23,969 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:09:23,969 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:09:24,047 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.23919508
2015-10-17 23:09:24,079 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.7556422
2015-10-17 23:09:24,219 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.23924798
2015-10-17 23:09:24,625 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.23924637
2015-10-17 23:09:24,735 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:7 RackLocal:3
2015-10-17 23:09:24,735 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:09:24,735 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000005
2015-10-17 23:09:24,735 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:7 RackLocal:3
2015-10-17 23:09:24,735 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:09:24,797 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.7912232
2015-10-17 23:09:24,797 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.73920745
2015-10-17 23:09:24,797 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.73113585
2015-10-17 23:09:27,110 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.78872955
2015-10-17 23:09:27,235 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.17814642
2015-10-17 23:09:27,344 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.23923388
2015-10-17 23:09:27,407 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.26972097
2015-10-17 23:09:27,626 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.23924798
2015-10-17 23:09:27,860 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.76313335
2015-10-17 23:09:27,860 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.82830006
2015-10-17 23:09:27,860 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.7694131
2015-10-17 23:09:28,266 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.23924637
2015-10-17 23:09:30,126 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.82094526
2015-10-17 23:09:30,876 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.86186576
2015-10-17 23:09:30,876 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.8018577
2015-10-17 23:09:30,876 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.7954146
2015-10-17 23:09:31,094 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.3419658
2015-10-17 23:09:31,407 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.27981374
2015-10-17 23:09:31,485 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.23826493
2015-10-17 23:09:31,719 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.23923388
2015-10-17 23:09:31,923 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.30986747
2015-10-17 23:09:33,173 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.8594102
2015-10-17 23:09:33,891 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.9052644
2015-10-17 23:09:33,891 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.8433898
2015-10-17 23:09:33,891 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.8361924
2015-10-17 23:09:34,610 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.3474061
2015-10-17 23:09:34,938 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.34088433
2015-10-17 23:09:35,266 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.23921585
2015-10-17 23:09:35,329 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.34743145
2015-10-17 23:09:35,688 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.23923388
2015-10-17 23:09:36,188 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.89775187
2015-10-17 23:09:36,954 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.86975586
2015-10-17 23:09:36,954 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.9462879
2015-10-17 23:09:36,954 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.8774811
2015-10-17 23:09:38,142 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.3474061
2015-10-17 23:09:38,610 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.34742972
2015-10-17 23:09:38,876 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.23921585
2015-10-17 23:09:38,892 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.34743145
2015-10-17 23:09:39,188 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.9338417
2015-10-17 23:09:39,360 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.23923388
2015-10-17 23:09:40,001 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 0.9811934
2015-10-17 23:09:40,001 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.9068395
2015-10-17 23:09:40,001 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.89847106
2015-10-17 23:09:41,642 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.3474061
2015-10-17 23:09:42,204 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 0.9639429
2015-10-17 23:09:42,251 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.34742972
2015-10-17 23:09:42,439 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.34743145
2015-10-17 23:09:42,689 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.23921585
2015-10-17 23:09:42,985 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.23923388
2015-10-17 23:09:43,032 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 1.0
2015-10-17 23:09:43,032 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.93513906
2015-10-17 23:09:43,032 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.92640835
2015-10-17 23:09:45,251 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.3474061
2015-10-17 23:09:45,517 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 1.0
2015-10-17 23:09:45,704 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.34742972
2015-10-17 23:09:45,939 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.34743145
2015-10-17 23:09:46,048 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 0.96503806
2015-10-17 23:09:46,048 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 0.97434306
2015-10-17 23:09:46,064 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 1.0
2015-10-17 23:09:46,392 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.23921585
2015-10-17 23:09:46,876 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.23923388
2015-10-17 23:09:48,532 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 1.0
2015-10-17 23:09:48,673 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.3474061
2015-10-17 23:09:49,064 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 1.0
2015-10-17 23:09:49,095 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 1.0
2015-10-17 23:09:49,157 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.34742972
2015-10-17 23:09:49,361 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.34743145
2015-10-17 23:09:50,126 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.23921585
2015-10-17 23:09:50,611 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.23923388
2015-10-17 23:09:51,970 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.3474061
2015-10-17 23:09:52,142 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 1.0
2015-10-17 23:09:52,158 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 1.0
2015-10-17 23:09:52,408 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.34742972
2015-10-17 23:09:52,595 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.34743145
2015-10-17 23:09:53,986 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.23921585
2015-10-17 23:09:54,517 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.23923388
2015-10-17 23:09:55,267 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.3474061
2015-10-17 23:09:55,720 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.34742972
2015-10-17 23:09:55,861 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.34743145
2015-10-17 23:09:57,626 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.23921585
2015-10-17 23:09:58,080 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.23923388
2015-10-17 23:09:58,533 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.36471725
2015-10-17 23:09:59,126 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.34742972
2015-10-17 23:09:59,220 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.34743145
2015-10-17 23:10:00,345 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000008_0 is : 1.0
2015-10-17 23:10:00,377 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0003_m_000008_0
2015-10-17 23:10:00,377 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:10:00,377 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000004 taskAttempt attempt_1445094324383_0003_m_000008_0
2015-10-17 23:10:00,377 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000008_0
2015-10-17 23:10:00,377 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:10:00,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:10:00,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0003_m_000008_0
2015-10-17 23:10:00,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:10:00,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 23:10:01,314 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:7 RackLocal:3
2015-10-17 23:10:01,439 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.23921585
2015-10-17 23:10:02,080 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.23923388
2015-10-17 23:10:02,142 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.44065616
2015-10-17 23:10:02,361 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000004
2015-10-17 23:10:02,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:10:02,361 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:7 RackLocal:3
2015-10-17 23:10:02,689 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000003_0 is : 1.0
2015-10-17 23:10:02,689 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0003_m_000003_0
2015-10-17 23:10:02,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:10:02,689 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000010 taskAttempt attempt_1445094324383_0003_m_000003_0
2015-10-17 23:10:02,689 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000003_0
2015-10-17 23:10:02,689 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:10:02,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:10:02,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0003_m_000003_0
2015-10-17 23:10:02,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:10:02,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 23:10:02,923 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.36659938
2015-10-17 23:10:02,955 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.40437
2015-10-17 23:10:03,424 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:7 RackLocal:3
2015-10-17 23:10:04,189 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000001_0 is : 1.0
2015-10-17 23:10:04,252 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0003_m_000001_0
2015-10-17 23:10:04,252 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000005_0 is : 1.0
2015-10-17 23:10:04,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:10:04,252 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000002 taskAttempt attempt_1445094324383_0003_m_000001_0
2015-10-17 23:10:04,252 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000001_0
2015-10-17 23:10:04,252 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:10:04,252 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0003_m_000005_0
2015-10-17 23:10:04,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:10:04,252 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000003 taskAttempt attempt_1445094324383_0003_m_000005_0
2015-10-17 23:10:04,252 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000005_0
2015-10-17 23:10:04,252 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:10:04,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:10:04,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0003_m_000001_0
2015-10-17 23:10:04,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:10:04,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 23:10:04,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:10:04,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0003_m_000005_0
2015-10-17 23:10:04,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:10:04,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 23:10:04,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000010
2015-10-17 23:10:04,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:10:04,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 23:10:04,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:10:04,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000013 to attempt_1445094324383_0003_r_000000_0
2015-10-17 23:10:04,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:12 ContRel:1 HostLocal:7 RackLocal:3
2015-10-17 23:10:04,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:10:04,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:10:04,611 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000013 taskAttempt attempt_1445094324383_0003_r_000000_0
2015-10-17 23:10:04,611 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_r_000000_0
2015-10-17 23:10:04,611 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:10:04,720 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_r_000000_0 : 13562
2015-10-17 23:10:04,720 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_r_000000_0] using containerId: [container_1445094324383_0003_01_000013 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 23:10:04,720 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:10:04,720 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_r_000000
2015-10-17 23:10:04,720 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:10:05,314 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.23921585
2015-10-17 23:10:05,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=1 release= 0 newContainers=1 finishedContainers=2 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:10:05,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000002
2015-10-17 23:10:05,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000003
2015-10-17 23:10:05,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:10:05,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:10:05,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:10:05,611 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:10:05,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000014 to attempt_1445094324383_0003_m_000000_1
2015-10-17 23:10:05,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:13 ContRel:1 HostLocal:7 RackLocal:4
2015-10-17 23:10:05,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:10:05,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:10:05,611 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000014 taskAttempt attempt_1445094324383_0003_m_000000_1
2015-10-17 23:10:05,611 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000000_1
2015-10-17 23:10:05,611 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:10:05,767 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445094324383_0003_m_000006
2015-10-17 23:10:05,767 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 23:10:05,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445094324383_0003_m_000006
2015-10-17 23:10:05,767 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.45561612
2015-10-17 23:10:05,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:10:05,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:10:05,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:10:05,767 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000000_1 : 13562
2015-10-17 23:10:05,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000000_1] using containerId: [container_1445094324383_0003_01_000014 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 23:10:05,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:10:05,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000000
2015-10-17 23:10:05,783 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.24102096
2015-10-17 23:10:06,345 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.43282968
2015-10-17 23:10:06,580 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.4556257
2015-10-17 23:10:06,642 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:13 ContRel:1 HostLocal:7 RackLocal:4
2015-10-17 23:10:06,658 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:10:08,017 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:10:08,064 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_r_000013 asked for a task
2015-10-17 23:10:08,064 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_r_000013 given task: attempt_1445094324383_0003_r_000000_0
2015-10-17 23:10:09,142 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.23921585
2015-10-17 23:10:09,142 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.45561612
2015-10-17 23:10:09,392 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:10:09,408 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000014 asked for a task
2015-10-17 23:10:09,408 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000014 given task: attempt_1445094324383_0003_m_000000_1
2015-10-17 23:10:09,721 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.3292631
2015-10-17 23:10:09,752 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 23:10:09,783 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.45565325
2015-10-17 23:10:10,064 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.4556257
2015-10-17 23:10:10,752 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:11,768 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:12,768 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:12,814 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.45561612
2015-10-17 23:10:12,814 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.24849486
2015-10-17 23:10:13,268 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.45565325
2015-10-17 23:10:13,408 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.34743717
2015-10-17 23:10:13,627 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.4556257
2015-10-17 23:10:13,799 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:14,814 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:15,689 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.06666667
2015-10-17 23:10:15,861 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:16,315 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.45561612
2015-10-17 23:10:16,736 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.45565325
2015-10-17 23:10:16,846 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:16,861 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.33895317
2015-10-17 23:10:16,955 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.13101934
2015-10-17 23:10:17,283 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.4556257
2015-10-17 23:10:17,486 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.34743717
2015-10-17 23:10:17,877 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:18,018 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:10:18,018 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000015 to attempt_1445094324383_0003_m_000006_1
2015-10-17 23:10:18,018 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:8 RackLocal:4
2015-10-17 23:10:18,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:10:18,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:10:18,018 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000015 taskAttempt attempt_1445094324383_0003_m_000006_1
2015-10-17 23:10:18,018 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000006_1
2015-10-17 23:10:18,018 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:58497
2015-10-17 23:10:18,361 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000006_1 : 13562
2015-10-17 23:10:18,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000006_1] using containerId: [container_1445094324383_0003_01_000015 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:58497]
2015-10-17 23:10:18,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:10:18,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000006
2015-10-17 23:10:18,705 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.10000001
2015-10-17 23:10:18,924 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:19,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-32> knownNMs=5
2015-10-17 23:10:19,783 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.45561612
2015-10-17 23:10:19,924 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:20,018 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.13101934
2015-10-17 23:10:20,299 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.45565325
2015-10-17 23:10:20,487 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.3474062
2015-10-17 23:10:20,799 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445094324383_0003_m_000004
2015-10-17 23:10:20,799 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 23:10:20,799 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445094324383_0003_m_000004
2015-10-17 23:10:20,799 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:10:20,799 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:10:20,799 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:10:20,830 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.4556257
2015-10-17 23:10:20,908 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:21,158 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:8 RackLocal:4
2015-10-17 23:10:21,158 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-33> knownNMs=5
2015-10-17 23:10:21,283 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.34743717
2015-10-17 23:10:21,721 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:10:21,940 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:22,190 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:10:22,190 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:10:22,190 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000016 to attempt_1445094324383_0003_m_000004_1
2015-10-17 23:10:22,190 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 23:10:22,190 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:10:22,190 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:10:22,190 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000016 taskAttempt attempt_1445094324383_0003_m_000004_1
2015-10-17 23:10:22,190 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000004_1
2015-10-17 23:10:22,190 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:58497
2015-10-17 23:10:22,705 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000004_1 : 13562
2015-10-17 23:10:22,705 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000004_1] using containerId: [container_1445094324383_0003_01_000016 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:58497]
2015-10-17 23:10:22,705 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:10:22,705 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000004
2015-10-17 23:10:22,924 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:23,065 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.13101934
2015-10-17 23:10:23,174 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.45561612
2015-10-17 23:10:23,221 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 23:10:23,815 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.45565325
2015-10-17 23:10:23,940 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:24,362 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.4556257
2015-10-17 23:10:24,409 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.3474062
2015-10-17 23:10:24,737 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:10:24,971 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:25,002 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.34743717
2015-10-17 23:10:25,987 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:26,080 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.18107554
2015-10-17 23:10:26,690 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.45561612
2015-10-17 23:10:27,065 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:27,315 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.45565325
2015-10-17 23:10:27,768 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.4556257
2015-10-17 23:10:27,862 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:10:28,143 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.3474062
2015-10-17 23:10:28,143 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:28,690 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.34743717
2015-10-17 23:10:29,143 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.23921585
2015-10-17 23:10:29,190 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:29,893 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.45561612
2015-10-17 23:10:30,237 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:30,549 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.45565325
2015-10-17 23:10:30,924 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:10:31,112 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.4556257
2015-10-17 23:10:31,315 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:31,784 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.3474062
2015-10-17 23:10:32,206 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.23921585
2015-10-17 23:10:32,393 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:32,596 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.34743717
2015-10-17 23:10:33,268 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.5250762
2015-10-17 23:10:33,440 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:33,956 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:10:33,956 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.45565325
2015-10-17 23:10:34,471 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.50698566
2015-10-17 23:10:34,471 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:34,596 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:10:34,924 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000016 asked for a task
2015-10-17 23:10:34,924 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000016 given task: attempt_1445094324383_0003_m_000004_1
2015-10-17 23:10:35,346 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.23921585
2015-10-17 23:10:35,518 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:35,659 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.3474062
2015-10-17 23:10:35,815 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445094324383_0003_m_000007
2015-10-17 23:10:35,815 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 23:10:35,815 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445094324383_0003_m_000007
2015-10-17 23:10:35,815 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:10:35,815 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:10:35,815 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000007_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:10:36,190 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:8 RackLocal:5
2015-10-17 23:10:36,659 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.56381613
2015-10-17 23:10:36,831 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:36,971 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:10:37,315 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.34743717
2015-10-17 23:10:37,440 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.5088422
2015-10-17 23:10:37,518 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 23:10:37,753 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.56380385
2015-10-17 23:10:37,846 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:38,378 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.27748016
2015-10-17 23:10:38,940 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:39,518 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.3474062
2015-10-17 23:10:39,565 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:10:39,737 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000015 asked for a task
2015-10-17 23:10:39,737 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000015 given task: attempt_1445094324383_0003_m_000006_1
2015-10-17 23:10:40,003 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:40,003 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:10:40,096 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.56381613
2015-10-17 23:10:40,784 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.5638328
2015-10-17 23:10:41,018 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:41,065 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.56380385
2015-10-17 23:10:41,315 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.34743717
2015-10-17 23:10:41,487 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.33842254
2015-10-17 23:10:42,034 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:43,456 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.56381613
2015-10-17 23:10:43,753 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:10:43,753 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:43,753 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.3474062
2015-10-17 23:10:44,237 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.5638328
2015-10-17 23:10:44,487 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.3474062
2015-10-17 23:10:44,550 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.56380385
2015-10-17 23:10:44,768 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:44,862 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.34743717
2015-10-17 23:10:45,800 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:46,800 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:10:46,831 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:46,878 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.56381613
2015-10-17 23:10:47,519 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.3474062
2015-10-17 23:10:47,534 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.3474062
2015-10-17 23:10:47,706 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.5638328
2015-10-17 23:10:47,831 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:48,034 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.56380385
2015-10-17 23:10:48,737 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.34743717
2015-10-17 23:10:48,894 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:49,831 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:10:49,972 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:50,441 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.56381613
2015-10-17 23:10:50,550 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.35115588
2015-10-17 23:10:51,019 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:51,222 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.5638328
2015-10-17 23:10:51,237 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.3474062
2015-10-17 23:10:51,581 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.56380385
2015-10-17 23:10:52,050 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:52,441 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.34978008
2015-10-17 23:10:52,909 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:10:53,128 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:53,566 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.37258005
2015-10-17 23:10:53,847 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.56381613
2015-10-17 23:10:54,175 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:54,206 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.101321734
2015-10-17 23:10:54,675 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.5638328
2015-10-17 23:10:54,753 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.3474062
2015-10-17 23:10:55,144 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.56380385
2015-10-17 23:10:55,175 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:55,972 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:10:56,019 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.40058938
2015-10-17 23:10:56,206 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:56,566 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.39863306
2015-10-17 23:10:57,222 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:57,253 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.56381613
2015-10-17 23:10:57,613 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.13101135
2015-10-17 23:10:58,253 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:58,253 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.5638328
2015-10-17 23:10:58,285 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.3735705
2015-10-17 23:10:58,644 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.56380385
2015-10-17 23:10:59,035 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:10:59,285 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:10:59,566 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.035229307
2015-10-17 23:10:59,628 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.4250155
2015-10-17 23:11:00,097 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.440323
2015-10-17 23:11:00,331 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:00,550 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.56381613
2015-10-17 23:11:00,925 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.13101135
2015-10-17 23:11:01,363 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:01,644 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.5638328
2015-10-17 23:11:01,910 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.56380385
2015-10-17 23:11:02,082 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:02,191 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.43608838
2015-10-17 23:11:02,972 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.45465156
2015-10-17 23:11:03,253 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.047545932
2015-10-17 23:11:03,316 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:03,832 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.6253426
2015-10-17 23:11:04,003 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.455643
2015-10-17 23:11:04,300 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.13101135
2015-10-17 23:11:04,332 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:05,113 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:05,222 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.5638328
2015-10-17 23:11:05,394 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:05,519 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.622929
2015-10-17 23:11:05,972 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.45563135
2015-10-17 23:11:06,003 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.45563135
2015-10-17 23:11:06,425 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:06,566 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.06317724
2015-10-17 23:11:06,613 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.6253426
2015-10-17 23:11:07,300 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.667
2015-10-17 23:11:07,472 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:07,738 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.13101135
2015-10-17 23:11:08,082 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.455643
2015-10-17 23:11:08,097 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.622929
2015-10-17 23:11:08,129 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:08,566 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.5993248
2015-10-17 23:11:08,785 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.667
2015-10-17 23:11:09,019 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.45563135
2015-10-17 23:11:09,019 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:09,847 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.45563135
2015-10-17 23:11:09,910 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.073274955
2015-10-17 23:11:10,050 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:10,832 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.667
2015-10-17 23:11:11,082 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:11,144 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:11,691 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.13101135
2015-10-17 23:11:11,879 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.455643
2015-10-17 23:11:12,035 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.4735411
2015-10-17 23:11:12,144 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:12,285 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.6669993
2015-10-17 23:11:12,363 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.667
2015-10-17 23:11:12,379 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.6669993
2015-10-17 23:11:13,176 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:13,347 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.09020855
2015-10-17 23:11:13,551 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.45563135
2015-10-17 23:11:14,144 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.667
2015-10-17 23:11:14,191 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:14,191 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:14,957 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.13101135
2015-10-17 23:11:15,066 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.4976439
2015-10-17 23:11:15,191 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:15,629 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.455643
2015-10-17 23:11:15,691 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.667
2015-10-17 23:11:15,691 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.667
2015-10-17 23:11:16,223 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:16,879 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.1195193
2015-10-17 23:11:17,238 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:17,301 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.45563135
2015-10-17 23:11:17,316 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:17,519 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:11:17,519 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000017 to attempt_1445094324383_0003_m_000007_1
2015-10-17 23:11:17,519 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:16 ContRel:1 HostLocal:9 RackLocal:5
2015-10-17 23:11:17,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:11:17,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000007_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:11:17,519 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000017 taskAttempt attempt_1445094324383_0003_m_000007_1
2015-10-17 23:11:17,519 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000007_1
2015-10-17 23:11:17,519 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:11:17,691 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.667
2015-10-17 23:11:17,723 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000007_1 : 13562
2015-10-17 23:11:17,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000007_1] using containerId: [container_1445094324383_0003_01_000017 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:18641]
2015-10-17 23:11:17,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000007_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:11:17,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000007
2015-10-17 23:11:18,129 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.52597725
2015-10-17 23:11:18,316 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:18,566 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 23:11:18,738 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.13101135
2015-10-17 23:11:19,066 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.667
2015-10-17 23:11:19,066 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.667
2015-10-17 23:11:19,332 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:19,379 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.455643
2015-10-17 23:11:20,254 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:20,363 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:20,895 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.13104042
2015-10-17 23:11:21,113 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.667
2015-10-17 23:11:21,160 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.54845077
2015-10-17 23:11:21,223 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.45563135
2015-10-17 23:11:21,395 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:22,426 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:22,488 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.667
2015-10-17 23:11:22,488 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.667
2015-10-17 23:11:22,566 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:11:22,629 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000017 asked for a task
2015-10-17 23:11:22,629 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000017 given task: attempt_1445094324383_0003_m_000007_1
2015-10-17 23:11:23,098 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.455643
2015-10-17 23:11:23,098 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.13591555
2015-10-17 23:11:23,301 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:23,442 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:24,207 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.56380075
2015-10-17 23:11:24,363 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.13104042
2015-10-17 23:11:24,473 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:24,613 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.667
2015-10-17 23:11:24,832 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.45563135
2015-10-17 23:11:25,520 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:26,113 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.667
2015-10-17 23:11:26,160 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.667
2015-10-17 23:11:26,348 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:26,551 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:26,676 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.455643
2015-10-17 23:11:26,723 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.21640609
2015-10-17 23:11:27,223 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.56380075
2015-10-17 23:11:27,582 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:27,942 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.13104042
2015-10-17 23:11:28,020 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.667
2015-10-17 23:11:28,426 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.45563135
2015-10-17 23:11:28,598 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:29,348 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:29,535 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.667
2015-10-17 23:11:29,629 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.667
2015-10-17 23:11:29,629 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:30,223 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.5650594
2015-10-17 23:11:30,285 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.455643
2015-10-17 23:11:30,285 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.23923388
2015-10-17 23:11:30,629 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:31,332 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.13104042
2015-10-17 23:11:31,442 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.667
2015-10-17 23:11:31,676 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:32,192 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.45563135
2015-10-17 23:11:32,192 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.12880006
2015-10-17 23:11:32,395 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:32,707 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:32,973 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.667
2015-10-17 23:11:33,020 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.667
2015-10-17 23:11:33,239 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.5915075
2015-10-17 23:11:34,192 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.23923388
2015-10-17 23:11:34,192 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:34,301 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.455643
2015-10-17 23:11:34,707 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.667
2015-10-17 23:11:34,786 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.13104042
2015-10-17 23:11:35,223 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:35,239 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.13103712
2015-10-17 23:11:35,411 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:35,833 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.45563135
2015-10-17 23:11:36,161 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.6683212
2015-10-17 23:11:36,208 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.667
2015-10-17 23:11:36,270 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:36,270 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.6116781
2015-10-17 23:11:37,286 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:37,739 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.23923388
2015-10-17 23:11:37,895 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.67937624
2015-10-17 23:11:38,239 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.455643
2015-10-17 23:11:38,286 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.13103712
2015-10-17 23:11:38,301 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:38,348 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.13104042
2015-10-17 23:11:38,442 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:39,317 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.6237083
2015-10-17 23:11:39,317 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:39,379 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.68354744
2015-10-17 23:11:39,426 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.667
2015-10-17 23:11:39,583 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.45563135
2015-10-17 23:11:40,395 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:41,036 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.69383466
2015-10-17 23:11:41,317 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.13103712
2015-10-17 23:11:41,426 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:41,458 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:41,880 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.45725518
2015-10-17 23:11:42,083 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.23923388
2015-10-17 23:11:42,286 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.13104042
2015-10-17 23:11:42,380 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.6373624
2015-10-17 23:11:42,473 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:42,739 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.6984287
2015-10-17 23:11:42,911 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.6780055
2015-10-17 23:11:43,130 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.49405998
2015-10-17 23:11:43,505 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:44,427 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.23924637
2015-10-17 23:11:44,427 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.7083549
2015-10-17 23:11:44,505 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:44,536 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:45,348 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.45725518
2015-10-17 23:11:45,411 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.65201545
2015-10-17 23:11:45,567 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:45,817 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.13104042
2015-10-17 23:11:46,177 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.7126011
2015-10-17 23:11:46,380 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.6907419
2015-10-17 23:11:47,333 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.53965354
2015-10-17 23:11:47,489 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.23924637
2015-10-17 23:11:47,489 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:47,552 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:47,833 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.7232062
2015-10-17 23:11:48,349 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.65201545
2015-10-17 23:11:48,474 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.667
2015-10-17 23:11:48,520 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:49,474 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.72499573
2015-10-17 23:11:49,567 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:49,567 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.14948337
2015-10-17 23:11:49,755 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.23923388
2015-10-17 23:11:49,786 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.7039878
2015-10-17 23:11:50,536 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.23924637
2015-10-17 23:11:50,599 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:50,599 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:51,130 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.56380075
2015-10-17 23:11:51,270 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.73596066
2015-10-17 23:11:51,552 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.667
2015-10-17 23:11:51,599 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:52,630 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:52,708 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.17456008
2015-10-17 23:11:52,896 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.23923388
2015-10-17 23:11:52,896 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.7367493
2015-10-17 23:11:53,255 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.7153119
2015-10-17 23:11:53,614 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:53,614 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.3211976
2015-10-17 23:11:53,630 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:54,599 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.6675781
2015-10-17 23:11:54,646 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:54,692 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.74795806
2015-10-17 23:11:54,786 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.56380075
2015-10-17 23:11:55,692 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:55,989 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.20419972
2015-10-17 23:11:56,099 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.25163442
2015-10-17 23:11:56,489 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.75030404
2015-10-17 23:11:56,630 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:56,661 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.34743145
2015-10-17 23:11:56,724 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:56,786 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.7272725
2015-10-17 23:11:57,630 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.7171705
2015-10-17 23:11:57,724 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:58,005 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.7604858
2015-10-17 23:11:58,068 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.48433182
2015-10-17 23:11:58,489 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.56380075
2015-10-17 23:11:58,755 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:59,646 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:11:59,677 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.22862642
2015-10-17 23:11:59,693 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.34743145
2015-10-17 23:11:59,786 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:11:59,818 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.3207926
2015-10-17 23:11:59,911 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.762729
2015-10-17 23:12:00,146 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.7389957
2015-10-17 23:12:00,677 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.7616831
2015-10-17 23:12:00,802 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:01,521 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.7745522
2015-10-17 23:12:01,833 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:01,833 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.5090419
2015-10-17 23:12:02,380 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.56380075
2015-10-17 23:12:02,677 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:12:02,724 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.34743145
2015-10-17 23:12:02,865 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:03,224 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.7745118
2015-10-17 23:12:03,286 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.23924798
2015-10-17 23:12:03,443 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.34743717
2015-10-17 23:12:03,599 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.7508268
2015-10-17 23:12:03,693 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.8066164
2015-10-17 23:12:03,880 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:04,911 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:04,958 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.7868615
2015-10-17 23:12:05,349 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.53574777
2015-10-17 23:12:05,693 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:12:05,771 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.4556257
2015-10-17 23:12:05,943 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:06,083 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.56380075
2015-10-17 23:12:06,583 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.7860711
2015-10-17 23:12:06,724 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.8539394
2015-10-17 23:12:06,818 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.23924798
2015-10-17 23:12:06,958 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:07,037 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.76172525
2015-10-17 23:12:07,115 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.34743717
2015-10-17 23:12:07,990 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:08,333 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.79998755
2015-10-17 23:12:08,708 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:12:08,818 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.4556257
2015-10-17 23:12:09,021 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:09,255 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.5638263
2015-10-17 23:12:09,740 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.90741897
2015-10-17 23:12:09,865 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.56380075
2015-10-17 23:12:10,037 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:10,146 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.79903233
2015-10-17 23:12:10,474 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.7736286
2015-10-17 23:12:10,584 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.23924798
2015-10-17 23:12:10,599 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.34743717
2015-10-17 23:12:11,052 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:11,724 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.8136414
2015-10-17 23:12:11,724 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:12:11,865 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.4556257
2015-10-17 23:12:12,084 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:12,771 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 0.9640255
2015-10-17 23:12:12,834 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.5638263
2015-10-17 23:12:13,099 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:13,490 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.8124459
2015-10-17 23:12:13,552 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.56380075
2015-10-17 23:12:13,818 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.7857218
2015-10-17 23:12:14,115 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:14,115 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.34743717
2015-10-17 23:12:14,115 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.23924798
2015-10-17 23:12:14,740 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:12:14,927 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.56380385
2015-10-17 23:12:15,006 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.8267953
2015-10-17 23:12:15,131 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:15,818 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 1.0
2015-10-17 23:12:16,146 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:16,443 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.5638263
2015-10-17 23:12:16,521 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445094324383_0003_m_000006_1 because it is running on unusable node:MININT-75DGDAM1.fareast.corp.microsoft.com:58497
2015-10-17 23:12:16,521 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445094324383_0003_m_000004_1 because it is running on unusable node:MININT-75DGDAM1.fareast.corp.microsoft.com:58497
2015-10-17 23:12:16,521 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000016
2015-10-17 23:12:16,521 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 23:12:16,521 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000015
2015-10-17 23:12:16,521 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 23:12:16,521 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:16 ContRel:1 HostLocal:9 RackLocal:5
2015-10-17 23:12:16,521 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000004_1: Container released on a *lost* node
2015-10-17 23:12:16,521 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000015 taskAttempt attempt_1445094324383_0003_m_000006_1
2015-10-17 23:12:16,521 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000006_1: Container released on a *lost* node
2015-10-17 23:12:16,521 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000016 taskAttempt attempt_1445094324383_0003_m_000004_1
2015-10-17 23:12:16,521 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000006_1
2015-10-17 23:12:16,521 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000004_1
2015-10-17 23:12:16,521 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:58497
2015-10-17 23:12:16,521 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:58497
2015-10-17 23:12:16,912 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.82723063
2015-10-17 23:12:17,177 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:17,177 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.7984376
2015-10-17 23:12:17,224 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.56380075
2015-10-17 23:12:17,756 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:12:17,974 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.56380385
2015-10-17 23:12:18,021 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_1 is : 0.34743717
2015-10-17 23:12:18,037 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.23924798
2015-10-17 23:12:18,193 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:18,349 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.8406404
2015-10-17 23:12:18,662 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 23:12:18,756 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 23:12:18,865 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 1.0
2015-10-17 23:12:18,881 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445094324383_0003_m_000006_1
2015-10-17 23:12:18,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 23:12:18,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:12:18,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:12:18,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:12:18,943 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 23:12:19,021 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 23:12:19,068 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445094324383_0003_m_000004_1
2015-10-17 23:12:19,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 23:12:19,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:12:19,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:12:19,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:12:19,209 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 23:12:19,396 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_1 is : 1.0
2015-10-17 23:12:19,396 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0003_m_000000_1
2015-10-17 23:12:19,396 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000000_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:12:19,396 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000014 taskAttempt attempt_1445094324383_0003_m_000000_1
2015-10-17 23:12:19,396 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000000_1
2015-10-17 23:12:19,396 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:12:19,474 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000000_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:12:19,474 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0003_m_000000_1
2015-10-17 23:12:19,474 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445094324383_0003_m_000000_0
2015-10-17 23:12:19,474 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:12:19,474 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 23:12:19,474 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000000_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 23:12:19,474 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000009 taskAttempt attempt_1445094324383_0003_m_000000_0
2015-10-17 23:12:19,474 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000000_0
2015-10-17 23:12:19,474 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-17 23:12:19,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:1 HostLocal:9 RackLocal:5
2015-10-17 23:12:19,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:12:19,959 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.5638263
2015-10-17 23:12:20,131 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.8409474
2015-10-17 23:12:20,146 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000000_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 23:12:20,224 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 23:12:20,240 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 23:12:20,256 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445094324383_0003_m_000000_0
2015-10-17 23:12:20,256 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000000_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 23:12:20,474 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.8113554
2015-10-17 23:12:20,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000014
2015-10-17 23:12:20,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000000_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:12:20,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:1 HostLocal:9 RackLocal:5
2015-10-17 23:12:20,771 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.16666667
2015-10-17 23:12:21,490 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000000_0 is : 0.56380075
2015-10-17 23:12:21,506 WARN [IPC Server handler 29 on 58622] org.apache.hadoop.ipc.Server: IPC Server handler 29 on 58622, call statusUpdate(attempt_1445094324383_0003_m_000000_0, org.apache.hadoop.mapred.MapTaskStatus@cdcdbf7), rpc version=2, client version=19, methodsFingerPrint=937413979 from *************:52490 Call#68 Retry#0: output error
2015-10-17 23:12:21,506 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.ipc.Server: IPC Server handler 29 on 58622 caught an exception
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:487)
	at org.apache.hadoop.ipc.Server.channelWrite(Server.java:2573)
	at org.apache.hadoop.ipc.Server.access$1900(Server.java:135)
	at org.apache.hadoop.ipc.Server$Responder.processResponse(Server.java:977)
	at org.apache.hadoop.ipc.Server$Responder.doRespond(Server.java:1042)
	at org.apache.hadoop.ipc.Server$Handler.run(Server.java:2094)
2015-10-17 23:12:21,506 INFO [Socket Reader #1 for port 58622] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 58622: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 23:12:21,506 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:21,506 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.56380385
2015-10-17 23:12:21,506 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_1 is : 0.23924798
2015-10-17 23:12:21,662 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.8543829
2015-10-17 23:12:22,193 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.56380385
2015-10-17 23:12:22,537 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:22,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000009
2015-10-17 23:12:22,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:1 HostLocal:9 RackLocal:5
2015-10-17 23:12:22,787 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:12:23,521 INFO [Socket Reader #1 for port 58622] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 58622: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 23:12:23,568 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:23,678 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.5638263
2015-10-17 23:12:23,678 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.8556482
2015-10-17 23:12:23,787 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.20000002
2015-10-17 23:12:23,990 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.82436377
2015-10-17 23:12:24,553 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.667
2015-10-17 23:12:24,600 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:25,021 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.8671313
2015-10-17 23:12:25,662 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:26,693 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:26,803 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.20000002
2015-10-17 23:12:27,068 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.86860967
2015-10-17 23:12:27,412 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.5638263
2015-10-17 23:12:27,428 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.83588517
2015-10-17 23:12:27,647 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.667
2015-10-17 23:12:27,709 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:28,537 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.8803655
2015-10-17 23:12:28,725 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:29,772 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:29,834 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.20000002
2015-10-17 23:12:30,490 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.8812511
2015-10-17 23:12:30,740 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.667
2015-10-17 23:12:30,850 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:31,022 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.84702754
2015-10-17 23:12:31,178 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.5638263
2015-10-17 23:12:31,881 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:32,069 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.8925684
2015-10-17 23:12:32,850 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.20000002
2015-10-17 23:12:32,897 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:33,787 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.7014889
2015-10-17 23:12:33,881 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.89351815
2015-10-17 23:12:33,881 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:34,397 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.85737723
2015-10-17 23:12:34,631 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.5638263
2015-10-17 23:12:34,897 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:35,584 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.9051
2015-10-17 23:12:35,850 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.20000002
2015-10-17 23:12:35,881 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:37,022 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.7416102
2015-10-17 23:12:37,022 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:37,272 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.90502954
2015-10-17 23:12:37,850 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.86886585
2015-10-17 23:12:38,256 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:38,553 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.5638263
2015-10-17 23:12:38,881 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.20000002
2015-10-17 23:12:39,084 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.9172293
2015-10-17 23:12:39,303 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:40,100 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.7848879
2015-10-17 23:12:40,319 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:40,616 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.91637385
2015-10-17 23:12:41,241 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.88011515
2015-10-17 23:12:41,350 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:41,913 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.20000002
2015-10-17 23:12:42,366 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:42,459 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.93000674
2015-10-17 23:12:42,553 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.5638263
2015-10-17 23:12:43,147 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.8262859
2015-10-17 23:12:43,413 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:44,022 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.92856246
2015-10-17 23:12:44,397 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:44,444 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:12:44,444 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:12:44,444 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000018 to attempt_1445094324383_0003_m_000006_2
2015-10-17 23:12:44,444 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:9 RackLocal:6
2015-10-17 23:12:44,444 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:12:44,444 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:12:44,444 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000018 taskAttempt attempt_1445094324383_0003_m_000006_2
2015-10-17 23:12:44,444 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000006_2
2015-10-17 23:12:44,444 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:12:44,553 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000006_2 : 13562
2015-10-17 23:12:44,553 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000006_2] using containerId: [container_1445094324383_0003_01_000018 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 23:12:44,553 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:12:44,553 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000006
2015-10-17 23:12:44,616 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.89145845
2015-10-17 23:12:44,975 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.20000002
2015-10-17 23:12:45,428 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:45,460 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:12:45,881 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.9428972
2015-10-17 23:12:46,178 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.8669802
2015-10-17 23:12:46,413 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:46,538 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:12:46,569 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000018 asked for a task
2015-10-17 23:12:46,569 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000018 given task: attempt_1445094324383_0003_m_000006_2
2015-10-17 23:12:46,866 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.64485335
2015-10-17 23:12:47,428 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.9410827
2015-10-17 23:12:47,428 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:47,960 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.9022788
2015-10-17 23:12:47,975 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.20000002
2015-10-17 23:12:48,366 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.64485335
2015-10-17 23:12:48,413 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:49,194 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.955021
2015-10-17 23:12:49,210 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.90763617
2015-10-17 23:12:49,428 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:50,413 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:50,710 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.667
2015-10-17 23:12:50,788 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.95318127
2015-10-17 23:12:50,991 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.20000002
2015-10-17 23:12:51,413 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:51,460 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.91328186
2015-10-17 23:12:52,288 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.9452798
2015-10-17 23:12:52,444 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:52,585 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.9671984
2015-10-17 23:12:53,491 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:53,616 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.13101135
2015-10-17 23:12:54,007 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.20000002
2015-10-17 23:12:54,147 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.9650296
2015-10-17 23:12:54,272 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.667
2015-10-17 23:12:54,475 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:55,038 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.92466724
2015-10-17 23:12:55,366 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.964075
2015-10-17 23:12:55,507 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:55,991 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.9793716
2015-10-17 23:12:56,522 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:56,632 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.13101135
2015-10-17 23:12:56,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:12:56,866 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:12:56,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0003_01_000019 to attempt_1445094324383_0003_m_000004_2
2015-10-17 23:12:56,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:18 ContRel:1 HostLocal:9 RackLocal:7
2015-10-17 23:12:56,866 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:12:56,866 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:12:56,866 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0003_01_000019 taskAttempt attempt_1445094324383_0003_m_000004_2
2015-10-17 23:12:56,866 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0003_m_000004_2
2015-10-17 23:12:56,866 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:12:57,007 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0003_m_000004_2 : 13562
2015-10-17 23:12:57,007 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0003_m_000004_2] using containerId: [container_1445094324383_0003_01_000019 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 23:12:57,007 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:12:57,007 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0003_m_000004
2015-10-17 23:12:57,022 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.20000002
2015-10-17 23:12:57,522 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:57,538 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.9760743
2015-10-17 23:12:57,882 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.667
2015-10-17 23:12:57,897 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:12:58,429 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 0.99201405
2015-10-17 23:12:58,429 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.9354467
2015-10-17 23:12:58,522 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:59,226 INFO [Socket Reader #1 for port 58622] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0003 (auth:SIMPLE)
2015-10-17 23:12:59,288 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0003_m_000019 asked for a task
2015-10-17 23:12:59,288 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0003_m_000019 given task: attempt_1445094324383_0003_m_000004_2
2015-10-17 23:12:59,413 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 0.9915431
2015-10-17 23:12:59,554 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:12:59,632 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.13101135
2015-10-17 23:12:59,976 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_1 is : 1.0
2015-10-17 23:12:59,991 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0003_m_000007_1
2015-10-17 23:12:59,991 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000007_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:12:59,991 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000017 taskAttempt attempt_1445094324383_0003_m_000007_1
2015-10-17 23:12:59,991 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000007_1
2015-10-17 23:12:59,991 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:13:00,101 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.20000002
2015-10-17 23:13:00,116 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000007_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:13:00,116 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0003_m_000007_1
2015-10-17 23:13:00,116 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445094324383_0003_m_000007_0
2015-10-17 23:13:00,116 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:13:00,116 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 23:13:00,116 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000007_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 23:13:00,116 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000008 taskAttempt attempt_1445094324383_0003_m_000007_0
2015-10-17 23:13:00,116 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000007_0
2015-10-17 23:13:00,116 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 23:13:00,585 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 23:13:00,773 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000007_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 23:13:00,913 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 23:13:01,023 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:18 ContRel:1 HostLocal:9 RackLocal:7
2015-10-17 23:13:01,023 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445094324383_0003_m_000007_0
2015-10-17 23:13:01,023 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000007_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 23:13:01,054 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000007_0 is : 0.98833257
2015-10-17 23:13:01,257 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.667
2015-10-17 23:13:01,569 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:13:01,663 INFO [Socket Reader #1 for port 58622] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 58622: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 23:13:01,726 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.9456025
2015-10-17 23:13:01,913 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000002_0 is : 1.0
2015-10-17 23:13:01,913 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0003_m_000002_0
2015-10-17 23:13:01,913 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:13:01,913 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000006 taskAttempt attempt_1445094324383_0003_m_000002_0
2015-10-17 23:13:01,913 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000002_0
2015-10-17 23:13:01,913 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 23:13:02,054 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000008
2015-10-17 23:13:02,054 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000017
2015-10-17 23:13:02,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:13:02,054 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:18 ContRel:1 HostLocal:9 RackLocal:7
2015-10-17 23:13:02,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000007_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:13:02,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:13:02,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0003_m_000002_0
2015-10-17 23:13:02,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:13:02,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 23:13:02,585 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:13:03,023 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.19052154
2015-10-17 23:13:03,054 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:18 ContRel:1 HostLocal:9 RackLocal:7
2015-10-17 23:13:03,132 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.20000002
2015-10-17 23:13:03,601 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:04,616 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:04,835 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.667
2015-10-17 23:13:05,226 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.9576644
2015-10-17 23:13:05,241 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000006
2015-10-17 23:13:05,241 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:18 ContRel:1 HostLocal:9 RackLocal:7
2015-10-17 23:13:05,241 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:13:05,648 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:06,085 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.23923388
2015-10-17 23:13:06,163 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_2 is : 0.13104042
2015-10-17 23:13:06,210 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:06,679 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:07,788 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:08,663 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.9686707
2015-10-17 23:13:08,679 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.667
2015-10-17 23:13:08,976 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:09,242 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.23923388
2015-10-17 23:13:09,335 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_2 is : 0.13104042
2015-10-17 23:13:09,382 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:10,195 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:11,351 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:12,101 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.667
2015-10-17 23:13:12,163 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.9793695
2015-10-17 23:13:12,320 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.23923388
2015-10-17 23:13:12,429 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_2 is : 0.13104042
2015-10-17 23:13:12,507 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:12,507 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:13,570 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:14,601 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:15,429 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.23923388
2015-10-17 23:13:15,539 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_2 is : 0.13104042
2015-10-17 23:13:15,539 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:15,617 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.667
2015-10-17 23:13:15,664 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.9874966
2015-10-17 23:13:15,695 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:16,742 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:17,773 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:18,539 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.34743717
2015-10-17 23:13:18,648 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:18,648 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_2 is : 0.23924798
2015-10-17 23:13:18,820 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:19,429 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.667
2015-10-17 23:13:19,695 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 0.9975674
2015-10-17 23:13:19,867 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:21,242 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:21,648 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.34743717
2015-10-17 23:13:21,726 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:21,726 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_2 is : 0.23924798
2015-10-17 23:13:21,804 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000004_0 is : 1.0
2015-10-17 23:13:21,804 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0003_m_000004_0
2015-10-17 23:13:21,804 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:13:21,804 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000007 taskAttempt attempt_1445094324383_0003_m_000004_0
2015-10-17 23:13:21,804 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000004_0
2015-10-17 23:13:21,804 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 23:13:22,133 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:13:22,133 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0003_m_000004_0
2015-10-17 23:13:22,133 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445094324383_0003_m_000004_2
2015-10-17 23:13:22,133 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:13:22,133 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 23:13:22,133 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_2 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 23:13:22,133 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000019 taskAttempt attempt_1445094324383_0003_m_000004_2
2015-10-17 23:13:22,133 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000004_2
2015-10-17 23:13:22,133 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:13:22,273 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:22,539 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:18 ContRel:1 HostLocal:9 RackLocal:7
2015-10-17 23:13:22,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_2 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 23:13:22,570 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 23:13:22,617 INFO [Socket Reader #1 for port 58622] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 58622: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 23:13:22,617 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.667
2015-10-17 23:13:22,867 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445094324383_0003_m_000004_2
2015-10-17 23:13:22,867 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000004_2 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 23:13:23,304 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:23,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000007
2015-10-17 23:13:23,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000019
2015-10-17 23:13:23,648 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:13:23,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:18 ContRel:1 HostLocal:9 RackLocal:7
2015-10-17 23:13:23,648 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000004_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:13:24,320 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:24,726 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.34743717
2015-10-17 23:13:24,758 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:25,367 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:25,789 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.68810993
2015-10-17 23:13:26,414 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:27,430 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:27,742 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.34743717
2015-10-17 23:13:27,789 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:28,476 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:29,086 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.71096694
2015-10-17 23:13:29,523 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:30,555 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:30,820 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.4029097
2015-10-17 23:13:30,867 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:31,617 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:32,414 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.73236275
2015-10-17 23:13:32,680 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:33,758 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:33,961 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.455643
2015-10-17 23:13:34,008 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:34,914 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:35,758 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.7577027
2015-10-17 23:13:36,367 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:37,070 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:37,070 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.455643
2015-10-17 23:13:37,430 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:38,508 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:39,086 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.7834565
2015-10-17 23:13:39,586 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:40,102 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.455643
2015-10-17 23:13:40,164 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:40,633 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:41,743 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:42,805 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:43,180 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.81357056
2015-10-17 23:13:43,196 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.49858668
2015-10-17 23:13:43,274 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:43,899 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:44,946 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:46,008 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:46,258 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.5638263
2015-10-17 23:13:46,321 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:46,508 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.83819616
2015-10-17 23:13:47,102 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:48,149 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:49,211 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:49,290 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.5638263
2015-10-17 23:13:49,368 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:50,024 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.855608
2015-10-17 23:13:50,305 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:51,352 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:52,383 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.5638263
2015-10-17 23:13:52,415 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:52,462 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:53,430 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.875729
2015-10-17 23:13:53,477 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:54,446 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.5638263
2015-10-17 23:13:54,524 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:55,509 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.667
2015-10-17 23:13:55,571 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:55,571 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:56,665 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:56,837 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.8956557
2015-10-17 23:13:57,712 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:58,571 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.667
2015-10-17 23:13:58,618 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:13:58,774 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:13:59,821 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:00,212 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.9155349
2015-10-17 23:14:00,852 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:01,696 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.667
2015-10-17 23:14:01,743 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:14:01,915 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:02,946 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:03,509 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.9378581
2015-10-17 23:14:03,993 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:04,806 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:14:04,806 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.6892808
2015-10-17 23:14:05,056 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:06,103 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:06,681 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.96612644
2015-10-17 23:14:07,181 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:07,915 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.7478306
2015-10-17 23:14:07,915 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.23333333
2015-10-17 23:14:08,212 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:09,259 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:10,259 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 0.9822587
2015-10-17 23:14:10,290 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:11,040 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.80699646
2015-10-17 23:14:11,040 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.26666668
2015-10-17 23:14:11,525 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:12,759 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:13,431 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_0 is : 1.0
2015-10-17 23:14:13,618 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0003_m_000006_0
2015-10-17 23:14:13,634 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:14:13,634 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000011 taskAttempt attempt_1445094324383_0003_m_000006_0
2015-10-17 23:14:13,634 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000006_0
2015-10-17 23:14:13,634 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-17 23:14:13,853 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:14,103 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.26666668
2015-10-17 23:14:14,103 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_m_000006_2 is : 0.8649055
2015-10-17 23:14:14,353 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:14:14,353 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0003_m_000006_0
2015-10-17 23:14:14,353 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445094324383_0003_m_000006_2
2015-10-17 23:14:14,353 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:14:14,353 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 23:14:14,353 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_2 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 23:14:14,353 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000018 taskAttempt attempt_1445094324383_0003_m_000006_2
2015-10-17 23:14:14,353 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_m_000006_2
2015-10-17 23:14:14,556 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:14:14,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:18 ContRel:1 HostLocal:9 RackLocal:7
2015-10-17 23:14:14,962 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 11 maxEvents 10000
2015-10-17 23:14:15,134 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_2 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 23:14:15,134 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 23:14:15,197 INFO [Socket Reader #1 for port 58622] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 58622: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 23:14:15,275 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445094324383_0003_m_000006_2
2015-10-17 23:14:15,275 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_m_000006_2 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 23:14:15,994 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000011
2015-10-17 23:14:15,994 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:18 ContRel:1 HostLocal:9 RackLocal:7
2015-10-17 23:14:15,994 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:14:16,134 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:17,103 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000018
2015-10-17 23:14:17,103 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:18 ContRel:1 HostLocal:9 RackLocal:7
2015-10-17 23:14:17,103 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_m_000006_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:14:17,275 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:17,322 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.26666668
2015-10-17 23:14:18,353 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:19,400 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:20,431 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.26666668
2015-10-17 23:14:20,541 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:21,666 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:22,806 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:23,556 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.26666668
2015-10-17 23:14:23,822 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:24,931 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:26,056 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:26,666 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.26666668
2015-10-17 23:14:27,166 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:28,353 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:29,400 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:29,822 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.26666668
2015-10-17 23:14:30,510 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:31,697 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:32,838 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:32,916 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.26666668
2015-10-17 23:14:33,900 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:35,041 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:35,994 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.26666668
2015-10-17 23:14:36,103 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:37,166 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:38,244 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:39,150 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.26666668
2015-10-17 23:14:39,338 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:40,400 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:41,432 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:42,307 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.26666668
2015-10-17 23:14:42,541 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:43,588 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:44,666 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:45,401 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.26666668
2015-10-17 23:14:45,729 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:46,776 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:47,854 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:48,448 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.3
2015-10-17 23:14:48,885 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:49,979 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0003_r_000000_0. startIndex 12 maxEvents 10000
2015-10-17 23:14:50,401 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.3
2015-10-17 23:14:50,432 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.3
2015-10-17 23:14:51,495 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.66740483
2015-10-17 23:14:54,557 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.6700877
2015-10-17 23:14:57,589 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.67234546
2015-10-17 23:15:00,636 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.6736037
2015-10-17 23:15:03,948 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.675091
2015-10-17 23:15:06,979 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.67647207
2015-10-17 23:15:09,995 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.6773308
2015-10-17 23:15:13,042 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.67881125
2015-10-17 23:15:16,058 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.6814585
2015-10-17 23:15:19,089 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.68311906
2015-10-17 23:15:22,839 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.6845539
2015-10-17 23:15:25,871 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.6865251
2015-10-17 23:15:29,933 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.6880864
2015-10-17 23:15:32,980 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.6903259
2015-10-17 23:15:35,996 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.6911655
2015-10-17 23:15:39,027 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.6945218
2015-10-17 23:15:42,043 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.69836193
2015-10-17 23:15:45,059 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7023526
2015-10-17 23:15:48,074 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.70721245
2015-10-17 23:15:51,090 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.71072865
2015-10-17 23:15:54,106 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.71495414
2015-10-17 23:15:57,106 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.718319
2015-10-17 23:16:00,122 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.72127694
2015-10-17 23:16:03,122 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7246235
2015-10-17 23:16:06,122 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.728722
2015-10-17 23:16:09,153 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.73399824
2015-10-17 23:16:12,169 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7384332
2015-10-17 23:16:15,185 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7434568
2015-10-17 23:16:18,185 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.74843943
2015-10-17 23:16:21,247 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.75323576
2015-10-17 23:16:24,279 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7582418
2015-10-17 23:16:27,294 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.76317376
2015-10-17 23:16:30,294 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.766015
2015-10-17 23:16:33,326 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7670867
2015-10-17 23:16:36,326 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.76795363
2015-10-17 23:16:39,342 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7690732
2015-10-17 23:16:42,342 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7700673
2015-10-17 23:16:45,357 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7709501
2015-10-17 23:16:48,373 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7716927
2015-10-17 23:16:51,389 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7725286
2015-10-17 23:16:54,404 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.77306396
2015-10-17 23:16:57,420 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7737889
2015-10-17 23:17:00,436 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.77430975
2015-10-17 23:17:03,467 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7751302
2015-10-17 23:17:06,483 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.77585506
2015-10-17 23:17:09,483 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.77640694
2015-10-17 23:17:12,530 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7768168
2015-10-17 23:17:15,546 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.77747834
2015-10-17 23:17:18,561 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.77813405
2015-10-17 23:17:21,577 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7787564
2015-10-17 23:17:24,593 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7794989
2015-10-17 23:17:27,609 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7805714
2015-10-17 23:17:30,624 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.78121305
2015-10-17 23:17:33,640 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7818439
2015-10-17 23:17:36,656 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7825577
2015-10-17 23:17:39,656 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.78328323
2015-10-17 23:17:42,656 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7839145
2015-10-17 23:17:45,672 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7845442
2015-10-17 23:17:48,687 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7851291
2015-10-17 23:17:51,703 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.78574497
2015-10-17 23:17:54,719 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.78643966
2015-10-17 23:17:57,735 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7874497
2015-10-17 23:18:00,750 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7884589
2015-10-17 23:18:03,766 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7895784
2015-10-17 23:18:06,782 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7908602
2015-10-17 23:18:09,797 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.79211223
2015-10-17 23:18:12,829 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7933388
2015-10-17 23:18:15,844 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7945765
2015-10-17 23:18:18,860 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7957282
2015-10-17 23:18:21,876 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7968334
2015-10-17 23:18:24,907 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.797577
2015-10-17 23:18:27,923 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7981235
2015-10-17 23:18:30,939 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.7992171
2015-10-17 23:18:33,954 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.80035216
2015-10-17 23:18:37,001 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.80152667
2015-10-17 23:18:40,017 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8024496
2015-10-17 23:18:43,064 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8036646
2015-10-17 23:18:46,095 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.80489004
2015-10-17 23:18:49,127 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8062517
2015-10-17 23:18:52,127 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8075135
2015-10-17 23:18:55,142 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8085568
2015-10-17 23:18:58,158 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.80935764
2015-10-17 23:19:01,174 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8101475
2015-10-17 23:19:04,190 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8107776
2015-10-17 23:19:07,205 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.81159806
2015-10-17 23:19:10,221 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8123543
2015-10-17 23:19:13,237 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.81314254
2015-10-17 23:19:16,252 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.81394756
2015-10-17 23:19:19,268 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8150817
2015-10-17 23:19:22,284 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.81632984
2015-10-17 23:19:25,300 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8175286
2015-10-17 23:19:28,315 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8186798
2015-10-17 23:19:31,331 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8196715
2015-10-17 23:19:34,347 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8203685
2015-10-17 23:19:37,362 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.82109356
2015-10-17 23:19:40,394 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8220715
2015-10-17 23:19:43,410 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8230194
2015-10-17 23:19:46,441 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8236966
2015-10-17 23:19:49,457 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8243272
2015-10-17 23:19:52,519 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8249741
2015-10-17 23:19:55,551 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.825494
2015-10-17 23:19:58,582 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.82599735
2015-10-17 23:20:02,535 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.82678896
2015-10-17 23:20:05,551 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8279426
2015-10-17 23:20:08,551 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8287759
2015-10-17 23:20:11,567 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8295334
2015-10-17 23:20:14,582 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8301136
2015-10-17 23:20:17,598 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8307483
2015-10-17 23:20:20,598 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.83166397
2015-10-17 23:20:23,614 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8325203
2015-10-17 23:20:26,630 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8331611
2015-10-17 23:20:29,645 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8340608
2015-10-17 23:20:32,661 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.83507425
2015-10-17 23:20:35,677 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.83596927
2015-10-17 23:20:38,692 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.83677024
2015-10-17 23:20:41,708 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8375465
2015-10-17 23:20:44,724 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.83833545
2015-10-17 23:20:47,755 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.83920246
2015-10-17 23:20:50,787 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.83999074
2015-10-17 23:20:53,787 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8407992
2015-10-17 23:20:56,818 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8414577
2015-10-17 23:20:59,818 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8421989
2015-10-17 23:21:02,818 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.84287745
2015-10-17 23:21:05,834 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.843366
2015-10-17 23:21:08,834 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8439971
2015-10-17 23:21:11,850 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.84497416
2015-10-17 23:21:14,850 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.845968
2015-10-17 23:21:17,881 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.84699464
2015-10-17 23:21:21,037 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8479966
2015-10-17 23:21:24,053 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.84890306
2015-10-17 23:21:27,069 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.84985936
2015-10-17 23:21:30,069 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8509694
2015-10-17 23:21:33,100 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.85212094
2015-10-17 23:21:36,100 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8532721
2015-10-17 23:21:39,132 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8544253
2015-10-17 23:21:42,147 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8552592
2015-10-17 23:21:45,148 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8560641
2015-10-17 23:21:48,148 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8572312
2015-10-17 23:21:51,163 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.858336
2015-10-17 23:21:54,163 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8591237
2015-10-17 23:21:57,179 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8600553
2015-10-17 23:22:00,195 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8607969
2015-10-17 23:22:03,195 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8616513
2015-10-17 23:22:06,211 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.86232674
2015-10-17 23:22:09,226 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8633056
2015-10-17 23:22:12,242 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8644502
2015-10-17 23:22:15,320 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8654827
2015-10-17 23:22:18,383 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.86645466
2015-10-17 23:22:21,461 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8676428
2015-10-17 23:22:24,524 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.868762
2015-10-17 23:22:27,586 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.86997664
2015-10-17 23:22:30,649 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.871002
2015-10-17 23:22:33,727 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.87214375
2015-10-17 23:22:36,790 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8731314
2015-10-17 23:22:39,868 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.87406963
2015-10-17 23:22:42,931 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.87533784
2015-10-17 23:22:45,993 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8766941
2015-10-17 23:22:49,056 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.87786525
2015-10-17 23:22:52,134 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8788263
2015-10-17 23:22:55,197 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8795678
2015-10-17 23:22:58,290 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.88045126
2015-10-17 23:23:01,337 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8814448
2015-10-17 23:23:04,416 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.88271177
2015-10-17 23:23:07,478 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8839532
2015-10-17 23:23:10,541 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8853879
2015-10-17 23:23:13,650 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8866506
2015-10-17 23:23:16,697 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8876915
2015-10-17 23:23:19,760 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.88871694
2015-10-17 23:23:22,822 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8899012
2015-10-17 23:23:25,901 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8909261
2015-10-17 23:23:28,979 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.89214015
2015-10-17 23:23:32,026 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8934658
2015-10-17 23:23:35,120 INFO [IPC Server handler 17 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8945648
2015-10-17 23:23:38,198 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8958496
2015-10-17 23:23:41,292 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.8970194
2015-10-17 23:23:44,385 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.89826095
2015-10-17 23:23:47,448 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.89956796
2015-10-17 23:23:50,511 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.90081114
2015-10-17 23:23:53,573 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9021892
2015-10-17 23:23:56,636 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9031507
2015-10-17 23:23:59,730 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.90439737
2015-10-17 23:24:02,792 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.90531176
2015-10-17 23:24:05,855 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9060842
2015-10-17 23:24:08,933 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9069684
2015-10-17 23:24:11,980 INFO [IPC Server handler 4 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.908057
2015-10-17 23:24:15,043 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9091145
2015-10-17 23:24:18,121 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.91029716
2015-10-17 23:24:21,199 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.91105413
2015-10-17 23:24:24,262 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.912174
2015-10-17 23:24:27,293 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9131679
2015-10-17 23:24:30,324 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.914461
2015-10-17 23:24:33,403 INFO [IPC Server handler 29 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9156444
2015-10-17 23:24:36,465 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.91689086
2015-10-17 23:24:39,528 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9181069
2015-10-17 23:24:42,590 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9193516
2015-10-17 23:24:45,669 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9205422
2015-10-17 23:24:48,731 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9219216
2015-10-17 23:24:51,794 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9232305
2015-10-17 23:24:55,716 INFO [IPC Server handler 12 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9244933
2015-10-17 23:24:58,747 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9262128
2015-10-17 23:25:01,794 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9271298
2015-10-17 23:25:04,903 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.92839223
2015-10-17 23:25:07,997 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9293002
2015-10-17 23:25:11,060 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9301368
2015-10-17 23:25:14,107 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9310271
2015-10-17 23:25:17,185 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9321231
2015-10-17 23:25:20,232 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9333627
2015-10-17 23:25:23,342 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9345436
2015-10-17 23:25:26,513 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9358071
2015-10-17 23:25:29,576 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9370408
2015-10-17 23:25:32,639 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.938141
2015-10-17 23:25:35,701 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.93926865
2015-10-17 23:25:38,764 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.94039714
2015-10-17 23:25:41,873 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9413911
2015-10-17 23:25:44,967 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9426696
2015-10-17 23:25:48,108 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9433791
2015-10-17 23:25:51,280 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.94405776
2015-10-17 23:25:54,358 INFO [IPC Server handler 5 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9449097
2015-10-17 23:25:57,436 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9459029
2015-10-17 23:26:00,764 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9470544
2015-10-17 23:26:03,812 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9486259
2015-10-17 23:26:06,905 INFO [IPC Server handler 13 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9497031
2015-10-17 23:26:10,015 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.95072913
2015-10-17 23:26:13,077 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9519911
2015-10-17 23:26:16,156 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.953347
2015-10-17 23:26:19,218 INFO [IPC Server handler 9 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9542711
2015-10-17 23:26:22,250 INFO [IPC Server handler 0 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.95484287
2015-10-17 23:26:25,375 INFO [IPC Server handler 19 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.95580614
2015-10-17 23:26:28,406 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9569589
2015-10-17 23:26:31,484 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.958012
2015-10-17 23:26:34,547 INFO [IPC Server handler 22 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9590702
2015-10-17 23:26:37,625 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9601144
2015-10-17 23:26:40,672 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9611305
2015-10-17 23:26:43,766 INFO [IPC Server handler 1 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.96186525
2015-10-17 23:26:46,860 INFO [IPC Server handler 16 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.96246445
2015-10-17 23:26:49,954 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9633474
2015-10-17 23:26:53,016 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9642782
2015-10-17 23:26:56,094 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.965509
2015-10-17 23:26:59,157 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9665968
2015-10-17 23:27:02,219 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9679942
2015-10-17 23:27:05,282 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9691688
2015-10-17 23:27:08,345 INFO [IPC Server handler 24 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9704043
2015-10-17 23:27:11,423 INFO [IPC Server handler 18 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9720405
2015-10-17 23:27:14,485 INFO [IPC Server handler 2 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9742362
2015-10-17 23:27:17,532 INFO [IPC Server handler 10 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9764504
2015-10-17 23:27:20,611 INFO [IPC Server handler 28 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.97816426
2015-10-17 23:27:23,673 INFO [IPC Server handler 27 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.97992563
2015-10-17 23:27:26,751 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9815675
2015-10-17 23:27:29,830 INFO [IPC Server handler 6 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.98322356
2015-10-17 23:27:32,892 INFO [IPC Server handler 3 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.98473823
2015-10-17 23:27:35,986 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9864112
2015-10-17 23:27:39,049 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9879897
2015-10-17 23:27:42,143 INFO [IPC Server handler 7 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.989516
2015-10-17 23:27:45,236 INFO [IPC Server handler 21 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.99101496
2015-10-17 23:27:48,315 INFO [IPC Server handler 26 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9923937
2015-10-17 23:27:51,377 INFO [IPC Server handler 23 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.99373704
2015-10-17 23:27:54,455 INFO [IPC Server handler 25 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 0.9968088
2015-10-17 23:27:57,534 INFO [IPC Server handler 8 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 1.0
2015-10-17 23:27:57,846 INFO [IPC Server handler 11 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445094324383_0003_r_000000_0
2015-10-17 23:27:57,846 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-17 23:27:57,846 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445094324383_0003_r_000000_0 given a go for committing the task output.
2015-10-17 23:27:57,940 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445094324383_0003_r_000000_0
2015-10-17 23:27:57,940 INFO [IPC Server handler 14 on 58622] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445094324383_0003_r_000000_0:true
2015-10-17 23:27:58,034 INFO [IPC Server handler 20 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0003_r_000000_0 is : 1.0
2015-10-17 23:27:58,127 INFO [IPC Server handler 15 on 58622] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0003_r_000000_0
2015-10-17 23:27:58,127 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:27:58,127 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0003_01_000013 taskAttempt attempt_1445094324383_0003_r_000000_0
2015-10-17 23:27:58,127 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0003_r_000000_0
2015-10-17 23:27:58,127 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:27:58,440 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0003_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:27:58,440 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0003_r_000000_0
2015-10-17 23:27:58,440 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0003_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:27:58,456 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 23:27:58,456 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445094324383_0003Job Transitioned from RUNNING to COMMITTING
2015-10-17 23:27:58,456 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-17 23:27:59,221 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:18 ContRel:1 HostLocal:9 RackLocal:7
2015-10-17 23:27:59,409 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-17 23:27:59,409 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445094324383_0003Job Transitioned from COMMITTING to SUCCEEDED
2015-10-17 23:27:59,409 INFO [Thread-138] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-17 23:27:59,409 INFO [Thread-138] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-17 23:27:59,409 INFO [Thread-138] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-17 23:27:59,409 INFO [Thread-138] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-17 23:27:59,409 INFO [Thread-138] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-17 23:27:59,409 INFO [Thread-138] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-17 23:27:59,424 INFO [Thread-138] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-17 23:28:00,143 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445094324383_0003/job_1445094324383_0003_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0003-1445094473124-msrabi-word+count-1445095679409-10-1-SUCCEEDED-default-1445094480842.jhist_tmp
2015-10-17 23:28:00,331 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0003_01_000013
2015-10-17 23:28:00,331 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:18 ContRel:1 HostLocal:9 RackLocal:7
2015-10-17 23:28:00,331 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0003_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:28:01,096 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0003-1445094473124-msrabi-word+count-1445095679409-10-1-SUCCEEDED-default-1445094480842.jhist_tmp
2015-10-17 23:28:01,143 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445094324383_0003/job_1445094324383_0003_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0003_conf.xml_tmp
2015-10-17 23:28:02,049 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0003_conf.xml_tmp
2015-10-17 23:28:02,096 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0003.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0003.summary
2015-10-17 23:28:02,127 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0003_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0003_conf.xml
2015-10-17 23:28:02,159 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0003-1445094473124-msrabi-word+count-1445095679409-10-1-SUCCEEDED-default-1445094480842.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0003-1445094473124-msrabi-word+count-1445095679409-10-1-SUCCEEDED-default-1445094480842.jhist
2015-10-17 23:28:02,174 INFO [Thread-138] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-17 23:28:02,174 INFO [Thread-138] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-17 23:28:02,174 INFO [Thread-138] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://04DN8IQ.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445094324383_0003
2015-10-17 23:28:02,221 INFO [Thread-138] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-17 23:28:03,237 INFO [Thread-138] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:18 ContRel:1 HostLocal:9 RackLocal:7
2015-10-17 23:28:03,237 INFO [Thread-138] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445094324383_0003
2015-10-17 23:28:03,253 INFO [Thread-138] org.apache.hadoop.ipc.Server: Stopping server on 58622
2015-10-17 23:28:03,253 INFO [IPC Server listener on 58622] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 58622
2015-10-17 23:28:03,253 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-17 23:28:03,253 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
