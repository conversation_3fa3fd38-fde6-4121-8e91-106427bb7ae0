import pandas as pd
import numpy as np
import os

def reduce_csv_size(input_path, output_path, 
                    columns_to_keep=None, 
                    remove_duplicates=True, 
                    compress_output=False,      # 👈 No compression
                    random_seed=42):
    # Step 1: Load CSV
    print("Loading CSV...")
    df = pd.read_csv("C:\\Users\\<USER>\\OneDrive\\Desktop\\ONGC\\Dataset\\CSV Data\\train_Copy.csv")

    # Step 2: Randomly keep 70% of the rows
    print("Keeping 70% of rows randomly (removing 30%)...")
    df = df.sample(frac=0.85, random_state=random_seed)

    # Step 3: Keep only specific columns (optional)
    if columns_to_keep is not None:
        df = df[columns_to_keep]

    # Step 4: Remove duplicate rows
    if remove_duplicates:
        df = df.drop_duplicates()

    # Step 5: Downcast numeric types to save space
    for col in df.select_dtypes(include=['int', 'float']).columns:
        df[col] = pd.to_numeric(df[col], downcast='unsigned' if df[col].min() >= 0 else 'integer')

    # Step 6: Save to plain CSV
    print(f"Saving file to {output_path}...")
    df.to_csv(output_path, index=False)

    print("Done. Final file size:", os.path.getsize(output_path), "bytes")

# ✅ Use the function like this:
reduce_csv_size(
    input_path='your_input.csv',
    output_path='reduced_output3.csv',     # 👈 Plain CSV
    remove_duplicates=True,
    compress_output=False                 # 👈 No .gz compression
)