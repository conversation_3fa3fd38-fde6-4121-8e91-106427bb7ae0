2015-10-19 14:41:47,332 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445182159119_0005_000001
2015-10-19 14:41:47,597 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-19 14:41:47,597 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 5 cluster_timestamp: 1445182159119 } attemptId: 1 } keyId: 1694045684)
2015-10-19 14:41:47,754 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-19 14:41:49,113 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-19 14:41:49,207 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-19 14:41:49,254 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-19 14:41:49,254 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-19 14:41:49,254 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-19 14:41:49,254 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-19 14:41:49,254 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-19 14:41:49,269 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-19 14:41:49,269 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-19 14:41:49,269 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-19 14:41:49,332 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 14:41:49,363 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 14:41:49,410 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 14:41:49,426 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-19 14:41:49,488 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-19 14:41:49,801 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 14:41:49,879 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 14:41:49,879 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-19 14:41:49,894 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445182159119_0005 to jobTokenSecretManager
2015-10-19 14:41:50,316 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445182159119_0005 because: not enabled; too many maps; too much input;
2015-10-19 14:41:50,348 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445182159119_0005 = 1313861632. Number of splits = 10
2015-10-19 14:41:50,348 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445182159119_0005 = 1
2015-10-19 14:41:50,348 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0005Job Transitioned from NEW to INITED
2015-10-19 14:41:50,348 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445182159119_0005.
2015-10-19 14:41:50,379 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 14:41:50,394 INFO [Socket Reader #1 for port 47374] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 47374
2015-10-19 14:41:50,441 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-19 14:41:50,441 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 14:41:50,441 INFO [IPC Server listener on 47374] org.apache.hadoop.ipc.Server: IPC Server listener on 47374: starting
2015-10-19 14:41:50,441 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-39.fareast.corp.microsoft.com/**************:47374
2015-10-19 14:41:50,551 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-19 14:41:50,566 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-19 14:41:50,582 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-19 14:41:50,598 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-19 14:41:50,598 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-19 14:41:50,598 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-19 14:41:50,598 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-19 14:41:50,613 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 47381
2015-10-19 14:41:50,613 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-19 14:41:50,660 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_47381_mapreduce____eqgtow\webapp
2015-10-19 14:41:50,941 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:47381
2015-10-19 14:41:50,941 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 47381
2015-10-19 14:41:51,348 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-19 14:41:51,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445182159119_0005
2015-10-19 14:41:51,363 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 14:41:51,363 INFO [Socket Reader #1 for port 47384] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 47384
2015-10-19 14:41:51,363 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 14:41:51,363 INFO [IPC Server listener on 47384] org.apache.hadoop.ipc.Server: IPC Server listener on 47384: starting
2015-10-19 14:41:51,395 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-19 14:41:51,395 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-19 14:41:51,395 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-19 14:41:51,441 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-19 14:41:51,520 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-19 14:41:51,520 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-19 14:41:51,520 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-19 14:41:51,520 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-19 14:41:51,535 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0005Job Transitioned from INITED to SETUP
2015-10-19 14:41:51,535 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-19 14:41:51,551 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0005Job Transitioned from SETUP to RUNNING
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:41:51,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:41:51,598 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-19 14:41:51,598 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-19 14:41:51,629 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445182159119_0005, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0005/job_1445182159119_0005_1.jhist
2015-10-19 14:41:52,520 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 14:41:52,566 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-4> knownNMs=3
2015-10-19 14:41:52,566 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-4>
2015-10-19 14:41:52,566 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:41:53,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 8
2015-10-19 14:41:53,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0005_01_000002 to attempt_1445182159119_0005_m_000000_0
2015-10-19 14:41:53,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0005_01_000003 to attempt_1445182159119_0005_m_000001_0
2015-10-19 14:41:53,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0005_01_000004 to attempt_1445182159119_0005_m_000002_0
2015-10-19 14:41:53,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0005_01_000005 to attempt_1445182159119_0005_m_000003_0
2015-10-19 14:41:53,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0005_01_000006 to attempt_1445182159119_0005_m_000004_0
2015-10-19 14:41:53,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0005_01_000007 to attempt_1445182159119_0005_m_000005_0
2015-10-19 14:41:53,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0005_01_000008 to attempt_1445182159119_0005_m_000006_0
2015-10-19 14:41:53,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0005_01_000009 to attempt_1445182159119_0005_m_000007_0
2015-10-19 14:41:53,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-12>
2015-10-19 14:41:53,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:41:53,613 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:8 RackLocal:0
2015-10-19 14:41:53,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:53,676 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0005/job.jar
2015-10-19 14:41:53,676 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0005/job.xml
2015-10-19 14:41:53,676 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-19 14:41:53,691 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-19 14:41:53,691 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-19 14:41:53,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:41:53,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:53,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:41:53,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:53,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:41:53,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:53,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:41:53,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:53,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:41:53,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:53,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:41:53,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:53,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:41:53,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:53,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:41:53,723 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0005_01_000003 taskAttempt attempt_1445182159119_0005_m_000001_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0005_01_000002 taskAttempt attempt_1445182159119_0005_m_000000_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0005_01_000004 taskAttempt attempt_1445182159119_0005_m_000002_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0005_01_000005 taskAttempt attempt_1445182159119_0005_m_000003_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0005_01_000006 taskAttempt attempt_1445182159119_0005_m_000004_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0005_01_000007 taskAttempt attempt_1445182159119_0005_m_000005_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0005_01_000008 taskAttempt attempt_1445182159119_0005_m_000006_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0005_01_000009 taskAttempt attempt_1445182159119_0005_m_000007_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0005_m_000001_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0005_m_000006_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0005_m_000002_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0005_m_000007_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0005_m_000004_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0005_m_000003_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0005_m_000005_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0005_m_000000_0
2015-10-19 14:41:53,723 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:41:53,738 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:41:53,738 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:41:53,738 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:41:53,738 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:41:53,754 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:41:53,754 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:41:53,754 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:41:53,801 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0005_m_000006_0 : 13562
2015-10-19 14:41:53,801 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0005_m_000005_0 : 13562
2015-10-19 14:41:53,801 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0005_m_000004_0 : 13562
2015-10-19 14:41:53,801 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0005_m_000007_0 : 13562
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0005_m_000004_0] using containerId: [container_1445182159119_0005_01_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0005_m_000006_0] using containerId: [container_1445182159119_0005_01_000008 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0005_m_000007_0] using containerId: [container_1445182159119_0005_01_000009 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0005_m_000005_0] using containerId: [container_1445182159119_0005_01_000007 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0005_m_000004
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0005_m_000006
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0005_m_000007
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0005_m_000005
2015-10-19 14:41:53,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:41:53,816 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0005_m_000003_0 : 13562
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0005_m_000003_0] using containerId: [container_1445182159119_0005_01_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0005_m_000003
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:41:53,816 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0005_m_000002_0 : 13562
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0005_m_000002_0] using containerId: [container_1445182159119_0005_01_000004 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0005_m_000002
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:41:53,816 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0005_m_000000_0 : 13562
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0005_m_000000_0] using containerId: [container_1445182159119_0005_01_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0005_m_000000
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:41:53,816 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0005_m_000001_0 : 13562
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0005_m_000001_0] using containerId: [container_1445182159119_0005_01_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0005_m_000001
2015-10-19 14:41:53,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:41:54,619 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:8192, vCores:-12> knownNMs=3
2015-10-19 14:41:54,619 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-12>
2015-10-19 14:41:54,619 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:41:55,619 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-12>
2015-10-19 14:41:55,619 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:41:56,620 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:41:56,620 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0005_01_000010 to attempt_1445182159119_0005_m_000008_0
2015-10-19 14:41:56,620 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-12>
2015-10-19 14:41:56,620 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:41:56,620 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:9 RackLocal:0
2015-10-19 14:41:56,620 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:56,620 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:41:56,620 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0005_01_000010 taskAttempt attempt_1445182159119_0005_m_000008_0
2015-10-19 14:41:56,620 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0005_m_000008_0
2015-10-19 14:41:56,620 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:41:56,635 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0005_m_000008_0 : 13562
2015-10-19 14:41:56,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0005_m_000008_0] using containerId: [container_1445182159119_0005_01_000010 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:41:56,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:41:56,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0005_m_000008
2015-10-19 14:41:56,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:41:57,088 INFO [Socket Reader #1 for port 47384] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0005 (auth:SIMPLE)
2015-10-19 14:41:57,104 INFO [Socket Reader #1 for port 47384] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0005 (auth:SIMPLE)
2015-10-19 14:41:57,104 INFO [Socket Reader #1 for port 47384] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0005 (auth:SIMPLE)
2015-10-19 14:41:57,120 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0005_m_000006 asked for a task
2015-10-19 14:41:57,120 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0005_m_000004 asked for a task
2015-10-19 14:41:57,120 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0005_m_000006 given task: attempt_1445182159119_0005_m_000004_0
2015-10-19 14:41:57,120 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0005_m_000004 given task: attempt_1445182159119_0005_m_000002_0
2015-10-19 14:41:57,120 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0005_m_000003 asked for a task
2015-10-19 14:41:57,135 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0005_m_000003 given task: attempt_1445182159119_0005_m_000001_0
2015-10-19 14:41:57,135 INFO [Socket Reader #1 for port 47384] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0005 (auth:SIMPLE)
2015-10-19 14:41:57,135 INFO [Socket Reader #1 for port 47384] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0005 (auth:SIMPLE)
2015-10-19 14:41:57,151 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0005_m_000005 asked for a task
2015-10-19 14:41:57,151 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0005_m_000005 given task: attempt_1445182159119_0005_m_000003_0
2015-10-19 14:41:57,151 INFO [Socket Reader #1 for port 47384] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0005 (auth:SIMPLE)
2015-10-19 14:41:57,166 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0005_m_000002 asked for a task
2015-10-19 14:41:57,166 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0005_m_000002 given task: attempt_1445182159119_0005_m_000000_0
2015-10-19 14:41:57,166 INFO [Socket Reader #1 for port 47384] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0005 (auth:SIMPLE)
2015-10-19 14:41:57,166 INFO [Socket Reader #1 for port 47384] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0005 (auth:SIMPLE)
2015-10-19 14:41:57,166 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0005_m_000009 asked for a task
2015-10-19 14:41:57,166 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0005_m_000009 given task: attempt_1445182159119_0005_m_000007_0
2015-10-19 14:41:57,182 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0005_m_000007 asked for a task
2015-10-19 14:41:57,182 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0005_m_000007 given task: attempt_1445182159119_0005_m_000005_0
2015-10-19 14:41:57,182 INFO [IPC Server handler 17 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0005_m_000008 asked for a task
2015-10-19 14:41:57,182 INFO [IPC Server handler 17 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0005_m_000008 given task: attempt_1445182159119_0005_m_000006_0
2015-10-19 14:41:57,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:8192, vCores:-12> knownNMs=3
2015-10-19 14:41:57,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-12>
2015-10-19 14:41:57,623 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:41:58,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-12>
2015-10-19 14:41:58,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:41:59,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:41:59,629 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:59,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0005_01_000011 to attempt_1445182159119_0005_m_000009_0
2015-10-19 14:41:59,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-13>
2015-10-19 14:41:59,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:41:59,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:41:59,629 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:41:59,629 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:41:59,644 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0005_01_000011 taskAttempt attempt_1445182159119_0005_m_000009_0
2015-10-19 14:41:59,644 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0005_m_000009_0
2015-10-19 14:41:59,644 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:41:59,722 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0005_m_000009_0 : 13562
2015-10-19 14:41:59,722 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0005_m_000009_0] using containerId: [container_1445182159119_0005_01_000011 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 14:41:59,722 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:41:59,722 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0005_m_000009
2015-10-19 14:41:59,722 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:42:00,316 INFO [Socket Reader #1 for port 47384] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0005 (auth:SIMPLE)
2015-10-19 14:42:00,347 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0005_m_000010 asked for a task
2015-10-19 14:42:00,347 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0005_m_000010 given task: attempt_1445182159119_0005_m_000008_0
2015-10-19 14:42:00,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-13> knownNMs=3
2015-10-19 14:42:03,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-12>
2015-10-19 14:42:03,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:42:03,879 INFO [Socket Reader #1 for port 47384] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0005 (auth:SIMPLE)
2015-10-19 14:42:03,894 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0005_m_000011 asked for a task
2015-10-19 14:42:03,894 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0005_m_000011 given task: attempt_1445182159119_0005_m_000009_0
2015-10-19 14:42:04,191 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.13101934
2015-10-19 14:42:04,191 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.13102706
2015-10-19 14:42:04,191 INFO [IPC Server handler 17 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.13102192
2015-10-19 14:42:04,207 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.131014
2015-10-19 14:42:04,707 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.13104042
2015-10-19 14:42:04,723 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.13104132
2015-10-19 14:42:04,723 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.13101135
2015-10-19 14:42:04,754 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.13103712
2015-10-19 14:42:07,238 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.13101934
2015-10-19 14:42:07,238 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.131014
2015-10-19 14:42:07,238 INFO [IPC Server handler 13 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.13102706
2015-10-19 14:42:07,254 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.13102192
2015-10-19 14:42:07,723 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.13101135
2015-10-19 14:42:07,723 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.13104042
2015-10-19 14:42:07,723 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.13104132
2015-10-19 14:42:07,754 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.13103712
2015-10-19 14:42:07,879 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.13102318
2015-10-19 14:42:10,254 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.17067797
2015-10-19 14:42:10,270 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.22359158
2015-10-19 14:42:10,270 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.1899017
2015-10-19 14:42:10,285 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.13102706
2015-10-19 14:42:10,754 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.13104042
2015-10-19 14:42:10,754 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.13104132
2015-10-19 14:42:10,754 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.13101135
2015-10-19 14:42:10,770 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.13103712
2015-10-19 14:42:10,879 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.13102318
2015-10-19 14:42:12,035 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.069238245
2015-10-19 14:42:13,285 INFO [IPC Server handler 13 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.23919508
2015-10-19 14:42:13,285 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.23921879
2015-10-19 14:42:13,301 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.23921585
2015-10-19 14:42:13,301 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.23922287
2015-10-19 14:42:13,770 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.21057442
2015-10-19 14:42:13,770 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.21228774
2015-10-19 14:42:13,770 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.20847812
2015-10-19 14:42:13,785 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.15893713
2015-10-19 14:42:13,910 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.13102318
2015-10-19 14:42:15,114 INFO [IPC Server handler 13 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.10359854
2015-10-19 14:42:16,301 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.23919508
2015-10-19 14:42:16,317 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.23921879
2015-10-19 14:42:16,317 INFO [IPC Server handler 7 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.23922287
2015-10-19 14:42:16,332 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.23921585
2015-10-19 14:42:16,770 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.23922269
2015-10-19 14:42:16,770 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.23923388
2015-10-19 14:42:16,770 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.23924798
2015-10-19 14:42:16,786 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.23924637
2015-10-19 14:42:16,911 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.19804218
2015-10-19 14:42:18,192 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.14115964
2015-10-19 14:42:19,333 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.2901352
2015-10-19 14:42:19,333 INFO [IPC Server handler 7 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.34743196
2015-10-19 14:42:19,333 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.23922287
2015-10-19 14:42:19,348 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.23921585
2015-10-19 14:42:19,770 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.23923388
2015-10-19 14:42:19,770 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.23922269
2015-10-19 14:42:19,786 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.23924798
2015-10-19 14:42:19,786 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.23924637
2015-10-19 14:42:19,911 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.23921506
2015-10-19 14:42:21,255 INFO [IPC Server handler 7 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.16604526
2015-10-19 14:42:22,348 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.3474061
2015-10-19 14:42:22,348 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.34743196
2015-10-19 14:42:22,348 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.34690893
2015-10-19 14:42:22,364 INFO [IPC Server handler 0 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.3474062
2015-10-19 14:42:22,770 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.23923388
2015-10-19 14:42:22,770 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.23922269
2015-10-19 14:42:22,786 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.23924798
2015-10-19 14:42:22,786 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.23924637
2015-10-19 14:42:22,911 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.23921506
2015-10-19 14:42:24,286 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.16604526
2015-10-19 14:42:25,380 INFO [IPC Server handler 0 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.3474061
2015-10-19 14:42:25,380 INFO [IPC Server handler 4 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.3473985
2015-10-19 14:42:25,380 INFO [IPC Server handler 6 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.34743196
2015-10-19 14:42:25,395 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.3474062
2015-10-19 14:42:25,786 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.33856988
2015-10-19 14:42:25,786 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.33772808
2015-10-19 14:42:25,786 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.33999714
2015-10-19 14:42:25,802 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.34742972
2015-10-19 14:42:25,911 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.23921506
2015-10-19 14:42:27,317 INFO [IPC Server handler 6 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.16604526
2015-10-19 14:42:28,395 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.37449732
2015-10-19 14:42:28,395 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.3473985
2015-10-19 14:42:28,395 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.455629
2015-10-19 14:42:28,411 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.36654964
2015-10-19 14:42:28,786 INFO [IPC Server handler 20 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.34743717
2015-10-19 14:42:28,786 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.3474054
2015-10-19 14:42:28,802 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.34742972
2015-10-19 14:42:28,802 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.34743145
2015-10-19 14:42:28,942 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.3474145
2015-10-19 14:42:30,364 INFO [IPC Server handler 4 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.20265914
2015-10-19 14:42:31,427 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.45561612
2015-10-19 14:42:31,427 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.44903573
2015-10-19 14:42:31,427 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.455629
2015-10-19 14:42:31,427 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.45563135
2015-10-19 14:42:31,786 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.34743717
2015-10-19 14:42:31,786 INFO [IPC Server handler 28 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.3474054
2015-10-19 14:42:31,802 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.34743145
2015-10-19 14:42:31,802 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.34742972
2015-10-19 14:42:31,942 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.3474145
2015-10-19 14:42:33,521 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.28562593
2015-10-19 14:42:34,443 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.455629
2015-10-19 14:42:34,443 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.45561612
2015-10-19 14:42:34,443 INFO [IPC Server handler 28 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.45559394
2015-10-19 14:42:34,443 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.45563135
2015-10-19 14:42:34,786 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.3474054
2015-10-19 14:42:34,786 INFO [IPC Server handler 20 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.34743717
2015-10-19 14:42:34,802 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.34743145
2015-10-19 14:42:34,802 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.45198828
2015-10-19 14:42:34,958 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.3474145
2015-10-19 14:42:36,615 INFO [IPC Server handler 20 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.3031575
2015-10-19 14:42:37,458 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.45559394
2015-10-19 14:42:37,458 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.5638294
2015-10-19 14:42:37,458 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.54371995
2015-10-19 14:42:37,458 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.5280393
2015-10-19 14:42:37,802 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.4556257
2015-10-19 14:42:37,802 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.3972388
2015-10-19 14:42:37,818 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.45565325
2015-10-19 14:42:37,818 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.44509315
2015-10-19 14:42:37,974 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.40098658
2015-10-19 14:42:39,709 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.3031575
2015-10-19 14:42:40,475 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.56380075
2015-10-19 14:42:40,491 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.5637838
2015-10-19 14:42:40,491 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.5638294
2015-10-19 14:42:40,506 INFO [IPC Server handler 20 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.56381613
2015-10-19 14:42:40,803 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.4556257
2015-10-19 14:42:40,803 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.45560944
2015-10-19 14:42:40,819 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.45565325
2015-10-19 14:42:40,819 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.455643
2015-10-19 14:42:40,975 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.45562187
2015-10-19 14:42:42,741 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.3031575
2015-10-19 14:42:43,506 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.56380075
2015-10-19 14:42:43,506 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.5637838
2015-10-19 14:42:43,506 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.5638294
2015-10-19 14:42:43,522 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.56381613
2015-10-19 14:42:43,819 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.5156181
2015-10-19 14:42:43,819 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.45560944
2015-10-19 14:42:43,819 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.4556257
2015-10-19 14:42:43,819 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.455643
2015-10-19 14:42:43,991 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.45562187
2015-10-19 14:42:45,757 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.38422704
2015-10-19 14:42:46,100 INFO [IPC Server handler 17 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.56380075
2015-10-19 14:42:46,350 INFO [IPC Server handler 6 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.5638294
2015-10-19 14:42:46,522 INFO [IPC Server handler 28 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.667
2015-10-19 14:42:46,522 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.56503135
2015-10-19 14:42:46,538 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.667
2015-10-19 14:42:46,538 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.5642255
2015-10-19 14:42:46,819 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.5638328
2015-10-19 14:42:46,819 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.45560944
2015-10-19 14:42:46,835 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.52966654
2015-10-19 14:42:46,835 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.45621297
2015-10-19 14:42:46,991 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.45562187
2015-10-19 14:42:47,835 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.56503135
2015-10-19 14:42:48,616 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.5642255
2015-10-19 14:42:48,835 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.4402952
2015-10-19 14:42:49,538 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.667
2015-10-19 14:42:49,538 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.667
2015-10-19 14:42:49,554 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.667
2015-10-19 14:42:49,554 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.667
2015-10-19 14:42:49,819 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.55256134
2015-10-19 14:42:49,819 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.5638328
2015-10-19 14:42:49,850 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.5638263
2015-10-19 14:42:49,850 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.56380385
2015-10-19 14:42:50,022 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.56297827
2015-10-19 14:42:51,882 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.4402952
2015-10-19 14:42:52,569 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.667
2015-10-19 14:42:52,569 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.667
2015-10-19 14:42:52,569 INFO [IPC Server handler 0 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.667
2015-10-19 14:42:52,569 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.667
2015-10-19 14:42:52,819 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.56381226
2015-10-19 14:42:52,835 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.5638328
2015-10-19 14:42:52,851 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.5638263
2015-10-19 14:42:52,866 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.56380385
2015-10-19 14:42:53,022 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.56384325
2015-10-19 14:42:54,648 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.5638328
2015-10-19 14:42:54,913 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.4402952
2015-10-19 14:42:55,585 INFO [IPC Server handler 28 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.6866777
2015-10-19 14:42:55,585 INFO [IPC Server handler 0 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.67947847
2015-10-19 14:42:55,585 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.667
2015-10-19 14:42:55,585 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.667
2015-10-19 14:42:55,632 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:18432, vCores:-11>
2015-10-19 14:42:55,632 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:42:55,819 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.56381226
2015-10-19 14:42:55,835 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.667
2015-10-19 14:42:55,851 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.5638263
2015-10-19 14:42:55,866 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.56380385
2015-10-19 14:42:56,023 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.56384325
2015-10-19 14:42:57,960 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.47880262
2015-10-19 14:42:58,601 INFO [IPC Server handler 28 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.6937644
2015-10-19 14:42:58,616 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.7257793
2015-10-19 14:42:58,616 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.6922862
2015-10-19 14:42:58,616 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.72198176
2015-10-19 14:42:58,788 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.56380385
2015-10-19 14:42:58,820 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.56381226
2015-10-19 14:42:58,835 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.667
2015-10-19 14:42:58,851 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.59907126
2015-10-19 14:42:58,866 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.667
2015-10-19 14:42:59,023 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.56384325
2015-10-19 14:43:00,413 INFO [IPC Server handler 13 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.59907126
2015-10-19 14:43:01,007 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.54897344
2015-10-19 14:43:01,163 INFO [IPC Server handler 17 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.56384325
2015-10-19 14:43:01,195 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.56381226
2015-10-19 14:43:01,617 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.7236817
2015-10-19 14:43:01,632 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.7743473
2015-10-19 14:43:01,632 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.73748016
2015-10-19 14:43:01,632 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.7550539
2015-10-19 14:43:01,835 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.667
2015-10-19 14:43:01,851 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.667
2015-10-19 14:43:01,867 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.667
2015-10-19 14:43:01,898 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.667
2015-10-19 14:43:02,054 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.667
2015-10-19 14:43:02,632 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:20480, vCores:-10>
2015-10-19 14:43:02,632 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:43:04,648 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.7757853
2015-10-19 14:43:04,648 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.7656041
2015-10-19 14:43:04,664 INFO [IPC Server handler 0 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.8135373
2015-10-19 14:43:04,679 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.80248165
2015-10-19 14:43:04,835 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.667
2015-10-19 14:43:04,851 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.667
2015-10-19 14:43:04,867 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.667
2015-10-19 14:43:04,898 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.667
2015-10-19 14:43:04,960 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.5773621
2015-10-19 14:43:05,070 INFO [IPC Server handler 18 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.667
2015-10-19 14:43:07,679 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.80037063
2015-10-19 14:43:07,679 INFO [IPC Server handler 7 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.7964598
2015-10-19 14:43:07,695 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.84187573
2015-10-19 14:43:07,695 INFO [IPC Server handler 28 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.83392
2015-10-19 14:43:07,836 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.667
2015-10-19 14:43:07,851 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.7077993
2015-10-19 14:43:07,867 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.667
2015-10-19 14:43:07,898 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.667
2015-10-19 14:43:08,007 INFO [IPC Server handler 18 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.5773621
2015-10-19 14:43:08,070 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.667
2015-10-19 14:43:10,711 INFO [IPC Server handler 28 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.82954216
2015-10-19 14:43:10,711 INFO [IPC Server handler 4 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.8236525
2015-10-19 14:43:10,726 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.86674136
2015-10-19 14:43:10,726 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.8692919
2015-10-19 14:43:10,851 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.667
2015-10-19 14:43:10,851 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.75770926
2015-10-19 14:43:10,883 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.67492294
2015-10-19 14:43:10,898 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.6738408
2015-10-19 14:43:11,023 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.58488244
2015-10-19 14:43:11,086 INFO [IPC Server handler 17 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.667
2015-10-19 14:43:13,742 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.84532684
2015-10-19 14:43:13,742 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.8490356
2015-10-19 14:43:13,758 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.8936633
2015-10-19 14:43:13,773 INFO [IPC Server handler 20 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.8862119
2015-10-19 14:43:13,805 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.58488244
2015-10-19 14:43:13,851 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.70268697
2015-10-19 14:43:13,867 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.789194
2015-10-19 14:43:13,898 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.71401626
2015-10-19 14:43:13,898 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.7014161
2015-10-19 14:43:14,055 INFO [IPC Server handler 17 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.667
2015-10-19 14:43:14,101 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.68378294
2015-10-19 14:43:16,773 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.874324
2015-10-19 14:43:16,789 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.9205486
2015-10-19 14:43:16,789 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.86109245
2015-10-19 14:43:16,805 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.9030271
2015-10-19 14:43:16,867 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.824072
2015-10-19 14:43:16,883 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.74999285
2015-10-19 14:43:16,898 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.7338286
2015-10-19 14:43:16,914 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.7629737
2015-10-19 14:43:17,086 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.667
2015-10-19 14:43:17,102 INFO [IPC Server handler 6 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.72038776
2015-10-19 14:43:19,807 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.89959407
2015-10-19 14:43:19,807 INFO [IPC Server handler 28 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.8767345
2015-10-19 14:43:19,822 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.9474395
2015-10-19 14:43:19,822 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.9197345
2015-10-19 14:43:19,901 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.78079736
2015-10-19 14:43:19,901 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.86568195
2015-10-19 14:43:19,916 INFO [IPC Server handler 18 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.7696625
2015-10-19 14:43:19,932 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.7950065
2015-10-19 14:43:20,119 INFO [IPC Server handler 13 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.7576047
2015-10-19 14:43:20,135 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.667
2015-10-19 14:43:22,838 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.892423
2015-10-19 14:43:22,838 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.9249635
2015-10-19 14:43:22,854 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 0.97423744
2015-10-19 14:43:22,854 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.9361018
2015-10-19 14:43:22,901 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.9014451
2015-10-19 14:43:22,916 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.8154508
2015-10-19 14:43:22,932 INFO [IPC Server handler 18 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.8027083
2015-10-19 14:43:22,948 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.83271146
2015-10-19 14:43:23,135 INFO [IPC Server handler 6 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.7903507
2015-10-19 14:43:23,229 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.68173194
2015-10-19 14:43:25,870 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.90810835
2015-10-19 14:43:25,870 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.950214
2015-10-19 14:43:25,885 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000001_0 is : 1.0
2015-10-19 14:43:25,885 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0005_m_000001_0
2015-10-19 14:43:25,885 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:43:25,885 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0005_01_000003 taskAttempt attempt_1445182159119_0005_m_000001_0
2015-10-19 14:43:25,885 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0005_m_000001_0
2015-10-19 14:43:25,885 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:43:25,885 INFO [IPC Server handler 18 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.9528359
2015-10-19 14:43:25,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:43:25,916 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.9372562
2015-10-19 14:43:25,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0005_m_000001_0
2015-10-19 14:43:25,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:43:25,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-19 14:43:25,916 INFO [IPC Server handler 17 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.8472681
2015-10-19 14:43:25,948 INFO [IPC Server handler 6 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.83587885
2015-10-19 14:43:25,948 INFO [IPC Server handler 13 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.8653796
2015-10-19 14:43:26,151 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.82490647
2015-10-19 14:43:26,260 INFO [IPC Server handler 4 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.7345102
2015-10-19 14:43:26,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:26,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:20480, vCores:-10>
2015-10-19 14:43:26,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-19 14:43:26,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-19 14:43:26,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:27,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0005: ask=1 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:21504, vCores:-9> knownNMs=4
2015-10-19 14:43:27,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0005_01_000003
2015-10-19 14:43:27,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:27,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0005_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:43:28,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:43:28,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-19 14:43:28,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0005_01_000012 to attempt_1445182159119_0005_r_000000_0
2015-10-19 14:43:28,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:28,651 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:43:28,651 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:43:28,651 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0005_01_000012 taskAttempt attempt_1445182159119_0005_r_000000_0
2015-10-19 14:43:28,651 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0005_r_000000_0
2015-10-19 14:43:28,651 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:43:28,667 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0005_r_000000_0 : 13562
2015-10-19 14:43:28,667 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0005_r_000000_0] using containerId: [container_1445182159119_0005_01_000012 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:43:28,667 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:43:28,667 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0005_r_000000
2015-10-19 14:43:28,667 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:43:28,901 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.9237404
2015-10-19 14:43:28,901 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 0.9752992
2015-10-19 14:43:28,917 INFO [IPC Server handler 18 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.9695957
2015-10-19 14:43:28,932 INFO [IPC Server handler 17 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 0.9776279
2015-10-19 14:43:28,932 INFO [IPC Server handler 13 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.8820337
2015-10-19 14:43:28,963 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.87332225
2015-10-19 14:43:28,979 INFO [IPC Server handler 4 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.9009761
2015-10-19 14:43:29,151 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.8616005
2015-10-19 14:43:29,307 INFO [IPC Server handler 7 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.7856369
2015-10-19 14:43:29,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0005: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:20480, vCores:-10> knownNMs=4
2015-10-19 14:43:31,307 INFO [Socket Reader #1 for port 47384] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0005 (auth:SIMPLE)
2015-10-19 14:43:31,323 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0005_r_000012 asked for a task
2015-10-19 14:43:31,323 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0005_r_000012 given task: attempt_1445182159119_0005_r_000000_0
2015-10-19 14:43:31,729 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000002_0 is : 1.0
2015-10-19 14:43:31,729 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0005_m_000002_0
2015-10-19 14:43:31,729 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:43:31,729 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0005_01_000004 taskAttempt attempt_1445182159119_0005_m_000002_0
2015-10-19 14:43:31,729 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0005_m_000002_0
2015-10-19 14:43:31,729 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:43:31,745 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:43:31,745 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0005_m_000002_0
2015-10-19 14:43:31,745 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:43:31,745 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-19 14:43:31,932 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.93973804
2015-10-19 14:43:31,948 INFO [IPC Server handler 17 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 1.0
2015-10-19 14:43:31,948 INFO [IPC Server handler 13 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 0.98662716
2015-10-19 14:43:31,948 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.91908073
2015-10-19 14:43:31,964 INFO [IPC Server handler 6 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.9092545
2015-10-19 14:43:31,979 INFO [IPC Server handler 4 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.9390639
2015-10-19 14:43:32,167 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.89641917
2015-10-19 14:43:32,339 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.83516335
2015-10-19 14:43:32,636 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:32,729 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 0 maxEvents 10000
2015-10-19 14:43:33,636 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0005_01_000004
2015-10-19 14:43:33,636 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:33,636 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0005_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:43:33,745 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:43:34,229 INFO [IPC Server handler 7 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000000_0 is : 1.0
2015-10-19 14:43:34,229 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0005_m_000000_0
2015-10-19 14:43:34,229 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:43:34,229 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0005_01_000002 taskAttempt attempt_1445182159119_0005_m_000000_0
2015-10-19 14:43:34,229 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0005_m_000000_0
2015-10-19 14:43:34,229 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:43:34,261 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:43:34,323 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0005_m_000000_0
2015-10-19 14:43:34,323 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:43:34,323 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-19 14:43:34,636 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:34,745 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:43:34,964 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 1.0
2015-10-19 14:43:34,964 INFO [IPC Server handler 18 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.95686793
2015-10-19 14:43:34,979 INFO [IPC Server handler 13 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.95439243
2015-10-19 14:43:34,995 INFO [IPC Server handler 6 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 0.9748279
2015-10-19 14:43:34,995 INFO [IPC Server handler 6 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.9424021
2015-10-19 14:43:35,182 INFO [IPC Server handler 7 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.9295988
2015-10-19 14:43:35,386 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.8862804
2015-10-19 14:43:35,636 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0005_01_000002
2015-10-19 14:43:35,636 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:35,636 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0005_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:43:35,745 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:43:36,745 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:43:37,167 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000004_0 is : 1.0
2015-10-19 14:43:37,183 INFO [IPC Server handler 7 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0005_m_000004_0
2015-10-19 14:43:37,183 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:43:37,183 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0005_01_000006 taskAttempt attempt_1445182159119_0005_m_000004_0
2015-10-19 14:43:37,183 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0005_m_000004_0
2015-10-19 14:43:37,183 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:43:37,198 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:43:37,198 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0005_m_000004_0
2015-10-19 14:43:37,198 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:43:37,198 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-19 14:43:37,323 INFO [Socket Reader #1 for port 47384] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 47384: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 14:43:37,636 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:37,745 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:43:37,995 INFO [IPC Server handler 6 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 0.98667455
2015-10-19 14:43:37,995 INFO [IPC Server handler 4 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.97182447
2015-10-19 14:43:38,011 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 1.0
2015-10-19 14:43:38,011 INFO [IPC Server handler 7 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.9721269
2015-10-19 14:43:38,183 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.9593948
2015-10-19 14:43:38,417 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.9395064
2015-10-19 14:43:38,636 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0005_01_000006
2015-10-19 14:43:38,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:38,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0005_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:43:38,698 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.0
2015-10-19 14:43:38,761 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 14:43:39,339 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000006_0 is : 1.0
2015-10-19 14:43:39,339 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0005_m_000006_0
2015-10-19 14:43:39,339 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:43:39,339 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0005_01_000008 taskAttempt attempt_1445182159119_0005_m_000006_0
2015-10-19 14:43:39,339 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0005_m_000006_0
2015-10-19 14:43:39,339 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:43:39,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:43:39,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0005_m_000006_0
2015-10-19 14:43:39,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:43:39,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-19 14:43:39,698 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:39,761 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 14:43:40,698 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0005_01_000008
2015-10-19 14:43:40,698 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:40,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0005_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:43:40,761 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:43:41,011 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 0.9917202
2015-10-19 14:43:41,011 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 1.0
2015-10-19 14:43:41,042 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 0.98738325
2015-10-19 14:43:41,183 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 0.97917604
2015-10-19 14:43:41,448 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 0.99280757
2015-10-19 14:43:41,745 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.033333335
2015-10-19 14:43:41,761 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:43:41,964 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000009_0 is : 1.0
2015-10-19 14:43:41,964 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0005_m_000009_0
2015-10-19 14:43:41,964 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:43:41,964 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0005_01_000011 taskAttempt attempt_1445182159119_0005_m_000009_0
2015-10-19 14:43:41,964 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0005_m_000009_0
2015-10-19 14:43:41,964 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:43:42,011 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:43:42,011 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0005_m_000009_0
2015-10-19 14:43:42,011 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:43:42,011 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-19 14:43:42,698 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:42,761 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:43:43,698 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000003_0 is : 1.0
2015-10-19 14:43:43,698 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0005_m_000003_0
2015-10-19 14:43:43,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:43:43,698 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0005_01_000005 taskAttempt attempt_1445182159119_0005_m_000003_0
2015-10-19 14:43:43,698 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0005_m_000003_0
2015-10-19 14:43:43,698 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:43:43,730 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:43:43,730 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0005_m_000003_0
2015-10-19 14:43:43,730 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:43:43,730 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-19 14:43:43,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:43,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0005_01_000011
2015-10-19 14:43:43,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:43,823 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0005_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:43:43,823 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:43:44,011 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 1.0
2015-10-19 14:43:44,027 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 1.0
2015-10-19 14:43:44,198 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 1.0
2015-10-19 14:43:44,792 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.033333335
2015-10-19 14:43:44,824 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0005_01_000005
2015-10-19 14:43:44,824 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:44,824 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0005_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:43:44,824 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:43:45,824 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:43:46,355 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000005_0 is : 1.0
2015-10-19 14:43:46,355 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0005_m_000005_0
2015-10-19 14:43:46,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:43:46,355 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0005_01_000007 taskAttempt attempt_1445182159119_0005_m_000005_0
2015-10-19 14:43:46,355 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0005_m_000005_0
2015-10-19 14:43:46,355 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:43:46,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:43:46,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0005_m_000005_0
2015-10-19 14:43:46,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:43:46,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-19 14:43:46,824 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:46,839 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:43:47,042 INFO [IPC Server handler 18 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 1.0
2015-10-19 14:43:47,214 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 1.0
2015-10-19 14:43:47,824 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.033333335
2015-10-19 14:43:47,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0005_01_000007
2015-10-19 14:43:47,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:47,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0005_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:43:47,996 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 14:43:48,277 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000007_0 is : 1.0
2015-10-19 14:43:48,277 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0005_m_000007_0
2015-10-19 14:43:48,277 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:43:48,277 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0005_01_000009 taskAttempt attempt_1445182159119_0005_m_000007_0
2015-10-19 14:43:48,277 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0005_m_000007_0
2015-10-19 14:43:48,277 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:43:48,292 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:43:48,292 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0005_m_000007_0
2015-10-19 14:43:48,292 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:43:48,292 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-19 14:43:48,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:48,996 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 14:43:49,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0005_01_000009
2015-10-19 14:43:49,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:49,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0005_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:43:49,996 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:43:50,871 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.10000001
2015-10-19 14:43:50,996 INFO [IPC Server handler 18 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:43:52,011 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:43:52,324 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_m_000008_0 is : 1.0
2015-10-19 14:43:52,324 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0005_m_000008_0
2015-10-19 14:43:52,324 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:43:52,324 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0005_01_000010 taskAttempt attempt_1445182159119_0005_m_000008_0
2015-10-19 14:43:52,324 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0005_m_000008_0
2015-10-19 14:43:52,324 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:43:52,340 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:43:52,340 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0005_m_000008_0
2015-10-19 14:43:52,340 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:43:52,340 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-19 14:43:52,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:53,011 INFO [IPC Server handler 18 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:43:53,902 INFO [IPC Server handler 18 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.10000001
2015-10-19 14:43:53,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0005_01_000010
2015-10-19 14:43:53,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:43:53,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0005_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:43:54,012 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:43:55,012 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:43:56,012 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:43:56,980 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.13333334
2015-10-19 14:43:57,012 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:43:58,012 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:43:59,013 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:00,013 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.13333334
2015-10-19 14:44:00,028 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:01,028 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:02,028 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:03,029 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:03,060 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.16666667
2015-10-19 14:44:04,029 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:05,029 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:06,029 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:06,091 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.20000002
2015-10-19 14:44:07,044 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:08,044 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:09,044 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:09,138 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.20000002
2015-10-19 14:44:10,044 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:11,044 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:12,045 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:12,170 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.23333333
2015-10-19 14:44:13,060 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:14,060 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:15,060 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:15,217 INFO [IPC Server handler 17 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.23333333
2015-10-19 14:44:16,060 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:17,060 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:18,060 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:18,248 INFO [IPC Server handler 7 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.26666668
2015-10-19 14:44:19,060 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:20,076 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:21,076 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:21,295 INFO [IPC Server handler 6 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.3
2015-10-19 14:44:22,076 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:23,076 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:24,092 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:24,342 INFO [IPC Server handler 4 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.3
2015-10-19 14:44:25,092 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:44:25,842 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.3
2015-10-19 14:44:25,904 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.3
2015-10-19 14:44:27,373 INFO [IPC Server handler 28 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.6672755
2015-10-19 14:44:30,405 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.67171806
2015-10-19 14:44:33,436 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.6761505
2015-10-19 14:44:36,467 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.6806067
2015-10-19 14:44:39,501 INFO [IPC Server handler 0 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.6850761
2015-10-19 14:44:42,532 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.68954873
2015-10-19 14:44:45,564 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.6940145
2015-10-19 14:44:48,596 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.69850314
2015-10-19 14:44:51,627 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7029958
2015-10-19 14:44:54,658 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7074656
2015-10-19 14:44:57,690 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.71193105
2015-10-19 14:45:00,721 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7164016
2015-10-19 14:45:03,752 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7208666
2015-10-19 14:45:06,784 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.72535324
2015-10-19 14:45:09,815 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.72982526
2015-10-19 14:45:12,847 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7342857
2015-10-19 14:45:15,878 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.738744
2015-10-19 14:45:18,902 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.74339074
2015-10-19 14:45:21,917 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7480433
2015-10-19 14:45:24,949 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7525378
2015-10-19 14:45:27,980 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7570114
2015-10-19 14:45:31,014 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.76148
2015-10-19 14:45:34,045 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.76594865
2015-10-19 14:45:37,077 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7688417
2015-10-19 14:45:40,126 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.77087337
2015-10-19 14:45:43,157 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.77281165
2015-10-19 14:45:46,188 INFO [IPC Server handler 20 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.77589023
2015-10-19 14:45:49,220 INFO [IPC Server handler 13 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.77803046
2015-10-19 14:45:52,251 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.780048
2015-10-19 14:45:55,298 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7819697
2015-10-19 14:45:58,331 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7846764
2015-10-19 14:46:01,362 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7886569
2015-10-19 14:46:04,393 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7912365
2015-10-19 14:46:07,419 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7933274
2015-10-19 14:46:10,450 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7963539
2015-10-19 14:46:13,497 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.7968981
2015-10-19 14:46:16,516 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.799204
2015-10-19 14:46:19,547 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8030877
2015-10-19 14:46:22,565 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.80766225
2015-10-19 14:46:25,597 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.81239986
2015-10-19 14:46:28,628 INFO [IPC Server handler 18 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.816953
2015-10-19 14:46:31,644 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.821427
2015-10-19 14:46:34,675 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.82590115
2015-10-19 14:46:37,707 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.83037376
2015-10-19 14:46:40,730 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8348639
2015-10-19 14:46:43,762 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.839334
2015-10-19 14:46:46,778 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.84379816
2015-10-19 14:46:49,809 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8482573
2015-10-19 14:46:52,840 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8527237
2015-10-19 14:46:55,856 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8572084
2015-10-19 14:46:58,887 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8616963
2015-10-19 14:47:01,936 INFO [IPC Server handler 19 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8646314
2015-10-19 14:47:04,968 INFO [IPC Server handler 10 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8665752
2015-10-19 14:47:08,016 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8687777
2015-10-19 14:47:11,047 INFO [IPC Server handler 18 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8705369
2015-10-19 14:47:14,078 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8737811
2015-10-19 14:47:17,110 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8765224
2015-10-19 14:47:20,142 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8790584
2015-10-19 14:47:23,174 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8828031
2015-10-19 14:47:26,221 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8849071
2015-10-19 14:47:29,239 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8886705
2015-10-19 14:47:32,255 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.891068
2015-10-19 14:47:35,302 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8933059
2015-10-19 14:47:38,333 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.8962321
2015-10-19 14:47:41,365 INFO [IPC Server handler 17 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.89952636
2015-10-19 14:47:44,396 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9026286
2015-10-19 14:47:47,428 INFO [IPC Server handler 6 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9033216
2015-10-19 14:47:50,459 INFO [IPC Server handler 0 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9050975
2015-10-19 14:47:53,506 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9077637
2015-10-19 14:47:56,540 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9100469
2015-10-19 14:47:59,573 INFO [IPC Server handler 27 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.91348565
2015-10-19 14:48:02,604 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.91531444
2015-10-19 14:48:05,644 INFO [IPC Server handler 28 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9170884
2015-10-19 14:48:08,675 INFO [IPC Server handler 28 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9203483
2015-10-19 14:48:11,722 INFO [IPC Server handler 13 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.92374814
2015-10-19 14:48:14,754 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9265752
2015-10-19 14:48:17,781 INFO [IPC Server handler 8 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9292642
2015-10-19 14:48:20,828 INFO [IPC Server handler 4 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9310703
2015-10-19 14:48:23,860 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9332129
2015-10-19 14:48:26,891 INFO [IPC Server handler 0 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.93667126
2015-10-19 14:48:29,938 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9401303
2015-10-19 14:48:32,969 INFO [IPC Server handler 11 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.94246376
2015-10-19 14:48:36,001 INFO [IPC Server handler 7 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9442899
2015-10-19 14:48:39,033 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.94645983
2015-10-19 14:48:42,075 INFO [IPC Server handler 15 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9481943
2015-10-19 14:48:45,106 INFO [IPC Server handler 3 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.95132613
2015-10-19 14:48:48,153 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9545704
2015-10-19 14:48:51,185 INFO [IPC Server handler 22 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9574542
2015-10-19 14:48:54,216 INFO [IPC Server handler 16 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.96074545
2015-10-19 14:48:57,264 INFO [IPC Server handler 28 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.962727
2015-10-19 14:49:00,298 INFO [IPC Server handler 17 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.96509504
2015-10-19 14:49:03,329 INFO [IPC Server handler 17 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9668038
2015-10-19 14:49:06,362 INFO [IPC Server handler 6 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.96957844
2015-10-19 14:49:09,409 INFO [IPC Server handler 20 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9715397
2015-10-19 14:49:12,440 INFO [IPC Server handler 9 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9728154
2015-10-19 14:49:15,487 INFO [IPC Server handler 2 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.97527933
2015-10-19 14:49:18,534 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9771878
2015-10-19 14:49:21,573 INFO [IPC Server handler 5 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9783349
2015-10-19 14:49:24,604 INFO [IPC Server handler 21 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.98122525
2015-10-19 14:49:27,635 INFO [IPC Server handler 14 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.98448855
2015-10-19 14:49:30,667 INFO [IPC Server handler 23 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.98638755
2015-10-19 14:49:33,714 INFO [IPC Server handler 12 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9895395
2015-10-19 14:49:36,745 INFO [IPC Server handler 24 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.9929882
2015-10-19 14:49:39,777 INFO [IPC Server handler 26 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.994958
2015-10-19 14:49:42,808 INFO [IPC Server handler 4 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 0.99755085
2015-10-19 14:49:45,839 INFO [IPC Server handler 18 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 1.0
2015-10-19 14:49:46,214 INFO [IPC Server handler 29 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445182159119_0005_r_000000_0
2015-10-19 14:49:46,214 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-19 14:49:46,214 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445182159119_0005_r_000000_0 given a go for committing the task output.
2015-10-19 14:49:46,214 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445182159119_0005_r_000000_0
2015-10-19 14:49:46,214 INFO [IPC Server handler 1 on 47384] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445182159119_0005_r_000000_0:true
2015-10-19 14:49:46,261 INFO [IPC Server handler 25 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0005_r_000000_0 is : 1.0
2015-10-19 14:49:46,261 INFO [IPC Server handler 7 on 47384] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0005_r_000000_0
2015-10-19 14:49:46,261 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:49:46,261 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0005_01_000012 taskAttempt attempt_1445182159119_0005_r_000000_0
2015-10-19 14:49:46,261 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0005_r_000000_0
2015-10-19 14:49:46,261 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:49:46,277 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0005_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:49:46,277 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0005_r_000000_0
2015-10-19 14:49:46,277 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0005_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:49:46,277 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-19 14:49:46,277 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0005Job Transitioned from RUNNING to COMMITTING
2015-10-19 14:49:46,277 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-19 14:49:46,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-19 14:49:46,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0005Job Transitioned from COMMITTING to SUCCEEDED
2015-10-19 14:49:46,371 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-19 14:49:46,371 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-19 14:49:46,371 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-19 14:49:46,371 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-19 14:49:46,371 INFO [Thread-101] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-19 14:49:46,371 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-19 14:49:46,371 INFO [Thread-101] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-19 14:49:46,527 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0005/job_1445182159119_0005_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0005-1445235688984-msrabi-word+count-1445237386355-10-1-SUCCEEDED-default-1445236911535.jhist_tmp
2015-10-19 14:49:47,105 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:49:47,136 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0005-1445235688984-msrabi-word+count-1445237386355-10-1-SUCCEEDED-default-1445236911535.jhist_tmp
2015-10-19 14:49:47,136 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0005/job_1445182159119_0005_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0005_conf.xml_tmp
2015-10-19 14:49:47,261 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0005_conf.xml_tmp
2015-10-19 14:49:47,261 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0005.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0005.summary
2015-10-19 14:49:47,261 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0005_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0005_conf.xml
2015-10-19 14:49:47,277 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0005-1445235688984-msrabi-word+count-1445237386355-10-1-SUCCEEDED-default-1445236911535.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0005-1445235688984-msrabi-word+count-1445237386355-10-1-SUCCEEDED-default-1445236911535.jhist
2015-10-19 14:49:47,277 INFO [Thread-101] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-19 14:49:47,277 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-19 14:49:47,277 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-39.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445182159119_0005
2015-10-19 14:49:47,292 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-19 14:49:48,293 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-19 14:49:48,293 INFO [Thread-101] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0005
2015-10-19 14:49:48,293 INFO [Thread-101] org.apache.hadoop.ipc.Server: Stopping server on 47384
2015-10-19 14:49:48,308 INFO [IPC Server listener on 47384] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 47384
2015-10-19 14:49:48,308 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
2015-10-19 14:49:48,308 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
