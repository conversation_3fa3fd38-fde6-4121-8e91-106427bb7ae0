import numpy as np

from statsmodels.tools.tools import Bunch

llf = np.array([-245.40783909604])

nobs = np.array([202])

k = np.array([5])

k_exog = np.array([1])

sigma = np.array([.8100467417583])

chi2 = np.array([2153.20304012])

df_model = np.array([3])

k_ar = np.array([1])

k_ma = np.array([2])

params = np.array([
    .92817025087557,
    -.89593490671979,
    1.3025011610587,
    .30250063082791,
    .8100467417583])

cov_params = np.array([
    .00638581549851,
    .0001858475428,
    2.8222806545671,
    .8538806860364,
    -1.1429127085819,
    .0001858475428,
    .00132037832566,
    -.14420925344502,
    -.04447007102804,
    .0576156187095,
    2.8222806545671,
    -.14420925344502,
    40397.568324803,
    12222.977216556,
    -16359.547340433,
    .8538806860364,
    -.04447007102804,
    12222.977216556,
    3698.2722243412,
    -4949.8609964351,
    -1.1429127085819,
    .0576156187095,
    -16359.547340433,
    -4949.8609964351,
    6625.0231409853]).reshape(5, 5)

xb = np.array([
    .92817026376724,
    .92817026376724,
    .69511789083481,
    .77192437648773,
    .66135895252228,
    .77525061368942,
    .64687132835388,
    .79659670591354,
    .65842008590698,
    .71215486526489,
    .69971066713333,
    .72092038393021,
    .68201982975006,
    .76510280370712,
    .64253836870193,
    .78239262104034,
    .64609551429749,
    .74087703227997,
    .71774411201477,
    .7119727730751,
    .73067259788513,
    .67785596847534,
    .70898467302322,
    .71334755420685,
    .72984194755554,
    .7017787694931,
    .75292426347733,
    .67507487535477,
    .78219056129456,
    .78040039539337,
    .71250075101852,
    .82028061151505,
    .63505899906158,
    .79452306032181,
    .72773635387421,
    .79555094242096,
    .76685506105423,
    .77427339553833,
    .82101213932037,
    .77917188405991,
    .78917801380157,
    .86641925573349,
    .78457218408585,
    .83697980642319,
    .83281791210175,
    .85224026441574,
    .75030690431595,
    .8551008105278,
    .78025943040848,
    .72790426015854,
    .84552866220474,
    .72061747312546,
    .78669738769531,
    .73868823051453,
    .78071022033691,
    .78002023696899,
    .83737623691559,
    .98988044261932,
    .72882527112961,
    1.2245427370071,
    .85331875085831,
    1.1637357473373,
    .86477434635162,
    1.3248475790024,
    .81245219707489,
    .98008638620377,
    .85591268539429,
    1.0162551403046,
    .8165408372879,
    .78947591781616,
    .94166398048401,
    .93266606330872,
    .85924750566483,
    1.1245046854019,
    .75576168298721,
    1.0030617713928,
    .91267073154449,
    1.0848042964935,
    1.0778224468231,
    1.1551086902618,
    .97817331552505,
    1.4012540578842,
    1.2360861301422,
    1.3335381746292,
    1.4352362155914,
    1.4941285848618,
    .9415163397789,
    1.437669634819,
    1.2404690980911,
    1.2285294532776,
    1.3219480514526,
    1.1560415029526,
    .83524394035339,
    .87116771936417,
    1.5561962127686,
    .47358739376068,
    .78093349933624,
    .90549737215042,
    1.0217791795731,
    .86397403478622,
    1.1526786088943,
    .87662625312805,
    .95803648233414,
    .89513635635376,
    .85281348228455,
    1.0852742195129,
    .76808404922485,
    .96872144937515,
    1.0732915401459,
    .02145584858954,
    1.3687089681625,
    .50049883127213,
    1.3895837068558,
    .6889950633049,
    1.2795144319534,
    .7050421833992,
    1.2218985557556,
    .74481928348541,
    1.3074514865875,
    .7919961810112,
    1.2807723283768,
    1.0120536088943,
    1.1938916444778,
    .68923074007034,
    1.6174983978271,
    .64740318059921,
    1.4949930906296,
    1.2678960561752,
    1.0586776733398,
    .55762887001038,
    1.2790743112564,
    .66515874862671,
    1.2538269758224,
    .70554333925247,
    1.2391568422318,
    .75241559743881,
    1.2129040956497,
    .69235223531723,
    1.0785228013992,
    .8043577671051,
    1.0037930011749,
    .78750842809677,
    1.1880930662155,
    .74399447441101,
    1.1791603565216,
    .85870295763016,
    1.0032330751419,
    .8019300699234,
    1.1696527004242,
    .92376220226288,
    .99186056852341,
    .94733852148056,
    1.0748032331467,
    .64247089624405,
    .95419937372208,
    .92043441534042,
    .8104555606842,
    .66252142190933,
    1.1178470849991,
    .69223344326019,
    1.0570795536041,
    .90239083766937,
    .95320242643356,
    1.0541093349457,
    1.0082466602325,
    1.1376332044601,
    1.1841852664948,
    .90440809726715,
    1.2733660936356,
    .66835701465607,
    1.1515763998032,
    .44600257277489,
    .93500959873199,
    1.0847823619843,
    .83353632688522,
    1.0442448854446,
    1.077241897583,
    .71010553836823,
    .89557945728302,
    1.0163468122482,
    1.094814658165,
    .89641278982162,
    1.2808450460434,
    1.0223702192307,
    .96094745397568,
    1.309353351593,
    .73499941825867,
    2.4902238845825,
    -.2579345703125,
    1.9272556304932,
    .53125941753387,
    .7708500623703,
    1.0312130451202,
    1.6360099315643,
    .6022145152092,
    1.6338716745377,
    1.3494771718979,
    1.1322995424271,
    2.1901025772095,
    -.72639065980911,
    -.37026473879814,
    1.2391144037247,
    1.1353877782822])

y = np.array([
    np.nan,
    29.908170700073,
    29.84511756897,
    30.121925354004,
    30.031360626221,
    30.315252304077,
    30.196870803833,
    30.5465965271,
    30.498420715332,
    30.52215385437,
    30.619710922241,
    30.70092010498,
    30.722021102905,
    30.975101470947,
    30.862537384033,
    31.162391662598,
    31.086095809937,
    31.220876693726,
    31.407745361328,
    31.461973190308,
    31.670673370361,
    31.627857208252,
    31.728984832764,
    31.833349227905,
    32.009841918945,
    32.08177947998,
    32.33292388916,
    32.325073242188,
    32.662189483643,
    33.060398101807,
    33.162502288818,
    33.670280456543,
    33.535060882568,
    33.894519805908,
    34.127738952637,
    34.495552062988,
    34.866851806641,
    35.17427444458,
    35.721012115479,
    36.079170227051,
    36.489177703857,
    37.16641998291,
    37.584571838379,
    38.136978149414,
    38.732818603516,
    39.352241516113,
    39.65030670166,
    40.255104064941,
    40.68025970459,
    40.827903747559,
    41.445526123047,
    41.620620727539,
    41.986698150635,
    42.238689422607,
    42.580707550049,
    42.98002243042,
    43.537376403809,
    44.689880371094,
    44.928825378418,
    46.824542999268,
    47.653316497803,
    49.263732910156,
    50.164772033691,
    52.324848175049,
    53.112449645996,
    53.980087280273,
    54.855911254883,
    55.916255950928,
    56.616539001465,
    56.889472961426,
    57.941665649414,
    58.832668304443,
    59.55924987793,
    61.124504089355,
    61.555759429932,
    62.603061676025,
    63.612670898438,
    64.984802246094,
    66.577819824219,
    68.255104064941,
    69.478172302246,
    72.001251220703,
    74.236083984375,
    76.533538818359,
    79.435234069824,
    82.39412689209,
    83.541511535645,
    86.137664794922,
    88.440467834473,
    90.32852935791,
    92.82194519043,
    94.556045532227,
    95.235244750977,
    95.871170043945,
    99.056198120117,
    98.573585510254,
    98.680938720703,
    99.705497741699,
    100.82178497314,
    101.66397857666,
    103.25267791748,
    104.17662811279,
    105.0580368042,
    105.99513244629,
    106.55281066895,
    108.08527374268,
    108.46807861328,
    109.46871948242,
    110.97328948975,
    108.72145080566,
    110.86870574951,
    110.70049285889,
    112.78958892822,
    113.38899230957,
    115.0795211792,
    115.70503997803,
    117.22190093994,
    117.94481658936,
    119.80744934082,
    120.69200134277,
    122.48076629639,
    124.11205291748,
    125.69389343262,
    126.08923339844,
    129.11749267578,
    129.54739379883,
    131.99499511719,
    134.66789245605,
    135.75866699219,
    135.6576385498,
    137.47906494141,
    137.86515808105,
    139.55383300781,
    140.10552978516,
    141.73915100098,
    142.45240783691,
    144.01290893555,
    144.49235534668,
    145.57852172852,
    146.40435791016,
    147.30380249023,
    147.98750305176,
    149.58808898926,
    150.14398193359,
    151.67915344238,
    152.65870666504,
    153.6032409668,
    154.30192565918,
    155.86964416504,
    157.02377319336,
    157.99186706543,
    159.14733886719,
    160.47479248047,
    160.54246520996,
    161.35418701172,
    162.42044067383,
    162.81045532227,
    162.86251831055,
    164.31784057617,
    164.59222412109,
    165.75708007813,
    166.80238342285,
    167.65319824219,
    169.15411376953,
    170.30824279785,
    172.03762817383,
    173.88418579102,
    174.80439758301,
    176.87336730957,
    177.06834411621,
    178.55157470703,
    178.04600524902,
    178.63500976563,
    180.38478088379,
    180.83354187012,
    182.24424743652,
    183.67724609375,
    183.91009521484,
    184.59558105469,
    185.91633605957,
    187.39482116699,
    188.29640197754,
    190.38084411621,
    191.82237243652,
    192.76095581055,
    195.10935974121,
    195.43499755859,
    201.69021606445,
    199.14205932617,
    202.62725830078,
    203.23126220703,
    202.67083740234,
    204.60522460938,
    207.55601501465,
    207.94021606445,
    210.76686096191,
    213.84446716309,
    215.12928771973,
    220.80010986328,
    216.16261291504,
    211.80372619629,
    213.91012573242,
    215.60438537598])

resid = np.array([
    np.nan,
    -.7581701874733,
    -.49511715769768,
    -.75192391872406,
    -.49135887622833,
    -.76525229215622,
    -.44687059521675,
    -.70659655332565,
    -.68842077255249,
    -.60215425491333,
    -.63971120119095,
    -.66091901063919,
    -.51202166080475,
    -.75510257482529,
    -.48253855109215,
    -.72239124774933,
    -.60609650611877,
    -.53087604045868,
    -.65774464607239,
    -.52197223901749,
    -.7206723690033,
    -.60785627365112,
    -.6089842915535,
    -.55334770679474,
    -.62984347343445,
    -.50177800655365,
    -.68292456865311,
    -.44507533311844,
    -.38219094276428,
    -.61039841175079,
    -.31250306963921,
    -.77027755975723,
    -.4350620508194,
    -.494520008564,
    -.42773708701134,
    -.39555323123932,
    -.46685197949409,
    -.27427339553833,
    -.42101442813873,
    -.37917038798332,
    -.18917952477932,
    -.36641922593117,
    -.28457221388817,
    -.23697751760483,
    -.23281940817833,
    -.45223876833916,
    -.25030693411827,
    -.35510078072548,
    -.58026248216629,
    -.22790426015854,
    -.54552561044693,
    -.42061823606491,
    -.48669815063477,
    -.43868899345398,
    -.38070866465569,
    -.28002023696899,
    .16262374818325,
    -.48988044261932,
    .67117244005203,
    -.02454199641943,
    .44668045639992,
    .0362650193274,
    .83522641658783,
    -.02484837733209,
    -.11245145648718,
    .01991361007094,
    .0440888479352,
    -.1162573993206,
    -.51654160022736,
    .11052562296391,
    -.04166246205568,
    -.13266679644585,
    .4407517015934,
    -.32450538873672,
    .04423752427101,
    .0969405695796,
    .28733000159264,
    .51519411802292,
    .52217602729797,
    .24489280581474,
    1.1218250989914,
    .99874752759933,
    .96391087770462,
    1.4664648771286,
    1.4647653102875,
    .20586840808392,
    1.1584821939468,
    1.062330365181,
    .65953236818314,
    1.1714720726013,
    .57805341482162,
    -.15604154765606,
    -.23524549603462,
    1.6288322210312,
    -.95619779825211,
    -.67358434200287,
    .1190680116415,
    .09450265020132,
    -.02177914790809,
    .43602138757706,
    .04732597246766,
    -.07663082331419,
    .0419635027647,
    -.29513788223267,
    .44718953967094,
    -.38527730107307,
    .0319189876318,
    .43128004670143,
    -2.2732961177826,
    .77854722738266,
    -.66871201992035,
    .69950574636459,
    -.08958829939365,
    .41101104021072,
    -.07951752096415,
    .2949578166008,
    -.02190163731575,
    .5551837682724,
    .0925500690937,
    .50799924135208,
    .61922925710678,
    .38794788718224,
    -.29389011859894,
    1.4107677936554,
    -.21750450134277,
    .95260292291641,
    1.4050008058548,
    .03210696578026,
    -.65866851806641,
    .54236197471619,
    -.27907428145409,
    .43484738469124,
    -.15383619070053,
    .39446276426315,
    -.03915995359421,
    .34759050607681,
    -.21290412545204,
    .00764474179596,
    .02148328535259,
    -.10436081886292,
    -.10379911959171,
    .41248852014542,
    -.18809306621552,
    .35601159930229,
    .12084264308214,
    -.05869990959764,
    -.10323911905289,
    .39806687831879,
    .2303563952446,
    -.02376830019057,
    .20813637971878,
    .25265842676163,
    -.57480323314667,
    -.14247089624405,
    .14580672979355,
    -.4204343855381,
    -.61045861244202,
    .33747857809067,
    -.41785016655922,
    .10776958614588,
    .14291742444038,
    -.1023878082633,
    .44680669903755,
    .14588765799999,
    .59174418449402,
    .66236984729767,
    .01581169478595,
    .7956041097641,
    -.47337827086449,
    .33164295554161,
    -.95156413316727,
    -.34601172804832,
    .66499650478363,
    -.38478538393974,
    .36646059155464,
    .35576421022415,
    -.47725108265877,
    -.21010553836823,
    .30441749095917,
    .38366231322289,
    .00517613813281,
    .80359941720963,
    .41915187239647,
    -.02237024717033,
    1.039052605629,
    -.409359395504,
    3.7650005817413,
    -2.2902269363403,
    1.5579376220703,
    .072744384408,
    -1.3312624692917,
    .90316116809845,
    1.3147799968719,
    -.21801064908504,
    1.1927837133408,
    1.7281278371811,
    .15252174437046,
    3.4807071685791,
    -3.9110956192017,
    -3.9886209964752,
    .86727404594421,
    .55887448787689,
    .78061258792877])

yr = np.array([
    np.nan,
    -.7581701874733,
    -.49511715769768,
    -.75192391872406,
    -.49135887622833,
    -.76525229215622,
    -.44687059521675,
    -.70659655332565,
    -.68842077255249,
    -.60215425491333,
    -.63971120119095,
    -.66091901063919,
    -.51202166080475,
    -.75510257482529,
    -.48253855109215,
    -.72239124774933,
    -.60609650611877,
    -.53087604045868,
    -.65774464607239,
    -.52197223901749,
    -.7206723690033,
    -.60785627365112,
    -.6089842915535,
    -.55334770679474,
    -.62984347343445,
    -.50177800655365,
    -.68292456865311,
    -.44507533311844,
    -.38219094276428,
    -.61039841175079,
    -.31250306963921,
    -.77027755975723,
    -.4350620508194,
    -.494520008564,
    -.42773708701134,
    -.39555323123932,
    -.46685197949409,
    -.27427339553833,
    -.42101442813873,
    -.37917038798332,
    -.18917952477932,
    -.36641922593117,
    -.28457221388817,
    -.23697751760483,
    -.23281940817833,
    -.45223876833916,
    -.25030693411827,
    -.35510078072548,
    -.58026248216629,
    -.22790426015854,
    -.54552561044693,
    -.42061823606491,
    -.48669815063477,
    -.43868899345398,
    -.38070866465569,
    -.28002023696899,
    .16262374818325,
    -.48988044261932,
    .67117244005203,
    -.02454199641943,
    .44668045639992,
    .0362650193274,
    .83522641658783,
    -.02484837733209,
    -.11245145648718,
    .01991361007094,
    .0440888479352,
    -.1162573993206,
    -.51654160022736,
    .11052562296391,
    -.04166246205568,
    -.13266679644585,
    .4407517015934,
    -.32450538873672,
    .04423752427101,
    .0969405695796,
    .28733000159264,
    .51519411802292,
    .52217602729797,
    .24489280581474,
    1.1218250989914,
    .99874752759933,
    .96391087770462,
    1.4664648771286,
    1.4647653102875,
    .20586840808392,
    1.1584821939468,
    1.062330365181,
    .65953236818314,
    1.1714720726013,
    .57805341482162,
    -.15604154765606,
    -.23524549603462,
    1.6288322210312,
    -.95619779825211,
    -.67358434200287,
    .1190680116415,
    .09450265020132,
    -.02177914790809,
    .43602138757706,
    .04732597246766,
    -.07663082331419,
    .0419635027647,
    -.29513788223267,
    .44718953967094,
    -.38527730107307,
    .0319189876318,
    .43128004670143,
    -2.2732961177826,
    .77854722738266,
    -.66871201992035,
    .69950574636459,
    -.08958829939365,
    .41101104021072,
    -.07951752096415,
    .2949578166008,
    -.02190163731575,
    .5551837682724,
    .0925500690937,
    .50799924135208,
    .61922925710678,
    .38794788718224,
    -.29389011859894,
    1.4107677936554,
    -.21750450134277,
    .95260292291641,
    1.4050008058548,
    .03210696578026,
    -.65866851806641,
    .54236197471619,
    -.27907428145409,
    .43484738469124,
    -.15383619070053,
    .39446276426315,
    -.03915995359421,
    .34759050607681,
    -.21290412545204,
    .00764474179596,
    .02148328535259,
    -.10436081886292,
    -.10379911959171,
    .41248852014542,
    -.18809306621552,
    .35601159930229,
    .12084264308214,
    -.05869990959764,
    -.10323911905289,
    .39806687831879,
    .2303563952446,
    -.02376830019057,
    .20813637971878,
    .25265842676163,
    -.57480323314667,
    -.14247089624405,
    .14580672979355,
    -.4204343855381,
    -.61045861244202,
    .33747857809067,
    -.41785016655922,
    .10776958614588,
    .14291742444038,
    -.1023878082633,
    .44680669903755,
    .14588765799999,
    .59174418449402,
    .66236984729767,
    .01581169478595,
    .7956041097641,
    -.47337827086449,
    .33164295554161,
    -.95156413316727,
    -.34601172804832,
    .66499650478363,
    -.38478538393974,
    .36646059155464,
    .35576421022415,
    -.47725108265877,
    -.21010553836823,
    .30441749095917,
    .38366231322289,
    .00517613813281,
    .80359941720963,
    .41915187239647,
    -.02237024717033,
    1.039052605629,
    -.409359395504,
    3.7650005817413,
    -2.2902269363403,
    1.5579376220703,
    .072744384408,
    -1.3312624692917,
    .90316116809845,
    1.3147799968719,
    -.21801064908504,
    1.1927837133408,
    1.7281278371811,
    .15252174437046,
    3.4807071685791,
    -3.9110956192017,
    -3.9886209964752,
    .86727404594421,
    .55887448787689,
    .78061258792877])

mse = np.array([
    .77732294797897,
    .77732294797897,
    .70387578010559,
    .69261533021927,
    .68906670808792,
    .68708789348602,
    .68558460474014,
    .68429106473923,
    .6831266283989,
    .68206071853638,
    .68107759952545,
    .68016695976257,
    .67932069301605,
    .67853212356567,
    .67779558897018,
    .67710596323013,
    .67645901441574,
    .6758508682251,
    .67527812719345,
    .67473775148392,
    .67422717809677,
    .67374390363693,
    .67328584194183,
    .6728510260582,
    .67243778705597,
    .67204451560974,
    .67166984081268,
    .67131245136261,
    .67097115516663,
    .67064493894577,
    .67033278942108,
    .67003381252289,
    .66974723339081,
    .66947221755981,
    .66920816898346,
    .66895437240601,
    .66871029138565,
    .66847538948059,
    .66824907064438,
    .66803097724915,
    .66782057285309,
    .66761755943298,
    .66742146015167,
    .66723203659058,
    .66704881191254,
    .66687160730362,
    .66670006513596,
    .66653394699097,
    .66637301445007,
    .66621696949005,
    .66606563329697,
    .66591882705688,
    .66577625274658,
    .66563785076141,
    .66550332307816,
    .66537261009216,
    .6652455329895,
    .66512185335159,
    .66500157117844,
    .66488444805145,
    .66477036476135,
    .66465926170349,
    .66455101966858,
    .66444545984268,
    .6643425822258,
    .66424214839935,
    .66414421796799,
    .66404861211777,
    .66395533084869,
    .66386413574219,
    .66377514600754,
    .66368812322617,
    .66360312700272,
    .66351997852325,
    .66343873739243,
    .6633592247963,
    .66328144073486,
    .66320532560349,
    .66313081979752,
    .66305786371231,
    .66298645734787,
    .6629164814949,
    .66284799575806,
    .66278082132339,
    .66271501779556,
    .66265046596527,
    .66258722543716,
    .6625252366066,
    .66246438026428,
    .66240465641022,
    .66234612464905,
    .66228866577148,
    .66223222017288,
    .66217684745789,
    .66212248802185,
    .66206908226013,
    .66201663017273,
    .661965072155,
    .66191446781158,
    .6618646979332,
    .66181582212448,
    .66176778078079,
    .66172051429749,
    .66167408227921,
    .66162836551666,
    .66158348321915,
    .66153925657272,
    .66149580478668,
    .66145300865173,
    .66141092777252,
    .6613695025444,
    .66132873296738,
    .6612885594368,
    .66124904155731,
    .66121011972427,
    .66117179393768,
    .66113406419754,
    .6610968708992,
    .66106027364731,
    .66102415323257,
    .66098862886429,
    .66095358133316,
    .66091907024384,
    .66088503599167,
    .66085147857666,
    .66081839799881,
    .66078579425812,
    .66075360774994,
    .66072189807892,
    .66069066524506,
    .66065979003906,
    .66062939167023,
    .66059935092926,
    .66056972742081,
    .66054052114487,
    .6605116724968,
    .66048324108124,
    .6604551076889,
    .66042739152908,
    .66040003299713,
    .66037303209305,
    .66034632921219,
    .66032004356384,
    .66029399633408,
    .66026836633682,
    .66024297475815,
    .66021794080734,
    .66019320487976,
    .6601687669754,
    .66014462709427,
    .66012072563171,
    .66009718179703,
    .66007387638092,
    .66005086898804,
    .66002810001373,
    .66000562906265,
    .65998339653015,
    .65996146202087,
    .65993976593018,
    .65991830825806,
    .65989708900452,
    .65987610816956,
    .65985536575317,
    .65983480215073,
    .6598145365715,
    .65979450941086,
    .65977466106415,
    .65975499153137,
    .65973562002182,
    .6597164273262,
    .65969741344452,
    .65967857837677,
    .6596599817276,
    .65964162349701,
    .65962338447571,
    .65960538387299,
    .6595875620842,
    .65956991910934,
    .65955245494843,
    .65953516960144,
    .65951806306839,
    .65950113534927,
    .65948438644409,
    .6594677567482,
    .65945136547089,
    .65943509340286,
    .65941900014877,
    .65940302610397,
    .65938723087311,
    .65937161445618,
    .65935611724854,
    .65934079885483,
    .65932559967041,
    .65931057929993,
    .65929567813873,
    .65928089618683,
    .65926629304886,
    .65925180912018,
    .65923744440079,
    .65922319889069,
    .65920913219452,
    .65919518470764,
    .65918135643005])

stdp = np.array([
    .92817026376724,
    .92817026376724,
    .69511789083481,
    .77192437648773,
    .66135895252228,
    .77525061368942,
    .64687132835388,
    .79659670591354,
    .65842008590698,
    .71215486526489,
    .69971066713333,
    .72092038393021,
    .68201982975006,
    .76510280370712,
    .64253836870193,
    .78239262104034,
    .64609551429749,
    .74087703227997,
    .71774411201477,
    .7119727730751,
    .73067259788513,
    .67785596847534,
    .70898467302322,
    .71334755420685,
    .72984194755554,
    .7017787694931,
    .75292426347733,
    .67507487535477,
    .78219056129456,
    .78040039539337,
    .71250075101852,
    .82028061151505,
    .63505899906158,
    .79452306032181,
    .72773635387421,
    .79555094242096,
    .76685506105423,
    .77427339553833,
    .82101213932037,
    .77917188405991,
    .78917801380157,
    .86641925573349,
    .78457218408585,
    .83697980642319,
    .83281791210175,
    .85224026441574,
    .75030690431595,
    .8551008105278,
    .78025943040848,
    .72790426015854,
    .84552866220474,
    .72061747312546,
    .78669738769531,
    .73868823051453,
    .78071022033691,
    .78002023696899,
    .83737623691559,
    .98988044261932,
    .72882527112961,
    1.2245427370071,
    .85331875085831,
    1.1637357473373,
    .86477434635162,
    1.3248475790024,
    .81245219707489,
    .98008638620377,
    .85591268539429,
    1.0162551403046,
    .8165408372879,
    .78947591781616,
    .94166398048401,
    .93266606330872,
    .85924750566483,
    1.1245046854019,
    .75576168298721,
    1.0030617713928,
    .91267073154449,
    1.0848042964935,
    1.0778224468231,
    1.1551086902618,
    .97817331552505,
    1.4012540578842,
    1.2360861301422,
    1.3335381746292,
    1.4352362155914,
    1.4941285848618,
    .9415163397789,
    1.437669634819,
    1.2404690980911,
    1.2285294532776,
    1.3219480514526,
    1.1560415029526,
    .83524394035339,
    .87116771936417,
    1.5561962127686,
    .47358739376068,
    .78093349933624,
    .90549737215042,
    1.0217791795731,
    .86397403478622,
    1.1526786088943,
    .87662625312805,
    .95803648233414,
    .89513635635376,
    .85281348228455,
    1.0852742195129,
    .76808404922485,
    .96872144937515,
    1.0732915401459,
    .02145584858954,
    1.3687089681625,
    .50049883127213,
    1.3895837068558,
    .6889950633049,
    1.2795144319534,
    .7050421833992,
    1.2218985557556,
    .74481928348541,
    1.3074514865875,
    .7919961810112,
    1.2807723283768,
    1.0120536088943,
    1.1938916444778,
    .68923074007034,
    1.6174983978271,
    .64740318059921,
    1.4949930906296,
    1.2678960561752,
    1.0586776733398,
    .55762887001038,
    1.2790743112564,
    .66515874862671,
    1.2538269758224,
    .70554333925247,
    1.2391568422318,
    .75241559743881,
    1.2129040956497,
    .69235223531723,
    1.0785228013992,
    .8043577671051,
    1.0037930011749,
    .78750842809677,
    1.1880930662155,
    .74399447441101,
    1.1791603565216,
    .85870295763016,
    1.0032330751419,
    .8019300699234,
    1.1696527004242,
    .92376220226288,
    .99186056852341,
    .94733852148056,
    1.0748032331467,
    .64247089624405,
    .95419937372208,
    .92043441534042,
    .8104555606842,
    .66252142190933,
    1.1178470849991,
    .69223344326019,
    1.0570795536041,
    .90239083766937,
    .95320242643356,
    1.0541093349457,
    1.0082466602325,
    1.1376332044601,
    1.1841852664948,
    .90440809726715,
    1.2733660936356,
    .66835701465607,
    1.1515763998032,
    .44600257277489,
    .93500959873199,
    1.0847823619843,
    .83353632688522,
    1.0442448854446,
    1.077241897583,
    .71010553836823,
    .89557945728302,
    1.0163468122482,
    1.094814658165,
    .89641278982162,
    1.2808450460434,
    1.0223702192307,
    .96094745397568,
    1.309353351593,
    .73499941825867,
    2.4902238845825,
    -.2579345703125,
    1.9272556304932,
    .53125941753387,
    .7708500623703,
    1.0312130451202,
    1.6360099315643,
    .6022145152092,
    1.6338716745377,
    1.3494771718979,
    1.1322995424271,
    2.1901025772095,
    -.72639065980911,
    -.37026473879814,
    1.2391144037247,
    1.1353877782822])

icstats = np.array([
    202,
    np.nan,
    -245.40783909604,
    5,
    500.81567819208,
    517.35701667909])


results = Bunch(
    llf=llf,
    nobs=nobs,
    k=k,
    k_exog=k_exog,
    sigma=sigma,
    chi2=chi2,
    df_model=df_model,
    k_ar=k_ar,
    k_ma=k_ma,
    params=params,
    cov_params=cov_params,
    xb=xb,
    y=y,
    resid=resid,
    yr=yr,
    mse=mse,
    stdp=stdp,
    icstats=icstats
)
