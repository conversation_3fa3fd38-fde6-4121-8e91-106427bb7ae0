2015-10-17 18:09:21,549 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445076437777_0004_000001
2015-10-17 18:09:21,940 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 18:09:21,940 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 4 cluster_timestamp: 1445076437777 } attemptId: 1 } keyId: 291674728)
2015-10-17 18:09:22,190 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 18:09:23,425 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 18:09:23,550 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 18:09:23,596 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 18:09:23,596 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 18:09:23,612 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 18:09:23,612 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 18:09:23,612 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 18:09:23,628 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 18:09:23,628 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 18:09:23,628 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 18:09:23,706 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:09:23,753 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:09:23,800 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:09:23,815 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 18:09:23,893 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 18:09:24,315 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 18:09:24,440 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 18:09:24,440 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 18:09:24,456 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445076437777_0004 to jobTokenSecretManager
2015-10-17 18:09:25,023 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445076437777_0004 because: not enabled; too many maps; too much input;
2015-10-17 18:09:25,054 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445076437777_0004 = 1256521728. Number of splits = 10
2015-10-17 18:09:25,054 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445076437777_0004 = 1
2015-10-17 18:09:25,054 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0004Job Transitioned from NEW to INITED
2015-10-17 18:09:25,054 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445076437777_0004.
2015-10-17 18:09:25,116 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 18:09:25,132 INFO [Socket Reader #1 for port 39925] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 39925
2015-10-17 18:09:25,179 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 18:09:25,179 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 18:09:25,179 INFO [IPC Server listener on 39925] org.apache.hadoop.ipc.Server: IPC Server listener on 39925: starting
2015-10-17 18:09:25,179 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-39.fareast.corp.microsoft.com/**************:39925
2015-10-17 18:09:25,304 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 18:09:25,304 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 18:09:25,320 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 18:09:25,320 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 18:09:25,320 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 18:09:25,335 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 18:09:25,335 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 18:09:25,351 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 39932
2015-10-17 18:09:25,351 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 18:09:25,413 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_39932_mapreduce____yd8kp3\webapp
2015-10-17 18:09:25,726 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:39932
2015-10-17 18:09:25,726 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 39932
2015-10-17 18:09:26,179 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 18:09:26,179 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445076437777_0004
2015-10-17 18:09:26,179 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 18:09:26,195 INFO [Socket Reader #1 for port 39935] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 39935
2015-10-17 18:09:26,195 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 18:09:26,195 INFO [IPC Server listener on 39935] org.apache.hadoop.ipc.Server: IPC Server listener on 39935: starting
2015-10-17 18:09:26,226 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 18:09:26,226 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 18:09:26,226 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 18:09:26,273 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 18:09:26,413 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 18:09:26,413 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 18:09:26,413 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 18:09:26,413 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 18:09:26,429 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0004Job Transitioned from INITED to SETUP
2015-10-17 18:09:26,429 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 18:09:26,445 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0004Job Transitioned from SETUP to RUNNING
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:26,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:26,491 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 18:09:26,507 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 18:09:26,538 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445076437777_0004, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0004/job_1445076437777_0004_1.jhist
2015-10-17 18:09:27,413 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 18:09:27,476 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:18432, vCores:-17> knownNMs=5
2015-10-17 18:09:27,476 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:18432, vCores:-17>
2015-10-17 18:09:27,476 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:28,486 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:14336, vCores:-21>
2015-10-17 18:09:28,486 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:29,486 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:13312, vCores:-22>
2015-10-17 18:09:29,486 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:30,486 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-26>
2015-10-17 18:09:30,486 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:31,486 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-29>
2015-10-17 18:09:31,486 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:32,518 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:09:32,596 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:32,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0004_01_000002 to attempt_1445076437777_0004_m_000000_0
2015-10-17 18:09:32,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-17 18:09:32,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:32,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:0 RackLocal:1
2015-10-17 18:09:32,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:32,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0004/job.jar
2015-10-17 18:09:32,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0004/job.xml
2015-10-17 18:09:32,736 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 18:09:32,736 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 18:09:32,736 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 18:09:32,783 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:32,783 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0004_01_000002 taskAttempt attempt_1445076437777_0004_m_000000_0
2015-10-17 18:09:32,799 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0004_m_000000_0
2015-10-17 18:09:32,799 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:09:33,018 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0004_m_000000_0 : 13562
2015-10-17 18:09:33,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0004_m_000000_0] using containerId: [container_1445076437777_0004_01_000002 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:53425]
2015-10-17 18:09:33,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:33,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0004_m_000000
2015-10-17 18:09:33,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:33,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:4096, vCores:-31> knownNMs=5
2015-10-17 18:09:33,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-17 18:09:33,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:34,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-33>
2015-10-17 18:09:34,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:35,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 18:09:35,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:36,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 18:09:36,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:37,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:09:37,612 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:37,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0004_01_000003 to attempt_1445076437777_0004_m_000001_0
2015-10-17 18:09:37,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:37,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:37,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:0 RackLocal:2
2015-10-17 18:09:37,627 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:37,627 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:37,627 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0004_01_000003 taskAttempt attempt_1445076437777_0004_m_000001_0
2015-10-17 18:09:37,627 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0004_m_000001_0
2015-10-17 18:09:37,627 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 18:09:37,737 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0004_m_000001_0 : 13562
2015-10-17 18:09:37,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0004_m_000001_0] using containerId: [container_1445076437777_0004_01_000003 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 18:09:37,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:37,752 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0004_m_000001
2015-10-17 18:09:37,752 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:38,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:09:38,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:38,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:39,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:39,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:40,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:40,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:41,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:41,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:42,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:42,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:43,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:43,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:44,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:44,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:45,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:45,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:46,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:46,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:47,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:47,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:48,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:48,643 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:48,675 INFO [Socket Reader #1 for port 39935] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0004 (auth:SIMPLE)
2015-10-17 18:09:48,847 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0004_m_000002 asked for a task
2015-10-17 18:09:48,847 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0004_m_000002 given task: attempt_1445076437777_0004_m_000000_0
2015-10-17 18:09:49,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:49,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:50,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:50,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:51,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:51,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:52,581 INFO [Socket Reader #1 for port 39935] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0004 (auth:SIMPLE)
2015-10-17 18:09:52,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:52,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:52,691 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0004_m_000003 asked for a task
2015-10-17 18:09:52,691 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0004_m_000003 given task: attempt_1445076437777_0004_m_000001_0
2015-10-17 18:09:53,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:53,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:54,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:54,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:55,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:55,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:56,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:56,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:57,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:57,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:58,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:58,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:59,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:59,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:00,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:00,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:00,847 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.010747375
2015-10-17 18:10:01,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:01,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:02,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:02,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:03,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:03,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:04,223 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.012698554
2015-10-17 18:10:04,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:04,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:05,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:05,644 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:06,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:06,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:07,301 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.019214815
2015-10-17 18:10:07,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:07,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:08,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:08,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:08,926 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.044616804
2015-10-17 18:10:09,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:09,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:10,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:10,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:11,207 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.02735686
2015-10-17 18:10:11,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:11,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:12,598 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.0780435
2015-10-17 18:10:12,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:12,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:13,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:13,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:14,583 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.039732542
2015-10-17 18:10:14,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:14,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:15,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:15,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:16,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:16,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:17,036 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.10260103
2015-10-17 18:10:17,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:17,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:17,864 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.051131863
2015-10-17 18:10:18,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:18,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:19,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:19,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:20,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:20,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:20,724 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.1066108
2015-10-17 18:10:21,130 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.06383295
2015-10-17 18:10:21,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:21,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:22,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:22,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:23,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:23,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:24,411 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.1066108
2015-10-17 18:10:24,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:24,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:24,833 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.08500332
2015-10-17 18:10:25,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:25,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:26,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:26,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:27,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:27,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:28,349 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.1066108
2015-10-17 18:10:28,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:28,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:29,427 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.10537208
2015-10-17 18:10:29,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:29,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:30,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:30,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:31,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:31,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:32,271 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.1066108
2015-10-17 18:10:32,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:32,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:32,927 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.10635664
2015-10-17 18:10:33,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:33,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:34,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:34,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:35,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:35,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:36,334 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.10635664
2015-10-17 18:10:36,506 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.1066108
2015-10-17 18:10:36,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:36,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:37,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:37,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:38,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:38,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:39,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:39,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:39,850 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.10635664
2015-10-17 18:10:40,381 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.1066108
2015-10-17 18:10:40,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:40,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:41,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:41,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:42,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:42,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:43,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:43,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:43,959 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.10635664
2015-10-17 18:10:44,084 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.1066108
2015-10-17 18:10:44,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:44,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:45,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:45,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:46,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:46,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:47,194 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.10635664
2015-10-17 18:10:47,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:47,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:48,194 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.1066108
2015-10-17 18:10:48,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:48,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:49,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:49,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:50,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:50,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:50,913 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.10635664
2015-10-17 18:10:51,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:51,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:51,788 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.1066108
2015-10-17 18:10:52,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:52,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:53,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:53,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:54,351 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.10635664
2015-10-17 18:10:54,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:54,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:55,601 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.1066108
2015-10-17 18:10:55,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:55,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:56,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:56,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:57,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:57,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:58,241 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.12935121
2015-10-17 18:10:58,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:58,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:10:59,398 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.1066108
2015-10-17 18:10:59,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:59,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:00,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:00,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:01,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:01,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:01,898 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.1738275
2015-10-17 18:11:02,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:02,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:03,039 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.118999906
2015-10-17 18:11:03,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:03,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:04,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:04,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:05,570 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.19158794
2015-10-17 18:11:05,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:05,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:06,554 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.14888678
2015-10-17 18:11:06,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:06,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:07,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:07,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:08,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:08,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:08,930 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.19158794
2015-10-17 18:11:09,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:09,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:10,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:10,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:11,430 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.18241078
2015-10-17 18:11:11,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:11,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:12,539 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.19158794
2015-10-17 18:11:12,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:12,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:13,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:13,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:14,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:14,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:15,352 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.19211523
2015-10-17 18:11:15,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:15,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:15,977 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.19158794
2015-10-17 18:11:16,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:16,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:17,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:17,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:18,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:18,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:19,133 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.19211523
2015-10-17 18:11:19,508 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.19158794
2015-10-17 18:11:19,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:19,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:20,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:20,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:21,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:21,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:22,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:22,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:22,743 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.19211523
2015-10-17 18:11:22,915 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.19158794
2015-10-17 18:11:23,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:23,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:24,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:24,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:25,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:25,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:26,493 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.19211523
2015-10-17 18:11:26,618 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.19158794
2015-10-17 18:11:26,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:26,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:27,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:27,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:28,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:28,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:29,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:29,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:30,071 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.19158794
2015-10-17 18:11:30,462 INFO [IPC Server handler 10 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.19211523
2015-10-17 18:11:30,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:30,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:31,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:31,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:32,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:32,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:33,369 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.21098875
2015-10-17 18:11:33,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:33,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:34,431 INFO [IPC Server handler 10 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.19211523
2015-10-17 18:11:34,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:34,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:35,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:35,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:36,431 INFO [IPC Server handler 10 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.22797708
2015-10-17 18:11:36,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:36,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:37,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:37,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:38,259 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.19211523
2015-10-17 18:11:38,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:38,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:39,509 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.24230738
2015-10-17 18:11:39,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:39,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:40,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:40,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:41,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:41,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:41,900 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.19211523
2015-10-17 18:11:42,603 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.24751937
2015-10-17 18:11:42,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:42,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:43,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:43,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:44,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:44,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:45,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:45,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:45,775 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.19211523
2015-10-17 18:11:46,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:46,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:47,326 INFO [IPC Server handler 10 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.25956818
2015-10-17 18:11:47,654 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:11:47,654 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0004_01_000004 to attempt_1445076437777_0004_m_000002_0
2015-10-17 18:11:47,654 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:47,654 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:47,654 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:1 RackLocal:2
2015-10-17 18:11:47,654 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:11:47,654 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:11:47,654 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0004_01_000004 taskAttempt attempt_1445076437777_0004_m_000002_0
2015-10-17 18:11:47,654 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0004_m_000002_0
2015-10-17 18:11:47,654 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:11:47,670 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0004_m_000002_0 : 13562
2015-10-17 18:11:47,670 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0004_m_000002_0] using containerId: [container_1445076437777_0004_01_000004 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:11:47,670 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:11:47,670 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0004_m_000002
2015-10-17 18:11:47,670 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:11:48,659 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0004: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:11:48,659 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:11:48,659 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0004_01_000005 to attempt_1445076437777_0004_m_000003_0
2015-10-17 18:11:48,659 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:48,659 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:48,659 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:2 RackLocal:2
2015-10-17 18:11:48,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:11:48,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:11:48,659 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0004_01_000005 taskAttempt attempt_1445076437777_0004_m_000003_0
2015-10-17 18:11:48,659 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0004_m_000003_0
2015-10-17 18:11:48,659 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:11:48,674 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0004_m_000003_0 : 13562
2015-10-17 18:11:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0004_m_000003_0] using containerId: [container_1445076437777_0004_01_000005 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:11:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:11:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0004_m_000003
2015-10-17 18:11:48,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:11:49,665 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0004: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:11:49,665 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:11:49,665 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0004_01_000006 to attempt_1445076437777_0004_m_000004_0
2015-10-17 18:11:49,665 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:49,665 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:49,665 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:3 RackLocal:2
2015-10-17 18:11:49,665 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:11:49,665 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:11:49,665 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0004_01_000006 taskAttempt attempt_1445076437777_0004_m_000004_0
2015-10-17 18:11:49,665 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0004_m_000004_0
2015-10-17 18:11:49,665 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:11:49,680 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0004_m_000004_0 : 13562
2015-10-17 18:11:49,680 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0004_m_000004_0] using containerId: [container_1445076437777_0004_01_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:11:49,680 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:11:49,680 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0004_m_000004
2015-10-17 18:11:49,680 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:11:49,790 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.19211523
2015-10-17 18:11:50,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:11:50,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:50,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:51,496 INFO [Socket Reader #1 for port 39935] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0004 (auth:SIMPLE)
2015-10-17 18:11:51,512 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0004_m_000004 asked for a task
2015-10-17 18:11:51,512 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0004_m_000004 given task: attempt_1445076437777_0004_m_000002_0
2015-10-17 18:11:51,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:51,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:52,512 INFO [Socket Reader #1 for port 39935] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0004 (auth:SIMPLE)
2015-10-17 18:11:52,527 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0004_m_000005 asked for a task
2015-10-17 18:11:52,527 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0004_m_000005 given task: attempt_1445076437777_0004_m_000003_0
2015-10-17 18:11:52,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:52,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:53,371 INFO [IPC Server handler 10 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.20349315
2015-10-17 18:11:53,496 INFO [Socket Reader #1 for port 39935] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0004 (auth:SIMPLE)
2015-10-17 18:11:53,527 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0004_m_000006 asked for a task
2015-10-17 18:11:53,527 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0004_m_000006 given task: attempt_1445076437777_0004_m_000004_0
2015-10-17 18:11:53,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 3
2015-10-17 18:11:53,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0004_01_000007 to attempt_1445076437777_0004_m_000005_0
2015-10-17 18:11:53,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0004_01_000008 to attempt_1445076437777_0004_m_000006_0
2015-10-17 18:11:53,668 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:11:53,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0004_01_000009 to attempt_1445076437777_0004_m_000007_0
2015-10-17 18:11:53,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:53,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:53,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:6 RackLocal:2
2015-10-17 18:11:53,668 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:11:53,668 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:11:53,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:11:53,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:11:53,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:11:53,683 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0004_01_000007 taskAttempt attempt_1445076437777_0004_m_000005_0
2015-10-17 18:11:53,683 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0004_m_000005_0
2015-10-17 18:11:53,683 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0004_01_000008 taskAttempt attempt_1445076437777_0004_m_000006_0
2015-10-17 18:11:53,683 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:11:53,683 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0004_m_000006_0
2015-10-17 18:11:53,683 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0004_01_000009 taskAttempt attempt_1445076437777_0004_m_000007_0
2015-10-17 18:11:53,683 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0004_m_000007_0
2015-10-17 18:11:53,683 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:11:53,683 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:11:53,715 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0004_m_000005_0 : 13562
2015-10-17 18:11:53,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0004_m_000005_0] using containerId: [container_1445076437777_0004_01_000007 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:11:53,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:11:53,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0004_m_000005
2015-10-17 18:11:53,715 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.27520248
2015-10-17 18:11:53,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:11:53,715 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0004_m_000007_0 : 13562
2015-10-17 18:11:53,715 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0004_m_000006_0 : 13562
2015-10-17 18:11:53,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0004_m_000007_0] using containerId: [container_1445076437777_0004_01_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:11:53,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:11:53,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0004_m_000006_0] using containerId: [container_1445076437777_0004_01_000008 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:11:53,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:11:53,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0004_m_000007
2015-10-17 18:11:53,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:11:53,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0004_m_000006
2015-10-17 18:11:53,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:11:54,671 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0004: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:11:54,671 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 18:11:54,671 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0004_01_000010 to attempt_1445076437777_0004_m_000008_0
2015-10-17 18:11:54,671 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0004_01_000011 to attempt_1445076437777_0004_m_000009_0
2015-10-17 18:11:54,671 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:11:54,671 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:54,671 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 18:11:54,671 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:11:54,671 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:11:54,671 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:11:54,671 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:11:54,671 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0004_01_000010 taskAttempt attempt_1445076437777_0004_m_000008_0
2015-10-17 18:11:54,671 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0004_01_000011 taskAttempt attempt_1445076437777_0004_m_000009_0
2015-10-17 18:11:54,671 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0004_m_000008_0
2015-10-17 18:11:54,671 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0004_m_000009_0
2015-10-17 18:11:54,671 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:11:54,671 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:11:54,702 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0004_m_000009_0 : 13562
2015-10-17 18:11:54,702 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0004_m_000008_0 : 13562
2015-10-17 18:11:54,702 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0004_m_000009_0] using containerId: [container_1445076437777_0004_01_000011 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:11:54,702 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:11:54,702 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0004_m_000008_0] using containerId: [container_1445076437777_0004_01_000010 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:11:54,702 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:11:54,702 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0004_m_000009
2015-10-17 18:11:54,702 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:11:54,702 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0004_m_000008
2015-10-17 18:11:54,702 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:11:55,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:11:56,533 INFO [Socket Reader #1 for port 39935] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0004 (auth:SIMPLE)
2015-10-17 18:11:56,580 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0004_m_000008 asked for a task
2015-10-17 18:11:56,580 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0004_m_000008 given task: attempt_1445076437777_0004_m_000006_0
2015-10-17 18:11:57,049 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.23807171
2015-10-17 18:11:57,205 INFO [Socket Reader #1 for port 39935] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0004 (auth:SIMPLE)
2015-10-17 18:11:57,237 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0004_m_000009 asked for a task
2015-10-17 18:11:57,237 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0004_m_000009 given task: attempt_1445076437777_0004_m_000007_0
2015-10-17 18:11:57,268 INFO [Socket Reader #1 for port 39935] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0004 (auth:SIMPLE)
2015-10-17 18:11:57,283 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0004_m_000007 asked for a task
2015-10-17 18:11:57,283 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0004_m_000007 given task: attempt_1445076437777_0004_m_000005_0
2015-10-17 18:11:57,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 18:11:57,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:58,330 INFO [Socket Reader #1 for port 39935] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0004 (auth:SIMPLE)
2015-10-17 18:11:58,362 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0004_m_000011 asked for a task
2015-10-17 18:11:58,362 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0004_m_000011 given task: attempt_1445076437777_0004_m_000009_0
2015-10-17 18:11:58,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-33>
2015-10-17 18:11:58,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:11:58,752 INFO [Socket Reader #1 for port 39935] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0004 (auth:SIMPLE)
2015-10-17 18:11:58,768 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0004_m_000010 asked for a task
2015-10-17 18:11:58,768 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0004_m_000010 given task: attempt_1445076437777_0004_m_000008_0
2015-10-17 18:11:59,143 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.09915685
2015-10-17 18:12:00,112 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.10362203
2015-10-17 18:12:01,127 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.10680563
2015-10-17 18:12:01,596 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.26521093
2015-10-17 18:12:02,143 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.10660437
2015-10-17 18:12:02,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-17 18:12:02,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:12:03,128 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.106493875
2015-10-17 18:12:04,128 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.10680563
2015-10-17 18:12:04,737 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.10685723
2015-10-17 18:12:04,737 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.0902688
2015-10-17 18:12:05,143 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.10660437
2015-10-17 18:12:05,299 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.10681946
2015-10-17 18:12:05,456 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.27776006
2015-10-17 18:12:05,753 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.295472
2015-10-17 18:12:06,143 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.106493875
2015-10-17 18:12:06,518 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.106881365
2015-10-17 18:12:06,675 INFO [Socket Reader #1 for port 39935] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0004 (auth:SIMPLE)
2015-10-17 18:12:06,737 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.27696857
2015-10-17 18:12:07,143 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.10680563
2015-10-17 18:12:07,737 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.10685723
2015-10-17 18:12:07,768 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.106964506
2015-10-17 18:12:08,175 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.10660437
2015-10-17 18:12:08,315 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.10681946
2015-10-17 18:12:08,753 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.295472
2015-10-17 18:12:09,143 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.106493875
2015-10-17 18:12:09,315 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.27776006
2015-10-17 18:12:09,550 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.106881365
2015-10-17 18:12:09,675 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-33>
2015-10-17 18:12:09,675 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:12:10,018 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.29311383
2015-10-17 18:12:10,159 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.10680563
2015-10-17 18:12:10,753 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.10685723
2015-10-17 18:12:10,800 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.106964506
2015-10-17 18:12:11,190 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.10660437
2015-10-17 18:12:11,440 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.10681946
2015-10-17 18:12:11,769 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.295472
2015-10-17 18:12:12,159 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.13596246
2015-10-17 18:12:12,565 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.106881365
2015-10-17 18:12:13,159 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.13609812
2015-10-17 18:12:13,190 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.27776006
2015-10-17 18:12:13,769 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.10685723
2015-10-17 18:12:13,816 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.106964506
2015-10-17 18:12:14,191 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.10660437
2015-10-17 18:12:14,472 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.1481939
2015-10-17 18:12:14,784 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.29776198
2015-10-17 18:12:15,175 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.19209063
2015-10-17 18:12:15,597 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.18770155
2015-10-17 18:12:16,175 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.19242907
2015-10-17 18:12:16,784 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.17999552
2015-10-17 18:12:16,847 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.106964506
2015-10-17 18:12:16,863 INFO [IPC Server handler 22 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.27776006
2015-10-17 18:12:17,488 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.19255035
2015-10-17 18:12:17,784 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.5003788
2015-10-17 18:12:18,113 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.31776828
2015-10-17 18:12:18,175 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.19209063
2015-10-17 18:12:18,628 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.19258286
2015-10-17 18:12:19,175 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.19242907
2015-10-17 18:12:19,800 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.19247705
2015-10-17 18:12:19,878 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.1821953
2015-10-17 18:12:20,488 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.27776006
2015-10-17 18:12:20,503 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.19255035
2015-10-17 18:12:20,785 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.5323719
2015-10-17 18:12:21,175 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.19209063
2015-10-17 18:12:21,285 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.33317325
2015-10-17 18:12:21,644 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.19258286
2015-10-17 18:12:22,191 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.19242907
2015-10-17 18:12:22,816 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.19247705
2015-10-17 18:12:22,910 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.19266446
2015-10-17 18:12:23,207 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.19212553
2015-10-17 18:12:23,535 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.19255035
2015-10-17 18:12:23,785 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.5323719
2015-10-17 18:12:24,175 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.19209063
2015-10-17 18:12:24,332 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.27776006
2015-10-17 18:12:24,519 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.3471781
2015-10-17 18:12:24,675 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 18:12:24,675 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:12:24,675 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.2066977
2015-10-17 18:12:25,207 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.19242907
2015-10-17 18:12:25,816 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.19247705
2015-10-17 18:12:25,941 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.19266446
2015-10-17 18:12:26,222 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.19212553
2015-10-17 18:12:26,566 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.27825075
2015-10-17 18:12:26,800 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.5323719
2015-10-17 18:12:27,207 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.19209063
2015-10-17 18:12:27,707 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.27811313
2015-10-17 18:12:27,785 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.3624012
2015-10-17 18:12:28,004 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.27776006
2015-10-17 18:12:28,222 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.19242907
2015-10-17 18:12:28,832 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.19247705
2015-10-17 18:12:28,957 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.19266446
2015-10-17 18:12:29,238 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.19212553
2015-10-17 18:12:29,597 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.27825075
2015-10-17 18:12:29,816 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.5323719
2015-10-17 18:12:30,723 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.27811313
2015-10-17 18:12:31,223 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.3624012
2015-10-17 18:12:31,801 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.27776006
2015-10-17 18:12:31,848 INFO [IPC Server handler 22 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.2531746
2015-10-17 18:12:31,988 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.2065626
2015-10-17 18:12:32,238 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.20175192
2015-10-17 18:12:32,410 INFO [IPC Server handler 10 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.5323719
2015-10-17 18:12:32,613 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.27825075
2015-10-17 18:12:32,676 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:12:32,676 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:12:33,207 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.27765483
2015-10-17 18:12:33,754 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.27811313
2015-10-17 18:12:34,238 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.2781602
2015-10-17 18:12:34,863 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.27813601
2015-10-17 18:12:34,988 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.3624012
2015-10-17 18:12:35,035 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.2741864
2015-10-17 18:12:35,238 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.27772525
2015-10-17 18:12:35,410 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.27776006
2015-10-17 18:12:35,645 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.34330806
2015-10-17 18:12:35,832 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.667
2015-10-17 18:12:36,223 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.27765483
2015-10-17 18:12:36,785 INFO [IPC Server handler 22 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.34034905
2015-10-17 18:12:37,238 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.2781602
2015-10-17 18:12:37,864 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.27813601
2015-10-17 18:12:38,067 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.2783809
2015-10-17 18:12:38,254 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.27772525
2015-10-17 18:12:38,379 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.3624012
2015-10-17 18:12:38,660 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.3638923
2015-10-17 18:12:38,848 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.667
2015-10-17 18:12:39,004 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.27776006
2015-10-17 18:12:39,223 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.27765483
2015-10-17 18:12:39,801 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.3637686
2015-10-17 18:12:40,254 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.2781602
2015-10-17 18:12:40,879 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.27813601
2015-10-17 18:12:41,082 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.2783809
2015-10-17 18:12:41,254 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.27772525
2015-10-17 18:12:41,692 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.3638923
2015-10-17 18:12:41,864 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.6735931
2015-10-17 18:12:42,254 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.31897187
2015-10-17 18:12:42,348 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.3624012
2015-10-17 18:12:42,707 INFO [IPC Server handler 22 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.27776006
2015-10-17 18:12:42,832 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.3637686
2015-10-17 18:12:43,270 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.32002404
2015-10-17 18:12:43,895 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.27813601
2015-10-17 18:12:44,114 INFO [IPC Server handler 28 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.2783809
2015-10-17 18:12:44,270 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.27772525
2015-10-17 18:12:44,708 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.3638923
2015-10-17 18:12:44,879 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.73180455
2015-10-17 18:12:45,254 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.36323506
2015-10-17 18:12:45,864 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.3637686
2015-10-17 18:12:45,926 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.3624012
2015-10-17 18:12:46,083 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.30548972
2015-10-17 18:12:46,286 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.36388028
2015-10-17 18:12:46,911 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.34025726
2015-10-17 18:12:47,130 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.33975956
2015-10-17 18:12:47,286 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.35610765
2015-10-17 18:12:47,739 INFO [IPC Server handler 22 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.39026338
2015-10-17 18:12:47,895 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.7970996
2015-10-17 18:12:48,270 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.36323506
2015-10-17 18:12:48,880 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.4074415
2015-10-17 18:12:49,286 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.36388028
2015-10-17 18:12:49,395 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.3624012
2015-10-17 18:12:49,661 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.33205736
2015-10-17 18:12:49,911 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.36390656
2015-10-17 18:12:50,161 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.36404583
2015-10-17 18:12:50,286 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.36317363
2015-10-17 18:12:50,770 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.44964966
2015-10-17 18:12:50,895 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.8776244
2015-10-17 18:12:51,270 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.36323506
2015-10-17 18:12:51,911 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.44950172
2015-10-17 18:12:52,286 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.36388028
2015-10-17 18:12:52,942 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.36390656
2015-10-17 18:12:53,192 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.36404583
2015-10-17 18:12:53,302 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.36317363
2015-10-17 18:12:53,349 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.37776577
2015-10-17 18:12:53,786 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.44964966
2015-10-17 18:12:53,911 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 0.98813033
2015-10-17 18:12:54,114 INFO [IPC Server handler 28 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.35690007
2015-10-17 18:12:54,286 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.38618836
2015-10-17 18:12:54,474 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000009_0 is : 1.0
2015-10-17 18:12:54,474 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0004_m_000009_0
2015-10-17 18:12:54,474 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:12:54,474 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0004_01_000011 taskAttempt attempt_1445076437777_0004_m_000009_0
2015-10-17 18:12:54,474 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0004_m_000009_0
2015-10-17 18:12:54,474 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:12:54,505 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:12:54,505 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0004_m_000009_0
2015-10-17 18:12:54,505 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:12:54,521 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 18:12:54,677 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 18:12:54,677 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:12:54,677 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 18:12:54,677 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-17 18:12:54,677 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 18:12:54,942 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.44950172
2015-10-17 18:12:55,005 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0004_m_000001
2015-10-17 18:12:55,005 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:12:55,005 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0004_m_000001
2015-10-17 18:12:55,005 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:55,005 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:12:55,005 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000001_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:12:55,286 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.38148576
2015-10-17 18:12:55,677 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 18:12:55,677 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0004: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:12:55,677 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0004_01_000011
2015-10-17 18:12:55,677 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 18:12:55,677 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0004_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:12:55,942 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.36390656
2015-10-17 18:12:56,224 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.36404583
2015-10-17 18:12:56,318 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.36317363
2015-10-17 18:12:56,586 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.40654695
2015-10-17 18:12:56,805 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.44964966
2015-10-17 18:12:57,305 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.4486067
2015-10-17 18:12:57,946 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.36319977
2015-10-17 18:12:57,961 INFO [IPC Server handler 28 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.44950172
2015-10-17 18:12:58,305 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.44968578
2015-10-17 18:12:58,946 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.4042818
2015-10-17 18:12:59,258 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.38862175
2015-10-17 18:12:59,336 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.41459236
2015-10-17 18:12:59,680 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.42338645
2015-10-17 18:12:59,836 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.5352825
2015-10-17 18:13:00,321 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.4486067
2015-10-17 18:13:00,993 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.53521925
2015-10-17 18:13:01,321 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.44968578
2015-10-17 18:13:01,805 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.36319977
2015-10-17 18:13:01,961 INFO [IPC Server handler 28 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.44950968
2015-10-17 18:13:02,274 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.44980705
2015-10-17 18:13:02,336 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.44859612
2015-10-17 18:13:02,852 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.5352825
2015-10-17 18:13:03,321 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.4486067
2015-10-17 18:13:04,024 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.53521925
2015-10-17 18:13:04,321 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.44968578
2015-10-17 18:13:04,571 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.43087813
2015-10-17 18:13:04,977 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.44950968
2015-10-17 18:13:05,305 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.44980705
2015-10-17 18:13:05,337 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.44859612
2015-10-17 18:13:05,571 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.36319977
2015-10-17 18:13:05,883 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.5352825
2015-10-17 18:13:06,321 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.4486067
2015-10-17 18:13:07,040 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.53521925
2015-10-17 18:13:07,321 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.44968578
2015-10-17 18:13:07,665 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.44162655
2015-10-17 18:13:07,993 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.44950968
2015-10-17 18:13:08,321 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.44980705
2015-10-17 18:13:08,337 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.44859612
2015-10-17 18:13:08,899 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.61003214
2015-10-17 18:13:09,165 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.36319977
2015-10-17 18:13:09,337 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.52868885
2015-10-17 18:13:10,056 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.58819306
2015-10-17 18:13:10,337 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.53428066
2015-10-17 18:13:10,712 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.44789755
2015-10-17 18:13:10,993 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.46699178
2015-10-17 18:13:11,337 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.46355015
2015-10-17 18:13:11,352 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.47992685
2015-10-17 18:13:11,931 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.620844
2015-10-17 18:13:12,353 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.5343203
2015-10-17 18:13:12,946 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.36319977
2015-10-17 18:13:13,103 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.6207798
2015-10-17 18:13:13,337 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.5352028
2015-10-17 18:13:13,821 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.44789755
2015-10-17 18:13:14,009 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.5352021
2015-10-17 18:13:14,353 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.5342037
2015-10-17 18:13:14,368 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.53543663
2015-10-17 18:13:14,946 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.620844
2015-10-17 18:13:15,353 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.5343203
2015-10-17 18:13:16,118 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.6207798
2015-10-17 18:13:16,353 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.5352028
2015-10-17 18:13:16,884 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.44789755
2015-10-17 18:13:16,915 INFO [IPC Server handler 22 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.36319977
2015-10-17 18:13:17,009 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.5352021
2015-10-17 18:13:17,384 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.5342037
2015-10-17 18:13:17,384 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.53543663
2015-10-17 18:13:17,962 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.620844
2015-10-17 18:13:18,353 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.5343203
2015-10-17 18:13:18,900 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.620844
2015-10-17 18:13:19,150 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.6207798
2015-10-17 18:13:19,353 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.5352028
2015-10-17 18:13:20,025 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.5352021
2015-10-17 18:13:20,400 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.5342037
2015-10-17 18:13:20,415 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.53543663
2015-10-17 18:13:20,728 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.36319977
2015-10-17 18:13:20,994 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.667
2015-10-17 18:13:21,040 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.6207798
2015-10-17 18:13:21,353 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.6002991
2015-10-17 18:13:21,415 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.46963483
2015-10-17 18:13:22,165 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.667
2015-10-17 18:13:22,369 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.6111065
2015-10-17 18:13:23,025 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.5352021
2015-10-17 18:13:23,416 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.5342037
2015-10-17 18:13:23,431 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.53543663
2015-10-17 18:13:24,009 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.667
2015-10-17 18:13:24,353 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.6199081
2015-10-17 18:13:24,541 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.36319977
2015-10-17 18:13:25,197 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.667
2015-10-17 18:13:25,369 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.6208445
2015-10-17 18:13:26,025 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.5631414
2015-10-17 18:13:26,416 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.6196791
2015-10-17 18:13:26,447 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.6018244
2015-10-17 18:13:27,025 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.67564535
2015-10-17 18:13:27,353 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.6199081
2015-10-17 18:13:27,556 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.49308378
2015-10-17 18:13:28,213 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.667
2015-10-17 18:13:28,306 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.36319977
2015-10-17 18:13:28,385 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.6208445
2015-10-17 18:13:29,025 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.6209487
2015-10-17 18:13:29,416 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.6196791
2015-10-17 18:13:29,478 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.6210422
2015-10-17 18:13:30,056 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.7107967
2015-10-17 18:13:30,353 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.6199081
2015-10-17 18:13:31,244 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.69946444
2015-10-17 18:13:31,385 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.6208445
2015-10-17 18:13:32,025 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.6209487
2015-10-17 18:13:32,166 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.36319977
2015-10-17 18:13:32,416 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.6196791
2015-10-17 18:13:32,510 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.6210422
2015-10-17 18:13:32,869 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.6199081
2015-10-17 18:13:33,072 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.74596524
2015-10-17 18:13:33,354 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.667
2015-10-17 18:13:33,525 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.6208445
2015-10-17 18:13:34,260 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.73549056
2015-10-17 18:13:34,385 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.667
2015-10-17 18:13:35,025 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.6209487
2015-10-17 18:13:35,416 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.62654096
2015-10-17 18:13:35,525 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.6210422
2015-10-17 18:13:35,604 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.38535082
2015-10-17 18:13:36,104 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.7822701
2015-10-17 18:13:36,354 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.667
2015-10-17 18:13:36,885 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.62654096
2015-10-17 18:13:37,276 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.77201843
2015-10-17 18:13:37,401 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.667
2015-10-17 18:13:38,041 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.65258867
2015-10-17 18:13:38,416 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.667
2015-10-17 18:13:38,557 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.6478274
2015-10-17 18:13:38,651 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.65258867
2015-10-17 18:13:39,119 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.81716555
2015-10-17 18:13:39,166 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.4162208
2015-10-17 18:13:39,276 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.6478274
2015-10-17 18:13:39,354 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.667
2015-10-17 18:13:40,135 INFO [Socket Reader #1 for port 39935] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0004 (auth:SIMPLE)
2015-10-17 18:13:40,182 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.53341997
2015-10-17 18:13:40,307 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.8072574
2015-10-17 18:13:40,401 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.667
2015-10-17 18:13:41,041 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.667
2015-10-17 18:13:41,416 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.667
2015-10-17 18:13:41,588 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.667
2015-10-17 18:13:42,151 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.851417
2015-10-17 18:13:42,354 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.6893234
2015-10-17 18:13:43,323 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.8418558
2015-10-17 18:13:43,385 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.44222376
2015-10-17 18:13:43,401 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.70644486
2015-10-17 18:13:43,698 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.53341997
2015-10-17 18:13:44,057 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.667
2015-10-17 18:13:44,417 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.667
2015-10-17 18:13:44,604 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.667
2015-10-17 18:13:45,182 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.8836691
2015-10-17 18:13:45,370 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.7270651
2015-10-17 18:13:46,338 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.86628914
2015-10-17 18:13:46,401 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.74053967
2015-10-17 18:13:47,073 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.667
2015-10-17 18:13:47,182 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.53341997
2015-10-17 18:13:47,276 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.448704
2015-10-17 18:13:47,448 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.6935864
2015-10-17 18:13:47,635 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.66712505
2015-10-17 18:13:48,214 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.8978398
2015-10-17 18:13:48,385 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.762497
2015-10-17 18:13:49,386 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.88073564
2015-10-17 18:13:49,417 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.7762504
2015-10-17 18:13:50,089 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.6892345
2015-10-17 18:13:50,464 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.7255711
2015-10-17 18:13:50,667 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.6819626
2015-10-17 18:13:50,667 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.53341997
2015-10-17 18:13:50,995 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.448704
2015-10-17 18:13:51,245 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.9146702
2015-10-17 18:13:51,401 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.79335797
2015-10-17 18:13:52,417 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.90058565
2015-10-17 18:13:52,433 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.81175065
2015-10-17 18:13:53,104 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.71706533
2015-10-17 18:13:53,479 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.7625072
2015-10-17 18:13:53,698 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.6990296
2015-10-17 18:13:54,104 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.53341997
2015-10-17 18:13:54,276 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.9393414
2015-10-17 18:13:54,417 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.82736623
2015-10-17 18:13:54,776 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.448704
2015-10-17 18:13:55,433 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.92999244
2015-10-17 18:13:55,448 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.84640414
2015-10-17 18:13:56,105 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.7461005
2015-10-17 18:13:56,495 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.801604
2015-10-17 18:13:56,730 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.71968657
2015-10-17 18:13:57,292 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 0.97455466
2015-10-17 18:13:57,417 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.86579496
2015-10-17 18:13:58,089 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.53341997
2015-10-17 18:13:58,448 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.88878244
2015-10-17 18:13:58,448 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 0.96751666
2015-10-17 18:13:58,823 INFO [IPC Server handler 10 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.448704
2015-10-17 18:13:59,120 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.77906317
2015-10-17 18:13:59,448 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000007_0 is : 1.0
2015-10-17 18:13:59,448 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0004_m_000007_0
2015-10-17 18:13:59,448 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:13:59,448 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0004_01_000009 taskAttempt attempt_1445076437777_0004_m_000007_0
2015-10-17 18:13:59,448 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0004_m_000007_0
2015-10-17 18:13:59,448 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:13:59,464 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:13:59,464 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0004_m_000007_0
2015-10-17 18:13:59,464 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:13:59,464 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 18:13:59,511 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.84425485
2015-10-17 18:13:59,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 18:13:59,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0004_01_000009
2015-10-17 18:13:59,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 18:13:59,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0004_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:13:59,745 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.7465927
2015-10-17 18:14:00,433 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.90834093
2015-10-17 18:14:01,105 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000008_0 is : 1.0
2015-10-17 18:14:01,105 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0004_m_000008_0
2015-10-17 18:14:01,105 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:14:01,105 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0004_01_000010 taskAttempt attempt_1445076437777_0004_m_000008_0
2015-10-17 18:14:01,105 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0004_m_000008_0
2015-10-17 18:14:01,105 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:14:01,105 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:14:01,105 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0004_m_000008_0
2015-10-17 18:14:01,105 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:14:01,105 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 18:14:01,402 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.53341997
2015-10-17 18:14:01,464 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.9385601
2015-10-17 18:14:01,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 18:14:02,136 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.80938077
2015-10-17 18:14:02,527 INFO [IPC Server handler 29 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.88778687
2015-10-17 18:14:02,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0004_01_000010
2015-10-17 18:14:02,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:7 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 18:14:02,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0004_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:14:02,777 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.7704954
2015-10-17 18:14:02,902 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.448704
2015-10-17 18:14:03,449 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.94711673
2015-10-17 18:14:04,480 INFO [IPC Server handler 23 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 0.9713453
2015-10-17 18:14:04,636 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.53341997
2015-10-17 18:14:04,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:14:04,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 18:14:04,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0004_01_000012 to attempt_1445076437777_0004_r_000000_0
2015-10-17 18:14:04,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 18:14:04,699 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:14:04,699 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:14:04,699 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0004_01_000012 taskAttempt attempt_1445076437777_0004_r_000000_0
2015-10-17 18:14:04,699 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0004_r_000000_0
2015-10-17 18:14:04,699 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 18:14:05,152 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.83999544
2015-10-17 18:14:05,433 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0004_r_000000_0 : 13562
2015-10-17 18:14:05,433 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0004_r_000000_0] using containerId: [container_1445076437777_0004_01_000012 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 18:14:05,433 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:14:05,433 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0004_r_000000
2015-10-17 18:14:05,433 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:14:05,543 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.9220189
2015-10-17 18:14:05,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0004: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:14:05,808 INFO [IPC Server handler 10 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.7871715
2015-10-17 18:14:06,464 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 0.9795303
2015-10-17 18:14:06,714 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.448704
2015-10-17 18:14:07,089 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000004_0 is : 1.0
2015-10-17 18:14:07,089 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0004_m_000004_0
2015-10-17 18:14:07,089 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:14:07,089 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0004_01_000006 taskAttempt attempt_1445076437777_0004_m_000004_0
2015-10-17 18:14:07,089 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0004_m_000004_0
2015-10-17 18:14:07,089 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:14:07,105 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:14:07,105 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0004_m_000004_0
2015-10-17 18:14:07,105 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:14:07,105 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 18:14:07,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 18:14:07,683 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.54942715
2015-10-17 18:14:08,168 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.8690481
2015-10-17 18:14:08,558 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.95512354
2015-10-17 18:14:08,558 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000003_0 is : 1.0
2015-10-17 18:14:08,558 INFO [IPC Server handler 10 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0004_m_000003_0
2015-10-17 18:14:08,558 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:14:08,558 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0004_01_000005 taskAttempt attempt_1445076437777_0004_m_000003_0
2015-10-17 18:14:08,558 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0004_m_000003_0
2015-10-17 18:14:08,558 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:14:08,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:14:08,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0004_m_000003_0
2015-10-17 18:14:08,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:14:08,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 18:14:08,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:8 RackLocal:2
2015-10-17 18:14:08,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0004_01_000006
2015-10-17 18:14:08,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:14:08,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0004_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:14:08,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0004_01_000013 to attempt_1445076437777_0004_m_000001_1
2015-10-17 18:14:08,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-17 18:14:08,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:14:08,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000001_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:14:08,683 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0004_01_000013 taskAttempt attempt_1445076437777_0004_m_000001_1
2015-10-17 18:14:08,683 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0004_m_000001_1
2015-10-17 18:14:08,683 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:14:08,699 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0004_m_000001_1 : 13562
2015-10-17 18:14:08,699 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0004_m_000001_1] using containerId: [container_1445076437777_0004_01_000013 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:14:08,699 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000001_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:14:08,699 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0004_m_000001
2015-10-17 18:14:08,824 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.8089701
2015-10-17 18:14:09,639 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0004_m_000000
2015-10-17 18:14:09,639 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:14:09,639 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0004_m_000000
2015-10-17 18:14:09,639 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:14:09,639 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:14:09,639 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:14:09,686 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-17 18:14:09,686 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0004: ask=4 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 18:14:09,686 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0004_01_000005
2015-10-17 18:14:09,686 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:9 RackLocal:2
2015-10-17 18:14:09,686 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0004_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:14:10,686 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:14:10,686 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0004_01_000014 to attempt_1445076437777_0004_m_000000_1
2015-10-17 18:14:10,686 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 18:14:10,686 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:14:10,686 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:14:10,686 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0004_01_000014 taskAttempt attempt_1445076437777_0004_m_000000_1
2015-10-17 18:14:10,686 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0004_m_000000_1
2015-10-17 18:14:10,686 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:14:10,702 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0004_m_000000_1 : 13562
2015-10-17 18:14:10,702 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0004_m_000000_1] using containerId: [container_1445076437777_0004_01_000014 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:14:10,702 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:14:10,702 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0004_m_000000
2015-10-17 18:14:10,749 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.5611509
2015-10-17 18:14:11,186 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.9000281
2015-10-17 18:14:11,221 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_0 is : 0.448704
2015-10-17 18:14:11,580 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 0.987139
2015-10-17 18:14:11,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0004: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:14:11,783 INFO [Socket Reader #1 for port 39935] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0004 (auth:SIMPLE)
2015-10-17 18:14:11,799 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0004_m_000013 asked for a task
2015-10-17 18:14:11,799 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0004_m_000013 given task: attempt_1445076437777_0004_m_000001_1
2015-10-17 18:14:11,846 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.8376344
2015-10-17 18:14:12,080 INFO [Socket Reader #1 for port 39935] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 39935: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 18:14:12,783 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000002_0 is : 1.0
2015-10-17 18:14:12,783 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0004_m_000002_0
2015-10-17 18:14:12,783 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:14:12,783 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0004_01_000004 taskAttempt attempt_1445076437777_0004_m_000002_0
2015-10-17 18:14:12,783 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0004_m_000002_0
2015-10-17 18:14:12,799 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:14:12,799 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:14:12,799 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0004_m_000002_0
2015-10-17 18:14:12,799 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:14:12,799 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 18:14:13,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 18:14:13,783 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.5745042
2015-10-17 18:14:14,018 INFO [Socket Reader #1 for port 39935] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0004 (auth:SIMPLE)
2015-10-17 18:14:14,049 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0004_m_000014 asked for a task
2015-10-17 18:14:14,049 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0004_m_000014 given task: attempt_1445076437777_0004_m_000000_1
2015-10-17 18:14:14,205 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.9285215
2015-10-17 18:14:14,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0004_01_000004
2015-10-17 18:14:14,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 18:14:14,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0004_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:14:14,862 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.8735856
2015-10-17 18:14:16,877 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.5803661
2015-10-17 18:14:17,221 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.96234864
2015-10-17 18:14:17,893 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.9103091
2015-10-17 18:14:19,143 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.1066108
2015-10-17 18:14:19,924 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.58622855
2015-10-17 18:14:20,237 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 0.99510413
2015-10-17 18:14:20,706 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000005_0 is : 1.0
2015-10-17 18:14:20,706 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0004_m_000005_0
2015-10-17 18:14:20,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:14:20,706 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0004_01_000007 taskAttempt attempt_1445076437777_0004_m_000005_0
2015-10-17 18:14:20,706 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0004_m_000005_0
2015-10-17 18:14:20,706 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:14:20,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:14:20,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0004_m_000005_0
2015-10-17 18:14:20,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:14:20,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 18:14:20,909 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.9468543
2015-10-17 18:14:21,268 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.10635664
2015-10-17 18:14:21,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 18:14:22,143 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.1066108
2015-10-17 18:14:22,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0004_01_000007
2015-10-17 18:14:22,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 18:14:22,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0004_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:14:22,987 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.58785754
2015-10-17 18:14:23,924 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 0.9827361
2015-10-17 18:14:24,268 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.10635664
2015-10-17 18:14:25,159 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.1066108
2015-10-17 18:14:25,471 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000006_0 is : 1.0
2015-10-17 18:14:25,471 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0004_m_000006_0
2015-10-17 18:14:25,471 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:14:25,471 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0004_01_000008 taskAttempt attempt_1445076437777_0004_m_000006_0
2015-10-17 18:14:25,471 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0004_m_000006_0
2015-10-17 18:14:25,471 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:14:25,487 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:14:25,487 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0004_m_000006_0
2015-10-17 18:14:25,487 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:14:25,487 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 18:14:25,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 18:14:26,081 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.59893143
2015-10-17 18:14:26,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0004_01_000008
2015-10-17 18:14:26,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 18:14:26,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0004_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:14:27,268 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.10635664
2015-10-17 18:14:28,159 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.1352101
2015-10-17 18:14:30,222 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.61050177
2015-10-17 18:14:30,284 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.10635664
2015-10-17 18:14:31,159 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.19211523
2015-10-17 18:14:33,284 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.18636206
2015-10-17 18:14:33,816 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.61898744
2015-10-17 18:14:34,159 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.19211523
2015-10-17 18:14:36,300 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.19158794
2015-10-17 18:14:37,159 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.19211523
2015-10-17 18:14:37,347 INFO [IPC Server handler 22 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.61898744
2015-10-17 18:14:39,316 INFO [IPC Server handler 22 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.19158794
2015-10-17 18:14:40,191 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.20283933
2015-10-17 18:14:40,785 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.61898744
2015-10-17 18:14:42,332 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.20227435
2015-10-17 18:14:43,207 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.2715943
2015-10-17 18:14:44,285 INFO [IPC Server handler 22 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.61898744
2015-10-17 18:14:45,332 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.27696857
2015-10-17 18:14:46,222 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.27776006
2015-10-17 18:14:47,535 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.61898744
2015-10-17 18:14:48,332 INFO [IPC Server handler 22 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.27696857
2015-10-17 18:14:49,238 INFO [IPC Server handler 11 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.27776006
2015-10-17 18:14:50,769 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.61898744
2015-10-17 18:14:51,332 INFO [IPC Server handler 22 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.27696857
2015-10-17 18:14:52,254 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.27776006
2015-10-17 18:14:54,035 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.6349931
2015-10-17 18:14:54,348 INFO [IPC Server handler 22 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.29795188
2015-10-17 18:14:55,254 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.28948176
2015-10-17 18:14:57,114 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.6656968
2015-10-17 18:14:57,364 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.3586832
2015-10-17 18:14:58,254 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.36319977
2015-10-17 18:14:58,910 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.6656968
2015-10-17 18:15:00,270 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.667
2015-10-17 18:15:00,364 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.3624012
2015-10-17 18:15:01,286 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.36319977
2015-10-17 18:15:03,379 INFO [IPC Server handler 22 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.667
2015-10-17 18:15:03,395 INFO [IPC Server handler 3 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.3624012
2015-10-17 18:15:04,301 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.36319977
2015-10-17 18:15:06,395 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.3624012
2015-10-17 18:15:06,442 INFO [IPC Server handler 20 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.667
2015-10-17 18:15:07,317 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.37214804
2015-10-17 18:15:09,395 INFO [IPC Server handler 18 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.42979714
2015-10-17 18:15:09,489 INFO [IPC Server handler 25 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.667
2015-10-17 18:15:10,333 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.4373971
2015-10-17 18:15:12,395 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.44789755
2015-10-17 18:15:12,567 INFO [IPC Server handler 19 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.66828805
2015-10-17 18:15:13,333 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.448704
2015-10-17 18:15:15,411 INFO [IPC Server handler 21 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.44789755
2015-10-17 18:15:15,614 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.68720716
2015-10-17 18:15:16,349 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.448704
2015-10-17 18:15:18,411 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.44789755
2015-10-17 18:15:18,693 INFO [IPC Server handler 17 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.706445
2015-10-17 18:15:19,365 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.448704
2015-10-17 18:15:21,427 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.5211366
2015-10-17 18:15:21,740 INFO [IPC Server handler 28 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.72797775
2015-10-17 18:15:22,380 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.50179666
2015-10-17 18:15:24,427 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.53341997
2015-10-17 18:15:24,802 INFO [IPC Server handler 9 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.74906975
2015-10-17 18:15:25,380 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.53425497
2015-10-17 18:15:27,443 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.53341997
2015-10-17 18:15:27,865 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.770159
2015-10-17 18:15:28,381 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.53425497
2015-10-17 18:15:30,459 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.54810727
2015-10-17 18:15:30,928 INFO [IPC Server handler 10 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.79190993
2015-10-17 18:15:31,396 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.5353727
2015-10-17 18:15:33,459 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.61898744
2015-10-17 18:15:34,006 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.81342626
2015-10-17 18:15:34,412 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.6038154
2015-10-17 18:15:36,459 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.61898744
2015-10-17 18:15:37,053 INFO [IPC Server handler 8 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.8295482
2015-10-17 18:15:37,412 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.6197233
2015-10-17 18:15:39,459 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.61898744
2015-10-17 18:15:40,131 INFO [IPC Server handler 6 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.8509954
2015-10-17 18:15:40,412 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.6197233
2015-10-17 18:15:41,444 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.61898744
2015-10-17 18:15:42,459 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.667
2015-10-17 18:15:43,272 INFO [IPC Server handler 0 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.8706094
2015-10-17 18:15:43,428 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.62798053
2015-10-17 18:15:45,053 INFO [IPC Server handler 26 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.62798053
2015-10-17 18:15:45,475 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.667
2015-10-17 18:15:46,350 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.89063454
2015-10-17 18:15:46,428 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.667
2015-10-17 18:15:48,475 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.667
2015-10-17 18:15:49,444 INFO [IPC Server handler 7 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.667
2015-10-17 18:15:49,460 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.9105083
2015-10-17 18:15:51,475 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.6905174
2015-10-17 18:15:52,460 INFO [IPC Server handler 13 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.667
2015-10-17 18:15:52,507 INFO [IPC Server handler 14 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.93296766
2015-10-17 18:15:54,491 INFO [IPC Server handler 4 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.7403984
2015-10-17 18:15:55,476 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.67696357
2015-10-17 18:15:55,538 INFO [IPC Server handler 27 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.9631624
2015-10-17 18:15:57,491 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_1 is : 0.79596704
2015-10-17 18:15:58,491 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.70587
2015-10-17 18:15:58,585 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 0.99137783
2015-10-17 18:15:59,570 INFO [IPC Server handler 24 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000000_0 is : 1.0
2015-10-17 18:15:59,570 INFO [IPC Server handler 22 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0004_m_000000_0
2015-10-17 18:15:59,570 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:15:59,570 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0004_01_000002 taskAttempt attempt_1445076437777_0004_m_000000_0
2015-10-17 18:15:59,570 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0004_m_000000_0
2015-10-17 18:15:59,585 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:15:59,601 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:15:59,601 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0004_m_000000_0
2015-10-17 18:15:59,601 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445076437777_0004_m_000000_1
2015-10-17 18:15:59,601 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:15:59,601 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 18:15:59,616 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000000_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 18:15:59,616 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0004_01_000014 taskAttempt attempt_1445076437777_0004_m_000000_1
2015-10-17 18:15:59,616 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0004_m_000000_1
2015-10-17 18:15:59,616 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:15:59,632 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000000_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 18:15:59,632 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 18:15:59,632 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out1/_temporary/1/_temporary/attempt_1445076437777_0004_m_000000_1
2015-10-17 18:15:59,648 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000000_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 18:15:59,663 INFO [Socket Reader #1 for port 39935] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 39935: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 18:15:59,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 18:16:00,698 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0004_01_000014
2015-10-17 18:16:00,698 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0004_01_000002
2015-10-17 18:16:00,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0004_m_000000_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:16:00,698 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 18:16:00,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0004_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:16:01,495 INFO [IPC Server handler 1 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.73967266
2015-10-17 18:16:04,513 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.77085495
2015-10-17 18:16:07,529 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.80050373
2015-10-17 18:16:10,545 INFO [IPC Server handler 12 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.8343213
2015-10-17 18:16:13,545 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.8692273
2015-10-17 18:16:16,545 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.915382
2015-10-17 18:16:19,545 INFO [IPC Server handler 15 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.9581797
2015-10-17 18:16:22,561 INFO [IPC Server handler 5 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 0.9956161
2015-10-17 18:16:23,139 INFO [IPC Server handler 2 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0004_m_000001_1 is : 1.0
2015-10-17 18:16:23,155 INFO [IPC Server handler 16 on 39935] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0004_m_000001_1
2015-10-17 18:16:23,155 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000001_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:16:23,155 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0004_01_000013 taskAttempt attempt_1445076437777_0004_m_000001_1
2015-10-17 18:16:23,155 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0004_m_000001_1
2015-10-17 18:16:23,155 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:16:23,155 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000001_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:16:23,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0004_m_000001_1
2015-10-17 18:16:23,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445076437777_0004_m_000001_0
2015-10-17 18:16:23,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0004_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:16:23,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 18:16:23,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_m_000001_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 18:16:23,170 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0004_01_000003 taskAttempt attempt_1445076437777_0004_m_000001_0
2015-10-17 18:16:23,170 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0004_m_000001_0
2015-10-17 18:16:23,170 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 18:16:23,702 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 18:16:24,108 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0004_m_000001
2015-10-17 18:16:24,108 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:16:24,702 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0004_01_000013
2015-10-17 18:16:24,702 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 18:16:24,702 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0004_m_000001_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:16:39,124 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0004_m_000001
2015-10-17 18:16:39,124 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:16:43,171 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 0 time(s); maxRetries=45
2015-10-17 18:17:03,172 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 1 time(s); maxRetries=45
2015-10-17 18:17:23,176 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 2 time(s); maxRetries=45
2015-10-17 18:17:43,177 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 3 time(s); maxRetries=45
2015-10-17 18:18:03,178 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 4 time(s); maxRetries=45
2015-10-17 18:18:23,178 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 5 time(s); maxRetries=45
2015-10-17 18:18:43,179 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 6 time(s); maxRetries=45
2015-10-17 18:19:03,180 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 7 time(s); maxRetries=45
2015-10-17 18:19:23,181 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 8 time(s); maxRetries=45
2015-10-17 18:19:43,186 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 9 time(s); maxRetries=45
2015-10-17 18:20:03,187 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 10 time(s); maxRetries=45
2015-10-17 18:20:23,187 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 11 time(s); maxRetries=45
2015-10-17 18:20:43,193 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 12 time(s); maxRetries=45
2015-10-17 18:21:03,194 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 13 time(s); maxRetries=45
2015-10-17 18:21:23,199 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 14 time(s); maxRetries=45
2015-10-17 18:21:43,201 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 15 time(s); maxRetries=45
2015-10-17 18:22:03,202 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 16 time(s); maxRetries=45
2015-10-17 18:22:23,203 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 17 time(s); maxRetries=45
2015-10-17 18:22:43,204 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 18 time(s); maxRetries=45
2015-10-17 18:23:03,213 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 19 time(s); maxRetries=45
2015-10-17 18:23:23,214 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 20 time(s); maxRetries=45
2015-10-17 18:23:43,216 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 21 time(s); maxRetries=45
2015-10-17 18:24:03,217 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 22 time(s); maxRetries=45
2015-10-17 18:24:23,222 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 23 time(s); maxRetries=45
2015-10-17 18:24:26,300 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0004_r_000000_0: AttemptID:attempt_1445076437777_0004_r_000000_0 Timed out after 600 secs
2015-10-17 18:24:26,300 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0004_r_000000_0 TaskAttempt Transitioned from RUNNING to FAIL_CONTAINER_CLEANUP
2015-10-17 18:24:26,300 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0004_01_000012 taskAttempt attempt_1445076437777_0004_r_000000_0
2015-10-17 18:24:26,300 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0004_r_000000_0
2015-10-17 18:24:26,300 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 18:24:43,222 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 24 time(s); maxRetries=45
2015-10-17 18:24:46,301 INFO [ContainerLauncher #5] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 0 time(s); maxRetries=45
2015-10-17 18:25:03,237 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 25 time(s); maxRetries=45
2015-10-17 18:25:06,315 INFO [ContainerLauncher #5] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 1 time(s); maxRetries=45
2015-10-17 18:25:23,239 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 26 time(s); maxRetries=45
2015-10-17 18:25:26,317 INFO [ContainerLauncher #5] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 2 time(s); maxRetries=45
2015-10-17 18:25:43,240 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 27 time(s); maxRetries=45
2015-10-17 18:25:46,318 INFO [ContainerLauncher #5] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 3 time(s); maxRetries=45
2015-10-17 18:26:03,241 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 28 time(s); maxRetries=45
2015-10-17 18:26:06,319 INFO [ContainerLauncher #5] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 4 time(s); maxRetries=45
2015-10-17 18:26:23,242 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 29 time(s); maxRetries=45
2015-10-17 18:26:26,320 INFO [ContainerLauncher #5] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 5 time(s); maxRetries=45
2015-10-17 18:26:43,244 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 30 time(s); maxRetries=45
2015-10-17 18:26:46,322 INFO [ContainerLauncher #5] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 6 time(s); maxRetries=45
2015-10-17 18:27:03,247 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 31 time(s); maxRetries=45
2015-10-17 18:27:06,325 INFO [ContainerLauncher #5] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 7 time(s); maxRetries=45
2015-10-17 18:27:16,325 INFO [ContainerLauncher #5] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 0 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 18:27:18,794 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445076437777_0004_m_000001_0 because it is running on unusable node:MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 18:27:18,794 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445076437777_0004_r_000000_0 because it is running on unusable node:MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 18:27:18,794 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0004_01_000003
2015-10-17 18:27:18,794 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0004_01_000012
2015-10-17 18:27:18,794 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0004_m_000001_0: Container released on a *lost* node
2015-10-17 18:27:18,794 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:10 RackLocal:2
2015-10-17 18:27:18,794 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0004_r_000000_0: Container released on a *lost* node
2015-10-17 18:27:23,248 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 32 time(s); maxRetries=45
2015-10-17 18:27:35,911 INFO [ContainerLauncher #5] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 1 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 18:27:42,771 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 0 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 18:27:55,475 INFO [ContainerLauncher #5] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 2 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 18:28:02,272 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 1 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 18:28:14,991 INFO [ContainerLauncher #5] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 3 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 18:28:21,788 INFO [ContainerLauncher #4] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 2 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 18:28:34,512 INFO [ContainerLauncher #5] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 4 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
