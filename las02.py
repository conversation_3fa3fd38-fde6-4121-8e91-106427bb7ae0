import lasio
import matplotlib.pyplot as plt

las = lasio.read("C:\\Users\\<USER>\\OneDrive\\Desktop\\ONGC\\Project1\\LAS_Sample_Data\\49-005-30258.las")

print(las.header)

print(las.well)

print(las.curves)

# Convert log data to a DataFrame
df = las.df()
print(df.head())

# Example: plot Gamma Ray log (GR)
plt.plot(df['GR'], df.index)
plt.gca().invert_yaxis()
plt.xlabel("Gamma Ray (API)")
plt.ylabel("Depth (m)")
plt.title("GR Log - 49-005-37851")
plt.grid()
plt.show()
