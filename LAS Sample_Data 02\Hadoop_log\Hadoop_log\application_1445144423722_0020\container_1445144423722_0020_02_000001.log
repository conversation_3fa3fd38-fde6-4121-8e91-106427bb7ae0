2015-10-18 18:20:33,799 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445144423722_0020_000002
2015-10-18 18:20:34,153 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-18 18:20:34,153 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 20 cluster_timestamp: 1445144423722 } attemptId: 2 } keyId: -127633188)
2015-10-18 18:20:34,290 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-18 18:20:34,895 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-18 18:20:34,944 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-18 18:20:34,972 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-18 18:20:34,973 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-18 18:20:34,974 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-18 18:20:34,975 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-18 18:20:34,976 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-18 18:20:34,982 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-18 18:20:34,982 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-18 18:20:34,984 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-18 18:20:35,023 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:20:35,044 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:20:35,065 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:20:35,074 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-18 18:20:35,077 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Recovery is enabled. Will try to recover from previous life on best effort basis.
2015-10-18 18:20:35,098 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:20:35,101 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Previous history file is at hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0020/job_1445144423722_0020_1.jhist
2015-10-18 18:20:35,857 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read from history task task_1445144423722_0020_m_000003
2015-10-18 18:20:35,857 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Read completed tasks from history 1
2015-10-18 18:20:35,894 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-18 18:20:35,937 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 18:20:35,988 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 18:20:35,988 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-18 18:20:35,995 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445144423722_0020 to jobTokenSecretManager
2015-10-18 18:20:36,041 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445144423722_0020 because: not enabled; too many maps; too much input;
2015-10-18 18:20:36,059 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445144423722_0020 = 1256521728. Number of splits = 10
2015-10-18 18:20:36,060 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445144423722_0020 = 1
2015-10-18 18:20:36,060 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0020Job Transitioned from NEW to INITED
2015-10-18 18:20:36,062 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445144423722_0020.
2015-10-18 18:20:36,093 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-18 18:20:36,103 INFO [Socket Reader #1 for port 30597] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 30597
2015-10-18 18:20:36,125 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-18 18:20:36,125 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-18 18:20:36,125 INFO [IPC Server listener on 30597] org.apache.hadoop.ipc.Server: IPC Server listener on 30597: starting
2015-10-18 18:20:36,126 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-41.fareast.corp.microsoft.com/**************:30597
2015-10-18 18:20:36,192 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-18 18:20:36,196 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-18 18:20:36,205 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-18 18:20:36,210 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-18 18:20:36,210 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-18 18:20:36,213 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-18 18:20:36,213 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-18 18:20:36,222 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 30604
2015-10-18 18:20:36,222 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-18 18:20:36,251 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\4\Jetty_0_0_0_0_30604_mapreduce____.k5qrms\webapp
2015-10-18 18:20:36,393 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:30604
2015-10-18 18:20:36,393 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 30604
2015-10-18 18:20:36,716 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-18 18:20:36,720 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445144423722_0020
2015-10-18 18:20:36,721 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-18 18:20:36,725 INFO [Socket Reader #1 for port 30607] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 30607
2015-10-18 18:20:36,730 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-18 18:20:36,730 INFO [IPC Server listener on 30607] org.apache.hadoop.ipc.Server: IPC Server listener on 30607: starting
2015-10-18 18:20:36,748 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-18 18:20:36,748 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-18 18:20:36,748 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-18 18:20:36,793 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at MSRA-SA-41/**************:8030
2015-10-18 18:20:36,853 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-18 18:20:36,853 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-18 18:20:36,856 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-18 18:20:36,858 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-18 18:20:36,860 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0020Job Transitioned from INITED to SETUP
2015-10-18 18:20:36,862 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-18 18:20:36,870 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0020Job Transitioned from SETUP to RUNNING
2015-10-18 18:20:36,886 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,887 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,888 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:20:36,889 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,889 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,889 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:20:36,889 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,889 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,889 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:20:36,889 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Recovering task task_1445144423722_0020_m_000003 from prior app attempt, status was SUCCEEDED
2015-10-18 18:20:36,890 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,890 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,898 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,899 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000003_0] using containerId: [container_1445144423722_0020_01_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:7109]
2015-10-18 18:20:36,903 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000003_0 TaskAttempt Transitioned from NEW to SUCCEEDED
2015-10-18 18:20:36,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0020_m_000003_0
2015-10-18 18:20:36,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000003 Task Transitioned from NEW to SUCCEEDED
2015-10-18 18:20:36,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,910 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:20:36,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:20:36,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:20:36,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:20:36,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:20:36,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:36,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:20:36,913 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:20:36,913 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445144423722_0020, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0020/job_1445144423722_0020_2.jhist
2015-10-18 18:20:36,914 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000000_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:20:36,914 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000001_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:20:36,914 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000002_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:20:36,919 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-18 18:20:36,919 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000004_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:20:36,919 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000005_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:20:36,919 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000006_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:20:36,919 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000007_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:20:36,919 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000008_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:20:36,919 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000009_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:20:36,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_r_000000_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:20:36,944 INFO [Thread-51] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-18 18:20:36,950 INFO [Thread-51] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-18 18:20:37,855 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-18 18:20:37,929 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-13> knownNMs=3
2015-10-18 18:20:37,937 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-13>
2015-10-18 18:20:37,937 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-18 18:20:37,938 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:38,954 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 4
2015-10-18 18:20:38,957 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000002 to attempt_1445144423722_0020_m_000000_1000
2015-10-18 18:20:38,957 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000003 to attempt_1445144423722_0020_m_000001_1000
2015-10-18 18:20:38,958 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000004 to attempt_1445144423722_0020_m_000002_1000
2015-10-18 18:20:38,958 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000005 to attempt_1445144423722_0020_m_000004_1000
2015-10-18 18:20:38,959 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-17>
2015-10-18 18:20:38,959 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:38,959 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:4 RackLocal:0
2015-10-18 18:20:39,007 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:39,037 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0020/job.jar
2015-10-18 18:20:39,042 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0020/job.xml
2015-10-18 18:20:39,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-18 18:20:39,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-18 18:20:39,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-18 18:20:39,111 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000000_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:20:39,112 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:39,112 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000001_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:20:39,112 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:39,113 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000002_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:20:39,113 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:39,114 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000004_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:20:39,115 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000002 taskAttempt attempt_1445144423722_0020_m_000000_1000
2015-10-18 18:20:39,115 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000003 taskAttempt attempt_1445144423722_0020_m_000001_1000
2015-10-18 18:20:39,115 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000004 taskAttempt attempt_1445144423722_0020_m_000002_1000
2015-10-18 18:20:39,115 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000005 taskAttempt attempt_1445144423722_0020_m_000004_1000
2015-10-18 18:20:39,117 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000001_1000
2015-10-18 18:20:39,117 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000000_1000
2015-10-18 18:20:39,117 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000004_1000
2015-10-18 18:20:39,117 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000002_1000
2015-10-18 18:20:39,118 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:20:39,135 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:20:39,136 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:20:39,136 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:20:39,175 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000000_1000 : 13562
2015-10-18 18:20:39,175 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000004_1000 : 13562
2015-10-18 18:20:39,175 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000002_1000 : 13562
2015-10-18 18:20:39,175 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000001_1000 : 13562
2015-10-18 18:20:39,176 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000001_1000] using containerId: [container_1445144423722_0020_02_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:20:39,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000001_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:20:39,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000000_1000] using containerId: [container_1445144423722_0020_02_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:20:39,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000000_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:20:39,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000002_1000] using containerId: [container_1445144423722_0020_02_000004 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:20:39,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000002_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:20:39,178 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000004_1000] using containerId: [container_1445144423722_0020_02_000005 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:20:39,178 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000004_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:20:39,178 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000001
2015-10-18 18:20:39,178 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:20:39,178 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000000
2015-10-18 18:20:39,178 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:20:39,179 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000002
2015-10-18 18:20:39,179 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:20:39,179 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000004
2015-10-18 18:20:39,179 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:20:39,960 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-17> knownNMs=3
2015-10-18 18:20:39,960 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-17>
2015-10-18 18:20:39,961 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:40,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-17>
2015-10-18 18:20:40,962 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:41,964 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-17>
2015-10-18 18:20:41,965 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:42,220 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:20:42,226 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:20:42,256 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000003 asked for a task
2015-10-18 18:20:42,256 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000003 given task: attempt_1445144423722_0020_m_000001_1000
2015-10-18 18:20:42,257 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000004 asked for a task
2015-10-18 18:20:42,257 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000004 given task: attempt_1445144423722_0020_m_000002_1000
2015-10-18 18:20:42,460 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:20:42,483 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000005 asked for a task
2015-10-18 18:20:42,483 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000005 given task: attempt_1445144423722_0020_m_000004_1000
2015-10-18 18:20:42,521 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:20:42,543 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000002 asked for a task
2015-10-18 18:20:42,543 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000002 given task: attempt_1445144423722_0020_m_000000_1000
2015-10-18 18:20:42,972 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:20:42,973 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:42,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000006 to attempt_1445144423722_0020_m_000005_1000
2015-10-18 18:20:42,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-18>
2015-10-18 18:20:42,975 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:42,975 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:4 RackLocal:1
2015-10-18 18:20:42,975 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:42,976 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000005_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:20:42,978 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000006 taskAttempt attempt_1445144423722_0020_m_000005_1000
2015-10-18 18:20:42,978 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000005_1000
2015-10-18 18:20:42,978 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:20:43,567 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000005_1000 : 13562
2015-10-18 18:20:43,567 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000005_1000] using containerId: [container_1445144423722_0020_02_000006 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:20:43,567 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000005_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:20:43,568 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000005
2015-10-18 18:20:43,568 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:20:43,976 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-18> knownNMs=3
2015-10-18 18:20:43,976 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-18>
2015-10-18 18:20:43,976 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:44,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-18>
2015-10-18 18:20:44,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:45,985 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:20:45,985 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:45,986 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000007 to attempt_1445144423722_0020_m_000006_1000
2015-10-18 18:20:45,986 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-19>
2015-10-18 18:20:45,986 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:45,986 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:45,986 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:4 RackLocal:2
2015-10-18 18:20:45,988 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000006_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:20:45,989 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000007 taskAttempt attempt_1445144423722_0020_m_000006_1000
2015-10-18 18:20:45,989 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000006_1000
2015-10-18 18:20:45,989 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:20:46,441 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000006_1000 : 13562
2015-10-18 18:20:46,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000006_1000] using containerId: [container_1445144423722_0020_02_000007 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:20:46,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000006_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:20:46,443 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000006
2015-10-18 18:20:46,443 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:20:46,987 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-19> knownNMs=3
2015-10-18 18:20:46,987 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-19>
2015-10-18 18:20:46,988 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:47,990 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-19>
2015-10-18 18:20:47,991 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:48,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:20:48,996 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:48,997 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000008 to attempt_1445144423722_0020_m_000007_1000
2015-10-18 18:20:48,997 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:20:48,997 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:48,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:48,997 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:4 RackLocal:3
2015-10-18 18:20:48,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000007_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:20:49,000 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000008 taskAttempt attempt_1445144423722_0020_m_000007_1000
2015-10-18 18:20:49,000 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000007_1000
2015-10-18 18:20:49,001 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:20:49,686 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.10660437
2015-10-18 18:20:49,738 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.1066108
2015-10-18 18:20:49,951 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.10680563
2015-10-18 18:20:49,953 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.10635664
2015-10-18 18:20:50,000 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:20:50,001 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:20:50,001 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:50,216 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000007_1000 : 13562
2015-10-18 18:20:50,217 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000007_1000] using containerId: [container_1445144423722_0020_02_000008 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:20:50,218 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000007_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:20:50,218 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000007
2015-10-18 18:20:50,218 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:20:51,003 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:20:51,004 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:52,006 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:20:52,006 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:52,696 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.10660437
2015-10-18 18:20:52,743 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.1066108
2015-10-18 18:20:52,962 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.10680563
2015-10-18 18:20:52,967 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.10635664
2015-10-18 18:20:53,008 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:20:53,008 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:54,010 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:20:54,011 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:55,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:20:55,014 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-13> finalMapResourceLimit:<memory:6452, vCores:-12> finalReduceResourceLimit:<memory:716, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:55,703 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.10660437
2015-10-18 18:20:55,754 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.1066108
2015-10-18 18:20:55,969 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.10680563
2015-10-18 18:20:55,983 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.10635664
2015-10-18 18:20:56,019 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:20:56,019 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000009 to attempt_1445144423722_0020_m_000008_1000
2015-10-18 18:20:56,020 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:20:56,020 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-12> finalMapResourceLimit:<memory:7373, vCores:-11> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:56,020 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:20:56,020 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:5 RackLocal:3
2015-10-18 18:20:56,021 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000008_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:20:56,023 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000009 taskAttempt attempt_1445144423722_0020_m_000008_1000
2015-10-18 18:20:56,023 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000008_1000
2015-10-18 18:20:56,023 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:20:56,050 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000008_1000 : 13562
2015-10-18 18:20:56,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000008_1000] using containerId: [container_1445144423722_0020_02_000009 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:20:56,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000008_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:20:56,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000008
2015-10-18 18:20:56,052 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:20:56,909 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:20:56,927 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:20:57,021 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:20:57,021 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:20:57,022 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-12> finalMapResourceLimit:<memory:7373, vCores:-11> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:57,160 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000007 asked for a task
2015-10-18 18:20:57,160 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000007 given task: attempt_1445144423722_0020_m_000006_1000
2015-10-18 18:20:57,238 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000006 asked for a task
2015-10-18 18:20:57,239 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000006 given task: attempt_1445144423722_0020_m_000005_1000
2015-10-18 18:20:58,024 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:20:58,025 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-12> finalMapResourceLimit:<memory:7373, vCores:-11> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:20:58,724 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.10660437
2015-10-18 18:20:58,766 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.1066108
2015-10-18 18:20:58,924 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:20:58,947 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000009 asked for a task
2015-10-18 18:20:58,947 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000009 given task: attempt_1445144423722_0020_m_000008_1000
2015-10-18 18:20:58,984 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.10680563
2015-10-18 18:20:59,002 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.10635664
2015-10-18 18:20:59,027 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-19>
2015-10-18 18:20:59,027 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:9216, vCores:-11> finalMapResourceLimit:<memory:8295, vCores:-10> finalReduceResourceLimit:<memory:921, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:21:00,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:21:00,030 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:00,030 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000010 to attempt_1445144423722_0020_m_000009_1000
2015-10-18 18:21:00,030 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:21:00,030 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-18 18:21:00,030 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:5 RackLocal:4
2015-10-18 18:21:00,030 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:00,031 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000009_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:21:00,031 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000010 taskAttempt attempt_1445144423722_0020_m_000009_1000
2015-10-18 18:21:00,032 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000009_1000
2015-10-18 18:21:00,032 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:21:00,755 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0020_m_000000
2015-10-18 18:21:00,756 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:21:00,756 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0020_m_000000
2015-10-18 18:21:00,756 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:00,756 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:00,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000000_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:21:00,988 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000009_1000 : 13562
2015-10-18 18:21:00,989 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000009_1000] using containerId: [container_1445144423722_0020_02_000010 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:21:00,989 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000009_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:21:00,990 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000009
2015-10-18 18:21:00,990 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:21:01,030 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:5 RackLocal:4
2015-10-18 18:21:01,033 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:21:01,734 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.13299951
2015-10-18 18:21:01,785 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.13000728
2015-10-18 18:21:02,009 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.17361976
2015-10-18 18:21:02,015 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.14074881
2015-10-18 18:21:04,750 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.19212553
2015-10-18 18:21:04,794 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.19211523
2015-10-18 18:21:05,013 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.19242907
2015-10-18 18:21:05,029 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.19158794
2015-10-18 18:21:06,322 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.106881365
2015-10-18 18:21:06,470 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:21:06,740 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000008 asked for a task
2015-10-18 18:21:06,741 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000008 given task: attempt_1445144423722_0020_m_000007_1000
2015-10-18 18:21:07,763 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.19212553
2015-10-18 18:21:07,812 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.19211523
2015-10-18 18:21:08,033 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.19242907
2015-10-18 18:21:08,047 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.19158794
2015-10-18 18:21:09,329 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.106881365
2015-10-18 18:21:10,766 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.19212553
2015-10-18 18:21:10,828 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.19211523
2015-10-18 18:21:11,053 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.19242907
2015-10-18 18:21:11,068 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.19158794
2015-10-18 18:21:12,056 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:21:12,057 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-18 18:21:12,057 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000011 to attempt_1445144423722_0020_r_000000_1000
2015-10-18 18:21:12,058 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:5 RackLocal:4
2015-10-18 18:21:12,074 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:12,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_r_000000_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:21:12,077 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000011 taskAttempt attempt_1445144423722_0020_r_000000_1000
2015-10-18 18:21:12,077 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_r_000000_1000
2015-10-18 18:21:12,078 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:21:12,095 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_r_000000_1000 : 13562
2015-10-18 18:21:12,095 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_r_000000_1000] using containerId: [container_1445144423722_0020_02_000011 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:7109]
2015-10-18 18:21:12,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_r_000000_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:21:12,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_r_000000
2015-10-18 18:21:12,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:21:12,345 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.106881365
2015-10-18 18:21:13,061 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:21:13,192 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:21:13,336 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000010 asked for a task
2015-10-18 18:21:13,336 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000010 given task: attempt_1445144423722_0020_m_000009_1000
2015-10-18 18:21:13,778 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.19212553
2015-10-18 18:21:13,842 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.19211523
2015-10-18 18:21:13,898 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:21:13,909 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_r_000011 asked for a task
2015-10-18 18:21:13,909 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_r_000011 given task: attempt_1445144423722_0020_r_000000_1000
2015-10-18 18:21:14,060 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.2474967
2015-10-18 18:21:14,063 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:21:14,064 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:14,064 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000012 to attempt_1445144423722_0020_m_000000_1001
2015-10-18 18:21:14,064 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:5 RackLocal:5
2015-10-18 18:21:14,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:14,065 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000000_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:21:14,065 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000012 taskAttempt attempt_1445144423722_0020_m_000000_1001
2015-10-18 18:21:14,065 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000000_1001
2015-10-18 18:21:14,065 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:21:14,074 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.2102172
2015-10-18 18:21:14,769 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000000_1001 : 13562
2015-10-18 18:21:14,770 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000000_1001] using containerId: [container_1445144423722_0020_02_000012 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:21:14,770 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000000_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:21:14,770 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000000
2015-10-18 18:21:14,965 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 0 maxEvents 10000
2015-10-18 18:21:15,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:21:15,358 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.11480458
2015-10-18 18:21:15,757 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0020_m_000008
2015-10-18 18:21:15,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0020_m_000008
2015-10-18 18:21:15,758 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:21:15,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:15,759 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:15,759 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000008_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:21:15,971 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:16,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:5 RackLocal:5
2015-10-18 18:21:16,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:21:16,781 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.27468833
2015-10-18 18:21:16,858 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.2703599
2015-10-18 18:21:16,972 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:17,059 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.2781602
2015-10-18 18:21:17,077 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.27696857
2015-10-18 18:21:17,974 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:18,372 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.19258286
2015-10-18 18:21:18,976 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:19,794 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.27772525
2015-10-18 18:21:19,872 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.27776006
2015-10-18 18:21:19,978 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:20,060 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.2781602
2015-10-18 18:21:20,091 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.27696857
2015-10-18 18:21:20,890 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:21:20,950 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:21:20,981 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:20,991 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000012 asked for a task
2015-10-18 18:21:20,991 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000012 given task: attempt_1445144423722_0020_m_000000_1001
2015-10-18 18:21:21,375 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.19258286
2015-10-18 18:21:21,983 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:22,817 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.27772525
2015-10-18 18:21:22,884 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.27776006
2015-10-18 18:21:22,985 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:23,065 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.2781602
2015-10-18 18:21:23,098 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.27696857
2015-10-18 18:21:23,987 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:23,993 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:21:24,395 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.19258286
2015-10-18 18:21:24,989 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:25,830 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.27772525
2015-10-18 18:21:25,894 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.27776006
2015-10-18 18:21:25,991 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:26,077 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.34566933
2015-10-18 18:21:26,109 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.3129203
2015-10-18 18:21:26,993 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:27,026 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:21:27,405 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.19258286
2015-10-18 18:21:27,606 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.028696446
2015-10-18 18:21:27,995 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:28,091 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:21:28,092 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000013 to attempt_1445144423722_0020_m_000008_1001
2015-10-18 18:21:28,092 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:6 RackLocal:5
2015-10-18 18:21:28,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:28,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000008_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:21:28,093 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000013 taskAttempt attempt_1445144423722_0020_m_000008_1001
2015-10-18 18:21:28,093 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000008_1001
2015-10-18 18:21:28,093 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:21:28,105 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000008_1001 : 13562
2015-10-18 18:21:28,105 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000008_1001] using containerId: [container_1445144423722_0020_02_000013 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:21:28,105 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000008_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:21:28,106 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000008
2015-10-18 18:21:28,844 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.33656734
2015-10-18 18:21:28,907 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.3279001
2015-10-18 18:21:28,995 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:29,093 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-19> knownNMs=3
2015-10-18 18:21:29,095 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.36388028
2015-10-18 18:21:29,125 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.3624012
2015-10-18 18:21:29,997 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:30,058 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:21:30,401 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:21:30,407 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.19258286
2015-10-18 18:21:30,420 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000013 asked for a task
2015-10-18 18:21:30,420 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000013 given task: attempt_1445144423722_0020_m_000008_1001
2015-10-18 18:21:30,545 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.05017859
2015-10-18 18:21:30,759 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0020_m_000007
2015-10-18 18:21:30,759 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:21:30,759 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0020_m_000007
2015-10-18 18:21:30,759 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:30,759 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:30,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000007_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:21:30,868 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.07311035
2015-10-18 18:21:30,998 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:31,094 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:6 RackLocal:5
2015-10-18 18:21:31,097 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-19> knownNMs=3
2015-10-18 18:21:31,845 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.36317363
2015-10-18 18:21:31,907 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.36319977
2015-10-18 18:21:32,000 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:32,098 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.36388028
2015-10-18 18:21:32,128 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.3624012
2015-10-18 18:21:33,002 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:33,096 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:21:33,103 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:21:33,103 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:33,104 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000014 to attempt_1445144423722_0020_m_000007_1001
2015-10-18 18:21:33,104 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:6 RackLocal:6
2015-10-18 18:21:33,104 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:33,106 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000007_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:21:33,106 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000014 taskAttempt attempt_1445144423722_0020_m_000007_1001
2015-10-18 18:21:33,106 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000007_1001
2015-10-18 18:21:33,107 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:21:33,412 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.24207932
2015-10-18 18:21:33,805 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000007_1001 : 13562
2015-10-18 18:21:33,806 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000007_1001] using containerId: [container_1445144423722_0020_02_000014 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:21:33,807 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000007_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:21:33,807 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000007
2015-10-18 18:21:34,004 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:34,107 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:21:34,412 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.078421384
2015-10-18 18:21:34,646 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.10582276
2015-10-18 18:21:34,864 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.36317363
2015-10-18 18:21:34,911 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.36319977
2015-10-18 18:21:35,006 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:35,108 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.36388028
2015-10-18 18:21:35,144 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.3624012
2015-10-18 18:21:36,009 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:36,123 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.044856425
2015-10-18 18:21:36,138 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:21:36,423 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.27811313
2015-10-18 18:21:37,011 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:37,736 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.106881365
2015-10-18 18:21:37,880 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.36317363
2015-10-18 18:21:37,926 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.36319977
2015-10-18 18:21:38,013 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:38,043 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.108067684
2015-10-18 18:21:38,110 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.44968578
2015-10-18 18:21:38,149 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.106964506
2015-10-18 18:21:38,160 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.43697044
2015-10-18 18:21:38,498 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.10685723
2015-10-18 18:21:39,015 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:39,180 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:21:39,424 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.27811313
2015-10-18 18:21:39,897 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.073998116
2015-10-18 18:21:40,017 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:40,752 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.106881365
2015-10-18 18:21:40,892 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.37249026
2015-10-18 18:21:40,944 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.36319977
2015-10-18 18:21:41,018 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:41,110 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.44968578
2015-10-18 18:21:41,178 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.44789755
2015-10-18 18:21:41,934 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.19252993
2015-10-18 18:21:42,020 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:42,212 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:21:42,215 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.10685723
2015-10-18 18:21:42,291 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.106964506
2015-10-18 18:21:42,438 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.27811313
2015-10-18 18:21:43,022 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:43,582 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.031418767
2015-10-18 18:21:43,754 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.106881365
2015-10-18 18:21:43,869 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.098910995
2015-10-18 18:21:43,898 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.4399656
2015-10-18 18:21:43,962 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.4339594
2015-10-18 18:21:44,024 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:44,117 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.44968578
2015-10-18 18:21:44,191 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.44789755
2015-10-18 18:21:45,026 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:45,251 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:21:45,441 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.27811313
2015-10-18 18:21:45,761 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0020_m_000005
2015-10-18 18:21:45,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0020_m_000005
2015-10-18 18:21:45,762 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:21:45,763 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:45,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:45,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000005_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:21:45,833 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.2631353
2015-10-18 18:21:46,028 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:46,093 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.10685723
2015-10-18 18:21:46,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:6 RackLocal:6
2015-10-18 18:21:46,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-19> knownNMs=3
2015-10-18 18:21:46,197 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.106964506
2015-10-18 18:21:46,772 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.106881365
2015-10-18 18:21:46,908 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.44859612
2015-10-18 18:21:46,974 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.448704
2015-10-18 18:21:47,030 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:47,127 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.46287853
2015-10-18 18:21:47,135 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:21:47,136 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000015 to attempt_1445144423722_0020_m_000005_1001
2015-10-18 18:21:47,136 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:7 RackLocal:6
2015-10-18 18:21:47,136 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:21:47,137 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000005_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:21:47,138 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000015 taskAttempt attempt_1445144423722_0020_m_000005_1001
2015-10-18 18:21:47,138 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000005_1001
2015-10-18 18:21:47,138 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:21:47,157 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000005_1001 : 13562
2015-10-18 18:21:47,157 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000005_1001] using containerId: [container_1445144423722_0020_02_000015 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:21:47,158 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000005_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:21:47,158 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000005
2015-10-18 18:21:47,207 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.44789755
2015-10-18 18:21:47,923 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.05999051
2015-10-18 18:21:48,021 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.10681946
2015-10-18 18:21:48,032 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:48,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:21:48,294 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:21:48,459 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.3450915
2015-10-18 18:21:49,034 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:49,413 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:21:49,494 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.295472
2015-10-18 18:21:49,524 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.10685723
2015-10-18 18:21:49,710 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000014 asked for a task
2015-10-18 18:21:49,711 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000014 given task: attempt_1445144423722_0020_m_000007_1001
2015-10-18 18:21:49,775 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.106964506
2015-10-18 18:21:49,789 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.18208472
2015-10-18 18:21:49,913 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.44859612
2015-10-18 18:21:49,957 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:21:49,974 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000015 asked for a task
2015-10-18 18:21:49,975 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000015 given task: attempt_1445144423722_0020_m_000005_1001
2015-10-18 18:21:49,990 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.448704
2015-10-18 18:21:50,036 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:50,146 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.5323876
2015-10-18 18:21:50,262 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.52057105
2015-10-18 18:21:51,038 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:51,335 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:21:51,475 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.3637686
2015-10-18 18:21:51,963 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.10681946
2015-10-18 18:21:52,024 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.09167796
2015-10-18 18:21:52,041 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:52,802 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.19258286
2015-10-18 18:21:52,927 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.44859612
2015-10-18 18:21:53,005 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.448704
2015-10-18 18:21:53,043 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:53,164 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.5352028
2015-10-18 18:21:53,272 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.53341997
2015-10-18 18:21:53,542 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.10685723
2015-10-18 18:21:53,618 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.106964506
2015-10-18 18:21:53,619 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.295472
2015-10-18 18:21:54,045 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:54,376 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:21:54,489 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.3637686
2015-10-18 18:21:55,047 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:55,789 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.10681946
2015-10-18 18:21:55,802 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.19258286
2015-10-18 18:21:55,807 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.10635664
2015-10-18 18:21:55,936 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.46968403
2015-10-18 18:21:56,006 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.5031185
2015-10-18 18:21:56,049 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:56,183 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.5352028
2015-10-18 18:21:56,286 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.53341997
2015-10-18 18:21:57,051 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:57,053 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.10685723
2015-10-18 18:21:57,161 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.295472
2015-10-18 18:21:57,178 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.10685723
2015-10-18 18:21:57,181 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.106964506
2015-10-18 18:21:57,407 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:21:57,506 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.3637686
2015-10-18 18:21:58,053 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:58,818 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.19258286
2015-10-18 18:21:58,944 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.5326224
2015-10-18 18:21:59,020 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.53425497
2015-10-18 18:21:59,055 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:21:59,193 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.54637426
2015-10-18 18:21:59,225 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.10681946
2015-10-18 18:21:59,271 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.10635664
2015-10-18 18:21:59,288 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.53341997
2015-10-18 18:22:00,057 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:00,195 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.10685723
2015-10-18 18:22:00,450 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:00,496 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.10685723
2015-10-18 18:22:00,521 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.3637686
2015-10-18 18:22:00,608 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.106964506
2015-10-18 18:22:00,650 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.295472
2015-10-18 18:22:00,764 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0020_m_000006
2015-10-18 18:22:00,764 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:22:00,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0020_m_000006
2015-10-18 18:22:00,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:22:00,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:22:00,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000006_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:22:01,060 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:01,164 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:7 RackLocal:6
2015-10-18 18:22:01,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-18> knownNMs=3
2015-10-18 18:22:01,835 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.19258286
2015-10-18 18:22:01,962 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.5342037
2015-10-18 18:22:02,022 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.53425497
2015-10-18 18:22:02,062 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:02,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:22:02,170 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000016 to attempt_1445144423722_0020_m_000006_1001
2015-10-18 18:22:02,170 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:8 RackLocal:6
2015-10-18 18:22:02,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:22:02,172 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000006_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:22:02,173 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000016 taskAttempt attempt_1445144423722_0020_m_000006_1001
2015-10-18 18:22:02,173 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000006_1001
2015-10-18 18:22:02,173 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:22:02,190 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000006_1001 : 13562
2015-10-18 18:22:02,191 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000006_1001] using containerId: [container_1445144423722_0020_02_000016 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:22:02,191 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000006_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:22:02,192 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000006
2015-10-18 18:22:02,242 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.617979
2015-10-18 18:22:02,303 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.60854876
2015-10-18 18:22:02,587 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.10681946
2015-10-18 18:22:02,716 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.10635664
2015-10-18 18:22:03,064 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:03,173 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-18> knownNMs=3
2015-10-18 18:22:03,211 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.10685723
2015-10-18 18:22:03,490 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:03,523 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.44630694
2015-10-18 18:22:03,830 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.10685723
2015-10-18 18:22:04,054 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.106964506
2015-10-18 18:22:04,056 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.295472
2015-10-18 18:22:04,066 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:04,854 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.23918316
2015-10-18 18:22:04,898 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:22:04,917 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000016 asked for a task
2015-10-18 18:22:04,917 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000016 given task: attempt_1445144423722_0020_m_000006_1001
2015-10-18 18:22:04,977 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.5342037
2015-10-18 18:22:05,044 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.53425497
2015-10-18 18:22:05,067 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:05,258 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.6208445
2015-10-18 18:22:05,323 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.61898744
2015-10-18 18:22:06,068 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:06,212 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.15958315
2015-10-18 18:22:06,254 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.10635664
2015-10-18 18:22:06,254 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.10681946
2015-10-18 18:22:06,529 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.44950172
2015-10-18 18:22:06,531 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:07,070 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:07,272 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.10685723
2015-10-18 18:22:07,462 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.106964506
2015-10-18 18:22:07,463 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.295472
2015-10-18 18:22:07,869 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.27811313
2015-10-18 18:22:07,982 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.5342037
2015-10-18 18:22:08,057 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.53425497
2015-10-18 18:22:08,072 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:08,265 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.6208445
2015-10-18 18:22:08,336 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.61898744
2015-10-18 18:22:09,074 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:09,227 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.19247705
2015-10-18 18:22:09,541 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.44950172
2015-10-18 18:22:09,551 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:09,635 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.10681946
2015-10-18 18:22:09,791 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.10635664
2015-10-18 18:22:10,056 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.049175814
2015-10-18 18:22:10,075 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:10,740 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.10685723
2015-10-18 18:22:10,868 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.27811313
2015-10-18 18:22:10,884 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.295472
2015-10-18 18:22:10,993 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.5919613
2015-10-18 18:22:11,061 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.106964506
2015-10-18 18:22:11,074 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.61678064
2015-10-18 18:22:11,077 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:11,272 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.64951074
2015-10-18 18:22:11,337 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.6282476
2015-10-18 18:22:11,881 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.64951074
2015-10-18 18:22:12,079 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:12,232 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.19247705
2015-10-18 18:22:12,251 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.106964506
2015-10-18 18:22:12,541 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.44950172
2015-10-18 18:22:12,582 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:12,765 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.6282476
2015-10-18 18:22:13,080 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:13,271 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.10681946
2015-10-18 18:22:13,447 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.10635664
2015-10-18 18:22:13,681 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.0922452
2015-10-18 18:22:13,868 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.27811313
2015-10-18 18:22:13,992 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.6196791
2015-10-18 18:22:14,083 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:14,086 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.6197233
2015-10-18 18:22:14,193 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.11882548
2015-10-18 18:22:14,273 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.667
2015-10-18 18:22:14,338 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.667
2015-10-18 18:22:14,367 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.295472
2015-10-18 18:22:14,662 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.106964506
2015-10-18 18:22:15,084 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:15,241 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.19247705
2015-10-18 18:22:15,259 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.106964506
2015-10-18 18:22:15,555 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.53128314
2015-10-18 18:22:15,619 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:15,766 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0020_m_000009
2015-10-18 18:22:15,766 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:22:15,766 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0020_m_000009
2015-10-18 18:22:15,768 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:22:15,768 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:22:15,769 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000009_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:22:16,086 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:16,191 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:8 RackLocal:6
2015-10-18 18:22:16,192 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-18> knownNMs=3
2015-10-18 18:22:16,867 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.10681946
2015-10-18 18:22:16,871 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.31132552
2015-10-18 18:22:16,930 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.10635664
2015-10-18 18:22:16,992 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.6196791
2015-10-18 18:22:17,088 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.6197233
2015-10-18 18:22:17,089 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:17,181 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.10681946
2015-10-18 18:22:17,273 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.667
2015-10-18 18:22:17,353 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.667
2015-10-18 18:22:17,674 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.15733507
2015-10-18 18:22:17,772 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.295472
2015-10-18 18:22:18,091 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:18,106 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.12378876
2015-10-18 18:22:18,196 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:22:18,197 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:22:18,197 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000017 to attempt_1445144423722_0020_m_000009_1001
2015-10-18 18:22:18,197 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:15 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-18 18:22:18,198 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:22:18,199 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000009_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:22:18,199 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000017 taskAttempt attempt_1445144423722_0020_m_000009_1001
2015-10-18 18:22:18,199 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000009_1001
2015-10-18 18:22:18,199 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:22:18,241 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.27813601
2015-10-18 18:22:18,264 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.106964506
2015-10-18 18:22:18,460 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000009_1001 : 13562
2015-10-18 18:22:18,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000009_1001] using containerId: [container_1445144423722_0020_02_000017 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:22:18,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000009_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:22:18,461 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000009
2015-10-18 18:22:18,555 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.53521925
2015-10-18 18:22:18,661 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:19,091 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:19,199 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-19> knownNMs=3
2015-10-18 18:22:19,884 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.3637686
2015-10-18 18:22:19,992 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.6196791
2015-10-18 18:22:20,093 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:20,101 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.6197233
2015-10-18 18:22:20,272 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.667
2015-10-18 18:22:20,366 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.667
2015-10-18 18:22:20,583 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.10635664
2015-10-18 18:22:20,659 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.10681946
2015-10-18 18:22:20,898 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.10681946
2015-10-18 18:22:21,094 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:21,242 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.27813601
2015-10-18 18:22:21,243 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.18819578
2015-10-18 18:22:21,275 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.12950405
2015-10-18 18:22:21,368 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.295472
2015-10-18 18:22:21,556 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.53521925
2015-10-18 18:22:21,702 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:21,713 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.1606859
2015-10-18 18:22:22,097 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:22,343 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.6197233
2015-10-18 18:22:22,892 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.3637686
2015-10-18 18:22:22,994 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.65043724
2015-10-18 18:22:23,098 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:23,105 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.667
2015-10-18 18:22:23,274 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.6845371
2015-10-18 18:22:23,369 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.6965265
2015-10-18 18:22:23,566 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.65043724
2015-10-18 18:22:24,099 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:24,181 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.12945008
2015-10-18 18:22:24,242 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.27813601
2015-10-18 18:22:24,277 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.19266446
2015-10-18 18:22:24,362 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.10635664
2015-10-18 18:22:24,555 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.53521925
2015-10-18 18:22:24,723 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:24,727 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.10681946
2015-10-18 18:22:24,974 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.19247705
2015-10-18 18:22:25,010 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.295472
2015-10-18 18:22:25,100 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:25,493 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.19266446
2015-10-18 18:22:25,899 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.3637686
2015-10-18 18:22:25,993 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.667
2015-10-18 18:22:26,102 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:26,118 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.667
2015-10-18 18:22:26,280 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.73734915
2015-10-18 18:22:26,368 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.7422868
2015-10-18 18:22:27,103 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:27,245 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.31029952
2015-10-18 18:22:27,291 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.19266446
2015-10-18 18:22:27,556 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.53521925
2015-10-18 18:22:27,754 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:27,935 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.16397104
2015-10-18 18:22:28,105 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:28,146 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.10635664
2015-10-18 18:22:28,554 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.10681946
2015-10-18 18:22:28,888 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.19247705
2015-10-18 18:22:28,906 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.39488932
2015-10-18 18:22:28,929 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.38387385
2015-10-18 18:22:28,997 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.667
2015-10-18 18:22:29,107 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:29,121 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.667
2015-10-18 18:22:29,292 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.77893615
2015-10-18 18:22:29,371 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.7835073
2015-10-18 18:22:29,750 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.19266446
2015-10-18 18:22:30,108 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:30,257 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.36390656
2015-10-18 18:22:30,290 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.19266446
2015-10-18 18:22:30,555 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.53521925
2015-10-18 18:22:30,768 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0020_m_000001
2015-10-18 18:22:30,768 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:22:30,768 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0020_m_000001
2015-10-18 18:22:30,768 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:22:30,769 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:22:30,769 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000001_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:22:30,784 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:30,934 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:22:31,110 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:31,216 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:15 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-18 18:22:31,219 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-19> knownNMs=3
2015-10-18 18:22:31,263 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000017 asked for a task
2015-10-18 18:22:31,263 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000017 given task: attempt_1445144423722_0020_m_000009_1001
2015-10-18 18:22:31,476 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.18897288
2015-10-18 18:22:31,902 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.11692039
2015-10-18 18:22:31,914 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.44950172
2015-10-18 18:22:32,007 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.667
2015-10-18 18:22:32,111 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:32,132 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.667
2015-10-18 18:22:32,221 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:22:32,221 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:22:32,221 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000018 to attempt_1445144423722_0020_m_000001_1001
2015-10-18 18:22:32,222 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:16 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-18 18:22:32,222 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:22:32,222 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000001_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:22:32,223 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000018 taskAttempt attempt_1445144423722_0020_m_000001_1001
2015-10-18 18:22:32,223 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000001_1001
2015-10-18 18:22:32,223 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:22:32,307 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.8214683
2015-10-18 18:22:32,382 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.8192006
2015-10-18 18:22:33,112 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:33,172 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000001_1001 : 13562
2015-10-18 18:22:33,173 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000001_1001] using containerId: [container_1445144423722_0020_02_000018 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:22:33,173 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000001_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:22:33,173 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000001
2015-10-18 18:22:33,224 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:22:33,248 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.10681946
2015-10-18 18:22:33,258 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.36390656
2015-10-18 18:22:33,290 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.21421999
2015-10-18 18:22:33,321 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.19247705
2015-10-18 18:22:33,448 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.4747104
2015-10-18 18:22:33,814 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:34,106 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.19266446
2015-10-18 18:22:34,114 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:34,914 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.44950172
2015-10-18 18:22:35,007 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.667
2015-10-18 18:22:35,115 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:35,132 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.667
2015-10-18 18:22:35,304 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.87652117
2015-10-18 18:22:35,368 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.19255035
2015-10-18 18:22:35,382 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.872202
2015-10-18 18:22:35,772 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.14267242
2015-10-18 18:22:36,116 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:36,259 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.36390656
2015-10-18 18:22:36,294 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.2783809
2015-10-18 18:22:36,855 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:36,958 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.10681946
2015-10-18 18:22:37,106 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.19247705
2015-10-18 18:22:37,117 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:37,154 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.5323719
2015-10-18 18:22:37,663 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.19266446
2015-10-18 18:22:37,915 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.44950172
2015-10-18 18:22:38,009 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.667
2015-10-18 18:22:38,119 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:38,306 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.9132925
2015-10-18 18:22:38,383 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.907894
2015-10-18 18:22:38,932 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.19255035
2015-10-18 18:22:39,120 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:39,260 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.40250373
2015-10-18 18:22:39,308 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.2783809
2015-10-18 18:22:39,505 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.17975734
2015-10-18 18:22:39,557 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.6207798
2015-10-18 18:22:39,887 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:40,122 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:40,584 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.10681946
2015-10-18 18:22:40,682 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.19247705
2015-10-18 18:22:40,887 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.5323719
2015-10-18 18:22:40,917 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.5303272
2015-10-18 18:22:41,124 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:41,307 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.9547759
2015-10-18 18:22:41,386 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.9477317
2015-10-18 18:22:41,609 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.19266446
2015-10-18 18:22:42,125 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:42,260 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.44950968
2015-10-18 18:22:42,323 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.2783809
2015-10-18 18:22:42,448 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.19255035
2015-10-18 18:22:42,556 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.6207798
2015-10-18 18:22:42,920 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:43,047 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.19158794
2015-10-18 18:22:43,126 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:43,916 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.53521925
2015-10-18 18:22:44,128 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:44,256 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.10681946
2015-10-18 18:22:44,307 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 0.9960743
2015-10-18 18:22:44,403 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 0.989426
2015-10-18 18:22:44,636 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.19247705
2015-10-18 18:22:44,744 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.5323719
2015-10-18 18:22:45,129 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:45,262 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.44950968
2015-10-18 18:22:45,322 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.35217696
2015-10-18 18:22:45,337 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.19266446
2015-10-18 18:22:45,559 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.6318842
2015-10-18 18:22:45,769 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0020_m_000002
2015-10-18 18:22:45,769 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:22:45,769 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0020_m_000002
2015-10-18 18:22:45,770 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:22:45,770 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:22:45,770 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000002_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:22:45,938 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:46,127 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.19255035
2015-10-18 18:22:46,130 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:46,241 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:16 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-18 18:22:46,242 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:22:46,581 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:22:46,604 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.19158794
2015-10-18 18:22:46,834 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000018 asked for a task
2015-10-18 18:22:46,834 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000018 given task: attempt_1445144423722_0020_m_000001_1001
2015-10-18 18:22:46,916 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.53521925
2015-10-18 18:22:46,971 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.6318842
2015-10-18 18:22:47,132 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:47,142 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.7109642
2015-10-18 18:22:47,306 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 1.0
2015-10-18 18:22:47,420 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 1.0
2015-10-18 18:22:48,134 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:48,274 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.44950968
2015-10-18 18:22:48,323 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.36404583
2015-10-18 18:22:48,514 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.10681946
2015-10-18 18:22:48,571 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.667
2015-10-18 18:22:48,819 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.5323719
2015-10-18 18:22:48,894 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.19247705
2015-10-18 18:22:48,980 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.033333335
2015-10-18 18:22:49,135 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:49,161 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.19266446
2015-10-18 18:22:49,667 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.19255035
2015-10-18 18:22:49,915 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.53521925
2015-10-18 18:22:50,009 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.7186975
2015-10-18 18:22:50,137 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:50,149 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.75723284
2015-10-18 18:22:50,306 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 1.0
2015-10-18 18:22:50,418 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1001 is : 0.19158794
2015-10-18 18:22:50,431 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 1.0
2015-10-18 18:22:50,676 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000004_1000 is : 1.0
2015-10-18 18:22:50,678 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0020_m_000004_1000
2015-10-18 18:22:50,681 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000004_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:22:50,681 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000005 taskAttempt attempt_1445144423722_0020_m_000004_1000
2015-10-18 18:22:50,681 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000004_1000
2015-10-18 18:22:50,682 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:22:50,708 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000004_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:22:50,708 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0020_m_000004_1000
2015-10-18 18:22:50,708 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:22:50,709 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-18 18:22:50,820 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000000_1000 is : 1.0
2015-10-18 18:22:50,823 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0020_m_000000_1000
2015-10-18 18:22:50,823 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000000_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:22:50,824 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000002 taskAttempt attempt_1445144423722_0020_m_000000_1000
2015-10-18 18:22:50,824 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000000_1000
2015-10-18 18:22:50,824 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:22:50,838 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000000_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:22:50,839 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0020_m_000000_1000
2015-10-18 18:22:50,839 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0020_m_000000_1001
2015-10-18 18:22:50,839 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:22:50,840 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-18 18:22:50,841 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000000_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:22:50,841 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000012 taskAttempt attempt_1445144423722_0020_m_000000_1001
2015-10-18 18:22:50,841 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000000_1001
2015-10-18 18:22:50,843 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:22:51,139 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 1 maxEvents 10000
2015-10-18 18:22:51,248 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:16 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-18 18:22:51,260 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000005
2015-10-18 18:22:51,262 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:22:51,262 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000004_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:22:51,262 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0020_02_000019 to attempt_1445144423722_0020_m_000002_1001
2015-10-18 18:22:51,263 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:16 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:22:51,263 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:22:51,264 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000002_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:22:51,265 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0020_02_000019 taskAttempt attempt_1445144423722_0020_m_000002_1001
2015-10-18 18:22:51,265 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0020_m_000002_1001
2015-10-18 18:22:51,265 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:22:51,275 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.5030427
2015-10-18 18:22:51,286 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0020_m_000002_1001 : 13562
2015-10-18 18:22:51,286 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0020_m_000002_1001] using containerId: [container_1445144423722_0020_02_000019 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:22:51,287 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000002_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:22:51,287 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0020_m_000002
2015-10-18 18:22:51,329 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.36404583
2015-10-18 18:22:51,576 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.667
2015-10-18 18:22:51,815 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000000_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:22:51,816 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:22:51,824 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out1/_temporary/2/_temporary/attempt_1445144423722_0020_m_000000_1001
2015-10-18 18:22:51,825 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000000_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:22:52,082 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.06666667
2015-10-18 18:22:52,097 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.10681946
2015-10-18 18:22:52,142 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:22:52,265 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0020: ask=4 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:1024, vCores:-19> knownNMs=3
2015-10-18 18:22:52,265 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000002
2015-10-18 18:22:52,265 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:15 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:22:52,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000000_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:22:52,601 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.5323719
2015-10-18 18:22:52,646 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.19247705
2015-10-18 18:22:52,814 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.19266446
2015-10-18 18:22:52,927 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.5738539
2015-10-18 18:22:53,013 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.7513578
2015-10-18 18:22:53,145 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:22:53,158 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.79132813
2015-10-18 18:22:53,274 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.19255035
2015-10-18 18:22:53,763 INFO [Socket Reader #1 for port 30607] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 30607: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:22:54,147 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:22:54,169 INFO [Socket Reader #1 for port 30607] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0020 (auth:SIMPLE)
2015-10-18 18:22:54,191 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0020_m_000019 asked for a task
2015-10-18 18:22:54,191 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0020_m_000019 given task: attempt_1445144423722_0020_m_000002_1001
2015-10-18 18:22:54,278 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.5352021
2015-10-18 18:22:54,353 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.36404583
2015-10-18 18:22:54,594 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.667
2015-10-18 18:22:55,127 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.10000001
2015-10-18 18:22:55,149 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:22:55,272 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000012
2015-10-18 18:22:55,272 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:22:55,273 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000000_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:22:55,560 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.12073027
2015-10-18 18:22:55,619 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.10681946
2015-10-18 18:22:55,936 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.6207798
2015-10-18 18:22:56,023 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.784497
2015-10-18 18:22:56,048 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.19247705
2015-10-18 18:22:56,151 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:22:56,169 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.8252958
2015-10-18 18:22:56,244 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.5323719
2015-10-18 18:22:56,249 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.19266446
2015-10-18 18:22:56,716 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.19255035
2015-10-18 18:22:57,153 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:22:57,284 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.5352021
2015-10-18 18:22:57,358 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.39525732
2015-10-18 18:22:57,611 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.68645245
2015-10-18 18:22:58,155 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:22:58,166 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.10000001
2015-10-18 18:22:58,960 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.6207798
2015-10-18 18:22:59,007 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.2221434
2015-10-18 18:22:59,020 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.13831116
2015-10-18 18:22:59,033 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.8166395
2015-10-18 18:22:59,157 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:22:59,176 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.8572054
2015-10-18 18:22:59,735 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.5323719
2015-10-18 18:22:59,736 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.19247705
2015-10-18 18:22:59,900 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.19266446
2015-10-18 18:23:00,159 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:23:00,247 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.19255035
2015-10-18 18:23:00,293 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.5352021
2015-10-18 18:23:00,363 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.44834188
2015-10-18 18:23:00,623 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.7178497
2015-10-18 18:23:01,161 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:23:01,195 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.10000001
2015-10-18 18:23:01,473 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1001 is : 0.10660437
2015-10-18 18:23:01,974 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.6207798
2015-10-18 18:23:02,044 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.85037047
2015-10-18 18:23:02,163 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:23:02,191 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.89019436
2015-10-18 18:23:02,451 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.295472
2015-10-18 18:23:02,758 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.1754474
2015-10-18 18:23:03,165 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:23:03,293 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.61879647
2015-10-18 18:23:03,372 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.44980705
2015-10-18 18:23:03,525 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.21145436
2015-10-18 18:23:03,592 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.5323719
2015-10-18 18:23:03,621 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.7598826
2015-10-18 18:23:03,775 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.19266446
2015-10-18 18:23:04,165 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.19255035
2015-10-18 18:23:04,167 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:23:04,235 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.10000001
2015-10-18 18:23:04,482 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1001 is : 0.10660437
2015-10-18 18:23:04,984 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.6207798
2015-10-18 18:23:05,045 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.8931881
2015-10-18 18:23:05,169 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:23:05,207 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.9339037
2015-10-18 18:23:06,171 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:23:06,181 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.295472
2015-10-18 18:23:06,293 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.6209487
2015-10-18 18:23:06,380 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.44980705
2015-10-18 18:23:06,439 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.19255035
2015-10-18 18:23:06,622 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.799422
2015-10-18 18:23:07,173 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:23:07,210 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.24443084
2015-10-18 18:23:07,265 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.10000001
2015-10-18 18:23:07,488 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1001 is : 0.10660437
2015-10-18 18:23:07,494 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.5323719
2015-10-18 18:23:07,569 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.19494733
2015-10-18 18:23:07,772 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.6207798
2015-10-18 18:23:07,873 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.19255035
2015-10-18 18:23:08,000 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.667
2015-10-18 18:23:08,048 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.9277486
2015-10-18 18:23:08,176 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:23:08,219 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 0.9690243
2015-10-18 18:23:08,390 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1001 is : 0.039218508
2015-10-18 18:23:09,178 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:23:09,303 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.6209487
2015-10-18 18:23:09,389 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.44980705
2015-10-18 18:23:09,626 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.84287524
2015-10-18 18:23:09,764 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.295472
2015-10-18 18:23:10,103 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.19255035
2015-10-18 18:23:10,111 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000001_1000 is : 1.0
2015-10-18 18:23:10,113 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0020_m_000001_1000
2015-10-18 18:23:10,114 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000001_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:23:10,114 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000003 taskAttempt attempt_1445144423722_0020_m_000001_1000
2015-10-18 18:23:10,114 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000001_1000
2015-10-18 18:23:10,115 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:23:10,130 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000001_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:23:10,131 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0020_m_000001_1000
2015-10-18 18:23:10,131 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0020_m_000001_1001
2015-10-18 18:23:10,131 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:23:10,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-18 18:23:10,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000001_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:23:10,133 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000018 taskAttempt attempt_1445144423722_0020_m_000001_1001
2015-10-18 18:23:10,133 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000001_1001
2015-10-18 18:23:10,133 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:23:10,180 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:23:10,296 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.10000001
2015-10-18 18:23:10,302 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:23:10,500 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1001 is : 0.10660437
2015-10-18 18:23:10,732 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000001_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:23:10,733 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:23:10,736 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out1/_temporary/2/_temporary/attempt_1445144423722_0020_m_000001_1001
2015-10-18 18:23:10,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000001_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:23:10,967 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.27369574
2015-10-18 18:23:11,013 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.667
2015-10-18 18:23:11,060 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 0.97938573
2015-10-18 18:23:11,183 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:23:11,220 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.5323719
2015-10-18 18:23:11,308 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000003
2015-10-18 18:23:11,309 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:23:11,309 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000001_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:23:11,491 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.22939819
2015-10-18 18:23:11,885 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.19255035
2015-10-18 18:23:12,142 INFO [Socket Reader #1 for port 30607] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 30607: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:23:12,186 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:23:12,310 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.629403
2015-10-18 18:23:12,392 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.5236449
2015-10-18 18:23:12,599 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000002_1000 is : 1.0
2015-10-18 18:23:12,601 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0020_m_000002_1000
2015-10-18 18:23:12,601 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000002_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:23:12,602 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000004 taskAttempt attempt_1445144423722_0020_m_000002_1000
2015-10-18 18:23:12,602 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000002_1000
2015-10-18 18:23:12,603 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:23:12,617 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000002_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:23:12,617 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0020_m_000002_1000
2015-10-18 18:23:12,618 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0020_m_000002_1001
2015-10-18 18:23:12,618 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:23:12,618 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-18 18:23:12,619 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000002_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:23:12,619 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000019 taskAttempt attempt_1445144423722_0020_m_000002_1001
2015-10-18 18:23:12,620 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000002_1001
2015-10-18 18:23:12,620 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:23:12,634 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000002_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:23:12,635 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:23:12,638 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out1/_temporary/2/_temporary/attempt_1445144423722_0020_m_000002_1001
2015-10-18 18:23:12,638 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.8939904
2015-10-18 18:23:12,638 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000002_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:23:12,682 INFO [Socket Reader #1 for port 30607] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 30607: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:23:13,188 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:23:13,250 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.629403
2015-10-18 18:23:13,311 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:23:13,313 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000018
2015-10-18 18:23:13,313 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:23:13,313 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000001_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:23:13,338 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.13333334
2015-10-18 18:23:13,414 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.295472
2015-10-18 18:23:13,606 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.19255035
2015-10-18 18:23:14,015 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.667
2015-10-18 18:23:14,191 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:23:14,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000004
2015-10-18 18:23:14,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000019
2015-10-18 18:23:14,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000002_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:23:14,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:23:14,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000002_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:23:14,553 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.27813601
2015-10-18 18:23:14,957 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.5323719
2015-10-18 18:23:15,181 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.26705083
2015-10-18 18:23:15,193 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:23:15,309 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.667
2015-10-18 18:23:15,403 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.53543663
2015-10-18 18:23:15,412 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.2258233
2015-10-18 18:23:15,637 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.9405241
2015-10-18 18:23:16,195 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:23:16,379 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.16666667
2015-10-18 18:23:16,853 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.295472
2015-10-18 18:23:17,029 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1001 is : 0.6926901
2015-10-18 18:23:17,147 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.19255035
2015-10-18 18:23:17,197 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:23:17,905 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.27813601
2015-10-18 18:23:18,199 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:23:18,310 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.667
2015-10-18 18:23:18,410 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.53543663
2015-10-18 18:23:18,418 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.56100214
2015-10-18 18:23:18,634 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.2783809
2015-10-18 18:23:18,643 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 0.99424887
2015-10-18 18:23:18,950 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.26260474
2015-10-18 18:23:19,201 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:23:19,259 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000008_1000 is : 1.0
2015-10-18 18:23:19,261 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0020_m_000008_1000
2015-10-18 18:23:19,261 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000008_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:23:19,262 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000009 taskAttempt attempt_1445144423722_0020_m_000008_1000
2015-10-18 18:23:19,262 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000008_1000
2015-10-18 18:23:19,263 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:23:19,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000008_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:23:19,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0020_m_000008_1000
2015-10-18 18:23:19,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0020_m_000008_1001
2015-10-18 18:23:19,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:23:19,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-18 18:23:19,275 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000008_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:23:19,275 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000013 taskAttempt attempt_1445144423722_0020_m_000008_1001
2015-10-18 18:23:19,275 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000008_1001
2015-10-18 18:23:19,275 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:23:19,285 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000008_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:23:19,286 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:23:19,288 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out1/_temporary/2/_temporary/attempt_1445144423722_0020_m_000008_1001
2015-10-18 18:23:19,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000008_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:23:19,323 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:23:19,346 INFO [Socket Reader #1 for port 30607] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 30607: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:23:19,412 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.16666667
2015-10-18 18:23:20,202 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:23:20,326 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000009
2015-10-18 18:23:20,326 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000013
2015-10-18 18:23:20,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000008_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:23:20,326 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:23:20,327 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000008_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:23:20,510 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.295472
2015-10-18 18:23:20,897 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.19255035
2015-10-18 18:23:21,205 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:21,310 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.683175
2015-10-18 18:23:21,419 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.53543663
2015-10-18 18:23:21,586 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.27813601
2015-10-18 18:23:21,882 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.6501114
2015-10-18 18:23:22,121 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.2783809
2015-10-18 18:23:22,207 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:22,413 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.6501114
2015-10-18 18:23:22,414 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.27825075
2015-10-18 18:23:22,452 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.20000002
2015-10-18 18:23:23,210 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:24,058 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.295472
2015-10-18 18:23:24,212 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:24,311 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.7327884
2015-10-18 18:23:24,384 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.19255035
2015-10-18 18:23:24,427 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.6121269
2015-10-18 18:23:24,994 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.27813601
2015-10-18 18:23:25,214 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:25,228 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.667
2015-10-18 18:23:25,491 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.20000002
2015-10-18 18:23:25,649 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.2783809
2015-10-18 18:23:25,898 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.27825075
2015-10-18 18:23:26,216 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:27,218 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:27,311 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.7869895
2015-10-18 18:23:27,387 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.295472
2015-10-18 18:23:27,436 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.6210422
2015-10-18 18:23:27,945 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.19255035
2015-10-18 18:23:28,220 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:28,446 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.27813601
2015-10-18 18:23:28,531 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.20000002
2015-10-18 18:23:28,789 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.667
2015-10-18 18:23:29,087 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.2783809
2015-10-18 18:23:29,222 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:29,278 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.27825075
2015-10-18 18:23:30,224 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:30,310 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.8439388
2015-10-18 18:23:30,435 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.6210422
2015-10-18 18:23:30,914 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.295472
2015-10-18 18:23:31,226 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:31,447 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.19255035
2015-10-18 18:23:31,569 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.20000002
2015-10-18 18:23:31,977 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.27813601
2015-10-18 18:23:32,228 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:32,272 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.667
2015-10-18 18:23:32,602 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.2783809
2015-10-18 18:23:32,775 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.27825075
2015-10-18 18:23:33,230 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:33,317 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.901201
2015-10-18 18:23:33,436 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.62731194
2015-10-18 18:23:34,232 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:34,261 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.295472
2015-10-18 18:23:34,600 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.20000002
2015-10-18 18:23:34,773 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.62731194
2015-10-18 18:23:34,788 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.19255035
2015-10-18 18:23:35,234 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:35,324 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.27813601
2015-10-18 18:23:35,743 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.667
2015-10-18 18:23:36,149 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.2783809
2015-10-18 18:23:36,236 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:36,328 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 0.95238376
2015-10-18 18:23:36,334 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.27825075
2015-10-18 18:23:36,441 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.667
2015-10-18 18:23:37,237 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:37,648 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.20000002
2015-10-18 18:23:37,734 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.31988487
2015-10-18 18:23:38,239 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:38,271 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.20426215
2015-10-18 18:23:38,712 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1000 is : 0.27813601
2015-10-18 18:23:39,178 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.667
2015-10-18 18:23:39,242 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:39,276 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000005_1001 is : 1.0
2015-10-18 18:23:39,278 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0020_m_000005_1001
2015-10-18 18:23:39,278 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000005_1001 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:23:39,278 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000015 taskAttempt attempt_1445144423722_0020_m_000005_1001
2015-10-18 18:23:39,279 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000005_1001
2015-10-18 18:23:39,279 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:23:39,294 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000005_1001 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:23:39,294 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0020_m_000005_1001
2015-10-18 18:23:39,295 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0020_m_000005_1000
2015-10-18 18:23:39,295 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:23:39,295 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-18 18:23:39,296 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000005_1000 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:23:39,296 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000006 taskAttempt attempt_1445144423722_0020_m_000005_1000
2015-10-18 18:23:39,296 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000005_1000
2015-10-18 18:23:39,298 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:23:39,358 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:23:39,451 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.667
2015-10-18 18:23:39,545 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.2783809
2015-10-18 18:23:39,806 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.27825075
2015-10-18 18:23:39,830 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000005_1000 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:23:39,831 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:23:39,831 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0020_m_000005
2015-10-18 18:23:39,831 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:23:39,833 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out1/_temporary/2/_temporary/attempt_1445144423722_0020_m_000005_1000
2015-10-18 18:23:39,833 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000005_1000 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:23:40,244 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:23:40,364 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000015
2015-10-18 18:23:40,364 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:23:40,364 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000005_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:23:40,687 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.20000002
2015-10-18 18:23:40,948 INFO [Socket Reader #1 for port 30607] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 30607: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:23:41,122 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.43630743
2015-10-18 18:23:41,246 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:41,642 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.24468967
2015-10-18 18:23:42,248 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:42,367 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000006
2015-10-18 18:23:42,367 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:23:42,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000005_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:23:42,457 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.677656
2015-10-18 18:23:42,526 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.667
2015-10-18 18:23:43,165 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.2783809
2015-10-18 18:23:43,250 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:43,359 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.27825075
2015-10-18 18:23:43,728 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.23333333
2015-10-18 18:23:44,252 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:44,617 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.52900237
2015-10-18 18:23:45,085 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.2770595
2015-10-18 18:23:45,254 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:45,469 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.7207605
2015-10-18 18:23:46,054 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.667
2015-10-18 18:23:46,256 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:46,570 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.2783809
2015-10-18 18:23:46,712 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.27825075
2015-10-18 18:23:46,767 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.23333333
2015-10-18 18:23:47,258 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:47,960 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.5323719
2015-10-18 18:23:48,260 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:48,336 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.27825075
2015-10-18 18:23:48,482 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.7776862
2015-10-18 18:23:49,262 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:49,620 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.6695607
2015-10-18 18:23:49,808 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.23333333
2015-10-18 18:23:49,951 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.30705437
2015-10-18 18:23:50,070 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.27825075
2015-10-18 18:23:50,264 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:51,266 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:51,308 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.5323719
2015-10-18 18:23:51,482 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.83470386
2015-10-18 18:23:51,686 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.27825075
2015-10-18 18:23:52,268 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:52,848 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.23333333
2015-10-18 18:23:53,071 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.7021809
2015-10-18 18:23:53,270 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:53,319 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.35235128
2015-10-18 18:23:53,656 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.31641844
2015-10-18 18:23:54,273 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:54,483 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.8918324
2015-10-18 18:23:54,717 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.5323719
2015-10-18 18:23:55,116 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.27825075
2015-10-18 18:23:55,275 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:55,890 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.23333333
2015-10-18 18:23:56,277 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:56,621 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.73080736
2015-10-18 18:23:56,770 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.36404583
2015-10-18 18:23:57,165 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.35711336
2015-10-18 18:23:57,279 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:57,483 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.9488133
2015-10-18 18:23:58,043 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.5323719
2015-10-18 18:23:58,281 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:58,522 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.27825075
2015-10-18 18:23:58,920 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.23333333
2015-10-18 18:23:59,283 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:23:59,915 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.76089734
2015-10-18 18:24:00,088 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1000 is : 0.36404583
2015-10-18 18:24:00,284 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:24:00,483 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 0.99566996
2015-10-18 18:24:00,495 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.3638923
2015-10-18 18:24:00,855 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000006_1001 is : 1.0
2015-10-18 18:24:00,857 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0020_m_000006_1001
2015-10-18 18:24:00,858 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000006_1001 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:24:00,858 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000016 taskAttempt attempt_1445144423722_0020_m_000006_1001
2015-10-18 18:24:00,859 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000006_1001
2015-10-18 18:24:00,860 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:24:00,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000006_1001 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:24:00,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0020_m_000006_1001
2015-10-18 18:24:00,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0020_m_000006_1000
2015-10-18 18:24:00,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:24:00,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-18 18:24:00,876 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000006_1000 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:24:00,876 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000007 taskAttempt attempt_1445144423722_0020_m_000006_1000
2015-10-18 18:24:00,876 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000006_1000
2015-10-18 18:24:00,877 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:24:01,286 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:24:01,370 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.5323719
2015-10-18 18:24:01,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:24:01,518 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000006_1000 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:24:01,518 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:24:01,526 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out1/_temporary/2/_temporary/attempt_1445144423722_0020_m_000006_1000
2015-10-18 18:24:01,527 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000006_1000 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:24:01,906 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.27825075
2015-10-18 18:24:01,961 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.23333333
2015-10-18 18:24:02,139 INFO [Socket Reader #1 for port 30607] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 30607: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:24:02,289 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:02,405 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000016
2015-10-18 18:24:02,405 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:24:02,405 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000006_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:24:03,169 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.7929285
2015-10-18 18:24:03,291 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:03,407 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000007
2015-10-18 18:24:03,407 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:24:03,408 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000006_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:24:03,721 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.3638923
2015-10-18 18:24:04,293 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:04,701 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.5323719
2015-10-18 18:24:05,001 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.26666668
2015-10-18 18:24:05,122 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.27825075
2015-10-18 18:24:05,295 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:06,297 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:06,398 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.83242434
2015-10-18 18:24:06,882 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.3638923
2015-10-18 18:24:07,299 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:08,009 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.5323719
2015-10-18 18:24:08,039 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.26666668
2015-10-18 18:24:08,301 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:08,324 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.27825075
2015-10-18 18:24:09,304 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:09,697 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.87098765
2015-10-18 18:24:10,181 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.3638923
2015-10-18 18:24:10,306 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:11,071 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.26666668
2015-10-18 18:24:11,230 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.62183553
2015-10-18 18:24:11,308 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:11,511 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.31801882
2015-10-18 18:24:12,143 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.62183553
2015-10-18 18:24:12,310 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:12,966 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.90491444
2015-10-18 18:24:13,312 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:13,650 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.3638923
2015-10-18 18:24:14,111 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.26666668
2015-10-18 18:24:14,314 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:14,582 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.667
2015-10-18 18:24:14,855 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.3638923
2015-10-18 18:24:15,316 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:16,179 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.94222057
2015-10-18 18:24:16,317 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:16,931 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.3638923
2015-10-18 18:24:17,152 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.26666668
2015-10-18 18:24:17,319 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:18,004 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.667
2015-10-18 18:24:18,212 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.3638923
2015-10-18 18:24:18,321 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:19,323 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:19,439 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 0.98305357
2015-10-18 18:24:20,149 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.38743404
2015-10-18 18:24:20,188 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.26666668
2015-10-18 18:24:20,325 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:21,083 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1001 is : 0.667
2015-10-18 18:24:21,133 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000009_1000 is : 1.0
2015-10-18 18:24:21,136 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0020_m_000009_1000
2015-10-18 18:24:21,137 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000009_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:24:21,137 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000010 taskAttempt attempt_1445144423722_0020_m_000009_1000
2015-10-18 18:24:21,138 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000009_1000
2015-10-18 18:24:21,141 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:24:21,277 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.3638923
2015-10-18 18:24:21,327 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:21,336 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000009_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:24:21,336 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0020_m_000009_1000
2015-10-18 18:24:21,336 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0020_m_000009_1001
2015-10-18 18:24:21,336 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:24:21,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-18 18:24:21,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000009_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:24:21,338 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000017 taskAttempt attempt_1445144423722_0020_m_000009_1001
2015-10-18 18:24:21,338 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000009_1001
2015-10-18 18:24:21,338 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:24:21,441 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:24:21,490 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000009_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:24:21,491 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:24:21,498 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out1/_temporary/2/_temporary/attempt_1445144423722_0020_m_000009_1001
2015-10-18 18:24:21,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000009_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:24:21,653 INFO [Socket Reader #1 for port 30607] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 30607: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:24:22,329 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:24:23,230 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.26666668
2015-10-18 18:24:23,332 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:23,369 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.4444642
2015-10-18 18:24:23,448 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000010
2015-10-18 18:24:23,448 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000017
2015-10-18 18:24:23,448 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000009_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:24:23,448 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:24:23,449 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000009_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:24:24,334 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.3638923
2015-10-18 18:24:24,334 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:25,337 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:26,262 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.26666668
2015-10-18 18:24:26,339 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:26,464 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.44964966
2015-10-18 18:24:27,341 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:27,400 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.3638923
2015-10-18 18:24:28,343 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:29,294 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.26666668
2015-10-18 18:24:29,345 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:29,538 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.44964966
2015-10-18 18:24:30,347 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:30,465 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.3638923
2015-10-18 18:24:31,349 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:32,324 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:24:32,351 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:32,595 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.44964966
2015-10-18 18:24:33,353 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:33,524 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.4149211
2015-10-18 18:24:34,355 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:35,357 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:35,368 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:24:35,634 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.44964966
2015-10-18 18:24:36,359 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:36,587 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.44964966
2015-10-18 18:24:37,361 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:38,363 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:38,397 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:24:38,673 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.47395837
2015-10-18 18:24:39,366 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:39,631 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.44964966
2015-10-18 18:24:40,368 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:41,370 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:41,440 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:24:41,729 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.5352825
2015-10-18 18:24:42,372 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:42,679 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.44964966
2015-10-18 18:24:43,373 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:44,374 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:44,471 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:24:44,756 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.5352825
2015-10-18 18:24:45,376 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:45,713 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.44964966
2015-10-18 18:24:46,378 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:47,380 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:47,512 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:24:47,797 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.5352825
2015-10-18 18:24:48,382 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:48,754 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.5084678
2015-10-18 18:24:49,384 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:50,386 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:50,541 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:24:50,836 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.5352825
2015-10-18 18:24:51,388 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:51,791 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.5352825
2015-10-18 18:24:52,390 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:53,392 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:53,582 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:24:53,877 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.55827814
2015-10-18 18:24:54,394 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:54,833 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.5352825
2015-10-18 18:24:55,397 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:56,399 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:56,624 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:24:56,949 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.620844
2015-10-18 18:24:57,401 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:57,884 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.5352825
2015-10-18 18:24:58,403 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:59,405 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:24:59,663 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:24:59,977 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.620844
2015-10-18 18:25:00,407 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:00,915 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.5352825
2015-10-18 18:25:01,409 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:02,411 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:02,704 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:03,036 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.620844
2015-10-18 18:25:03,413 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:03,961 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.589761
2015-10-18 18:25:04,415 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:05,417 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:05,741 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:06,104 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.620844
2015-10-18 18:25:06,419 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:07,028 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.620844
2015-10-18 18:25:07,421 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:08,422 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:08,772 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:09,145 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.6439783
2015-10-18 18:25:09,424 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:09,870 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.6439783
2015-10-18 18:25:10,057 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.620844
2015-10-18 18:25:10,427 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:11,429 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:11,802 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:12,216 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.667
2015-10-18 18:25:12,431 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:13,094 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.620844
2015-10-18 18:25:13,433 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:14,435 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:14,822 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:15,258 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.667
2015-10-18 18:25:15,437 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:16,155 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.620844
2015-10-18 18:25:16,439 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:17,441 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:17,863 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:18,310 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.667
2015-10-18 18:25:18,443 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:19,235 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.66165537
2015-10-18 18:25:19,445 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:19,488 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.66165537
2015-10-18 18:25:20,447 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:20,892 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:21,449 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:21,494 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.6672845
2015-10-18 18:25:22,315 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.667
2015-10-18 18:25:22,451 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:23,453 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:23,934 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:24,455 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:24,538 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.6892619
2015-10-18 18:25:25,361 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.667
2015-10-18 18:25:25,458 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:26,460 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:26,967 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:27,462 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:27,582 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.71166104
2015-10-18 18:25:28,416 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.667
2015-10-18 18:25:28,464 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:29,466 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:29,994 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:30,468 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:30,629 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.73251593
2015-10-18 18:25:31,461 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.667
2015-10-18 18:25:31,470 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:32,472 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:33,034 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:33,474 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:33,675 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.75284094
2015-10-18 18:25:34,476 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:34,509 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.6784065
2015-10-18 18:25:35,478 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:36,064 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:36,480 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:36,724 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.7739015
2015-10-18 18:25:37,482 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:37,548 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.7003755
2015-10-18 18:25:38,484 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:39,106 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:39,486 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:39,776 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.794835
2015-10-18 18:25:40,488 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:40,576 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.7239548
2015-10-18 18:25:41,491 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:42,147 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:42,493 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:42,809 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.8228391
2015-10-18 18:25:43,495 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:43,605 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.7532629
2015-10-18 18:25:44,497 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:45,186 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:45,499 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:45,846 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.85160303
2015-10-18 18:25:46,500 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:46,637 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.7830234
2015-10-18 18:25:47,502 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:48,223 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:48,504 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:48,871 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.8799484
2015-10-18 18:25:49,506 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:49,665 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.81297404
2015-10-18 18:25:50,508 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:51,258 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:51,510 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:51,902 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.90789837
2015-10-18 18:25:52,511 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:52,711 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.84161615
2015-10-18 18:25:53,513 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:54,300 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:54,515 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:54,930 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.93567836
2015-10-18 18:25:55,517 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:55,742 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.87023497
2015-10-18 18:25:56,520 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:57,340 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:25:57,522 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:57,962 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.96367896
2015-10-18 18:25:58,524 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:25:58,778 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.90008664
2015-10-18 18:25:59,526 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:26:00,379 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:26:00,528 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:26:00,993 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 0.9905465
2015-10-18 18:26:01,530 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:26:01,806 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1001 is : 0.9282893
2015-10-18 18:26:02,127 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_m_000007_1000 is : 1.0
2015-10-18 18:26:02,130 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0020_m_000007_1000
2015-10-18 18:26:02,131 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000007_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:26:02,131 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000008 taskAttempt attempt_1445144423722_0020_m_000007_1000
2015-10-18 18:26:02,132 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000007_1000
2015-10-18 18:26:02,133 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:26:02,165 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000007_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:26:02,165 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0020_m_000007_1000
2015-10-18 18:26:02,165 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0020_m_000007_1001
2015-10-18 18:26:02,166 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:26:02,166 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-18 18:26:02,167 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000007_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:26:02,167 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000014 taskAttempt attempt_1445144423722_0020_m_000007_1001
2015-10-18 18:26:02,167 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_m_000007_1001
2015-10-18 18:26:02,167 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:26:02,196 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000007_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:26:02,196 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:26:02,204 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out1/_temporary/2/_temporary/attempt_1445144423722_0020_m_000007_1001
2015-10-18 18:26:02,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_m_000007_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:26:02,354 INFO [Socket Reader #1 for port 30607] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 30607: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:26:02,532 INFO [IPC Server handler 17 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:26:02,618 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:26:03,419 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:26:03,535 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-18 18:26:03,622 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000008
2015-10-18 18:26:03,622 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0020_02_000014
2015-10-18 18:26:03,622 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000007_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:26:03,622 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:26:03,622 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0020_m_000007_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:26:04,537 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-18 18:26:05,539 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-18 18:26:06,448 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:26:06,541 INFO [IPC Server handler 7 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-18 18:26:07,543 INFO [IPC Server handler 27 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-18 18:26:08,545 INFO [IPC Server handler 11 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-18 18:26:09,489 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:26:09,547 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-18 18:26:10,548 INFO [IPC Server handler 29 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-18 18:26:11,551 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-18 18:26:12,529 INFO [IPC Server handler 2 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:26:12,553 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-18 18:26:13,555 INFO [IPC Server handler 23 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0020_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-18 18:26:13,565 INFO [IPC Server handler 24 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:26:13,614 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.3
2015-10-18 18:26:15,581 INFO [IPC Server handler 1 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.6682903
2015-10-18 18:26:18,615 INFO [IPC Server handler 22 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.6850339
2015-10-18 18:26:21,655 INFO [IPC Server handler 6 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.70413893
2015-10-18 18:26:24,686 INFO [IPC Server handler 9 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.71965784
2015-10-18 18:26:27,716 INFO [IPC Server handler 26 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.7339598
2015-10-18 18:26:30,745 INFO [IPC Server handler 4 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.7488035
2015-10-18 18:26:33,784 INFO [IPC Server handler 28 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.7634186
2015-10-18 18:26:36,820 INFO [IPC Server handler 19 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.7804886
2015-10-18 18:26:39,851 INFO [IPC Server handler 10 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.79666305
2015-10-18 18:26:42,881 INFO [IPC Server handler 5 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.8174538
2015-10-18 18:26:45,921 INFO [IPC Server handler 20 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.8295271
2015-10-18 18:26:48,953 INFO [IPC Server handler 21 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.8464713
2015-10-18 18:26:51,981 INFO [IPC Server handler 18 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.8628497
2015-10-18 18:26:55,011 INFO [IPC Server handler 0 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.8832647
2015-10-18 18:26:58,031 INFO [IPC Server handler 15 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.90503097
2015-10-18 18:27:01,048 INFO [IPC Server handler 13 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.9222006
2015-10-18 18:27:04,086 INFO [IPC Server handler 16 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.93709236
2015-10-18 18:27:07,115 INFO [IPC Server handler 14 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.95637715
2015-10-18 18:27:10,158 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.9753876
2015-10-18 18:27:13,189 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 0.99223685
2015-10-18 18:27:15,162 INFO [IPC Server handler 12 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445144423722_0020_r_000000_1000
2015-10-18 18:27:15,162 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_r_000000_1000 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-18 18:27:15,162 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445144423722_0020_r_000000_1000 given a go for committing the task output.
2015-10-18 18:27:15,163 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445144423722_0020_r_000000_1000
2015-10-18 18:27:15,163 INFO [IPC Server handler 25 on 30607] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445144423722_0020_r_000000_1000:true
2015-10-18 18:27:15,198 INFO [IPC Server handler 8 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0020_r_000000_1000 is : 1.0
2015-10-18 18:27:15,199 INFO [IPC Server handler 3 on 30607] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0020_r_000000_1000
2015-10-18 18:27:15,199 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_r_000000_1000 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:27:15,199 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0020_02_000011 taskAttempt attempt_1445144423722_0020_r_000000_1000
2015-10-18 18:27:15,200 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0020_r_000000_1000
2015-10-18 18:27:15,200 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:27:15,213 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0020_r_000000_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:27:15,213 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0020_r_000000_1000
2015-10-18 18:27:15,213 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0020_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:27:15,213 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-18 18:27:15,214 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0020Job Transitioned from RUNNING to COMMITTING
2015-10-18 18:27:15,214 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-18 18:27:15,250 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-18 18:27:15,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0020Job Transitioned from COMMITTING to SUCCEEDED
2015-10-18 18:27:15,252 INFO [Thread-116] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-18 18:27:15,252 INFO [Thread-116] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-18 18:27:15,252 INFO [Thread-116] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-18 18:27:15,252 INFO [Thread-116] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-18 18:27:15,252 INFO [Thread-116] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-18 18:27:15,252 INFO [Thread-116] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-18 18:27:15,252 INFO [Thread-116] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-18 18:27:15,352 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0020/job_1445144423722_0020_2.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0020-1445162504986-msrabi-pagerank-1445164035248-10-1-SUCCEEDED-default-1445162513713.jhist_tmp
2015-10-18 18:27:15,448 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0020-1445162504986-msrabi-pagerank-1445164035248-10-1-SUCCEEDED-default-1445162513713.jhist_tmp
2015-10-18 18:27:15,451 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0020/job_1445144423722_0020_2_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0020_conf.xml_tmp
2015-10-18 18:27:15,539 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0020_conf.xml_tmp
2015-10-18 18:27:15,543 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0020.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0020.summary
2015-10-18 18:27:15,545 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0020_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0020_conf.xml
2015-10-18 18:27:15,548 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0020-1445162504986-msrabi-pagerank-1445164035248-10-1-SUCCEEDED-default-1445162513713.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0020-1445162504986-msrabi-pagerank-1445164035248-10-1-SUCCEEDED-default-1445162513713.jhist
2015-10-18 18:27:15,548 INFO [Thread-116] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-18 18:27:15,550 INFO [Thread-116] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-18 18:27:15,551 INFO [Thread-116] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-41.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445144423722_0020
2015-10-18 18:27:15,556 INFO [Thread-116] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-18 18:27:16,558 INFO [Thread-116] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-18 18:27:16,560 INFO [Thread-116] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0020
2015-10-18 18:27:16,570 INFO [Thread-116] org.apache.hadoop.ipc.Server: Stopping server on 30607
2015-10-18 18:27:16,571 INFO [IPC Server listener on 30607] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 30607
2015-10-18 18:27:16,573 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-18 18:27:16,573 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
