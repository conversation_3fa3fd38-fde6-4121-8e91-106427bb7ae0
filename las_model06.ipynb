{"cells": [{"cell_type": "code", "execution_count": 1, "id": "79c6cc2a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "id": "b0aa317a", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/CSV Data/volve_wells.csv\", usecols=['WELL', 'DEPTH', 'RHOB', 'GR', 'NPHI', 'PEF', \"DT\"])"]}, {"cell_type": "code", "execution_count": 3, "id": "53266435", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['15/9-F-11 B', '15/9-F-11 A', '15/9-F-1 B', '15/9-F-1 A'],\n", "      dtype=object)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df['WELL'].unique()"]}, {"cell_type": "code", "execution_count": 4, "id": "d89ffdc6", "metadata": {}, "outputs": [], "source": ["training_wells = ['15/9-F-11 B', '15/9-F-11 A', '15/9-F-1 A']\n", "test_well = ['15/9-F-1 B']"]}, {"cell_type": "code", "execution_count": 5, "id": "8bc9e838", "metadata": {}, "outputs": [], "source": ["train_val_df = df[df['WELL'].isin(training_wells)].copy()\n", "test_df = df[df['WELL'].isin(test_well)].copy()"]}, {"cell_type": "code", "execution_count": 6, "id": "cbba84dc", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPTH</th>\n", "      <th>DT</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>RHOB</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>116914.000000</td>\n", "      <td>21699.000000</td>\n", "      <td>115933.000000</td>\n", "      <td>37587.000000</td>\n", "      <td>37668.000000</td>\n", "      <td>37668.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2154.233438</td>\n", "      <td>77.252247</td>\n", "      <td>51.823119</td>\n", "      <td>0.174302</td>\n", "      <td>6.450603</td>\n", "      <td>2.443072</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1180.976133</td>\n", "      <td>14.350893</td>\n", "      <td>37.606884</td>\n", "      <td>0.085660</td>\n", "      <td>1.478121</td>\n", "      <td>0.166466</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>145.900000</td>\n", "      <td>53.165000</td>\n", "      <td>0.149100</td>\n", "      <td>0.010000</td>\n", "      <td>3.647000</td>\n", "      <td>1.627000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1148.525000</td>\n", "      <td>66.854450</td>\n", "      <td>22.126100</td>\n", "      <td>0.115000</td>\n", "      <td>5.078850</td>\n", "      <td>2.276000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>2122.800000</td>\n", "      <td>72.724000</td>\n", "      <td>52.217000</td>\n", "      <td>0.163000</td>\n", "      <td>6.548700</td>\n", "      <td>2.501000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>3097.100000</td>\n", "      <td>86.132300</td>\n", "      <td>74.201000</td>\n", "      <td>0.212100</td>\n", "      <td>7.728625</td>\n", "      <td>2.577000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>4770.200000</td>\n", "      <td>126.827000</td>\n", "      <td>1124.403000</td>\n", "      <td>0.593200</td>\n", "      <td>13.841000</td>\n", "      <td>3.090000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               DEPTH            DT             GR          NPHI           PEF  \\\n", "count  116914.000000  21699.000000  115933.000000  37587.000000  37668.000000   \n", "mean     2154.233438     77.252247      51.823119      0.174302      6.450603   \n", "std      1180.976133     14.350893      37.606884      0.085660      1.478121   \n", "min       145.900000     53.165000       0.149100      0.010000      3.647000   \n", "25%      1148.525000     66.854450      22.126100      0.115000      5.078850   \n", "50%      2122.800000     72.724000      52.217000      0.163000      6.548700   \n", "75%      3097.100000     86.132300      74.201000      0.212100      7.728625   \n", "max      4770.200000    126.827000    1124.403000      0.593200     13.841000   \n", "\n", "               RHOB  \n", "count  37668.000000  \n", "mean       2.443072  \n", "std        0.166466  \n", "min        1.627000  \n", "25%        2.276000  \n", "50%        2.501000  \n", "75%        2.577000  \n", "max        3.090000  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["train_val_df.describe()"]}, {"cell_type": "code", "execution_count": 7, "id": "5b6da592", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPTH</th>\n", "      <th>DT</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>RHOB</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>33191.000000</td>\n", "      <td>4262.000000</td>\n", "      <td>32498.000000</td>\n", "      <td>3413.000000</td>\n", "      <td>3441.000000</td>\n", "      <td>3441.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1805.400000</td>\n", "      <td>80.380006</td>\n", "      <td>56.864115</td>\n", "      <td>0.203836</td>\n", "      <td>6.445014</td>\n", "      <td>2.453695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>958.156073</td>\n", "      <td>14.698333</td>\n", "      <td>35.935409</td>\n", "      <td>0.095991</td>\n", "      <td>0.811407</td>\n", "      <td>0.129990</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>145.900000</td>\n", "      <td>54.928300</td>\n", "      <td>0.149300</td>\n", "      <td>0.059500</td>\n", "      <td>4.729900</td>\n", "      <td>2.111800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>975.650000</td>\n", "      <td>71.513225</td>\n", "      <td>29.481175</td>\n", "      <td>0.146900</td>\n", "      <td>5.938300</td>\n", "      <td>2.379200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1805.400000</td>\n", "      <td>76.295350</td>\n", "      <td>58.005700</td>\n", "      <td>0.172700</td>\n", "      <td>6.352900</td>\n", "      <td>2.482100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2635.150000</td>\n", "      <td>85.365475</td>\n", "      <td>79.247200</td>\n", "      <td>0.222300</td>\n", "      <td>6.872600</td>\n", "      <td>2.536700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3464.900000</td>\n", "      <td>125.982700</td>\n", "      <td>297.767300</td>\n", "      <td>0.557600</td>\n", "      <td>10.987600</td>\n", "      <td>3.051700</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              DEPTH           DT            GR         NPHI          PEF  \\\n", "count  33191.000000  4262.000000  32498.000000  3413.000000  3441.000000   \n", "mean    1805.400000    80.380006     56.864115     0.203836     6.445014   \n", "std      958.156073    14.698333     35.935409     0.095991     0.811407   \n", "min      145.900000    54.928300      0.149300     0.059500     4.729900   \n", "25%      975.650000    71.513225     29.481175     0.146900     5.938300   \n", "50%     1805.400000    76.295350     58.005700     0.172700     6.352900   \n", "75%     2635.150000    85.365475     79.247200     0.222300     6.872600   \n", "max     3464.900000   125.982700    297.767300     0.557600    10.987600   \n", "\n", "              RHOB  \n", "count  3441.000000  \n", "mean      2.453695  \n", "std       0.129990  \n", "min       2.111800  \n", "25%       2.379200  \n", "50%       2.482100  \n", "75%       2.536700  \n", "max       3.051700  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["test_df.describe()"]}, {"cell_type": "code", "execution_count": 8, "id": "43ab9f62", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPTH</th>\n", "      <th>DT</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>RHOB</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "      <td>21688.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>3141.098875</td>\n", "      <td>77.235857</td>\n", "      <td>39.803246</td>\n", "      <td>0.166648</td>\n", "      <td>7.093603</td>\n", "      <td>2.475232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>314.723749</td>\n", "      <td>14.336048</td>\n", "      <td>57.907158</td>\n", "      <td>0.099200</td>\n", "      <td>1.188313</td>\n", "      <td>0.147635</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>2577.000000</td>\n", "      <td>53.165000</td>\n", "      <td>0.852000</td>\n", "      <td>0.010000</td>\n", "      <td>4.297800</td>\n", "      <td>1.980600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>2869.475000</td>\n", "      <td>66.849300</td>\n", "      <td>9.416350</td>\n", "      <td>0.096000</td>\n", "      <td>6.218475</td>\n", "      <td>2.379000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3140.550000</td>\n", "      <td>72.720750</td>\n", "      <td>27.552000</td>\n", "      <td>0.136000</td>\n", "      <td>7.487700</td>\n", "      <td>2.533000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>3411.625000</td>\n", "      <td>86.093800</td>\n", "      <td>44.877425</td>\n", "      <td>0.217200</td>\n", "      <td>8.001000</td>\n", "      <td>2.581400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3723.300000</td>\n", "      <td>126.827000</td>\n", "      <td>1124.403000</td>\n", "      <td>0.593200</td>\n", "      <td>13.841000</td>\n", "      <td>3.025000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              DEPTH            DT            GR          NPHI           PEF  \\\n", "count  21688.000000  21688.000000  21688.000000  21688.000000  21688.000000   \n", "mean    3141.098875     77.235857     39.803246      0.166648      7.093603   \n", "std      314.723749     14.336048     57.907158      0.099200      1.188313   \n", "min     2577.000000     53.165000      0.852000      0.010000      4.297800   \n", "25%     2869.475000     66.849300      9.416350      0.096000      6.218475   \n", "50%     3140.550000     72.720750     27.552000      0.136000      7.487700   \n", "75%     3411.625000     86.093800     44.877425      0.217200      8.001000   \n", "max     3723.300000    126.827000   1124.403000      0.593200     13.841000   \n", "\n", "               RHOB  \n", "count  21688.000000  \n", "mean       2.475232  \n", "std        0.147635  \n", "min        1.980600  \n", "25%        2.379000  \n", "50%        2.533000  \n", "75%        2.581400  \n", "max        3.025000  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["train_val_df.dropna(inplace=True)\n", "test_df.dropna(inplace=True)\n", "train_val_df.describe()"]}, {"cell_type": "code", "execution_count": 9, "id": "b8bf2664", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "from sklearn import metrics\n", "from sklearn.ensemble import RandomForestRegressor"]}, {"cell_type": "code", "execution_count": 10, "id": "2e547e18", "metadata": {}, "outputs": [], "source": ["X = train_val_df[['RHOB', 'GR', 'NPHI', 'PEF']]\n", "y = train_val_df['DT']"]}, {"cell_type": "code", "execution_count": 12, "id": "65c7f417", "metadata": {}, "outputs": [{"data": {"text/plain": ["69703     109.3850\n", "69704     110.1320\n", "69705     110.8430\n", "69706     111.0100\n", "69707     110.7970\n", "            ...   \n", "149702     74.4395\n", "149703     74.5584\n", "149704     74.6748\n", "149705     74.7656\n", "149706     74.8246\n", "Name: DT, Length: 21688, dtype: float64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["y"]}, {"cell_type": "code", "execution_count": 13, "id": "a7d2649f", "metadata": {}, "outputs": [], "source": ["X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2)"]}, {"cell_type": "code", "execution_count": 14, "id": "0f04dd4d", "metadata": {}, "outputs": [], "source": ["regr = RandomForestRegressor()"]}, {"cell_type": "code", "execution_count": 24, "id": "03aec9a7", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-3 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-3 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-3 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-3 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-3 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-3 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-3 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-3 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-3 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-3 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-3 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-3 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-3 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-3 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-3 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-3 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-3 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-3 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-3 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-3 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-3 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-3 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-3 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-3 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-3 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-3 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-3 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-3 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-3 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-3 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-3 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-3 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-3\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>RandomForestRegressor()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-3\" type=\"checkbox\" checked><label for=\"sk-estimator-id-3\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;RandomForestRegressor<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.ensemble.RandomForestRegressor.html\">?<span>Documentation for RandomForestRegressor</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>RandomForestRegressor()</pre></div> </div></div></div></div>"], "text/plain": ["RandomForestRegressor()"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["regr.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 25, "id": "02577707", "metadata": {}, "outputs": [], "source": ["y_pred = regr.predict(X_val)"]}, {"cell_type": "code", "execution_count": 26, "id": "b4885415", "metadata": {}, "outputs": [{"data": {"text/plain": ["1.638290511065007"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["metrics.mean_absolute_error(y_val, y_pred)"]}, {"cell_type": "code", "execution_count": 27, "id": "f21595a2", "metadata": {}, "outputs": [{"data": {"text/plain": ["3.0088602637715063"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["mse = metrics.mean_squared_error(y_val, y_pred)\n", "rmse = mse**0.5\n", "rmse"]}, {"cell_type": "code", "execution_count": 28, "id": "f1c64988", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x1d6f3465520>]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjUAAAGiCAYAAAAfnjf+AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAB3j0lEQVR4nO3dd3xT9foH8E+SpulOmxaaFhkVUShD9hBcUGQJuK4XLFMERZEpAvcneJ0ILkAUBBeowNV7lalVlrLKLAUryLLsttCVdKZtcn5/1MSmTZuT9GS0/bxfr77uJeOcb4Y9T7/f53m+MkEQBBARERHVcXJPD4CIiIhICgxqiIiIqF5gUENERET1AoMaIiIiqhcY1BAREVG9wKCGiIiI6gUGNURERFQvMKghIiKieoFBDREREdULDGqIiIioXnA4qNmzZw+GDh2K6OhoyGQybNy4sdrHPvPMM5DJZFiyZInV7dnZ2YiPj0dISAhCQ0MxYcIE5OfnOzoUIiIiIguHg5qCggLceeed+PDDD2t83Pfff4+DBw8iOjq6yn3x8fH4/fffsX37dmzduhV79uzBpEmTHB0KERERkYWPo08YNGgQBg0aVONjrl27hueffx4//fQThgwZYnXf6dOnkZCQgCNHjqBr164AgA8++ACDBw/GO++8YzMIIiIiIrLH4aDGHpPJhNGjR2P27Nlo27ZtlfsTExMRGhpqCWgAIC4uDnK5HIcOHcLDDz9c5TkGgwEGg8HqHNnZ2QgPD4dMJpP6JRAREZELCIKAvLw8REdHQy6XPq1X8qBm0aJF8PHxwdSpU23en56ejsaNG1sPwscHGo0G6enpNp+zcOFCvPLKK1IPlYiIiDzgypUruOWWWyQ/rqRBzbFjx7B06VIkJSVJOoMyb948zJw50/JvnU6HZs2a4cqVKwgJCZHsPERERCSdI0eOYNy4cbh69SqUSiXmz5+PBQsWIDg42CXnkzSo2bt3L27cuIFmzZpZbjMajZg1axaWLFmCixcvQqvV4saNG1bPKysrQ3Z2NrRarc3jqlQqqFSqKreHhIQwqCEiIvIyJpMJ7777Lv71r3+hrKwMLVu2xDfffIPbbrsNCxYscFnqiKRBzejRoxEXF2d124ABAzB69GiMHz8eANCrVy/k5ubi2LFj6NKlCwBg165dMJlM6NGjh5TDISIiIjfLzMzEuHHjsG3bNgDAP//5T6xatQohISHQ6/UuPbfDQU1+fj7Onz9v+XdqaiqSk5Oh0WjQrFkzhIeHWz1eqVRCq9XijjvuAAC0adMGAwcOxMSJE7Fy5UqUlpZiypQpGDFiBCufiIiI6rB9+/ZhxIgRuHbtGlQqFZYuXYpJkya5rajH4dTjo0ePolOnTujUqRMAYObMmejUqRMWLFgg+hhff/01WrdujX79+mHw4MHo06cPVq1a5ehQiIiIyAuYTCYsXLgQ9913H65du4bbb78dhw4dwtNPP+3WKmWZIAiC284mEb1eD7VaDZ1Ox5waIiIiD7px4wZGjx6Nn3/+GQAwatQorFixAkFBQVUe6+rrt+Ql3URERNQw/PLLL3jiiSeQlpYGf39/LF++HOPHj/dYDzluaElEREQOMRqNePXVV9GvXz+kpaWhTZs2OHLkCJ588kmPNsXlTA0RERGJlp6ejvj4eOzatQsAMH78eHzwwQcIDAz08MgY1BAREZFIO3bsQHx8PG7cuIHAwECsWLECo0eP9vSwLLj8RERERDUqKyvDSy+9hAceeAA3btxA+/btcfToUa8KaADO1BAREVENrl27hieeeAJ79uwBAEyaNAlLliyBv7+/h0dWFYMaIiIisikhIQGjR49GZmYmgoKCsHr1aowYMcLTw6oWl5+IiIjISmlpKebOnYtBgwYhMzMTHTt2RFJSklcHNABnaoiIiKiCy5cvY+TIkThw4AAA4LnnnsM777wDPz8/D4/MPgY1REREBADYsmULxo0bh+zsbISEhODTTz/FY4895ulhicblJyIiogaupKQEs2bNwrBhw5CdnY2uXbvi+PHjdSqgARjUEBERNWipqam4++678d577wEApk+fjn379uHWW2/18Mgcx+UnIiKiBuq7777Dk08+CZ1Oh9DQUHzxxRcYPny4p4flNM7UEBERNTAGgwHPP/88Hn30Ueh0OvTs2RPJycl1OqABGNQQERE1KOfPn8ddd92F5cuXAwBmz56NPXv2oHnz5h4eWe1x+YmIiKiB+Oabb/DUU08hLy8P4eHhWLNmDYYMGeLpYUmGMzVERET1XFFRESZPnox//vOfyMvLQ58+fZCcnFyvAhqAQQ0REVG9dubMGfTs2RMrV64EAMybNw+7d+/GLbfc4uGRSY/LT0RERPXU119/jaeffhoFBQVo1KgRvvzySwwYMMDTw3IZztQQERHVM4WFhXjqqacwatQoFBQU4L777kNycnK9DmgABjVERET1yqlTp9C9e3d8+umnkMlkWLBgAXbs2IHo6GhPD83luPxERERUT3zxxRd47rnnUFhYiMjISKxbtw59+/b19LDchjM1REREdVx+fj7Gjh2L8ePHo7CwEHFxcThx4kSDCmgABjVERER12m+//YZu3bph7dq1kMvleP3115GQkIDIyEhPD83tuPxERERUBwmCgE8++QRTp05FcXExoqOjsX79etxzzz2eHprHMKghIiKqY/Ly8vD0009j/fr1AICBAwdi7dq1aNSokYdH5llcfiIiIqpDjh8/js6dO2P9+vVQKBR46623sG3btgYf0ACcqSEiIqoTBEHAihUrMHPmTBgMBjRt2hQbNmzAXXfd5emheQ0GNURERF5Op9Phqaeewn//+18AwNChQ/H5558jPDzcwyPzLlx+IiIi8mJHjx5F586d8d///hc+Pj549913sWnTJgY0NnCmhoiIyAsJgoBly5Zh9uzZKC0tRfPmzfGf//wHPXr08PTQvBaDGiIiIi+Tk5ODJ598Ehs3bgQAPPzww/j0008RFhbm2YF5OS4/EREReZGDBw+iU6dO2LhxI3x9ffHBBx/gf//7HwMaERjUEBEReQGTyYR33nkHd999Ny5duoSWLVviwIEDmDJlCmQymaeHVydw+YmIiMjDsrKyMHbsWGzbtg0A8Pjjj2PVqlVQq9UeHlndwpkaIiIiD9q3bx86duyIbdu2QaVSYcWKFdiwYQMDGicwqCEiIvIAk8mEhQsX4r777sPVq1dx++2349ChQ3jmmWe43OQkLj8RERG52Y0bNzBmzBj89NNPAID4+HisWLECwcHBHh5Z3caghoiIyI1+/fVXjBw5EmlpafD398cHH3yAJ598krMzEuDyExERkRsYjUa8+uqr6Nu3L9LS0tCmTRscPnwYEyZMYEAjEc7UEBERuVh6ejpGjRqFnTt3AgDGjRuH5cuXIzAw0MMjq18Y1BAREbnQzp07ER8fj4yMDAQEBGDFihUYM2aMp4dVL3H5iYiIyAXKysqwYMEC9O/fHxkZGWjXrh2OHj3KgMaFOFNDREQksWvXruGJJ57Anj17AAATJ07E0qVL4e/v7+GR1W8MaoiIiCSUkJCA0aNHIzMzE0FBQVi1ahVGjhzp6WE1CFx+IiIikkBpaSnmzZuHQYMGITMzEx07dsSxY8cY0LgRZ2qIiIhq6cqVKxgxYgQOHDgAAHj22Wfx7rvvws/Pz8Mja1gY1BAREdXC1q1bMXbsWGRnZyMkJASffPIJ/vGPf3h6WA0Sl5+IiIicUFJSglmzZmHo0KHIzs5Gly5dkJSUxIDGgzhTQ0RE5KCLFy9ixIgROHToEABg2rRpWLRoEVQqlYdH1rAxqCEiInLAxo0bMX78eOTm5iI0NBSff/45HnroIU8Pi8DlJyIiIlEMBgOmTZuGhx9+GLm5uejRoweOHz/OgMaLMKghIiKy48KFC+jduzeWLVsGAHjhhRewd+9etGjRwrMDIytcfiIiIqrBt99+i6eeegp6vR4ajQZr167FkCFDPD0ssoEzNURERDYUFxfj2WefxeOPPw69Xo/evXsjOTmZAY0XY1BDRERUydmzZ9GzZ0+sWLECADBv3jz88ssvaNq0qYdHRjXh8hMREVEF69atw9NPP438/Hw0atQIX375JQYMGODpYZEInKkhIiICUFhYiIkTJyI+Ph75+fm49957kZyczICmDmFQQ0REDd7p06fRo0cPfPLJJ5DJZFiwYAF27NiB6OhoTw+NHMCghoiIGrQ1a9aga9euSElJgSaiMf69Yj0GjpkKmVzh6aGRgxjUEBFRg1RQUIBx48Zh3LhxKCwsRFBMRwT88118lhqEkasPos+iXUhISfP0MMkBDGqIiKjBSUlJQdeuXbFmzRpAJoe6Tzw0j70CRVCY5THpumJM/iqJgU0dwqCGiIjqBaNJQOKFLGxKvobEC1kwmoQqjxEEAZ988gm6deuGP/74A4ogDSJHvIHQ3iOrLDeZn/3KllM2j0Xex+GgZs+ePRg6dCiio6Mhk8mwceNGy32lpaWYM2cO2rdvj8DAQERHR2PMmDG4fv261TGys7MRHx+PkJAQhIaGYsKECcjPz6/1iyEiooYpISUNfRbtwsjVBzFtQ7LN5aO8vDyMGjUKEydORHFxMdS3d0PU+A/g16x9tccVAKTpinE4NdsNr4Jqy+GgpqCgAHfeeSc+/PDDKvcVFhYiKSkJ8+fPR1JSEr777jucOXMGw4YNs3pcfHw8fv/9d2zfvh1bt27Fnj17MGnSJOdfBRERNVgJKWmY/FUS0nTFVren6YrxzFdJWLrjLI4lHUeXLl2wbt06KBQKTJ69AOqH5kMRoBZ1jht5xfYfRB4nEwTB6Tk1mUyG77//vsYdSo8cOYLu3bvj0qVLaNasGU6fPo3Y2FgcOXIEXbt2BQAkJCRg8ODBuHr1qqjyOb1eD7VaDZ1Oh5CQEGeHT0REdZzRJKDL69uRW1hq835BEJCf/CNydq2GUFaKW265BRs2bEBmYAtM25As+jzrJ/ZEr5bhEo264XL19dvlOTU6nQ4ymQyhoaEAgMTERISGhloCGgCIi4uDXC7HoUOHbB7DYDBAr9db/RARES3fda7agMZkKEDmpkXI/vkjCGWl8G/ZDe+uT0Dv3r3RONhP9Dmi1H7oHqORasjkQi4NaoqLizFnzhyMHDnSEpGlp6ejcePGVo/z8fGBRqNBenq6zeMsXLgQarXa8sO9N4iI6jYxSb1ijvHxnj9t3mdIO4e0L6ah8Mw+QK5A2P0T0PjRBXh/bzqMJgHdYzSIUvtBZuccMgAvD42FQm7vkeQNXLb3U2lpKR5//HEIgmDZEMxZ8+bNw8yZMy3/1uv1DGyIiOqohJQ0vLLllFUOTJTaDy8PjcXAdlGij3PwQhYKS4xWtwmCgLxjW5Cz+zPAVAZFSGM0Gj4Hqug7APyd9NurZTheHhqLZ75Kqvb4YQFKLHykvUNjIs9ySVBjDmguXbqEXbt2Wa2babVa3Lhxw+rxZWVlyM7OhlartXk8lUoFlUrliqESEZEbmZN6K8/LmHvCrBjV2WYQYTQJOPhnFhIvZAEQ0OvWCKxNTLV+THE+sn5ciqKziQAA/1Y9ET54OhR+Qdbn0v8dTIUGKKssXwX4KvD0PbdiSt9WnKGpYyQPaswBzblz57B7926Eh1snVvXq1Qu5ubk4duwYunTpAgDYtWsXTCYTevToIfVwiIjISxhNAl7ZcqpKQAOUl07LUN4Tpn+s1iqYSEhJw9zvfrMKPpbvvmD1fMP1M7i5aRGM+huA3Adh9z+J4C5DIZNVDUpe2/o7/kjTYdWeVJtjKSox4g5tsFMBjdEk4HBqNm7kFaNxcHkuDgMj93E4qMnPz8f58+ct/05NTUVycjI0Gg2ioqLw2GOPISkpCVu3boXRaLTkyWg0Gvj6+qJNmzYYOHAgJk6ciJUrV6K0tBRTpkzBiBEjuHEYEVE9djg1u0rZdUUVe8KYK40SUtJqXCISBAF5R75Hzq9rAJMRPqFaRAybA1VUq2qfk11Qio/3pFZ7P2A7uLJHqmU1cp7DicJHjx5Fp06d0KlTJwDAzJkz0alTJyxYsADXrl3D5s2bcfXqVXTs2BFRUVGWnwMHDliO8fXXX6N169bo168fBg8ejD59+mDVqlXSvSoiIvI6Ynu9mB9XUmbCC9+eqPZxxiI9bv7v1b/yZ4wIuKMPosYtrTGgEcOZhnvV9crhVgvu5fBMzX333YeaWtuIaXuj0Wiwbt06R09NRER1mNgy6sbBfkhIScOsb0+gwGC0+Zjiq6eQuXkxjHmZgEIJTb+JCOo4yOZyk7PEBmHOLquR9FxW/URERFRR9xiNzcRcMxkArdoPOQUGPLfuuO0gQTBBf+h/yN3zJSCY4BMWjUbD58I38lbJxys2CHNmWY1cg0ENERG5xfZT6dUGNED5xX/+kFi8ts32rIexUIfMre+hOPUYACAg9l6EP/Ac5KoAScdpDq7ENtxzdFmNXIdBDRERuZx5iaYmgb4KXMkptDnrUXz5N2RueRvG/GzIfHwRFvcMgjr0l3S5CYClGZ8jDfccWVYj12JQQ0RELmdviQYACkqMWPjjH1a3CSYjdInfQLd/fflyk+YWNHpoLnwbtZBkXHIZULGZsdaJaiVzd+J0XbHNGSZHZ37IeQxqiIioRlL0XnFm6cWYn4PMre+g+FJ5BVRgu37Q9J8Mua90Mx4mAZg/pA0iglVOvzaFXIaXh8Zi8ldJkAFWgY0zMz/kPAY1RERULbG9V+wFPo4uvRRdTEbm1ndgKsiFTKmCpv+zCGrfr/YvyIaIYBWGd2xSq2MMbBeFFaM6V3mvnJn5IecxqCEiIpvEbmkgJvDJKTCIOqdgMkK3fwN0BzYAEKCMaI6I4XPgG9FMoldVlVS5LgPbRaF/rJYdhT2IQQ0REVmYZ1zSdUV4bdtpu71XTCbBZvl1xcCnf6wWr207bffcZXlZyNzyNgxXUgAAQR0eQFjcJMiVrkmwdUWui0IuY9m2BzGoISIiALaXmqpj7r3y0qYUu4FPsEpp95hFfx5D5rb3YCrUQebrj/ABzyEw9j4nXoVtzHVpGBjUEBFRtUtN9mQX1Nx3Jk1XjMQ/M6t/jMmI3L1fQn/wvwAAZeMYNBo+F0pN7XJcgL9nYuYPaYPXtp1mrksDwKCGiKgecaZSqaY2/1K4cLPA5u1l+pvI3Pw2DNfK+9cEdRoMTd+nIPPxdfgcNc3EDGwXhQHtopjr0gAwqCEiqiec3SVaTA+ZymQANIG+yCoosfvYPeduVrmt8MIRZG19D6biPMh8AxA+8HkEtrnboTGYDWmvRdLl3CozMSO6NYOhzITEC1noHqNhrksDwKCGiKge+OFkGp5dl1Tl9sqVSrY400NGAPDa8HZ4bdupGpvOhQX6IrtC4CMYy5D76xroj3wPAPDV3oaIYXOgDHNuGUjlI8eykZ0BwDITczGzAOsPX8b7O85aHqcNUWFk92ZoERHImZp6jEENEVEd98PJ65iy/rjN+8TsEu1MSXOgSoEB7bSQy2Gz6Zz53N1jwpCQkgEAKNNl4OamxShJOwMACO4yFGH3PQmZj9Lh85sZykzYfiodA9tFoVfLcCSkpGHJjnNVq7H0Bry/45zl32JmsKjukXt6AERE5LyElDQ8u+64Vav/yswJuwf/zLJ5v7nNvyPzFgUGI5bvOoeB7aLw4ROdEBZoOzDZe7Y8SbjwbCLSPp+KkrQzkKsC0ejhf0ET93StAhqzf33/G74/fg37z2fi35t/F5UblPbXDNbSHeewKfkaEi9kwVjTm0h1gkwQhDr3Ker1eqjVauh0OoSEhHh6OEREHmE0CeizaJfofJhQfyXeerS9zdkJc/UTUHXGpabjvfZQO7y8+XerJaaKhLJS5PzyOfKObQYA+EbdjkbD58BHHSnyLO7D2RvXc/X1m0ENEVEdUrG6KTPPIKqpXUUyoNr8Gkf61IhRmpuOzE1voST9PAAgpNvDCL13DGSK2s/OuIJ5pqqm/COqHVdfv5lTQ0RUR0gVdJgb4mUWGKySZs1t/g9eyMLEL4+isMTo9DkK/tiHrB+XQSgphNwvGOFDpiPgth61Grerick/Iu/GoIaIqA5wtjleZeb8mvhPD1luq7jsopDL0LtVBJ6+p6VV9ZDo45eVIHvXJ8g//gMAQNUkFhHDZsMnpFEtR+4e5vfncGo2S8DrICYKExF5OVc3xzOXfSekpFnO17VFGAJ8FQ4dpzT7GtK+fMES0IT0fAyRI9+sEtCE+isRFqB0KDHZ3ZwpcyfP40wNEZGXc6Y5niOsN6gEXtvm+BJXwalfkfXTcgglRZD7hyDiwVnwv7WLzcfmFpViRlwrqxJrbyPVzt3kXgxqiIi8nDtmDczLLrYa+FWm8pHDUGYCAJhKDcjZ8THyT/5cfl/TdogY+gJ8giNqPEZuof1OxJ7gip27yX0Y1BAReTmxswbzh7RBRLAKFzMLseSvfBgpl6x8FTL0vFWDPefK+92UZl7BzU1voTTzEgAZ1Hf9E+reIyGT21+2+iLxkoQjc4yfUo7iUhN37q6HGNQQEXk5c3O8mrYj0Kr9MK53DBRymaWJ3Of7U5FbVP0u2o4qMQqWgCY/ZSeyf/4IQqkB8sBQRDz4AvxbdBR9LHc3E6kYwBSXmhAaUF5Wnlv49/vDnbvrPgY1REReTiGX4eWhsTa3IzD/e0S3pth68rpl36N0vcHymAClAoPaR2Lv2UzcyK/dso+ppBjZ21eiIGUHAMCveQdEPDgbiqCwWh3X1SrHULrCUggAZsS14n5Q9Qib7xER1RG2+tQE+Cogl8mQbyiz+3xb+zM5ouTmRWRuWoTSrCuATA5175FQ93pc1HKTJ9h7veYZrn1z+jKYcRM23yMiasCMJgEH/8xC4oUsAAIWP9oBRy9lY82BS8gtKnWoQZ6zAY0gCMg/uR05Oz6GUGaAIkiDiKEvwK9ZByePKF6gSoECQ82vUQZg0j0x2HwizSrg0wT6Iqua7RsA9qSpjxjUEBF5qYSUNMz97jervI/luy+4dQwmQyGyf/4IBad+AQD4teiEiAdnQREY6tLzhgYoMf6uFqLKvqfH3Y5pca3w4sA2li0kGgf7IV1XhBnfnLD7fPakqT8Y1BAReaEfTqaJKq92pZIbf+LmpkUoy74GyOQIvWc0Qno8CplMfN9WuQw17iBuy6B2Wix/ojO2nrwu6vEtIgIAlOceVZxxKZ/dso89aeoPBjVERF7mh5PXMWX9cY+dXxAE5Cf/iOydqwFjKRTBEYgYNht+t7QVfYzB7SIxulcMcgpK8Nw6x3b/HtWzORRymehgo7rHia0aY0+a+oNBDRHVGxV3sG4c7IcuzcNw7FKO5d/eUt1SeZwVx5WQkoZn13kuoDEZCpCVsByFf+wFAPi37IbwITOg8HcsqXNAuyjLrMkKeWeHNuI8dV2HzHwDIoJU0IaorCq5KouqISixVzUGsCdNfcOghojqBVuVQZWXPqJq2YekpmCkNuM0j6t/rBavbDnl1NikYEg/j8xNi1CWmwbIFQi7dyyCuz3k0HKTWcXZE/Pu3+9vP4vlu8/bfe4bP/xh+f/29p8admdUjZ/BwHZRWDGqalDFnjT1E0u6iajOE7uDtfnSt2JUZ4cvZjUFI2KPVd04zeOaHne7Uztj15YgCMhL2oqc3Z8CxjIoQhqj0bAXoWrS2uFj2SqTLikz4cvEiziUmo2fT2VIOvYokSXZUgSkVHss6SYit6mLv/gd2cG64saN/WO1ol9bdcGIeXdrMUFSTeM0j2vVHvdWNgGAsTgfWT8uRdHZRACAf6ueCB88HQq/IKePWXFJZ+EPp7B6b6rDycJiiS3JrpxETPUTgxoiAiDNTIQnOLqDtaO9ScQEI2KCJHvjFAAUONBzRgqG62dwc/NiGHUZgNwHYfc/ieAuQyGTORfIymXAhD4xUPv7YlPyNWw/lYGtJ9MkHnVVLMkmMwY1RCTJTISnOHtBE/s8McGImCDJmy68giAg78hG5Pz6BWAywidUi4hhc6CKalWr45oEYPXeVKzemyrNQEViSTaZMaghauCkmonwFGcvaGKfJzYYqfy4ykt5EUEqh8foCsaiPGT98D6Kzh8GAATc0Rvhg6ZCrgr0yHge6hiN9k3UeG3baYefy5JsqoxBDVEDJ9VMREXuzM2x14ukspouhLbG7UyvFFtLedoQFUIDlJaNFD2h+OppZG5eDGPeTUChhKbfRAR1HOT0cpMUCkuMiFI7HpiyJJtsYVBD1MA5OxNRHXfn5tTUi6Symi6E1Y17/pBYhxq4VbeUl6E3WG6r7caSjhIEE/SHvkPunrWAYIJPWDQaDZ8L38hb3TgK2/yVclGzNKEBSqvtIliSTbYwqCFq4GrbtbUiT+XmVNeLpHKfmuouhDWN+7l1SZh0TwxW7Um128BNzFKeOkAJPx8F0vXWwZPOwc0pxTIW6pC59T0Upx4DAAS0uRfhA56DXBUg+bmc0TZKjU0n7CcTfziyM+RyWZ2qzCP3Y1BD1MA500re1jINAI/m5pgbvDnaUVhMILL5RBo+fKITXtt2usYGbmKW8nILS/H1hL8v0BGBKpgEAU9/daz2b0IlxVdSypeb8rMh8/FFWNzTCOrwgEeXmyoK8FWgkcilp8wCA4Z3bOL0uepiuwJyHIMaogbO0Vby1S3TjOjWVPLcHEfZ6kVi71xic4rCAlXYN6dvjRdGsUt05gt0QkoaXvjvCYdK0sUQTEboDn4L3b515ctNmlvQ6KG58G3UQtLz1NZ7j9+JM+n5oh5bmwqnutqugBzHoIaIRLeSr2mZ5v0d50SdS4rSZin/6nYkp8heAzdHlvLEdkF2lLEgB5lb3kXxpWQAQGC7vtD0nwy5r79Tx/P1kaOkzCThCIHGQUq8+lB7AMASOx2Ua1vhVJfbFZDjGNQQEQDbyzcVgwV7yzRi1baniNR/dUuZU9Q9RlMlobWy0AAlyspMmPu/3yQPaIounUDmlrdhKsiFTKmCpv9kBLWPq9Uxn7uvpeiAVaz3R3RGz1vD0WfRLrvvgQBgRLdm2HryusMBbF1vV0COY1BDRBY1zUQ42rm3Mil6irjir25ncorMbO0KXmpnVkNXWIrRnx92aIz2CCYjdPs3QHdgAwAByohmiBg+F74RzWp13Ci1H6b0bYVWjYPw3LrjkgVhmfkG0d+nQJXCaj8sRwJYV7QrIO/GoIaIRHFk2UhMbo6jXPVXt6M5RWa2ZowCVQq7Wx1IPTtTlpeFzK3vwHD5NwBAUIcHEBY3CXJl7bvsFpUYsf1UOsICVZKOu3Gwn+jvU4HB+v10JICVul0BeT8GNURezluqNsQu08yIux0bjlyuMTenOjW91tr+1V3x2BGBKkBWPmPQONgP/WO1onKKzKqbMap8AXa1otQkZG59F6ZCHWRKP2gGPIegtvdLdvzcolJM/ioJT/ZuIcnxzLNeXZqH4cvEi04dw5EAVsqlRaobGNQQeTFvqtoQu0wzpe9tmNL3NocDMXuvtTZ/dds6dkXm89irbgIc2xXcVQSTEbl7v4L+4LcAAGXjGDQaPhdKjfMlzzX5Pvma6McGqhQoMBirnfUadmcU7n17d62WMsUuG9VmaZHqJrmnB0BEtplnAyr/8jdPvyekuH7344rMyzTA3xcos8rLNObcnOEdm6BXy3BRAY291+rsX93VHdvWebafSrc77trmFtVWmT4TGevnWQKaoI6DoB31jssCGgFAdkEpNIG+VT73ygJ85Xj70Q5YOaoztJX6z2jVfpYmhlK9f/YCXUe+s1Q/MKgh8kJiKo1e2XIKRpN75wvMpd+2LljOlsbae60CgP/7PgUdm4YiSu1X7YVVhvIZl8pNAsXMqjjynnoy/6LwwhGkfTEVhqunIPP1R8SwOeXdgZWu3yzzoY7RAKoGBxUVlZjw3LrjAIB9c/pi/cSeWDqiI9ZP7IlfZ9+PzSfSJM/NsccV31nyXlx+IvJC3ly1Ya/021FiZj6yCkrQe9FO/KPLLaK2K3Dk2GZi31NP5F8IxjLk7lkL/eHvAAC+kS0RMXwulGHuuyD3j9Wie4wG/958ymqLB6txwjrfpeL7mHghS7IZGkeXjaT+zpL3YlBD5IW8vWrDXhM6R4h9DdkFpVi1JxWT7onB5hNpohJ6nXl/anqO0STAJAgI9Vcit6j6XjRSKtPdwM3Ni1By/QwAILjLUITd9yRkPkq3nL9iAKGQyxCsUiL+00PVPr664FCq76qzy0ZSfmed4S0J//UdgxoiL9SQqjYcfQ2bT6Th19n3293TyZlj1/Qce8nGrlB47iCytr0Pk6EAMlUgIgZNQ8Add7nt/LYCiMwCg6jnVg5ipPqu1sXdub0p4b++Y1BD5IXcUbXhLX852nutFZlnAY5dyhH1V7cjxwYAbYjK5nvqqi0NqiMYS5Gz+3PkHdsMAPCNuh0Rw16EMlTrphGUsxVAOBtwi/0sbC0tCgCe7N3CsgRWl2Y4uE2DezFRmMgLubpqIyElDX0W7cLI1QcxbUMyRq4+iD6Ldrm9ogqwfq1iiV3KqOl9tKW4zITtp9KtbnN3CXdpbjrSv3rREtAEd3sI2vhFbg1oAnwV+PqpHtg3p2+VC645OHEkYRsQ91kE+iqgDrBeVtOq/bByVGcsGNpWVCWdN/HWhP/6jEENkYSMJgGJF7KwKfkaEi9k1eqXlauqNrytVBz4+7VqAn1FPb6m2YLKn0H/WC0+fKITwgLt56DkFpbimUrvgTtLuAv+2Ie0z6eiJP0c5H5BaPTofGj6PgWZwj35M2ajezZDz1vDcTg1u8p3uTYBt/lzrhy4mBWWGKErLMWMuNstVVO2Aqu6wpGEf5IGl5+IJOKKdXOpqza8eYO/ge2i0Ld1JHou3IHsguqTcMMDfdGleZjN+2x9BqF/XUArbjIpkwFCDfHm3O9+s7wH7kjGFspKkL3rU+Qf3wYAUDVpg4hhs+ET0tjl57blP0evYlNymlWVU8Xvsthd3W3pH6vFvzefAlD1MzZ/BzccuYx9c/rWqVkZW7w94b8+YlBDJAFXrptXrNqobR6MN5eKA4CvjxxvPtwek79KsoynsqyCEtz79u4qF8/qPgNbO2bXFNCYnzNtw3EsHdEJmXniEmOdVZp9DZmbF6Mk4wIAIKTHYwi9exRkCs/9ei5/z6zft8rfZWcD7sOp2dWWhAOe/w5KqSEl/HsLBjVEteSu2Q8pZoLE/kW4//xNlydkVhegVTcLUFHlC6wr8l62nkzDrtMZKCytedft2ig49SuyfloOoaQIcv8QRDw4C/63dnHZ+WrD1nfZmTLphjR7wW0a3I85NUS15I51c6nyYMT+Rbh89wWXJg7bS1Qe2C4Kv86+H5pq8mAqJ1m6Ku/FVQGNqdSArIQPkLnlbQglRVA1bYeo8cu8NqAxc+S7XF1+WUOaveA2De7HmRqiWnL1X55SzgQ5UuLsqpJTsUt1xy7l1JhbU/ECW5f+qi/NuoKbmxah9OZFADKoez0OdZ8nIJMrPD000X78K/isbjbvh5PX8dKmFKvPzzyr2D9W26BmL2qTf0SOY1BDVEuu/stTyjwY81+Ok79KqtIPxNZxpU4cdiRAcyRYrCt/1een7ET2zx9BKDVAHhCKiKEvwL9FR08Py2FrEy9hbeIlm8ufb2w7hdV7U6s8J61C0Frdd7C+zl5wmwb34fITUS0527dDLKlngqorFbdFiuWGig7a2f+n4vkcCRbtfQaeZiopRua2Jcja9j6EUgP8mncoX27ysoBGG6JCgK/4GaPKy5/VBTRmAv4OWhvaJpOO7lxPznE4qNmzZw+GDh2K6OhoyGQybNy40ep+QRCwYMECREVFwd/fH3FxcTh37pzVY7KzsxEfH4+QkBCEhoZiwoQJyM/Pr9ULIfIUV6+bu2ImaGC7KOyb0xdT7r9N1OPtBUximvklpKThuXVJos/XpXkYxLxliedvYuvJ6xjRranbGuQ5ouTmJaSvnYmClB2ATA51n3g0fvw1+AR51/LK/CFtsODBtigsMYp+TsXcpq3J12oMaMzMQav5O1hxJ++63JOGvIPDQU1BQQHuvPNOfPjhhzbvX7x4MZYtW4aVK1fi0KFDCAwMxIABA1Bc/Pcvxfj4ePz+++/Yvn07tm7dij179mDSpEnOvwoiD3NVozzAdTNBCrkMvW+LEPXYmgImMUnM5seI3QSycbAfjl3KgZjehct2X8C0Dcl4f8c5BPgqIPOSP4AFQUD+yZ+RvnYmSrMuQxGkQeSI1xHae6RX5s9oAn3x6tZTDj/PPLs2b+Nvop9jDpI5e0FSczinZtCgQRg0aJDN+wRBwJIlS/DSSy9h+PDhAIC1a9ciMjISGzduxIgRI3D69GkkJCTgyJEj6Nq1KwDggw8+wODBg/HOO+8gOjq6Fi+HyHNctW5eUx5MbWeCaltyKjZHRhAEUbMoFc+39eR1sS/DwpFZBlcylRQh+6cPUXDqFwCAX4tOiHhwFhSBoR4dV02yC0pq7B9jT16x+Pe+ruRAUd0jaU5Namoq0tPTERcXZ7lNrVajR48eSExMBAAkJiYiNDTUEtAAQFxcHORyOQ4dsr2dvcFggF6vt/oh8kau+suztjNB1eW72Fs6EwAMalceqNnKkRGbxJyuF9/Azhyg1dULX8mNP5G2Znp5QCOTI/SeMWj8+CteHdBEqf2gCVK55VyaQGW9qWwi7yNp9VN6evlGcJGRkVa3R0ZGWu5LT09H48bWrb99fHyg0Wgsj6ls4cKFeOWVV6QcKlGd4+xMkL2mfdWVnJq3Evhs/0V8tv+izUoXKUupQ/2VeOvR9pbjd4/RQBPoi+yCEsnO4UqCICD/RAKyd6wCjKVQBIUjYviL8LulraeHZtf8IW0QFuhcUCND+dJVlsjP6fXh7bjMRC5TJ6qf5s2bB51OZ/m5cuWKp4dE5BGOzgSJbdpXMWlzQu8WAFAln8VWoz8pZ1M+jLeecVLIZXh9eDvJju9KJkMhMjcvRvZPHwLGUvjf2hVR45fViYAGAM7dyEf3GA20IY59nuZv32vD2yFKRDXdxLtjMLgDUwzIdSQNarRaLQAgIyPD6vaMjAzLfVqtFjdu3LC6v6ysDNnZ2ZbHVKZSqRASEmL1Q0Q1s5fvAvzdkRcoDyK6x2jwQ4rtGVNbz5GilNqc6Nzz1qo9dgZ3iMLT98TU4uiuZ0g/j7QvpqHwj72AXIHQ+55Eo8cWQBGg9vTQRPt8/0UAwL+HxTr0PPPy5+AOUXh5aGyN34OJd8fg/4b8fXwpd7QnMpN0+SkmJgZarRY7d+5Ex44dAQB6vR6HDh3C5MmTAQC9evVCbm4ujh07hi5dyluC79q1CyaTCT169JByOEQNmjNN+8Q+5/3tZ9H7tgh0j9FYkpidISbRed7gWNx5S2iVDrWeJggC8pK2Imf3p4CxDIqQRmg0bA5UTVp7emgOyy0qtZRZrxzVGXO/+63KRqChAUq8+VA7hAWqbC5/VreMqQlU4vXh7axmaFyxoz0R4ERQk5+fj/Pnz1v+nZqaiuTkZGg0GjRr1gzTp0/H66+/jlatWiEmJgbz589HdHQ0HnroIQBAmzZtMHDgQEycOBErV65EaWkppkyZghEjRrDyiUhCzjTtE/uc5bvPY/nu85YL0fS42/H+jrN2n6cJVFoFJmJbxZsviN4S2JiK85H14zIUnj0AAPBv1RPhg6ZB4R/s0vMO7aDF3vNZNncery3zZ2/O3Tr4ZxYSL2QBENDr1gj0FLHcKSbvy5U72hM5HNQcPXoU999/v+XfM2fOBACMHTsWX3zxBV588UUUFBRg0qRJyM3NRZ8+fZCQkAA/v7/XW7/++mtMmTIF/fr1g1wux6OPPoply5ZJ8HKIyMyZpn2O5siYL0Tj/8rDsWdk92aQ/VVXJfZCCZgb9x33iuZ6hutncHPzYhh1GYDcB2H3j0dwl2GQubhBTliAEu8+3gn3LN4NQFxQE6X2Q3SoH45dyrX72IuZhZb/b+5hJLaPUUU17dzt7D5m1e3oTlSZTBAEb/g94RC9Xg+1Wg2dTsf8Gqq3avuL3GgS0GfRLrs9aH6dfT+OXcrBjbxiRASpMPM/x5GRJ77iyNHqFzOxyw3m1+GKXbgdIQgC8o5uQs4vXwCmMvioIxExfA5UUbe7/NwyACtGdYba3xcjVx8U9RxNoBKvDm2H5zeICwaj1H7YN6evS4OFxAtZosa/fmJPS2DEpar6xdXXb25oSeSFpPhFLqZp37A7o3Dv27utzhOkcuzXggAgq6AEmkAlcgpKRc+mVFxuqGnJwl6ejzsYi/KQ9cP7KDp/GAAQcPtdCB80FXK/IJefWxOoxJsPl5e6b0q+Jvp52QWlWLDld9Gfh9hNUWvD0SVRLlWRoxjUEHkZKX+RV5e8qVX7YdidUVi1J7XKefINZU6N++GOTfDZ/ot2d/82My83zP3uN/x78ymrbrYVAzgpe+E4o/jqaWRuXgxj3k1A4QNN34kI6jTY5ctNABAe6IvEef3g61NeqOro8qCjPX7SdUUOPd5RjiyJOrtURQ0bgxoiL+KKX+S2kje7NA/DvW/vljRHJS5Wi24xmioBVE0E4K+kV+sckYoBnKc6CwuCCfrD3yH317WAYIJPWBQaDZ8L38iWbhtDx6ZqHLuUY5m5MpfQu2rm6rVtp+Hvq3DZ7Icj23I4U71HxKCGGoS6kmjoTEm1mNdROXkz8UKWZBfGihcihVxmFUCdy8jD8t0XHD5mxQBu16z7qlRNuZqxUIfMbe+h+M9jAICANvcgfMAUyFUBbhsDAOz84yZ2/nHTaubKvKRYU0AqAxDmxHuWU1Di0mUdR/Yxc6Z6j6hOdBQmqo2ElDT0WbQLI1cfxLQNyRi5+iD6LNpl1RnXWzhSUl2b1yHVhcBWn5mKXY+VCud3ozYHcL0X7XJrQFN8JQVpnz+P4j+PQebjC82AKYgYOtvtAU1FFbs5m5cUQwOUNh9r/kxe/6vLryOhu60Gi1Krbh8zTaAvPnzi72DKmeo9IgY1VK+J3SbAWzhbUu3o67iYWSDqcZpA6wtn5UmhmjbUTEhJwxIRvWvsqSkvJMBXul9hgmCC7sB/kLH+XzDmZ8NHcwu0o99FcMeBbsmfqXFsf/2vOdgY2C4Kx17qjxlxrRDqb/0Z/d3lN7razUrtncu8rOMqA9tFYf6QNlbfr6yCEry27ZTlu2yvW7W5EzU3x6SKWNJN9Za9UmDzsomry1gdYa8M2xZHX4fRJKD3Wzvt7pwdVanc25yLU/Hf1S1/uasMW2xSsj3Gghxkbn0PxRePAwAC294PzQPPQu7rL8HRpVWx3Bmwv7Rqq5JOjKUjOuLBDtEuWbatLhnefGRzoGx+HGB7qYrVT3UPS7qJnFQXEw1ryjmojqOv43Bqtt2ABgBGdGsGXx95lWOKPYc7yrClCGiKLp1A1pZ3YCzIgcxHBc0DkxHUPk6CI7tG5aXDmprdAVUTxTPzDHht22m757mYWVglMJWiP4wjyfA1Ve+xTw3ZwqCG6q26mmhY3S9ye6R+vfoix8qBnTmHJwkmI3QHNkC3fwMAAcqIZogYPhe+Ec08PbQaOZNDUjHwMZoEfLIvtcYKJHWAEkt2nHVJfxhH/9gQs/UCkRlzaqjeqsuJhgPbRWHfnL5YP7Ennr3vVlHPiQhUiXqc2Nf7ffI1p5NFvfE9ragsPxsZ/3kJuv3rAQgIbN8f2jHveX1AI0UOiXk2EKiaa1NxdlDs7u6Ocib4rph83kvk1hrUMDGooXqrricamn+R976tkbgniPw93z1GA02gr93HZReUOp0sau+996Si1CSkff48DJd/g0zph/AHZyFi8DTIlZ4JxPyV4n8ND7szSpILenUVSFq1H2bEtapxw8zaJhLX5T82yPsxqKF6y95fpIB1KXJlRpOAxAtZ2JR8DYkXslxW4mpPZr79/BdHHqeQy/BQx2hRj3V2Gamm995TBJMROXvW4sY3L8NUqIOyUQtEjV2CoLb323+yC8j++nn3H3eKDgBX7UmVrGKv4mzg0hEdsX5iT+yb0xctIgJFPd/Z70Zd/2ODvBtzaqheczbR0Js20RNbfu3IX7b9Y7X4bP9FSY9ZuQqnf6zWqdwgVyjTZyJzy9swXP0dABDUcSDC+k6EXCluya62OjZV43J2kVV5esXvoFwuwzN/VfnYI+XWALaSjF09k+JIAz4iRzGooXrP0URDb9pEz2gSsP7wZbuPc/QvW3vt9it2Ca6uZLji7RczC7D+8GWrqipzELhvTl8cTs3G/vM3neouXFtFF44gc9v7MBXpIfP1R/jA5xHY5h63jiH5ig4fPdEZYYG+SNcXIzvfAE2gL9T+vjCaBPSP1SI0QFnjsg/gnoo9R7YycBarmshVGNRQg2Cv7NXM2zbRc6T82pHxVPxrGaj+r+Xtp9JtzlgNuzMKm0+k1TgDUzkI7B6jwf+SrjnUg6c2BGMZcveshf7wdwAA38iWiBg+B8owcUtvUpIBeG3bKcwf0gaLE/6o8n6O6NbUbkBTkSury9w1k8KqJnIF5tQQVeBIuak7iL14tYhwvIV/TcmiK0Z1BgCb3ZjTdMX4eE+q3SUl4a+ff2/+HUaT4NY8mzLdDaSvm2MJaIK7DIV21DseCWiAv783z647brO79fs7zjl0PFcn0dr7bkg1k8KqJpIaZ2qIKvC23jauzm+o7q9lAOizaJckMyrpegOW7zqPaXGtMLBdFCbdE4NVe1IlOLJthecOIuuHJTAV50OmCkTEoGkIuOMul52vthx9j+Wy8o0nXc3VMyl1ZZNZqlsY1BBV4G3lpo7kNzh6kaj8+Ac7RFseL+Uu3gDw/o6zuEMbBAD42EUBjWAsRc4vXyDv6CYAgG9UK0QMmwNlqNYl5/MUkwA8ty4JK+Suz+0Su2zrKG9KxKf6hUENUQXuSJJ0hNj8hupyX6q7SNi7qLhiJuqVLadQUOyaGYbS3HRkbl6EkrTyZZzgrsMRdt84yBS2d7KuD9yZ2yUlb0rEp/qHOTVEFdS2t40rOJv7Ut0O3mJ2LnfFTFSarhh6g0ny4xac2Y+0L6ahJO0c5H5BaPTIfGj6TazXAY2rcrvE9mZytoeTvUR8oHbdiok4U0NUiTeUm9rq+eJo7outai2x1V2/zr6/xhkrbyCUlSBn96fIS9oGAFBFt0bE8BfhE9JY8nP9a1Br/Hr2BvZfcDyICA1Q4s2H2uPVrb+LqmRzRLquCIkXsiTJSxG7JFSbpaO6uMks1S0Maohs8GS5qSMXDXu5L5UvEmIvKscu5Ti8W7g7leZcR+amRSjJKO97E9LjUYTePRoyhWt+pX30ywXkFokvuQaAUH8lxveOwZS+t0Ehl+HcjTyHq5zseW3baauGfs7mpYhdEqrt0pG3JeJT/cPlJ6JqeKLcVMzSUEVif/n/mJKGxAtZSNcViXr8jbziape9Qv09u6xTcOrX8uWmjAuQ+4eg8WMvI+y+8S4LaAA4HNAAgJ9Sjju0QZbvjdjtBxyRXakKqrrvSU3ELgmVlJlqvXTkbYn4VP9wpobISzjT+E/sL/+1iZewNvGSqI0sKx7X1oyVSRAQ/8khUceRkqnUgJydq5F/IgEAoLqlLSKGzoZPSITbxyJGut5gNXvh7IXakZkyZxpEip29+zLxYq2XjrwtEZ/qH87UEHkJZxr/Obobtr3+JjIA2hAVTIJgSQIFYDVj1fPWcLfvwF2adQXpX876K6CRQd3rn4gc+abXBjRmAoC53/2G/ecy0aV5mOj3zbyp40dPVJ0p0wTWPFPmaBKx2Nm+S9mFtT6eNybiU/3CmRoiL+FMvkHl7Q7sqekvfvOMQHGZyWompnKeRk1l5q6Qn7IL2T9/BKG0GPKAUEQ8OAv+MZ0kOXZN4xezF5MYuYWliP/0kGV7iVV7Ums8b8WL+8B2URjQznqmLF1fjBn/SbZ7XrHfJ7EzSM014rpW2zueNyTiU/3FoIbIS9Qm30DtxAU4LECJnArPMR+j8nFsJYGaL0z/3nwK6XrXJHWaSoqRvWMlCn7bAQBQNeuAiKEvwCdIuqUJAcD8IW0QEaxCRKAKkAGZ+YbyZTaTgPhPpVtmS9cVY9WeVEy6J6bGfbMqX9wrN8Azz57ZI/b7JHZJaHSvFvhkX6okS0fc94lchUENkZdwJt+gumoUMUyC9bP01STD1pyn4Zp5mpKbl5C5aRFKsy4DkEHdeyTUd/0TMrlC8nPlFJZgXO+YKhdUo0mQtKzd/D5uPpGGX2ffj2OXcnAjrxgRQSpAADILDKIu7lLnpYht8OjrI5d0o0tXdSumho05NURewtF8g5oSi8XQFZVZ/bumfmeV8zTMwZTUfVcEQUD+ye1IXzsTpVmXoQgMQ+SINxDa5wmXBDQAsHz3BfRZtKtKxZArNuCsWDJvzlPqfVsEereKEF1l54q8FLEbWDqz0aWzjfqInCETBKHOfcP0ej3UajV0Oh1CQkI8PRwiSYntU5N4IQsjVx9069im3H8bpvZrhXvf3i3p3lAAYCopQvbPH6Hg990AAL8WnRDx4CwoAkMlPU9NPnqiEwZ3sN7JOyElDf/eLG3jvKUjOmJ4xya1OoYr9k8Su3+Y2MdxjyeqzNXXbwY1RF5IzEVjU/I1TNuQ7PaxaQJ9q/RHqa2SG6m4uWkRyrKvAjI5Qu8ehZCej0Emc+9kslwGLB/ZGYM7WHfQlTp3aP3EnpIsvXjzTtfVLY2aR8c9nhomV1+/mVND5IXE5Bs40/dE7e9TZdnJUVIGNIIgIP/ET8je8TFgLIUiKBwRw2bDr2k7SY4f7OeDvGLxr9ckAM+uS8JKec0ddJ0ldR8Wb81LcabnEpEUGNQQ1VFiEkYjQ1R49/GOyMw34GJmIdYkXnTzKKtnMhQi66flKDy9BwDgd2sXRAyZCUWAWrJzvDGsLRqp/ZGuL8avZ25gY/J1Uc97Zcsp9G0d6VDOkvnSPOmeGKzakwqg9sm0dRX3eCJPYaIwUR0lJmH038PaovdtEVD5yLFkx1nJl42cZUg/j7Q108oDGpkcofeNR+PHXpY0oAGAad+cgK6oBA93aoJ/dmsm+nliOuhWZk6WnTc41uFk2vqGezyRp3CmhqgOE9PIrLZVUlISBAF5SVuRs/tTwFgGRUgjNBr2IlRN2rjsnOZlji7NwyCX1VzlVZHYDrpjejXHoHZRVvksDb0PC/d4Ik9hUENUx1W8gKbri5Gdb4Am0Bdqf19LIqnUlUrOMBXnI+vHZSg8ewAA4H9bD4QPng6Ff7DLzlm5FN2RamKxHXQHtYviEkol3OOJPIVBDVE9oJDLoCsqweKEP6qUzw5sq/XgyMoZ0s4ic9MilOkyALkPwu4bj+CuwyCTuWfmwtFljqhadtBt6KXMYhv6NZSZK3If5tQQ1SHVNTIzV+lUnpFJ1xXj8wMXPTDScoIgQH9kE9K/ehFlugz4qCOhHbUYId2Guy2gAcqXORxZ6hh2Z5Slgy7gWJO7mj6LyV8lVWnyV18506iPqLY4U0PkIY72GKnur//5Q2Lx2rbqy2c9xViUh6wflqDofPn+SQG334XwQVMh9wty6zi0ISrLbIrYbQ9W7UlFp2ZhDm++yFJmaw09t4jcj833iNzMaBKwfNd5fL4/FbkV9luqaXlC6n4prma4dho3Ny+GUX8TUPhA0/cpBHUa4tbZGbPQACXeeqS9Vd8ZwP6O5Vq1H/bN6QuFXCY6ABXb5Vmq5ntEdY2rr99cfiJyo4SUNHR5fTve33HWKqABql+e8KbqJXsEwQTdof8hfd1cGPU34RMaBe2odxDc+UHJAhoZygMVsXSFpZb31TzzEhboW+NzKicYm5vc2dufiaXMRJ7FoIbITRJS0vDMV0nILax+N2ygfHmi4qZ/3lK9ZI+xUIeb/30Vub98DpiMCGh9N6LGLYVKe5vk5xp/V4zox1Z+Xwe2i8L8IeJKyB0NPljKTORZDGqIbJB6Z2HzbIs95hmCL/anWs5ZF/6qL76SgrTPp6Loz6OAQgnNgCmIGPYi5CpxZdFihQf6YsWozpjS9zZEqf1E755deeZFq/YX9TxHgw9zKXN145KhfJmRpcxErsFEYaJKXFGO6+hsy2vbTuOTfal4eWisV/9VLwgm6A/+F7l7vwIEE3w0t6DR8DnwbSx+JkUsTaASifP6wden/G+x6kqGa2IOEF3VR0WqUmZv3qiSyJsxqKEGrfLFI6fAgOfWHa9yoTPnuzhbiurMbIv5nP3aNHL4ue5gLMhB5tb3UHzxOAAgsO390DzwLOS+4mZBHCED8ObD7S0BDVB9N+WamANEV/ZRcbRiqrKG3uOGqDZY/UQNlq2LR01t9CtXxDhCbFWMrXN643+gxZdOInPL2zAW5EDmo4Km/zMIbB/nkuombYgK/x7W1nJBrxyIdmkehiOp2XhuXVKV5Guz6j47VwYQzsy2VFflZn4W+7tQXefq6zeDGmqQalMi7Uw5rtEkoM+iXaJ6pHgzwWSE7sB/oDuwARBMUIY3Q8TwOfBt1Nwl5wtS+SBpfn/LDE1NQQgAm+Xa9gICb1nqMX9Hqpt1qk1QTeQtWNJN9ZrUCbliz1mbEmlnlpLMyx11OaApy89Gxn/mQ7d/HSCYENi+P7Rj33NZQAMA+YYyHLuUA6D6Tr1pumI881USTCY41cFWbLm2q9nLu6qc7ExEVTGnhjzGU7kDtS2RdjZxt3+sFmp/H+iKypw+t6cUpR5H5tZ3YSrMhUzpB80DzyKoXV+3nPtGXrGoQHTK+iQsH9kJ++b09YqZF0exxw1R7TGoIY+obvmntgm5Yjh7UajtzsKHU7MdDmg8nVMjmIzI3bcO+sRvAAhQNmqBRsPnQBne1G1jaBzsJyoQNQnAs+uOY+UoWZ3MO2GPG6La4/ITuZ29/XGAqg3opOTMRUGKnYUdDabMZ+kf29ip89VWmT4TGev/BX3ifwAICOo4ENrR77o1oAkP9EX3GI1D750rvzuuxB43RLXHoIbcztO5A/YuHkB5FVRFUuws7GgwFRqgxIpRnbF6TDc82MG9Mw9FF44i7YupMFz9HTJff0QMnY3wAVMgV6rcOo5hd0ZBIZc59N7V1bwTc94V4Niu4ET0Ny4/kdt5OndATI+S5SPL9weSMi+je4wG2hAV0vUGUY/PqbCdQv/YSGw9mVbDo6UhGMuQu/dL6A/9DwDgG9kSEcNehFLTxOXntuWWsPKOxOZAVGwuVF3NO6ltjxuiho5BDbmdN+QOeOLioZDL8O9hbfHMX2XHYsz97jf0j9W6JY+iTH8DmZsWw3D9DwBAcOchCLt/AmQ+NW/+6EqhAeXnNgeiYt+7iED3zihJaWC7KPSP1dbJZGciT2NQQ27nqhb1jqrtxcOZ/iYD20Vh5ajOmPvdb9VubFlRbmEplu86b9nryFUbWxaeO4SsH96HqTgfMlUgwgdNReAdvV1yLke88cNpBKoUGNguCgPbRWF6v1ZYsvOc/SdKdP33VA8bc5k5ETmGQQ25nStb1DszFmcuHjWVo9sLlAa2i4LJBLzw32QUlpjsnuvjPRfQuWko/tHlFizbdd7hsdZEMJYi55cvkHd0EwDAN6oVIobNgTJUK+l5nJVTUGJVDRfTKFDU8zLzxS3x1YTbFRDVPewoTB5TVy8aNbWyF1Ce4FtxFqbya6pNN2MpleamI3PzIpSklc98BHcdjrD7xkGmUHp4ZNYqdtI9nJotarsJZ7o+V8TtCohcw9XXb87UkMfUxdwBMeXolZeVKvbe6R+rrVU3Y6kUnjmAzB+XQjAUQO4XhPDBMxDQqoeHR2VbxWq47jGaKkFjRVIsXdr7jGUoLxvvH6v16u8qUUPEoIY8qq7lDjjTjdh8cZz17QlMu7/QZXkxosZSVoqc3Z8iL2krAMA3+g40GjYHPmrP9MJxxI28Ymw/lV5jLpKA2i9dOtJyoC59d4kaAgY1RA6oTalwgcGINxP+kHA0jinNuY7MTYtQknEBABDS41GE3j0aMkXd+DUQEaTCC9+eqPExYQFK9I+tXT6Qp1sOEJHz6sZvMyIvUVdb1Bec3oOshA8glBRB7h+CiCEz4N+ym8fGIwMwPe52tIgIQESgCrO+PYF0fc27U0OA3VmunMLSWs+geEPLASJyDoMaIjsqlvVGBKmgDVEhQ2/weF6MGKZSA3J2rUZ+cgIAQHVLLCKGvgifkAiPjclWMvjwjlH4eE9qtc95eWgsMgvEVTTVdgbFW1oOEJHjGNQQ1cBWhVZogNKSMOrNgU1p1lXc3PQWSm9eBCBDSK9/ILRPPGRyhVvOH+qvRFybxuh1azhyi0qhCVJBG1I1GTwhJQ2raghoJt0Tg4HtopB4IUvUeWs7g+JNLQeIyDGS7/1kNBoxf/58xMTEwN/fHy1btsRrr72GipXjgiBgwYIFiIqKgr+/P+Li4nDunIiGWkRuZC7rrbzkofsrUVUdYF36HBbgPaXQ+b/vRtqa6Si9eRHyADUaP/4Kwu4Z45aAZkLvFlg/sSeOze+Pdx7viEe7NsWEu2/Fw52aoFfLcKtgoKZKI6A8iNh8Ig1Gk+DWDR/NHae1ausASYo9wIjIdSSfqVm0aBFWrFiBNWvWoG3btjh69CjGjx8PtVqNqVOnAgAWL16MZcuWYc2aNYiJicH8+fMxYMAAnDp1Cn5+XKcmzxNT1uuvVODDCZ2RWWCwlKNvP5WOWd+cQEGJ0c0jLmcqLUb29o9R8Nt2AICqWQdEDH0BPkGuXyrRhqjw72FtHbrgO1pp5M4ZlLrYcoCooZM8qDlw4ACGDx+OIUOGAABatGiB9evX4/DhwwDKZ2mWLFmCl156CcOHDwcArF27FpGRkdi4cSNGjBhR5ZgGgwEGw9/r6Xq9XuphE1kRe7GVy2UY3rGJJe/GUGbCsI5RWH/4qvsG+5eSzMvI3PgWSrMuA5BB3XsE1HeNcMvszIy42zGl720OX/AdqTQymgSo/X0xvncLbEy+juyCEsv9rtqzq661HCBq6CQPau666y6sWrUKZ8+exe23344TJ05g3759eO+99wAAqampSE9PR1xcnOU5arUaPXr0QGJios2gZuHChXjllVekHirVUe7Yj8eRi+0PJ6/jpU0pyC6wv5eTKwiCgILfdiB7+0oIZQYoAsMQPvQF+De/0y3n79u6EabFtbK6TexnJDb/5WJmAfos2mUVaGoClXi4YxPExWo5g0JEAFwQ1MydOxd6vR6tW7eGQqGA0WjEG2+8gfj4eABAeno6ACAyMtLqeZGRkZb7Kps3bx5mzpxp+bder0fTpk2lHjrVAe7aWkHsxXbHqXRsOWn7e+sOppIiZP/8EQp+3w0A8GvRCREPzoQiMMxtYzhxRQejSbAEFY58RmIqjUIDlHh/R9Wcu5yCUny2/yK6MaAhor9Inij8zTff4Ouvv8a6deuQlJSENWvW4J133sGaNWucPqZKpUJISIjVDzU81SXumrchSEhJk+xc9pJSASDQV+HRgKbkRirS1swoD2hkcoTePRqNH3/FrQENAGQVlOBwajYAxz8jc6URUHVjbfO/q0siNt/+ypZTMJq8uQ6NiNxF8qBm9uzZmDt3LkaMGIH27dtj9OjRmDFjBhYuXAgA0GrLu31mZGRYPS8jI8NyH1FlYvZcMl/cjCYBiReysCn5GhIvZDl1wVPIZZg/JLbGkm1PJQMLgoC85ASkfzkLZdlXoQjSIHLkm1Df9U/IZJL/Jy2KOedF7GdUUU2VRtPjbre7LYI5kZiISPLlp8LCQsjl1r9YFQoFTCYTACAmJgZarRY7d+5Ex44dAZQvJx06dAiTJ0+WejhUT4hN3F2+6xw2HLlS6+WpH06m4f82/labIbuEyVCIrJ+Wo/D0HgCA361dEDFkJhQBao+Oq3GwX632TKqu0mjryeuizs8tC4gIcEFQM3ToULzxxhto1qwZ2rZti+PHj+O9997Dk08+CQCQyWSYPn06Xn/9dbRq1cpS0h0dHY2HHnpI6uFQPSH2omUr96LiLtkVA5vqklkX/nCqxu62nlKScQE3N72Fspy08uWme8cgpPsjHpudAay769Y2ALFVacQtC4jIEZIHNR988AHmz5+PZ599Fjdu3EB0dDSefvppLFiwwPKYF198EQUFBZg0aRJyc3PRp08fJCQksEcNVas2Fy1zX5lXtpxC/1gtFHJZtcmsD3aIwuq93hXQCIKA/OM/IHvXasBYBkVwI0QMexF+t7Tx9NAgABjRrTxp3xUBCLcsICJHyISKrX7rCL1eD7VaDZ1Ox6ThBsJoEtBn0a5qL25irZ/YE7qiEkz+KqnKcbxx2wNTcT6yEj5A4Zn9AAD/27ojfPAMKPyD3TYGcwWSykeOdL3t/Zei1H6YPyQWr207ZTcA2Tenr0PVSubkY8B2wz12+CWqO1x9/fbcvDWRA8RUyYiRriuym8zqLQxpZ5H2xbTygEbug7C+T6HRI/PdHtAAwMJH2mP/3H6YEXe7zcel64rx3LokDLszyup5lY/jTMdfbllARGJxQ0uqE+x1kx3RrRne33HW7nGyC0pqTGb1BoIgIO/oZuT88jlgKoNCHYlGw16EKvoOt4+lYqdeo0nAhiOXbT7OvMS3+UQaPnyiE17bdtrqfa5tx193bVngjsaOROQ6DGrI69nKfwn0VaBtdAi6tghD75aN0C1Ggw1HLttd+tAEqdw2bmcYi/KQ9eNSFJ07CAAIuP0uhA+aCrlfkOTneqJ7U3RroUFmvgG5RaWQQYYeMRrI5TJk5huqXNQP/pklqropLFCFfXP6Sh4cuHrLAnc1diQi12FQQ17NnE9ROVApKDHi8MUcHL6Yg49++RNRaj8MuzOq2qolAeVLH8F+3rOTdmWGa3/g5uZFMOpvAgofhN0/AcGdH4RM5pqZguyCEiz+6YzVRfx/SeUX8eEdm1g9NiElDXP/J67E/UZecZ3bM6m671l1lXNE5J2YU0Neq6ZmbpWl6YrtlmGfSc/H/vM3pRmchATBBN2h/yF93RwY9TfhExoF7ah3ENJlqMsCGgBI+D1DVOdf8wU/t0jc3lZ1rbza2aaBROR9OFNDXsteMzdHicm5MQvwVaDQDR2DjYU6ZP2wBEUXjpSft/XdCB/4POSqAJef25bK5e/46/+LuZxLWV7tztyW2jQNJCLvwqCGvJYnu8Q+1ScGy3add+k5iq/+jsxNi2HMzwIUSmjiJiHozoEunZ0Ro/LWA44Els5UN1Xm7twWR3ZkJyLvxuUn8lqeWMaQofwC+uXBSy47hyCYoEv8Bhnr5sGYnwUfTRNEjXkXwR0HeTygqWj7qXTRF/LQAKUkeSfu3LTUjF2LieoPBjXktczdZN3FHE6M6NYMOTVsolgbxoJc3PjmZeTuWQsIJgS2vR9RY5fAt/GtLjlfbXy2/yIuZhaIeuyHI2sf0Hgqt8XejuzmQJddi4m8H4Ma8hqVd9cGYGm45w7+SgUe7dwE2QWuWWYovnwSaV9MRfHF45D5qBA+aCrCh8yE3NffJeerLRmANQcuQu1ffcWY+YLfU4JcE0dyW6QkprGjFMtqROR6zKkhr1BTHsXKUZ0x97vfkOui2ROzwlIj/pt0TfLjCiYjdAf+A92BDYBggjK8GSKGz4Fvo+aSn0tKAoDsGt5zqS/4nsxtMXctrvwdrG3TQCJyLwY15HFieoQce6k/lu86j9V7LyDf4PqqJKkY83OQufVtFF86CQAIbB8HTdwzkPvW/fwMqS/4ns5tcVfXYiJyHQY15FH28igqlhdPi2uFyfe1RM+FO5BdYHsGQQagcbAvnrq7Ja7kFOJsug4HU3Nd9wJqUHQxGZlb3oGpMBcypR80DzyLoHZ9PTIWKYX6K/FhfGf0vDVc0gu+N+zIXdeaBhKRNebUkEc5mkfh6yPHmw+3hwy28x8EAAajgDd+OI21iZc8EtAIJiNy9nyJG/+ZD1NhLpSNWiBq7PteEdAEqWr/d0xuUSnkMpnkMxjMbSGi2mJQQx6141S6qMftP59pqXqpbtdmdUB5Qqurc29qUpaXiYwN/wd94n8ACAi6cyC0o9+FMrypW85f3eU+wFeBGXG3I2l+/xorfcRyVc8W7shNRLUhEwShzvX+1uv1UKvV0Ol0CAkJ8fRwyElGk4Bub2yvdimpssoN2Cp2nY0IVGHWtyeQrvdcg7SiP48hc+u7MBXpIfP1R/iAKQiMvddt53+0czR2nr5ptZ1BaIAS4++KwZS+t1lmOMw5TABEdQq2Zf3Eni5dpuFu2UT1k6uv3wxqyGMSL2Rh5OqDoh9vvqTZ+ovd0WNJSTCWIXfvV9Af+i8AQNn4VjQaPgdKTRM7z3SdUH8lxve2DmYqslVtFh7oi2Edo7Ep+VqNOUtatR/2zenLIIOIHObq6zcThcljHF3CqJw4rJDLLH/R/+iCTrNilOlvIHPz2zBcOw0ACO48BGH3T4DMx9cj4zHTFZViyY6zuEMbZHPJpqZKnx4xGjzz10xORcxrISJvx6CGPMaZ0tyKicO6opIqsw3uVHj+ELK2LYGpOA8y3wCED5qKwNZ9PDKWymwFgJXVVOkTGqCskpukDlDirUfaM6+FiLwWgxryGHslvDXZfiodn++/6HROSG0IxlLk/LoGeUc2AgB8ta0QMXwOlKFaD4ymes7sLl1dzyAA0HkwAZuISAxWP5HH1FTCa8/G5OseCWhKc9OR/vUcS0AT3HU4tPGLvS6gqUjsMl9NPYPMXLH3EhGRVBjUkEdVV8JbHRkATaAS2QUlrh2YDYVnDiDti2koSTsLuSoQjR55CZp+EyHzqX5vJFcK8RM30Sp2mc9Tey8REUmFy0/kcZWTVi9mFmLJjrMArEuOzbM5TUL9RZeBS0EoK0XO7k+Rl7QVAOAbfQcaDZsDH3Vjt43BFn1xGTSBSuQUlErSgdeTey8REUmBQQ15hcpJq3dog2xuLvhghyis3pvqtnGV5lxH5qZFKMm4AAAI6f4IQu8ZA5nCO/7TebhjE3y2/6Klm7KZM5VKnt57iYiotrzjNzNRJZVnbzQBvjiVpsd728+6bQwFp/ciK2EZhJIiyP1DED5kBgJadnPb+cWIi9WiW4xGkt2lvWHvJSKi2mBQQ17LPHuz8IdTWL03Fe7KTzWVGpCz6xPkJ/8IAFDdEouIoS/CJyTCPQMQoWKAoZDLJNld2py4PfmrJElmfoiI3I1BDXklc1O91XsvYNcfN9123tKsq7i56S2U3rwIAAjp+Q+E3j0KMrnCbWOoTEyAIdXu0ubEbSlmfoiI3I1BDXkdWy383SH/993I/ulDCKXFkAeoETFkJvxv7eLWMVQ2I+52bDhy2a0BRk3dhomIvBmDGvIqNTV/cxVTaTFydqxC/smfAQCqZu0R8eAL8Al23YaN9piXl6b0vQ1T+t7m9gBDqpkfIiJ3YlBDXkNM8zeplWReRuamt1CaeRmADOq7RkDde4THl5sA6+UlBhhERPYxqCGvYa/5m9Tyf9uB7O0rIJQaoAgMQ/jQF+Df/E63nd9MLoNVEjTzV4iInMOghmrNnNRb2+URdzV1M5UUIXv7ChSk7AIA+DXviIihs6AIDHPL+SuSAVg+shPCAlW4kVeMiCAVIACZBQYkXshiLgsRkQMY1FCt2ErqjXJypiEiUCX18KoouXkRNze+hbLsq4BMjtA+8Qjp+Zhblpv8lHIUl5os/678PiWkpOGFb09I8l4SETVEDGrIadUl9abrijH5qySsGNW5ysW4xlkdF05ICIKA/BM/IWfnKghlJVAEaRAx7EX4NW3nupP+JSxACQFAboVdrjWBSvzfoDZQ+/tiU/I1y9YQjryXRERkjUENOaWmpF4B5fHJK1tOoX+s1hK02JvVycw3uGSsJkMhsn76EIWnfwUA+MV0QcSDM6EIULvkfGah/kqMu6s5luw8X+W+7IJSTNlw3O4xqnsviYioKgY15BSxOzq/v/0serUMx5HUbCzZea7K4yrORLhiT6GSjAu4uektlOWklS833TMGIT0egUzmug3qzWHHmw+3x2vbTtX6eBV3x2YVFBFR9RjUkFPEJvUu330ey3dXnakwM8/0zP3uN4zq0UyCkf11XEFA/vEfkL3rE8BYCkVwo/LlplvaSHaO6pirl9T+vpJWc3F3bCKimjGoIYcZTQIy86RdKsotLMXy3RckOZbJUICsH5eh8Mx+AID/bd0RPng6FP4hkhy/OlPuvw29b4uw5AltSr4m6fG5OzYRUc0Y1JBDPLWFgViGtHPI3LwIZbnpgFyBsHvHIbjbQ5DJXJuLog1RYUb/261yXqQKQrg7NhGROAxqSDRPbGEgliAIyDu2GTm7PwdMZVCENEaj4XOgir7DLef/97C2VZJ4u8doEKX2Q7qu2On3jLtjExGJ57psSapXPLGFgVjG4nzc/P4N5OxcDZjK4H97L0SNX+a2gMZfafs/I4VchpeHxgJwvlpdq/ZjOTcRkUicqSFR3L2FgViGa3/g5ubFMOpvAAofhN0/AcGdH3T5clNFRaUmTP4qCR8+8XdnYHMPnoHtorBiVGdRS3YylCdOz4hrhRYRgdwdm4jIQQxqSBSxlTeBKgUKDEYXjwYQBBP0hzcid88awGSET2gUIobPgUp7m8vPbXM8AKasP261h1PFHjz9Y7VWTQdzCgx4bdtpq0CHez4REdUOgxoSRWzSqzsCGmORHlnb3kfRhSMAgIDWdyN84BTIVYEuP3dNTJXW5ip3A67cY2ZAuyhJ9swiIqJyDGpIlO4xGmhD/JCu9+wSVPHV35G5+W0Y8zIBhRKafhMR1HGQW5ebxLLXDVghl7GZHhGRhBjUkCjbT6WjuMz1szDVEQQT9Af/i9y9XwGCCT6aJmg0fA58G9/qsTGJwW7ARETuw6CG7PJ0KbexIBeZ295DcWoSACAw9j5oHngWclWAh0bkOHYDJiJyPQY1VCNPl3IXX/4NmVvehjE/GzIfFcLinkZQh/4eXW4yVyk5gt2AiYhcj31qqEaeKuUWTEbk7l+PjA3/B2N+NpThTaEd8y6C73zAYwGNJlCJ5SM6Qqt2LEAJ9VeyGzARkRtwpoZq5IllE2N+DjK3vo3iSycBAIHt4qDp/wzkvp6d7cguKMUbP/6B+UNiERboi/3nb4rar2p87xasaiIicgPO1FCN3L1sUnQxGde/eB7Fl05CplQhfMgMRAyZ7vGAxixdV4zn1iVBV1SCGf3vQJTar8ZuwWEBSkzp28pt4yMiasg4U0PVMpoElBlNUPnIYChzbVaNYDJCt28ddInfABCgjGiORg/NhTK8qUvPWx1/pQJFpVWrvSqWaQerlBjcTotP91+0eQwZgIWPtOcsDRGRmzCoIZsSUtIw97vfkFtY6vJzleVlInPLOzBcSQEABN05AGH9JkGuVLn83LaEBSiRU8PrNpdpx396yHKbXIZquwkTEZF7MKihKhJS0vDMV0luOVfRn8eQufVdmIr0kPn6I3zAFATG3uuSc/n7yDCj/x1IupyDhN8zqtxvnk95pFOTamdfqiP8FdA82bsF+sdq2R2YiMgDGNSQFaNJwL83n3L5eQRjGXL3fQX9wf8CAJSNb0Wj4XOg1DRx2TnfH9HJMnOSkJJWZZNJ895Lan9fx4MalAdFP6ak4/+GxDKgISLyAAY1ZOVwarbLt0Io099E5ubFMFw7DQAI6jQEmr4TIPPxddk5QwOUVv+2tcmkeXbFaBIQpfZDuq7YoX407B5MRORZrH4iK+m6Ipcev/D8YaR9PhWGa6ch8w1AxPC5CH9gsksDGgDQFZZi8ldJSEhJs9xm3ntpeMcm6NUy3DK7opDL8PLQWACosbKpOuweTETkGQxqyEp2QYlLjisYS5G96xPc/N+rMBXnwVd7G6LGLUVg6z4uOV+V8//1v69sOQVj5e20bRjYLgorRnV2uNEewO7BRESewuUnsnI1p1DyY5bpMnBz02KUpJ0BAAR3GYaw+8ZD5qO080xpmZeH3t9+Fr1vi7CbzFt5iSoiSIVZ3yQjQ2+wuSwlQ3leDrsHExF5hkwQBE9t6+M0vV4PtVoNnU6HkJAQTw+n3nBF1VPh2QPI+mEpTIYCyFWBCB88HQG395L0HM5ypuzavLknYL3/kzk0WjGqM8u4iYiq4errN5efCIC56ul3yY4nlJUie8fHuPn9mzAZCuAbdQeixi/zmoAGKO8OXDnPxp7qlqW0aj8GNEREHsblJwJgrnoySHKs0pw0ZG5ehJL08wCAkO6PIPSeMZApvOvrVrE7cP9Yregy7Joqp4iIyHO86ypDHiNVxU7BH/uQ9eMyCCWFkPsFI/zBmQho2U2SY9sSpFIAkCHfUObU850twzZXThERkfdwyfLTtWvXMGrUKISHh8Pf3x/t27fH0aNHLfcLgoAFCxYgKioK/v7+iIuLw7lz51wxFBLpYmbtEoSFshJk/fwRMje9BaGkEKomseXLTS4MaABgTK8WWPxoh1ofh2XYRER1n+RBTU5ODnr37g2lUokff/wRp06dwrvvvouwsDDLYxYvXoxly5Zh5cqVOHToEAIDAzFgwAAUF/PC4k5Gk4DEC1n4Pukq1iRedPo4pdnXkPblLOQf/wEAENLzH4h8YiF8QhpJNNLq9b4tAoM7ROHpe2JqdRyWYRMR1X2SVz/NnTsX+/fvx969e23eLwgCoqOjMWvWLLzwwgsAAJ1Oh8jISHzxxRcYMWJElecYDAYYDH/ne+j1ejRt2pTVT7WQkJKGf2/+vdZ5NAWnfkHWTx9CKCmCPECNiCEz4X9rF4lGWbOwACWOvtTfksvyw8nr+L+NvyGnUPxSlLkMe9+cvsyJISJysTpX/bR582Z07doV//jHP9C4cWN06tQJq1evttyfmpqK9PR0xMXFWW5Tq9Xo0aMHEhMTbR5z4cKFUKvVlp+mTZtKPewGxVy6XZuAxlRajKwflyFzyzsQSoqgatoOUeOWuS2gAYCFj7S3CkQGd4jG0ZcewIy420U93/zMl4dyryYiovpA8qDmzz//xIoVK9CqVSv89NNPmDx5MqZOnYo1a9YAANLT0wEAkZGRVs+LjIy03FfZvHnzoNPpLD9XrlyRetgNhtEkYO53v9XqGKWZV5C+dhbyT/4MQAb1XSMROeIN+AS7L3E2LECJ/rHaKrcr5DJMi2uFlaM6I6pS2XXluIVl2ERE9Yvk1U8mkwldu3bFm2++CQDo1KkTUlJSsHLlSowdO9apY6pUKqhUKimH2WAdvJCF3MJSp5+f/9tOZG//CEKpAfLAUEQ8+AL8W3SUboAi5RSW1lixZKvsukvzMBy7lMMybCKiekryoCYqKgqxsbFWt7Vp0wb/+9//AABabflf1xkZGYiK+vsv5IyMDHTs2FHq4VAliX9mOvU8U0kxsrd/hIKUXQAAv+Z3IuLBF6AICrPzTNexV7Fkq+yaZdhERPWX5MtPvXv3xpkzZ6xuO3v2LJo3bw4AiImJgVarxc6dOy336/V6HDp0CL16eU+32frGXOl0Jj3P4eeW3LyItDXTywMamRzqu0eh8eOvejSgAVixRERE1iSfqZkxYwbuuusuvPnmm3j88cdx+PBhrFq1CqtWrQIAyGQyTJ8+Ha+//jpatWqFmJgYzJ8/H9HR0XjooYekHg6hvCropU0pyC5wbNlJEATkn/wZOTs+hlBWAkWQBhFDZ8OvWXsXjVQcbhxJRES2SB7UdOvWDd9//z3mzZuHV199FTExMViyZAni4+Mtj3nxxRdRUFCASZMmITc3F3369EFCQgL8/PiXtzOMJqHalv0LfziFj/ekOnxMk6EQWT9/iMJTvwIA/GK6IOLBmVAEqB0+Vo8WYTh0Mcfh59nCiiUiIqoOd+mu4xJS0vDKllNI0/2dX2LefdpkAp5d5/iu2yUZf+LmprdQlnMdkMkRes8YhPR4BDKZ46uVHz3RCYM7RFc7zmF3RuHbY9eQXVAi6njO7KxNRETewdXXbwY1dVhCShomf5WEyh+gDOV7GgWpfBzaE0kQBOQn/4jsnasBYykUwRGIGPYi/G6Jtf/kSuQyYPnI8oDGrLoZpe+TrmLGNyfsHnPK/S0xo/8dnKEhIqqjXH395oaWdZS534ytiNR8myMBjclQgKwfP0DhmX0AAP+W3RA+ZAYU/s596ZaP7IzBHaxnU6rbBFKr9hd1zN63NWJAQ0RE1WJQU0ct33WuVv1mKjKknUPm5kUoy00H5AqE3TsOwd0egkzmeAARHuiLNx5u59DyUPcYDaLUfkjXFdsM0pgYTEREYjCoqYOMJgGf779Y6+MIgoC8Y1uQs/szwFQGRUhjNBo+B6roO5w6XliADxLn9YOvj2O5Nwq5DC8PjcXkr5IsS2dmTAwmIiKxJO9TQ653ODUbuUW1m6UxFufj5sY3kbNzFWAqg3+rnogav8zpgAYAFj7SweGAxmxguyisGNUZ2kpbG3ArAyIiEoszNXWQvU669hiun8HNTYtg1N8A5D4Iu/9JBHcZWuNyU5BKAZMAFJYYq9wXGqDEW4+0r3XgYWtrA25lQEREYjGoqYPEdtKtXP0kCALyjnyPnF/XACYjfEK1iBg2B6qoVtUeY0LvFoiL1VryWQ5eyMKBPzNxPacI0aH+uKtlBHq2DJcs8KgumZiIiMgeBjV1kL3EWgAIVCmw8JH2eH79cQCAsUiPrG3vo+jCEQBAwB19ED7oechVgTafLwPw4RNVK5h6t4pA71YRUr0UIiIiyTCnpg4yJ9YCfyfSVlZgMOLNH06jX+tGKL56CmmfTy0PaBRKaB54FhHD51Qb0ADAuLuaVwloiIiIvBlnauooc2Jt5S69FaXlFuKPn75E7p4vAcEEn7BoNBo+F76Rt9o9/gNtGdAQEVHdwqCmDhvYLgp9W0ei58KdVbYZMBbqkLn1PRSnHgMABMbeC80Dz0GuCqjxmOwJQ0REdRWDmjrMaBLwZeLFKgFN8eXfkLnlbRjzsyHz8UVY3DMI6tAfcpms2hwcgD1hiIiobmNQU0f9cPI6XtqUguyCv/vVCCYjdInfQLd/fflyk+YWNHpoLnwbtcCTvVvgx5R0q6UquQwwVYhytNwskoiI6jAGNXXQwh9O4eM9qVa3GfNzkLn1HRRfKt8YMrBdP2j6T4bct7z8u3+sFv83JNaqB0yX5mE4dimHPWGIiKheYFBTx/xwMq1KQFN0MRmZW9+BqSAXMqUKmv7PIqh9PwDWOTK2esCwJwwREdUXDGrqEKNJwEubUiz/FkxG6Pavh+7AfwAIUEY0R8TwOfCNaAaAOTJERNSwMKipQw6nZluSgsvyspC55W0YrpQHOUEdHkBY3CTIlX93G2aODBERNSQMauoQ855PRX8eQ+bWd2Eq0kPm64/wAc8hMPY+q8fOH9IG43rHcIaGiIgaDAY1dYjG3wc5v34B/cH/AgCUjWPQaPhcKDVNrB8XqGRAQ0REDQ6DmjriypUrmDthJPQH9wMAgjoNhqbvU5D5+FZ57OvD2zGgISKiBodBTR2wbds2jBkzBtnZ2QgICkZg32cR0OZum499+p4YDO4Q7eYREhEReR43tPRipaWleOGFF/Dggw8iOzsbXbp0wcnk41j7xjREqf2sHhse6IuPnuiMeYNjPTRaIiIiz+JMjZe6ePEiRowYgUOHDgEApk6disWLF0OlUqElypvpVWykx8Z5RETU0DGo8UIbN27E+PHjkZubi9DQUHz22Wd4+OGHrR5jq5EeERFRQ8blJy9iMBgwffp0PPzww8jNzUX37t1x/PjxKgENERERVcWgxktcuHABvXv3xtKlSwEAs2bNwt69e9GiRQvPDoyIiKiO4PKTF/j222/x1FNPQa/XQ6PR4IsvvsDQoUM9PSwiIqI6hTM1HlRcXIxnn30Wjz/+OPR6PXr37o3k5GQGNERERE5gUOMh586dQ69evbBixQoAwNy5c7F79240bdrUwyMjIiKqm7j85AHr16/HpEmTkJ+fj4iICHz55ZcYOHCgp4dFRERUp3Gmxo2KioowceJEPPHEE8jPz8c999yD5ORkBjREREQSYFDjJqdPn0b37t3xySefQCaTYf78+di5cyeaNGli/8lERERkF5ef3GDt2rWYPHkyCgsLERkZia+++gpxcXGeHhYREVG9wpkaFyooKMD48eMxduxYFBYWom/fvkhOTmZAQ0RE5AIMalwkJSUF3bp1wxdffAG5XI5XX30VP//8M7RaraeHRkREVC9x+UligiDgs88+w5QpU1BcXIyoqCisW7cO9913n6eHRkREVK8xqJFQXl4eJk+ejK+//hoAMGDAAKxduxaNGzf28MiIiIjqPy4/SeTEiRPo2rUrvv76aygUCixcuBA//PADAxoiIiI34UxNLQmCgI8//hjTp0+HwWDALbfcgvXr16NPnz6eHhoREVGDwqCmFnQ6HSZNmoRvvvkGADBkyBCsWbMG4eHhHh4ZERFRw8PlJycdO3YMXbp0wTfffAMfHx+888472Lx5MwMaIiIiD+FMjYMEQcDy5cvxwgsvoKSkBM2bN8eGDRvQs2dPTw+NiIioQWNQ44CcnBxMmDAB33//PQDgoYcewmeffYawsDAPj4yIiIi4/CTS4cOH0blzZ3z//fdQKpVYunQpvvvuOwY0REREXoJBjR2CIOC9995D7969cfHiRdx66604cOAApk6dCplM5unhERER0V+4/FSDrKwsjBs3Dlu3bgUAPPbYY/jkk0+gVqs9PDIiIiKqjDM11Thw4AA6deqErVu3QqVS4aOPPsI333zDgIaIiMhLMaipxGQyYdGiRbjnnntw5coVtGrVCgcPHsTkyZO53EREROTFuPxUwc2bNzFmzBgkJCQAAEaOHImPP/4YwcHBHh4ZERER2cOg5i979uzByJEjcf36dfj5+eGDDz7AhAkTODtDRERURzT45Sej0YjXX38d999/P65fv47WrVvj8OHDeOqppxjQEBER1SENeqYmIyMDo0aNwo4dOwAAY8aMwYcffoigoCAPj4yIiIgc1WCDml27duGJJ55ARkYGAgIC8OGHH2LcuHGeHhYRERE5qcEtPxmNRrz88suIi4tDRkYG2rZtiyNHjjCgISIiquMa1EzN9evXER8fj19++QUAMGHCBCxbtgwBAQGeHRgRERHVWoMJan766SeMHj0aN2/eRGBgID7++GPEx8d7elhEREQkkXq//FRWVoZ//etfGDhwIG7evIk777wTSUlJDGiIiIjqmXo9U3P16lWMHDkS+/btAwA888wzeP/99+Hn5+fhkREREZHU6m1Qs23bNowdOxZZWVkIDg7GJ598gscff9zTwyIiIiIXqXfLT6WlpZg9ezYefPBBZGVloXPnzjh+/DgDGiIionquXs3UXLp0CSNGjMDBgwcBAM8//zzefvttqFQqD4+MiIiIXK3eBDWbNm3CuHHjkJubC7Vajc8++wyPPPKIp4dFREREblLnl59KSkowffp0PPTQQ8jNzUX37t1x/PhxBjREREQNjMuDmrfeegsymQzTp0+33FZcXIznnnsO4eHhCAoKwqOPPoqMjAyHj52amorevXtj6dKlAICZM2di7969iImJkWr4REREVEe4NKg5cuQIPv74Y3To0MHq9hkzZmDLli349ttv8euvv+L69etOzazcfffdOHr0KMLCwrB582a8++678PX1lWr4REREVIe4LKcmPz8f8fHxWL16NV5//XXL7TqdDp9++inWrVuHvn37AgA+//xztGnTBgcPHkTPnj2rHMtgMMBgMFgdAwDy8vLQvXt3fPbZZ2jatCn0er2rXg4RERHVkvk6LQiCa04guMiYMWOE6dOnC4IgCPfee68wbdo0QRAEYefOnQIAIScnx+rxzZo1E9577z2bx3r55ZcFAPzhD3/4wx/+8Kce/Fy4cMElsYdLZmo2bNiApKQkHDlypMp96enp8PX1RWhoqNXtkZGRSE9Pt3m8efPmYebMmZZ/5+bmonnz5rh8+TLUarWkYyfH6PV6NG3aFFeuXEFISIinh9Og8bPwLvw8vAc/C++h0+nQrFkzaDQalxxf8qDmypUrmDZtGrZv3y7ZdgQqlcpmrxm1Ws0vqJcICQnhZ+El+Fl4F34e3oOfhfeQy12T0iv5UY8dO4YbN26gc+fO8PHxgY+PD3799VcsW7YMPj4+iIyMRElJCXJzc62el5GRAa1WK/VwiIiIqIGQfKamX79++O2336xuGz9+PFq3bo05c+agadOmUCqV2LlzJx599FEAwJkzZ3D58mX06tVL6uEQERFRAyF5UBMcHIx27dpZ3RYYGIjw8HDL7RMmTMDMmTOh0WgQEhKC559/Hr169bJZ+WSLSqXCyy+/zO0PvAA/C+/Bz8K78PPwHvwsvIerPwuZILiqrupv9913Hzp27IglS5YAKG++N2vWLKxfvx4GgwEDBgzARx99xOUnIiIicppbghoiIiIiV6vzez8RERERAQxqiIiIqJ5gUENERET1AoMaIiIiqhfqTFDz1ltvQSaTYfr06ZbbiouL8dxzzyE8PBxBQUF49NFHkZGR4blB1mPXrl3DqFGjEB4eDn9/f7Rv3x5Hjx613C8IAhYsWICoqCj4+/sjLi4O586d8+CI6y+j0Yj58+cjJiYG/v7+aNmyJV577TWrDeL4ebjGnj17MHToUERHR0Mmk2Hjxo1W94t537OzsxEfH4+QkBCEhoZiwoQJyM/Pd+OrqB9q+ixKS0sxZ84ctG/fHoGBgYiOjsaYMWNw/fp1q2Pws5CGvf8uKnrmmWcgk8ks1dBmUn0WdSKoOXLkCD7++GN06NDB6vYZM2Zgy5Yt+Pbbb/Hrr7/i+vXreOSRRzw0yvorJycHvXv3hlKpxI8//ohTp07h3XffRVhYmOUxixcvxrJly7By5UocOnQIgYGBGDBgAIqLiz048vpp0aJFWLFiBZYvX47Tp09j0aJFWLx4MT744APLY/h5uEZBQQHuvPNOfPjhhzbvF/O+x8fH4/fff8f27duxdetW7NmzB5MmTXLXS6g3avosCgsLkZSUhPnz5yMpKQnfffcdzpw5g2HDhlk9jp+FNOz9d2H2/fff4+DBg4iOjq5yn2SfhUu2yZRQXl6e0KpVK2H79u1Wu33n5uYKSqVS+Pbbby2PPX36tABASExM9NBo66c5c+YIffr0qfZ+k8kkaLVa4e2337bclpubK6hUKmH9+vXuGGKDMmTIEOHJJ5+0uu2RRx4R4uPjBUHg5+EuAITvv//e8m8x7/upU6cEAMKRI0csj/nxxx8FmUwmXLt2zW1jr28qfxa2HD58WAAgXLp0SRAEfhauUt1ncfXqVaFJkyZCSkqK0Lx5c+H999+33CflZ+H1MzXPPfcchgwZgri4OKvbjx07htLSUqvbW7dujWbNmiExMdHdw6zXNm/ejK5du+If//gHGjdujE6dOmH16tWW+1NTU5Genm71WajVavTo0YOfhQvcdddd2LlzJ86ePQsAOHHiBPbt24dBgwYB4OfhKWLe98TERISGhqJr166Wx8TFxUEul+PQoUNuH3NDotPpIJPJEBoaCoCfhTuZTCaMHj0as2fPRtu2bavcL+VnIfk2CVLasGEDkpKScOTIkSr3paenw9fX1/IFNYuMjER6erqbRtgw/Pnnn1ixYgVmzpyJf/3rXzhy5AimTp0KX19fjB071vJ+R0ZGWj2Pn4VrzJ07F3q9Hq1bt4ZCoYDRaMQbb7yB+Ph4AODn4SFi3vf09HQ0btzY6n4fHx9oNBp+Ni5UXFyMOXPmYOTIkZZduvlZuM+iRYvg4+ODqVOn2rxfys/Ca4OaK1euYNq0adi+fTv8/Pw8PZwGzWQyoWvXrnjzzTcBAJ06dUJKSgpWrlyJsWPHenh0Dc8333yDr7/+GuvWrUPbtm2RnJyM6dOnIzo6mp8HUSWlpaV4/PHHIQgCVqxY4enhNDjHjh3D0qVLkZSUBJlM5vLzee3y07Fjx3Djxg107twZPj4+8PHxwa+//oply5bBx8cHkZGRKCkpQW5urtXzMjIyuIeUxKKiohAbG2t1W5s2bXD58mUAsLzflSvP+Fm4xuzZszF37lyMGDEC7du3x+jRozFjxgwsXLgQAD8PTxHzvmu1Wty4ccPq/rKyMmRnZ/OzcQFzQHPp0iVs377dMksD8LNwl7179+LGjRto1qyZ5Vp+6dIlzJo1Cy1atAAg7WfhtUFNv3798NtvvyE5Odny07VrV8THx1v+v1KpxM6dOy3POXPmDC5fvoxevXp5cOT1T+/evXHmzBmr286ePYvmzZsDAGJiYqDVaq0+C71ej0OHDvGzcIHCwkLI5db/6SoUCphMJgD8PDxFzPveq1cv5Obm4tixY5bH7Nq1CyaTCT169HD7mOszc0Bz7tw57NixA+Hh4Vb387Nwj9GjR+PkyZNW1/Lo6GjMnj0bP/30EwCJPwsnE5w9omL1kyAIwjPPPCM0a9ZM2LVrl3D06FGhV69eQq9evTw3wHrq8OHDgo+Pj/DGG28I586dE77++mshICBA+OqrryyPeeutt4TQ0FBh06ZNwsmTJ4Xhw4cLMTExQlFRkQdHXj+NHTtWaNKkibB161YhNTVV+O6774SIiAjhxRdftDyGn4dr5OXlCcePHxeOHz8uABDee+894fjx45aKGjHv+8CBA4VOnToJhw4dEvbt2ye0atVKGDlypKdeUp1V02dRUlIiDBs2TLjllluE5ORkIS0tzfJjMBgsx+BnIQ17/11UVrn6SRCk+yzqdFBTVFQkPPvss0JYWJgQEBAgPPzww0JaWprnBliPbdmyRWjXrp2gUqmE1q1bC6tWrbK632QyCfPnzxciIyMFlUol9OvXTzhz5oyHRlu/6fV6Ydq0aUKzZs0EPz8/4dZbbxX+7//+z+qXNT8P19i9e7cAoMrP2LFjBUEQ975nZWUJI0eOFIKCgoSQkBBh/PjxQl5engdeTd1W02eRmppq8z4Awu7duy3H4GchDXv/XVRmK6iR6rOQCUKFNqREREREdZTX5tQQEREROYJBDREREdULDGqIiIioXmBQQ0RERPUCgxoiIiKqFxjUEBERUb3AoIaIiIjqBQY1REREVC8wqCEiIqJ6gUENERER1QsMaoiIiKhe+H9AMPVw2gWFDwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(y_val, y_pred)\n", "plt.xlim(40, 140)\n", "plt.ylim(40, 140)\n", "plt.plot([40, 140], [40, 140], 'black')"]}, {"cell_type": "code", "execution_count": 29, "id": "23cd8472", "metadata": {}, "outputs": [], "source": ["test_well_x = test_df[['RHOB', 'GR', 'NPHI', \"PEF\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "f6bd36cc", "metadata": {}, "outputs": [], "source": ["test_df['TEST_DT'] = regr.predict(test_well_x)"]}, {"cell_type": "code", "execution_count": 31, "id": "13ea69b8", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x1d692fff380>]"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjUAAAGiCAYAAAAfnjf+AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAABjv0lEQVR4nO3deZjNdf/H8eeZfWF2Zqkw3LoRZcsSbUwRScudMNZEqBAVum/crdJOC6UFhdJ9J1spUZbsxlTiV9KQZWaYfTPbOd/fH+6ZDGPmzMw5c2bOvB7X5bpyzvd8z3vmxLx8trfJMAwDERERkVrOxdEFiIiIiNiCQo2IiIg4BYUaERERcQoKNSIiIuIUFGpERETEKSjUiIiIiFNQqBERERGnoFAjIiIiTkGhRkRERJyCQo2IiIg4hQqHmi1bttCvXz8iIiIwmUx88cUXl7x27NixmEwmXn/99RKPp6SkEB0djZ+fHwEBAYwaNYqsrKyKliIiIiJSrMKhJjs7m2uuuYa33nqrzOtWrlzJzp07iYiIuOi56OhofvnlFzZs2MDatWvZsmULY8aMqWgpIiIiIsXcKvqC2267jdtuu63Ma06ePMkjjzzC119/Td++fUs8d+jQIdavX8+ePXvo2LEjAG+88QZ9+vTh5ZdfLjUEiYiIiJSnwqGmPBaLhaFDh/L4449z1VVXXfT8jh07CAgIKA40AFFRUbi4uLBr1y7uuuuui16Tl5dHXl5eifdISUkhODgYk8lk6y9BRERE7MAwDDIzM4mIiMDFxfbLem0eaubMmYObmxsTJkwo9fmEhAQaNmxYsgg3N4KCgkhISCj1NbNnz+app56ydakiIiLiAMePH+fyyy+3+X1tGmr27dvH3LlziYmJsekIyvTp05k8eXLx79PT02nUqBHHjx/Hz8/PZu8jIiIitrNnzx5GjBjBiRMncHd3Z8aMGcycOZP69evb5f1sGmq2bt3K6dOnadSoUfFjZrOZKVOm8Prrr3P06FHCwsI4ffp0idcVFhaSkpJCWFhYqff19PTE09Pzosf9/PwUakRERGoYi8XCK6+8wpNPPklhYSHNmjVjxYoV/O1vf2PmzJl2Wzpi01AzdOhQoqKiSjzWq1cvhg4dysiRIwHo2rUraWlp7Nu3jw4dOgCwadMmLBYLnTt3tmU5IiIiUs2SkpIYMWIE69atA+C+++7j3Xffxc/Pj4yMDLu+d4VDTVZWFr///nvx7+Pi4oiNjSUoKIhGjRoRHBxc4np3d3fCwsL4+9//DkDLli3p3bs3o0ePZsGCBRQUFPDwww8zcOBA7XwSERGpxbZt28bAgQM5efIknp6ezJ07lzFjxlTbpp4KLz3eu3cv7dq1o127dgBMnjyZdu3aMXPmTKvvsXTpUlq0aEHPnj3p06cP3bt35913361oKSIiIlIDWCwWZs+ezU033cTJkye58sor2bVrFw8++GC17lI2GYZhVNu72UhGRgb+/v6kp6drTY2IiIgDnT59mqFDh/LNN98AMGTIEObPn0+9evUuutbeP79tvqVbRERE6obvv/+ewYMHEx8fj7e3N2+++SYjR4502BlyamgpIiIiFWI2m3n66afp2bMn8fHxtGzZkj179nD//fc79FBcjdSIiIiI1RISEoiOjmbTpk0AjBw5kjfeeANfX18HV6ZQIyIiIlb69ttviY6O5vTp0/j6+jJ//nyGDh3q6LKKafpJREREylRYWMi//vUvbr31Vk6fPk2bNm3Yu3dvjQo0oJEaERERKcPJkycZPHgwW7ZsAWDMmDG8/vrreHt7O7iyiynUiIiISKnWr1/P0KFDSUpKol69eixcuJCBAwc6uqxL0vSTiIiIlFBQUMC0adO47bbbSEpKom3btsTExNToQAMaqREREZHz/PnnnwwaNIjt27cD8NBDD/Hyyy/j5eXl4MrKp1AjIiIiAKxZs4YRI0aQkpKCn58f77//Pv/4xz8cXZbVNP0kIiJSx+Xn5zNlyhTuuOMOUlJS6NixI/v3769VgQYUakREROq0uLg4rr/+el599VUAJk2axLZt22jatKmDK6s4TT+JiIjUUZ9//jn3338/6enpBAQEsGjRIvr37+/osipNIzUiIiJ1TF5eHo888gj33HMP6enpdOnShdjY2FodaEChRkREpE75/fffue6663jzzTcBePzxx9myZQuNGzd2cGVVp+knERGROmLFihU88MADZGZmEhwczOLFi+nbt6+jy7IZjdSIiIg4ubNnzzJu3Djuu+8+MjMz6d69O7GxsU4VaEChRkRExKn9+uuvdOnShQULFgAwffp0vvvuOy6//HIHV2Z7mn4SERFxUkuXLuXBBx8kOzubBg0a8NFHH9GrVy9Hl2U3GqkRERFxMjk5OTzwwAMMGTKE7OxsbrrpJmJjY5060IBCjYiIiFM5ePAgnTp14v3338dkMjFz5ky+/fZbIiIiHF2a3Wn6SURExEksWrSIhx56iJycHEJDQ1m2bBk9evRwdFnVRiM1IiIitVxWVhbDhw9n5MiR5OTkEBUVxY8//linAg0o1IiIiNRqP//8M9deey1LlizBxcWFZ599lvXr1xMaGuro0qqdpp9ERERqIcMweO+995gwYQK5ublERESwfPlybrjhBkeX5jAKNSIiIrVMZmYmDz74IMuXLwegd+/eLFmyhAYNGji4MsfS9JOIiEgtsn//ftq3b8/y5ctxdXXlhRdeYN26dXU+0IBGakRERGoFwzCYP38+kydPJi8vjyuuuIJPPvmE6667ztGl1RgKNSIiIjVceno6DzzwAP/5z38A6NevHx9++CHBwcEOrqxm0fSTiIhIDbZ3717at2/Pf/7zH9zc3HjllVdYtWqVAk0pNFIjIiJSAxmGwbx583j88ccpKCigcePGfPrpp3Tu3NnRpdVYCjUiIiI1TGpqKvfffz9ffPEFAHfddRfvv/8+gYGBji2shtP0k4iISA2yc+dO2rVrxxdffIGHhwdvvPEG//3vfxVorKBQIyIiUgNYLBZefvllrr/+eo4dO0azZs3Yvn07Dz/8MCaTydHl1QqafhIREXGw5ORkhg8fzrp16wAYMGAA7777Lv7+/g6urHbRSI2IiIgDbdu2jbZt27Ju3To8PT2ZP38+n3zyiQJNJSjUiIiIOIDFYmH27NncdNNNnDhxgiuvvJJdu3YxduxYTTdVkqafREREqtnp06cZNmwYX3/9NQDR0dHMnz+f+vXrO7iy2k2hRkREpBpt3ryZQYMGER8fj7e3N2+88Qb333+/RmdsQNNPIiIi1cBsNvP000/To0cP4uPjadmyJbt372bUqFEKNDaikRoRERE7S0hIYMiQIWzcuBGAESNG8Oabb+Lr6+vgypyLQo2IiIgdbdy4kejoaBITE/Hx8WH+/PkMGzbM0WU5JU0/iYiI2EFhYSEzZ87klltuITExkdatW7N3714FGjvSSI2IiIiNnTx5ksGDB7NlyxYARo8ezdy5c/H29nZwZc5NoUZERMSG1q9fz9ChQ0lKSqJevXq8++67DBo0yNFl1QmafhIREbGBgoICpk+fzm233UZSUhJt27Zl3759CjTVSCM1IiIiVXT8+HEGDhzI9u3bARg/fjyvvPIKXl5eDq6sblGoERERqYK1a9cyfPhwUlJS8PPz47333uPee+91dFl1kqafREREKiE/P58pU6bQr18/UlJS6NChAzExMQo0DqSRGhERkQo6evQoAwcOZNeuXQBMnDiROXPm4Onp6eDK6jaFGhERkQr44osvGDlyJGlpaQQEBPDhhx9y5513OrosQdNPIiIiVsnLy2PixIncddddpKWl0blzZ/bv369AU4Mo1IiIiJTjyJEjdOvWjXnz5gHw2GOPsXXrVpo0aeLYwqQETT+JiIiU4bPPPuOBBx4gIyODoKAglixZQt++fR1dlpRCIzUiIiKlyM3NZfz48QwYMICMjAy6detGbGysAk0NplAjIiJygd9++40uXbowf/58AKZPn87333/PFVdc4eDKpCyafhIRETnPsmXLePDBB8nKyqJBgwZ89NFH9OrVy9FliRUUakRExGmZLQa741I4nZlLw/pedIoMwtXFVOq1OTk5TJw4kffeew+AG2+8kWXLlhEREVGdJUsVKNSIiIhTWn8gnqfWHCQ+Pbf4sXB/L2b1a0Xv1uElrj106BADBgzgwIEDmEwmZsyYwYwZM3Bz04/J2kRrakRExOmsPxDPuI9jSgQagPj0XMZ+HMP6A/HFjy1evJiOHTty4MABQkND2bBhA0899ZQCTS2kUCMiIk7FbDF4as1BjDKumfb5z2RkZjFixAhGjBhBTk4OPXv2JDY2lp49e1ZbrWJbiqEiIuJUdselXDRCc6HTxw7T/KpxnD7+By4uLvz73//mySefxNXVtZqqFHtQqBEREadyOvPSgcYwDLJ++obUb9/BKMwnIiKCZcuWceONN1ZjhWIvFZ5+2rJlC/369SMiIgKTycQXX3xR/FxBQQFTp06lTZs2+Pr6EhERwbBhwzh16lSJe6SkpBAdHY2fnx8BAQGMGjWKrKysKn8xIiIiDet7lfq4JS+HpLUvk7L+DYzCfLwiO7Bw5SYFGidS4VCTnZ3NNddcw1tvvXXRczk5OcTExDBjxgxiYmL4/PPP+fXXX7njjjtKXBcdHc0vv/zChg0bWLt2LVu2bGHMmDGV/ypERET+p1NkEAHe7iUey0/8g/jFk8g5uBlMLgTcOIKG986iwKOeg6oUezAZhlHWWqqyX2wysXLlyjI7lO7Zs4dOnTpx7NgxGjVqxKFDh2jVqhV79uyhY8eOAKxfv54+ffpw4sQJq84DyMjIwN/fn/T0dPz8/CpbvoiIOKm53/7Ga98ePjfdFPsVKRsXgrkA1/ohhNzxBF6XtwJg+egudG0W7OBq6w57//y2++6n9PR0TCYTAQEBAOzYsYOAgIDiQAMQFRWFi4sLu3btKvUeeXl5ZGRklPglIiJyKQ/3aI6nJZekVXNI+eZtMBfg3exawkfOKw40AT7udIoMcnClYkt2XSicm5vL1KlTGTRoUHEiS0hIoGHDhiWLcHMjKCiIhISEUu8ze/ZsnnrqKXuWKiIiTmTv3r0cfe8R8lPjwcWVwBtHUP/aOzGZ/jpNuPRzhaU2s9tITUFBAQMGDMAwjOKGYJU1ffp00tPTi38dP37cRlWKiIgzMQyDefPm0b17N/JT43H1a0hY9Iv4dbqrRKABSM0pYHdcSqn3MVsMdhxJZlXsSXYcScZsqfRKDalGdhmpKQo0x44dY9OmTSXmzcLCwjh9+nSJ6wsLC0lJSSEsLKzU+3l6euLp6WmPUkVExEmkpqYyatQoVq5cCYB38y4E95mEq9elFwMXbf8+v0dU3JlsFu84SmpOQfF1gT7uPHdna/pcrT5QNZnNQ01RoDl8+DDfffcdwcElF2B17dqVtLQ09u3bR4cOHQDYtGkTFouFzp0727ocERFxQueHkJB6nvwSu49/TXiAM/EncHNzp/6NI6nfod9FozMXaljfi/UH4vn36l9IyMi75HWpOQWMX7afB0+kMb1PK1t/OWIjFQ41WVlZ/P7778W/j4uLIzY2lqCgIMLDw/nHP/5BTEwMa9euxWw2F6+TCQoKwsPDg5YtW9K7d29Gjx7NggULKCgo4OGHH2bgwIHqhCoiIuU6v1GlYRhk7llJ6ubFYDHjFhBGg/5T8QhrXuY9TECYvxep2fmMXxZj9Xu/syWOay4PpM/V4eVfLNWuwlu6v//+e26++eaLHh8+fDj//ve/iYyMLPV13333HTfddBNw7vC9hx9+mDVr1uDi4sI999zDvHnzqFfPuvMCtKVbRKRuKmpUaQDmsxkkr3uNs0f2AODz9+4E3/YILp6+Vt3r7cHtmL7yZ9LPFlaohiBfD/b8MwpXFy01rih7//yu8EjNTTfdRFk5yJqMFBQUxLJlyyr61iIiUoed36gy98RBkla/iDkzCVzdCeo5mnptbyt3uqnI/d2a8FtiVoUDDUBKdj6741J0vk0NpN5PIiJSK+yOS+FUWg4Zu/5L2paPwLDgFhhBg/7T8AhtWqF7+Xm58/rGw5Wupaz+UuI4CjUiIlIrHD52ktOfPUVu3D4AfFrdSPCtD+Hi6VPhey3ecbRKtYT4akduTaRQIyIiNd7mzZuZMngguYkJmNw8CIwaS72rb7F6uulC52/XroxdcUl0ax5S/Pvzd2M1rO9Fp8ggrblxAIUaERGpscxmM88//zz//ve/sVgseDVoRFC/J3Bv0MShdb2/7SgTo/6Oq4upxG6sIuH+Xszq14rerbVLqjrZvfeTiIhIZSQkJNCrVy9mzpyJxWJh+PDhLF+7CY8GTRze4iA738zuuJTi3VjnBxqAhPRcxn0cw/oD8Q6qsG7SSI2IiJNxhqmQjRs3Eh0dTWJiIj4+Prz99tsMHz4cAC8fn4tGRhwhIf0sL379K6Xt+TU4dxbOU2sOckursFr3/a+tFGpERJxIbZ8KMZvNPP300zzzzDMYhkHr1q359NNPadXqr1N8e7cOp0eLUBZvP8qc9f9HoYP6MiVl5ZcZrAwgPj1X27+rkUKNiIiTOP9guvMVTYXMH9K+RgebU6dOMXjwYDZv3gzAAw88wNy5c/HxObe7qWgE6tuDCayMPUlKdtUW+1ZVWk6+Vddp+3f1UagREXEC5x9Md6HaMBXy9ddfM3ToUM6cOUO9evV45513GDx4cPHzpY1AOZq1G68a1veybyFSTAuFRUScwO64FKunQmqSwsJCpk+fTu/evTlz5gzXXHMN+/btuyjQlLYY19G6Ng0h3N/rkouWTZyb+usUGVSdZdVpCjUiIk7A2imOmjQVcvz4cW666SZeeOEFAMaNG8fOnTu58sori68pawTKkcL9vejSLJhZ/c6t9Skt2BjAjL6tauTImLNSqBERcQLWTnHUlKmQdevW0bZtW3744Qf8/Pz49NNPefvtt/HyKllfeSNQjvJkn5a4upjo3Tqc+UPaE+Zf+vf1X6sO8OVP2tZdXRRqREScQKfIoFoxFVJQUMBjjz3G7bffTkpKCh06dCAmJoYBAwaUen1NGlk63+mMc3WZLQb+3h70viq01OtSsvMZvyyG59YdrM7y6iwtFBYRcQKuLiZm9WvFuI9jMEGJ6ZqioDOrn2OnQo4ePcrAgQPZtWsXABMmTODFF1/E0/PSfZRqysjShbYcTuKyQG+rFy8v3BoHGPyz71X2L64O00iNiIiTuNRUSJi/l8O3c3/xxRe0a9eOXbt2ERAQwOeff87cuXPLDDTw1whUTbM7LrnCi5cXbj3Klz+dsmNVopEaEREn0rt1OLe0CqsxJwrn5eUxdepU5s6dC0CnTp349NNPadKkiVWvd3Uxccc14byzJc6OVVbc2QJLpV73r1UH6NU6XIuH7UShRkTEybi6mGrECbZ//PEHAwYMYN++fQBMmTKF559/Hg8PD6vvsf5APO/WsEBTFSnZBTph2I4UakRExOb+85//MGrUKDIyMggKCmLRokX069evQveoqdu5q6qmLn52BlpTIyIiNpObm8v48eO59957ycjIoFu3bsTGxlY40EDN3c5dVTV18bMz0EiNiIjYxOHDhxkwYACxsbEATJs2jaeffhp3d/eLrrWmk7gzjmjUhG31zkyhRkREqmz58uWMGTOGrKwsQkJC+Oijj+jdu3ep11rbSdwZRzRm9G2pRcJ2pOknERGptLNnzzJ69GgGDx5MVlYWN9xwA7GxsWUGmtK2Qhd1El9/4K/TdztFBhHgc/EoT20W6Fv2FnapGoUaERGplEOHDtGpUyfee+89TCYTM2bMYOPGjVx22WWlXl9eJ3E410ncbHG2pcF/ccYptZpEoUZERCpsyZIldOzYkQMHDhAaGso333zD008/jZvbpVc1VLST+O64FNJyCmxdukM545RaTaJQIyIiVsvOzmbkyJEMHz6cnJwcevToQWxsLFFRUeW+tqKdxJ1tVEOLhO1PoUZERKxy4MABrr32WhYtWoSLiwtPP/0033zzDWFhYVa9vqKdxJ1tVEOLhO1PoUZERMpkGAbvv/8+nTp14tChQ4SHh7Nx40ZmzJiBq6ur1fepaCfxTpFBBPk6z0Lhr85bBC32oVAjIiKXlJmZydChQ3nggQc4e/Yst956K7Gxsdx0000VvldRJ3HgomBTWidxVxcTd7UtfdFxbbT2pwTyCyvXM0qso1AjIiKl+vHHH+nYsSNLly7F1dWV2bNn89VXX9GwYcNK37OincSjWlk3tVUbGMBHO446ugynpsP3RESkBMMweOedd5g0aRJ5eXlcfvnlLF++nO7du9vk/hXpJF40ZZWQnusUPaCOJuc4ugSnplAjIiLF0tPTGTNmDCtWrACgb9++LF68mOBg23aVtraTeNGU1biPY2z6/o7jDNGs5tL0k4iIALBv3z46dOjAihUrcHNz4+WXX2b16tU2DzSlMVsMdhxJZlXsSXYcSS5xAF/RlFWAd+3/d3jbywMcXYJTq/3/h4iISJUYhsGbb77JY489Rn5+Po0bN+aTTz6hS5cu1fL+1vSC6t06nJ9PpvPWd0eqpSZ7iQj0cXQJTk0jNSIidVhqair33HMPEyZMID8/nzvvvJP9+/dXa6CxtheUYdTuqRsdvmd/CjUiInXU7t27ad++PStXrsTd3Z25c+fy+eefExgYWC3vX9FeUIE+tbsZ5Pnb1cU+FGpEROoYwzB49dVX6datG0ePHqVp06Zs376dCRMmYDJV3w/divaCCqylHbv9vVxZUMp2dbE9hRoRkTokJSWF/v37M2XKFAoLC/nHP/5BTEwMHTt2rPZaKtoL6scTaXasxn4y88zs/zPV0WXUCQo1IiJ1xPbt22nbti1r1qzB09OTt99+mxUrVuDv7++QeiraC6q2rqixGPDOljhmf3nQ0aU4PYUaEREnZ7FYmDNnDjfccAPHjx+nefPm7Ny5k3HjxlXrdNOFKtoLKjLYt9pqs4eFW+PUJsHOFGpERJzYmTNn6Nu3L9OmTcNsNjNo0CD27dtH27ZtHV1ahXtBBXt7VF9xdmAx1CbB3hRqRESc1JYtW2jbti3r16/Hy8uLhQsXsnTpUurXr+/o0opZ2wvKbDGYsfaAI0q0qWMpapNgTzp8T0TEyZjNZmbPns2sWbOwWCy0aNGCFStW0KZNG0eXViprekHtjkshM9fswCpto3GQDt+zJ4UaEREnkpiYyJAhQ/j2228BGDZsGG+99Rb16tVzcGVlK68XlLU7pWoykwmGdm3i6DKcmkKNiIiT2LRpE4MHDyYxMREfHx/eeustRowY4eiybMLanVI12W1XheHhplUf9qTvrohILWc2m5k1axZRUVEkJiZy1VVXsWfPHqcJNAAdGlfPKcf21LRBzR4tcwYaqRERcRCzxShzHYk1Tp06RXR0NN9//z0Ao0aNYt68efj4VM/aDVt8DdbYd8wZDq+rrSft1B4KNSIiDmBNZ+ryfPPNNwwZMoQzZ87g6+vLO++8Q3R0tL1KvogtvgZrOcOamq5NQxxdgtPT9JOISDWrSGfq0hQWFvLkk0/Sq1cvzpw5wzXXXENMTEy1B5qqfA0VVdvX1AT4uNOljIXQYhsKNSIi1aiinakvdOLECW6++WZmz54NwNixY9mxYwdXXnmlfQouRVW/hsooOn24JqjM5NoLd7dRh+5qoFAjIlKNKtqZ+nzr1q2jbdu2bNu2jfr16/Ppp58yf/58vL297VjxxaryNVRW0enDNSEW3NSigdXXhvl5qkN3NdKaGhGRalTRztQABQUFPPnkk7z88ssAtG/fnhUrVtCsWTO71FiR2mxxnbWKTh++cB3PhcL8PBl4bSOW7DhKSk6BTWsA+O7/zlzyudHXN6FHizC7L5yW0inUiIhUo4p2pj527BgDBw5k586dADzyyCO89NJLeHp62q1Ga2uz1XUVcf7pw6dSc1j14yn2HE3hbMH5jSJNNGtYj1Q7BBoX07keTpey9qcEpt3WSkHGQTT9JCJSjSrSmXrVqlW0bduWnTt34u/vz3//+1/mzZvn0EADFe+ubWtFpw/7ermx9XDSBYEGEjNyeWT5fptuoC76WstbJmTraTepGIUaEZFqZE1n6id7/40pkx/lzjvvJC0tjU6dOrF//***************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(test_df['DT'], test_df['TEST_DT'])\n", "plt.xlim(40,140)\n", "plt.ylim(40, 140)\n", "plt.plot([40, 140], [40, 140], 'black')"]}, {"cell_type": "code", "execution_count": 32, "id": "9c9716c1", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(15, 5))\n", "plt.plot(test_df['DEPTH'], test_df['DT'])\n", "plt.plot(test_df['DEPTH'], test_df['TEST_DT'])\n", "plt.ylim(40, 140)\n", "plt.grid()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}