<p class="item-header h4" title="{{ var_name }}">
    <a href="#pp_var_{{ anchor_id }}">{{ var_name }}</a>
    <br/>
    <span class="fs-6 text-body-secondary">{{ var_type }}</span>
</p>
{% if alerts | length > 0 %}
    {% if alerts[0].__class__.__name__ == 'list' %}
        {% set n_labels = style.labels | length %}
        <table width="100%">
            <thead>
                <tr>
                    {% for idx in range(n_labels) %}
                        <th width="{{ 100 / n_labels }}%" style="color: {{ style.primary_colors[idx] }}">{{ style.labels[idx] }}</th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody>
                <tr>
                {% for idx in range(n_labels) %}
                    <td valign="top" width="{{ 100 / n_labels }}%">
                        {% if alerts[idx] | length > 0 %}
                            <p>
                                {% for alert in alerts[idx] %}
                                    {{ alert }}&nbsp
                                {% endfor %}
                            </p>
                        {% else %}
                            <em>No alerts</em>
                        {% endif %}
                    </td>
                {% endfor %}
                </tr>
            </tbody>
        </table>
    {% else %}
        {% if alerts | length > 0 %}
            <p>
                {% for alert in alerts %}
                    {{ alert }}&nbsp
                {% endfor %}
            </p>
        {% else %}
            <em>No alerts</em>
        {% endif %}
    {% endif %}
{% endif %}
<p class="variable-description">
    {{ description }}
</p>
