Metadata-Version: 2.1
Name: pydeck
Version: 0.9.1
Summary: Widget for deck.gl maps
Home-page: https://github.com/visgl/deck.gl/tree/master/bindings/pydeck
Author: <PERSON>
Author-email: a<PERSON><PERSON><PERSON><PERSON>@gmail.com
License: Apache License 2.0
Keywords: data,visualization,graphics,GIS,maps
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Topic :: Multimedia :: Graphics
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Framework :: Jupyter
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE.txt
Requires-Dist: jinja2 >=2.10.1
Requires-Dist: numpy >=1.16.4
Provides-Extra: carto
Requires-Dist: pydeck-carto ; extra == 'carto'
Provides-Extra: jupyter
Requires-Dist: ipywidgets <8,>=7 ; extra == 'jupyter'
Requires-Dist: traitlets >=4.3.2 ; extra == 'jupyter'
Requires-Dist: ipython >=5.8.0 ; (python_version < "3.4") and extra == 'jupyter'
Requires-Dist: ipykernel >=5.1.2 ; (python_version >= "3.4") and extra == 'jupyter'

[![Binder](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/uber/deck.gl/binder)
[![Documentation Status](https://readthedocs.org/projects/deckgl/badge/?version=latest)](https://pydeck.gl)
[![Anaconda-Server Badge](https://anaconda.org/conda-forge/pydeck/badges/version.svg)](https://anaconda.org/conda-forge/pydeck)
[![Downloads](https://pepy.tech/badge/pydeck/week)](https://pepy.tech/project/pydeck/week)

# pydeck: Large-scale interactive data visualization in Python

[![demo](https://user-images.githubusercontent.com/2204757/58838976-1538f400-8615-11e9-84f6-a2fe42bb300b.gif)](https://pydeck.gl/)

The pydeck library is a set of Python bindings for making spatial visualizations with [deck.gl](https://deck.gl),
optimized for a Jupyter environment. To get started, __[see the documentation](https://pydeck.gl/)__.

__[To install pydeck, see the instructions here](https://pydeck.gl/installation.html)__.

For __interactive demos__, click the binder logo below:

[![Binder](https://mybinder.org/static/logo.svg?v=f9f0d927b67cc9dc99d788c822ca21c0)](https://mybinder.org/v2/gh/uber/deck.gl/binder)

## Sample code

The following code renders a visualization similar to the one above in a Jupyter notebook:

```python
import pydeck as pdk

# 2014 locations of car accidents in the UK
UK_ACCIDENTS_DATA = ('https://raw.githubusercontent.com/uber-common/'
                     'deck.gl-data/master/examples/3d-heatmap/heatmap-data.csv')

# Define a layer to display on a map
layer = pdk.Layer(
    'HexagonLayer',
    UK_ACCIDENTS_DATA,
    get_position=['lng', 'lat'],
    auto_highlight=True,
    elevation_scale=50,
    pickable=True,
    elevation_range=[0, 3000],
    extruded=True,                 
    coverage=1)

# Set the viewport location
view_state = pdk.ViewState(
    longitude=-1.415,
    latitude=52.2323,
    zoom=6,
    min_zoom=5,
    max_zoom=15,
    pitch=40.5,
    bearing=-27.36)

# Render
r = pdk.Deck(layers=[layer], initial_view_state=view_state)
r.to_html('demo.html')
```

If you're developing outside a Jupyter environment, you can run:

```python
r.to_html('demo.html', notebook_display=False)
```

__[See the gallery for more examples.](https://pydeck.gl/#gallery)__

### Issues and contributing

If you encounter an issue, file it in the [deck.gl issues page](https://github.com/visgl/deck.gl/issues/new?assignees=&labels=question&template=question.md&title=)
and include your browser's console output, if any.

If you'd like to contribute to pydeck, please follow the [deck.gl contribution guidelines](https://github.com/visgl/deck.gl/blob/master/CONTRIBUTING.md)
and the [pydeck development installation instructions](https://pydeck.gl/installation.html#development-notes).
