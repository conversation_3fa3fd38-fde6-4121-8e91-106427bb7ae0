@echo off
echo.
echo ========================================
echo   Enhanced Well Log Analyzer Launcher
echo ========================================
echo.

REM Check if we're in the right directory
if not exist "Core Application\well_log_app.py" (
    echo ERROR: well_log_app.py not found in Core Application folder
    echo Please run this script from the "Web App Log visuals" directory
    pause
    exit /b 1
)

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if Streamlit is installed
python -c "import streamlit" >nul 2>&1
if errorlevel 1 (
    echo Installing required dependencies...
    pip install -r "Configuration\requirements.txt"
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo Starting Enhanced Well Log Analyzer...
echo.
echo The application will open in your default web browser at:
echo http://localhost:8501
echo.
echo Press Ctrl+C to stop the application
echo.

REM Change to Core Application directory and launch
cd "Core Application"
streamlit run well_log_app.py

pause
