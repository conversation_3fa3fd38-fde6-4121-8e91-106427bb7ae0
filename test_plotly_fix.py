"""
Quick test to verify Plotly compatibility fixes
"""

import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import sys

def test_plotly_functions():
    """Test the plotting functions with sample data"""

    print("🧪 Testing Plotly Compatibility Fixes")
    print("=" * 50)

    # Create sample data
    sample_data = {
        'DEPTH_MD': [1500, 1501, 1502, 1503, 1504],
        'GR': [45, 50, 55, 48, 52],
        'RDEP': [10, 15, 8, 12, 18],
        'RHOB': [2.3, 2.4, 2.2, 2.35, 2.45],
        'NPHI': [0.15, 0.18, 0.12, 0.16, 0.20],
        'LITH': ['Shale', 'Sandstone', 'Shale', 'Limestone', 'Shale']
    }

    df = pd.DataFrame(sample_data)
    print(f"✅ Sample data created: {df.shape}")

    # Test 1: Basic Figure with update_layout
    print("\n🧪 Test 1: Basic Figure Layout")
    try:
        fig1 = go.Figure()
        fig1.add_trace(go.Scatter(x=df['GR'], y=df['DEPTH_MD'], mode='lines'))
        fig1.update_layout(
            title='Test Plot',
            xaxis=dict(showgrid=True, gridwidth=1, gridcolor='lightgray'),
            yaxis=dict(showgrid=True, gridwidth=1, gridcolor='lightgray', autorange='reversed')
        )
        print("✅ Basic figure layout - SUCCESS")
    except Exception as e:
        print(f"❌ Basic figure layout - FAILED: {e}")
        return False

    # Test 2: Subplots with side-by-side layout
    print("\n🧪 Test 2: Subplots Layout")
    try:
        fig2 = make_subplots(rows=1, cols=2, shared_yaxes=True)
        fig2.add_trace(go.Scatter(x=df['RHOB'], y=df['DEPTH_MD'], mode='lines'), row=1, col=1)
        fig2.add_trace(go.Scatter(x=df['NPHI'], y=df['DEPTH_MD'], mode='lines'), row=1, col=2)
        fig2.update_xaxes(title_text="Density", row=1, col=1)
        fig2.update_xaxes(title_text="Neutron", row=1, col=2)
        fig2.update_xaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')
        fig2.update_yaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')
        print("✅ Subplots layout - SUCCESS")
    except Exception as e:
        print(f"❌ Subplots layout - FAILED: {e}")
        return False

    # Test 3: Log scale
    print("\n🧪 Test 3: Log Scale")
    try:
        fig3 = go.Figure()
        fig3.add_trace(go.Scatter(x=df['RDEP'], y=df['DEPTH_MD'], mode='lines'))
        fig3.update_layout(
            xaxis=dict(type='log', showgrid=True, gridwidth=1, gridcolor='lightgray'),
            yaxis=dict(autorange='reversed', showgrid=True, gridwidth=1, gridcolor='lightgray')
        )
        print("✅ Log scale - SUCCESS")
    except Exception as e:
        print(f"❌ Log scale - FAILED: {e}")
        return False

    print(f"\n🎉 All Plotly compatibility tests PASSED!")
    return True

def test_with_xeek_data():
    """Test with actual Xeek data if available"""

    print(f"\n🧪 Testing with Xeek Data")
    print("=" * 30)

    # Try to load Xeek data
    xeek_files = [
        'xeek_single_well_15_9-13.csv',
        'xeek_lithology_balanced.csv'
    ]

    for filename in xeek_files:
        try:
            df = pd.read_csv(filename)
            print(f"✅ Loaded {filename}: {df.shape}")

            # Test basic plotting
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=df['GR'].head(100),
                y=df['DEPTH_MD'].head(100),
                mode='lines'
            ))
            fig.update_layout(
                title=f'Test Plot - {filename}',
                xaxis=dict(showgrid=True, gridwidth=1, gridcolor='lightgray'),
                yaxis=dict(showgrid=True, gridwidth=1, gridcolor='lightgray', autorange='reversed')
            )
            print(f"✅ Plot creation successful for {filename}")
            break

        except FileNotFoundError:
            print(f"⚠️ {filename} not found")
            continue
        except Exception as e:
            print(f"❌ Error with {filename}: {e}")
            return False

    return True

def main():
    """Main test function"""

    print("🔧 PLOTLY COMPATIBILITY TEST")
    print("=" * 60)

    # Check Plotly version
    try:
        import plotly
        print(f"📦 Plotly version: {plotly.__version__}")
    except:
        print("❌ Could not determine Plotly version")

    # Run tests
    basic_test = test_plotly_functions()
    xeek_test = test_with_xeek_data()

    print(f"\n📊 TEST RESULTS:")
    print(f"✅ Basic Plotly functions: {'PASS' if basic_test else 'FAIL'}")
    print(f"✅ Xeek data compatibility: {'PASS' if xeek_test else 'FAIL'}")

    if basic_test and xeek_test:
        print(f"\n🎉 ALL TESTS PASSED - Well Log App should work correctly!")
        print(f"🚀 You can now upload Xeek datasets to the Streamlit app")
    else:
        print(f"\n❌ Some tests failed - Please check the errors above")

    return basic_test and xeek_test

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
