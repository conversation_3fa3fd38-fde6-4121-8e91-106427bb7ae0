async function fetchLogData() {
  const response = await fetch('/api/logdata');
  return await response.json();
}

function plotLogs(data) {
  const depth = data.DEPTH_MD;

  // GR Plot
  Plotly.newPlot('gr_plot', [{
    x: data.GR,
    y: depth,
    mode: 'lines',
    line: { color: 'green' },
    name: 'GR'
  }], {
    title: 'Gamma Ray vs Depth',
    yaxis: { autorange: 'reversed', title: 'Depth (m)' },
    xaxis: { title: 'GR (API)' }
  });

  // RDEP Plot
  Plotly.newPlot('rdep_plot', [{
    x: data.RDEP,
    y: depth,
    mode: 'lines',
    line: { color: 'red' },
    name: 'RDEP'
  }], {
    title: 'Resistivity (RDEP) vs Depth',
    yaxis: { autorange: 'reversed', title: 'Depth (m)' },
    xaxis: { title: 'RDEP (Ohm·m)', type: 'log' }
  });

  // RHOB and NPHI Plot
  Plotly.newPlot('rhob_nphi_plot', [
    {
      x: data.RHOB,
      y: depth,
      mode: 'lines',
      name: 'RHOB',
      line: { color: 'blue' }
    },
    {
      x: data.NPHI,
      y: depth,
      mode: 'lines',
      name: 'NPHI',
      line: { color: 'orange' }
    }
  ], {
    title: 'RHOB & NPHI vs Depth',
    yaxis: { autorange: 'reversed', title: 'Depth (m)' },
    xaxis: { title: 'RHOB / NPHI' }
  });

  // ✅ RHOB vs NPHI Crossplot (fixed placement!)
  Plotly.newPlot('rhob_nphi_crossplot', [{
    x: data.NPHI,
    y: data.RHOB,
    mode: 'markers',
    marker: {
      size: 6,
      color: data.GR,  // GR color scale
      colorscale: 'Viridis',
      colorbar: { title: 'GR (API)' }
    },
    text: data.DEPTH_MD.map(d => `Depth: ${d} m`),
    name: 'Crossplot'
  }], {
    title: 'Density-Neutron Crossplot (NPHI vs RHOB)',
    xaxis: { title: 'NPHI (v/v)' },
    yaxis: { title: 'RHOB (g/cc)', autorange: 'reversed' },
    height: 500
  });
}

// Call fetch → then plot
fetchLogData().then(plotLogs);
