{"cells": [{"cell_type": "code", "execution_count": 1, "id": "58688a36", "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import lasio as las\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "from sklearn.svm import SVC\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score"]}, {"cell_type": "code", "execution_count": 2, "id": "252dde84", "metadata": {}, "outputs": [], "source": ["def load_las_files(folder_path):\n", "    wells = []\n", "    for filename in os.listdir(folder_path):\n", "        if filename.endswith(\".las\"):\n", "            las_path = os.path.join(folder_path, filename)\n", "            well = las.read(las_path)\n", "            wells.append(well)\n", "    return wells\n", "folder_path = 'C:/Users/<USER>/OneDrive/Desktop/ONGC/Project1/LAS_Sample_Data/Data'"]}, {"cell_type": "code", "execution_count": 3, "id": "f6ed7881", "metadata": {}, "outputs": [], "source": ["wells = load_las_files(folder_path)"]}, {"cell_type": "code", "execution_count": 4, "id": "82c33ee7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 4 LAS files.\n"]}], "source": ["print(f\"Loaded {len(wells)} LAS files.\")"]}, {"cell_type": "code", "execution_count": 5, "id": "b19405b7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['DEPT', 'DT', 'RESD', 'SP', 'GR']\n", "['DEPT', 'DT', 'RESD', 'SP', 'GR']\n", "['DEPT', 'DT', 'RESD', 'SP', 'GR']\n", "['DEPT', 'DT', 'RESD', 'SP', 'GR']\n"]}], "source": ["for well in wells:\n", "    print(well.keys())"]}, {"cell_type": "code", "execution_count": 6, "id": "cb30fddd", "metadata": {}, "outputs": [], "source": ["data = []\n", "for well in wells:\n", "    df = well.df()  # Convert LAS data to a DataFrame\n", "\n", "    # Ensure DEPT is not used as the index\n", "    if df.index.name == 'DEPT':\n", "        df = df.reset_index()\n", "\n", "    # Ensure consistent data type for DEPT\n", "    df['DEPT'] = df['DEPT'].astype(float)\n", "\n", "    data.append(df)"]}, {"cell_type": "code", "execution_count": 7, "id": "19ef4f04", "metadata": {}, "outputs": [], "source": ["data = pd.concat(data, axis=0, ignore_index=True)"]}, {"cell_type": "code", "execution_count": 8, "id": "59e767d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1032 entries, 0 to 1031\n", "Data columns (total 5 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   DEPT    1032 non-null   float64\n", " 1   DT      1032 non-null   float64\n", " 2   RESD    1032 non-null   float64\n", " 3   SP      1032 non-null   float64\n", " 4   GR      1032 non-null   float64\n", "dtypes: float64(5)\n", "memory usage: 40.4 KB\n", "None\n", "      DEPT    DT  RESD    SP     GR\n", "0  10180.0  59.9  22.0  45.6  116.0\n", "1  10181.0  59.9  21.0  49.0  114.0\n", "2  10182.0  60.5  19.7  53.0  127.0\n", "3  10183.0  63.5  18.9  55.6  150.0\n", "4  10184.0  64.5  18.2  58.4  155.0\n"]}], "source": ["print(data.info())\n", "print(data.head())"]}, {"cell_type": "code", "execution_count": 9, "id": "f01e48ee", "metadata": {}, "outputs": [], "source": ["data.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": 10, "id": "4d82a182", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'LITH'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3805\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3804\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3805\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3806\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON>Erro<PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mindex.pyx:167\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mindex.pyx:196\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7081\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7089\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON>eyError\u001b[0m: 'LITH'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[10], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m data[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mLITH\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m=\u001b[39m\u001b[43mdata\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mLITH\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mastype(\u001b[38;5;28mint\u001b[39m)\n\u001b[0;32m      2\u001b[0m data\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\frame.py:4102\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   4100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   4101\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 4102\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   4103\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   4104\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3812\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3807\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   3808\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[0;32m   3809\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[0;32m   3810\u001b[0m     ):\n\u001b[0;32m   3811\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[1;32m-> 3812\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3813\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3814\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3815\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3816\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3817\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON>eyError\u001b[0m: 'LITH'"]}], "source": ["data['LITH']=data['LITH'].astype(int)\n", "data"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}