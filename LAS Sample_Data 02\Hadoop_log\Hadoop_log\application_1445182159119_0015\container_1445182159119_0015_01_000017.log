2015-10-19 15:58:06,404 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 15:58:06,513 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 15:58:06,638 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-19 15:58:06,669 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-19 15:58:06,669 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445182159119_0015, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@1623b78d)
2015-10-19 15:58:06,810 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-19 15:58:07,482 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445182159119_0015
2015-10-19 15:58:07,857 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-19 15:58:08,451 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-19 15:58:08,466 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@db57326
2015-10-19 15:58:08,732 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:1073741824+134217728
2015-10-19 15:58:08,857 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-19 15:58:08,857 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-19 15:58:08,857 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-19 15:58:08,857 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-19 15:58:08,857 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-19 15:58:08,873 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-19 15:58:12,142 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:58:12,142 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48246341; bufvoid = 104857600
2015-10-19 15:58:12,142 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17304468(69217872); length = 8909929/6553600
2015-10-19 15:58:12,142 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57315093 kvi 14328768(57315072)
2015-10-19 15:58:25,096 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-19 15:58:25,112 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57315093 kv 14328768(57315072) kvi 12122788(48491152)
2015-10-19 15:58:27,159 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:58:27,159 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57315093; bufend = 701411; bufvoid = 104857600
2015-10-19 15:58:27,159 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14328768(57315072); kvend = 5418228(21672912); length = 8910541/6553600
2015-10-19 15:58:27,159 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9770147 kvi 2442532(9770128)
2015-10-19 15:58:36,690 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-19 15:58:36,706 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9770147 kv 2442532(9770128) kvi 233240(932960)
2015-10-19 15:58:38,737 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:58:38,737 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9770147; bufend = 58023267; bufvoid = 104857600
2015-10-19 15:58:38,737 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2442532(9770128); kvend = 19748700(78994800); length = 8908233/6553600
2015-10-19 15:58:38,737 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67092019 kvi 16773000(67092000)
2015-10-19 15:58:47,191 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-19 15:58:47,206 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67092019 kv 16773000(67092000) kvi 14575980(58303920)
2015-10-19 15:58:49,019 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:58:49,019 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67092019; bufend = 10489855; bufvoid = 104857600
2015-10-19 15:58:49,019 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16773000(67092000); kvend = 7865344(31461376); length = 8907657/6553600
2015-10-19 15:58:49,019 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19558607 kvi 4889644(19558576)
2015-10-19 15:58:57,383 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-19 15:58:59,351 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 19558607 kv 4889644(19558576) kvi 2691852(10767408)
2015-10-19 15:59:00,914 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:59:00,914 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 19558607; bufend = 67814201; bufvoid = 104857600
2015-10-19 15:59:00,914 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 4889644(19558576); kvend = 22196432(88785728); length = 8907613/6553600
2015-10-19 15:59:00,914 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 76882953 kvi 19220732(76882928)
2015-10-19 15:59:09,727 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-19 15:59:09,727 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 76882953 kv 19220732(76882928) kvi 17013156(68052624)
2015-10-19 15:59:11,289 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:59:11,289 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 76882953; bufend = 20214328; bufvoid = 104857600
2015-10-19 15:59:11,289 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 19220732(76882928); kvend = 10296460(41185840); length = 8924273/6553600
2015-10-19 15:59:11,289 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29283080 kvi 7320764(29283056)
2015-10-19 15:59:19,821 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-19 15:59:19,821 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29283080 kv 7320764(29283056) kvi 5121912(20487648)
2015-10-19 15:59:21,805 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:59:21,805 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29283080; bufend = 77555951; bufvoid = 104857600
2015-10-19 15:59:21,805 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7320764(29283056); kvend = 24631868(98527472); length = 8903297/6553600
2015-10-19 15:59:21,805 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 86624703 kvi 21656168(86624672)
2015-10-19 15:59:31,665 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-19 15:59:31,681 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 86624703 kv 21656168(86624672) kvi 19462536(77850144)
2015-10-19 15:59:33,634 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-19 15:59:33,634 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 15:59:33,634 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 86624703; bufend = 19663524; bufvoid = 104857600
2015-10-19 15:59:33,634 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 21656168(86624672); kvend = 14655792(58623168); length = 7000377/6553600
2015-10-19 15:59:41,932 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 7
2015-10-19 15:59:41,948 INFO [main] org.apache.hadoop.mapred.Merger: Merging 8 sorted segments
2015-10-19 15:59:41,963 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 8 segments left of total size: 288340204 bytes
2015-10-19 16:00:12,768 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445182159119_0015_m_000008_1 is done. And is in the process of committing
2015-10-19 16:00:12,924 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445182159119_0015_m_000008_1' done.
2015-10-19 16:00:13,033 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Stopping MapTask metrics system...
2015-10-19 16:00:13,033 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system stopped.
2015-10-19 16:00:13,033 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system shutdown complete.
