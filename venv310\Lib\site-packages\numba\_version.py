
# This file was generated by 'versioneer.py' (0.28) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-01-15T14:48:07+0100",
 "dirty": false,
 "error": null,
 "full-revisionid": "8ec16ceeb27c1e4ebd05ec0a1e715dc4c1fcfca1",
 "version": "0.61.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
