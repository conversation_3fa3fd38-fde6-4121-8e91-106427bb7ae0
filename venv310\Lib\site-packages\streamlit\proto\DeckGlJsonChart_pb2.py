# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/DeckGlJsonChart.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n%streamlit/proto/DeckGlJsonChart.proto\"\x8d\x02\n\x0f\x44\x65\x63kGlJsonChart\x12\x0c\n\x04json\x18\x01 \x01(\t\x12\x0f\n\x07tooltip\x18\x02 \x01(\t\x12\x1b\n\x13use_container_width\x18\x04 \x01(\x08\x12\n\n\x02id\x18\x05 \x01(\t\x12\x14\n\x0cmapbox_token\x18\x06 \x01(\t\x12\r\n\x05width\x18\x07 \x01(\r\x12\x0e\n\x06height\x18\x08 \x01(\r\x12\x36\n\x0eselection_mode\x18\t \x03(\x0e\x32\x1e.DeckGlJsonChart.SelectionMode\x12\x0f\n\x07\x66orm_id\x18\n \x01(\t\"4\n\rSelectionMode\x12\x11\n\rSINGLE_OBJECT\x10\x00\x12\x10\n\x0cMULTI_OBJECT\x10\x01\x42\x34\n\x1c\x63om.snowflake.apps.streamlitB\x14\x44\x65\x63kGlJsonChartProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.DeckGlJsonChart_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\024DeckGlJsonChartProto'
  _globals['_DECKGLJSONCHART']._serialized_start=42
  _globals['_DECKGLJSONCHART']._serialized_end=311
  _globals['_DECKGLJSONCHART_SELECTIONMODE']._serialized_start=259
  _globals['_DECKGLJSONCHART_SELECTIONMODE']._serialized_end=311
# @@protoc_insertion_point(module_scope)
