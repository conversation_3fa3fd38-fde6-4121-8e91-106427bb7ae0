2015-10-19 14:24:41,362 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 14:24:41,475 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 14:24:41,475 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-19 14:24:41,498 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-19 14:24:41,498 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445182159119_0002, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@666adef3)
2015-10-19 14:24:41,662 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-19 14:24:42,146 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445182159119_0002
2015-10-19 14:24:42,647 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-19 14:24:43,288 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-19 14:24:43,320 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@52841423
2015-10-19 14:24:43,559 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:805306368+134217728
2015-10-19 14:24:43,625 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-19 14:24:43,625 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-19 14:24:43,625 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-19 14:24:43,625 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-19 14:24:43,625 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-19 14:24:43,635 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-19 14:24:45,077 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 14:24:45,077 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34172179; bufvoid = 104857600
2015-10-19 14:24:45,077 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13785924(55143696); length = 12428473/6553600
2015-10-19 14:24:45,077 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44657929 kvi 11164476(44657904)
2015-10-19 14:24:53,850 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-19 14:24:53,856 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44657929 kv 11164476(44657904) kvi 8543052(34172208)
2015-10-19 14:24:54,777 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 14:24:54,777 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44657929; bufend = 78836928; bufvoid = 104857600
2015-10-19 14:24:54,777 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11164476(44657904); kvend = 24952116(99808464); length = 12426761/6553600
2015-10-19 14:24:54,777 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89322688 kvi 22330668(89322672)
2015-10-19 14:25:03,419 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-19 14:25:03,424 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 89322688 kv 22330668(89322672) kvi 19709236(78836944)
2015-10-19 14:25:04,290 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 14:25:04,290 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 89322688; bufend = 18642505; bufvoid = 104857598
2015-10-19 14:25:04,290 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 22330668(89322672); kvend = 9903508(39614032); length = 12427161/6553600
2015-10-19 14:25:04,291 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29128260 kvi 7282060(29128240)
2015-10-19 14:25:12,378 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-19 14:25:12,383 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29128260 kv 7282060(29128240) kvi 4660632(18642528)
2015-10-19 14:25:13,242 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 14:25:13,243 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29128260; bufend = 63302794; bufvoid = 104857600
2015-10-19 14:25:13,243 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7282060(29128240); kvend = 21068580(84274320); length = 12427881/6553600
2015-10-19 14:25:13,243 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 73788549 kvi 18447132(73788528)
2015-10-19 14:25:21,261 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-19 14:25:21,266 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 73788549 kv 18447132(73788528) kvi 15825704(63302816)
2015-10-19 14:25:22,419 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 14:25:22,419 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 73788549; bufend = 3106177; bufvoid = 104857591
2015-10-19 14:25:22,420 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 18447132(73788528); kvend = 6019428(24077712); length = 12427705/6553600
2015-10-19 14:25:22,420 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 13591936 kvi 3397980(13591920)
2015-10-19 14:25:31,839 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-19 14:25:31,844 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 13591936 kv 3397980(13591920) kvi 776552(3106208)
2015-10-19 14:25:32,867 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 14:25:32,867 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 13591936; bufend = 47766714; bufvoid = 104857600
2015-10-19 14:25:32,867 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 3397980(13591920); kvend = 17184560(68738240); length = 12427821/6553600
2015-10-19 14:25:32,867 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 58252469 kvi 14563112(58252448)
2015-10-19 14:25:33,220 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-19 14:25:42,656 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-19 14:25:42,662 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 58252469 kv 14563112(58252448) kvi 12518536(50074144)
2015-10-19 14:25:42,662 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 14:25:42,662 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 58252469; bufend = 63874262; bufvoid = 104857600
2015-10-19 14:25:42,662 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14563112(58252448); kvend = 12518540(50074160); length = 2044573/6553600
2015-10-19 14:25:44,806 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-19 14:25:44,836 INFO [main] org.apache.hadoop.mapred.Merger: Merging 7 sorted segments
2015-10-19 14:25:44,856 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 7 segments left of total size: 228418410 bytes
2015-10-19 14:26:22,613 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445182159119_0002_m_000006_1 is done. And is in the process of committing
2015-10-19 14:26:22,729 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445182159119_0002_m_000006_1' done.
2015-10-19 14:26:22,830 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Stopping MapTask metrics system...
2015-10-19 14:26:22,830 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system stopped.
2015-10-19 14:26:22,830 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system shutdown complete.
