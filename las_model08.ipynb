{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d6c968a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original DataFrame Info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 50 entries, 0 to 49\n", "Data columns (total 29 columns):\n", " #   Column                             Non-Null Count  Dtype  \n", "---  ------                             --------------  -----  \n", " 0   WELL                               50 non-null     object \n", " 1   DEPTH_MD                           50 non-null     float64\n", " 2   X_LOC                              50 non-null     float64\n", " 3   Y_LOC                              50 non-null     object \n", " 4   Z_LOC                              49 non-null     float64\n", " 5   GROUP                              49 non-null     object \n", " 6   FORMATION                          0 non-null      float64\n", " 7   CALI                               49 non-null     float64\n", " 8   RSHA                               0 non-null      float64\n", " 9   RMED                               49 non-null     float64\n", " 10  RDEP                               49 non-null     float64\n", " 11  RHOB                               49 non-null     float64\n", " 12  GR                                 49 non-null     float64\n", " 13  SGR                                0 non-null      float64\n", " 14  NPHI                               0 non-null      float64\n", " 15  PEF                                49 non-null     float64\n", " 16  DTC                                49 non-null     float64\n", " 17  SP                                 49 non-null     float64\n", " 18  BS                                 0 non-null      float64\n", " 19  ROP                                49 non-null     float64\n", " 20  DTS                                0 non-null      float64\n", " 21  DCAL                               0 non-null      float64\n", " 22  DRHO                               49 non-null     float64\n", " 23  MUDWEIGHT                          0 non-null      float64\n", " 24  RMIC                               0 non-null      float64\n", " 25  ROPA                               0 non-null      float64\n", " 26  RXO                                0 non-null      float64\n", " 27  FORCE_2020_LITHOFACIES_LITHOLOGY   49 non-null     float64\n", " 28  FORCE_2020_LITHOFACIES_CONFIDENCE  49 non-null     float64\n", "dtypes: float64(26), object(3)\n", "memory usage: 11.5+ KB\n", "\n", "Original DataFrame Head:\n", "      WELL  DEPTH_MD         X_LOC      Y_LOC       Z_LOC         GROUP  \\\n", "0  15/9-13   494.528  437641.96875  6470972.5 -469.501831  NORDLAND GP.   \n", "1  15/9-13   494.680  437641.96875  6470972.5 -469.653809  NORDLAND GP.   \n", "2  15/9-13   494.832  437641.96875  6470972.5 -469.805786  NORDLAND GP.   \n", "3  15/9-13   494.984  437641.96875  6470972.5 -469.957794  NORDLAND GP.   \n", "4  15/9-13   495.136  437641.96875  6470972.5 -470.109772  NORDLAND GP.   \n", "\n", "   FORMATION       CALI  RSHA      RMED  ...        ROP  DTS  DCAL      DRHO  \\\n", "0        NaN  19.480835   NaN  1.611410  ...  34.636410  NaN   NaN -0.574928   \n", "1        NaN  19.468800   NaN  1.618070  ...  34.636410  NaN   NaN -0.570188   \n", "2        NaN  19.468800   NaN  1.626459  ...  34.779556  NaN   NaN -0.574245   \n", "3        NaN  19.459282   NaN  1.621594  ...  39.965164  NaN   NaN -0.586315   \n", "4        NaN  19.453100   NaN  1.602679  ...  57.483765  NaN   NaN -0.597914   \n", "\n", "   MUDWEIGHT  RMIC  ROPA  RXO  FORCE_2020_LITHOFACIES_LITHOLOGY  \\\n", "0        NaN   NaN   NaN  NaN                           65000.0   \n", "1        NaN   NaN   NaN  NaN                           65000.0   \n", "2        NaN   NaN   NaN  NaN                           65000.0   \n", "3        NaN   NaN   NaN  NaN                           65000.0   \n", "4        NaN   NaN   NaN  NaN                           65000.0   \n", "\n", "   FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "0                                1.0  \n", "1                                1.0  \n", "2                                1.0  \n", "3                                1.0  \n", "4                                1.0  \n", "\n", "[5 rows x 29 columns]\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from io import StringIO\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_squared_error\n", "\n", "# The content of the CSV file. In a real scenario, you would read this directly from the file.\n", "csv_content = \"\"\"WEL<PERSON>,DEPTH_MD,X_LOC,Y_LOC,Z_LOC,GROUP,FORMATION,CALI,RSHA,RMED,RDEP,RHOB,GR,SGR,NPHI,PEF,DTC,SP,BS,ROP,DTS,DCAL,DRHO,MUDWEIGHT,RMIC,ROPA,RXO,FORCE_2020_LITHOFACIES_LITHOLOGY,FORCE_2020_LITHOFACIES_CONFIDENCE\n", "15/9-13,494.528,437641.96875,6470972.5,-469.50183110000006,NORDLAND GP.,,19.480834961,,1.6114097834,1.7986813784,1.884185791,80.20085144,,,20.915468216,161.13117981,24.612379074,,34.63640976,,,-0.574927628,,,,,65000,1.0\n", "15/9-13,494.68,437641.96875,6470972.5,-469.6538086,NORDLAND GP.,,19.468799591,,1.6180702448,1.7956413031,1.8897935152,79.262886047,,,19.383012772,160.60346985,23.895530701,,34.63640976,,,-0.570188403,,,,,65000,1.0\n", "15/9-13,494.832,437641.96875,6470972.5,-469.80578610000003,NORDLAND GP.,,19.468799591,,1.6264585257,1.8007333279,1.8965227604,74.821998596,,,22.591518402,160.1736145,23.91635704,,34.779556274,,,-0.574245155,,,,,65000,1.0\n", "15/9-13,494.98400000000004,437641.96875,6470972.5,-469.95779419999997,NORDLAND GP.,,19.459281921,,1.6215940714,1.8015166521000001,1.8919128180000002,72.878921509,,,32.19190979,160.14942932,23.79368782,,39.965164185,,,-0.5863152739999999,,,,,65000,1.0\n", "15/9-13,495.13599999999997,437641.96875,6470972.5,-470.1097717,NORDLAND GP.,,19.453100204000002,,1.6026790141999998,1.7952990532,1.8800340891,71.729141235,,,38.495632172,160.12834167,24.104078293,,57.483764648000005,,,-0.597913623,,,,,65000,1.0\n", "15/9-13,495.288,437641.96875,6470972.5,-470.26177980000006,NORDLAND GP.,,19.453100204000002,,1.5855668783,1.8047186136000002,1.8796874285,72.01441955600001,,,43.657482146999996,160.14929199,23.931278229,,75.281410217,,,-0.6015999910000001,,,,,65000,1.0\n", "15/9-13,495.44,437641.96875,6470972.5,-470.4137878,NORDLAND GP.,,19.462495804,,1.5765693187999998,1.8054984808,1.8787307738999999,72.588088989,,,42.236221313,161.25038147,23.381790160999998,,76.199951172,,,-0.598368526,,,,,65000,1.0\n", "15/9-13,495.592,437641.96875,6470972.5,-470.56579589999996,NORDLAND GP.,,19.468799591,,1.5870107412,1.8083672522999998,1.8678369522,71.283050537,,,39.933563232,162.2144165,23.632165909,,76.199951172,,,-0.602039278,,,,,65000,1.0\n", "15/9-13,495.744,437641.96875,6470972.5,-470.7177734,NORDLAND GP.,,19.468799591,,1.613673687,1.8158134222,1.8472329378,69.721435547,,,39.163223267,161.57510376,22.163541794,,75.898796082,,,-0.6143639679999999,,,,,65000,1.0\n", "15/9-13,495.89599999999996,437641.96875,6470972.5,-470.86978150000004,NORDLAND GP.,,19.468799591,,1.6346218586,1.8139158487,1.836309433,66.677726746,,,37.802932739,160.58335876,23.659925461,,68.121261597,,,-0.621812582,,,,,65000,1.0\n", "15/9-13,496.048,437641.96875,6470972.5,-471.0217896000001,NORDLAND GP.,,19.468799591,,1.638895154,1.8114999533,1.8451297283,65.892799377,,,36.285533905,159.62438965,24.038724899,,47.22714233399999,,,-0.611830294,,,,,65000,1.0\n", "15/9-13,496.2,437641.96875,6470972.5,-471.1737671,NORDLAND GP.,,19.468799591,,1.6354175806,1.8178203106,1.8601442575,66.928260803,,,37.292488098,158.85522461,23.761310577,,46.828399658,,,-0.59656769,,,,,65000,1.0\n", "15/9-13,496.352,437641.96875,6470972.5,-471.32574460000006,NORDLAND GP.,,19.468799591,,1.6292560101,1.8143187761000001,1.8645105362,70.428024292,,,37.34254837,159.0859375,24.113840103,,46.828399658,,,-0.592989445,,,,,65000,1.0\n", "15/9-13,496.504,437641.96875,6470972.5,-471.4777527,NORDLAND GP.,,19.468799591,,1.6186336279,1.826174736,1.8604578971999999,72.568740845,,,38.716503143000004,159.11553955,24.287750244,,46.828399658,,,-0.597912192,,,,,65000,1.0\n", "15/9-13,496.656,437641.96875,6470972.5,-471.62976069999996,NORDLAND GP.,,19.459733962999998,,1.6183816194999998,1.8354929686,1.8589774369999998,71.932228088,,,33.712387085,159.13540649,24.305999756,,46.766792296999995,,,-0.6022745970000001,,,,,65000,1.0\n", "15/9-13,496.80800000000005,437641.96875,6470972.5,-471.7817688,NORDLAND GP.,,19.471090317,,1.6282503605,1.8268495798,1.8633086681,71.095474243,,,30.341299056999997,159.44454956,24.412090302,,46.560054779,,,-0.601545691,,,,,65000,1.0\n", "15/9-13,496.96,437641.96875,6470972.5,-471.93374630000005,NORDLAND GP.,,19.493326187,,1.6327999830000002,1.8267626762,1.879073143,74.254371643,,,29.63158989,159.09140015,23.796445846999998,,46.310504913,,,-0.593978047,,,,,65000,1.0\n", "15/9-13,497.11199999999997,437641.96875,6470972.5,-472.0857544,NORDLAND GP.,,19.5,,1.6200435162,1.8239495754,1.8897819518999999,77.266525269,,,34.332809448,159.35050964,23.575572968000003,,46.060894012,,,-0.592418253,,,,,65000,1.0\n", "15/9-13,497.264,437641.96875,6470972.5,-472.2377625,NORDLAND GP.,,19.5,,1.5970795155000002,1.8226443528999998,1.8950212002000002,77.243659973,,,44.43385696399999,159.91371155,23.661094666,,45.811397551999995,,,-0.5977608560000001,,,,,65000,1.0\n", "15/9-13,497.416,437641.96875,6470972.5,-472.38974,NORDLAND GP.,,19.5,,1.5808621644999998,1.8240714073,1.8818663359,74.072761536,,,58.937194823999995,160.68223572,23.785762787,,45.561885833999995,,,-0.616915762,,,,,65000,1.0\n", "15/9-13,497.56800000000004,437641.96875,6470972.5,-472.54174800000004,NORDLAND GP.,,19.5,,1.5833338499,1.8226944208000002,1.8718523979,72.198684692,,,54.921722412,161.35916138,24.250545501999998,,45.326885223000005,,,-0.627428412,,,,,65000,1.0\n", "15/9-13,497.72,437641.96875,6470972.5,-472.6937256,NORDLAND GP.,,19.5,,1.5998016596,1.8217409849000001,1.8767393827,73.68801116899999,,,47.026332855,161.16438293,24.519592285,,44.244506836000006,,,-0.6183999179999999,,,,,65000,1.0\n", "15/9-13,497.87199999999996,437641.96875,6470972.5,-472.8457336,NORDLAND GP.,,19.5,,1.6116827725999998,1.8185735940999999,1.8875800371,72.747123718,,,36.532077789,160.58572388,25.074958800999998,,40.593151093,,,-0.608738244,,,,,65000,1.0\n", "15/9-13,498.024,437641.96875,6470972.5,-472.99771119999997,NORDLAND GP.,,19.508638382,,1.6137462854,1.8126344681,1.9042459725999998,70.038673401,,,30.112548828,160.08589172,24.663700104,,39.007671355999996,,,-0.604946256,,,,,65000,1.0\n", "15/9-13,498.176,437641.96875,6470972.5,-473.1497192,NORDLAND GP.,,19.515600204000002,,1.6148980856,1.8170949221000001,1.9239485263999998,69.458602905,,,36.195671082,159.83093262,23.963632584000003,,38.924438476999995,,,-0.603397846,,,,,65000,1.0\n", "15/9-13,498.32800000000003,437641.96875,6470972.5,-473.3017273,NORDLAND GP.,,19.515600204000002,,1.6097832918000001,1.8280262946999999,1.9202179909000001,70.175437927,,,56.303665161000005,160.04148865,24.120071410999998,,38.841323853,,,-0.6068326829999999,,,,,65000,1.0\n", "15/9-13,498.48,437641.96875,6470972.5,-473.4537353999999,NORDLAND GP.,,19.515600204000002,,1.6039541959999999,1.8309628963,1.8905304669999998,69.169387817,,,66.030319214,160.02119446,23.873607635,,38.758102416999996,,,-0.615422189,,,,,65000,1.0\n", "15/9-13,498.63199999999995,437642.0,6470972.5,-473.60571289999996,NORDLAND GP.,,19.507123947,,1.6136058569,1.8337945938,1.8665241003,66.941886902,,,49.00489807100001,159.78358459,24.418914795,,38.673583984000004,,,-0.618839324,,,,,65000,1.0\n", "15/9-13,498.784,437642.0,6470972.5,-473.7577209,NORDLAND GP.,,19.5,,1.6289664507,1.8400899172,1.863617301,68.770942688,,,38.920906067,159.63774109,24.276592255,,38.589603424,,,-0.61290139,,,,,65000,1.0\n", "15/9-13,498.936,437642.0,6470972.5,-473.909729,NORDLAND GP.,,19.5,,1.6363601685,1.8386034966,1.8740327358000002,71.864768982,,,35.378421783,159.51885986,23.705533981,,37.036060333,,,-0.6110363010000001,,,,,65000,1.0\n", "15/9-13,499.088,437642.0,6470972.5,-474.0617371,NORDLAND GP.,,19.483297348,,1.6392525434,1.8370878696000001,1.8631432056000001,74.23991394,,,29.563484191999997,159.71707153,24.16906929,,32.157131195,,,-0.627915323,,,,,65000,1.0\n", "15/9-13,499.24,437642.0,6470972.5,-474.21371460000006,NORDLAND GP.,,19.485420227,,1.6395878791999998,1.8383673428999998,1.8512102365,73.419425964,,,26.189195633,159.78979492,24.029325485,,31.865451813000004,,,-0.643742979,,,,,65000,1.0\n", "15/9-13,499.392,437642.0,6470972.5,-474.3656921,NORDLAND GP.,,19.5,,1.6355726718999999,1.8410272598,1.8566273451,74.15774536100001,,,30.852775574000002,160.27708435,24.13826561,,31.865451813000004,,,-0.63950336,,,,,65000,1.0\n", "15/9-13,499.54400000000004,437642.0,6470972.5,-474.51770020000004,NORDLAND GP.,,19.5,,1.6230412722,1.8427449465,1.8640419245,74.61391449,,,36.393230438,160.46502686,22.978778839,,31.865451813000004,,,-0.631506503,,,,,65000,1.0\n", "15/9-13,499.69599999999997,437642.0,6470972.5,-474.6697083000001,NORDLAND GP.,,19.5,,1.6091556549000001,1.8346083163999998,1.8697222471000001,74.092033386,,,44.721645355,160.50489807,23.679580688,,31.865451813000004,,,-0.631422281,,,,,65000,1.0\n", "15/9-13,499.848,437642.0,6470972.5,-474.8216858,NORDLAND GP.,,19.5,,1.6014186144,1.8378143311000001,1.8720923662,73.944297791,,,43.747562408,160.49928284,24.130052566999996,,31.865451813000004,,,-0.635620296,,,,,65000,1.0\n", "15/9-13,500.0,437642.0,6470972.5,-474.97369380000004,NORDLAND GP.,,19.5,,1.6031726599,1.8436342478,1.8679596186,75.05583190899999,,,37.737670898000005,160.48558044,23.88341713,,31.739553452,,,-0.643759966,,,,,65000,1.0\n", "15/9-13,500.152,437642.0,6470972.5,-475.12570189999997,NORDLAND GP.,,19.5,,1.6104500294,1.8367676735,1.8572515249000001,76.508979797,,,31.725177765,160.36244202,23.805999756,,31.073074340999998,,,-0.652932346,,,,,65000,1.0\n", "15/9-13,500.30400000000003,437642.0,6470972.5,-475.27767939999995,NORDLAND GP.,,19.5,,1.6238434315,1.8315085172999999,1.845531106,73.464164734,,,26.771625519,159.83699036,23.978624344,,30.601942062,,,-0.659477293,,,,,65000,1.0\n", "15/9-13,500.45599999999996,437642.0,6470972.5,-475.4296875,NORDLAND GP.,,19.5,,1.6372792721,1.8285651207,1.8473213911000002,68.757133484,,,29.252349854000002,159.25857544,24.123197555999997,,30.155824661,,,-0.6578532460000001,,,,,65000,1.0\n", "15/9-13,500.608,437642.0,6470972.5,-475.58169560000005,NORDLAND GP.,,19.5,,1.640599966,1.8311219215,1.8530236483000002,66.214767456,,,31.11192894,159.04550171,24.24788475,,29.709711075,,,-0.6552764179999999,,,,,65000,1.0\n", "15/9-13,500.76,437642.0,6470972.5,-475.73367310000003,NORDLAND GP.,,19.476243973,,1.6307224034999999,1.8260848522,1.8832590580000002,67.519630432,,,25.780155181999998,159.09196472,23.884763718000002,,29.292463303,,,-0.652039766,,,,,65000,1.0\n", "15/9-13,500.912,437642.0,6470972.5,-475.8856506,NORDLAND GP.,,19.445240021,,1.6196386814,1.8227915763999998,1.9485155344,67.74028015100001,,,17.592674255,159.31990051,24.09079361,,28.97000885,,,-0.658081651,,,,,65000,1.0\n", "15/9-13,501.064,437642.0,6470972.5,-476.0376587,NORDLAND GP.,,19.4375,,1.5937899351,1.8256438971000002,2.0333251953,66.249382019,,,17.641107559,159.49562073,24.47168541,,28.769348145,,,-0.679025888,,,,,65000,1.0\n", "15/9-13,501.216,437642.0,6470972.5,-476.1896666999999,NORDLAND GP.,,19.421943665,,1.558780551,1.8266402483000002,2.1492137909,65.563743591,,,16.600845337,159.68937683,24.942251205,,28.568696976,,,-0.6539515260000001,,,,,65000,1.0\n", "15/9-13,501.36800000000005,437642.0,6470972.5,-476.34167479999996,NORDLAND GP.,,19.421772003,,1.5373376608000002,1.8281658887999999,2.1131362915,66.860717773,,,9.6992645264,159.92002869,25.633386612,,28.368062973,,,-0.594197512,,,,,65000,1.0\n", "15/9-13,501.52,437642.0,6470972.5,-476.4936523,NORDLAND GP.,,19.545425415,,1.5158455372,1.8295542002,1.9948540925999998,69.580696106,,,4.78414011,159.99858093,24.919124603,,28.167427063,,,-0.430847555,,,,,65000,1.0\n", "15/9-13,501.67199999999997,437642.0,6470972.5,-476.64566039999994,NORDLAND GP.,,19.702278137,,1.4943498373,1.8334907293,1.8836650847999998,71.94261169399999,,,3.8371455669,159.79779053,24.578119278000003,,27.96676445,,,-0.220775113,,,,,65000,1.0\n", "15/9-13,501.824,437642.0,6470972.5,-476.7976685,NORDLAND GP.,,20.016969681,,1.4729046822,1.835708499,1.7584255934,73.34449005100001,,,3.5728118419999997,159.55261230000002,24.555999756,,27.762060165,,,-0.09583643800000001,,,,,65000,1.0\n", "15/9-13,501.976,437642.0,647...\"\"\"\n", "\n", "df = pd.read_csv(StringIO(csv_content))\n", "\n", "# Display initial information about the DataFrame\n", "print(\"Original DataFrame Info:\")\n", "df.info()\n", "print(\"\\nOriginal DataFrame Head:\")\n", "print(df.head())\n", "\n", "# Select a numerical column for demonstration\n", "column_to_manipulate = 'RHOB' # Density"]}, {"cell_type": "code", "execution_count": 2, "id": "02c70d0d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "DataFrame after random 10% removal in 'RHOB':\n", "6 missing values\n", "      WELL  DEPTH_MD         X_LOC      Y_LOC       Z_LOC         GROUP  \\\n", "0  15/9-13   494.528  437641.96875  6470972.5 -469.501831  NORDLAND GP.   \n", "1  15/9-13   494.680  437641.96875  6470972.5 -469.653809  NORDLAND GP.   \n", "2  15/9-13   494.832  437641.96875  6470972.5 -469.805786  NORDLAND GP.   \n", "3  15/9-13   494.984  437641.96875  6470972.5 -469.957794  NORDLAND GP.   \n", "4  15/9-13   495.136  437641.96875  6470972.5 -470.109772  NORDLAND GP.   \n", "\n", "   FORMATION       CALI  RSHA      RMED  ...        ROP  DTS  DCAL      DRHO  \\\n", "0        NaN  19.480835   NaN  1.611410  ...  34.636410  NaN   NaN -0.574928   \n", "1        NaN  19.468800   NaN  1.618070  ...  34.636410  NaN   NaN -0.570188   \n", "2        NaN  19.468800   NaN  1.626459  ...  34.779556  NaN   NaN -0.574245   \n", "3        NaN  19.459282   NaN  1.621594  ...  39.965164  NaN   NaN -0.586315   \n", "4        NaN  19.453100   NaN  1.602679  ...  57.483765  NaN   NaN -0.597914   \n", "\n", "   MUDWEIGHT  RMIC  ROPA  RXO  FORCE_2020_LITHOFACIES_LITHOLOGY  \\\n", "0        NaN   NaN   NaN  NaN                           65000.0   \n", "1        NaN   NaN   NaN  NaN                           65000.0   \n", "2        NaN   NaN   NaN  NaN                           65000.0   \n", "3        NaN   NaN   NaN  NaN                           65000.0   \n", "4        NaN   NaN   NaN  NaN                           65000.0   \n", "\n", "   FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "0                                1.0  \n", "1                                1.0  \n", "2                                1.0  \n", "3                                1.0  \n", "4                                1.0  \n", "\n", "[5 rows x 29 columns]\n"]}], "source": ["# Create a copy to work with, so the original DataFrame remains untouched\n", "df_random_missing = df.copy()\n", "\n", "# Set 10% of 'RHOB' values to NaN randomly\n", "missing_percentage = 0.10\n", "n_missing = int(len(df_random_missing) * missing_percentage)\n", "\n", "# Get random indices to set to NaN\n", "random_indices = np.random.choice(df_random_missing.index, n_missing, replace=False)\n", "df_random_missing.loc[random_indices, column_to_manipulate] = np.nan\n", "\n", "print(f\"\\nDataFrame after random 10% removal in '{column_to_manipulate}':\")\n", "print(df_random_missing[column_to_manipulate].isnull().sum(), \"missing values\")\n", "print(df_random_missing.head())"]}, {"cell_type": "code", "execution_count": 3, "id": "5e238b3c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DEPTH_MD</th>\n", "      <th>X_LOC</th>\n", "      <th>Z_LOC</th>\n", "      <th>FORMATION</th>\n", "      <th>CALI</th>\n", "      <th>RSHA</th>\n", "      <th>RMED</th>\n", "      <th>RDEP</th>\n", "      <th>RHOB</th>\n", "      <th>GR</th>\n", "      <th>...</th>\n", "      <th>ROP</th>\n", "      <th>DTS</th>\n", "      <th>DCAL</th>\n", "      <th>DRHO</th>\n", "      <th>MUDWEIGHT</th>\n", "      <th>RMIC</th>\n", "      <th>ROPA</th>\n", "      <th>RXO</th>\n", "      <th>FORCE_2020_LITHOFACIES_LITHOLOGY</th>\n", "      <th>FORCE_2020_LITHOFACIES_CONFIDENCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>50.000000</td>\n", "      <td>50.000000</td>\n", "      <td>49.000000</td>\n", "      <td>0.0</td>\n", "      <td>49.000000</td>\n", "      <td>0.0</td>\n", "      <td>49.000000</td>\n", "      <td>49.000000</td>\n", "      <td>49.000000</td>\n", "      <td>49.000000</td>\n", "      <td>...</td>\n", "      <td>49.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>49.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>49.0</td>\n", "      <td>49.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>498.252000</td>\n", "      <td>437641.983125</td>\n", "      <td>-473.149730</td>\n", "      <td>NaN</td>\n", "      <td>19.498996</td>\n", "      <td>NaN</td>\n", "      <td>1.605717</td>\n", "      <td>1.823782</td>\n", "      <td>1.888615</td>\n", "      <td>71.656830</td>\n", "      <td>...</td>\n", "      <td>41.032882</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.594646</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>2.215762</td>\n", "      <td>0.015733</td>\n", "      <td>2.171837</td>\n", "      <td>NaN</td>\n", "      <td>0.085429</td>\n", "      <td>NaN</td>\n", "      <td>0.036139</td>\n", "      <td>0.012925</td>\n", "      <td>0.064052</td>\n", "      <td>3.532759</td>\n", "      <td>...</td>\n", "      <td>13.435249</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.098787</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>494.528000</td>\n", "      <td>437641.968750</td>\n", "      <td>-476.797668</td>\n", "      <td>NaN</td>\n", "      <td>19.421772</td>\n", "      <td>NaN</td>\n", "      <td>1.472905</td>\n", "      <td>1.795299</td>\n", "      <td>1.758426</td>\n", "      <td>65.563744</td>\n", "      <td>...</td>\n", "      <td>27.762060</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.679026</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>496.390000</td>\n", "      <td>437641.968750</td>\n", "      <td>-474.973694</td>\n", "      <td>NaN</td>\n", "      <td>19.468800</td>\n", "      <td>NaN</td>\n", "      <td>1.599802</td>\n", "      <td>1.815813</td>\n", "      <td>1.860458</td>\n", "      <td>69.169388</td>\n", "      <td>...</td>\n", "      <td>31.739553</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.631507</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>498.252000</td>\n", "      <td>437641.968750</td>\n", "      <td>-473.149719</td>\n", "      <td>NaN</td>\n", "      <td>19.500000</td>\n", "      <td>NaN</td>\n", "      <td>1.613746</td>\n", "      <td>1.826175</td>\n", "      <td>1.874033</td>\n", "      <td>71.942612</td>\n", "      <td>...</td>\n", "      <td>38.673584</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.611036</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>500.114000</td>\n", "      <td>437642.000000</td>\n", "      <td>-471.325745</td>\n", "      <td>NaN</td>\n", "      <td>19.500000</td>\n", "      <td>NaN</td>\n", "      <td>1.628966</td>\n", "      <td>1.833795</td>\n", "      <td>1.889794</td>\n", "      <td>74.072762</td>\n", "      <td>...</td>\n", "      <td>46.310505</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.597761</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>501.976000</td>\n", "      <td>437642.000000</td>\n", "      <td>-469.501831</td>\n", "      <td>NaN</td>\n", "      <td>20.016970</td>\n", "      <td>NaN</td>\n", "      <td>1.640600</td>\n", "      <td>1.843634</td>\n", "      <td>2.149214</td>\n", "      <td>80.200851</td>\n", "      <td>...</td>\n", "      <td>76.199951</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.095836</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>65000.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8 rows × 26 columns</p>\n", "</div>"], "text/plain": ["         DEPTH_MD          X_LOC       Z_LOC  FORMATION       CALI  RSHA  \\\n", "count   50.000000      50.000000   49.000000        0.0  49.000000   0.0   \n", "mean   498.252000  437641.983125 -473.149730        NaN  19.498996   NaN   \n", "std      2.215762       0.015733    2.171837        NaN   0.085429   NaN   \n", "min    494.528000  437641.968750 -476.797668        NaN  19.421772   NaN   \n", "25%    496.390000  437641.968750 -474.973694        NaN  19.468800   NaN   \n", "50%    498.252000  437641.968750 -473.149719        NaN  19.500000   NaN   \n", "75%    500.114000  437642.000000 -471.325745        NaN  19.500000   NaN   \n", "max    501.976000  437642.000000 -469.501831        NaN  20.016970   NaN   \n", "\n", "            RMED       RDEP       RHOB         GR  ...        ROP  DTS  DCAL  \\\n", "count  49.000000  49.000000  49.000000  49.000000  ...  49.000000  0.0   0.0   \n", "mean    1.605717   1.823782   1.888615  71.656830  ...  41.032882  NaN   NaN   \n", "std     0.036139   0.012925   0.064052   3.532759  ...  13.435249  NaN   NaN   \n", "min     1.472905   1.795299   1.758426  65.563744  ...  27.762060  NaN   NaN   \n", "25%     1.599802   1.815813   1.860458  69.169388  ...  31.739553  NaN   NaN   \n", "50%     1.613746   1.826175   1.874033  71.942612  ...  38.673584  NaN   NaN   \n", "75%     1.628966   1.833795   1.889794  74.072762  ...  46.310505  NaN   NaN   \n", "max     1.640600   1.843634   2.149214  80.200851  ...  76.199951  NaN   NaN   \n", "\n", "            DRHO  MUDWEIGHT  RMIC  ROPA  RXO  \\\n", "count  49.000000        0.0   0.0   0.0  0.0   \n", "mean   -0.594646        NaN   NaN   NaN  NaN   \n", "std     0.098787        NaN   NaN   NaN  NaN   \n", "min    -0.679026        NaN   NaN   NaN  NaN   \n", "25%    -0.631507        NaN   NaN   NaN  NaN   \n", "50%    -0.611036        NaN   NaN   NaN  NaN   \n", "75%    -0.597761        NaN   NaN   NaN  NaN   \n", "max    -0.095836        NaN   NaN   NaN  NaN   \n", "\n", "       FORCE_2020_LITHOFACIES_LITHOLOGY  FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "count                              49.0                               49.0  \n", "mean                            65000.0                                1.0  \n", "std                                 0.0                                0.0  \n", "min                             65000.0                                1.0  \n", "25%                             65000.0                                1.0  \n", "50%                             65000.0                                1.0  \n", "75%                             65000.0                                1.0  \n", "max                             65000.0                                1.0  \n", "\n", "[8 rows x 26 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": 4, "id": "10a2d50f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 50 entries, 0 to 49\n", "Data columns (total 29 columns):\n", " #   Column                             Non-Null Count  Dtype  \n", "---  ------                             --------------  -----  \n", " 0   WELL                               50 non-null     object \n", " 1   DEPTH_MD                           50 non-null     float64\n", " 2   X_LOC                              50 non-null     float64\n", " 3   Y_LOC                              50 non-null     object \n", " 4   Z_LOC                              49 non-null     float64\n", " 5   GROUP                              49 non-null     object \n", " 6   FORMATION                          0 non-null      float64\n", " 7   CALI                               49 non-null     float64\n", " 8   RSHA                               0 non-null      float64\n", " 9   RMED                               49 non-null     float64\n", " 10  RDEP                               49 non-null     float64\n", " 11  RHOB                               49 non-null     float64\n", " 12  GR                                 49 non-null     float64\n", " 13  SGR                                0 non-null      float64\n", " 14  NPHI                               0 non-null      float64\n", " 15  PEF                                49 non-null     float64\n", " 16  DTC                                49 non-null     float64\n", " 17  SP                                 49 non-null     float64\n", " 18  BS                                 0 non-null      float64\n", " 19  ROP                                49 non-null     float64\n", " 20  DTS                                0 non-null      float64\n", " 21  DCAL                               0 non-null      float64\n", " 22  DRHO                               49 non-null     float64\n", " 23  MUDWEIGHT                          0 non-null      float64\n", " 24  RMIC                               0 non-null      float64\n", " 25  ROPA                               0 non-null      float64\n", " 26  RXO                                0 non-null      float64\n", " 27  FORCE_2020_LITHOFACIES_LITHOLOGY   49 non-null     float64\n", " 28  FORCE_2020_LITHOFACIES_CONFIDENCE  49 non-null     float64\n", "dtypes: float64(26), object(3)\n", "memory usage: 11.5+ KB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 5, "id": "78ae325c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "DataFrame after conditional removal in 'RHOB' (values outside 1.0-3.0):\n", "1 missing values\n", "      WELL  DEPTH_MD         X_LOC      Y_LOC       Z_LOC         GROUP  \\\n", "0  15/9-13   494.528  437641.96875  6470972.5 -469.501831  NORDLAND GP.   \n", "1  15/9-13   494.680  437641.96875  6470972.5 -469.653809  NORDLAND GP.   \n", "2  15/9-13   494.832  437641.96875  6470972.5 -469.805786  NORDLAND GP.   \n", "3  15/9-13   494.984  437641.96875  6470972.5 -469.957794  NORDLAND GP.   \n", "4  15/9-13   495.136  437641.96875  6470972.5 -470.109772  NORDLAND GP.   \n", "\n", "   FORMATION       CALI  RSHA      RMED  ...        ROP  DTS  DCAL      DRHO  \\\n", "0        NaN  19.480835   NaN  1.611410  ...  34.636410  NaN   NaN -0.574928   \n", "1        NaN  19.468800   NaN  1.618070  ...  34.636410  NaN   NaN -0.570188   \n", "2        NaN  19.468800   NaN  1.626459  ...  34.779556  NaN   NaN -0.574245   \n", "3        NaN  19.459282   NaN  1.621594  ...  39.965164  NaN   NaN -0.586315   \n", "4        NaN  19.453100   NaN  1.602679  ...  57.483765  NaN   NaN -0.597914   \n", "\n", "   MUDWEIGHT  RMIC  ROPA  RXO  FORCE_2020_LITHOFACIES_LITHOLOGY  \\\n", "0        NaN   NaN   NaN  NaN                           65000.0   \n", "1        NaN   NaN   NaN  NaN                           65000.0   \n", "2        NaN   NaN   NaN  NaN                           65000.0   \n", "3        NaN   NaN   NaN  NaN                           65000.0   \n", "4        NaN   NaN   NaN  NaN                           65000.0   \n", "\n", "   FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "0                                1.0  \n", "1                                1.0  \n", "2                                1.0  \n", "3                                1.0  \n", "4                                1.0  \n", "\n", "[5 rows x 29 columns]\n"]}], "source": ["# Create another copy\n", "df_conditional_missing = df.copy()\n", "\n", "# Define a plausible range for 'RHOB' (e.g., density values between 1.0 and 3.0 g/cm^3)\n", "min_rhob = 1.0\n", "max_rhob = 3.0\n", "\n", "# Set values outside this range to NaN\n", "df_conditional_missing.loc[(df_conditional_missing[column_to_manipulate] < min_rhob) |\n", "                           (df_conditional_missing[column_to_manipulate] > max_rhob),\n", "                           column_to_manipulate] = np.nan\n", "\n", "print(f\"\\nDataFrame after conditional removal in '{column_to_manipulate}' (values outside {min_rhob}-{max_rhob}):\")\n", "print(df_conditional_missing[column_to_manipulate].isnull().sum(), \"missing values\")\n", "print(df_conditional_missing.head())"]}, {"cell_type": "code", "execution_count": 6, "id": "99d7d02f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "DataFrame after random noise injection in 'RHOB':\n", "      WELL  DEPTH_MD         X_LOC      Y_LOC       Z_LOC         GROUP  \\\n", "0  15/9-13   494.528  437641.96875  6470972.5 -469.501831  NORDLAND GP.   \n", "1  15/9-13   494.680  437641.96875  6470972.5 -469.653809  NORDLAND GP.   \n", "2  15/9-13   494.832  437641.96875  6470972.5 -469.805786  NORDLAND GP.   \n", "3  15/9-13   494.984  437641.96875  6470972.5 -469.957794  NORDLAND GP.   \n", "4  15/9-13   495.136  437641.96875  6470972.5 -470.109772  NORDLAND GP.   \n", "\n", "   FORMATION       CALI  RSHA      RMED  ...        ROP  DTS  DCAL      DRHO  \\\n", "0        NaN  19.480835   NaN  1.611410  ...  34.636410  NaN   NaN -0.574928   \n", "1        NaN  19.468800   NaN  1.618070  ...  34.636410  NaN   NaN -0.570188   \n", "2        NaN  19.468800   NaN  1.626459  ...  34.779556  NaN   NaN -0.574245   \n", "3        NaN  19.459282   NaN  1.621594  ...  39.965164  NaN   NaN -0.586315   \n", "4        NaN  19.453100   NaN  1.602679  ...  57.483765  NaN   NaN -0.597914   \n", "\n", "   MUDWEIGHT  RMIC  ROPA  RXO  FORCE_2020_LITHOFACIES_LITHOLOGY  \\\n", "0        NaN   NaN   NaN  NaN                           65000.0   \n", "1        NaN   NaN   NaN  NaN                           65000.0   \n", "2        NaN   NaN   NaN  NaN                           65000.0   \n", "3        NaN   NaN   NaN  NaN                           65000.0   \n", "4        NaN   NaN   NaN  NaN                           65000.0   \n", "\n", "   FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "0                                1.0  \n", "1                                1.0  \n", "2                                1.0  \n", "3                                1.0  \n", "4                                1.0  \n", "\n", "[5 rows x 29 columns]\n"]}], "source": ["# Create another copy\n", "df_noise_injection = df.copy()\n", "\n", "# Add Gaussian noise to 'RHOB'\n", "# Mean of noise = 0, Standard deviation of noise = 0.05 * std_dev_of_RHOB\n", "noise_std = 0.05 * df_noise_injection[column_to_manipulate].std()\n", "noise = np.random.normal(0, noise_std, size=len(df_noise_injection))\n", "df_noise_injection[column_to_manipulate] = df_noise_injection[column_to_manipulate] + noise\n", "\n", "print(f\"\\nDataFrame after random noise injection in '{column_to_manipulate}':\")\n", "print(df_noise_injection.head())"]}, {"cell_type": "code", "execution_count": 8, "id": "d6ddcb5b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "DataFrame after injecting 6 outliers in 'RHOB':\n", "38    10.0\n", "22    -5.0\n", "33    15.0\n", "19     5.0\n", "29    13.0\n", "7     30.0\n", "Name: RHOB, dtype: float64\n", "      WELL  DEPTH_MD         X_LOC      Y_LOC       Z_LOC         GROUP  \\\n", "0  15/9-13   494.528  437641.96875  6470972.5 -469.501831  NORDLAND GP.   \n", "1  15/9-13   494.680  437641.96875  6470972.5 -469.653809  NORDLAND GP.   \n", "2  15/9-13   494.832  437641.96875  6470972.5 -469.805786  NORDLAND GP.   \n", "3  15/9-13   494.984  437641.96875  6470972.5 -469.957794  NORDLAND GP.   \n", "4  15/9-13   495.136  437641.96875  6470972.5 -470.109772  NORDLAND GP.   \n", "\n", "   FORMATION       CALI  RSHA      RMED  ...        ROP  DTS  DCAL      DRHO  \\\n", "0        NaN  19.480835   NaN  1.611410  ...  34.636410  NaN   NaN -0.574928   \n", "1        NaN  19.468800   NaN  1.618070  ...  34.636410  NaN   NaN -0.570188   \n", "2        NaN  19.468800   NaN  1.626459  ...  34.779556  NaN   NaN -0.574245   \n", "3        NaN  19.459282   NaN  1.621594  ...  39.965164  NaN   NaN -0.586315   \n", "4        NaN  19.453100   NaN  1.602679  ...  57.483765  NaN   NaN -0.597914   \n", "\n", "   MUDWEIGHT  RMIC  ROPA  RXO  FORCE_2020_LITHOFACIES_LITHOLOGY  \\\n", "0        NaN   NaN   NaN  NaN                           65000.0   \n", "1        NaN   NaN   NaN  NaN                           65000.0   \n", "2        NaN   NaN   NaN  NaN                           65000.0   \n", "3        NaN   NaN   NaN  NaN                           65000.0   \n", "4        NaN   NaN   NaN  NaN                           65000.0   \n", "\n", "   FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "0                                1.0  \n", "1                                1.0  \n", "2                                1.0  \n", "3                                1.0  \n", "4                                1.0  \n", "\n", "[5 rows x 29 columns]\n"]}], "source": ["# Create another copy\n", "df_outlier_injection = df.copy()\n", "\n", "# Inject 3 extreme outliers into 'RHOB'\n", "num_outliers = 6\n", "outlier_values = [10.0, -5.0, 15.0, 5.0, 13.0, 30.0] # Values far outside the normal range\n", "\n", "# Select random indices to inject outliers\n", "outlier_indices = np.random.choice(df_outlier_injection.index, num_outliers, replace=False)\n", "df_outlier_injection.loc[outlier_indices, column_to_manipulate] = outlier_values\n", "\n", "print(f\"\\nDataFrame after injecting {num_outliers} outliers in '{column_to_manipulate}':\")\n", "print(df_outlier_injection.loc[outlier_indices, column_to_manipulate])\n", "print(df_outlier_injection.head())"]}, {"cell_type": "code", "execution_count": 9, "id": "ea9b3a7f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "DataFrame after mean imputation in 'RHOB':\n", "0 missing values (should be 0)\n", "      WELL  DEPTH_MD         X_LOC      Y_LOC       Z_LOC         GROUP  \\\n", "0  15/9-13   494.528  437641.96875  6470972.5 -469.501831  NORDLAND GP.   \n", "1  15/9-13   494.680  437641.96875  6470972.5 -469.653809  NORDLAND GP.   \n", "2  15/9-13   494.832  437641.96875  6470972.5 -469.805786  NORDLAND GP.   \n", "3  15/9-13   494.984  437641.96875  6470972.5 -469.957794  NORDLAND GP.   \n", "4  15/9-13   495.136  437641.96875  6470972.5 -470.109772  NORDLAND GP.   \n", "\n", "   FORMATION       CALI  RSHA      RMED  ...        ROP  DTS  DCAL      DRHO  \\\n", "0        NaN  19.480835   NaN  1.611410  ...  34.636410  NaN   NaN -0.574928   \n", "1        NaN  19.468800   NaN  1.618070  ...  34.636410  NaN   NaN -0.570188   \n", "2        NaN  19.468800   NaN  1.626459  ...  34.779556  NaN   NaN -0.574245   \n", "3        NaN  19.459282   NaN  1.621594  ...  39.965164  NaN   NaN -0.586315   \n", "4        NaN  19.453100   NaN  1.602679  ...  57.483765  NaN   NaN -0.597914   \n", "\n", "   MUDWEIGHT  RMIC  ROPA  RXO  FORCE_2020_LITHOFACIES_LITHOLOGY  \\\n", "0        NaN   NaN   NaN  NaN                           65000.0   \n", "1        NaN   NaN   NaN  NaN                           65000.0   \n", "2        NaN   NaN   NaN  NaN                           65000.0   \n", "3        NaN   NaN   NaN  NaN                           65000.0   \n", "4        NaN   NaN   NaN  NaN                           65000.0   \n", "\n", "   FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "0                                1.0  \n", "1                                1.0  \n", "2                                1.0  \n", "3                                1.0  \n", "4                                1.0  \n", "\n", "[5 rows x 29 columns]\n", "\n", "DataFrame after linear interpolation in 'RHOB':\n", "0 missing values (should be 0)\n", "      WELL  DEPTH_MD         X_LOC      Y_LOC       Z_LOC         GROUP  \\\n", "0  15/9-13   494.528  437641.96875  6470972.5 -469.501831  NORDLAND GP.   \n", "1  15/9-13   494.680  437641.96875  6470972.5 -469.653809  NORDLAND GP.   \n", "2  15/9-13   494.832  437641.96875  6470972.5 -469.805786  NORDLAND GP.   \n", "3  15/9-13   494.984  437641.96875  6470972.5 -469.957794  NORDLAND GP.   \n", "4  15/9-13   495.136  437641.96875  6470972.5 -470.109772  NORDLAND GP.   \n", "\n", "   FORMATION       CALI  RSHA      RMED  ...        ROP  DTS  DCAL      DRHO  \\\n", "0        NaN  19.480835   NaN  1.611410  ...  34.636410  NaN   NaN -0.574928   \n", "1        NaN  19.468800   NaN  1.618070  ...  34.636410  NaN   NaN -0.570188   \n", "2        NaN  19.468800   NaN  1.626459  ...  34.779556  NaN   NaN -0.574245   \n", "3        NaN  19.459282   NaN  1.621594  ...  39.965164  NaN   NaN -0.586315   \n", "4        NaN  19.453100   NaN  1.602679  ...  57.483765  NaN   NaN -0.597914   \n", "\n", "   MUDWEIGHT  RMIC  ROPA  RXO  FORCE_2020_LITHOFACIES_LITHOLOGY  \\\n", "0        NaN   NaN   NaN  NaN                           65000.0   \n", "1        NaN   NaN   NaN  NaN                           65000.0   \n", "2        NaN   NaN   NaN  NaN                           65000.0   \n", "3        NaN   NaN   NaN  NaN                           65000.0   \n", "4        NaN   NaN   NaN  NaN                           65000.0   \n", "\n", "   FORCE_2020_LITHOFACIES_CONFIDENCE  \n", "0                                1.0  \n", "1                                1.0  \n", "2                                1.0  \n", "3                                1.0  \n", "4                                1.0  \n", "\n", "[5 rows x 29 columns]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11456\\4152669914.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_imputed_mean[column_to_manipulate].fillna(df_imputed_mean[column_to_manipulate].mean(), inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11456\\4152669914.py:14: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_imputed_linear[column_to_manipulate].interpolate(method='linear', direction='both', inplace=True)\n"]}], "source": ["# Let's use df_random_missing for imputation demonstration\n", "df_imputed_mean = df_random_missing.copy()\n", "df_imputed_linear = df_random_missing.copy()\n", "\n", "# 3.1 Mean Imputation\n", "df_imputed_mean[column_to_manipulate].fillna(df_imputed_mean[column_to_manipulate].mean(), inplace=True)\n", "print(f\"\\nDataFrame after mean imputation in '{column_to_manipulate}':\")\n", "print(df_imputed_mean[column_to_manipulate].isnull().sum(), \"missing values (should be 0)\")\n", "print(df_imputed_mean.head())\n", "\n", "# 3.2 Linear Interpolation\n", "# This is particularly useful for sequential data like time series or depth data.\n", "# The 'limit_direction' can be 'forward', 'backward', or 'both'.\n", "df_imputed_linear[column_to_manipulate].interpolate(method='linear', direction='both', inplace=True)\n", "print(f\"\\nDataFrame after linear interpolation in '{column_to_manipulate}':\")\n", "print(df_imputed_linear[column_to_manipulate].isnull().sum(), \"missing values (should be 0)\")\n", "print(df_imputed_linear.head())"]}, {"cell_type": "code", "execution_count": 10, "id": "e9d4c29a", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[10], line 15\u001b[0m\n\u001b[0;32m     12\u001b[0m y_baseline \u001b[38;5;241m=\u001b[39m df_baseline[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mGR\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m     14\u001b[0m \u001b[38;5;66;03m# Split data into training and testing sets\u001b[39;00m\n\u001b[1;32m---> 15\u001b[0m X_train_baseline, X_test_baseline, y_train_baseline, y_test_baseline \u001b[38;5;241m=\u001b[39m \u001b[43mtrain_test_split\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m     16\u001b[0m \u001b[43m    \u001b[49m\u001b[43mX_baseline\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_baseline\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtest_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0.2\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrandom_state\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m42\u001b[39;49m\n\u001b[0;32m     17\u001b[0m \u001b[43m)\u001b[49m\n\u001b[0;32m     19\u001b[0m \u001b[38;5;66;03m# Train Baseline Model\u001b[39;00m\n\u001b[0;32m     20\u001b[0m model_baseline \u001b[38;5;241m=\u001b[39m LinearRegression()\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\utils\\_param_validation.py:213\u001b[0m, in \u001b[0;36mvalidate_params.<locals>.decorator.<locals>.wrapper\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    207\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m    208\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m    209\u001b[0m         skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m    210\u001b[0m             prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m    211\u001b[0m         )\n\u001b[0;32m    212\u001b[0m     ):\n\u001b[1;32m--> 213\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    214\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m InvalidParameterError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    215\u001b[0m     \u001b[38;5;66;03m# When the function is just a wrapper around an estimator, we allow\u001b[39;00m\n\u001b[0;32m    216\u001b[0m     \u001b[38;5;66;03m# the function to delegate validation to the estimator, but we replace\u001b[39;00m\n\u001b[0;32m    217\u001b[0m     \u001b[38;5;66;03m# the name of the estimator by the name of the function in the error\u001b[39;00m\n\u001b[0;32m    218\u001b[0m     \u001b[38;5;66;03m# message to avoid confusion.\u001b[39;00m\n\u001b[0;32m    219\u001b[0m     msg \u001b[38;5;241m=\u001b[39m re\u001b[38;5;241m.\u001b[39msub(\n\u001b[0;32m    220\u001b[0m         \u001b[38;5;124mr\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mparameter of \u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mw+ must be\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    221\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mparameter of \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfunc\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__qualname__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m must be\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    222\u001b[0m         \u001b[38;5;28mstr\u001b[39m(e),\n\u001b[0;32m    223\u001b[0m     )\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\model_selection\\_split.py:2780\u001b[0m, in \u001b[0;36mtrain_test_split\u001b[1;34m(test_size, train_size, random_state, shuffle, stratify, *arrays)\u001b[0m\n\u001b[0;32m   2777\u001b[0m arrays \u001b[38;5;241m=\u001b[39m indexable(\u001b[38;5;241m*\u001b[39marrays)\n\u001b[0;32m   2779\u001b[0m n_samples \u001b[38;5;241m=\u001b[39m _num_samples(arrays[\u001b[38;5;241m0\u001b[39m])\n\u001b[1;32m-> 2780\u001b[0m n_train, n_test \u001b[38;5;241m=\u001b[39m \u001b[43m_validate_shuffle_split\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   2781\u001b[0m \u001b[43m    \u001b[49m\u001b[43mn_samples\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtest_size\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtrain_size\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdefault_test_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0.25\u001b[39;49m\n\u001b[0;32m   2782\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   2784\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m shuffle \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m:\n\u001b[0;32m   2785\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m stratify \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\model_selection\\_split.py:2410\u001b[0m, in \u001b[0;36m_validate_shuffle_split\u001b[1;34m(n_samples, test_size, train_size, default_test_size)\u001b[0m\n\u001b[0;32m   2407\u001b[0m n_train, n_test \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mint\u001b[39m(n_train), \u001b[38;5;28mint\u001b[39m(n_test)\n\u001b[0;32m   2409\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m n_train \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m-> 2410\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[0;32m   2411\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWith n_samples=\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m, test_size=\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m and train_size=\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m, the \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   2412\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresulting train set will be empty. Adjust any of the \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   2413\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124maforementioned parameters.\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(n_samples, test_size, train_size)\n\u001b[0;32m   2414\u001b[0m     )\n\u001b[0;32m   2416\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m n_train, n_test\n", "\u001b[1;31mValueError\u001b[0m: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters."]}], "source": ["# Make sure to handle any remaining NaNs in the original dataframe for the baseline\n", "# For simplicity, we'll drop rows with NaNs in the relevant columns for the baseline\n", "df_baseline = df.copy().dropna(subset=['RHOB', 'NPHI', 'GR'])\n", "\n", "# Ensure relevant columns are numeric, coercing errors to NaN and then dropping\n", "for col in ['RHOB', 'NPHI', 'GR']:\n", "    df_baseline[col] = pd.to_numeric(df_baseline[col], errors='coerce')\n", "df_baseline.dropna(subset=['RHOB', 'NPHI', 'GR'], inplace=True)\n", "\n", "# Prepare features (X) and target (y) for the baseline\n", "X_baseline = df_baseline[['RHOB', 'NPHI']]\n", "y_baseline = df_baseline['GR']\n", "\n", "# Split data into training and testing sets\n", "X_train_baseline, X_test_baseline, y_train_baseline, y_test_baseline = train_test_split(\n", "    X_baseline, y_baseline, test_size=0.2, random_state=42\n", ")\n", "\n", "# Train Baseline Model\n", "model_baseline = LinearRegression()\n", "model_baseline.fit(X_train_baseline, y_train_baseline)\n", "y_pred_baseline = model_baseline.predict(X_test_baseline)\n", "mse_baseline = mean_squared_error(y_test_baseline, y_pred_baseline)\n", "print(f\"\\n--- Baseline Model Performance ---\")\n", "print(f\"Mean Squared Error (Baseline): {mse_baseline:.4f}\")\n", "\n", "# --- Example of training and comparing with a manipulated dataset ---\n", "# Let's use df_imputed_mean (which had random missing data filled with mean imputation)\n", "df_manipulated_for_training = df_imputed_mean.copy()\n", "\n", "# Ensure relevant columns are numeric and handle any NaNs introduced by the original dataset or manipulation\n", "for col in ['RHOB', 'NPHI', 'GR']:\n", "    df_manipulated_for_training[col] = pd.to_numeric(df_manipulated_for_training[col], errors='coerce')\n", "df_manipulated_for_training.dropna(subset=['RHOB', 'NPHI', 'GR'], inplace=True)\n", "\n", "# Prepare features (X) and target (y) for the manipulated data\n", "X_manipulated = df_manipulated_for_training[['RHOB', 'NPHI']]\n", "y_manipulated = df_manipulated_for_training['GR']\n", "\n", "# Split data (using the same random state for comparability)\n", "X_train_manipulated, X_test_manipulated, y_train_manipulated, y_test_manipulated = train_test_split(\n", "    X_manipulated, y_manipulated, test_size=0.2, random_state=42\n", ")\n", "\n", "# Train Model on Manipulated Data\n", "model_manipulated = LinearRegression()\n", "model_manipulated.fit(X_train_manipulated, y_train_manipulated)\n", "y_pred_manipulated = model_manipulated.predict(X_test_manipulated)\n", "mse_manipulated = mean_squared_error(y_test_manipulated, y_pred_manipulated)\n", "print(f\"\\n--- Manipulated Model Performance (Random Removal + Mean Imputation) ---\")\n", "print(f\"Mean Squared Error (Manipulated Data): {mse_manipulated:.4f}\")\n", "\n", "# Recording the difference\n", "difference_mse = mse_manipulated - mse_baseline\n", "print(f\"\\nDifference in MSE (Manipulated - Baseline): {difference_mse:.4f}\")\n", "\n", "# You would repeat this process for each type of manipulation and interpolation\n", "# and store the results (e.g., in a dictionary or a new DataFrame) for comparison.\n", "\n", "results = {\n", "    'Baseline': {'MSE': mse_baseline},\n", "    'Random Removal + Mean Imputation': {'MSE': mse_manipulated}\n", "}\n", "\n", "print(\"\\nSummary of Results:\")\n", "for scenario, metrics in results.items():\n", "    print(f\"- {scenario}: MSE = {metrics['MSE']:.4f}\")\n", "\n", "# For more advanced recording, you might use libraries like MLflow or Weights & Biases\n", "# to log experiments, parameters, metrics, and even models automatically."]}, {"cell_type": "code", "execution_count": 12, "id": "9bbb49e2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Imputed missing values in 'RHOB' of baseline data with mean: 1.8886\n", "Imputed missing values in 'NPHI' of baseline data with mean: nan\n", "Imputed missing values in 'GR' of baseline data with mean: 71.6568\n", "\n", "--- Baseline DataFrame NaN check after imputation ---\n", "RHOB     0\n", "NPHI    50\n", "GR       0\n", "dtype: int64\n", "Shape of df_baseline after imputation: (50, 29)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11456\\540310107.py:77: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_baseline[col].fillna(mean_val, inplace=True)\n"]}, {"ename": "ValueError", "evalue": "Input X contains NaN.\nLinearRegression does not accept missing values encoded as NaN natively. For supervised learning, you might want to consider sklearn.ensemble.HistGradientBoostingClassifier and Regressor which accept missing values encoded as NaNs natively. Alternatively, it is possible to preprocess the data, for instance by using an imputer transformer in a pipeline or drop samples with missing values. See https://scikit-learn.org/stable/modules/impute.html You can find a list of all estimators that handle NaN values at the following page: https://scikit-learn.org/stable/modules/impute.html#estimators-that-handle-nan-values", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[12], line 97\u001b[0m\n\u001b[0;32m     95\u001b[0m \u001b[38;5;66;03m# Train Baseline Model\u001b[39;00m\n\u001b[0;32m     96\u001b[0m model_baseline \u001b[38;5;241m=\u001b[39m LinearRegression()\n\u001b[1;32m---> 97\u001b[0m \u001b[43mmodel_baseline\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX_train_baseline\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_train_baseline\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     98\u001b[0m y_pred_baseline \u001b[38;5;241m=\u001b[39m model_baseline\u001b[38;5;241m.\u001b[39mpredict(X_test_baseline)\n\u001b[0;32m     99\u001b[0m mse_baseline \u001b[38;5;241m=\u001b[39m mean_squared_error(y_test_baseline, y_pred_baseline)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\base.py:1473\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[1;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1466\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[0;32m   1468\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m   1469\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m   1470\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m   1471\u001b[0m     )\n\u001b[0;32m   1472\u001b[0m ):\n\u001b[1;32m-> 1473\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfit_method\u001b[49m\u001b[43m(\u001b[49m\u001b[43mestimator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\linear_model\\_base.py:609\u001b[0m, in \u001b[0;36mLinearRegression.fit\u001b[1;34m(self, X, y, sample_weight)\u001b[0m\n\u001b[0;32m    605\u001b[0m n_jobs_ \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_jobs\n\u001b[0;32m    607\u001b[0m accept_sparse \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpositive \u001b[38;5;28;01melse\u001b[39;00m [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcsr\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcsc\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcoo\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m--> 609\u001b[0m X, y \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_data\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    610\u001b[0m \u001b[43m    \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    611\u001b[0m \u001b[43m    \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    612\u001b[0m \u001b[43m    \u001b[49m\u001b[43maccept_sparse\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maccept_sparse\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    613\u001b[0m \u001b[43m    \u001b[49m\u001b[43my_numeric\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    614\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmulti_output\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    615\u001b[0m \u001b[43m    \u001b[49m\u001b[43mforce_writeable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    616\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    618\u001b[0m has_sw \u001b[38;5;241m=\u001b[39m sample_weight \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m    619\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m has_sw:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\base.py:650\u001b[0m, in \u001b[0;36mBaseEstimator._validate_data\u001b[1;34m(self, X, y, reset, validate_separately, cast_to_ndarray, **check_params)\u001b[0m\n\u001b[0;32m    648\u001b[0m         y \u001b[38;5;241m=\u001b[39m check_array(y, input_name\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124my\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mcheck_y_params)\n\u001b[0;32m    649\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 650\u001b[0m         X, y \u001b[38;5;241m=\u001b[39m \u001b[43mcheck_X_y\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mcheck_params\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    651\u001b[0m     out \u001b[38;5;241m=\u001b[39m X, y\n\u001b[0;32m    653\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m no_val_X \u001b[38;5;129;01mand\u001b[39;00m check_params\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mensure_2d\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mTrue\u001b[39;00m):\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\utils\\validation.py:1301\u001b[0m, in \u001b[0;36mcheck_X_y\u001b[1;34m(X, y, accept_sparse, accept_large_sparse, dtype, order, copy, force_writeable, force_all_finite, ensure_2d, allow_nd, multi_output, ensure_min_samples, ensure_min_features, y_numeric, estimator)\u001b[0m\n\u001b[0;32m   1296\u001b[0m         estimator_name \u001b[38;5;241m=\u001b[39m _check_estimator_name(estimator)\n\u001b[0;32m   1297\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[0;32m   1298\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mestimator_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m requires y to be passed, but the target y is None\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1299\u001b[0m     )\n\u001b[1;32m-> 1301\u001b[0m X \u001b[38;5;241m=\u001b[39m \u001b[43mcheck_array\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1302\u001b[0m \u001b[43m    \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1303\u001b[0m \u001b[43m    \u001b[49m\u001b[43maccept_sparse\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maccept_sparse\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1304\u001b[0m \u001b[43m    \u001b[49m\u001b[43maccept_large_sparse\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maccept_large_sparse\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1305\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1306\u001b[0m \u001b[43m    \u001b[49m\u001b[43morder\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43morder\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1307\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcopy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcopy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1308\u001b[0m \u001b[43m    \u001b[49m\u001b[43mforce_writeable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mforce_writeable\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1309\u001b[0m \u001b[43m    \u001b[49m\u001b[43mforce_all_finite\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mforce_all_finite\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1310\u001b[0m \u001b[43m    \u001b[49m\u001b[43mensure_2d\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mensure_2d\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1311\u001b[0m \u001b[43m    \u001b[49m\u001b[43mallow_nd\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mallow_nd\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1312\u001b[0m \u001b[43m    \u001b[49m\u001b[43mensure_min_samples\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mensure_min_samples\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1313\u001b[0m \u001b[43m    \u001b[49m\u001b[43mensure_min_features\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mensure_min_features\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1314\u001b[0m \u001b[43m    \u001b[49m\u001b[43mestimator\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mestimator\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1315\u001b[0m \u001b[43m    \u001b[49m\u001b[43minput_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mX\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1316\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1318\u001b[0m y \u001b[38;5;241m=\u001b[39m _check_y(y, multi_output\u001b[38;5;241m=\u001b[39mmulti_output, y_numeric\u001b[38;5;241m=\u001b[39my_numeric, estimator\u001b[38;5;241m=\u001b[39mestimator)\n\u001b[0;32m   1320\u001b[0m check_consistent_length(X, y)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\utils\\validation.py:1064\u001b[0m, in \u001b[0;36mcheck_array\u001b[1;34m(array, accept_sparse, accept_large_sparse, dtype, order, copy, force_writeable, force_all_finite, ensure_2d, allow_nd, ensure_min_samples, ensure_min_features, estimator, input_name)\u001b[0m\n\u001b[0;32m   1058\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[0;32m   1059\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFound array with dim \u001b[39m\u001b[38;5;132;01m%d\u001b[39;00m\u001b[38;5;124m. \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m expected <= 2.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1060\u001b[0m         \u001b[38;5;241m%\u001b[39m (array\u001b[38;5;241m.\u001b[39mndim, estimator_name)\n\u001b[0;32m   1061\u001b[0m     )\n\u001b[0;32m   1063\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m force_all_finite:\n\u001b[1;32m-> 1064\u001b[0m     \u001b[43m_assert_all_finite\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1065\u001b[0m \u001b[43m        \u001b[49m\u001b[43marray\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1066\u001b[0m \u001b[43m        \u001b[49m\u001b[43minput_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43minput_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1067\u001b[0m \u001b[43m        \u001b[49m\u001b[43mestimator_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mestimator_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1068\u001b[0m \u001b[43m        \u001b[49m\u001b[43mallow_nan\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mforce_all_finite\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m==\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mallow-nan\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1069\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1071\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m copy:\n\u001b[0;32m   1072\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m _is_numpy_namespace(xp):\n\u001b[0;32m   1073\u001b[0m         \u001b[38;5;66;03m# only make a copy if `array` and `array_orig` may share memory`\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\utils\\validation.py:123\u001b[0m, in \u001b[0;36m_assert_all_finite\u001b[1;34m(X, allow_nan, msg_dtype, estimator_name, input_name)\u001b[0m\n\u001b[0;32m    120\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m first_pass_isfinite:\n\u001b[0;32m    121\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m\n\u001b[1;32m--> 123\u001b[0m \u001b[43m_assert_all_finite_element_wise\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    124\u001b[0m \u001b[43m    \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    125\u001b[0m \u001b[43m    \u001b[49m\u001b[43mxp\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mxp\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    126\u001b[0m \u001b[43m    \u001b[49m\u001b[43mallow_nan\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mallow_nan\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    127\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmsg_dtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmsg_dtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    128\u001b[0m \u001b[43m    \u001b[49m\u001b[43mestimator_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mestimator_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    129\u001b[0m \u001b[43m    \u001b[49m\u001b[43minput_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43minput_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    130\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\utils\\validation.py:172\u001b[0m, in \u001b[0;36m_assert_all_finite_element_wise\u001b[1;34m(X, xp, allow_nan, msg_dtype, estimator_name, input_name)\u001b[0m\n\u001b[0;32m    155\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m estimator_name \u001b[38;5;129;01mand\u001b[39;00m input_name \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mX\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m has_nan_error:\n\u001b[0;32m    156\u001b[0m     \u001b[38;5;66;03m# Improve the error message on how to handle missing values in\u001b[39;00m\n\u001b[0;32m    157\u001b[0m     \u001b[38;5;66;03m# scikit-learn.\u001b[39;00m\n\u001b[0;32m    158\u001b[0m     msg_err \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m    159\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mestimator_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m does not accept missing values\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    160\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m encoded as NaN natively. For supervised learning, you might want\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    170\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m#estimators-that-handle-nan-values\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    171\u001b[0m     )\n\u001b[1;32m--> 172\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(msg_err)\n", "\u001b[1;31mValueError\u001b[0m: Input X contains NaN.\nLinearRegression does not accept missing values encoded as NaN natively. For supervised learning, you might want to consider sklearn.ensemble.HistGradientBoostingClassifier and Regressor which accept missing values encoded as NaNs natively. Alternatively, it is possible to preprocess the data, for instance by using an imputer transformer in a pipeline or drop samples with missing values. See https://scikit-learn.org/stable/modules/impute.html You can find a list of all estimators that handle NaN values at the following page: https://scikit-learn.org/stable/modules/impute.html#estimators-that-handle-nan-values"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from io import StringIO\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_squared_error\n", "\n", "# The content of the CSV file. In a real scenario, you would read this directly from the file.\n", "csv_content = \"\"\"WEL<PERSON>,DEPTH_MD,X_LOC,Y_LOC,Z_LOC,GROUP,FORMATION,CALI,RSHA,RMED,RDEP,RHOB,GR,SGR,NPHI,PEF,DTC,SP,BS,ROP,DTS,DCAL,DRHO,MUDWEIGHT,RMIC,ROPA,RXO,FORCE_2020_LITHOFACIES_LITHOLOGY,FORCE_2020_LITHOFACIES_CONFIDENCE\n", "15/9-13,494.528,437641.96875,6470972.5,-469.50183110000006,NORDLAND GP.,,19.480834961,,1.6114097834,1.7986813784,1.884185791,80.20085144,,,20.915468216,161.13117981,24.612379074,,34.63640976,,,-0.574927628,,,,,65000,1.0\n", "15/9-13,494.68,437641.96875,6470972.5,-469.6538086,NORDLAND GP.,,19.468799591,,1.6180702448,1.7956413031,1.8897935152,79.262886047,,,19.383012772,160.60346985,23.895530701,,34.63640976,,,-0.570188403,,,,,65000,1.0\n", "15/9-13,494.832,437641.96875,6470972.5,-469.80578610000003,NORDLAND GP.,,19.468799591,,1.6264585257,1.8007333279,1.8965227604,74.821998596,,,22.591518402,160.1736145,23.91635704,,34.779556274,,,-0.574245155,,,,,65000,1.0\n", "15/9-13,494.98400000000004,437641.96875,6470972.5,-469.95779419999997,NORDLAND GP.,,19.459281921,,1.6215940714,1.8015166521000001,1.8919128180000002,72.878921509,,,32.19190979,160.14942932,23.79368782,,39.965164185,,,-0.5863152739999999,,,,,65000,1.0\n", "15/9-13,495.13599999999997,437641.96875,6470972.5,-470.1097717,NORDLAND GP.,,19.453100204000002,,1.6026790141999998,1.7952990532,1.8800340891,71.729141235,,,38.495632172,160.12834167,24.104078293,,57.483764648000005,,,-0.597913623,,,,,65000,1.0\n", "15/9-13,495.288,437641.96875,6470972.5,-470.26177980000006,NORDLAND GP.,,19.453100204000002,,1.5855668783,1.8047186136000002,1.8796874285,72.01441955600001,,,43.657482146999996,160.14929199,23.931278229,,75.281410217,,,-0.6015999910000001,,,,,65000,1.0\n", "15/9-13,495.44,437641.96875,6470972.5,-470.4137878,NORDLAND GP.,,19.462495804,,1.5765693187999998,1.8054984808,1.8787307738999999,72.588088989,,,42.236221313,161.25038147,23.381790160999998,,76.199951172,,,-0.598368526,,,,,65000,1.0\n", "15/9-13,495.592,437641.96875,6470972.5,-470.56579589999996,NORDLAND GP.,,19.468799591,,1.5870107412,1.8083672522999998,1.8678369522,71.283050537,,,39.933563232,162.2144165,23.632165909,,76.199951172,,,-0.602039278,,,,,65000,1.0\n", "15/9-13,495.744,437641.96875,6470972.5,-470.7177734,NORDLAND GP.,,19.468799591,,1.613673687,1.8158134222,1.8472329378,69.721435547,,,39.163223267,161.57510376,22.163541794,,75.898796082,,,-0.6143639679999999,,,,,65000,1.0\n", "15/9-13,495.89599999999996,437641.96875,6470972.5,-470.86978150000004,NORDLAND GP.,,19.468799591,,1.6346218586,1.8139158487,1.836309433,66.677726746,,,37.802932739,160.58335876,23.659925461,,68.121261597,,,-0.621812582,,,,,65000,1.0\n", "15/9-13,496.048,437641.96875,6470972.5,-471.0217896000001,NORDLAND GP.,,19.468799591,,1.638895154,1.8114999533,1.8451297283,65.892799377,,,36.285533905,159.62438965,24.038724899,,47.22714233399999,,,-0.611830294,,,,,65000,1.0\n", "15/9-13,496.2,437641.96875,6470972.5,-471.1737671,NORDLAND GP.,,19.468799591,,1.6354175806,1.8178203106,1.8601442575,66.928260803,,,37.292488098,158.85522461,23.761310577,,46.828399658,,,-0.59656769,,,,,65000,1.0\n", "15/9-13,496.352,437641.96875,6470972.5,-471.32574460000006,NORDLAND GP.,,19.468799591,,1.6292560101,1.8143187761000001,1.8645105362,70.428024292,,,37.34254837,159.0859375,24.113840103,,46.828399658,,,-0.592989445,,,,,65000,1.0\n", "15/9-13,496.504,437641.96875,6470972.5,-471.4777527,NORDLAND GP.,,19.468799591,,1.6186336279,1.826174736,1.8604578971999999,72.568740845,,,38.716503143000004,159.11553955,24.287750244,,46.828399658,,,-0.597912192,,,,,65000,1.0\n", "15/9-13,496.656,437641.96875,6470972.5,-471.62976069999996,NORDLAND GP.,,19.459733962999998,,1.6183816194999998,1.8354929686,1.8589774369999998,71.932228088,,,33.712387085,159.13540649,24.305999756,,46.766792296999995,,,-0.6022745970000001,,,,,65000,1.0\n", "15/9-13,496.80800000000005,437641.96875,6470972.5,-471.7817688,NORDLAND GP.,,19.471090317,,1.6282503605,1.8268495798,1.8633086681,71.095474243,,,30.341299056999997,159.44454956,24.412090302,,46.560054779,,,-0.601545691,,,,,65000,1.0\n", "15/9-13,496.96,437641.96875,6470972.5,-471.93374630000005,NORDLAND GP.,,19.493326187,,1.6327999830000002,1.8267626762,1.879073143,74.254371643,,,29.63158989,159.09140015,23.796445846999998,,46.310504913,,,-0.593978047,,,,,65000,1.0\n", "15/9-13,497.11199999999997,437641.96875,6470972.5,-472.0857544,NORDLAND GP.,,19.5,,1.6200435162,1.8239495754,1.8897819518999999,77.266525269,,,34.332809448,159.35050964,23.575572968000003,,46.060894012,,,-0.592418253,,,,,65000,1.0\n", "15/9-13,497.264,437641.96875,6470972.5,-472.2377625,NORDLAND GP.,,19.5,,1.5970795155000002,1.8226443528999998,1.8950212002000002,77.243659973,,,44.43385696399999,159.91371155,23.661094666,,45.811397551999995,,,-0.5977608560000001,,,,,65000,1.0\n", "15/9-13,497.416,437641.96875,6470972.5,-472.38974,NORDLAND GP.,,19.5,,1.5808621644999998,1.8240714073,1.8818663359,74.072761536,,,58.937194823999995,160.68223572,23.785762787,,45.561885833999995,,,-0.616915762,,,,,65000,1.0\n", "15/9-13,497.56800000000004,437641.96875,6470972.5,-472.54174800000004,NORDLAND GP.,,19.5,,1.5833338499,1.8226944208000002,1.8718523979,72.198684692,,,54.921722412,161.35916138,24.250545501999998,,45.326885223000005,,,-0.627428412,,,,,65000,1.0\n", "15/9-13,497.72,437641.96875,6470972.5,-472.6937256,NORDLAND GP.,,19.5,,1.5998016596,1.8217409849000001,1.8767393827,73.68801116899999,,,47.026332855,161.16438293,24.519592285,,44.244506836000006,,,-0.6183999179999999,,,,,65000,1.0\n", "15/9-13,497.87199999999996,437641.96875,6470972.5,-472.8457336,NORDLAND GP.,,19.5,,1.6116827725999998,1.8185735940999999,1.8875800371,72.747123718,,,36.532077789,160.58572388,25.074958800999998,,40.593151093,,,-0.608738244,,,,,65000,1.0\n", "15/9-13,498.024,437641.96875,6470972.5,-472.99771119999997,NORDLAND GP.,,19.508638382,,1.6137462854,1.8126344681,1.9042459725999998,70.038673401,,,30.112548828,160.08589172,24.663700104,,39.007671355999996,,,-0.604946256,,,,,65000,1.0\n", "15/9-13,498.176,437641.96875,6470972.5,-473.1497192,NORDLAND GP.,,19.515600204000002,,1.6148980856,1.8170949221000001,1.9239485263999998,69.458602905,,,36.195671082,159.83093262,23.963632584000003,,38.924438476999995,,,-0.603397846,,,,,65000,1.0\n", "15/9-13,498.32800000000003,437641.96875,6470972.5,-473.3017273,NORDLAND GP.,,19.515600204000002,,1.6097832918000001,1.8280262946999999,1.9202179909000001,70.175437927,,,56.303665161000005,160.04148865,24.120071410999998,,38.841323853,,,-0.6068326829999999,,,,,65000,1.0\n", "15/9-13,498.48,437641.96875,6470972.5,-473.4537353999999,NORDLAND GP.,,19.515600204000002,,1.6039541959999999,1.8309628963,1.8905304669999998,69.169387817,,,66.030319214,160.02119446,23.873607635,,38.758102416999996,,,-0.615422189,,,,,65000,1.0\n", "15/9-13,498.63199999999995,437642.0,6470972.5,-473.60571289999996,NORDLAND GP.,,19.507123947,,1.6136058569,1.8337945938,1.8665241003,66.941886902,,,49.00489807100001,159.78358459,24.418914795,,38.673583984000004,,,-0.618839324,,,,,65000,1.0\n", "15/9-13,498.784,437642.0,6470972.5,-473.7577209,NORDLAND GP.,,19.5,,1.6289664507,1.8400899172,1.863617301,68.770942688,,,38.920906067,159.63774109,24.276592255,,38.589603424,,,-0.61290139,,,,,65000,1.0\n", "15/9-13,498.936,437642.0,6470972.5,-473.909729,NORDLAND GP.,,19.5,,1.6363601685,1.8386034966,1.8740327358000002,71.864768982,,,35.378421783,159.51885986,23.705533981,,37.036060333,,,-0.6110363010000001,,,,,65000,1.0\n", "15/9-13,499.088,437642.0,6470972.5,-474.0617371,NORDLAND GP.,,19.483297348,,1.6392525434,1.8370878696000001,1.8631432056000001,74.23991394,,,29.563484191999997,159.71707153,24.16906929,,32.157131195,,,-0.627915323,,,,,65000,1.0\n", "15/9-13,499.24,437642.0,6470972.5,-474.21371460000006,NORDLAND GP.,,19.485420227,,1.6395878791999998,1.8383673428999998,1.8512102365,73.419425964,,,26.189195633,159.78979492,24.029325485,,31.865451813000004,,,-0.643742979,,,,,65000,1.0\n", "15/9-13,499.392,437642.0,6470972.5,-474.3656921,NORDLAND GP.,,19.5,,1.6355726718999999,1.8410272598,1.8566273451,74.15774536100001,,,30.852775574000002,160.27708435,24.13826561,,31.865451813000004,,,-0.63950336,,,,,65000,1.0\n", "15/9-13,499.54400000000004,437642.0,6470972.5,-474.51770020000004,NORDLAND GP.,,19.5,,1.6230412722,1.8427449465,1.8640419245,74.61391449,,,36.393230438,160.46502686,22.978778839,,31.865451813000004,,,-0.631506503,,,,,65000,1.0\n", "15/9-13,499.69599999999997,437642.0,6470972.5,-474.6697083000001,NORDLAND GP.,,19.5,,1.6091556549000001,1.8346083163999998,1.8697222471000001,74.092033386,,,44.721645355,160.50489807,23.679580688,,31.865451813000004,,,-0.631422281,,,,,65000,1.0\n", "15/9-13,499.848,437642.0,6470972.5,-474.8216858,NORDLAND GP.,,19.5,,1.6014186144,1.8378143311000001,1.8720923662,73.944297791,,,43.747562408,160.49928284,24.130052566999996,,31.865451813000004,,,-0.635620296,,,,,65000,1.0\n", "15/9-13,500.0,437642.0,6470972.5,-474.97369380000004,NORDLAND GP.,,19.5,,1.6031726599,1.8436342478,1.8679596186,75.05583190899999,,,37.737670898000005,160.48558044,23.88341713,,31.739553452,,,-0.643759966,,,,,65000,1.0\n", "15/9-13,500.152,437642.0,6470972.5,-475.12570189999997,NORDLAND GP.,,19.5,,1.6104500294,1.8367676735,1.8572515249000001,76.508979797,,,31.725177765,160.36244202,23.805999756,,31.073074340999998,,,-0.652932346,,,,,65000,1.0\n", "15/9-13,500.30400000000003,437642.0,6470972.5,-475.27767939999995,NORDLAND GP.,,19.5,,1.6238434315,1.8315085172999999,1.845531106,73.464164734,,,26.771625519,159.83699036,23.978624344,,30.601942062,,,-0.659477293,,,,,65000,1.0\n", "15/9-13,500.45599999999996,437642.0,6470972.5,-475.4296875,NORDLAND GP.,,19.5,,1.6372792721,1.8285651207,1.8473213911000002,68.757133484,,,29.252349854000002,159.25857544,24.123197555999997,,30.155824661,,,-0.6578532460000001,,,,,65000,1.0\n", "15/9-13,500.608,437642.0,6470972.5,-475.58169560000005,NORDLAND GP.,,19.5,,1.640599966,1.8311219215,1.8530236483000002,66.214767456,,,31.11192894,159.04550171,24.24788475,,29.709711075,,,-0.6552764179999999,,,,,65000,1.0\n", "15/9-13,500.76,437642.0,6470972.5,-475.73367310000003,NORDLAND GP.,,19.476243973,,1.6307224034999999,1.8260848522,1.8832590580000002,67.519630432,,,25.780155181999998,159.09196472,23.884763718000002,,29.292463303,,,-0.652039766,,,,,65000,1.0\n", "15/9-13,500.912,437642.0,6470972.5,-475.8856506,NORDLAND GP.,,19.445240021,,1.6196386814,1.8227915763999998,1.9485155344,67.74028015100001,,,17.592674255,159.31990051,24.09079361,,28.97000885,,,-0.658081651,,,,,65000,1.0\n", "15/9-13,501.064,437642.0,6470972.5,-476.0376587,NORDLAND GP.,,19.4375,,1.5937899351,1.8256438971000002,2.0333251953,66.249382019,,,17.641107559,159.49562073,24.47168541,,28.769348145,,,-0.679025888,,,,,65000,1.0\n", "15/9-13,501.216,437642.0,6470972.5,-476.1896666999999,NORDLAND GP.,,19.421943665,,1.558780551,1.8266402483000002,2.1492137909,65.563743591,,,16.600845337,159.68937683,24.942251205,,28.568696976,,,-0.6539515260000001,,,,,65000,1.0\n", "15/9-13,501.36800000000005,437642.0,6470972.5,-476.34167479999996,NORDLAND GP.,,19.421772003,,1.5373376608000002,1.8281658887999999,2.1131362915,66.860717773,,,9.6992645264,159.92002869,25.633386612,,28.368062973,,,-0.594197512,,,,,65000,1.0\n", "15/9-13,501.52,437642.0,6470972.5,-476.4936523,NORDLAND GP.,,19.545425415,,1.5158455372,1.8295542002,1.9948540925999998,69.580696106,,,4.78414011,159.99858093,24.919124603,,28.167427063,,,-0.430847555,,,,,65000,1.0\n", "15/9-13,501.67199999999997,437642.0,6470972.5,-476.64566039999994,NORDLAND GP.,,19.702278137,,1.4943498373,1.8334907293,1.8836650847999998,71.94261169399999,,,3.8371455669,159.79779053,24.578119278000003,,27.96676445,,,-0.220775113,,,,,65000,1.0\n", "15/9-13,501.824,437642.0,6470972.5,-476.7976685,NORDLAND GP.,,20.016969681,,1.4729046822,1.835708499,1.7584255934,73.34449005100001,,,3.5728118419999997,159.55261230000002,24.555999756,,27.762060165,,,-0.09583643800000001,,,,,65000,1.0\n", "15/9-13,501.976,437642.0,647...\"\"\"\n", "\n", "df = pd.read_csv(StringIO(csv_content))\n", "\n", "# Make sure to handle any remaining NaNs in the original dataframe for the baseline\n", "df_baseline = df.copy()\n", "\n", "# Ensure relevant columns are numeric, coercing errors to NaN\n", "# This is crucial as 'NPHI' and potentially others might have non-numeric entries (like empty strings)\n", "for col in ['RHOB', 'NPHI', 'GR']:\n", "    df_baseline[col] = pd.to_numeric(df_baseline[col], errors='coerce')\n", "\n", "# --- Start of the fix ---\n", "# Impute missing values with the mean AFTER coercing to numeric\n", "# This ensures that we don't drop all rows if a column like 'NPHI' has many NaNs\n", "for col in ['RHOB', 'NPHI', 'GR']:\n", "    if df_baseline[col].isnull().any():\n", "        mean_val = df_baseline[col].mean()\n", "        df_baseline[col].fillna(mean_val, inplace=True)\n", "        print(f\"Imputed missing values in '{col}' of baseline data with mean: {mean_val:.4f}\")\n", "\n", "# Verify no NaNs remain in the relevant columns for the baseline before proceeding\n", "print(\"\\n--- Baseline DataFrame NaN check after imputation ---\")\n", "print(df_baseline[['RHOB', 'NPHI', 'GR']].isnull().sum())\n", "print(f\"Shape of df_baseline after imputation: {df_baseline.shape}\")\n", "# --- End of the fix ---\n", "\n", "# Prepare features (X) and target (y) for the baseline\n", "X_baseline = df_baseline[['RHOB', 'NPHI']]\n", "y_baseline = df_baseline['GR']\n", "\n", "# Split data into training and testing sets\n", "X_train_baseline, X_test_baseline, y_train_baseline, y_test_baseline = train_test_split(\n", "    X_baseline, y_baseline, test_size=0.2, random_state=42\n", ")\n", "\n", "# Train Baseline Model\n", "model_baseline = LinearRegression()\n", "model_baseline.fit(X_train_baseline, y_train_baseline)\n", "y_pred_baseline = model_baseline.predict(X_test_baseline)\n", "mse_baseline = mean_squared_error(y_test_baseline, y_pred_baseline)\n", "print(f\"\\n--- Baseline Model Performance ---\")\n", "print(f\"Mean Squared Error (Baseline): {mse_baseline:.4f}\")\n", "\n", "# --- Example of training and comparing with a manipulated dataset ---\n", "# Let's re-run the random missing data generation and mean imputation for df_imputed_mean\n", "# This ensures df_imputed_mean is properly set up for the next step\n", "\n", "# Select a numerical column for demonstration\n", "column_to_manipulate = 'RHOB' # Density\n", "\n", "# Create a copy to work with, so the original DataFrame remains untouched\n", "df_random_missing = df.copy()\n", "\n", "# Set 10% of 'RHOB' values to NaN randomly\n", "missing_percentage = 0.10\n", "n_missing = int(len(df_random_missing) * missing_percentage)\n", "\n", "# Get random indices to set to NaN\n", "random_indices = np.random.choice(df_random_missing.index, n_missing, replace=False)\n", "df_random_missing.loc[random_indices, column_to_manipulate] = np.nan\n", "\n", "# Now, apply mean imputation to df_random_missing to create df_imputed_mean\n", "df_imputed_mean = df_random_missing.copy()\n", "\n", "# Ensure relevant columns are numeric, coercing errors to NaN\n", "for col in ['RHOB', 'NPHI', 'GR']:\n", "    df_imputed_mean[col] = pd.to_numeric(df_imputed_mean[col], errors='coerce')\n", "\n", "# Impute missing values with the mean AFTER coercing to numeric\n", "for col in ['RHOB', 'NPHI', 'GR']:\n", "    if df_imputed_mean[col].isnull().any():\n", "        mean_val = df_imputed_mean[col].mean()\n", "        df_imputed_mean[col].fillna(mean_val, inplace=True)\n", "        print(f\"Imputed missing values in '{col}' of manipulated data with mean: {mean_val:.4f}\")\n", "\n", "df_manipulated_for_training = df_imputed_mean.copy()\n", "\n", "# Verify that there are no NaNs left in the target columns for manipulated data\n", "print(\"\\n--- Manipulated DataFrame NaN check after imputation ---\")\n", "print(df_manipulated_for_training[['RHOB', 'NPHI', 'GR']].isnull().sum())\n", "print(f\"Shape of df_manipulated_for_training after imputation: {df_manipulated_for_training.shape}\")\n", "\n", "# Prepare features (X) and target (y) for the manipulated data\n", "X_manipulated = df_manipulated_for_training[['RHOB', 'NPHI']]\n", "y_manipulated = df_manipulated_for_training['GR']\n", "\n", "# Split data (using the same random state for comparability)\n", "X_train_manipulated, X_test_manipulated, y_train_manipulated, y_test_manipulated = train_test_split(\n", "    X_manipulated, y_manipulated, test_size=0.2, random_state=42\n", ")\n", "\n", "# Train Model on Manipulated Data\n", "model_manipulated = LinearRegression()\n", "model_manipulated.fit(X_train_manipulated, y_train_manipulated)\n", "y_pred_manipulated = model_manipulated.predict(X_test_manipulated)\n", "mse_manipulated = mean_squared_error(y_test_manipulated, y_pred_manipulated)\n", "print(f\"\\n--- Manipulated Model Performance (Random Removal + Mean Imputation) ---\")\n", "print(f\"Mean Squared Error (Manipulated Data): {mse_manipulated:.4f}\")\n", "\n", "# Recording the difference\n", "difference_mse = mse_manipulated - mse_baseline\n", "print(f\"\\nDifference in MSE (Manipulated - Baseline): {difference_mse:.4f}\")\n", "\n", "results = {\n", "    'Baseline': {'MSE': mse_baseline},\n", "    'Random Removal + Mean Imputation': {'MSE': mse_manipulated}\n", "}\n", "\n", "print(\"\\nSummary of Results:\")\n", "for scenario, metrics in results.items():\n", "    print(f\"- {scenario}: MSE = {metrics['MSE']:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "1b21cbe3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "db6da3bf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a440985a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1e3be02e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "da33350a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7ca75a6c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5045db20", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "62f6a088", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4703ac6e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}