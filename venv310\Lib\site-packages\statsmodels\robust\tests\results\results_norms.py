from statsmodels.tools.testing import Holder

res_hampel = Holder(
    rho=[7.5, 6.833333333333333, 1.875, 0.5, 0.0, 0.5, 1.875,
         6.833333333333333, 7.5],
    psi=[0.0, -0.6666666666666666, -1.5, -1.0, 0.0, 1.0, 1.5,
         0.6666666666666666, 0.0],
    psi_deriv=[0.0, -0.3333333333333333, 0.0, 1.0, 1.0, 1.0, 0.0,
               -0.3333333333333333, 0.0],
    weights=[0.0, 0.1111111111111111, 0.75, 1.0, 1.0, 1.0, 0.75,
             0.1111111111111111, 0.0],
    )

res_biweight = Holder(
    rho=[2.6666666666666665, 2.6666666666666665, 1.5416666666666665,
         0.46940104166666663, 0.0, 0.46940104166666663, 1.5416666666666665,
         2.6666666666666665, 2.6666666666666665],
    psi=[0.0, 0.0, -1.125, -0.87890625, 0.0, 0.87890625, 1.125, 0.0, 0.0],
    psi_deriv=[0.0, 0.0, -0.1875, 0.64453125, 1.0, 0.64453125, -0.1875, 0.0,
               0.0],
    weights=[0.0, 0.0, 0.5625, 0.87890625, 1.0, 0.87890625, 0.5625, 0.0, 0.0],
    )

res_huber = Holder(
    rho=[11.200487500000001, 7.165487499999999, 1.7854875000000001, 0.5, 0.0,
         0.5, 1.7854875000000001, 7.165487499999999, 11.200487500000001],
    psi=[-1.345, -1.345, -1.345, -1.0, 0.0, 1.0, 1.345, 1.345, 1.345],
    psi_deriv=[0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0],
    weights=[0.14944444444444444, 0.22416666666666665, 0.6725, 1.0, 1.0, 1.0,
             0.6725, 0.22416666666666665, 0.14944444444444444],
    )
