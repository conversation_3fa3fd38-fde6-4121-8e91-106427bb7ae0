..\..\..\Scripts\htmlmin-script.py
..\..\..\Scripts\htmlmin.exe
..\htmlmin\__init__.py
..\htmlmin\__pycache__\__init__.cpython-310.pyc
..\htmlmin\__pycache__\command.cpython-310.pyc
..\htmlmin\__pycache__\decorator.cpython-310.pyc
..\htmlmin\__pycache__\escape.cpython-310.pyc
..\htmlmin\__pycache__\main.cpython-310.pyc
..\htmlmin\__pycache__\middleware.cpython-310.pyc
..\htmlmin\__pycache__\parser.cpython-310.pyc
..\htmlmin\command.py
..\htmlmin\decorator.py
..\htmlmin\escape.py
..\htmlmin\main.py
..\htmlmin\middleware.py
..\htmlmin\parser.py
..\htmlmin\python3html\__init__.py
..\htmlmin\python3html\__pycache__\__init__.cpython-310.pyc
..\htmlmin\python3html\__pycache__\parser.cpython-310.pyc
..\htmlmin\python3html\parser.py
PKG-INFO
SOURCES.txt
dependency_links.txt
entry_points.txt
top_level.txt
zip-safe
