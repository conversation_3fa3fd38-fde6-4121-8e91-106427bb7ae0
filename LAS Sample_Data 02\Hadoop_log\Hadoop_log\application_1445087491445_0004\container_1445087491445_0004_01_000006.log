2015-10-17 21:24:23,882 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 21:24:24,257 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 21:24:24,257 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 21:24:24,366 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 21:24:24,382 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445087491445_0004, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@54e0a229)
2015-10-17 21:24:24,835 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 21:24:27,304 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445087491445_0004
2015-10-17 21:24:30,413 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 21:24:32,413 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 21:24:32,554 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@3c28dd37
2015-10-17 21:24:34,116 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:134217728+134217728
2015-10-17 21:24:34,335 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 21:24:34,335 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 21:24:34,335 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 21:24:34,335 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 21:24:34,335 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 21:24:34,382 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 21:24:41,101 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:24:41,101 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34175485; bufvoid = 104857600
2015-10-17 21:24:41,101 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13786752(55147008); length = 12427645/6553600
2015-10-17 21:24:41,101 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44661238 kvi 11165304(44661216)
2015-10-17 21:25:13,023 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 21:25:13,116 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44661238 kv 11165304(44661216) kvi 8543876(34175504)
2015-10-17 21:25:17,351 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:25:17,351 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44661238; bufend = 78838272; bufvoid = 104857600
2015-10-17 21:25:17,351 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11165304(44661216); kvend = 24952452(99809808); length = 12427253/6553600
2015-10-17 21:25:17,351 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89324032 kvi 22331004(89324016)
2015-10-17 21:25:47,273 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 21:25:47,366 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 89324032 kv 22331004(89324016) kvi 19709572(78838288)
2015-10-17 21:25:51,429 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:25:51,429 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 89324032; bufend = 18640650; bufvoid = 104857596
2015-10-17 21:25:51,445 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 22331004(89324016); kvend = 9903044(39612176); length = 12427961/6553600
2015-10-17 21:25:51,445 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29126405 kvi 7281596(29126384)
2015-10-17 21:26:19,351 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 21:26:19,429 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29126405 kv 7281596(29126384) kvi 4660168(18640672)
2015-10-17 21:26:23,320 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:26:23,320 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29126405; bufend = 63302164; bufvoid = 104857600
2015-10-17 21:26:23,320 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7281596(29126384); kvend = 21068424(84273696); length = 12427573/6553600
2015-10-17 21:26:23,320 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 73787922 kvi 18446976(73787904)
2015-10-17 21:26:52,632 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 21:26:52,679 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 73787922 kv 18446976(73787904) kvi 15825548(63302192)
2015-10-17 21:26:56,820 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:26:56,820 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 73787922; bufend = 3104422; bufvoid = 104857600
2015-10-17 21:26:56,820 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 18446976(73787904); kvend = 6018988(24075952); length = 12427989/6553600
2015-10-17 21:26:56,820 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 13590179 kvi 3397540(13590160)
2015-10-17 21:27:25,008 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 21:27:25,133 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 13590179 kv 3397540(13590160) kvi 776112(3104448)
2015-10-17 21:27:29,008 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:27:29,008 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 13590179; bufend = 47765124; bufvoid = 104857600
2015-10-17 21:27:29,008 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 3397540(13590160); kvend = 17184160(68736640); length = 12427781/6553600
2015-10-17 21:27:29,008 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 58250874 kvi 14562712(58250848)
2015-10-17 21:27:31,320 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-17 21:27:57,773 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-17 21:27:57,961 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 58250874 kv 14562712(58250848) kvi 12518956(50075824)
2015-10-17 21:27:57,961 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:27:57,961 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 58250874; bufend = 63873325; bufvoid = 104857600
2015-10-17 21:27:57,961 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14562712(58250848); kvend = 12518960(50075840); length = 2043753/6553600
2015-10-17 21:28:01,461 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-17 21:28:01,508 INFO [main] org.apache.hadoop.mapred.Merger: Merging 7 sorted segments
2015-10-17 21:28:01,976 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 7 segments left of total size: 228401589 bytes
2015-10-17 21:29:36,979 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445087491445_0004_m_000002_0 is done. And is in the process of committing
2015-10-17 21:29:37,838 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445087491445_0004_m_000002_0' done.
2015-10-17 21:29:37,947 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Stopping MapTask metrics system...
2015-10-17 21:29:37,947 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system stopped.
2015-10-17 21:29:37,947 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system shutdown complete.
