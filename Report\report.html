<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ONGC: Powering India's Energy Future - Infographic</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #E1F4FC;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 320px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
        .kpi-card {
            background-color: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .kpi-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.1);
        }
        .section-title {
            font-size: 2.25rem;
            font-weight: 900;
            color: #003F5C;
            text-align: center;
            margin-bottom: 1rem;
        }
        .section-subtitle {
            font-size: 1.125rem;
            color: #366E8A;
            text-align: center;
            max-width: 800px;
            margin: 0 auto 3rem auto;
        }
        .flowchart-arrow {
            font-size: 2rem;
            color: #6A9DBB3;
            font-weight: bold;
        }
    </style>
</head>
<body class="text-gray-800">

    <header class="bg-[#003F5C] text-white text-center py-12 px-4">
        <h1 class="text-4xl md:text-6xl font-black tracking-tight">ONGC: Powering India's Energy Future</h1>
        <p class="mt-4 text-lg md:text-xl text-[#A2D6E8] max-w-3xl mx-auto">An interactive overview of the nation's energy anchor, from deep-sea exploration to its strategic role in a transitioning energy landscape.</p>
        <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            <div class="bg-[#366E8A] p-6 rounded-lg shadow-lg">
                <p class="text-5xl font-bold text-white">70%</p>
                <p class="mt-2 text-[#E1F4FC]">of India's Domestic Crude Oil Production</p>
            </div>
            <div class="bg-[#366E8A] p-6 rounded-lg shadow-lg">
                <p class="text-5xl font-bold text-white">84%</p>
                <p class="mt-2 text-[#E1F4FC]">of India's Domestic Natural Gas Production</p>
            </div>
        </div>
    </header>

    <main class="container mx-auto p-4 md:p-8">

        <section id="snapshot" class="my-12">
            <h2 class="section-title">Corporate Snapshot</h2>
            <p class="section-subtitle">ONGC's massive scale is reflected in its key financial and operational metrics, underscoring its pivotal role in the Indian economy.</p>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                <div class="kpi-card text-center">
                    <p class="text-4xl font-bold text-[#003F5C]">₹6.63L Cr</p>
                    <p class="text-gray-600 mt-2">Consolidated Revenue (FY25)</p>
                </div>
                <div class="kpi-card text-center">
                    <p class="text-4xl font-bold text-[#003F5C]">₹38,329 Cr</p>
                    <p class="text-gray-600 mt-2">Consolidated Net Profit (FY25)</p>
                </div>
                <div class="kpi-card text-center">
                    <p class="text-4xl font-bold text-[#003F5C]">₹3.1L Cr</p>
                    <p class="text-gray-600 mt-2">Market Cap (July 2025)</p>
                </div>
                <div class="kpi-card text-center">
                    <p class="text-4xl font-bold text-[#003F5C]">25,847</p>
                    <p class="text-gray-600 mt-2">Dedicated Professionals (2024)</p>
                </div>
            </div>
        </section>

        <section id="operations" class="my-16">
            <h2 class="section-title">The Engine of India: Operations Deep Dive</h2>
            <p class="section-subtitle">From scientific exploration to consumer delivery, ONGC's integrated value chain is a marvel of engineering and logistics. This operational excellence is marked by record-breaking drilling activity.</p>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-2xl font-bold text-center text-[#003F5C] mb-4">The Hydrocarbon Value Chain</h3>
                    <div class="flex flex-col md:flex-row justify-around items-center text-center gap-4">
                        <div class="flex-1">
                            <div class="p-4 bg-[#E1F4FC] rounded-lg">
                                <p class="text-xl font-bold text-[#003F5C]">Upstream</p>
                                <p class="text-sm text-[#366E8A]">Exploration & Production</p>
                            </div>
                        </div>
                        <div class="flowchart-arrow transform md:rotate-0 rotate-90">&rarr;</div>
                        <div class="flex-1">
                            <div class="p-4 bg-[#A2D6E8] rounded-lg">
                                <p class="text-xl font-bold text-[#003F5C]">Midstream</p>
                                <p class="text-sm text-[#366E8A]">Transport & Storage</p>
                            </div>
                        </div>
                        <div class="flowchart-arrow transform md:rotate-0 rotate-90">&rarr;</div>
                        <div class="flex-1">
                             <div class="p-4 bg-[#6A9DB3] rounded-lg">
                                <p class="text-xl font-bold text-[#003F5C]">Downstream</p>
                                <p class="text-sm text-[#366E8A]">Refining & Retail</p>
                            </div>
                        </div>
                    </div>
                    <p class="mt-4 text-center text-gray-600">ONGC's core is Upstream, but its subsidiaries like HPCL and MRPL provide a formidable Downstream presence, creating a resilient, integrated energy major.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-2xl font-bold text-center text-[#003F5C] mb-4">Record Drilling Performance</h3>
                    <div class="chart-container">
                        <canvas id="drillingChart"></canvas>
                    </div>
                    <p class="mt-4 text-center text-gray-600">ONGC achieved a new record by drilling 578 wells in FY25, the highest in over three decades, showcasing remarkable operational efficiency and a commitment to replenishing reserves.</p>
                </div>
            </div>
        </section>

        <section id="financials" class="my-16">
            <h2 class="section-title">Financial Pulse</h2>
            <p class="section-subtitle">ONGC's financial health reflects the dynamic global energy market. While profits are sensitive to crude prices, its integrated model shows that the core upstream business remains the primary profit driver.</p>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-2xl font-bold text-center text-[#003F5C] mb-4">Consolidated Net Profit Trend (in ₹ Crore)</h3>
                    <div class="chart-container">
                        <canvas id="profitTrendChart"></canvas>
                    </div>
                     <p class="mt-4 text-center text-gray-600">Profitability has been volatile, peaking in FY24 due to favorable prices and moderating in FY25 as crude realizations and capital expenditures shifted.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-2xl font-bold text-center text-[#003F5C] mb-4">Profit Engine: Segment Contribution (Q1 FY25)</h3>
                     <div class="chart-container">
                        <canvas id="segmentProfitChart"></canvas>
                    </div>
                    <p class="mt-4 text-center text-gray-600">Despite downstream operations generating most of the revenue, the upstream Exploration & Production (E&P) segment delivers over 80% of the group's profit.</p>
                </div>
            </div>
        </section>
        
        <section id="strategy" class="my-16">
            <h2 class="section-title">Navigating the Future: A Dual-Engine Strategy</h2>
            <p class="section-subtitle">ONGC is fortifying its core hydrocarbon business through strategic partnerships while aggressively building a new growth engine in clean energy to align with India's climate goals.</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-2xl font-bold text-center text-[#003F5C] mb-4">Strengthening the Core Business</h3>
                    <div class="space-y-4">
                        <div class="border-l-4 border-[#366E8A] pl-4">
                            <h4 class="font-bold text-lg">Collaboration with bp</h4>
                            <p class="text-gray-600">Partnering to explore high-risk, high-reward frontier basins, leveraging bp's deepwater expertise to de-risk exploration and unlock future reserves.</p>
                        </div>
                        <div class="border-l-4 border-[#6A9DB3] pl-4">
                            <h4 class="font-bold text-lg">Partnership with Vedanta</h4>
                            <p class="text-gray-600">A pragmatic alliance to monetize "stranded" gas assets in Assam by sharing infrastructure, accelerating development, and reducing capital expenditure.</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <h3 class="text-2xl font-bold text-[#003F5C] mb-4">Clean Energy Transition</h3>
                    <p class="text-6xl font-black text-[#003F5C]">10 GW</p>
                    <p class="text-xl font-semibold text-[#366E8A] mb-4">Renewable Capacity Target by 2030</p>
                    <div class="chart-container mx-auto" style="height: 150px; max-width: 150px;">
                        <canvas id="renewableProgressChart"></canvas>
                    </div>
                    <p class="mt-4 text-gray-600">Through strategic acquisitions, ONGC has already secured 2.345 GW of renewable capacity, fast-tracking its journey towards the 2030 goal.</p>
                </div>
            </div>
        </section>

        <section id="context" class="my-16">
            <h2 class="section-title">India's Energy Landscape</h2>
            <p class="section-subtitle">ONGC operates within one of the world's fastest-growing energy markets, defined by soaring demand, high import dependency, and an ambitious national pivot to clean energy.</p>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                 <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-2xl font-bold text-center text-[#003F5C] mb-4">Installed Power Capacity Mix (June 2025)</h3>
                    <div class="chart-container">
                        <canvas id="powerMixChart"></canvas>
                    </div>
                    <p class="mt-4 text-center text-gray-600">India is rapidly approaching its goal of 50% non-fossil fuel capacity, with renewables like solar and wind leading the charge in new capacity additions.</p>
                </div>
                <div class="kpi-card text-center bg-[#003F5C] text-white">
                    <p class="text-6xl font-black">89.1%</p>
                    <p class="text-xl text-[#A2D6E8] mt-2">Crude Oil Import Dependency (FY25)</p>
                    <p class="mt-4 text-[#E1F4FC]">This stark figure highlights the critical strategic importance of ONGC's mission to maximize domestic production and enhance India's energy security.</p>
                </div>
            </div>
        </section>
        
        <section id="swot" class="my-16">
            <h2 class="section-title">SWOT Analysis</h2>
            <p class="section-subtitle">A strategic look at ONGC's internal strengths and weaknesses versus its external opportunities and threats in the evolving energy sector.</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white p-6 rounded-lg shadow-md border-t-4 border-green-500">
                    <h3 class="font-bold text-xl text-green-700 mb-2">Strengths</h3>
                    <ul class="list-disc list-inside space-y-1 text-gray-700">
                        <li>Dominant market position in India</li>
                        <li>Vast reserve base and infrastructure</li>
                        <li>'Maharatna' status providing autonomy</li>
                        <li>Integrated operations hedging price risk</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md border-t-4 border-red-500">
                    <h3 class="font-bold text-xl text-red-700 mb-2">Weaknesses</h3>
                    <ul class="list-disc list-inside space-y-1 text-gray-700">
                        <li>High vulnerability to global oil prices</li>
                        <li>Production decline from mature fields</li>
                        <li>Potential bureaucratic hurdles (PSU structure)</li>
                        <li>High operational cost structure</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md border-t-4 border-blue-500">
                    <h3 class="font-bold text-xl text-blue-700 mb-2">Opportunities</h3>
                    <ul class="list-disc list-inside space-y-1 text-gray-700">
                        <li>Surging domestic energy demand</li>
                        <li>Government push for new exploration</li>
                        <li>Strategic partnerships to unlock resources</li>
                        <li>Diversification into high-growth renewables</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md border-t-4 border-yellow-500">
                    <h3 class="font-bold text-xl text-yellow-700 mb-2">Threats</h3>
                    <ul class="list-disc list-inside space-y-1 text-gray-700">
                        <li>Global pressure for decarbonization</li>
                        <li>Increasing competition from private players</li>
                        <li>Geopolitical instability impacting overseas assets</li>
                        <li>Regulatory and policy changes</li>
                    </ul>
                </div>
            </div>
        </section>

    </main>

    <footer class="bg-[#003F5C] text-center text-white p-6 mt-12">
        <p>Infographic created based on the "In-Depth Analysis of ONGC" report.</p>
        <p class="text-sm text-[#A2D6E8]">Data sourced for FY2025 and as of July 2025 where applicable.</p>
    </footer>

    <script>
        const brilliantBlues = {
            dark: '#003F5C',
            mediumDark: '#366E8A',
            medium: '#6A9DB3',
            light: '#A2D6E8',
            extraLight: '#E1F4FC'
        };

        function wrapLabels(label, maxLength) {
            if (typeof label !== 'string' || label.length <= maxLength) {
                return label;
            }
            const words = label.split(' ');
            const lines = [];
            let currentLine = '';
            for (const word of words) {
                if ((currentLine + word).length > maxLength) {
                    lines.push(currentLine.trim());
                    currentLine = '';
                }
                currentLine += word + ' ';
            }
            lines.push(currentLine.trim());
            return lines;
        }
        
        const tooltipTitleCallback = {
            plugins: {
                tooltip: {
                    callbacks: {
                        title: function(tooltipItems) {
                            const item = tooltipItems[0];
                            let label = item.chart.data.labels[item.dataIndex];
                            if (Array.isArray(label)) {
                              return label.join(' ');
                            }
                            return label;
                        }
                    }
                }
            }
        };

        new Chart(document.getElementById('drillingChart'), {
            type: 'bar',
            data: {
                labels: ['FY24', 'FY25'],
                datasets: [{
                    label: 'Wells Drilled',
                    data: [541, 578],
                    backgroundColor: [brilliantBlues.medium, brilliantBlues.dark],
                    borderColor: [brilliantBlues.dark, brilliantBlues.dark],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: tooltipTitleCallback.plugins.tooltip
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: { color: brilliantBlues.extraLight }
                    },
                    x: {
                        grid: { display: false }
                    }
                }
            }
        });

        new Chart(document.getElementById('profitTrendChart'), {
            type: 'line',
            data: {
                labels: ['FY2023', 'FY2024', 'FY2025'],
                datasets: [{
                    label: 'Consolidated Net Profit (₹ Cr)',
                    data: [34046, 57101, 38329],
                    fill: true,
                    backgroundColor: 'rgba(54, 110, 138, 0.2)',
                    borderColor: brilliantBlues.dark,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: tooltipTitleCallback.plugins.tooltip
                },
                scales: {
                    y: { grid: { color: brilliantBlues.extraLight } },
                    x: { grid: { display: false } }
                }
            }
        });

        new Chart(document.getElementById('segmentProfitChart'), {
            type: 'doughnut',
            data: {
                labels: [
                    wrapLabels('E&P (Upstream)', 16), 
                    wrapLabels('Refining & Marketing', 16), 
                    wrapLabels('Outside India (E&P)', 16)
                ],
                datasets: [{
                    label: 'Profit Contribution',
                    data: [82.8, 9.6, 7.6],
                    backgroundColor: [brilliantBlues.dark, brilliantBlues.medium, brilliantBlues.light],
                    hoverOffset: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'bottom' },
                    tooltip: tooltipTitleCallback.plugins.tooltip
                }
            }
        });
        
        new Chart(document.getElementById('renewableProgressChart'), {
            type: 'doughnut',
            data: {
                labels: ['Acquired Capacity (GW)', 'Remaining Target (GW)'],
                datasets: [{
                    data: [2.345, 10 - 2.345],
                    backgroundColor: [brilliantBlues.dark, brilliantBlues.extraLight],
                    borderColor: [brilliantBlues.dark, brilliantBlues.light],
                    borderWidth: 1,
                    hoverOffset: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: { display: false },
                    tooltip: tooltipTitleCallback.plugins.tooltip
                }
            }
        });

        new Chart(document.getElementById('powerMixChart'), {
            type: 'bar',
            data: {
                labels: ['Installed Capacity (GW)'],
                datasets: [
                    {
                        label: 'Coal',
                        data: [219.3],
                        backgroundColor: '#4B5563',
                    },
                    {
                        label: 'Solar',
                        data: [110.9],
                        backgroundColor: '#FBBF24',
                    },
                    {
                        label: 'Wind',
                        data: [51.3],
                        backgroundColor: '#3B82F6',
                    },
                    {
                        label: 'Large Hydro',
                        data: [48.0],
                        backgroundColor: '#0EA5E9',
                    },
                    {
                        label: 'Other Fossil',
                        data: [20.7],
                        backgroundColor: '#6B7280',
                    },
                    {
                        label: 'Other Renewables & Nuclear',
                        data: [25.5],
                        backgroundColor: '#10B981',
                    }
                ]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { stacked: true, grid: { color: brilliantBlues.extraLight } },
                    y: { stacked: true, grid: { display: false } }
                },
                plugins: {
                    legend: { position: 'bottom' },
                    tooltip: {
                        ...tooltipTitleCallback.plugins.tooltip,
                        callbacks: {
                            ...tooltipTitleCallback.plugins.tooltip.callbacks,
                            title: () => '' 
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
