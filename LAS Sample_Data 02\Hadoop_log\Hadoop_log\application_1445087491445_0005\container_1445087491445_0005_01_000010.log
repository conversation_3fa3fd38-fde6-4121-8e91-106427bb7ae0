2015-10-17 21:49:17,751 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 21:49:17,969 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 21:49:17,969 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 21:49:18,016 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 21:49:18,016 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445087491445_0005, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@54e0a229)
2015-10-17 21:49:18,376 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 21:49:19,672 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445087491445_0005
2015-10-17 21:49:22,126 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 21:49:24,704 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 21:49:24,954 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@49a29f92
2015-10-17 21:49:32,141 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:1342177280+134217728
2015-10-17 21:49:32,392 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 21:49:32,392 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 21:49:32,392 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 21:49:32,392 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 21:49:32,392 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 21:49:32,438 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 21:49:40,782 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:49:40,782 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34174413; bufvoid = 104857600
2015-10-17 21:49:40,782 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13786484(55145936); length = 12427913/6553600
2015-10-17 21:49:40,782 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44660166 kvi 11165036(44660144)
2015-10-17 21:50:10,002 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 21:50:10,158 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44660166 kv 11165036(44660144) kvi 8543608(34174432)
2015-10-17 21:50:14,205 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:50:14,205 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44660166; bufend = 78839764; bufvoid = 104857600
2015-10-17 21:50:14,205 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11165036(44660144); kvend = 24952820(99811280); length = 12426617/6553600
2015-10-17 21:50:14,205 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89325514 kvi 22331372(89325488)
2015-10-17 21:50:47,847 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 21:50:48,097 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 89325514 kv 22331372(89325488) kvi 19709948(78839792)
2015-10-17 21:50:52,269 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:50:52,269 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 89325514; bufend = 18644327; bufvoid = 104857600
2015-10-17 21:50:52,269 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 22331372(89325488); kvend = 9903964(39615856); length = 12427409/6553600
2015-10-17 21:50:52,269 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29130083 kvi 7282516(29130064)
