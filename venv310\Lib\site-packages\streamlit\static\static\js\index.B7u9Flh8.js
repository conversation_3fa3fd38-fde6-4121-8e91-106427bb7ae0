import{s as w,br as T,ct as O,r as s,aP as _,z as q,cu as H,C as G,j as u,bH as K,bs as $,b9 as N,bt as J}from"./index.BTGIlECR.js";import{u as M}from"./uniqueId.BW6icrs1.js";import{u as Q,a as X,b as Y}from"./useOnInputChange.CBaDeGVQ.js";import{I as Z}from"./InputInstructions.7NdlV9ze.js";import{a as tt}from"./useBasicWidgetState.oPxte5Mj.js";import{u as et,T as it}from"./useTextInputAutoExpand.s7ZXp9QC.js";import"./inputUtils.CptNuJwn.js";import"./FormClearHelper.DuzI-rET.js";import"./base-input.Dl5fJ2xw.js";const at=w("div",{target:"e1r0q00f0"})({height:"100%",display:"flex",flexDirection:"column"}),st=(e,t)=>{let a="auto";if(e.heightConfig?.useStretch)a="100%";else if(e.heightConfig?.pixelHeight&&e.heightConfig.pixelHeight>0){const o=T(t.labelVisibility?.value)===O.Collapsed?2:30;a=`${e.heightConfig.pixelHeight-o}px`}return a},S=(e,t)=>e.getStringValue(t)??t.default??null,ot=e=>e.default??null,rt=e=>e.value??null,lt=(e,t,a,o)=>{t.setStringValue(e,a.value,{fromUi:a.fromUi},o)},nt=({disabled:e,element:t,widgetMgr:a,fragmentId:o,outerElement:d})=>{const m=s.useRef(M("text_area_")).current,[W,y]=_(),[r,c]=s.useState(!1),[I,b]=s.useState(!1),l=d.heightConfig?.useContent??!1,V=d.heightConfig?.useStretch??!1,v=st(d,t),x=s.useRef(null),[n,h]=s.useState(()=>S(a,t)??null),A=s.useCallback(()=>{h(t.default??null),c(!0)},[t]),[F,g]=tt({getStateFromWidgetMgr:S,getDefaultStateFromProto:ot,getCurrStateFromProto:rt,updateWidgetMgrState:lt,element:t,widgetMgr:a,fragmentId:o,onFormCleared:A});Q(F,n,h,r);const i=q(),{height:R,maxHeight:z,updateScrollHeight:C}=et({textareaRef:x,dependencies:[t.placeholder]}),p=s.useCallback(()=>{c(!1),g({value:n,fromUi:!0})},[n,g]),P=s.useCallback(()=>{r&&p(),b(!1)},[r,p]),k=s.useCallback(()=>{b(!0)},[]),E=s.useCallback(()=>{l&&C()},[l,C]),L=X({formId:t.formId,maxChars:t.maxChars,setDirty:c,setUiValue:h,setValueWithSource:g,additionalAction:E}),U=Y(t.formId,p,r,a,o,!0),{placeholder:D,formId:f}=t,B=H({formId:f})?a.allowFormEnterToSubmit(f):r,j=I&&W>i.breakpoints.hideWidgetDetails;return G(at,{className:"stTextArea","data-testid":"stTextArea",ref:y,children:[u(J,{label:t.label,disabled:e,labelVisibility:T(t.labelVisibility?.value),htmlFor:m,children:t.help&&u(K,{children:u($,{content:t.help,placement:N.TOP_RIGHT})})}),u(it,{inputRef:l?x:void 0,value:n??"",placeholder:D,onBlur:P,onFocus:k,onChange:L,onKeyDown:U,"aria-label":t.label,disabled:e,id:m,overrides:{Input:{style:{fontWeight:i.fontWeights.normal,lineHeight:i.lineHeights.inputWidget,height:l?R:v,maxHeight:l?z:"",minHeight:i.sizes.largestElementHeight,resize:V?"none":"vertical",paddingRight:i.spacing.md,paddingLeft:i.spacing.md,paddingBottom:i.spacing.md,paddingTop:i.spacing.md,"::placeholder":{color:i.colors.fadedText60}}},Root:{props:{"data-testid":"stTextAreaRootElement"},style:{borderLeftWidth:i.sizes.borderWidth,borderRightWidth:i.sizes.borderWidth,borderTopWidth:i.sizes.borderWidth,borderBottomWidth:i.sizes.borderWidth,flexGrow:1}}}}),j&&u(Z,{dirty:r,value:n??"",maxLength:t.maxChars,type:"multiline",inForm:H({formId:f}),allowEnterToSubmit:B})]})},xt=s.memo(nt);export{xt as default};
