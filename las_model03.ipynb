{"cells": [{"cell_type": "code", "execution_count": 1, "id": "46b0d827", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import numpy as np\n", "\n", "from sklearn.linear_model import LinearRegression"]}, {"cell_type": "code", "execution_count": 3, "id": "ee11058f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20192\\4063967944.py:1: DtypeWarning: Columns (6,10) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/CSV Data/to_be_deletion.csv\")\n"]}], "source": ["df = pd.read_csv(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/CSV Data/to_be_deletion.csv\")"]}, {"cell_type": "code", "execution_count": 7, "id": "72397e6d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 728 entries, 0 to 727\n", "Data columns (total 4 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   DEPTH   728 non-null    float64\n", " 1   CKHL    557 non-null    float64\n", " 2   CPOR    593 non-null    float64\n", " 3   CGD     594 non-null    float64\n", "dtypes: float64(4)\n", "memory usage: 22.9 KB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 4, "id": "c6938863", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Index: 80831 entries, 4238 to 133123\n", "Data columns (total 12 columns):\n", " #   Column     Non-Null Count  Dtype  \n", "---  ------     --------------  -----  \n", " 0   WELL       80831 non-null  object \n", " 1   DEPTH_MD   80831 non-null  float64\n", " 2   GROUP      80831 non-null  object \n", " 3   FORMATION  80831 non-null  object \n", " 4   CALI       80831 non-null  float64\n", " 5   RDEP       80831 non-null  float64\n", " 6   RHOB       80831 non-null  object \n", " 7   GR         80831 non-null  float64\n", " 8   NPHI       80831 non-null  float64\n", " 9   PEF        80831 non-null  float64\n", " 10  DTC        80831 non-null  object \n", " 11  LITH       80831 non-null  object \n", "dtypes: float64(6), object(6)\n", "memory usage: 8.0+ MB\n"]}], "source": ["df.dropna(inplace=True)\n", "df.info()"]}, {"cell_type": "code", "execution_count": 12, "id": "78414beb", "metadata": {}, "outputs": [], "source": ["# p = sns.scatterplot(x=df['CPOR'], y=df['CKHL'],hue=df['CGD'], palette='YlOrRd', s=50)\n", "# p.set(yscale='log')\n", "# p.set_ylabel(\"Core Permeability (mD)\", fontsize=12, fontweight='bold')\n", "# p.set_xlabel(\"Core Porosity (%)\", fontsize=12, fontweight='bold')"]}, {"cell_type": "code", "execution_count": 5, "id": "10fbca45", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'CPOR'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3805\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3804\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3805\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3806\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON>Erro<PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mindex.pyx:167\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mindex.pyx:196\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7081\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7089\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: 'CPOR'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[5], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m p \u001b[38;5;241m=\u001b[39m sns\u001b[38;5;241m.\u001b[39mscatterplot(x\u001b[38;5;241m=\u001b[39m\u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mCPOR\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m, y\u001b[38;5;241m=\u001b[39mdf[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mCKHL\u001b[39m\u001b[38;5;124m'\u001b[39m],hue\u001b[38;5;241m=\u001b[39mdf[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mCGD\u001b[39m\u001b[38;5;124m'\u001b[39m], palette\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mYlOrRd\u001b[39m\u001b[38;5;124m'\u001b[39m, s\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m50\u001b[39m)\n\u001b[0;32m      2\u001b[0m p\u001b[38;5;241m.\u001b[39mset(yscale\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlog\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m      3\u001b[0m p\u001b[38;5;241m.\u001b[39mset_ylabel(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCore Permeability (mD)\u001b[39m\u001b[38;5;124m\"\u001b[39m, fontsize\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m12\u001b[39m, fontweight\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbold\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\frame.py:4102\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   4100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   4101\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 4102\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   4103\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   4104\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3812\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3807\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   3808\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[0;32m   3809\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[0;32m   3810\u001b[0m     ):\n\u001b[0;32m   3811\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[1;32m-> 3812\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3813\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3814\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3815\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3816\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3817\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: 'CPOR'"]}], "source": ["p = sns.scatterplot(x=df['CPOR'], y=df['CKHL'],hue=df['CGD'], palette='YlOrRd', s=50)\n", "p.set(yscale='log')\n", "p.set_ylabel(\"Core Permeability (mD)\", fontsize=12, fontweight='bold')\n", "p.set_xlabel(\"Core Porosity (%)\", fontsize=12, fontweight='bold')\n", "# plt.colorbar(p)"]}, {"cell_type": "code", "execution_count": null, "id": "ffd8cc92", "metadata": {}, "outputs": [], "source": ["# x = df['CPOR'].values\n", "# y = df['CKHL'].values"]}, {"cell_type": "code", "execution_count": 22, "id": "94cb1640", "metadata": {}, "outputs": [], "source": ["x = df['CPOR'].values\n", "y = np.log10(df['CKHL'].values)"]}, {"cell_type": "code", "execution_count": 23, "id": "66adc84c", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 1.06069784,  1.33041377, -0.15864053,  2.69108149,  2.40312052,\n", "        3.03342376,  2.61489722,  1.2121876 , -0.7235382 ,  0.72916479,\n", "        2.7458552 , -1.03621217,  2.37657696,  2.18184359,  2.46686762,\n", "        1.5899496 ,  2.2764618 ,  1.75587486,  1.64048144, -0.17069623,\n", "        1.08278537,  2.1931246 ,  2.08635983,  2.07188201,  2.04921802,\n", "        2.03342376,  1.88422877,  1.81822589,  1.78958071,  1.95664858,\n", "        1.92220628,  2.09342169,  2.20139712,  2.07918125,  2.04139269,\n", "        1.41497335, -0.13966199,  1.17026172,  1.1172713 ,  2.10037055,\n", "        2.10720997,  1.99694925,  2.15228834,  2.09342169,  1.69897   ,\n", "        1.51188336,  1.72672721,  1.67302091,  1.70070372,  1.66931688,\n", "        1.5865873 ,  1.55266822,  1.96941591,  2.02530587,  2.05690485,\n", "        2.32014629,  1.26007139,  0.50514998, -0.04575749, -1.69897   ,\n", "       -1.85387196, -1.92081875,  1.25527251,  1.40654018, -0.81815641,\n", "       -1.92081875, -2.        , -0.41566878,  1.23044892,  1.51851394,\n", "        1.10037055,  2.29003461,  2.35983548,  1.8488047 ,  1.96614173,\n", "        0.68930886,  1.24797327,  1.48995848,  1.93094903,  1.54530712,\n", "        1.2380461 ,  4.31175386,  4.30319606,  3.3783979 ,  3.70671778,\n", "        3.56348109,  3.78532984,  3.8299467 ,  3.68304704,  3.33845649,\n", "        3.79726754,  3.68214508,  3.17026172,  3.72181062,  3.84385542,\n", "        3.80002936,  3.96142109,  3.99166901,  3.90902085,  3.93449845,\n", "        3.85369821,  3.9132839 ,  3.92324402,  4.02530587,  4.06445799,\n", "        4.03342376,  4.06445799,  4.11058971,  4.06069784,  3.80277373,\n", "        3.81291336,  3.54157924,  3.59328607,  3.39445168,  3.22271647,\n", "        3.4456042 ,  2.42813479,  2.10720997,  2.55022835,  1.13033377,\n", "        0.1271048 , -0.67985371, -0.52578374, -1.52287875,  1.92168648,\n", "        2.51587384,  2.34830486,  2.25527251,  2.04139269,  1.8998205 ,\n", "        0.61595005,  0.94349452,  0.58433122, -0.41453927, -0.32790214,\n", "        1.63948649,  2.04532298,  1.83884909,  1.72263392,  1.76641285,\n", "        1.72345567,  1.8344207 ,  1.84323278,  1.81756537,  1.92737036,\n", "        1.74818803,  1.71096312,  1.68930886,  1.74193908,  1.78031731,\n", "        1.96473092,  1.83695674,  1.78390358,  2.48144263,  2.68033551,\n", "        2.51054501,  2.58319877,  2.60422605,  1.70156799,  2.06818586,\n", "        2.29885308,  2.30319606,  2.18752072,  2.27415785,  2.20411998,\n", "        1.99033885,  1.54654266,  2.2121876 ,  1.92427929,  2.24303805,\n", "        2.12057393,  1.8739016 ,  2.29885308,  2.20682588,  1.92427929,\n", "        0.1271048 ,  1.03342376,  0.6946052 ,  1.88817949,  1.8260748 ,\n", "        2.2121876 ,  2.02530587,  2.22271647,  1.6180481 ,  2.04532298,\n", "        2.07188201, -0.03857891,  0.9360108 ,  0.35024802,  1.80071708,\n", "        1.87909588,  1.80888587,  1.96378783,  1.9360108 ,  1.98000337,\n", "        1.5390761 ,  1.96941591,  1.95664858,  1.76937733,  1.73319727,\n", "        1.87448182,  1.7355989 ,  1.80685803,  1.62838893,  2.16731733,\n", "        1.9127533 ,  3.18184359,  3.17897695,  3.17609126,  3.40993312,\n", "        3.19589965,  3.02938378,  3.06445799,  3.42975228,  3.08635983,\n", "        2.64542227,  3.10720997,  3.15836249,  2.6364879 ,  2.45024911,\n", "        2.34044411,  2.39967372,  2.8344207 ,  2.34830486,  2.67486114,\n", "        2.69108149,  2.50514998,  3.05690485,  2.84633711,  3.02938378,\n", "        2.18752072,  2.29225607,  2.36548798,  2.78532984,  2.15533604,\n", "        1.78816837,  2.56229286,  3.29446623, -0.20551195,  1.66181269,\n", "        2.30319606,  2.2121876 ,  2.17026172,  2.20139712,  2.15533604,\n", "        2.28780173,  2.31806333,  2.3783979 ,  2.30103   ,  2.34830486,\n", "        1.9498777 ,  1.90955603,  2.40140054,  2.38201704,  2.47567119,\n", "        2.38738983,  2.29885308,  2.30535137,  2.38021124,  1.83186977,\n", "        1.7458552 ,  1.5575072 ,  1.71850169,  1.87679498,  1.95568775,\n", "        2.06445799,  2.09691001,  2.45024911,  2.35602586,  2.33845649,\n", "        2.21748394,  2.34830486,  1.63447727,  1.53529412,  1.76342799,\n", "        1.74507479,  1.30103   ,  1.77232171,  0.82151353,  0.00860017,\n", "        0.12385164,  0.39619935,  0.0374265 , -1.52287875, -0.13489603,\n", "       -0.07262964,  0.42651126,  0.01703334, -0.61261017, -1.22914799,\n", "       -0.09691001, -1.11350927, -0.36251027,  0.8585372 , -0.70333481,\n", "        0.69635639,  0.56820172,  1.59988307,  0.94546859,  1.15228834,\n", "       -0.75202673, -0.11350927, -0.19997064, -0.71219827,  0.35602586,\n", "       -0.08460016, -0.12901119, -0.60205999,  0.10380372,  1.15836249,\n", "       -0.0209071 , -0.75202673, -0.08884239, -0.33913452,  0.86923172,\n", "       -0.7235382 , -0.89619628, -0.70553377, -0.59516628, -0.838632  ,\n", "       -0.43533394,  1.34439227, -1.58502665,  0.82282165,  1.39619935,\n", "        1.95760729, -0.74714697,  0.70329138,  1.77742682,  1.95375969,\n", "        0.55388303,  1.64246452,  1.55509445,  1.53529412,  0.93399316,\n", "       -0.04866248, -0.3419886 , -0.57511836,  1.93500315,  2.08635983,\n", "       -0.2644011 , -0.28483264, -0.70553377, -0.60906489,  0.58883173,\n", "        0.73078228,  0.71432976, -0.09420412,  0.85551916,  0.24303805,\n", "       -0.70774393,  0.30749604,  0.76789762, -0.61439373, -0.22914799,\n", "        0.26481782, -0.62525165, -0.88605665,  0.36548798,  0.36361198,\n", "       -0.67366414, -0.82681373,  1.88024178,  2.24054925,  1.81756537,\n", "        1.94051648,  0.98542647,  2.34830486,  2.50105926,  2.56348109,\n", "        2.07554696,  1.74818803, -1.09151498, -0.75945075, -0.70774393,\n", "        0.26951294,  3.07554696,  2.79795964,  1.82930377,  0.72263392,\n", "        2.18752072,  2.75511227,  1.7126497 ,  2.77011529,  1.44870632,\n", "        0.47275645,  2.34635297,  2.11394335,  2.5171959 ,  2.90417437,\n", "        2.95424251,  2.87157294,  2.66651798,  2.54530712,  2.80550086,\n", "        2.72672721,  1.71600334, -0.70553377,  0.36735592,  2.87794695,\n", "        1.77451697,  0.79028516,  1.75434834,  3.17897695,  2.12057393,\n", "        2.32633586,  0.54530712, -0.8569852 , -1.02687215, -1.01322827,\n", "        1.48287358,  1.77742682,  0.1172713 ,  2.12057393,  1.49692965,\n", "        0.87098881,  1.59549622,  0.81090428,  2.04139269, -1.00436481,\n", "       -0.46724562, -1.11918641, -0.69464863,  0.02530587,  0.21484385,\n", "        1.12057393,  1.44715803,  1.48000694, -0.63264408,  0.20139712,\n", "        2.75281643,  1.4345689 ,  2.71096312, -1.        , -1.32790214,\n", "        0.67302091,  0.24551267,  0.93399316,  2.97680834,  2.95856388,\n", "        1.76192784,  1.83758844,  0.85491302, -1.4202164 , -1.65757732,\n", "       -1.67778071,  1.40654018,  1.92427929,  0.06818586,  0.35983548,\n", "        0.09691001, -0.17198494,  0.53275438,  0.47712125, -0.23507702,\n", "        2.16435286,  1.60314437,  1.11058971,  0.46834733,  1.42324587,\n", "        2.08990511,  2.26481782,  2.17609126,  2.07188201,  2.28780173,\n", "        2.30749604,  2.33845649, -1.18045606, -1.45593196, -1.4202164 ,\n", "        1.87506126,  3.1271048 ,  0.5865873 , -1.13667714, -0.92445304,\n", "        2.44870632,  0.94939001, -0.06248211,  1.70586371,  0.32014629,\n", "       -0.08407279,  1.68394713,  1.94792362,  2.70243054, -1.38721614,\n", "       -0.06148027,  0.8591383 ,  0.24551267,  1.97312785,  1.81023252,\n", "        0.91539984, -0.84771166, -0.61261017, -0.41566878,  0.87273883,\n", "       -0.52287875,  2.32428246,  1.82347423,  0.49968708, -0.11350927,\n", "        0.24303805,  0.2278867 ,  0.40140054, -0.04527521,  0.07188201,\n", "       -0.63264408,  0.54282543,  0.2278867 , -1.20760831,  0.7466342 ,\n", "        1.31806333,  1.48855072,  0.8350561 ,  0.66181269,  0.79588002,\n", "        0.6180481 , -0.08618615,  1.64345268,  0.99122608,  0.51851394,\n", "       -0.04191415, -1.40893539, -1.61978876, -1.55284197,  3.        ,\n", "        3.33645973, -0.28903688, -0.09097915,  1.31175386,  3.28330123,\n", "        2.95808585, -1.21467016, -1.27572413,  2.13672057,  0.13672057,\n", "       -0.73282827,  0.97451169,  0.39445168,  1.51054501,  0.73957234,\n", "        0.53275438,  1.55990663,  0.06069784, -0.01144104, -0.36754271,\n", "       -0.66154351,  0.84757266, -0.61978876, -0.53313238, -0.52724355,\n", "        1.35983548,  0.83122969,  3.68574174,  3.52504481,  3.54282543,\n", "        2.2121876 ,  2.90579588])"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["y"]}, {"cell_type": "code", "execution_count": 30, "id": "e04af1bf", "metadata": {}, "outputs": [], "source": ["x = x.reshape(-1,1)\n", "y = y.reshape(-1,1)"]}, {"cell_type": "code", "execution_count": 31, "id": "db003c53", "metadata": {}, "outputs": [{"data": {"text/plain": ["(557, 1)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["y.shape"]}, {"cell_type": "code", "execution_count": 32, "id": "687bebe5", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 1.06069784],\n", "       [ 1.33041377],\n", "       [-0.15864053],\n", "       [ 2.69108149],\n", "       [ 2.40312052],\n", "       [ 3.03342376],\n", "       [ 2.61489722],\n", "       [ 1.2121876 ],\n", "       [-0.7235382 ],\n", "       [ 0.72916479],\n", "       [ 2.7458552 ],\n", "       [-1.03621217],\n", "       [ 2.37657696],\n", "       [ 2.18184359],\n", "       [ 2.46686762],\n", "       [ 1.5899496 ],\n", "       [ 2.2764618 ],\n", "       [ 1.75587486],\n", "       [ 1.64048144],\n", "       [-0.17069623],\n", "       [ 1.08278537],\n", "       [ 2.1931246 ],\n", "       [ 2.08635983],\n", "       [ 2.07188201],\n", "       [ 2.04921802],\n", "       [ 2.03342376],\n", "       [ 1.88422877],\n", "       [ 1.81822589],\n", "       [ 1.78958071],\n", "       [ 1.95664858],\n", "       [ 1.92220628],\n", "       [ 2.09342169],\n", "       [ 2.20139712],\n", "       [ 2.07918125],\n", "       [ 2.04139269],\n", "       [ 1.41497335],\n", "       [-0.13966199],\n", "       [ 1.17026172],\n", "       [ 1.1172713 ],\n", "       [ 2.10037055],\n", "       [ 2.10720997],\n", "       [ 1.99694925],\n", "       [ 2.15228834],\n", "       [ 2.09342169],\n", "       [ 1.69897   ],\n", "       [ 1.51188336],\n", "       [ 1.72672721],\n", "       [ 1.67302091],\n", "       [ 1.70070372],\n", "       [ 1.66931688],\n", "       [ 1.5865873 ],\n", "       [ 1.55266822],\n", "       [ 1.96941591],\n", "       [ 2.02530587],\n", "       [ 2.05690485],\n", "       [ 2.32014629],\n", "       [ 1.26007139],\n", "       [ 0.50514998],\n", "       [-0.04575749],\n", "       [-1.69897   ],\n", "       [-1.85387196],\n", "       [-1.92081875],\n", "       [ 1.25527251],\n", "       [ 1.40654018],\n", "       [-0.81815641],\n", "       [-1.92081875],\n", "       [-2.        ],\n", "       [-0.41566878],\n", "       [ 1.23044892],\n", "       [ 1.51851394],\n", "       [ 1.10037055],\n", "       [ 2.29003461],\n", "       [ 2.35983548],\n", "       [ 1.8488047 ],\n", "       [ 1.96614173],\n", "       [ 0.68930886],\n", "       [ 1.24797327],\n", "       [ 1.48995848],\n", "       [ 1.93094903],\n", "       [ 1.54530712],\n", "       [ 1.2380461 ],\n", "       [ 4.31175386],\n", "       [ 4.30319606],\n", "       [ 3.3783979 ],\n", "       [ 3.70671778],\n", "       [ 3.56348109],\n", "       [ 3.78532984],\n", "       [ 3.8299467 ],\n", "       [ 3.68304704],\n", "       [ 3.33845649],\n", "       [ 3.79726754],\n", "       [ 3.68214508],\n", "       [ 3.17026172],\n", "       [ 3.72181062],\n", "       [ 3.84385542],\n", "       [ 3.80002936],\n", "       [ 3.96142109],\n", "       [ 3.99166901],\n", "       [ 3.90902085],\n", "       [ 3.93449845],\n", "       [ 3.85369821],\n", "       [ 3.9132839 ],\n", "       [ 3.92324402],\n", "       [ 4.02530587],\n", "       [ 4.06445799],\n", "       [ 4.03342376],\n", "       [ 4.06445799],\n", "       [ 4.11058971],\n", "       [ 4.06069784],\n", "       [ 3.80277373],\n", "       [ 3.81291336],\n", "       [ 3.54157924],\n", "       [ 3.59328607],\n", "       [ 3.39445168],\n", "       [ 3.22271647],\n", "       [ 3.4456042 ],\n", "       [ 2.42813479],\n", "       [ 2.10720997],\n", "       [ 2.55022835],\n", "       [ 1.13033377],\n", "       [ 0.1271048 ],\n", "       [-0.67985371],\n", "       [-0.52578374],\n", "       [-1.52287875],\n", "       [ 1.92168648],\n", "       [ 2.51587384],\n", "       [ 2.34830486],\n", "       [ 2.25527251],\n", "       [ 2.04139269],\n", "       [ 1.8998205 ],\n", "       [ 0.61595005],\n", "       [ 0.94349452],\n", "       [ 0.58433122],\n", "       [-0.41453927],\n", "       [-0.32790214],\n", "       [ 1.63948649],\n", "       [ 2.04532298],\n", "       [ 1.83884909],\n", "       [ 1.72263392],\n", "       [ 1.76641285],\n", "       [ 1.72345567],\n", "       [ 1.8344207 ],\n", "       [ 1.84323278],\n", "       [ 1.81756537],\n", "       [ 1.92737036],\n", "       [ 1.74818803],\n", "       [ 1.71096312],\n", "       [ 1.68930886],\n", "       [ 1.74193908],\n", "       [ 1.78031731],\n", "       [ 1.96473092],\n", "       [ 1.83695674],\n", "       [ 1.78390358],\n", "       [ 2.48144263],\n", "       [ 2.68033551],\n", "       [ 2.51054501],\n", "       [ 2.58319877],\n", "       [ 2.60422605],\n", "       [ 1.70156799],\n", "       [ 2.06818586],\n", "       [ 2.29885308],\n", "       [ 2.30319606],\n", "       [ 2.18752072],\n", "       [ 2.27415785],\n", "       [ 2.20411998],\n", "       [ 1.99033885],\n", "       [ 1.54654266],\n", "       [ 2.2121876 ],\n", "       [ 1.92427929],\n", "       [ 2.24303805],\n", "       [ 2.12057393],\n", "       [ 1.8739016 ],\n", "       [ 2.29885308],\n", "       [ 2.20682588],\n", "       [ 1.92427929],\n", "       [ 0.1271048 ],\n", "       [ 1.03342376],\n", "       [ 0.6946052 ],\n", "       [ 1.88817949],\n", "       [ 1.8260748 ],\n", "       [ 2.2121876 ],\n", "       [ 2.02530587],\n", "       [ 2.22271647],\n", "       [ 1.6180481 ],\n", "       [ 2.04532298],\n", "       [ 2.07188201],\n", "       [-0.03857891],\n", "       [ 0.9360108 ],\n", "       [ 0.35024802],\n", "       [ 1.80071708],\n", "       [ 1.87909588],\n", "       [ 1.80888587],\n", "       [ 1.96378783],\n", "       [ 1.9360108 ],\n", "       [ 1.98000337],\n", "       [ 1.5390761 ],\n", "       [ 1.96941591],\n", "       [ 1.95664858],\n", "       [ 1.76937733],\n", "       [ 1.73319727],\n", "       [ 1.87448182],\n", "       [ 1.7355989 ],\n", "       [ 1.80685803],\n", "       [ 1.62838893],\n", "       [ 2.16731733],\n", "       [ 1.9127533 ],\n", "       [ 3.18184359],\n", "       [ 3.17897695],\n", "       [ 3.17609126],\n", "       [ 3.40993312],\n", "       [ 3.19589965],\n", "       [ 3.02938378],\n", "       [ 3.06445799],\n", "       [ 3.42975228],\n", "       [ 3.08635983],\n", "       [ 2.64542227],\n", "       [ 3.10720997],\n", "       [ 3.15836249],\n", "       [ 2.6364879 ],\n", "       [ 2.45024911],\n", "       [ 2.34044411],\n", "       [ 2.39967372],\n", "       [ 2.8344207 ],\n", "       [ 2.34830486],\n", "       [ 2.67486114],\n", "       [ 2.69108149],\n", "       [ 2.50514998],\n", "       [ 3.05690485],\n", "       [ 2.84633711],\n", "       [ 3.02938378],\n", "       [ 2.18752072],\n", "       [ 2.29225607],\n", "       [ 2.36548798],\n", "       [ 2.78532984],\n", "       [ 2.15533604],\n", "       [ 1.78816837],\n", "       [ 2.56229286],\n", "       [ 3.29446623],\n", "       [-0.20551195],\n", "       [ 1.66181269],\n", "       [ 2.30319606],\n", "       [ 2.2121876 ],\n", "       [ 2.17026172],\n", "       [ 2.20139712],\n", "       [ 2.15533604],\n", "       [ 2.28780173],\n", "       [ 2.31806333],\n", "       [ 2.3783979 ],\n", "       [ 2.30103   ],\n", "       [ 2.34830486],\n", "       [ 1.9498777 ],\n", "       [ 1.90955603],\n", "       [ 2.40140054],\n", "       [ 2.38201704],\n", "       [ 2.47567119],\n", "       [ 2.38738983],\n", "       [ 2.29885308],\n", "       [ 2.30535137],\n", "       [ 2.38021124],\n", "       [ 1.83186977],\n", "       [ 1.7458552 ],\n", "       [ 1.5575072 ],\n", "       [ 1.71850169],\n", "       [ 1.87679498],\n", "       [ 1.95568775],\n", "       [ 2.06445799],\n", "       [ 2.09691001],\n", "       [ 2.45024911],\n", "       [ 2.35602586],\n", "       [ 2.33845649],\n", "       [ 2.21748394],\n", "       [ 2.34830486],\n", "       [ 1.63447727],\n", "       [ 1.53529412],\n", "       [ 1.76342799],\n", "       [ 1.74507479],\n", "       [ 1.30103   ],\n", "       [ 1.77232171],\n", "       [ 0.82151353],\n", "       [ 0.00860017],\n", "       [ 0.12385164],\n", "       [ 0.39619935],\n", "       [ 0.0374265 ],\n", "       [-1.52287875],\n", "       [-0.13489603],\n", "       [-0.07262964],\n", "       [ 0.42651126],\n", "       [ 0.01703334],\n", "       [-0.61261017],\n", "       [-1.22914799],\n", "       [-0.09691001],\n", "       [-1.11350927],\n", "       [-0.36251027],\n", "       [ 0.8585372 ],\n", "       [-0.70333481],\n", "       [ 0.69635639],\n", "       [ 0.56820172],\n", "       [ 1.59988307],\n", "       [ 0.94546859],\n", "       [ 1.15228834],\n", "       [-0.75202673],\n", "       [-0.11350927],\n", "       [-0.19997064],\n", "       [-0.71219827],\n", "       [ 0.35602586],\n", "       [-0.08460016],\n", "       [-0.12901119],\n", "       [-0.60205999],\n", "       [ 0.10380372],\n", "       [ 1.15836249],\n", "       [-0.0209071 ],\n", "       [-0.75202673],\n", "       [-0.08884239],\n", "       [-0.33913452],\n", "       [ 0.86923172],\n", "       [-0.7235382 ],\n", "       [-0.89619628],\n", "       [-0.70553377],\n", "       [-0.59516628],\n", "       [-0.838632  ],\n", "       [-0.43533394],\n", "       [ 1.34439227],\n", "       [-1.58502665],\n", "       [ 0.82282165],\n", "       [ 1.39619935],\n", "       [ 1.95760729],\n", "       [-0.74714697],\n", "       [ 0.70329138],\n", "       [ 1.77742682],\n", "       [ 1.95375969],\n", "       [ 0.55388303],\n", "       [ 1.64246452],\n", "       [ 1.55509445],\n", "       [ 1.53529412],\n", "       [ 0.93399316],\n", "       [-0.04866248],\n", "       [-0.3419886 ],\n", "       [-0.57511836],\n", "       [ 1.93500315],\n", "       [ 2.08635983],\n", "       [-0.2644011 ],\n", "       [-0.28483264],\n", "       [-0.70553377],\n", "       [-0.60906489],\n", "       [ 0.58883173],\n", "       [ 0.73078228],\n", "       [ 0.71432976],\n", "       [-0.09420412],\n", "       [ 0.85551916],\n", "       [ 0.24303805],\n", "       [-0.70774393],\n", "       [ 0.30749604],\n", "       [ 0.76789762],\n", "       [-0.61439373],\n", "       [-0.22914799],\n", "       [ 0.26481782],\n", "       [-0.62525165],\n", "       [-0.88605665],\n", "       [ 0.36548798],\n", "       [ 0.36361198],\n", "       [-0.67366414],\n", "       [-0.82681373],\n", "       [ 1.88024178],\n", "       [ 2.24054925],\n", "       [ 1.81756537],\n", "       [ 1.94051648],\n", "       [ 0.98542647],\n", "       [ 2.34830486],\n", "       [ 2.50105926],\n", "       [ 2.56348109],\n", "       [ 2.07554696],\n", "       [ 1.74818803],\n", "       [-1.09151498],\n", "       [-0.75945075],\n", "       [-0.70774393],\n", "       [ 0.26951294],\n", "       [ 3.07554696],\n", "       [ 2.79795964],\n", "       [ 1.82930377],\n", "       [ 0.72263392],\n", "       [ 2.18752072],\n", "       [ 2.75511227],\n", "       [ 1.7126497 ],\n", "       [ 2.77011529],\n", "       [ 1.44870632],\n", "       [ 0.47275645],\n", "       [ 2.34635297],\n", "       [ 2.11394335],\n", "       [ 2.5171959 ],\n", "       [ 2.90417437],\n", "       [ 2.95424251],\n", "       [ 2.87157294],\n", "       [ 2.66651798],\n", "       [ 2.54530712],\n", "       [ 2.80550086],\n", "       [ 2.72672721],\n", "       [ 1.71600334],\n", "       [-0.70553377],\n", "       [ 0.36735592],\n", "       [ 2.87794695],\n", "       [ 1.77451697],\n", "       [ 0.79028516],\n", "       [ 1.75434834],\n", "       [ 3.17897695],\n", "       [ 2.12057393],\n", "       [ 2.32633586],\n", "       [ 0.54530712],\n", "       [-0.8569852 ],\n", "       [-1.02687215],\n", "       [-1.01322827],\n", "       [ 1.48287358],\n", "       [ 1.77742682],\n", "       [ 0.1172713 ],\n", "       [ 2.12057393],\n", "       [ 1.49692965],\n", "       [ 0.87098881],\n", "       [ 1.59549622],\n", "       [ 0.81090428],\n", "       [ 2.04139269],\n", "       [-1.00436481],\n", "       [-0.46724562],\n", "       [-1.11918641],\n", "       [-0.69464863],\n", "       [ 0.02530587],\n", "       [ 0.21484385],\n", "       [ 1.12057393],\n", "       [ 1.44715803],\n", "       [ 1.48000694],\n", "       [-0.63264408],\n", "       [ 0.20139712],\n", "       [ 2.75281643],\n", "       [ 1.4345689 ],\n", "       [ 2.71096312],\n", "       [-1.        ],\n", "       [-1.32790214],\n", "       [ 0.67302091],\n", "       [ 0.24551267],\n", "       [ 0.93399316],\n", "       [ 2.97680834],\n", "       [ 2.95856388],\n", "       [ 1.76192784],\n", "       [ 1.83758844],\n", "       [ 0.85491302],\n", "       [-1.4202164 ],\n", "       [-1.65757732],\n", "       [-1.67778071],\n", "       [ 1.40654018],\n", "       [ 1.92427929],\n", "       [ 0.06818586],\n", "       [ 0.35983548],\n", "       [ 0.09691001],\n", "       [-0.17198494],\n", "       [ 0.53275438],\n", "       [ 0.47712125],\n", "       [-0.23507702],\n", "       [ 2.16435286],\n", "       [ 1.60314437],\n", "       [ 1.11058971],\n", "       [ 0.46834733],\n", "       [ 1.42324587],\n", "       [ 2.08990511],\n", "       [ 2.26481782],\n", "       [ 2.17609126],\n", "       [ 2.07188201],\n", "       [ 2.28780173],\n", "       [ 2.30749604],\n", "       [ 2.33845649],\n", "       [-1.18045606],\n", "       [-1.45593196],\n", "       [-1.4202164 ],\n", "       [ 1.87506126],\n", "       [ 3.1271048 ],\n", "       [ 0.5865873 ],\n", "       [-1.13667714],\n", "       [-0.92445304],\n", "       [ 2.44870632],\n", "       [ 0.94939001],\n", "       [-0.06248211],\n", "       [ 1.70586371],\n", "       [ 0.32014629],\n", "       [-0.08407279],\n", "       [ 1.68394713],\n", "       [ 1.94792362],\n", "       [ 2.70243054],\n", "       [-1.38721614],\n", "       [-0.06148027],\n", "       [ 0.8591383 ],\n", "       [ 0.24551267],\n", "       [ 1.97312785],\n", "       [ 1.81023252],\n", "       [ 0.91539984],\n", "       [-0.84771166],\n", "       [-0.61261017],\n", "       [-0.41566878],\n", "       [ 0.87273883],\n", "       [-0.52287875],\n", "       [ 2.32428246],\n", "       [ 1.82347423],\n", "       [ 0.49968708],\n", "       [-0.11350927],\n", "       [ 0.24303805],\n", "       [ 0.2278867 ],\n", "       [ 0.40140054],\n", "       [-0.04527521],\n", "       [ 0.07188201],\n", "       [-0.63264408],\n", "       [ 0.54282543],\n", "       [ 0.2278867 ],\n", "       [-1.20760831],\n", "       [ 0.7466342 ],\n", "       [ 1.31806333],\n", "       [ 1.48855072],\n", "       [ 0.8350561 ],\n", "       [ 0.66181269],\n", "       [ 0.79588002],\n", "       [ 0.6180481 ],\n", "       [-0.08618615],\n", "       [ 1.64345268],\n", "       [ 0.99122608],\n", "       [ 0.51851394],\n", "       [-0.04191415],\n", "       [-1.40893539],\n", "       [-1.61978876],\n", "       [-1.55284197],\n", "       [ 3.        ],\n", "       [ 3.33645973],\n", "       [-0.28903688],\n", "       [-0.09097915],\n", "       [ 1.31175386],\n", "       [ 3.28330123],\n", "       [ 2.95808585],\n", "       [-1.21467016],\n", "       [-1.27572413],\n", "       [ 2.13672057],\n", "       [ 0.13672057],\n", "       [-0.73282827],\n", "       [ 0.97451169],\n", "       [ 0.39445168],\n", "       [ 1.51054501],\n", "       [ 0.73957234],\n", "       [ 0.53275438],\n", "       [ 1.55990663],\n", "       [ 0.06069784],\n", "       [-0.01144104],\n", "       [-0.36754271],\n", "       [-0.66154351],\n", "       [ 0.84757266],\n", "       [-0.61978876],\n", "       [-0.53313238],\n", "       [-0.52724355],\n", "       [ 1.35983548],\n", "       [ 0.83122969],\n", "       [ 3.68574174],\n", "       [ 3.52504481],\n", "       [ 3.54282543],\n", "       [ 2.2121876 ],\n", "       [ 2.90579588]])"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["y"]}, {"cell_type": "code", "execution_count": 33, "id": "d35210df", "metadata": {}, "outputs": [], "source": ["model = LinearRegression()"]}, {"cell_type": "code", "execution_count": 35, "id": "3fa65dec", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-2 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-2 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-2 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-2 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-2 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-2 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-2 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-2 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-2 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-2 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-2 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-2 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-2 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-2 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-2 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-2 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-2 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LinearRegression()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" checked><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;LinearRegression<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.linear_model.LinearRegression.html\">?<span>Documentation for LinearRegression</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>LinearRegression()</pre></div> </div></div></div></div>"], "text/plain": ["LinearRegression()"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["model.fit(x,y)"]}, {"cell_type": "code", "execution_count": 36, "id": "015ce2f8", "metadata": {}, "outputs": [], "source": ["r2 = model.score(x,y)"]}, {"cell_type": "code", "execution_count": 37, "id": "e7309030", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.7104407167237579"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["r2"]}, {"cell_type": "code", "execution_count": 38, "id": "29875b4c", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-1.79142803])"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["model.intercept_"]}, {"cell_type": "markdown", "id": "e34a0b28", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 39, "id": "dd7c602b", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.18299988]])"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["model.coef_"]}, {"cell_type": "code", "execution_count": 40, "id": "123692db", "metadata": {}, "outputs": [{"data": {"text/plain": ["'10**(0.18299987515737742) + (-1.7914280261739903)'"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["regression_eq = f'10**({model.coef_[0][0]}) + ({model.intercept_[0]})'\n", "regression_eq"]}, {"cell_type": "code", "execution_count": 42, "id": "b97dcc0f", "metadata": {}, "outputs": [], "source": ["x_plot_vals = np.arange(0,50)\n", "y_pred = model.predict(x_plot_vals.reshape(-1,1))"]}, {"cell_type": "code", "execution_count": 44, "id": "1ba2b28b", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1.61648610e-02],\n", "       [2.46360938e-02],\n", "       [3.75466958e-02],\n", "       [5.72231287e-02],\n", "       [8.72110418e-02],\n", "       [1.32914190e-01],\n", "       [2.02568179e-01],\n", "       [3.08724503e-01],\n", "       [4.70512293e-01],\n", "       [7.17085350e-01],\n", "       [1.09287559e+00],\n", "       [1.66559957e+00],\n", "       [2.53846088e+00],\n", "       [3.86874719e+00],\n", "       [5.89617311e+00],\n", "       [8.98607629e+00],\n", "       [1.36952504e+01],\n", "       [2.08722780e+01],\n", "       [3.18104437e+01],\n", "       [4.84807804e+01],\n", "       [7.38872456e+01],\n", "       [1.12608028e+02],\n", "       [1.71620525e+02],\n", "       [2.61558659e+02],\n", "       [3.98629080e+02],\n", "       [6.07531573e+02],\n", "       [9.25909901e+02],\n", "       [1.41113513e+03],\n", "       [2.15064376e+03],\n", "       [3.27769360e+03],\n", "       [4.99537652e+03],\n", "       [7.61321516e+03],\n", "       [1.16029382e+04],\n", "       [1.76834848e+04],\n", "       [2.69505560e+04],\n", "       [4.10740573e+04],\n", "       [6.25990121e+04],\n", "       [9.54041694e+04],\n", "       [1.45400945e+05],\n", "       [2.21598647e+05],\n", "       [3.37727932e+05],\n", "       [5.14715036e+05],\n", "       [7.84452643e+05],\n", "       [1.19554687e+06],\n", "       [1.82207597e+06],\n", "       [2.77693910e+06],\n", "       [4.23220047e+06],\n", "       [6.45009493e+06],\n", "       [9.83028211e+06],\n", "       [1.49818642e+07]])"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["y_pred_log = 10**y_pred\n", "y_pred_log"]}, {"cell_type": "code", "execution_count": 45, "id": "87affec5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>pors_vals</th>\n", "      <th>perm_vals</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>1.616486e-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>2.463609e-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>3.754670e-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>5.722313e-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>8.721104e-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>5</td>\n", "      <td>1.329142e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>6</td>\n", "      <td>2.025682e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>7</td>\n", "      <td>3.087245e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>8</td>\n", "      <td>4.705123e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>9</td>\n", "      <td>7.170854e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>10</td>\n", "      <td>1.092876e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>11</td>\n", "      <td>1.665600e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>12</td>\n", "      <td>2.538461e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>13</td>\n", "      <td>3.868747e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>14</td>\n", "      <td>5.896173e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>15</td>\n", "      <td>8.986076e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>16</td>\n", "      <td>1.369525e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>17</td>\n", "      <td>2.087228e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>18</td>\n", "      <td>3.181044e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>19</td>\n", "      <td>4.848078e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>20</td>\n", "      <td>7.388725e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>21</td>\n", "      <td>1.126080e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>22</td>\n", "      <td>1.716205e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>23</td>\n", "      <td>2.615587e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>24</td>\n", "      <td>3.986291e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>25</td>\n", "      <td>6.075316e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>26</td>\n", "      <td>9.259099e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>27</td>\n", "      <td>1.411135e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>28</td>\n", "      <td>2.150644e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>29</td>\n", "      <td>3.277694e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>30</td>\n", "      <td>4.995377e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>31</td>\n", "      <td>7.613215e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>32</td>\n", "      <td>1.160294e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>33</td>\n", "      <td>1.768348e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>34</td>\n", "      <td>2.695056e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>35</td>\n", "      <td>4.107406e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>36</td>\n", "      <td>6.259901e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>37</td>\n", "      <td>9.540417e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>38</td>\n", "      <td>1.454009e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>39</td>\n", "      <td>2.215986e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>40</td>\n", "      <td>3.377279e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>41</td>\n", "      <td>5.147150e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>42</td>\n", "      <td>7.844526e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>43</td>\n", "      <td>1.195547e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>44</td>\n", "      <td>1.822076e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>45</td>\n", "      <td>2.776939e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>46</td>\n", "      <td>4.232200e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>47</td>\n", "      <td>6.450095e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>48</td>\n", "      <td>9.830282e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>49</td>\n", "      <td>1.498186e+07</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    pors_vals     perm_vals\n", "0           0  1.616486e-02\n", "1           1  2.463609e-02\n", "2           2  3.754670e-02\n", "3           3  5.722313e-02\n", "4           4  8.721104e-02\n", "5           5  1.329142e-01\n", "6           6  2.025682e-01\n", "7           7  3.087245e-01\n", "8           8  4.705123e-01\n", "9           9  7.170854e-01\n", "10         10  1.092876e+00\n", "11         11  1.665600e+00\n", "12         12  2.538461e+00\n", "13         13  3.868747e+00\n", "14         14  5.896173e+00\n", "15         15  8.986076e+00\n", "16         16  1.369525e+01\n", "17         17  2.087228e+01\n", "18         18  3.181044e+01\n", "19         19  4.848078e+01\n", "20         20  7.388725e+01\n", "21         21  1.126080e+02\n", "22         22  1.716205e+02\n", "23         23  2.615587e+02\n", "24         24  3.986291e+02\n", "25         25  6.075316e+02\n", "26         26  9.259099e+02\n", "27         27  1.411135e+03\n", "28         28  2.150644e+03\n", "29         29  3.277694e+03\n", "30         30  4.995377e+03\n", "31         31  7.613215e+03\n", "32         32  1.160294e+04\n", "33         33  1.768348e+04\n", "34         34  2.695056e+04\n", "35         35  4.107406e+04\n", "36         36  6.259901e+04\n", "37         37  9.540417e+04\n", "38         38  1.454009e+05\n", "39         39  2.215986e+05\n", "40         40  3.377279e+05\n", "41         41  5.147150e+05\n", "42         42  7.844526e+05\n", "43         43  1.195547e+06\n", "44         44  1.822076e+06\n", "45         45  2.776939e+06\n", "46         46  4.232200e+06\n", "47         47  6.450095e+06\n", "48         48  9.830282e+06\n", "49         49  1.498186e+07"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["results_df = pd.DataFrame({'pors_vals': x_plot_vals, 'perm_vals': y_pred_log.flatten()})\n", "results_df"]}, {"cell_type": "code", "execution_count": 49, "id": "e77671ca", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'por_vals'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3805\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3804\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3805\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3806\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON>Erro<PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mindex.pyx:167\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mindex.pyx:196\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7081\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7089\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON>Error\u001b[0m: 'por_vals'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[49], line 10\u001b[0m\n\u001b[0;32m      7\u001b[0m sns\u001b[38;5;241m.\u001b[39mscatterplot(x\u001b[38;5;241m=\u001b[39mdf[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mCPOR\u001b[39m\u001b[38;5;124m'\u001b[39m], y\u001b[38;5;241m=\u001b[39mdf[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mCKHL\u001b[39m\u001b[38;5;124m'\u001b[39m], hue\u001b[38;5;241m=\u001b[39mdf[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mCGD\u001b[39m\u001b[38;5;124m'\u001b[39m], palette\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mYlOrRd\u001b[39m\u001b[38;5;124m'\u001b[39m, s\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m50\u001b[39m)\n\u001b[0;32m      9\u001b[0m \u001b[38;5;66;03m# Line plot (overlay)\u001b[39;00m\n\u001b[1;32m---> 10\u001b[0m sns\u001b[38;5;241m.\u001b[39mlineplot(x\u001b[38;5;241m=\u001b[39m\u001b[43mresults_df\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mpor_vals\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m, y\u001b[38;5;241m=\u001b[39mresults_df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mperm_vals\u001b[39m\u001b[38;5;124m'\u001b[39m], color\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mblack\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     12\u001b[0m \u001b[38;5;66;03m# Get current axis to set labels and scale\u001b[39;00m\n\u001b[0;32m     13\u001b[0m plt\u001b[38;5;241m.\u001b[39myscale(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlog\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\frame.py:4102\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   4100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   4101\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 4102\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   4103\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   4104\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3812\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3807\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   3808\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[0;32m   3809\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[0;32m   3810\u001b[0m     ):\n\u001b[0;32m   3811\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[1;32m-> 3812\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3813\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3814\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3815\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3816\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3817\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON><PERSON>Error\u001b[0m: 'por_vals'"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 6))\n", "\n", "# Scatter plot\n", "sns.scatterplot(x=df['CPOR'], y=df['CKHL'], hue=df['CGD'], palette='YlOrRd', s=50)\n", "\n", "# Line plot (overlay)\n", "sns.lineplot(x=results_df['por_vals'], y=results_df['perm_vals'], color='black')\n", "\n", "# Get current axis to set labels and scale\n", "plt.yscale('log')\n", "plt.ylabel('Core Permeability (mD)', fontsize=12, fontweight='bold')\n", "plt.xlabel(\"Core Porosity (%)\", fontsize=12, fontweight='bold')\n", "\n", "plt.title(\"Porosity vs Permeability\", fontsize=14, fontweight='bold')\n", "plt.legend(title='CGD')\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "0df6b329", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0e1d1865", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5ba6e6b0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "05296708", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}