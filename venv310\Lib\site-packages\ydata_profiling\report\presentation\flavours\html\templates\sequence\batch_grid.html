{% for batch_items in items | batch(batch_size) %}
    <div class="row sub-item">
        {% for item in batch_items %}
            <div class="col-sm-{{ '%d' | format(12 / batch_size) }}{% if item.content['classes'] %} {{ item.content['classes'] }}{% endif %}">
                {% if "name" in item.content %}
                    {% if subtitles or item.content["subtitles"] %}
                        <h5 class="item-header">{{ item.name }}</h5>
                    {% elif titles or item.content["titles"] %}
                        <h4 class="item-header">{{ item.name }}</h4>
                    {% endif %}
                {% endif %}
                {{ item.render() }}
            </div>
        {% endfor %}
    </div>
{% endfor %}