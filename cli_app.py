"""
Command Line Interface for the Offline Semantic Search Application
"""

import click
import json
import time
from typing import List, Dict
from tabulate import tabulate
import os

from semantic_search_engine import SemanticSearchEngine
from data_generator import create_sample_dataset
from config import *

class CLISearchApp:
    """Command line interface for semantic search"""
    
    def __init__(self):
        self.engine = SemanticSearchEngine()
        self.initialize()
    
    def initialize(self):
        """Initialize the search engine"""
        click.echo("🔍 Initializing ONGC Knowledge Management System...")
        
        # Create sample dataset if it doesn't exist
        if not os.path.exists(SAMPLE_DATASET_FILE):
            click.echo("📝 Creating sample dataset...")
            create_sample_dataset()
        
        # Load components
        click.echo("📚 Loading articles...")
        self.engine.load_articles()
        
        click.echo("🤖 Loading SBERT model...")
        self.engine.load_model()
        
        click.echo("🔢 Generating embeddings...")
        self.engine.generate_embeddings()
        
        # Build FAISS index if available
        try:
            click.echo("⚡ Building FAISS index...")
            self.engine.build_faiss_index()
        except Exception as e:
            click.echo(f"⚠️  FAISS index building failed: {e}")
        
        click.echo("✅ Initialization complete!")
        click.echo()
    
    def display_results(self, results: List[Dict], query_time: float):
        """Display search results in a formatted table"""
        if not results:
            click.echo("❌ No results found. Try adjusting your search query.")
            return
        
        click.echo(f"✅ Found {len(results)} results in {query_time:.3f} seconds")
        click.echo()
        
        # Prepare table data
        table_data = []
        for result in results:
            # Truncate content for table display
            content_preview = result['content'][:100] + "..." if len(result['content']) > 100 else result['content']
            
            table_data.append([
                result['rank'],
                result['title'][:40] + "..." if len(result['title']) > 40 else result['title'],
                result['category'],
                result['author'],
                f"{result['similarity_score']:.3f}",
                content_preview
            ])
        
        headers = ["Rank", "Title", "Category", "Author", "Score", "Content Preview"]
        click.echo(tabulate(table_data, headers=headers, tablefmt="grid"))
        click.echo()
    
    def display_full_article(self, article: Dict):
        """Display full article details"""
        click.echo("=" * 80)
        click.echo(f"📄 {article['title']}")
        click.echo("=" * 80)
        click.echo(f"📂 Category: {article['category']}")
        click.echo(f"👤 Author: {article['author']}")
        click.echo(f"📅 Date: {article['date']}")
        
        if article.get('tags'):
            click.echo(f"🏷️  Tags: {', '.join(article['tags'])}")
        
        if 'similarity_score' in article:
            click.echo(f"🎯 Similarity Score: {article['similarity_score']:.3f}")
        
        click.echo()
        click.echo("📝 Content:")
        click.echo("-" * 40)
        click.echo(article['content'])
        click.echo("=" * 80)
        click.echo()
    
    def interactive_search(self):
        """Interactive search mode"""
        click.echo("🔍 Interactive Search Mode")
        click.echo("Type 'quit' to exit, 'help' for commands")
        click.echo()
        
        while True:
            try:
                query = click.prompt("Search", type=str).strip()
                
                if query.lower() in ['quit', 'exit', 'q']:
                    click.echo("👋 Goodbye!")
                    break
                
                if query.lower() == 'help':
                    self.show_help()
                    continue
                
                if query.lower() == 'stats':
                    self.show_statistics()
                    continue
                
                if query.lower().startswith('view '):
                    try:
                        article_id = int(query.split()[1])
                        article = self.engine.get_article_by_id(article_id)
                        if article:
                            self.display_full_article(article)
                        else:
                            click.echo(f"❌ Article with ID {article_id} not found.")
                    except (IndexError, ValueError):
                        click.echo("❌ Invalid command. Use 'view <article_id>'")
                    continue
                
                if not query:
                    continue
                
                # Perform search
                start_time = time.time()
                results = self.engine.search(query, top_k=DEFAULT_TOP_K)
                query_time = time.time() - start_time
                
                self.display_results(results, query_time)
                
                # Ask if user wants to view full article
                if results:
                    view_full = click.confirm("View full article? (Enter article rank)")
                    if view_full:
                        try:
                            rank = click.prompt("Enter rank (1-{}):".format(len(results)), type=int)
                            if 1 <= rank <= len(results):
                                self.display_full_article(results[rank-1])
                            else:
                                click.echo("❌ Invalid rank number.")
                        except click.Abort:
                            continue
                
            except KeyboardInterrupt:
                click.echo("\n👋 Goodbye!")
                break
            except Exception as e:
                click.echo(f"❌ Error: {e}")
    
    def show_help(self):
        """Show help information"""
        help_text = """
🔍 ONGC Knowledge Management System - Help

Commands:
  <search_query>     - Search for articles (e.g., "drilling fluid optimization")
  help              - Show this help message
  stats             - Show knowledge base statistics
  view <id>         - View full article by ID
  quit/exit/q       - Exit the application

Search Tips:
  • Use natural language queries
  • Combine multiple keywords for better results
  • Try different phrasings if you don't find what you're looking for

Examples:
  "drilling fluid optimization high temperature"
  "reservoir characterization well logs"
  "corrosion prevention offshore"
  "seismic interpretation"
        """
        click.echo(help_text)
    
    def show_statistics(self):
        """Show knowledge base statistics"""
        stats = self.engine.get_statistics()
        
        click.echo("📊 Knowledge Base Statistics")
        click.echo("=" * 40)
        click.echo(f"📚 Total Articles: {stats['total_articles']}")
        click.echo(f"📂 Total Categories: {stats['total_categories']}")
        click.echo(f"🔢 Embedding Dimensions: {stats['embedding_dimensions']}")
        click.echo(f"🤖 Model: {stats['model_name']}")
        click.echo()
        click.echo("📂 Categories:")
        for category in stats['categories']:
            count = sum(1 for article in self.engine.articles if article['category'] == category)
            click.echo(f"  • {category}: {count} articles")
        click.echo()

@click.group()
def cli():
    """ONGC Knowledge Management System - Offline Semantic Search"""
    pass

@cli.command()
@click.argument('query', required=False)
@click.option('--top-k', '-k', default=DEFAULT_TOP_K, help='Number of results to return')
@click.option('--threshold', '-t', default=SIMILARITY_THRESHOLD, help='Similarity threshold')
@click.option('--interactive', '-i', is_flag=True, help='Start interactive mode')
def search(query, top_k, threshold, interactive):
    """Search the knowledge base"""
    app = CLISearchApp()
    
    # Update threshold
    import config
    config.SIMILARITY_THRESHOLD = threshold
    
    if interactive or not query:
        app.interactive_search()
    else:
        start_time = time.time()
        results = app.engine.search(query, top_k=top_k)
        query_time = time.time() - start_time
        
        app.display_results(results, query_time)

@cli.command()
def stats():
    """Show knowledge base statistics"""
    app = CLISearchApp()
    app.show_statistics()

@cli.command()
@click.argument('article_id', type=int)
def view(article_id):
    """View full article by ID"""
    app = CLISearchApp()
    article = app.engine.get_article_by_id(article_id)
    if article:
        app.display_full_article(article)
    else:
        click.echo(f"❌ Article with ID {article_id} not found.")

@cli.command()
def init():
    """Initialize the knowledge base"""
    app = CLISearchApp()
    click.echo("✅ Knowledge base initialized successfully!")

if __name__ == "__main__":
    cli()
