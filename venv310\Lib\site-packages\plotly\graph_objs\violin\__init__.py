import sys
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ._box import Box
    from ._hoverlabel import Hoverlabel
    from ._legendgrouptitle import Legendgrouptitle
    from ._line import Line
    from ._marker import Marker
    from ._meanline import Meanline
    from ._selected import Selected
    from ._stream import Stream
    from ._unselected import Unselected
    from . import box
    from . import hoverlabel
    from . import legendgrouptitle
    from . import marker
    from . import selected
    from . import unselected
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [
            ".box",
            ".hoverlabel",
            ".legendgrouptitle",
            ".marker",
            ".selected",
            ".unselected",
        ],
        [
            "._box.Box",
            "._hoverlabel.Hoverlabel",
            "._legendgrouptitle.Legendgrouptitle",
            "._line.Line",
            "._marker.Marker",
            "._meanline.Meanline",
            "._selected.Selected",
            "._stream.Stream",
            "._unselected.Unselected",
        ],
    )
