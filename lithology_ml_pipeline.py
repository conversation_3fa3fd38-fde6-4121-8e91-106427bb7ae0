"""
🪨 LITHOLOGY CLASSIFICATION ML PIPELINE
=====================================
A comprehensive machine learning pipeline for predicting lithology classes from well log data.

Features:
- Data preprocessing with intelligent missing value handling
- Random Forest and XGBoost classifiers
- Comprehensive evaluation metrics and visualizations
- Interactive Plotly plots for geoscientist interpretation
- Model persistence and inference capabilities
- Professional presentation-ready outputs

Author: ONGC Petrophysical Analysis Team
Date: 2025-01-19
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# Machine Learning imports
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import (classification_report, confusion_matrix,
                           accuracy_score, precision_recall_fscore_support)
from sklearn.impute import SimpleImputer
import xgboost as xgb
import joblib

# System imports
import os
import glob
from datetime import datetime
import json

class LithologyMLPipeline:
    """
    Complete ML Pipeline for Lithology Classification
    """

    def __init__(self, data_directory="litho_data", results_dir="model_results"):
        """
        Initialize the ML pipeline

        Args:
            data_directory (str): Directory containing CSV files with well log data
            results_dir (str): Directory to save models and results
        """
        self.data_directory = data_directory
        self.results_dir = results_dir
        self.models = {}
        self.scalers = {}
        self.label_encoder = LabelEncoder()
        self.feature_columns = ['GR', 'RHOB', 'NPHI', 'RDEP', 'DTC', 'PEF']
        self.target_column = 'FORCE_2020_LITHOFACIES_LITHOLOGY'

        # Create results directory
        os.makedirs(results_dir, exist_ok=True)
        os.makedirs(f"{results_dir}/visualizations", exist_ok=True)

        print("🚀 Lithology ML Pipeline Initialized")
        print(f"📁 Data Directory: {data_directory}")
        print(f"💾 Results Directory: {results_dir}")

    def load_and_combine_data(self):
        """
        Load and combine all CSV files from the data directory

        Returns:
            pd.DataFrame: Combined dataset
        """
        print("\n📊 LOADING AND COMBINING DATA")
        print("=" * 50)

        # Get all CSV files
        csv_files = glob.glob(f"{self.data_directory}/*.csv")

        if not csv_files:
            raise FileNotFoundError(f"No CSV files found in {self.data_directory}")

        print(f"📁 Found {len(csv_files)} CSV files:")

        dataframes = []
        total_rows = 0

        for file in csv_files:
            try:
                df = pd.read_csv(file)
                filename = os.path.basename(file)
                print(f"   ✅ {filename}: {len(df):,} rows, {len(df.columns)} columns")

                # Add source file column for tracking
                df['SOURCE_FILE'] = filename
                dataframes.append(df)
                total_rows += len(df)

            except Exception as e:
                print(f"   ❌ Error loading {file}: {str(e)}")

        if not dataframes:
            raise ValueError("No valid CSV files could be loaded")

        # Combine all dataframes
        combined_df = pd.concat(dataframes, ignore_index=True, sort=False)

        print(f"\n📈 COMBINED DATASET SUMMARY:")
        print(f"   📊 Total rows: {len(combined_df):,}")
        print(f"   📋 Total columns: {len(combined_df.columns)}")
        print(f"   🏢 Wells: {combined_df['WELL'].nunique() if 'WELL' in combined_df.columns else 'N/A'}")

        self.raw_data = combined_df
        return combined_df

    def analyze_data_quality(self, df):
        """
        Analyze data quality and missing values

        Args:
            df (pd.DataFrame): Input dataset
        """
        print(f"\n🔍 DATA QUALITY ANALYSIS")
        print("=" * 50)

        # Check for required columns
        available_features = [col for col in self.feature_columns if col in df.columns]
        missing_features = [col for col in self.feature_columns if col not in df.columns]

        print(f"✅ Available features: {available_features}")
        if missing_features:
            print(f"⚠️  Missing features: {missing_features}")

        # Check target column
        if self.target_column in df.columns:
            print(f"✅ Target column '{self.target_column}' found")

            # Lithology distribution
            lith_counts = df[self.target_column].value_counts()
            print(f"\n🪨 LITHOLOGY DISTRIBUTION:")
            for lith, count in lith_counts.head(10).items():
                pct = (count / len(df)) * 100
                print(f"   🔸 {lith}: {count:,} samples ({pct:.1f}%)")
        else:
            print(f"❌ Target column '{self.target_column}' not found")
            print(f"Available columns: {list(df.columns)}")

        # Missing value analysis
        print(f"\n📋 MISSING VALUE ANALYSIS:")
        for col in available_features + [self.target_column]:
            if col in df.columns:
                missing_count = df[col].isna().sum()
                missing_pct = (missing_count / len(df)) * 100
                status = "✅" if missing_count == 0 else "⚠️" if missing_pct < 10 else "❌"
                print(f"   {status} {col}: {missing_count:,} missing ({missing_pct:.1f}%)")

    def preprocess_data(self, df):
        """
        Preprocess the data for machine learning

        Args:
            df (pd.DataFrame): Raw dataset

        Returns:
            tuple: (X_processed, y_processed, feature_names)
        """
        print(f"\n⚙️  DATA PREPROCESSING")
        print("=" * 50)

        # Filter for available features
        available_features = [col for col in self.feature_columns if col in df.columns]

        if len(available_features) < 3:
            raise ValueError(f"Insufficient features available. Need at least 3, got {len(available_features)}")

        # Check if target column exists
        if self.target_column not in df.columns:
            raise ValueError(f"Target column '{self.target_column}' not found in dataset")

        # Remove rows where target is missing
        df_clean = df.dropna(subset=[self.target_column]).copy()
        print(f"📊 Removed {len(df) - len(df_clean):,} rows with missing target values")

        # Extract features and target
        X = df_clean[available_features].copy()
        y = df_clean[self.target_column].copy()

        print(f"📈 Dataset shape: {X.shape}")
        print(f"🎯 Target classes: {y.nunique()}")

        # Handle missing values in features
        print(f"\n🔧 HANDLING MISSING VALUES:")
        imputer = SimpleImputer(strategy='median')
        X_imputed = pd.DataFrame(
            imputer.fit_transform(X),
            columns=available_features,
            index=X.index
        )

        for col in available_features:
            missing_before = X[col].isna().sum()
            missing_after = X_imputed[col].isna().sum()
            if missing_before > 0:
                print(f"   🔧 {col}: {missing_before} → {missing_after} missing values")

        # Scale features
        print(f"\n📏 FEATURE SCALING:")
        scaler = StandardScaler()
        X_scaled = pd.DataFrame(
            scaler.fit_transform(X_imputed),
            columns=available_features,
            index=X_imputed.index
        )

        # Encode target labels
        print(f"\n🏷️  LABEL ENCODING:")
        y_encoded = self.label_encoder.fit_transform(y)

        print(f"   📋 Label mapping:")
        for i, label in enumerate(self.label_encoder.classes_):
            count = (y_encoded == i).sum()
            print(f"      {i}: {label} ({count:,} samples)")

        # Store preprocessing objects
        self.imputer = imputer
        self.scaler = scaler
        self.feature_names = available_features

        return X_scaled, y_encoded, available_features

    def train_models(self, X, y):
        """
        Train Random Forest and XGBoost models with hyperparameter tuning

        Args:
            X (pd.DataFrame): Preprocessed features
            y (np.array): Encoded target labels
        """
        print(f"\n🤖 MODEL TRAINING")
        print("=" * 50)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )

        print(f"📊 Training set: {X_train.shape[0]:,} samples")
        print(f"📊 Test set: {X_test.shape[0]:,} samples")

        self.X_train, self.X_test = X_train, X_test
        self.y_train, self.y_test = y_train, y_test

        # 1. Random Forest with hyperparameter tuning
        print(f"\n🌲 TRAINING RANDOM FOREST:")
        rf_param_grid = {
            'n_estimators': [100, 200],
            'max_depth': [10, None],
            'min_samples_split': [2, 5],
            'min_samples_leaf': [1, 2]
        }

        rf_base = RandomForestClassifier(random_state=42, n_jobs=-1)
        rf_grid = GridSearchCV(
            rf_base, rf_param_grid, cv=5, scoring='f1_weighted',
            n_jobs=-1, verbose=1
        )

        rf_grid.fit(X_train, y_train)
        self.models['random_forest'] = rf_grid.best_estimator_

        print(f"   ✅ Best RF parameters: {rf_grid.best_params_}")
        print(f"   📈 Best CV score: {rf_grid.best_score_:.4f}")

        # 2. XGBoost with hyperparameter tuning
        print(f"\n🚀 TRAINING XGBOOST:")
        xgb_param_grid = {
            'n_estimators': [100, 200],
            'max_depth': [3, 6],
            'learning_rate': [0.1, 0.2],
            'subsample': [0.8, 1.0]
        }

        xgb_base = xgb.XGBClassifier(random_state=42, n_jobs=-1)
        xgb_grid = GridSearchCV(
            xgb_base, xgb_param_grid, cv=5, scoring='f1_weighted',
            n_jobs=-1, verbose=1
        )

        xgb_grid.fit(X_train, y_train)
        self.models['xgboost'] = xgb_grid.best_estimator_

        print(f"   ✅ Best XGB parameters: {xgb_grid.best_params_}")
        print(f"   📈 Best CV score: {xgb_grid.best_score_:.4f}")

        print(f"\n✅ Model training completed!")

    def evaluate_models(self):
        """
        Evaluate trained models and generate comprehensive metrics
        """
        print(f"\n📊 MODEL EVALUATION")
        print("=" * 50)

        self.evaluation_results = {}

        for model_name, model in self.models.items():
            print(f"\n🔍 Evaluating {model_name.upper()}:")

            # Predictions
            y_pred = model.predict(self.X_test)

            # Metrics
            accuracy = accuracy_score(self.y_test, y_pred)
            precision, recall, f1, _ = precision_recall_fscore_support(
                self.y_test, y_pred, average='weighted'
            )

            print(f"   📈 Accuracy: {accuracy:.4f}")
            print(f"   📈 Precision: {precision:.4f}")
            print(f"   📈 Recall: {recall:.4f}")
            print(f"   📈 F1-Score: {f1:.4f}")

            # Store results
            self.evaluation_results[model_name] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'predictions': y_pred,
                'classification_report': classification_report(
                    self.y_test, y_pred,
                    target_names=self.label_encoder.classes_,
                    output_dict=True
                )
            }

            # Detailed classification report
            print(f"\n📋 Classification Report for {model_name.upper()}:")
            target_names = [str(cls) for cls in self.label_encoder.classes_]
            print(classification_report(
                self.y_test, y_pred,
                target_names=target_names
            ))

    def create_visualizations(self):
        """
        Create comprehensive visualizations for model interpretation
        """
        print(f"\n🎨 CREATING VISUALIZATIONS")
        print("=" * 50)

        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")

        # 1. Confusion Matrices
        self._plot_confusion_matrices()

        # 2. Feature Importance
        self._plot_feature_importance()

        # 3. Lithology vs Depth plots
        self._plot_lithology_depth()

        # 4. Interactive Plotly visualizations
        self._create_interactive_plots()

        print(f"✅ All visualizations saved to {self.results_dir}/visualizations/")

    def _plot_confusion_matrices(self):
        """Create confusion matrix plots for all models"""
        fig, axes = plt.subplots(1, len(self.models), figsize=(15, 6))
        if len(self.models) == 1:
            axes = [axes]

        for idx, (model_name, model) in enumerate(self.models.items()):
            y_pred = self.evaluation_results[model_name]['predictions']
            cm = confusion_matrix(self.y_test, y_pred)

            target_names = [str(cls) for cls in self.label_encoder.classes_]
            sns.heatmap(
                cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=target_names,
                yticklabels=target_names,
                ax=axes[idx]
            )

            axes[idx].set_title(f'{model_name.title()} Confusion Matrix')
            axes[idx].set_xlabel('Predicted')
            axes[idx].set_ylabel('Actual')

        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/visualizations/confusion_matrices.png',
                   dpi=300, bbox_inches='tight')
        plt.show()

    def _plot_feature_importance(self):
        """Plot feature importance for tree-based models"""
        fig, axes = plt.subplots(1, len(self.models), figsize=(15, 6))
        if len(self.models) == 1:
            axes = [axes]

        for idx, (model_name, model) in enumerate(self.models.items()):
            if hasattr(model, 'feature_importances_'):
                importance_df = pd.DataFrame({
                    'feature': self.feature_names,
                    'importance': model.feature_importances_
                }).sort_values('importance', ascending=True)

                axes[idx].barh(importance_df['feature'], importance_df['importance'])
                axes[idx].set_title(f'{model_name.title()} Feature Importance')
                axes[idx].set_xlabel('Importance')

        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/visualizations/feature_importance.png',
                   dpi=300, bbox_inches='tight')
        plt.show()

    def _plot_lithology_depth(self):
        """Create lithology vs depth visualization"""
        if 'DEPTH_MD' not in self.raw_data.columns:
            print("⚠️  DEPTH_MD column not found, skipping depth plots")
            return

        # Get predictions for the best model (highest F1 score)
        best_model_name = max(self.evaluation_results.keys(),
                             key=lambda x: self.evaluation_results[x]['f1_score'])
        best_model = self.models[best_model_name]

        # Create predictions for visualization
        test_indices = self.X_test.index
        test_data = self.raw_data.loc[test_indices].copy()

        if len(test_data) == 0:
            print("⚠️  No test data with depth information available")
            return

        y_pred = self.evaluation_results[best_model_name]['predictions']

        # Decode predictions and actual values
        pred_labels = self.label_encoder.inverse_transform(y_pred)
        actual_labels = self.label_encoder.inverse_transform(self.y_test)

        # Create depth plot
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 10))

        # Plot 1: Actual vs Predicted
        depths = test_data['DEPTH_MD'].values

        # Create color mapping for lithologies
        unique_lithologies = np.unique(np.concatenate([actual_labels, pred_labels]))
        colors = plt.cm.Set3(np.linspace(0, 1, len(unique_lithologies)))
        color_map = dict(zip(unique_lithologies, colors))

        # Actual lithology
        for lith in unique_lithologies:
            mask = actual_labels == lith
            if mask.any():
                ax1.scatter(np.ones(mask.sum()) * 1, depths[mask],
                           c=[color_map[lith]], label=lith, alpha=0.7, s=20)

        ax1.set_xlim(0.5, 1.5)
        ax1.set_xlabel('Actual Lithology')
        ax1.set_ylabel('Depth (MD)')
        ax1.set_title('Actual Lithology vs Depth')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.invert_yaxis()

        # Predicted lithology
        for lith in unique_lithologies:
            mask = pred_labels == lith
            if mask.any():
                ax2.scatter(np.ones(mask.sum()) * 1, depths[mask],
                           c=[color_map[lith]], label=lith, alpha=0.7, s=20)

        ax2.set_xlim(0.5, 1.5)
        ax2.set_xlabel('Predicted Lithology')
        ax2.set_ylabel('Depth (MD)')
        ax2.set_title(f'Predicted Lithology vs Depth\n({best_model_name.title()} Model)')
        ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax2.invert_yaxis()

        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/visualizations/lithology_depth_comparison.png',
                   dpi=300, bbox_inches='tight')
        plt.show()

    def _create_interactive_plots(self):
        """Create interactive Plotly visualizations"""
        # Get the best model
        best_model_name = max(self.evaluation_results.keys(),
                             key=lambda x: self.evaluation_results[x]['f1_score'])

        # 1. Interactive confusion matrix
        y_pred = self.evaluation_results[best_model_name]['predictions']
        cm = confusion_matrix(self.y_test, y_pred)

        fig_cm = px.imshow(
            cm,
            labels=dict(x="Predicted", y="Actual", color="Count"),
            x=self.label_encoder.classes_,
            y=self.label_encoder.classes_,
            color_continuous_scale='Blues',
            title=f'Interactive Confusion Matrix - {best_model_name.title()}'
        )

        fig_cm.update_layout(
            width=800, height=600,
            title_x=0.5,
            font=dict(size=12)
        )

        fig_cm.write_html(f'{self.results_dir}/visualizations/interactive_confusion_matrix.html')

        # 2. Feature importance interactive plot
        if hasattr(self.models[best_model_name], 'feature_importances_'):
            importance_df = pd.DataFrame({
                'Feature': self.feature_names,
                'Importance': self.models[best_model_name].feature_importances_
            }).sort_values('Importance', ascending=True)

            fig_imp = px.bar(
                importance_df,
                x='Importance',
                y='Feature',
                orientation='h',
                title=f'Interactive Feature Importance - {best_model_name.title()}',
                color='Importance',
                color_continuous_scale='Viridis'
            )

            fig_imp.update_layout(
                width=800, height=500,
                title_x=0.5,
                font=dict(size=12)
            )

            fig_imp.write_html(f'{self.results_dir}/visualizations/interactive_feature_importance.html')

        print("📊 Interactive plots saved as HTML files")

    def save_models(self):
        """Save trained models and preprocessing objects"""
        print(f"\n💾 SAVING MODELS AND PREPROCESSING OBJECTS")
        print("=" * 50)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save models
        for model_name, model in self.models.items():
            model_path = f'{self.results_dir}/{model_name}_model_{timestamp}.joblib'
            joblib.dump(model, model_path)
            print(f"✅ Saved {model_name} model: {model_path}")

        # Save preprocessing objects
        preprocessing_objects = {
            'imputer': self.imputer,
            'scaler': self.scaler,
            'label_encoder': self.label_encoder,
            'feature_names': self.feature_names
        }

        preprocessing_path = f'{self.results_dir}/preprocessing_objects_{timestamp}.joblib'
        joblib.dump(preprocessing_objects, preprocessing_path)
        print(f"✅ Saved preprocessing objects: {preprocessing_path}")

        # Save evaluation results
        results_path = f'{self.results_dir}/evaluation_results_{timestamp}.json'

        # Convert numpy arrays to lists for JSON serialization
        json_results = {}
        for model_name, results in self.evaluation_results.items():
            json_results[model_name] = {
                'accuracy': float(results['accuracy']),
                'precision': float(results['precision']),
                'recall': float(results['recall']),
                'f1_score': float(results['f1_score'])
            }

        with open(results_path, 'w') as f:
            json.dump(json_results, f, indent=2)
        print(f"✅ Saved evaluation results: {results_path}")

        return {
            'models': {name: f'{self.results_dir}/{name}_model_{timestamp}.joblib'
                      for name in self.models.keys()},
            'preprocessing': preprocessing_path,
            'results': results_path
        }

    def predict_new_data(self, new_data, model_name='best'):
        """
        Make predictions on new data

        Args:
            new_data (pd.DataFrame): New well log data
            model_name (str): Model to use ('best', 'random_forest', 'xgboost')

        Returns:
            dict: Predictions and probabilities
        """
        if model_name == 'best':
            model_name = max(self.evaluation_results.keys(),
                           key=lambda x: self.evaluation_results[x]['f1_score'])

        model = self.models[model_name]

        # Preprocess new data
        X_new = new_data[self.feature_names].copy()
        X_new_imputed = pd.DataFrame(
            self.imputer.transform(X_new),
            columns=self.feature_names
        )
        X_new_scaled = pd.DataFrame(
            self.scaler.transform(X_new_imputed),
            columns=self.feature_names
        )

        # Make predictions
        predictions = model.predict(X_new_scaled)
        probabilities = model.predict_proba(X_new_scaled)

        # Decode predictions
        predicted_labels = self.label_encoder.inverse_transform(predictions)

        return {
            'predictions': predicted_labels,
            'probabilities': probabilities,
            'class_names': self.label_encoder.classes_,
            'model_used': model_name
        }

    def run_complete_pipeline(self):
        """
        Execute the complete ML pipeline

        Returns:
            dict: Summary of results and saved files
        """
        print("🚀 STARTING COMPLETE LITHOLOGY ML PIPELINE")
        print("=" * 60)

        try:
            # 1. Load and combine data
            df = self.load_and_combine_data()

            # 2. Analyze data quality
            self.analyze_data_quality(df)

            # 3. Preprocess data
            X, y, feature_names = self.preprocess_data(df)

            # 4. Train models
            self.train_models(X, y)

            # 5. Evaluate models
            self.evaluate_models()

            # 6. Create visualizations
            self.create_visualizations()

            # 7. Save models
            saved_files = self.save_models()

            # 8. Generate summary report
            summary = self._generate_summary_report()

            print("\n🎉 PIPELINE COMPLETED SUCCESSFULLY!")
            print("=" * 60)
            print(f"📊 Best Model: {summary['best_model']}")
            print(f"📈 Best F1-Score: {summary['best_f1_score']:.4f}")
            print(f"💾 Results saved to: {self.results_dir}")

            return {
                'summary': summary,
                'saved_files': saved_files,
                'models': self.models,
                'evaluation_results': self.evaluation_results
            }

        except Exception as e:
            print(f"❌ Pipeline failed with error: {str(e)}")
            raise

    def _generate_summary_report(self):
        """Generate a summary report of the pipeline results"""
        best_model_name = max(self.evaluation_results.keys(),
                             key=lambda x: self.evaluation_results[x]['f1_score'])
        best_results = self.evaluation_results[best_model_name]

        summary = {
            'timestamp': datetime.now().isoformat(),
            'dataset_info': {
                'total_samples': len(self.raw_data),
                'features_used': self.feature_names,
                'num_classes': len(self.label_encoder.classes_),
                'class_names': list(self.label_encoder.classes_)
            },
            'best_model': best_model_name,
            'best_f1_score': best_results['f1_score'],
            'all_model_results': {
                name: {
                    'accuracy': results['accuracy'],
                    'precision': results['precision'],
                    'recall': results['recall'],
                    'f1_score': results['f1_score']
                }
                for name, results in self.evaluation_results.items()
            }
        }

        # Save summary report
        summary_path = f'{self.results_dir}/pipeline_summary_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)

        return summary


def create_demo_data():
    """
    Create synthetic demo data if no real data is available

    Returns:
        pd.DataFrame: Synthetic well log data with lithology labels
    """
    print("🔧 Creating synthetic demo data...")

    np.random.seed(42)
    n_samples = 5000

    # Define lithology types and their typical log characteristics
    lithologies = {
        'Sandstone': {'GR': (30, 80), 'RHOB': (2.0, 2.4), 'NPHI': (0.05, 0.25),
                     'RDEP': (10, 1000), 'DTC': (80, 120), 'PEF': (1.8, 3.0)},
        'Shale': {'GR': (80, 200), 'RHOB': (2.2, 2.8), 'NPHI': (0.15, 0.45),
                 'RDEP': (1, 20), 'DTC': (100, 200), 'PEF': (2.8, 3.5)},
        'Limestone': {'GR': (10, 60), 'RHOB': (2.4, 2.8), 'NPHI': (0.0, 0.15),
                     'RDEP': (50, 2000), 'DTC': (50, 90), 'PEF': (4.5, 5.5)},
        'Dolomite': {'GR': (10, 50), 'RHOB': (2.6, 2.9), 'NPHI': (0.0, 0.10),
                    'RDEP': (100, 5000), 'DTC': (45, 80), 'PEF': (2.8, 3.2)},
        'Coal': {'GR': (20, 100), 'RHOB': (1.2, 1.8), 'NPHI': (0.25, 0.60),
                'RDEP': (100, 10000), 'DTC': (120, 300), 'PEF': (0.2, 0.8)}
    }

    data = []

    for i in range(n_samples):
        # Random lithology selection
        lith = np.random.choice(list(lithologies.keys()))
        lith_props = lithologies[lith]

        # Generate log values with some noise
        row = {
            'WELL': f'DEMO_WELL_{(i // 1000) + 1}',
            'DEPTH_MD': 1500 + i * 0.5,
            'FORCE_2020_LITHOFACIES_LITHOLOGY': lith
        }

        for log_type, (min_val, max_val) in lith_props.items():
            # Add realistic noise and correlations
            base_value = np.random.uniform(min_val, max_val)
            noise = np.random.normal(0, (max_val - min_val) * 0.05)
            row[log_type] = max(0, base_value + noise)

        data.append(row)

    df = pd.DataFrame(data)

    # Add some missing values to make it realistic
    for col in ['GR', 'RHOB', 'NPHI', 'RDEP', 'DTC', 'PEF']:
        missing_mask = np.random.random(len(df)) < 0.02  # 2% missing
        df.loc[missing_mask, col] = np.nan

    return df


def main():
    """
    Main function to run the lithology classification pipeline
    """
    print("🪨 LITHOLOGY CLASSIFICATION ML PIPELINE")
    print("=" * 60)
    print("🎯 Objective: Predict lithology classes from well log data")
    print("🔬 Features: GR, RHOB, NPHI, RDEP, DTC, PEF")
    print("🤖 Models: Random Forest & XGBoost")
    print("=" * 60)

    # Initialize pipeline
    pipeline = LithologyMLPipeline()

    try:
        # Check if real data exists
        if not os.path.exists("litho_data") or not glob.glob("litho_data/*.csv"):
            print("⚠️  No real data found in litho_data directory")
            print("🔧 Creating synthetic demo data...")

            # Create demo data
            demo_df = create_demo_data()
            os.makedirs("litho_data", exist_ok=True)
            demo_df.to_csv("litho_data/demo_synthetic_data.csv", index=False)
            print(f"✅ Created demo data: litho_data/demo_synthetic_data.csv")

        # Run the complete pipeline
        results = pipeline.run_complete_pipeline()

        # Display final summary
        print("\n📋 FINAL SUMMARY")
        print("=" * 40)
        summary = results['summary']
        print(f"🏆 Best Model: {summary['best_model']}")
        print(f"📊 Dataset: {summary['dataset_info']['total_samples']:,} samples")
        print(f"🔢 Features: {len(summary['dataset_info']['features_used'])}")
        print(f"🪨 Lithologies: {summary['dataset_info']['num_classes']}")
        print(f"📈 Best F1-Score: {summary['best_f1_score']:.4f}")

        print(f"\n📁 All results saved to: model_results/")
        print(f"🎨 Visualizations: model_results/visualizations/")

        # Demonstration of inference
        print(f"\n🔮 DEMONSTRATION: Predicting on new data")
        if 'demo_synthetic_data.csv' in glob.glob("litho_data/*.csv"):
            demo_data = pd.read_csv("litho_data/demo_synthetic_data.csv").head(10)
            predictions = pipeline.predict_new_data(demo_data)

            print(f"📊 Sample predictions:")
            for i, (pred, prob) in enumerate(zip(predictions['predictions'],
                                               predictions['probabilities'])):
                max_prob = np.max(prob)
                print(f"   Sample {i+1}: {pred} (confidence: {max_prob:.3f})")

        print(f"\n🎉 PIPELINE COMPLETED SUCCESSFULLY!")
        print(f"💡 Ready for presentation to project mentor!")

        return results

    except Exception as e:
        print(f"❌ Error in pipeline execution: {str(e)}")
        raise


if __name__ == "__main__":
    results = main()
