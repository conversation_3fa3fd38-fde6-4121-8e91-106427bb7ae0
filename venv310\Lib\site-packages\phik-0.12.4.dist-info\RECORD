phik-0.12.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
phik-0.12.4.dist-info/METADATA,sha256=-3-ndLTNCvYdyJhisgm36YAK3fFSTGP2OgP_l2ifs24,5643
phik-0.12.4.dist-info/RECORD,,
phik-0.12.4.dist-info/WHEEL,sha256=dIUwsCkD2xS7N7asGQ7gd0g7EFe3ibY0LR24JBgh4xQ,105
phik-0.12.4.dist-info/entry_points.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
phik-0.12.4.dist-info/licenses/LICENSE,sha256=imCB8WtAfeZEnVuLIbuGUrwahcJ2Ygl2dojZzB-hrk8,803
phik-0.12.4.dist-info/licenses/NOTICE,sha256=QA4v7F2TyRM2XX2zxlOTfMO3S3F2FRKyeFgJMicEUxw,913
phik/__init__.py,sha256=VhxW02HSNygBL5vOS9iU5uZdddd2DwHjCba7WUkBDuU,694
phik/__pycache__/__init__.cpython-310.pyc,,
phik/__pycache__/betainc.cpython-310.pyc,,
phik/__pycache__/binning.cpython-310.pyc,,
phik/__pycache__/bivariate.cpython-310.pyc,,
phik/__pycache__/data_quality.cpython-310.pyc,,
phik/__pycache__/definitions.cpython-310.pyc,,
phik/__pycache__/entry_points.cpython-310.pyc,,
phik/__pycache__/outliers.cpython-310.pyc,,
phik/__pycache__/phik.cpython-310.pyc,,
phik/__pycache__/report.cpython-310.pyc,,
phik/__pycache__/resources.cpython-310.pyc,,
phik/__pycache__/significance.cpython-310.pyc,,
phik/__pycache__/simulation.cpython-310.pyc,,
phik/__pycache__/statistics.cpython-310.pyc,,
phik/__pycache__/utils.cpython-310.pyc,,
phik/betainc.py,sha256=JvXvalkDtcpEz71kTF6oz-ndJ74U3lECYJRVy1n_K0E,4106
phik/binning.py,sha256=P_4DWqi2UZosf181mdWS54uAeHPDUjO5q_fblU0cIkM,11559
phik/bivariate.py,sha256=lDMYzzzpio9-dOfwSvgOQfQIpWANbSIt2x1xdU6iE88,9314
phik/data/fake_insurance_data.csv.gz,sha256=lFWlse_pHznj2jo6ZA-r_Xv25pwpyPhowkKRCPu-r9w,44032
phik/data_quality.py,sha256=jbJ1b_dxRgWzXFxVjgzPd7Hko-ZocvDzQf94xFdApaA,5281
phik/decorators/__init__.py,sha256=shSB6BCpSDYKt74U5JZ60Qupea_eBtzBLYK_HXW_aPw,92
phik/decorators/__pycache__/__init__.cpython-310.pyc,,
phik/decorators/__pycache__/pandas.cpython-310.pyc,,
phik/decorators/pandas.py,sha256=zfoyPth0Hoi293r70-ZWv2yg9lAQxcnE9QDmYtnD7-k,1301
phik/definitions.py,sha256=6C1pXFgbIPwxrfTJIdwpF86I1uSBm7haVKFqX65q8Rc,522
phik/entry_points.py,sha256=j2-0Od4Me0XTC6xzt9hTmzQZY4Wa3pZk0y8ZqT1GRNA,865
phik/lib/_phik_simulation_core.cp310-win_amd64.pyd,sha256=cJxn3gJj1d-CrU-1PQwKAQ3qokyw6WY1LA8y9rcR2OA,117760
phik/notebooks/phik_tutorial_advanced.ipynb,sha256=t05o9MtlGLuPSdZoAgy9CsrstWRT2r3j_eZkYviV6uE,72746
phik/notebooks/phik_tutorial_basic.ipynb,sha256=jCW6R9Jm5T04Lzwy26hn3z5BimOILBg6x9lfpcyY5ew,731285
phik/notebooks/phik_tutorial_spark.ipynb,sha256=bjFtVmdh02q3KJ69ecMYs2vNLbzWPdVwRsWopFSOIK0,5392
phik/outliers.py,sha256=bHdNYXCpo5VtV-L0bL0cJRppo5wc17OqKvGc2cWHy9s,30003
phik/phik.py,sha256=mikoCMyBMV3i7v44fl47fFMA8dB8XQjWplsvxWmdoZw,25592
phik/report.py,sha256=DhNn1B8JJF6PM3W0TCRiFfgRn5Y_tkPQ7BPWfFtpMv0,18954
phik/resources.py,sha256=tf6cLHOorydLkE2kiB67EH8s6ZbwEDTGRPpQsKJaq4Q,2243
phik/significance.py,sha256=J2jzoRD_l0timfN99sJbSHk3_GZZO4wC-yESlosARTw,21673
phik/simcore/__init__.py,sha256=_-3LK85oJRIw_tfeK81_fIRRZZCCsrnf_EJ-qQYdcVs,479
phik/simcore/__pycache__/__init__.cpython-310.pyc,,
phik/simcore/asa159.cpp,sha256=NffdR2LjJQ2HG60iOMaT8z2GDjBWh_YTIoR0txZgwAM,16227
phik/simcore/asa159.hpp,sha256=3kgQyig_M6lm_H_riujGAOtvZoUqmpwB7D4eWiEq1uo,944
phik/simcore/bindings.cpp,sha256=HPIXqaMTkw_xfxVAUh-9uR81Cf2nvpBx96W2jQDk2m0,128
phik/simcore/simulation.hpp,sha256=9N9kDpjakvBbgf6ez0pSOzDZqtZ4B9uOdbLKToZMrLM,2623
phik/simulation.py,sha256=HpllP6aSZuH4wnwOr3F6j2bRYnS-qoF36W5oB5g0yp0,6694
phik/statistics.py,sha256=A5Oyam_eTjdL0NWtTumowjfvLHkAdBmOuJb7tq8sHAk,5701
phik/utils.py,sha256=L5SD7KuJM2IuplXqV0MNZKyr71kpT3fZItDEdUrlfgg,3005
