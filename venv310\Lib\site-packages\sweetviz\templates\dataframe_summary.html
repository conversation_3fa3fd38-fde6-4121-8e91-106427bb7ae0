<div class="container-feature-summary" id="summary-df" data-detail-div="detail-df">
    <div class="pos-dataframe-summary text-label">
        <span class="top-header"></span>
        <div class="pos-dataframe-summary-title-source text-dataframe-title color-source">{{ dataframe.summary_source.name }}</div>
        {% if dataframe.summary_compare is not none: %}
            <div class="pos-dataframe-summary-title-compare text-dataframe-title color-compare">{{ dataframe.summary_compare.name }}</div>
        {% else %}
            <div class="pos-dataframe-summary-title-compare-none text-dataframe-title-small color-title-disabled">NO COMPARISON TARGET</div>
        {% endif %}
        <div class="pos-dataframe-summary-rows">
            <div class="dataframe-summary-row row-colored">
                <div class="pos-df-summary-source color-source">{{ dataframe.summary_source.num_rows }}</div>
                <div class="pos-df-summary-center">ROWS</div>
                <div class="pos-df-summary-compare color-compare">{{ dataframe.summary_compare.num_rows if dataframe.summary_compare is not none }}</div>
            </div>
             <div class="dataframe-summary-row">
                <div class="pos-df-summary-source color-source">{{ dataframe.summary_source.duplicates.number }}</div>
                <div class="pos-df-summary-center">DUPLICATES</div>
                <div class="pos-df-summary-compare color-compare">{{ dataframe.summary_compare.duplicates.number if dataframe.summary_compare is not none  }}</div>
            </div>
            <div class="dataframe-summary-row row-colored">
                <div class="pos-df-summary-source color-source">{{ dataframe.summary_source.memory_total|fmt_RAM }}</div>
                <div class="pos-df-summary-center">RAM</div>
                <div class="pos-df-summary-compare color-compare">{{ dataframe.summary_compare.memory_total|fmt_RAM if dataframe.summary_compare is not none  }}</div>
            </div>
            <div class="dataframe-summary-row row">
                <div class="pos-df-summary-source color-source">
                    {% if dataframe.summary_source.num_skipped_columns > 0 %}
                        (INCL. {{ dataframe.summary_source.num_skipped_columns }} SKIPPED)
                    {% endif %}
                    {{ dataframe.summary_source.num_columns }}
                </div>
                <div class="pos-df-summary-center">FEATURES</div>
                <div class="pos-df-summary-compare color-compare">
                    {% if dataframe.summary_compare is not none %}
                        {% if dataframe.summary_compare.num_skipped_columns is not none %}
                            {{ dataframe.summary_compare.num_columns }}
                            {% if dataframe.summary_compare.num_skipped_columns > 0 %}
                                (INCL. {{ dataframe.summary_compare.num_skipped_columns }} SKIPPED)
                            {% endif %}
                        {% endif %}
                    {% endif %}
                </div>
            </div>
            <div class="dataframe-summary-row row-colored">
                <div class="pos-df-summary-source color-source">{{ dataframe.summary_source.num_cat }}</div>
                <div class="pos-df-summary-center">CATEGORICAL</div>
                <div class="pos-df-summary-compare color-compare">{{ dataframe.summary_compare.num_cat if dataframe.summary_compare is not none  }}</div>
            </div>
             <div class="dataframe-summary-row ">
                <div class="pos-df-summary-source color-source">{{ dataframe.summary_source.num_numerical }}</div>
                <div class="pos-df-summary-center">NUMERICAL</div>
                <div class="pos-df-summary-compare color-compare">{{ dataframe.summary_compare.num_numerical if dataframe.summary_compare is not none  }}</div>
            </div>
            <div class="dataframe-summary-row row-colored">
                <div class="pos-df-summary-source color-source">{{ dataframe.summary_source.num_text }}</div>
                <div class="pos-df-summary-center">TEXT</div>
                <div class="pos-df-summary-compare color-compare">{{ dataframe.summary_compare.num_text if dataframe.summary_compare is not none  }}</div>
            </div>
            {% if dataframe.summary_compare is not none: %}
                {% if dataframe.summary_compare.num_cmp_not_in_source > 0 %}
                    <div class="dataframe-summary-row">
                        <div class="pos-df-summary-source color-source"></div>
                        <div class="pos-df-summary-center"> </div>
                        <div class="pos-df-summary-compare color-compare">{{ dataframe.summary_compare.num_cmp_not_in_source }}
                            <span class="color-normal" style="font-size: 10px;"> (IGNORED: NOT IN SOURCE DATASET)</span></div>
                    </div>
                {% endif %}
            {% endif %}
            <button class="button-assoc color-source button-border-source size-df-summary-button pos-df-summary-button-assoc-source" id="button-summary-associations-source"
                data-detail-div="df-assoc-source">ASSOCIATIONS</button >
            {% if dataframe.summary_compare is not none %}
                <button class="button-assoc color-compare button-border-compare size-df-summary-button pos-df-summary-button-assoc-compare" id="button-summary-associations-compare"
                    data-detail-div="df-assoc-compare">ASSOCIATIONS</button >
            {% endif %}
        </div>
    </div>
</div>



