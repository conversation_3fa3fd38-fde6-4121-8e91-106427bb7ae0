2015-10-18 18:21:13,646 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 18:21:13,703 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 18:21:13,703 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-18 18:21:13,717 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-18 18:21:13,717 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445144423722_0020, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@271ff531)
2015-10-18 18:21:13,814 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-18 18:21:14,017 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445144423722_0020
2015-10-18 18:21:14,469 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-18 18:21:14,906 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-18 18:21:14,924 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@2eedd32f
2015-10-18 18:21:14,943 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@5b904247
2015-10-18 18:21:14,962 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=140928608, maxSingleShuffleLimit=35232152, mergeThreshold=93012888, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-18 18:21:14,964 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0020_r_000000_1000 Thread started: EventFetcher for fetching Map Completion Events
2015-10-18 18:21:14,970 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0020_r_000000_1000: Got 1 new map-outputs
2015-10-18 18:21:14,970 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:21:14,970 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:21:14,993 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0020&reduce=0&map=attempt_1445144423722_0020_m_000003_0 sent hash and received reply
2015-10-18 18:21:14,995 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0020_m_000003_0: Shuffling to disk since 60515787 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:21:15,000 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0020_m_000003_0 decomp: 60515787 len: 60515791 to DISK
2015-10-18 18:21:15,675 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515791 bytes from map-output for attempt_1445144423722_0020_m_000003_0
2015-10-18 18:21:15,683 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 712ms
2015-10-18 18:22:51,141 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:22:51,141 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:22:51,142 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0020_r_000000_1000: Got 2 new map-outputs
2015-10-18 18:22:51,150 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0020&reduce=0&map=attempt_1445144423722_0020_m_000004_1000 sent hash and received reply
2015-10-18 18:22:51,151 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0020_m_000004_1000: Shuffling to disk since 60513765 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:22:51,160 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0020_m_000004_1000 decomp: 60513765 len: 60513769 to DISK
2015-10-18 18:22:51,808 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60513769 bytes from map-output for attempt_1445144423722_0020_m_000004_1000
2015-10-18 18:22:51,883 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 742ms
2015-10-18 18:22:51,883 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:22:51,883 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:22:51,890 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0020&reduce=0&map=attempt_1445144423722_0020_m_000000_1000 sent hash and received reply
2015-10-18 18:22:51,892 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0020_m_000000_1000: Shuffling to disk since 60515385 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:22:51,895 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0020_m_000000_1000 decomp: 60515385 len: 60515389 to DISK
2015-10-18 18:22:52,412 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515389 bytes from map-output for attempt_1445144423722_0020_m_000000_1000
2015-10-18 18:22:52,418 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 534ms
2015-10-18 18:23:10,181 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0020_r_000000_1000: Got 1 new map-outputs
2015-10-18 18:23:10,181 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:23:10,182 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:23:10,190 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0020&reduce=0&map=attempt_1445144423722_0020_m_000001_1000 sent hash and received reply
2015-10-18 18:23:10,191 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0020_m_000001_1000: Shuffling to disk since 60515836 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:23:10,199 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0020_m_000001_1000 decomp: 60515836 len: 60515840 to DISK
2015-10-18 18:23:11,001 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515840 bytes from map-output for attempt_1445144423722_0020_m_000001_1000
2015-10-18 18:23:11,017 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 836ms
2015-10-18 18:23:13,190 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0020_r_000000_1000: Got 1 new map-outputs
2015-10-18 18:23:13,190 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:23:13,191 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:23:13,199 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0020&reduce=0&map=attempt_1445144423722_0020_m_000002_1000 sent hash and received reply
2015-10-18 18:23:13,200 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0020_m_000002_1000: Shuffling to disk since 60514392 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:23:13,208 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0020_m_000002_1000 decomp: 60514392 len: 60514396 to DISK
2015-10-18 18:23:13,961 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60514396 bytes from map-output for attempt_1445144423722_0020_m_000002_1000
2015-10-18 18:23:14,014 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 824ms
2015-10-18 18:23:20,204 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0020_r_000000_1000: Got 1 new map-outputs
2015-10-18 18:23:20,204 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:23:20,204 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:23:20,212 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0020&reduce=0&map=attempt_1445144423722_0020_m_000008_1000 sent hash and received reply
2015-10-18 18:23:20,213 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0020_m_000008_1000: Shuffling to disk since 60516677 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:23:20,220 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0020_m_000008_1000 decomp: 60516677 len: 60516681 to DISK
2015-10-18 18:23:20,880 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60516681 bytes from map-output for attempt_1445144423722_0020_m_000008_1000
2015-10-18 18:23:20,893 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 689ms
2015-10-18 18:23:40,245 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0020_r_000000_1000: Got 1 new map-outputs
2015-10-18 18:23:40,245 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:23:40,246 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:23:40,253 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0020&reduce=0&map=attempt_1445144423722_0020_m_000005_1001 sent hash and received reply
2015-10-18 18:23:40,254 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0020_m_000005_1001: Shuffling to disk since 60514806 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:23:40,260 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0020_m_000005_1001 decomp: 60514806 len: 60514810 to DISK
2015-10-18 18:23:40,900 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60514810 bytes from map-output for attempt_1445144423722_0020_m_000005_1001
2015-10-18 18:23:40,913 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 667ms
2015-10-18 18:24:01,288 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0020_r_000000_1000: Got 1 new map-outputs
2015-10-18 18:24:01,288 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:24:01,288 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:24:01,297 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0020&reduce=0&map=attempt_1445144423722_0020_m_000006_1001 sent hash and received reply
2015-10-18 18:24:01,298 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0020_m_000006_1001: Shuffling to disk since 60515100 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:24:01,305 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0020_m_000006_1001 decomp: 60515100 len: 60515104 to DISK
2015-10-18 18:24:02,065 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515104 bytes from map-output for attempt_1445144423722_0020_m_000006_1001
2015-10-18 18:24:02,078 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 791ms
2015-10-18 18:24:22,331 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0020_r_000000_1000: Got 1 new map-outputs
2015-10-18 18:24:22,331 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning 04DN8IQ.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:24:22,331 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to 04DN8IQ.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:24:22,346 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0020&reduce=0&map=attempt_1445144423722_0020_m_000009_1000 sent hash and received reply
2015-10-18 18:24:22,350 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0020_m_000009_1000: Shuffling to disk since 56695786 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:24:22,357 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0020_m_000009_1000 decomp: 56695786 len: 56695790 to DISK
2015-10-18 18:24:30,658 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 56695790 bytes from map-output for attempt_1445144423722_0020_m_000009_1000
2015-10-18 18:24:30,672 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: 04DN8IQ.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 8340ms
2015-10-18 18:26:02,533 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0020_r_000000_1000: Got 1 new map-outputs
2015-10-18 18:26:02,533 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning 04DN8IQ.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:26:02,534 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to 04DN8IQ.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:26:02,549 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0020&reduce=0&map=attempt_1445144423722_0020_m_000007_1000 sent hash and received reply
2015-10-18 18:26:02,551 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0020_m_000007_1000: Shuffling to disk since 60517368 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:26:02,558 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0020_m_000007_1000 decomp: 60517368 len: 60517372 to DISK
2015-10-18 18:26:13,548 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60517372 bytes from map-output for attempt_1445144423722_0020_m_000007_1000
2015-10-18 18:26:13,561 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: 04DN8IQ.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 11028ms
2015-10-18 18:26:13,562 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: EventFetcher is interrupted.. Returning
2015-10-18 18:26:13,566 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: finalMerge called with 0 in-memory map-outputs and 10 on-disk map-outputs
2015-10-18 18:26:13,581 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 10 files, 601334942 bytes from disk
2015-10-18 18:26:13,582 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 0 segments, 0 bytes from memory into reduce
2015-10-18 18:26:13,589 INFO [main] org.apache.hadoop.mapred.Merger: Merging 10 sorted segments
2015-10-18 18:26:13,612 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 10 segments left of total size: 601334842 bytes
2015-10-18 18:26:13,814 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: mapred.skip.on is deprecated. Instead, use mapreduce.job.skiprecords
2015-10-18 18:27:15,125 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445144423722_0020_r_000000_1000 is done. And is in the process of committing
2015-10-18 18:27:15,163 INFO [main] org.apache.hadoop.mapred.Task: Task attempt_1445144423722_0020_r_000000_1000 is allowed to commit now
2015-10-18 18:27:15,171 INFO [main] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Saved output of task 'attempt_1445144423722_0020_r_000000_1000' to hdfs://msra-sa-41:9000/pageout/out1/_temporary/2/task_1445144423722_0020_r_000000
2015-10-18 18:27:15,200 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445144423722_0020_r_000000_1000' done.
