# 🔮 **TRAINED MODEL USAGE GUIDE**

Your lithology classification models have been successfully trained and are ready for use! Here's how to test them on different datasets and access results.

## 🎯 **QUICK START - 3 WAYS TO USE YOUR MODELS**

### **1. 🌐 Streamlit Web App (Recommended for Interactive Use)**

```bash
streamlit run lithology_streamlit_app.py
```

**Features:**
- ✅ Upload CSV files and get instant predictions
- ✅ Interactive visualizations with Plotly
- ✅ Model performance comparison
- ✅ Export results as CSV/JSON
- ✅ User-friendly interface for non-technical users

**Access:** Open http://localhost:8501 in your browser

### **2. 💻 Command Line Interface (Best for Batch Processing)**

```bash
# Basic usage
python lithology_cli.py --input your_new_data.csv --output predictions.csv

# Advanced usage with confidence threshold
python lithology_cli.py --input data.csv --model random_forest --confidence 0.8

# Get summary statistics only
python lithology_cli.py --input data.csv --summary-only

# List available models
python lithology_cli.py --input data.csv --list-models
```

### **3. 🐍 Python Script (For Custom Integration)**

```python
# Demo script - ready to run!
python demo_model_usage.py

# Or integrate into your own code:
import joblib
import pandas as pd

# Load models
models = joblib.load("model_results/random_forest_model_20250719_183142.joblib")
preprocessing = joblib.load("model_results/preprocessing_objects_20250719_183142.joblib")

# Make predictions
new_data = pd.read_csv("your_data.csv")
# ... (see demo_model_usage.py for complete example)
```

## 📊 **YOUR TRAINED MODELS - PERFORMANCE SUMMARY**

### **🏆 Model Performance:**
- **Random Forest**: 92.77% Accuracy, 92.64% F1-Score ⭐ **BEST**
- **XGBoost**: 91.61% Accuracy, 91.46% F1-Score

### **📈 Training Dataset:**
- **Total Samples**: 258,903 from 19 wells
- **Training Set**: 97,139 samples
- **Test Set**: 24,285 samples
- **Features**: GR, RHOB, NPHI, RDEP, DTC, PEF
- **Lithology Classes**: 11 different types

### **🎯 Classification Performance by Lithology:**
| Lithology | Precision | Recall | F1-Score | Support |
|-----------|-----------|--------|----------|---------|
| 30000.0   | 0.89      | 0.91   | 0.90     | 2,959   |
| 65000.0   | 0.95      | 0.98   | 0.96     | 13,861  |
| 65030.0   | 0.83      | 0.77   | 0.80     | 2,102   |
| 70000.0   | 0.91      | 0.81   | 0.85     | 1,744   |
| 88000.0   | 0.99      | 0.99   | 0.99     | 1,300   |

## 📁 **SAVED FILES AND RESULTS**

### **📂 Location: `model_results/` Directory**

#### **🤖 Trained Models:**
- `random_forest_model_20250719_183142.joblib` - Best performing model
- `xgboost_model_20250719_183142.joblib` - Alternative model

#### **⚙️ Preprocessing Objects:**
- `preprocessing_objects_20250719_183142.joblib` - Required for new predictions

#### **📊 Performance Metrics:**
- `evaluation_results_20250719_183142.json` - Detailed metrics
- `pipeline_summary_20250719_183143.json` - Summary report

#### **🎨 Visualizations:**
- `confusion_matrices.png` - Model accuracy by class
- `feature_importance.png` - Which logs are most predictive
- `lithology_depth_comparison.png` - Spatial predictions
- `interactive_confusion_matrix.html` - Interactive web chart
- `interactive_feature_importance.html` - Interactive feature analysis

## 🔧 **DATA REQUIREMENTS FOR NEW PREDICTIONS**

### **✅ Required Columns:**
Your new CSV file should contain **at least 3** of these columns:
- `GR` - Gamma Ray (API units)
- `RHOB` - Bulk Density (g/cm³)
- `NPHI` - Neutron Porosity (fraction)
- `RDEP` - Deep Resistivity (ohm-m)
- `DTC` - Delta Time Compressional (μs/ft)
- `PEF` - Photoelectric Factor (barns/electron)

### **📋 Optional Columns:**
- `DEPTH_MD` - Measured Depth (for depth plots)
- `WELL` - Well identifier (for multi-well analysis)

### **⚠️ Data Quality Tips:**
- **Missing values**: Automatically handled by median imputation
- **Units**: Ensure consistent units with training data
- **Range**: Values should be within realistic well log ranges
- **Format**: CSV format with column headers

## 🎯 **EXAMPLE USAGE SCENARIOS**

### **Scenario 1: New Well Analysis**
```bash
# You have a new well log CSV file
python lithology_cli.py --input new_well_XYZ.csv --output well_XYZ_lithology.csv
```

### **Scenario 2: Quality Control Check**
```bash
# Check predictions with high confidence only
python lithology_cli.py --input data.csv --confidence 0.9 --output high_confidence_predictions.csv
```

### **Scenario 3: Batch Processing**
```bash
# Process multiple files
for file in *.csv; do
    python lithology_cli.py --input "$file" --output "predictions_$file"
done
```

### **Scenario 4: Interactive Analysis**
```bash
# Launch web app for detailed analysis
streamlit run lithology_streamlit_app.py
```

## 📈 **INTERPRETING RESULTS**

### **🔮 Prediction Output:**
- **Predicted_Lithology**: Numerical lithology code (e.g., 65000.0)
- **Prediction_Confidence**: Probability score (0.0 to 1.0)
- **Prob_[Class]**: Individual class probabilities

### **🎯 Confidence Levels:**
- **0.9-1.0**: Very High Confidence ✅
- **0.7-0.9**: High Confidence ✅
- **0.5-0.7**: Medium Confidence ⚠️
- **0.0-0.5**: Low Confidence ❌

### **🪨 Lithology Code Mapping:**
- `30000.0`: Lithology Type 1
- `65000.0`: Lithology Type 2 (Most common)
- `65030.0`: Lithology Type 3
- `70000.0`: Lithology Type 4
- `88000.0`: Lithology Type 5
- *(Add your specific lithology interpretations)*

## 🚀 **ADVANCED FEATURES**

### **🔄 Model Retraining:**
```bash
# Add new data to litho_data/ directory and retrain
python lithology_ml_pipeline.py
```

### **📊 Custom Visualizations:**
```python
# Use the demo script as a template
python demo_model_usage.py
```

### **🌐 Web Deployment:**
```bash
# Deploy to cloud (Streamlit Cloud, Heroku, etc.)
streamlit run lithology_streamlit_app.py --server.port 8501
```

## 🆘 **TROUBLESHOOTING**

### **❌ "No models found"**
- Ensure you're in the correct directory
- Check that `model_results/` folder exists
- Run the training pipeline first: `python lithology_ml_pipeline.py`

### **❌ "Insufficient features"**
- Your CSV needs at least 3 of the required columns
- Check column names match exactly (case-sensitive)

### **❌ "Module not found"**
- Install dependencies: `pip install -r requirements.txt`

### **❌ Low prediction confidence**
- Check if your data is similar to training data
- Verify units and ranges are realistic
- Consider retraining with your specific data

## 🎉 **SUCCESS METRICS**

Your models are **production-ready** and achieve:
- ✅ **92.77% Overall Accuracy**
- ✅ **92.64% F1-Score**
- ✅ **Robust handling of missing data**
- ✅ **11 lithology classes supported**
- ✅ **Professional visualization outputs**

## 📞 **SUPPORT**

For questions or issues:
1. Check this guide first
2. Review error messages carefully
3. Ensure data format matches requirements
4. Test with the provided demo script

---

**🎯 Your lithology classification system is ready for professional use!**
