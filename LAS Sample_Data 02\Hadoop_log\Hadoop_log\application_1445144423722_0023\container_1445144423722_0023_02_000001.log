2015-10-18 18:17:06,237 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445144423722_0023_000002
2015-10-18 18:17:06,672 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-18 18:17:06,672 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 23 cluster_timestamp: 1445144423722 } attemptId: 2 } keyId: -127633188)
2015-10-18 18:17:06,818 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-18 18:17:07,473 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-18 18:17:07,526 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-18 18:17:07,553 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-18 18:17:07,554 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-18 18:17:07,555 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-18 18:17:07,556 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-18 18:17:07,557 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-18 18:17:07,563 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-18 18:17:07,564 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-18 18:17:07,565 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-18 18:17:07,604 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:17:07,626 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:17:07,647 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:17:07,656 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-18 18:17:07,659 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Recovery is enabled. Will try to recover from previous life on best effort basis.
2015-10-18 18:17:07,680 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:17:07,684 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Previous history file is at hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0023/job_1445144423722_0023_1.jhist
2015-10-18 18:17:07,700 WARN [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Unable to parse prior job history, aborting recovery
java.io.IOException: Incompatible event log version: null
	at org.apache.hadoop.mapreduce.jobhistory.EventReader.<init>(EventReader.java:71)
	at org.apache.hadoop.mapreduce.jobhistory.JobHistoryParser.parse(JobHistoryParser.java:139)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.parsePreviousJobHistory(MRAppMaster.java:1183)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.processRecovery(MRAppMaster.java:1152)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.serviceStart(MRAppMaster.java:1016)
	at org.apache.hadoop.service.AbstractService.start(AbstractService.java:193)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster$4.run(MRAppMaster.java:1500)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.initAndStartAppMaster(MRAppMaster.java:1496)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.main(MRAppMaster.java:1429)
2015-10-18 18:17:07,724 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-18 18:17:07,727 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Previous history file is at hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0023/job_1445144423722_0023_1.jhist
2015-10-18 18:17:07,728 WARN [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Could not parse the old history file. Will not have old AMinfos 
java.io.IOException: Incompatible event log version: null
	at org.apache.hadoop.mapreduce.jobhistory.EventReader.<init>(EventReader.java:71)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.readJustAMInfos(MRAppMaster.java:1229)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.processRecovery(MRAppMaster.java:1156)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.serviceStart(MRAppMaster.java:1016)
	at org.apache.hadoop.service.AbstractService.start(AbstractService.java:193)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster$4.run(MRAppMaster.java:1500)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.initAndStartAppMaster(MRAppMaster.java:1496)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.main(MRAppMaster.java:1429)
2015-10-18 18:17:07,778 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-18 18:17:08,007 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 18:17:08,057 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 18:17:08,057 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-18 18:17:08,063 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445144423722_0023 to jobTokenSecretManager
2015-10-18 18:17:08,194 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445144423722_0023 because: not enabled; too many maps; too much input;
2015-10-18 18:17:08,212 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445144423722_0023 = 1256521728. Number of splits = 10
2015-10-18 18:17:08,213 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445144423722_0023 = 1
2015-10-18 18:17:08,213 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0023Job Transitioned from NEW to INITED
2015-10-18 18:17:08,214 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445144423722_0023.
2015-10-18 18:17:08,247 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-18 18:17:08,257 INFO [Socket Reader #1 for port 30340] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 30340
2015-10-18 18:17:08,279 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-18 18:17:08,280 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-18 18:17:08,304 INFO [IPC Server listener on 30340] org.apache.hadoop.ipc.Server: IPC Server listener on 30340: starting
2015-10-18 18:17:08,305 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-41.fareast.corp.microsoft.com/**************:30340
2015-10-18 18:17:08,371 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-18 18:17:08,375 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-18 18:17:08,386 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-18 18:17:08,391 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-18 18:17:08,391 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-18 18:17:08,394 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-18 18:17:08,394 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-18 18:17:08,404 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 30347
2015-10-18 18:17:08,404 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-18 18:17:08,437 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\4\Jetty_0_0_0_0_30347_mapreduce____8s9dk0\webapp
2015-10-18 18:17:08,577 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:30347
2015-10-18 18:17:08,577 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 30347
2015-10-18 18:17:08,884 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-18 18:17:08,887 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445144423722_0023
2015-10-18 18:17:08,888 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-18 18:17:08,891 INFO [Socket Reader #1 for port 30358] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 30358
2015-10-18 18:17:08,896 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-18 18:17:08,896 INFO [IPC Server listener on 30358] org.apache.hadoop.ipc.Server: IPC Server listener on 30358: starting
2015-10-18 18:17:08,914 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-18 18:17:08,914 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-18 18:17:08,914 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-18 18:17:08,959 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at MSRA-SA-41/**************:8030
2015-10-18 18:17:09,018 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-18 18:17:09,018 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-18 18:17:09,022 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-18 18:17:09,023 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-18 18:17:09,029 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0023Job Transitioned from INITED to SETUP
2015-10-18 18:17:09,030 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-18 18:17:09,039 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0023Job Transitioned from SETUP to RUNNING
2015-10-18 18:17:09,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,056 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,058 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:17:09,058 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,058 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,058 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:17:09,059 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,059 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,059 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:17:09,059 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,059 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,059 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:17:09,059 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,059 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:17:09,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:17:09,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:17:09,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,061 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,061 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:17:09,061 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,061 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,061 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:17:09,061 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,061 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:09,061 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:17:09,062 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-18 18:17:09,063 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000000_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:17:09,063 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000001_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:17:09,063 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000002_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:17:09,063 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000003_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:17:09,063 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000004_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:17:09,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000005_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:17:09,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000006_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:17:09,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000007_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:17:09,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000008_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:17:09,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000009_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:17:09,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_r_000000_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:17:09,065 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-18 18:17:09,071 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-18 18:17:09,105 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445144423722_0023, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0023/job_1445144423722_0023_2.jhist
2015-10-18 18:17:10,020 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-18 18:17:10,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-11> knownNMs=3
2015-10-18 18:17:10,069 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-11>
2015-10-18 18:17:10,070 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:11,079 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 3
2015-10-18 18:17:11,080 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000002 to attempt_1445144423722_0023_m_000000_1000
2015-10-18 18:17:11,081 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000003 to attempt_1445144423722_0023_m_000001_1000
2015-10-18 18:17:11,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000004 to attempt_1445144423722_0023_m_000002_1000
2015-10-18 18:17:11,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-18 18:17:11,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:11,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:3 RackLocal:0
2015-10-18 18:17:11,155 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:11,186 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0023/job.jar
2015-10-18 18:17:11,191 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0023/job.xml
2015-10-18 18:17:11,193 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-18 18:17:11,193 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-18 18:17:11,194 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-18 18:17:11,291 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000000_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:11,298 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:11,299 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000001_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:11,300 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:11,301 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000002_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:11,304 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000002 taskAttempt attempt_1445144423722_0023_m_000000_1000
2015-10-18 18:17:11,304 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000003 taskAttempt attempt_1445144423722_0023_m_000001_1000
2015-10-18 18:17:11,304 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000004 taskAttempt attempt_1445144423722_0023_m_000002_1000
2015-10-18 18:17:11,309 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000002_1000
2015-10-18 18:17:11,309 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000001_1000
2015-10-18 18:17:11,309 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000000_1000
2015-10-18 18:17:11,311 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:17:11,351 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:17:11,354 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:17:11,439 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000000_1000 : 13562
2015-10-18 18:17:11,439 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000001_1000 : 13562
2015-10-18 18:17:11,439 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000002_1000 : 13562
2015-10-18 18:17:11,443 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000002_1000] using containerId: [container_1445144423722_0023_02_000004 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:7109]
2015-10-18 18:17:11,449 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000002_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:11,449 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000000_1000] using containerId: [container_1445144423722_0023_02_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:7109]
2015-10-18 18:17:11,450 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000000_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:11,450 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000001_1000] using containerId: [container_1445144423722_0023_02_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:7109]
2015-10-18 18:17:11,451 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000001_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:11,451 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000002
2015-10-18 18:17:11,452 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:11,452 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000000
2015-10-18 18:17:11,452 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:11,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000001
2015-10-18 18:17:11,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:12,085 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:6144, vCores:-14> knownNMs=3
2015-10-18 18:17:12,085 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-18 18:17:12,085 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:13,086 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-14>
2015-10-18 18:17:13,086 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:13,367 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:17:13,385 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000003 asked for a task
2015-10-18 18:17:13,385 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000003 given task: attempt_1445144423722_0023_m_000001_1000
2015-10-18 18:17:13,551 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:17:13,552 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:17:13,563 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000004 asked for a task
2015-10-18 18:17:13,563 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000004 given task: attempt_1445144423722_0023_m_000002_1000
2015-10-18 18:17:13,563 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000002 asked for a task
2015-10-18 18:17:13,564 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000002 given task: attempt_1445144423722_0023_m_000000_1000
2015-10-18 18:17:14,090 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-18 18:17:14,091 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000006 to attempt_1445144423722_0023_m_000003_1000
2015-10-18 18:17:14,091 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:14,091 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:14,092 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000005 to attempt_1445144423722_0023_m_000004_1000
2015-10-18 18:17:14,092 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-15>
2015-10-18 18:17:14,092 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:14,092 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:4 RackLocal:1
2015-10-18 18:17:14,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000003_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:14,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:14,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000004_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:14,094 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000006 taskAttempt attempt_1445144423722_0023_m_000003_1000
2015-10-18 18:17:14,094 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000005 taskAttempt attempt_1445144423722_0023_m_000004_1000
2015-10-18 18:17:14,094 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000003_1000
2015-10-18 18:17:14,094 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000004_1000
2015-10-18 18:17:14,094 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:17:14,095 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:17:14,111 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000003_1000 : 13562
2015-10-18 18:17:14,111 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000003_1000] using containerId: [container_1445144423722_0023_02_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:17:14,111 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000003_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:14,112 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000003
2015-10-18 18:17:14,112 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:14,127 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000004_1000 : 13562
2015-10-18 18:17:14,128 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000004_1000] using containerId: [container_1445144423722_0023_02_000005 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:17:14,128 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000004_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:14,128 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000004
2015-10-18 18:17:14,128 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:15,093 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:5120, vCores:-15> knownNMs=3
2015-10-18 18:17:15,093 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-15>
2015-10-18 18:17:15,093 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:16,095 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-15>
2015-10-18 18:17:16,095 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:16,377 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:17:16,394 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000005 asked for a task
2015-10-18 18:17:16,395 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000005 given task: attempt_1445144423722_0023_m_000004_1000
2015-10-18 18:17:17,097 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-15>
2015-10-18 18:17:17,098 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:17,421 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:17:17,439 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000006 asked for a task
2015-10-18 18:17:17,439 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000006 given task: attempt_1445144423722_0023_m_000003_1000
2015-10-18 18:17:18,101 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:17:18,101 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:18,101 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000007 to attempt_1445144423722_0023_m_000005_1000
2015-10-18 18:17:18,101 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-16>
2015-10-18 18:17:18,101 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:18,101 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:4 RackLocal:2
2015-10-18 18:17:18,102 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:18,102 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000005_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:18,103 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000007 taskAttempt attempt_1445144423722_0023_m_000005_1000
2015-10-18 18:17:18,103 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000005_1000
2015-10-18 18:17:18,103 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:17:18,137 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000005_1000 : 13562
2015-10-18 18:17:18,137 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000005_1000] using containerId: [container_1445144423722_0023_02_000007 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:17:18,137 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000005_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:18,138 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000005
2015-10-18 18:17:18,138 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:19,104 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-17> knownNMs=3
2015-10-18 18:17:19,104 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-17>
2015-10-18 18:17:19,104 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:20,105 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-17>
2015-10-18 18:17:20,105 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:20,407 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.1066108
2015-10-18 18:17:20,612 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.10660437
2015-10-18 18:17:20,979 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.10635664
2015-10-18 18:17:21,107 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:17:21,108 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:21,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000008 to attempt_1445144423722_0023_m_000006_1000
2015-10-18 18:17:21,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-18>
2015-10-18 18:17:21,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:21,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:4 RackLocal:3
2015-10-18 18:17:21,108 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:21,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000006_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:21,110 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000008 taskAttempt attempt_1445144423722_0023_m_000006_1000
2015-10-18 18:17:21,110 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000006_1000
2015-10-18 18:17:21,110 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:17:21,504 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000006_1000 : 13562
2015-10-18 18:17:21,505 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000006_1000] using containerId: [container_1445144423722_0023_02_000008 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:17:21,505 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000006_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:21,505 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000006
2015-10-18 18:17:21,505 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:22,039 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:17:22,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-18> knownNMs=3
2015-10-18 18:17:22,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-18>
2015-10-18 18:17:22,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:22,177 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000007 asked for a task
2015-10-18 18:17:22,177 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000007 given task: attempt_1445144423722_0023_m_000005_1000
2015-10-18 18:17:23,110 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-18>
2015-10-18 18:17:23,110 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:23,448 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.1066108
2015-10-18 18:17:23,644 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.10660437
2015-10-18 18:17:24,010 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.10635664
2015-10-18 18:17:24,111 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-18>
2015-10-18 18:17:24,111 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:24,352 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.070555195
2015-10-18 18:17:24,609 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.106493875
2015-10-18 18:17:25,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:17:25,114 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:25,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000009 to attempt_1445144423722_0023_m_000007_1000
2015-10-18 18:17:25,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-19>
2015-10-18 18:17:25,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:25,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:4 RackLocal:4
2015-10-18 18:17:25,115 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:25,115 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000007_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:25,116 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000009 taskAttempt attempt_1445144423722_0023_m_000007_1000
2015-10-18 18:17:25,116 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000007_1000
2015-10-18 18:17:25,116 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:17:25,786 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000007_1000 : 13562
2015-10-18 18:17:25,786 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000007_1000] using containerId: [container_1445144423722_0023_02_000009 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:17:25,786 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000007_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:25,787 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000007
2015-10-18 18:17:25,787 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:26,116 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-19> knownNMs=3
2015-10-18 18:17:26,116 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-19>
2015-10-18 18:17:26,116 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:26,469 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.1066108
2015-10-18 18:17:26,666 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.10660437
2015-10-18 18:17:27,033 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.10635664
2015-10-18 18:17:27,119 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:17:27,119 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:27,120 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000010 to attempt_1445144423722_0023_m_000008_1000
2015-10-18 18:17:27,120 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:27,120 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:27,120 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:4 RackLocal:5
2015-10-18 18:17:27,120 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:17:27,121 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000008_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:17:27,121 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000010 taskAttempt attempt_1445144423722_0023_m_000008_1000
2015-10-18 18:17:27,121 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000008_1000
2015-10-18 18:17:27,122 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:17:27,606 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000008_1000 : 13562
2015-10-18 18:17:27,607 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000008_1000] using containerId: [container_1445144423722_0023_02_000010 on NM: [04DN8IQ.fareast.corp.microsoft.com:54883]
2015-10-18 18:17:27,607 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000008_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:17:27,607 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000008
2015-10-18 18:17:27,607 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:17:27,614 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.106493875
2015-10-18 18:17:27,759 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.10627687
2015-10-18 18:17:28,121 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:17:28,121 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:28,121 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:28,937 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:17:29,064 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000008 asked for a task
2015-10-18 18:17:29,064 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000008 given task: attempt_1445144423722_0023_m_000006_1000
2015-10-18 18:17:29,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:29,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:29,489 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.19211523
2015-10-18 18:17:29,687 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.19212553
2015-10-18 18:17:30,053 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.19158794
2015-10-18 18:17:30,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:30,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:30,627 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.106493875
2015-10-18 18:17:31,125 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:31,125 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:31,256 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.10680563
2015-10-18 18:17:32,126 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:32,126 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:32,510 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.19211523
2015-10-18 18:17:32,708 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.19212553
2015-10-18 18:17:32,860 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:17:32,960 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000009 asked for a task
2015-10-18 18:17:32,961 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000009 given task: attempt_1445144423722_0023_m_000007_1000
2015-10-18 18:17:33,074 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.19158794
2015-10-18 18:17:33,127 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:33,127 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:33,640 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.11880998
2015-10-18 18:17:34,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:34,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:34,645 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.10680563
2015-10-18 18:17:35,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:35,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:35,530 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.19211523
2015-10-18 18:17:35,532 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:17:35,576 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000010 asked for a task
2015-10-18 18:17:35,576 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000010 given task: attempt_1445144423722_0023_m_000008_1000
2015-10-18 18:17:35,731 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.19212553
2015-10-18 18:17:36,096 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.19158794
2015-10-18 18:17:36,130 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:36,130 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:36,646 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.1906546
2015-10-18 18:17:37,132 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:37,132 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:37,832 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.10680563
2015-10-18 18:17:38,134 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:38,134 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:38,550 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.261069
2015-10-18 18:17:38,751 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.2701764
2015-10-18 18:17:39,116 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.27091494
2015-10-18 18:17:39,135 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:39,135 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:39,657 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.19209063
2015-10-18 18:17:40,136 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:40,136 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:41,137 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:41,137 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:41,443 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.10680563
2015-10-18 18:17:41,570 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.27776006
2015-10-18 18:17:41,585 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.06773994
2015-10-18 18:17:41,770 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.27772525
2015-10-18 18:17:42,136 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.27696857
2015-10-18 18:17:42,138 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:42,138 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:42,676 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.19209063
2015-10-18 18:17:43,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:43,139 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:44,140 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:44,140 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:44,495 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.022145262
2015-10-18 18:17:44,590 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.27776006
2015-10-18 18:17:44,647 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.10680563
2015-10-18 18:17:44,769 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.08337434
2015-10-18 18:17:44,801 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.27772525
2015-10-18 18:17:45,141 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:45,141 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:45,155 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.27696857
2015-10-18 18:17:45,687 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.19209063
2015-10-18 18:17:46,143 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:46,143 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:46,353 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.024209471
2015-10-18 18:17:47,144 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:47,144 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:47,629 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.3295689
2015-10-18 18:17:47,766 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.04038147
2015-10-18 18:17:47,846 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.30043155
2015-10-18 18:17:47,982 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.10680563
2015-10-18 18:17:48,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:48,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:48,175 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.34181052
2015-10-18 18:17:48,241 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.10056353
2015-10-18 18:17:48,628 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.05762807
2015-10-18 18:17:48,687 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.2659592
2015-10-18 18:17:49,146 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:49,146 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:49,893 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.055098753
2015-10-18 18:17:50,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:50,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:50,650 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.36319977
2015-10-18 18:17:50,867 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.36317363
2015-10-18 18:17:51,148 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:51,148 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:51,194 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.3624012
2015-10-18 18:17:51,255 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.06318097
2015-10-18 18:17:51,616 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.10680563
2015-10-18 18:17:51,695 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.27765483
2015-10-18 18:17:51,914 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.10685723
2015-10-18 18:17:52,150 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:52,150 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:52,303 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.09358419
2015-10-18 18:17:53,151 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:53,151 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:53,507 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.08935026
2015-10-18 18:17:53,670 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.36319977
2015-10-18 18:17:53,887 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.36317363
2015-10-18 18:17:54,152 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:54,152 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:54,216 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.3624012
2015-10-18 18:17:54,707 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.27765483
2015-10-18 18:17:54,910 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.09596569
2015-10-18 18:17:55,153 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:55,153 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:55,318 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.10680563
2015-10-18 18:17:55,583 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.10685723
2015-10-18 18:17:56,031 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.106881365
2015-10-18 18:17:56,155 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:56,155 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:56,703 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.3829837
2015-10-18 18:17:56,864 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.10681946
2015-10-18 18:17:56,918 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.36317363
2015-10-18 18:17:57,156 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:57,156 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:57,236 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.41115487
2015-10-18 18:17:57,720 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.27765483
2015-10-18 18:17:58,157 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:58,157 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:58,324 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.106964506
2015-10-18 18:17:58,793 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.10680563
2015-10-18 18:17:59,158 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:17:59,158 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:17:59,236 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.10685723
2015-10-18 18:17:59,487 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.106881365
2015-10-18 18:17:59,722 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.448704
2015-10-18 18:17:59,937 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.44859612
2015-10-18 18:18:00,160 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:18:00,160 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:18:00,266 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.44789755
2015-10-18 18:18:00,285 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.10681946
2015-10-18 18:18:00,734 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.32271892
2015-10-18 18:18:01,162 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:18:01,162 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:18:01,756 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.106964506
2015-10-18 18:18:02,163 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:18:02,163 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:18:02,259 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.10680563
2015-10-18 18:18:02,729 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.10685723
2015-10-18 18:18:02,743 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.448704
2015-10-18 18:18:02,959 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.44859612
2015-10-18 18:18:03,071 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.106881365
2015-10-18 18:18:03,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:18:03,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000011 to attempt_1445144423722_0023_m_000009_1000
2015-10-18 18:18:03,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:18:03,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-18 18:18:03,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:5 RackLocal:5
2015-10-18 18:18:03,166 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:18:03,167 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000009_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:18:03,168 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000011 taskAttempt attempt_1445144423722_0023_m_000009_1000
2015-10-18 18:18:03,168 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000009_1000
2015-10-18 18:18:03,168 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:18:03,182 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000009_1000 : 13562
2015-10-18 18:18:03,183 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000009_1000] using containerId: [container_1445144423722_0023_02_000011 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:18:03,183 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000009_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:18:03,183 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000009
2015-10-18 18:18:03,183 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:18:03,288 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.44789755
2015-10-18 18:18:03,737 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.36323506
2015-10-18 18:18:03,849 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.10681946
2015-10-18 18:18:04,168 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:18:05,211 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.106964506
2015-10-18 18:18:05,615 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.1417238
2015-10-18 18:18:05,762 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.448704
2015-10-18 18:18:05,978 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.44859612
2015-10-18 18:18:06,311 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.10685723
2015-10-18 18:18:06,318 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.4670369
2015-10-18 18:18:06,437 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:18:06,457 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000011 asked for a task
2015-10-18 18:18:06,458 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000011 given task: attempt_1445144423722_0023_m_000009_1000
2015-10-18 18:18:06,553 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.106881365
2015-10-18 18:18:06,744 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.36323506
2015-10-18 18:18:07,473 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.10681946
2015-10-18 18:18:08,739 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.106964506
2015-10-18 18:18:08,800 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.53274024
2015-10-18 18:18:09,009 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.5099864
2015-10-18 18:18:09,248 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.18065788
2015-10-18 18:18:09,348 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.53341997
2015-10-18 18:18:09,758 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.36323506
2015-10-18 18:18:09,880 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.10685723
2015-10-18 18:18:10,052 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.106881365
2015-10-18 18:18:11,122 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.10681946
2015-10-18 18:18:11,819 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.53425497
2015-10-18 18:18:12,027 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.5342037
2015-10-18 18:18:12,208 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.106964506
2015-10-18 18:18:12,379 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.53341997
2015-10-18 18:18:12,771 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.36323506
2015-10-18 18:18:12,799 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.19242907
2015-10-18 18:18:13,330 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.10685723
2015-10-18 18:18:13,457 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.106881365
2015-10-18 18:18:13,707 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 0.295472
2015-10-18 18:18:14,536 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.10681946
2015-10-18 18:18:14,846 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.53425497
2015-10-18 18:18:15,054 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.5342037
2015-10-18 18:18:15,398 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.53341997
2015-10-18 18:18:15,613 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.106964506
2015-10-18 18:18:15,784 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.4426403
2015-10-18 18:18:16,207 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.19242907
2015-10-18 18:18:16,722 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 0.295472
2015-10-18 18:18:16,770 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.10685723
2015-10-18 18:18:16,864 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.106881365
2015-10-18 18:18:17,875 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.53425497
2015-10-18 18:18:17,898 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.10681946
2015-10-18 18:18:18,085 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.5464766
2015-10-18 18:18:18,428 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.6094704
2015-10-18 18:18:18,787 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.4486067
2015-10-18 18:18:19,132 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.106964506
2015-10-18 18:18:19,723 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 0.295472
2015-10-18 18:18:19,738 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.19242907
2015-10-18 18:18:20,352 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.10685723
2015-10-18 18:18:20,378 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.106881365
2015-10-18 18:18:20,895 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.6197233
2015-10-18 18:18:21,105 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.6196791
2015-10-18 18:18:21,301 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.10681946
2015-10-18 18:18:21,449 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.61898744
2015-10-18 18:18:21,800 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.4486067
2015-10-18 18:18:22,576 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.106964506
2015-10-18 18:18:22,726 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 0.3758051
2015-10-18 18:18:23,237 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.19242907
2015-10-18 18:18:23,725 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.106881365
2015-10-18 18:18:23,777 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.12966284
2015-10-18 18:18:23,915 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.6197233
2015-10-18 18:18:24,124 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.6196791
2015-10-18 18:18:24,473 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.61898744
2015-10-18 18:18:24,721 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.10681946
2015-10-18 18:18:24,801 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.4486067
2015-10-18 18:18:25,737 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 0.5323719
2015-10-18 18:18:26,036 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.106964506
2015-10-18 18:18:26,759 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.19242907
2015-10-18 18:18:26,933 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.6197233
2015-10-18 18:18:27,143 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.6196791
2015-10-18 18:18:27,162 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.106881365
2015-10-18 18:18:27,164 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.16627291
2015-10-18 18:18:27,492 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.6243935
2015-10-18 18:18:27,803 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.5343203
2015-10-18 18:18:28,361 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.6243935
2015-10-18 18:18:28,449 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.10681946
2015-10-18 18:18:28,738 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 0.5323719
2015-10-18 18:18:29,502 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.106964506
2015-10-18 18:18:29,763 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.6196791
2015-10-18 18:18:29,805 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.6197233
2015-10-18 18:18:29,966 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.667
2015-10-18 18:18:30,163 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.667
2015-10-18 18:18:30,274 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.19242907
2015-10-18 18:18:30,512 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.667
2015-10-18 18:18:30,623 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.13710395
2015-10-18 18:18:30,816 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.5343203
2015-10-18 18:18:30,822 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.19247705
2015-10-18 18:18:31,744 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 0.5323719
2015-10-18 18:18:31,785 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.12653348
2015-10-18 18:18:32,942 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.1226636
2015-10-18 18:18:32,987 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.667
2015-10-18 18:18:33,183 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.667
2015-10-18 18:18:33,533 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.667
2015-10-18 18:18:33,818 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.5343203
2015-10-18 18:18:33,912 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.19242907
2015-10-18 18:18:34,339 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.17657046
2015-10-18 18:18:34,618 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.19247705
2015-10-18 18:18:34,760 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 0.63695824
2015-10-18 18:18:35,107 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 0.63695824
2015-10-18 18:18:35,252 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.16219527
2015-10-18 18:18:36,016 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.667
2015-10-18 18:18:36,203 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.667
2015-10-18 18:18:36,551 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.68548715
2015-10-18 18:18:36,635 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.14958821
2015-10-18 18:18:36,818 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.5343203
2015-10-18 18:18:37,514 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.19242907
2015-10-18 18:18:37,770 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 0.667
2015-10-18 18:18:38,054 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.19258286
2015-10-18 18:18:38,165 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.19247705
2015-10-18 18:18:38,820 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.19255035
2015-10-18 18:18:39,035 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.69176465
2015-10-18 18:18:39,231 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.69422716
2015-10-18 18:18:39,580 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.7174375
2015-10-18 18:18:39,836 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.6199081
2015-10-18 18:18:40,177 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.18597676
2015-10-18 18:18:40,769 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 0.667
2015-10-18 18:18:41,019 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.19242907
2015-10-18 18:18:41,593 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.19258286
2015-10-18 18:18:41,725 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.19247705
2015-10-18 18:18:42,078 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.7234634
2015-10-18 18:18:42,249 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.72045106
2015-10-18 18:18:42,257 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.19255035
2015-10-18 18:18:42,606 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.7493662
2015-10-18 18:18:42,849 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.6199081
2015-10-18 18:18:43,672 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.19266446
2015-10-18 18:18:43,771 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 0.71240354
2015-10-18 18:18:44,462 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.19242907
2015-10-18 18:18:45,107 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.7544412
2015-10-18 18:18:45,279 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.74191016
2015-10-18 18:18:45,389 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.19247705
2015-10-18 18:18:45,398 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.19258286
2015-10-18 18:18:45,634 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.76983005
2015-10-18 18:18:45,787 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.19255035
2015-10-18 18:18:45,864 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.6199081
2015-10-18 18:18:46,773 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 0.8157521
2015-10-18 18:18:47,147 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.19266446
2015-10-18 18:18:47,925 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.23011988
2015-10-18 18:18:48,128 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.79374814
2015-10-18 18:18:48,300 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.7765448
2015-10-18 18:18:48,667 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.8105599
2015-10-18 18:18:48,837 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.19258286
2015-10-18 18:18:48,838 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.19247705
2015-10-18 18:18:48,863 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.6392585
2015-10-18 18:18:49,353 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.19255035
2015-10-18 18:18:49,536 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.6392585
2015-10-18 18:18:49,787 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 0.931399
2015-10-18 18:18:50,726 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.19266446
2015-10-18 18:18:51,169 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.8277889
2015-10-18 18:18:51,341 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.81215996
2015-10-18 18:18:51,490 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.26789373
2015-10-18 18:18:51,696 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.8451546
2015-10-18 18:18:51,870 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.667
2015-10-18 18:18:52,493 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000009_1000 is : 1.0
2015-10-18 18:18:52,496 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0023_m_000009_1000
2015-10-18 18:18:52,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000009_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:18:52,498 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000011 taskAttempt attempt_1445144423722_0023_m_000009_1000
2015-10-18 18:18:52,499 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000009_1000
2015-10-18 18:18:52,500 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:18:52,520 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.19247705
2015-10-18 18:18:52,566 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000009_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:18:52,582 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0023_m_000009_1000
2015-10-18 18:18:52,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:18:52,588 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-18 18:18:52,788 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.19258286
2015-10-18 18:18:52,994 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0023_m_000005
2015-10-18 18:18:52,994 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:18:52,995 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0023_m_000005
2015-10-18 18:18:52,995 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:18:52,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:18:52,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000005_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:18:53,012 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.19255035
2015-10-18 18:18:53,229 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:5 RackLocal:5
2015-10-18 18:18:53,232 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:18:53,232 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-20>
2015-10-18 18:18:53,232 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-18 18:18:53,232 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:10240, vCores:-10> finalMapResourceLimit:<memory:9216, vCores:-9> finalReduceResourceLimit:<memory:1024, vCores:-1> netScheduledMapResource:<memory:11264, vCores:11> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-18 18:18:53,232 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-18 18:18:53,232 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:5 RackLocal:5
2015-10-18 18:18:54,162 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.19266446
2015-10-18 18:18:54,212 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.8581594
2015-10-18 18:18:54,244 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=1 release= 0 newContainers=1 finishedContainers=1 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:18:54,245 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000011
2015-10-18 18:18:54,246 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:18:54,247 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000009_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:18:54,247 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000012 to attempt_1445144423722_0023_m_000005_1001
2015-10-18 18:18:54,247 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:6 RackLocal:5
2015-10-18 18:18:54,248 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:18:54,249 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000005_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:18:54,250 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000012 taskAttempt attempt_1445144423722_0023_m_000005_1001
2015-10-18 18:18:54,250 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000005_1001
2015-10-18 18:18:54,250 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:18:54,267 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000005_1001 : 13562
2015-10-18 18:18:54,268 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000005_1001] using containerId: [container_1445144423722_0023_02_000012 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:18:54,268 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000005_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:18:54,269 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000005
2015-10-18 18:18:54,383 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.8322409
2015-10-18 18:18:54,715 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.8667992
2015-10-18 18:18:54,885 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.667
2015-10-18 18:18:54,945 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.2781602
2015-10-18 18:18:55,250 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:18:55,947 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.19247705
2015-10-18 18:18:56,194 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.19258286
2015-10-18 18:18:56,342 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.19255035
2015-10-18 18:18:57,256 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.888392
2015-10-18 18:18:57,425 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.853822
2015-10-18 18:18:57,467 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:18:57,490 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000012 asked for a task
2015-10-18 18:18:57,491 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000012 given task: attempt_1445144423722_0023_m_000005_1001
2015-10-18 18:18:57,631 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.19266446
2015-10-18 18:18:57,742 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.89024657
2015-10-18 18:18:57,907 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.667
2015-10-18 18:18:58,378 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.2781602
2015-10-18 18:18:59,372 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.19247705
2015-10-18 18:18:59,540 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.19258286
2015-10-18 18:18:59,794 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.19255035
2015-10-18 18:19:00,295 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.9198113
2015-10-18 18:19:00,464 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.8795042
2015-10-18 18:19:00,766 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.91756034
2015-10-18 18:19:00,920 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.67207044
2015-10-18 18:19:01,138 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.19266446
2015-10-18 18:19:01,941 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.2781602
2015-10-18 18:19:02,750 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.19247705
2015-10-18 18:19:02,989 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.19258286
2015-10-18 18:19:03,308 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.19255035
2015-10-18 18:19:03,329 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.9534636
2015-10-18 18:19:03,496 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.9050561
2015-10-18 18:19:03,789 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.94694227
2015-10-18 18:19:03,931 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.70000356
2015-10-18 18:19:04,519 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.19266446
2015-10-18 18:19:04,902 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.10685723
2015-10-18 18:19:05,313 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.2781602
2015-10-18 18:19:06,193 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.23341306
2015-10-18 18:19:06,348 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 0.986163
2015-10-18 18:19:06,514 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.9347879
2015-10-18 18:19:06,566 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.19258286
2015-10-18 18:19:06,804 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.19255035
2015-10-18 18:19:06,830 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 0.97725654
2015-10-18 18:19:06,936 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.7394125
2015-10-18 18:19:07,712 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000001_1000 is : 1.0
2015-10-18 18:19:07,713 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0023_m_000001_1000
2015-10-18 18:19:07,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000001_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:19:07,714 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000003 taskAttempt attempt_1445144423722_0023_m_000001_1000
2015-10-18 18:19:07,714 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000001_1000
2015-10-18 18:19:07,714 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:19:07,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000001_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:19:07,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0023_m_000001_1000
2015-10-18 18:19:07,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:19:07,724 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-18 18:19:07,922 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.10685723
2015-10-18 18:19:07,996 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0023_m_000006
2015-10-18 18:19:07,996 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:19:07,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0023_m_000006
2015-10-18 18:19:07,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:19:07,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:19:07,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000006_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:19:08,020 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.19266446
2015-10-18 18:19:08,273 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:6 RackLocal:5
2015-10-18 18:19:08,274 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:19:08,873 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.2781602
2015-10-18 18:19:08,941 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000000_1000 is : 1.0
2015-10-18 18:19:08,943 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0023_m_000000_1000
2015-10-18 18:19:08,943 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000000_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:19:08,943 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000002 taskAttempt attempt_1445144423722_0023_m_000000_1000
2015-10-18 18:19:08,943 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000000_1000
2015-10-18 18:19:08,944 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:19:08,955 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000000_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:19:08,955 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0023_m_000000_1000
2015-10-18 18:19:08,955 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:19:08,956 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-18 18:19:09,275 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:6 RackLocal:5
2015-10-18 18:19:09,280 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000003
2015-10-18 18:19:09,280 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:19:09,280 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000001_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:19:09,280 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-18 18:19:09,281 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000013 to attempt_1445144423722_0023_r_000000_1000
2015-10-18 18:19:09,281 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:6 RackLocal:5
2015-10-18 18:19:09,298 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:19:09,299 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_r_000000_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:19:09,300 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000013 taskAttempt attempt_1445144423722_0023_r_000000_1000
2015-10-18 18:19:09,300 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_r_000000_1000
2015-10-18 18:19:09,300 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:19:09,323 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_r_000000_1000 : 13562
2015-10-18 18:19:09,324 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_r_000000_1000] using containerId: [container_1445144423722_0023_02_000013 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:7109]
2015-10-18 18:19:09,324 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_r_000000_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:19:09,325 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_r_000000
2015-10-18 18:19:09,325 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-18 18:19:09,554 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 0.9640079
2015-10-18 18:19:09,757 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.2692351
2015-10-18 18:19:09,947 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.77055085
2015-10-18 18:19:10,033 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.21494864
2015-10-18 18:19:10,243 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.19255035
2015-10-18 18:19:10,284 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=1 release= 0 newContainers=1 finishedContainers=1 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:19:10,284 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000002
2015-10-18 18:19:10,284 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:19:10,284 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000000_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:19:10,284 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000014 to attempt_1445144423722_0023_m_000006_1001
2015-10-18 18:19:10,284 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:7 RackLocal:5
2015-10-18 18:19:10,284 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:19:10,285 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000006_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:19:10,285 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000014 taskAttempt attempt_1445144423722_0023_m_000006_1001
2015-10-18 18:19:10,285 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000006_1001
2015-10-18 18:19:10,285 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:19:10,301 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000006_1001 : 13562
2015-10-18 18:19:10,301 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000006_1001] using containerId: [container_1445144423722_0023_02_000014 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:7109]
2015-10-18 18:19:10,301 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000006_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:19:10,301 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000006
2015-10-18 18:19:10,932 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.10685723
2015-10-18 18:19:11,175 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:19:11,187 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_r_000013 asked for a task
2015-10-18 18:19:11,187 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_r_000013 given task: attempt_1445144423722_0023_r_000000_1000
2015-10-18 18:19:11,285 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-20> knownNMs=3
2015-10-18 18:19:11,465 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.19266446
2015-10-18 18:19:12,275 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 0 maxEvents 10000
2015-10-18 18:19:12,301 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:19:12,357 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.2781602
2015-10-18 18:19:12,409 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000014 asked for a task
2015-10-18 18:19:12,409 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000014 given task: attempt_1445144423722_0023_m_000006_1001
2015-10-18 18:19:12,420 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000002_1000 is : 1.0
2015-10-18 18:19:12,421 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0023_m_000002_1000
2015-10-18 18:19:12,422 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000002_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:19:12,422 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000004 taskAttempt attempt_1445144423722_0023_m_000002_1000
2015-10-18 18:19:12,422 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000002_1000
2015-10-18 18:19:12,422 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:19:12,434 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000002_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:19:12,435 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0023_m_000002_1000
2015-10-18 18:19:12,435 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:19:12,435 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-18 18:19:12,948 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.80104685
2015-10-18 18:19:13,241 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.27813601
2015-10-18 18:19:13,282 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-18 18:19:13,287 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:7 RackLocal:5
2015-10-18 18:19:13,597 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.25110963
2015-10-18 18:19:13,862 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.22026767
2015-10-18 18:19:13,951 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.10685723
2015-10-18 18:19:14,284 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:14,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000004
2015-10-18 18:19:14,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:7 RackLocal:5
2015-10-18 18:19:14,289 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000002_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:19:15,243 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.20524845
2015-10-18 18:19:15,285 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:15,968 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.8323632
2015-10-18 18:19:15,993 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.2781602
2015-10-18 18:19:16,286 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:16,822 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.27813601
2015-10-18 18:19:16,970 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.110884696
2015-10-18 18:19:17,185 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.27811313
2015-10-18 18:19:17,297 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:17,436 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.25562406
2015-10-18 18:19:18,228 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.13333334
2015-10-18 18:19:18,298 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:18,709 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.23974903
2015-10-18 18:19:18,981 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.8674461
2015-10-18 18:19:19,299 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:19,535 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.2781602
2015-10-18 18:19:19,625 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.106964506
2015-10-18 18:19:19,982 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.18863669
2015-10-18 18:19:20,300 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:20,331 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.27813601
2015-10-18 18:19:20,831 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.27811313
2015-10-18 18:19:20,988 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.27825075
2015-10-18 18:19:21,248 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.13333334
2015-10-18 18:19:21,301 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:21,998 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.9053851
2015-10-18 18:19:22,302 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:22,364 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.2746376
2015-10-18 18:19:22,656 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.106964506
2015-10-18 18:19:22,996 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.19247705
2015-10-18 18:19:22,997 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0023_m_000004
2015-10-18 18:19:22,997 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:19:22,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0023_m_000004
2015-10-18 18:19:22,998 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:19:22,998 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:19:22,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000004_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:19:23,112 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.2781602
2015-10-18 18:19:23,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:7 RackLocal:5
2015-10-18 18:19:23,300 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:5120, vCores:-15> knownNMs=3
2015-10-18 18:19:23,303 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:23,925 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.27813601
2015-10-18 18:19:24,270 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.13333334
2015-10-18 18:19:24,302 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.27811313
2015-10-18 18:19:24,302 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:19:24,302 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000015 to attempt_1445144423722_0023_m_000004_1001
2015-10-18 18:19:24,303 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:8 RackLocal:5
2015-10-18 18:19:24,303 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:19:24,303 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000004_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:19:24,304 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000015 taskAttempt attempt_1445144423722_0023_m_000004_1001
2015-10-18 18:19:24,304 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000004_1001
2015-10-18 18:19:24,304 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:19:24,304 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:24,315 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000004_1001 : 13562
2015-10-18 18:19:24,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000004_1001] using containerId: [container_1445144423722_0023_02_000015 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:19:24,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000004_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:19:24,316 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000004
2015-10-18 18:19:24,456 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.27825075
2015-10-18 18:19:25,022 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.94934744
2015-10-18 18:19:25,306 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:25,307 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:6144, vCores:-14> knownNMs=3
2015-10-18 18:19:25,699 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.11643466
2015-10-18 18:19:25,786 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.2783809
2015-10-18 18:19:26,013 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.19247705
2015-10-18 18:19:26,148 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:19:26,163 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000015 asked for a task
2015-10-18 18:19:26,163 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000015 given task: attempt_1445144423722_0023_m_000004_1001
2015-10-18 18:19:26,308 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:26,403 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.28138998
2015-10-18 18:19:27,305 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.13333334
2015-10-18 18:19:27,307 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.27813601
2015-10-18 18:19:27,311 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:27,634 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.27811313
2015-10-18 18:19:27,772 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.27825075
2015-10-18 18:19:28,030 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 0.9925916
2015-10-18 18:19:28,312 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:28,648 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000003_1000 is : 1.0
2015-10-18 18:19:28,649 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0023_m_000003_1000
2015-10-18 18:19:28,650 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000003_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:19:28,650 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000006 taskAttempt attempt_1445144423722_0023_m_000003_1000
2015-10-18 18:19:28,650 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000003_1000
2015-10-18 18:19:28,650 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:19:28,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000003_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:19:28,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0023_m_000003_1000
2015-10-18 18:19:28,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:19:28,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-18 18:19:28,719 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.19266446
2015-10-18 18:19:29,026 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.19247705
2015-10-18 18:19:29,302 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.2783809
2015-10-18 18:19:29,311 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:8 RackLocal:5
2015-10-18 18:19:29,313 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 4 maxEvents 10000
2015-10-18 18:19:29,973 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.31949484
2015-10-18 18:19:30,314 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000006
2015-10-18 18:19:30,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:8 RackLocal:5
2015-10-18 18:19:30,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000003_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:19:30,315 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:30,335 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:19:30,710 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.27813601
2015-10-18 18:19:31,209 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.27811313
2015-10-18 18:19:31,259 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.27825075
2015-10-18 18:19:31,317 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:31,749 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.19266446
2015-10-18 18:19:32,037 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.2533788
2015-10-18 18:19:32,319 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:32,693 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.2783809
2015-10-18 18:19:32,943 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.10680563
2015-10-18 18:19:33,321 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:33,364 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:19:33,444 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.36042917
2015-10-18 18:19:34,238 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.27813601
2015-10-18 18:19:34,322 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:34,544 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.27811313
2015-10-18 18:19:34,581 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.27825075
2015-10-18 18:19:34,770 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.19266446
2015-10-18 18:19:35,043 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.27813601
2015-10-18 18:19:35,323 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:35,950 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.10680563
2015-10-18 18:19:36,036 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.2783809
2015-10-18 18:19:36,324 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:36,395 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:19:36,743 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.36388028
2015-10-18 18:19:37,325 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:37,631 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.27813601
2015-10-18 18:19:37,811 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.21464974
2015-10-18 18:19:37,999 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0023_m_000007
2015-10-18 18:19:37,999 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:19:37,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0023_m_000007
2015-10-18 18:19:37,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:19:38,000 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:19:38,000 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000007_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:19:38,044 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.27813601
2015-10-18 18:19:38,067 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.27811313
2015-10-18 18:19:38,189 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.27825075
2015-10-18 18:19:38,325 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:8 RackLocal:5
2015-10-18 18:19:38,327 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:38,328 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:8192, vCores:-12> knownNMs=3
2015-10-18 18:19:38,956 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.12661457
2015-10-18 18:19:39,329 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:39,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:19:39,333 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000016 to attempt_1445144423722_0023_m_000007_1001
2015-10-18 18:19:39,333 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:9 RackLocal:5
2015-10-18 18:19:39,334 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:19:39,336 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000007_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:19:39,336 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000016 taskAttempt attempt_1445144423722_0023_m_000007_1001
2015-10-18 18:19:39,336 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000007_1001
2015-10-18 18:19:39,337 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:19:39,355 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000007_1001 : 13562
2015-10-18 18:19:39,356 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000007_1001] using containerId: [container_1445144423722_0023_02_000016 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:19:39,356 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000007_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:19:39,356 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000007
2015-10-18 18:19:39,366 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.2783809
2015-10-18 18:19:39,435 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:19:40,087 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.36388028
2015-10-18 18:19:40,331 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:40,335 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-13> knownNMs=3
2015-10-18 18:19:40,842 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.2783809
2015-10-18 18:19:41,046 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.28443676
2015-10-18 18:19:41,164 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.3119912
2015-10-18 18:19:41,332 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:41,543 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.27811313
2015-10-18 18:19:41,754 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.27825075
2015-10-18 18:19:41,972 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.18489255
2015-10-18 18:19:42,335 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:42,409 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:19:42,432 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000016 asked for a task
2015-10-18 18:19:42,433 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000016 given task: attempt_1445144423722_0023_m_000007_1001
2015-10-18 18:19:42,454 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:19:42,833 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.2783809
2015-10-18 18:19:43,335 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:43,465 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.36388028
2015-10-18 18:19:43,861 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.2783809
2015-10-18 18:19:44,045 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.36390656
2015-10-18 18:19:44,337 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:44,650 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.3559714
2015-10-18 18:19:44,976 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.3014315
2015-10-18 18:19:44,982 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.19242907
2015-10-18 18:19:45,287 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.27825075
2015-10-18 18:19:45,337 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:45,473 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:19:46,017 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.2783809
2015-10-18 18:19:46,339 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:46,834 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.36388028
2015-10-18 18:19:46,891 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.2783809
2015-10-18 18:19:47,049 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.36390656
2015-10-18 18:19:47,339 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:47,984 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.19242907
2015-10-18 18:19:48,059 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.36390656
2015-10-18 18:19:48,341 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:48,478 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.34894583
2015-10-18 18:19:48,507 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:19:48,724 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.31063345
2015-10-18 18:19:49,344 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:49,477 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.2783809
2015-10-18 18:19:49,654 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.10681946
2015-10-18 18:19:49,929 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.30780083
2015-10-18 18:19:50,062 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.36390656
2015-10-18 18:19:50,231 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.36388028
2015-10-18 18:19:50,346 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:50,984 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.19397669
2015-10-18 18:19:51,347 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:51,364 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.36390656
2015-10-18 18:19:51,526 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:19:51,919 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.3637686
2015-10-18 18:19:52,348 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:52,362 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.3537619
2015-10-18 18:19:52,660 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.10681946
2015-10-18 18:19:52,869 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.32272944
2015-10-18 18:19:52,960 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.36404583
2015-10-18 18:19:53,000 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0023_m_000008
2015-10-18 18:19:53,000 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:19:53,000 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445144423722_0023_m_000008
2015-10-18 18:19:53,001 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:19:53,001 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:19:53,002 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000008_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-18 18:19:53,068 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.42281905
2015-10-18 18:19:53,350 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:53,352 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:9 RackLocal:5
2015-10-18 18:19:53,353 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-13> knownNMs=3
2015-10-18 18:19:53,776 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.36388028
2015-10-18 18:19:53,990 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.26463756
2015-10-18 18:19:54,351 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:54,355 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-18 18:19:54,355 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445144423722_0023_02_000017 to attempt_1445144423722_0023_m_000008_1001
2015-10-18 18:19:54,355 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:19:54,356 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-18 18:19:54,356 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000008_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-18 18:19:54,357 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445144423722_0023_02_000017 taskAttempt attempt_1445144423722_0023_m_000008_1001
2015-10-18 18:19:54,357 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445144423722_0023_m_000008_1001
2015-10-18 18:19:54,357 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:19:54,368 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445144423722_0023_m_000008_1001 : 13562
2015-10-18 18:19:54,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445144423722_0023_m_000008_1001] using containerId: [container_1445144423722_0023_02_000017 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-18 18:19:54,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000008_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-18 18:19:54,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445144423722_0023_m_000008
2015-10-18 18:19:54,549 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:19:54,824 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.36390656
2015-10-18 18:19:55,351 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:55,356 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445144423722_0023: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:6144, vCores:-14> knownNMs=3
2015-10-18 18:19:55,476 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.3637686
2015-10-18 18:19:55,675 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.10681946
2015-10-18 18:19:55,785 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.3638923
2015-10-18 18:19:55,990 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.36404583
2015-10-18 18:19:56,087 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.44950968
2015-10-18 18:19:56,352 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:56,354 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.36404583
2015-10-18 18:19:56,678 INFO [Socket Reader #1 for port 30358] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445144423722_0023 (auth:SIMPLE)
2015-10-18 18:19:56,693 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445144423722_0023_m_000017 asked for a task
2015-10-18 18:19:56,693 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445144423722_0023_m_000017 given task: attempt_1445144423722_0023_m_000008_1001
2015-10-18 18:19:57,002 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.2781602
2015-10-18 18:19:57,163 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.36388028
2015-10-18 18:19:57,354 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:57,579 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:19:58,177 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.36390656
2015-10-18 18:19:58,354 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:58,681 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.15689929
2015-10-18 18:19:58,937 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.3637686
2015-10-18 18:19:59,022 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.36404583
2015-10-18 18:19:59,099 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.44950968
2015-10-18 18:19:59,162 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.3638923
2015-10-18 18:19:59,355 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:19:59,762 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.36404583
2015-10-18 18:20:00,004 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.2781602
2015-10-18 18:20:00,357 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:00,550 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.36388028
2015-10-18 18:20:00,599 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:01,357 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:01,446 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.36390656
2015-10-18 18:20:01,690 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.19255035
2015-10-18 18:20:02,041 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.44980705
2015-10-18 18:20:02,115 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.48247385
2015-10-18 18:20:02,275 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.3637686
2015-10-18 18:20:02,359 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:02,586 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.3638923
2015-10-18 18:20:03,003 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.2781602
2015-10-18 18:20:03,115 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.36404583
2015-10-18 18:20:03,359 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:03,622 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.106881365
2015-10-18 18:20:03,633 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:03,819 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.39218354
2015-10-18 18:20:04,362 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:04,692 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.19255035
2015-10-18 18:20:04,749 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.36390656
2015-10-18 18:20:05,070 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.44980705
2015-10-18 18:20:05,131 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.5352021
2015-10-18 18:20:05,363 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:05,540 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.3637686
2015-10-18 18:20:05,793 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.3638923
2015-10-18 18:20:06,008 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.3569707
2015-10-18 18:20:06,363 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.36404583
2015-10-18 18:20:06,364 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:06,632 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.106881365
2015-10-18 18:20:06,664 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:07,238 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.43612182
2015-10-18 18:20:07,365 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:07,694 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.19255035
2015-10-18 18:20:08,105 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.44980705
2015-10-18 18:20:08,143 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.5352021
2015-10-18 18:20:08,287 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.36390656
2015-10-18 18:20:08,367 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:08,982 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.3637686
2015-10-18 18:20:09,018 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.36388028
2015-10-18 18:20:09,115 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.3638923
2015-10-18 18:20:09,369 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:09,645 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.106881365
2015-10-18 18:20:09,676 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.36404583
2015-10-18 18:20:09,692 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:10,370 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:10,581 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.44968578
2015-10-18 18:20:10,708 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.25028175
2015-10-18 18:20:11,124 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.49560812
2015-10-18 18:20:11,143 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.5352021
2015-10-18 18:20:11,371 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:11,506 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.36390656
2015-10-18 18:20:12,024 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.36388028
2015-10-18 18:20:12,226 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.3637686
2015-10-18 18:20:12,372 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:12,412 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.3638923
2015-10-18 18:20:12,644 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.1443741
2015-10-18 18:20:12,722 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:12,918 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.36404583
2015-10-18 18:20:13,372 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:13,706 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.27825075
2015-10-18 18:20:13,883 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.44968578
2015-10-18 18:20:14,143 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.6209487
2015-10-18 18:20:14,155 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.53543663
2015-10-18 18:20:14,374 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:14,798 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.4066804
2015-10-18 18:20:15,034 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.36388028
2015-10-18 18:20:15,374 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:15,571 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.36554378
2015-10-18 18:20:15,643 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.19258286
2015-10-18 18:20:15,681 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.3638923
2015-10-18 18:20:15,740 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:16,224 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.36404583
2015-10-18 18:20:16,376 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:16,706 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.27825075
2015-10-18 18:20:17,143 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.6209487
2015-10-18 18:20:17,184 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.53543663
2015-10-18 18:20:17,378 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:17,426 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.44968578
2015-10-18 18:20:18,034 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.44968578
2015-10-18 18:20:18,310 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.44890836
2015-10-18 18:20:18,379 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:18,643 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.19258286
2015-10-18 18:20:18,759 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:18,836 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.4078791
2015-10-18 18:20:19,200 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.3638923
2015-10-18 18:20:19,381 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:19,631 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.36404583
2015-10-18 18:20:19,707 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.2899122
2015-10-18 18:20:20,144 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.6209487
2015-10-18 18:20:20,215 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.541703
2015-10-18 18:20:20,381 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:20,831 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.44968578
2015-10-18 18:20:21,035 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.44968578
2015-10-18 18:20:21,383 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:21,645 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.19411838
2015-10-18 18:20:21,738 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.44950968
2015-10-18 18:20:21,791 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:22,361 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.4480535
2015-10-18 18:20:22,383 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:22,425 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.6209487
2015-10-18 18:20:22,673 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.39235327
2015-10-18 18:20:22,706 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.3638923
2015-10-18 18:20:23,068 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.36404583
2015-10-18 18:20:23,144 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.667
2015-10-18 18:20:23,234 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.6210422
2015-10-18 18:20:23,385 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:24,036 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.44968578
2015-10-18 18:20:24,199 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.44968578
2015-10-18 18:20:24,385 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:24,644 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.27811313
2015-10-18 18:20:24,814 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:25,160 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.44950968
2015-10-18 18:20:25,387 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:25,708 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.3638923
2015-10-18 18:20:25,879 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.44950172
2015-10-18 18:20:26,143 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.667
2015-10-18 18:20:26,208 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.43888646
2015-10-18 18:20:26,254 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.6210422
2015-10-18 18:20:26,388 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:26,555 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.4051228
2015-10-18 18:20:27,035 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.5168698
2015-10-18 18:20:27,389 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:27,522 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.44968578
2015-10-18 18:20:27,644 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.27811313
2015-10-18 18:20:27,834 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:28,390 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:28,707 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.3638923
2015-10-18 18:20:28,709 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.44950968
2015-10-18 18:20:29,143 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.67076534
2015-10-18 18:20:29,285 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.6210422
2015-10-18 18:20:29,302 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.44950172
2015-10-18 18:20:29,390 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:29,723 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.44964966
2015-10-18 18:20:29,995 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.4478565
2015-10-18 18:20:30,036 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.5352028
2015-10-18 18:20:30,392 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:30,647 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.27811313
2015-10-18 18:20:30,876 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:30,988 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.44968578
2015-10-18 18:20:31,393 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:31,481 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.6210422
2015-10-18 18:20:31,707 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.44964966
2015-10-18 18:20:32,139 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.44950968
2015-10-18 18:20:32,143 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.7081115
2015-10-18 18:20:32,314 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.667
2015-10-18 18:20:32,394 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:32,754 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.44950172
2015-10-18 18:20:33,035 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.5352028
2015-10-18 18:20:33,062 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.44964966
2015-10-18 18:20:33,387 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.44980705
2015-10-18 18:20:33,394 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:33,665 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.3477326
2015-10-18 18:20:33,895 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:34,295 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.44968578
2015-10-18 18:20:34,395 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:34,708 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.44964966
2015-10-18 18:20:35,144 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.752148
2015-10-18 18:20:35,347 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.667
2015-10-18 18:20:35,397 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:35,457 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.44950968
2015-10-18 18:20:36,036 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.5526831
2015-10-18 18:20:36,123 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.44950172
2015-10-18 18:20:36,398 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:36,424 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.44964966
2015-10-18 18:20:36,676 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.3637686
2015-10-18 18:20:36,724 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.44980705
2015-10-18 18:20:36,926 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:37,400 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:37,554 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.46687168
2015-10-18 18:20:37,707 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.44964966
2015-10-18 18:20:38,144 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.8051015
2015-10-18 18:20:38,365 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.667
2015-10-18 18:20:38,401 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:38,740 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.44950968
2015-10-18 18:20:39,036 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.6208445
2015-10-18 18:20:39,403 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:39,438 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.44950172
2015-10-18 18:20:39,689 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.3637686
2015-10-18 18:20:39,728 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.44964966
2015-10-18 18:20:39,944 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:40,074 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.44980705
2015-10-18 18:20:40,404 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:40,717 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.49198148
2015-10-18 18:20:40,897 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.51495945
2015-10-18 18:20:41,154 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.8416553
2015-10-18 18:20:41,397 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.7036382
2015-10-18 18:20:41,404 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:42,044 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.6208445
2015-10-18 18:20:42,256 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.44950968
2015-10-18 18:20:42,405 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:42,705 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.3637686
2015-10-18 18:20:42,892 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.44950172
2015-10-18 18:20:42,975 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:43,198 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.44964966
2015-10-18 18:20:43,407 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:43,528 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.44980705
2015-10-18 18:20:43,732 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.5352825
2015-10-18 18:20:44,167 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.86914563
2015-10-18 18:20:44,266 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.5352028
2015-10-18 18:20:44,407 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:44,414 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.73968583
2015-10-18 18:20:45,062 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.6208445
2015-10-18 18:20:45,408 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:45,630 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.4667511
2015-10-18 18:20:45,715 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.41169423
2015-10-18 18:20:46,017 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:46,148 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.44950172
2015-10-18 18:20:46,410 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:46,557 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.44964966
2015-10-18 18:20:46,747 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.5352825
2015-10-18 18:20:46,787 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.44980705
2015-10-18 18:20:47,185 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.89674824
2015-10-18 18:20:47,411 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:47,440 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.7710529
2015-10-18 18:20:47,817 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.5352028
2015-10-18 18:20:48,079 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.6208445
2015-10-18 18:20:48,413 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:48,733 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.44950172
2015-10-18 18:20:48,899 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.5125241
2015-10-18 18:20:49,060 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:49,416 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:49,763 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.5352825
2015-10-18 18:20:49,941 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.45553538
2015-10-18 18:20:50,201 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.9279344
2015-10-18 18:20:50,406 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.44964966
2015-10-18 18:20:50,417 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:50,445 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.44980705
2015-10-18 18:20:50,478 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.79986656
2015-10-18 18:20:51,088 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.6426442
2015-10-18 18:20:51,419 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:51,492 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.5352028
2015-10-18 18:20:51,750 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.44950172
2015-10-18 18:20:52,007 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.6426442
2015-10-18 18:20:52,088 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:52,421 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:52,514 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.5352021
2015-10-18 18:20:52,774 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.5352825
2015-10-18 18:20:53,212 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 0.979627
2015-10-18 18:20:53,422 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:53,498 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.83145165
2015-10-18 18:20:54,090 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.667
2015-10-18 18:20:54,249 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.4949876
2015-10-18 18:20:54,323 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.44980705
2015-10-18 18:20:54,423 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:54,459 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.44964966
2015-10-18 18:20:54,730 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1001 is : 1.0
2015-10-18 18:20:54,733 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0023_m_000005_1001
2015-10-18 18:20:54,733 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000005_1001 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:20:54,734 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000012 taskAttempt attempt_1445144423722_0023_m_000005_1001
2015-10-18 18:20:54,734 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000005_1001
2015-10-18 18:20:54,735 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:20:54,751 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000005_1001 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:20:54,752 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0023_m_000005_1001
2015-10-18 18:20:54,752 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0023_m_000005_1000
2015-10-18 18:20:54,753 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:20:54,753 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-18 18:20:54,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000005_1000 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:20:54,754 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000007 taskAttempt attempt_1445144423722_0023_m_000005_1000
2015-10-18 18:20:54,755 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000005_1000
2015-10-18 18:20:54,756 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:20:54,762 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.44950172
2015-10-18 18:20:55,066 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0023_m_000005
2015-10-18 18:20:55,067 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:20:55,115 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.16666667
2015-10-18 18:20:55,310 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.5352028
2015-10-18 18:20:55,424 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-18 18:20:55,428 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:20:55,685 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000005_1000 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:20:55,686 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:20:55,702 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/2/_temporary/attempt_1445144423722_0023_m_000005_1000
2015-10-18 18:20:55,705 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000005_1000 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:20:55,782 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.5708395
2015-10-18 18:20:56,071 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000005_1000 is : 0.5352021
2015-10-18 18:20:56,426 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:20:56,431 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000012
2015-10-18 18:20:56,431 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:20:56,431 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000005_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:20:56,520 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.86134857
2015-10-18 18:20:57,107 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.667
2015-10-18 18:20:57,166 INFO [Socket Reader #1 for port 30358] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 30358: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:20:57,428 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:20:57,780 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.44950172
2015-10-18 18:20:58,155 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.20000002
2015-10-18 18:20:58,429 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:20:58,731 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.44980705
2015-10-18 18:20:58,799 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.620844
2015-10-18 18:20:58,817 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.45973817
2015-10-18 18:20:58,831 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.52841985
2015-10-18 18:20:59,431 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:20:59,433 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.5352028
2015-10-18 18:20:59,436 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000007
2015-10-18 18:20:59,436 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:20:59,436 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000005_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:20:59,561 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.8895675
2015-10-18 18:21:00,110 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.667
2015-10-18 18:21:00,433 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:21:00,807 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.51225173
2015-10-18 18:21:01,175 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.20000002
2015-10-18 18:21:01,435 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:21:01,811 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.620844
2015-10-18 18:21:02,330 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.4958028
2015-10-18 18:21:02,369 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.44980705
2015-10-18 18:21:02,436 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:21:02,549 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.53521925
2015-10-18 18:21:02,585 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.9255005
2015-10-18 18:21:03,079 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.5352028
2015-10-18 18:21:03,127 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.67913646
2015-10-18 18:21:03,438 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:21:03,809 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.53521925
2015-10-18 18:21:04,205 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.20000002
2015-10-18 18:21:04,440 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:21:04,810 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.620844
2015-10-18 18:21:05,442 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:21:05,627 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.95338523
2015-10-18 18:21:05,741 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.5269873
2015-10-18 18:21:05,934 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.4805803
2015-10-18 18:21:06,006 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.53521925
2015-10-18 18:21:06,138 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.72306645
2015-10-18 18:21:06,444 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:21:06,570 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.5352028
2015-10-18 18:21:06,810 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.53521925
2015-10-18 18:21:07,244 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.20000002
2015-10-18 18:21:07,446 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:21:07,811 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.620844
2015-10-18 18:21:08,448 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:21:08,646 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 0.9813087
2015-10-18 18:21:09,192 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.7693146
2015-10-18 18:21:09,449 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:21:09,463 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.5352825
2015-10-18 18:21:09,637 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.53521925
2015-10-18 18:21:09,645 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.620844
2015-10-18 18:21:09,647 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1000 is : 0.5177261
2015-10-18 18:21:09,816 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.53521925
2015-10-18 18:21:10,082 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.5352028
2015-10-18 18:21:10,265 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.20000002
2015-10-18 18:21:10,450 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:21:10,586 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000006_1001 is : 1.0
2015-10-18 18:21:10,588 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0023_m_000006_1001
2015-10-18 18:21:10,588 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000006_1001 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:21:10,588 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000014 taskAttempt attempt_1445144423722_0023_m_000006_1001
2015-10-18 18:21:10,588 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000006_1001
2015-10-18 18:21:10,589 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:21:10,604 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000006_1001 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:21:10,604 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0023_m_000006_1001
2015-10-18 18:21:10,605 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0023_m_000006_1000
2015-10-18 18:21:10,605 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:21:10,606 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-18 18:21:10,606 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000006_1000 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:21:10,606 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000008 taskAttempt attempt_1445144423722_0023_m_000006_1000
2015-10-18 18:21:10,607 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000006_1000
2015-10-18 18:21:10,607 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:21:10,815 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.667
2015-10-18 18:21:11,069 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0023_m_000006
2015-10-18 18:21:11,069 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:21:11,452 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-18 18:21:11,454 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:21:11,507 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000006_1000 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:21:11,508 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:21:11,511 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/2/_temporary/attempt_1445144423722_0023_m_000006_1000
2015-10-18 18:21:11,511 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000006_1000 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:21:12,207 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.80344033
2015-10-18 18:21:12,454 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:12,460 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000014
2015-10-18 18:21:12,460 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:21:12,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000006_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:21:12,628 INFO [Socket Reader #1 for port 30358] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 30358: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:21:12,828 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.5597172
2015-10-18 18:21:13,100 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.5352825
2015-10-18 18:21:13,162 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.53521925
2015-10-18 18:21:13,296 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.23333333
2015-10-18 18:21:13,386 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.5352028
2015-10-18 18:21:13,455 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:13,826 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.667
2015-10-18 18:21:14,456 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:14,462 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000008
2015-10-18 18:21:14,462 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:21:14,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000006_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:21:15,215 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.84727144
2015-10-18 18:21:15,458 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:15,843 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.6207798
2015-10-18 18:21:16,239 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.5352825
2015-10-18 18:21:16,293 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.53521925
2015-10-18 18:21:16,338 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.23333333
2015-10-18 18:21:16,460 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:16,553 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.5352028
2015-10-18 18:21:16,825 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.667
2015-10-18 18:21:17,462 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:18,216 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.88867724
2015-10-18 18:21:18,464 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:18,856 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.6207798
2015-10-18 18:21:19,378 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.23333333
2015-10-18 18:21:19,466 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:19,690 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.5352825
2015-10-18 18:21:19,727 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.53521925
2015-10-18 18:21:19,825 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.6978681
2015-10-18 18:21:19,876 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.5452104
2015-10-18 18:21:20,469 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:21,217 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.939029
2015-10-18 18:21:21,471 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:21,857 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.6207798
2015-10-18 18:21:22,409 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.23333333
2015-10-18 18:21:22,473 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:22,835 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.73936063
2015-10-18 18:21:22,855 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.5352825
2015-10-18 18:21:22,946 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.53521925
2015-10-18 18:21:23,118 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.59043276
2015-10-18 18:21:23,475 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:24,224 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 0.9727411
2015-10-18 18:21:24,477 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:24,862 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.62192
2015-10-18 18:21:25,447 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.23333333
2015-10-18 18:21:25,479 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:25,849 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.7728613
2015-10-18 18:21:26,376 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.5352825
2015-10-18 18:21:26,441 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.53521925
2015-10-18 18:21:26,480 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:26,639 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1000 is : 0.6208445
2015-10-18 18:21:26,664 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.62192
2015-10-18 18:21:26,745 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000004_1001 is : 1.0
2015-10-18 18:21:26,746 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0023_m_000004_1001
2015-10-18 18:21:26,746 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000004_1001 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:21:26,747 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000015 taskAttempt attempt_1445144423722_0023_m_000004_1001
2015-10-18 18:21:26,747 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000004_1001
2015-10-18 18:21:26,748 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:21:26,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000004_1001 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:21:26,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0023_m_000004_1001
2015-10-18 18:21:26,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0023_m_000004_1000
2015-10-18 18:21:26,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:21:26,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-18 18:21:26,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000004_1000 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:21:26,758 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000005 taskAttempt attempt_1445144423722_0023_m_000004_1000
2015-10-18 18:21:26,758 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000004_1000
2015-10-18 18:21:26,758 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:21:27,073 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0023_m_000004
2015-10-18 18:21:27,073 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:21:27,176 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000004_1000 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:21:27,177 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:21:27,182 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/2/_temporary/attempt_1445144423722_0023_m_000004_1000
2015-10-18 18:21:27,182 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000004_1000 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:21:27,481 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-18 18:21:27,485 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:21:27,874 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.667
2015-10-18 18:21:28,011 INFO [Socket Reader #1 for port 30358] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 30358: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:21:28,465 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.26666668
2015-10-18 18:21:28,482 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:28,487 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000015
2015-10-18 18:21:28,487 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000005
2015-10-18 18:21:28,487 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:21:28,487 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000004_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:21:28,487 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000004_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:21:28,861 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.8178531
2015-10-18 18:21:29,483 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:29,522 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.5352825
2015-10-18 18:21:29,777 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.53521925
2015-10-18 18:21:30,483 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:30,898 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.667
2015-10-18 18:21:31,485 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:31,503 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.26666668
2015-10-18 18:21:31,862 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.853756
2015-10-18 18:21:32,487 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:33,178 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.5352825
2015-10-18 18:21:33,489 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:33,620 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.55132246
2015-10-18 18:21:33,908 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.667
2015-10-18 18:21:34,491 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:34,532 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.26666668
2015-10-18 18:21:34,875 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.8869678
2015-10-18 18:21:35,494 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:36,496 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:36,908 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.6794783
2015-10-18 18:21:36,975 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.5352825
2015-10-18 18:21:37,209 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.5847024
2015-10-18 18:21:37,498 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:37,574 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.26666668
2015-10-18 18:21:37,877 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.9278997
2015-10-18 18:21:38,500 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:39,502 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:39,909 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.7214651
2015-10-18 18:21:40,504 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:40,606 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.26666668
2015-10-18 18:21:40,650 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.54280627
2015-10-18 18:21:40,883 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 0.97200644
2015-10-18 18:21:40,932 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.6142906
2015-10-18 18:21:41,506 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:42,507 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:42,912 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.76248586
2015-10-18 18:21:43,379 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1001 is : 1.0
2015-10-18 18:21:43,381 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0023_m_000007_1001
2015-10-18 18:21:43,382 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000007_1001 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:21:43,382 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000016 taskAttempt attempt_1445144423722_0023_m_000007_1001
2015-10-18 18:21:43,383 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000007_1001
2015-10-18 18:21:43,383 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:21:43,399 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000007_1001 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:21:43,400 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0023_m_000007_1001
2015-10-18 18:21:43,400 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0023_m_000007_1000
2015-10-18 18:21:43,400 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:21:43,401 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-18 18:21:43,401 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000007_1000 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:21:43,402 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000009 taskAttempt attempt_1445144423722_0023_m_000007_1000
2015-10-18 18:21:43,402 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000007_1000
2015-10-18 18:21:43,403 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:21:43,510 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-18 18:21:43,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:21:43,649 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.26666668
2015-10-18 18:21:44,077 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0023_m_000007
2015-10-18 18:21:44,077 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:21:44,512 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:44,517 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000016
2015-10-18 18:21:44,517 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:21:44,517 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000007_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:21:44,711 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000007_1000 is : 0.5718401
2015-10-18 18:21:44,795 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000007_1000 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:21:44,796 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:21:44,799 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/2/_temporary/attempt_1445144423722_0023_m_000007_1000
2015-10-18 18:21:44,800 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000007_1000 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:21:45,442 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.6207798
2015-10-18 18:21:45,514 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:45,923 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.80097723
2015-10-18 18:21:46,516 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:46,563 INFO [Socket Reader #1 for port 30358] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 30358: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:21:46,678 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.3
2015-10-18 18:21:47,518 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:48,520 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:48,928 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.85013086
2015-10-18 18:21:49,039 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.6207798
2015-10-18 18:21:49,522 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:49,527 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000009
2015-10-18 18:21:49,527 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:21:49,527 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000007_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:21:49,719 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.3
2015-10-18 18:21:50,524 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:51,527 INFO [IPC Server handler 7 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:51,944 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.89452565
2015-10-18 18:21:52,529 INFO [IPC Server handler 4 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:52,714 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.6207798
2015-10-18 18:21:52,749 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.3
2015-10-18 18:21:53,531 INFO [IPC Server handler 15 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:54,533 INFO [IPC Server handler 11 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:54,949 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.9400784
2015-10-18 18:21:55,535 INFO [IPC Server handler 2 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:55,789 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.3
2015-10-18 18:21:56,261 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.6207798
2015-10-18 18:21:56,537 INFO [IPC Server handler 20 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:57,539 INFO [IPC Server handler 13 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:57,965 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 0.9730097
2015-10-18 18:21:58,541 INFO [IPC Server handler 21 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:58,818 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.3
2015-10-18 18:21:59,543 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:21:59,841 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1000 is : 0.6207798
2015-10-18 18:21:59,976 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_m_000008_1001 is : 1.0
2015-10-18 18:21:59,979 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0023_m_000008_1001
2015-10-18 18:21:59,979 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000008_1001 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:21:59,980 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000017 taskAttempt attempt_1445144423722_0023_m_000008_1001
2015-10-18 18:21:59,980 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000008_1001
2015-10-18 18:21:59,981 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-18 18:21:59,998 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000008_1001 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:21:59,998 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0023_m_000008_1001
2015-10-18 18:21:59,998 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445144423722_0023_m_000008_1000
2015-10-18 18:21:59,998 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:21:59,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-18 18:21:59,999 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000008_1000 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-18 18:22:00,000 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000010 taskAttempt attempt_1445144423722_0023_m_000008_1000
2015-10-18 18:22:00,000 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_m_000008_1000
2015-10-18 18:22:00,001 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:54883
2015-10-18 18:22:00,080 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445144423722_0023_m_000008
2015-10-18 18:22:00,080 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-18 18:22:00,143 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000008_1000 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-18 18:22:00,143 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-18 18:22:00,154 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/2/_temporary/attempt_1445144423722_0023_m_000008_1000
2015-10-18 18:22:00,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_m_000008_1000 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-18 18:22:00,545 INFO [IPC Server handler 29 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445144423722_0023_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-18 18:22:00,547 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:22:01,136 INFO [Socket Reader #1 for port 30358] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 30358: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-18 18:22:01,227 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.3
2015-10-18 18:22:01,248 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.3
2015-10-18 18:22:01,550 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000017
2015-10-18 18:22:01,550 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:22:01,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000008_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:22:01,861 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.66683465
2015-10-18 18:22:02,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445144423722_0023_02_000010
2015-10-18 18:22:02,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:22:02,552 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445144423722_0023_m_000008_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-18 18:22:04,882 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.67874986
2015-10-18 18:22:07,924 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.6938176
2015-10-18 18:22:10,967 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.70833814
2015-10-18 18:22:14,004 INFO [IPC Server handler 10 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.72806394
2015-10-18 18:22:17,033 INFO [IPC Server handler 27 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.7479839
2015-10-18 18:22:20,077 INFO [IPC Server handler 28 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.76509976
2015-10-18 18:22:23,104 INFO [IPC Server handler 17 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.77987957
2015-10-18 18:22:26,136 INFO [IPC Server handler 18 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.803032
2015-10-18 18:22:29,172 INFO [IPC Server handler 9 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.8185219
2015-10-18 18:22:32,203 INFO [IPC Server handler 23 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.8340535
2015-10-18 18:22:35,233 INFO [IPC Server handler 19 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.85474586
2015-10-18 18:22:38,253 INFO [IPC Server handler 3 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.87623703
2015-10-18 18:22:41,295 INFO [IPC Server handler 24 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.89240956
2015-10-18 18:22:44,334 INFO [IPC Server handler 6 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.91212803
2015-10-18 18:22:47,363 INFO [IPC Server handler 22 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.9313826
2015-10-18 18:22:50,405 INFO [IPC Server handler 26 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.9485669
2015-10-18 18:22:53,435 INFO [IPC Server handler 25 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.96541065
2015-10-18 18:22:56,477 INFO [IPC Server handler 12 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.976874
2015-10-18 18:22:59,518 INFO [IPC Server handler 16 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.986627
2015-10-18 18:23:02,560 INFO [IPC Server handler 5 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 0.99712056
2015-10-18 18:23:03,777 INFO [IPC Server handler 0 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445144423722_0023_r_000000_1000
2015-10-18 18:23:03,778 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_r_000000_1000 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-18 18:23:03,778 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445144423722_0023_r_000000_1000 given a go for committing the task output.
2015-10-18 18:23:03,780 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445144423722_0023_r_000000_1000
2015-10-18 18:23:03,780 INFO [IPC Server handler 14 on 30358] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445144423722_0023_r_000000_1000:true
2015-10-18 18:23:03,838 INFO [IPC Server handler 1 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445144423722_0023_r_000000_1000 is : 1.0
2015-10-18 18:23:03,840 INFO [IPC Server handler 8 on 30358] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445144423722_0023_r_000000_1000
2015-10-18 18:23:03,841 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_r_000000_1000 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-18 18:23:03,841 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445144423722_0023_02_000013 taskAttempt attempt_1445144423722_0023_r_000000_1000
2015-10-18 18:23:03,842 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445144423722_0023_r_000000_1000
2015-10-18 18:23:03,843 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:7109
2015-10-18 18:23:03,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445144423722_0023_r_000000_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-18 18:23:03,866 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445144423722_0023_r_000000_1000
2015-10-18 18:23:03,866 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445144423722_0023_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-18 18:23:03,866 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-18 18:23:03,868 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0023Job Transitioned from RUNNING to COMMITTING
2015-10-18 18:23:03,868 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-18 18:23:03,938 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-18 18:23:03,939 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445144423722_0023Job Transitioned from COMMITTING to SUCCEEDED
2015-10-18 18:23:03,941 INFO [Thread-110] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-18 18:23:03,941 INFO [Thread-110] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-18 18:23:03,941 INFO [Thread-110] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-18 18:23:03,941 INFO [Thread-110] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-18 18:23:03,941 INFO [Thread-110] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-18 18:23:03,941 INFO [Thread-110] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-18 18:23:03,943 INFO [Thread-110] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-18 18:23:04,064 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0023/job_1445144423722_0023_2.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0023-1445162506952-msrabi-pagerank-1445163783933-10-1-SUCCEEDED-default-1445163429025.jhist_tmp
2015-10-18 18:23:04,647 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:23:05,273 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0023-1445162506952-msrabi-pagerank-1445163783933-10-1-SUCCEEDED-default-1445163429025.jhist_tmp
2015-10-18 18:23:05,278 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0023/job_1445144423722_0023_2_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0023_conf.xml_tmp
2015-10-18 18:23:05,348 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0023_conf.xml_tmp
2015-10-18 18:23:05,354 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0023.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0023.summary
2015-10-18 18:23:05,357 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0023_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0023_conf.xml
2015-10-18 18:23:05,360 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0023-1445162506952-msrabi-pagerank-1445163783933-10-1-SUCCEEDED-default-1445163429025.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445144423722_0023-1445162506952-msrabi-pagerank-1445163783933-10-1-SUCCEEDED-default-1445163429025.jhist
2015-10-18 18:23:05,361 INFO [Thread-110] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-18 18:23:05,365 INFO [Thread-110] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-18 18:23:05,367 INFO [Thread-110] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-41.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445144423722_0023
2015-10-18 18:23:05,378 INFO [Thread-110] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-18 18:23:06,380 INFO [Thread-110] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:16 ContRel:0 HostLocal:10 RackLocal:5
2015-10-18 18:23:06,382 INFO [Thread-110] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445144423722_0023
2015-10-18 18:23:06,391 INFO [Thread-110] org.apache.hadoop.ipc.Server: Stopping server on 30358
2015-10-18 18:23:06,393 INFO [IPC Server listener on 30358] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 30358
2015-10-18 18:23:06,394 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-18 18:23:06,395 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
