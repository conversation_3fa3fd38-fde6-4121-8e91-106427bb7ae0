"""
Custom Dataset Manager for Personal Articles
Allows users to add their own articles and documents to the search system
"""

import json
import os
import pandas as pd
from typing import List, Dict, Optional
from datetime import datetime
import uuid

from config import SAMPLE_DATASET_FILE, DATA_DIR

class CustomDatasetManager:
    """Manage custom datasets and articles"""
    
    def __init__(self, dataset_file: str = SAMPLE_DATASET_FILE):
        self.dataset_file = dataset_file
        self.articles = []
        self.load_articles()
    
    def load_articles(self) -> List[Dict]:
        """Load existing articles from file"""
        if os.path.exists(self.dataset_file):
            try:
                with open(self.dataset_file, 'r', encoding='utf-8') as f:
                    self.articles = json.load(f)
                print(f"✅ Loaded {len(self.articles)} existing articles")
            except Exception as e:
                print(f"⚠️ Error loading articles: {e}")
                self.articles = []
        else:
            self.articles = []
        return self.articles
    
    def save_articles(self):
        """Save articles to file"""
        try:
            # Create backup
            if os.path.exists(self.dataset_file):
                backup_file = self.dataset_file.replace('.json', f'_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
                os.rename(self.dataset_file, backup_file)
                print(f"📁 Backup created: {backup_file}")
            
            with open(self.dataset_file, 'w', encoding='utf-8') as f:
                json.dump(self.articles, f, indent=2, ensure_ascii=False)
            print(f"✅ Saved {len(self.articles)} articles to {self.dataset_file}")
        except Exception as e:
            print(f"❌ Error saving articles: {e}")
    
    def add_article(self, title: str, content: str, category: str = "General", 
                   author: str = "User", tags: List[str] = None, 
                   source: str = "Custom") -> Dict:
        """Add a new article to the dataset"""
        
        if tags is None:
            tags = []
        
        # Generate unique ID
        article_id = len(self.articles) + 1
        
        article = {
            "id": article_id,
            "title": title.strip(),
            "content": content.strip(),
            "category": category.strip(),
            "tags": tags,
            "author": author.strip(),
            "date": datetime.now().strftime("%Y-%m-%d"),
            "source": source,
            "uuid": str(uuid.uuid4())
        }
        
        self.articles.append(article)
        print(f"✅ Added article: '{title}' (ID: {article_id})")
        return article
    
    def add_articles_from_text_file(self, file_path: str, title_prefix: str = "Document", 
                                   category: str = "Custom", author: str = "User") -> int:
        """Add articles from a text file (one article per file)"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            filename = os.path.basename(file_path)
            title = f"{title_prefix}: {filename}"
            
            self.add_article(
                title=title,
                content=content,
                category=category,
                author=author,
                tags=["imported", "text_file"],
                source=file_path
            )
            return 1
        except Exception as e:
            print(f"❌ Error reading file {file_path}: {e}")
            return 0
    
    def add_articles_from_csv(self, csv_file: str, title_col: str, content_col: str,
                             category_col: str = None, author_col: str = None,
                             tags_col: str = None) -> int:
        """Add articles from CSV file"""
        try:
            df = pd.read_csv(csv_file)
            added_count = 0
            
            for _, row in df.iterrows():
                title = str(row[title_col]) if title_col in df.columns else f"Article {len(self.articles) + 1}"
                content = str(row[content_col]) if content_col in df.columns else ""
                category = str(row[category_col]) if category_col and category_col in df.columns else "Imported"
                author = str(row[author_col]) if author_col and author_col in df.columns else "CSV Import"
                
                # Handle tags
                tags = ["csv_import"]
                if tags_col and tags_col in df.columns:
                    tag_str = str(row[tags_col])
                    if tag_str and tag_str != 'nan':
                        tags.extend([tag.strip() for tag in tag_str.split(',')])
                
                if content and content != 'nan' and len(content.strip()) > 10:
                    self.add_article(
                        title=title,
                        content=content,
                        category=category,
                        author=author,
                        tags=tags,
                        source=csv_file
                    )
                    added_count += 1
            
            print(f"✅ Added {added_count} articles from CSV")
            return added_count
            
        except Exception as e:
            print(f"❌ Error reading CSV file: {e}")
            return 0
    
    def add_articles_from_directory(self, directory: str, file_extension: str = ".txt",
                                   category: str = "Documents", author: str = "User") -> int:
        """Add all text files from a directory"""
        added_count = 0
        
        if not os.path.exists(directory):
            print(f"❌ Directory not found: {directory}")
            return 0
        
        for filename in os.listdir(directory):
            if filename.endswith(file_extension):
                file_path = os.path.join(directory, filename)
                if self.add_articles_from_text_file(file_path, "Document", category, author):
                    added_count += 1
        
        print(f"✅ Added {added_count} articles from directory: {directory}")
        return added_count
    
    def remove_article(self, article_id: int) -> bool:
        """Remove an article by ID"""
        for i, article in enumerate(self.articles):
            if article['id'] == article_id:
                removed_article = self.articles.pop(i)
                print(f"✅ Removed article: '{removed_article['title']}'")
                return True
        print(f"❌ Article with ID {article_id} not found")
        return False
    
    def search_articles(self, query: str) -> List[Dict]:
        """Simple keyword search in articles"""
        query_lower = query.lower()
        results = []
        
        for article in self.articles:
            title_match = query_lower in article['title'].lower()
            content_match = query_lower in article['content'].lower()
            tag_match = any(query_lower in tag.lower() for tag in article.get('tags', []))
            
            if title_match or content_match or tag_match:
                results.append(article)
        
        return results
    
    def get_statistics(self) -> Dict:
        """Get dataset statistics"""
        if not self.articles:
            return {"total": 0}
        
        categories = {}
        authors = {}
        sources = {}
        
        for article in self.articles:
            # Count categories
            cat = article.get('category', 'Unknown')
            categories[cat] = categories.get(cat, 0) + 1
            
            # Count authors
            auth = article.get('author', 'Unknown')
            authors[auth] = authors.get(auth, 0) + 1
            
            # Count sources
            src = article.get('source', 'Unknown')
            sources[src] = sources.get(src, 0) + 1
        
        return {
            "total": len(self.articles),
            "categories": categories,
            "authors": authors,
            "sources": sources,
            "avg_content_length": sum(len(a['content']) for a in self.articles) / len(self.articles)
        }
    
    def list_articles(self, limit: int = 10) -> None:
        """List articles with basic info"""
        print(f"\n📚 Articles in Dataset ({len(self.articles)} total):")
        print("-" * 80)
        
        for i, article in enumerate(self.articles[:limit]):
            content_preview = article['content'][:100] + "..." if len(article['content']) > 100 else article['content']
            print(f"{article['id']:3d}. {article['title']}")
            print(f"     Category: {article['category']} | Author: {article['author']}")
            print(f"     Preview: {content_preview}")
            print()
        
        if len(self.articles) > limit:
            print(f"... and {len(self.articles) - limit} more articles")
    
    def clear_all_articles(self) -> bool:
        """Clear all articles (with confirmation)"""
        if self.articles:
            # Create backup before clearing
            backup_file = self.dataset_file.replace('.json', f'_full_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(self.articles, f, indent=2, ensure_ascii=False)
            
            self.articles = []
            print(f"✅ Cleared all articles. Backup saved to: {backup_file}")
            return True
        else:
            print("ℹ️ No articles to clear")
            return False

def main():
    """Interactive CLI for managing custom datasets"""
    manager = CustomDatasetManager()
    
    print("🔍 Custom Dataset Manager for ONGC Knowledge System")
    print("=" * 60)
    
    while True:
        print("\nOptions:")
        print("1. Add single article")
        print("2. Add from text file")
        print("3. Add from CSV file")
        print("4. Add from directory")
        print("5. List articles")
        print("6. Remove article")
        print("7. Search articles")
        print("8. Show statistics")
        print("9. Save and exit")
        print("0. Clear all articles")
        
        choice = input("\nEnter your choice (0-9): ").strip()
        
        if choice == "1":
            title = input("Article title: ")
            content = input("Article content: ")
            category = input("Category (default: General): ") or "General"
            author = input("Author (default: User): ") or "User"
            tags_input = input("Tags (comma-separated): ")
            tags = [tag.strip() for tag in tags_input.split(",")] if tags_input else []
            
            manager.add_article(title, content, category, author, tags)
        
        elif choice == "2":
            file_path = input("Text file path: ")
            category = input("Category (default: Custom): ") or "Custom"
            author = input("Author (default: User): ") or "User"
            manager.add_articles_from_text_file(file_path, "Document", category, author)
        
        elif choice == "3":
            csv_file = input("CSV file path: ")
            title_col = input("Title column name: ")
            content_col = input("Content column name: ")
            category_col = input("Category column (optional): ") or None
            author_col = input("Author column (optional): ") or None
            manager.add_articles_from_csv(csv_file, title_col, content_col, category_col, author_col)
        
        elif choice == "4":
            directory = input("Directory path: ")
            extension = input("File extension (default: .txt): ") or ".txt"
            category = input("Category (default: Documents): ") or "Documents"
            author = input("Author (default: User): ") or "User"
            manager.add_articles_from_directory(directory, extension, category, author)
        
        elif choice == "5":
            limit = input("Number to show (default: 10): ")
            limit = int(limit) if limit.isdigit() else 10
            manager.list_articles(limit)
        
        elif choice == "6":
            article_id = input("Article ID to remove: ")
            if article_id.isdigit():
                manager.remove_article(int(article_id))
        
        elif choice == "7":
            query = input("Search query: ")
            results = manager.search_articles(query)
            print(f"\n🔍 Found {len(results)} results:")
            for result in results[:5]:
                print(f"- {result['title']} (ID: {result['id']})")
        
        elif choice == "8":
            stats = manager.get_statistics()
            print(f"\n📊 Dataset Statistics:")
            print(f"Total articles: {stats['total']}")
            if stats['total'] > 0:
                print(f"Categories: {dict(list(stats['categories'].items())[:5])}")
                print(f"Authors: <AUTHORS>
                print(f"Average content length: {stats['avg_content_length']:.0f} characters")
        
        elif choice == "9":
            manager.save_articles()
            print("👋 Goodbye!")
            break
        
        elif choice == "0":
            confirm = input("Are you sure you want to clear ALL articles? (yes/no): ")
            if confirm.lower() == 'yes':
                manager.clear_all_articles()
        
        else:
            print("❌ Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
