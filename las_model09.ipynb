{"cells": [{"cell_type": "code", "execution_count": 3, "id": "19e77a2d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Clean Data Accuracy: 0.9633\n", "                 precision    recall  f1-score   support\n", "\n", "      Anhydrite       0.97      0.99      0.98        90\n", "          Chalk       0.97      0.95      0.96      1019\n", "           Coal       0.75      0.92      0.83        13\n", "       Dolomite       0.86      0.70      0.77        46\n", "         Halite       1.00      1.00      1.00       846\n", "      Limestone       0.94      0.91      0.93      2373\n", "           Marl       0.95      0.93      0.94       983\n", "      Sandstone       0.95      0.95      0.95      3227\n", "Sandstone/Shale       0.86      0.80      0.83      1282\n", "          Shale       0.98      0.99      0.98     16482\n", "           Tuff       0.91      0.95      0.93       279\n", "\n", "       accuracy                           0.96     26640\n", "      macro avg       0.92      0.92      0.92     26640\n", "   weighted avg       0.96      0.96      0.96     26640\n", "\n", "\n", "Corrupted Data Accuracy: 0.9515\n", "                 precision    recall  f1-score   support\n", "\n", "      Anhydrite       0.98      0.98      0.98        90\n", "          Chalk       0.94      0.93      0.94      1019\n", "           Coal       0.69      0.85      0.76        13\n", "       Dolomite       0.91      0.65      0.76        46\n", "         Halite       1.00      1.00      1.00       846\n", "      Limestone       0.92      0.89      0.90      2373\n", "           Marl       0.94      0.91      0.92       983\n", "      Sandstone       0.94      0.93      0.93      3227\n", "Sandstone/Shale       0.82      0.72      0.77      1282\n", "          Shale       0.97      0.99      0.98     16482\n", "           Tuff       0.89      0.95      0.92       279\n", "\n", "       accuracy                           0.95     26640\n", "      macro avg       0.91      0.89      0.90     26640\n", "   weighted avg       0.95      0.95      0.95     26640\n", "\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler, OrdinalEncoder\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score, classification_report\n", "from copy import deepcopy\n", "\n", "# Load data\n", "df = pd.read_csv(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/CSV Data/Xeek_train_subset_clean.csv\")\n", "\n", "# Drop high-cardinality columns to avoid memory issues\n", "df.drop(columns=[\"WELL\"], inplace=True)\n", "\n", "# Define target and features\n", "target_col = 'LITH'\n", "numerical_cols = ['DEP<PERSON>_<PERSON>', 'CALI', 'RDEP', 'RHOB', 'GR', 'NPHI', 'PEF', 'DTC']\n", "categorical_cols = ['GROUP', 'FORMATION']\n", "df = df.dropna(subset=[target_col])\n", "\n", "# Create clean and corrupted copies\n", "df_clean = deepcopy(df)\n", "df_corrupt = deepcopy(df)\n", "\n", "# Corrupt 5% of numeric data\n", "for col in numerical_cols:\n", "    mask = np.random.rand(len(df_corrupt)) < 0.05\n", "    df_corrupt.loc[mask, col] = np.nan\n", "\n", "# Preprocessing\n", "numeric_transformer = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='mean')),\n", "    ('scaler', StandardScaler())\n", "])\n", "\n", "categorical_transformer = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='most_frequent')),\n", "    ('encoder', OrdinalEncoder(handle_unknown='use_encoded_value', unknown_value=-1))\n", "])\n", "\n", "preprocessor = ColumnTransformer([\n", "    ('num', numeric_transformer, numerical_cols),\n", "    ('cat', categorical_transformer, categorical_cols)\n", "])\n", "\n", "# Model\n", "model_pipeline = Pipeline([\n", "    ('preprocessor', preprocessor),\n", "    ('classifier', RandomForestClassifier(n_estimators=50, random_state=42))\n", "])\n", "\n", "# Training function\n", "def train_and_evaluate(df_input, label):\n", "    X = df_input[numerical_cols + categorical_cols]\n", "    y = df_input[target_col]\n", "    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "    model_pipeline.fit(X_train, y_train)\n", "    y_pred = model_pipeline.predict(X_test)\n", "    acc = accuracy_score(y_test, y_pred)\n", "    report = classification_report(y_test, y_pred)\n", "    print(f\"\\n{label} Accuracy: {acc:.4f}\")\n", "    print(report)\n", "\n", "# Run both\n", "train_and_evaluate(df_clean, \"Clean Data\")\n", "train_and_evaluate(df_corrupt, \"Corrupted Data\")\n", "\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "import matplotlib.pyplot as plt\n", "\n", "# Impute missing values in baseline training and test sets\n", "imputer = SimpleImputer(strategy='mean')\n", "X_train_baseline_imputed = imputer.fit_transform(X_train_baseline)\n", "X_test_baseline_imputed = imputer.transform(X_test_baseline)\n", "\n", "# Train Linear Regression model\n", "model_baseline = LinearRegression()\n", "model_baseline.fit(X_train_baseline_imputed, y_train_baseline)\n", "y_pred_baseline = model_baseline.predict(X_test_baseline_imputed)\n", "\n", "# Evaluate performance\n", "mse_baseline = mean_squared_error(y_test_baseline, y_pred_baseline)\n", "r2_baseline = r2_score(y_test_baseline, y_pred_baseline)\n", "\n", "print(f\"Baseline MSE: {mse_baseline:.4f}\")\n", "print(f\"Baseline R²: {r2_baseline:.4f}\")\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'X_train_baseline' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 8\u001b[0m\n\u001b[0;32m      6\u001b[0m \u001b[38;5;66;03m# Impute missing values in baseline training and test sets\u001b[39;00m\n\u001b[0;32m      7\u001b[0m imputer \u001b[38;5;241m=\u001b[39m SimpleImputer(strategy\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmean\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m----> 8\u001b[0m X_train_baseline_imputed \u001b[38;5;241m=\u001b[39m imputer\u001b[38;5;241m.\u001b[39mfit_transform(\u001b[43mX_train_baseline\u001b[49m)\n\u001b[0;32m      9\u001b[0m X_test_baseline_imputed \u001b[38;5;241m=\u001b[39m imputer\u001b[38;5;241m.\u001b[39mtransform(X_test_baseline)\n\u001b[0;32m     11\u001b[0m \u001b[38;5;66;03m# Train Linear Regression model\u001b[39;00m\n", "\u001b[1;31mNameError\u001b[0m: name 'X_train_baseline' is not defined"]}], "source": ["from sklearn.impute import SimpleImputer\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "import matplotlib.pyplot as plt\n", "\n", "# Impute missing values in baseline training and test sets\n", "imputer = SimpleImputer(strategy='mean')\n", "X_train_baseline_imputed = imputer.fit_transform(X_train_baseline)\n", "X_test_baseline_imputed = imputer.transform(X_test_baseline)\n", "\n", "# Train Linear Regression model\n", "model_baseline = LinearRegression()\n", "model_baseline.fit(X_train_baseline_imputed, y_train_baseline)\n", "y_pred_baseline = model_baseline.predict(X_test_baseline_imputed)\n", "\n", "# Evaluate performance\n", "mse_baseline = mean_squared_error(y_test_baseline, y_pred_baseline)\n", "r2_baseline = r2_score(y_test_baseline, y_pred_baseline)\n", "\n", "print(f\"Baseline MSE: {mse_baseline:.4f}\")\n", "print(f\"Baseline R²: {r2_baseline:.4f}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}