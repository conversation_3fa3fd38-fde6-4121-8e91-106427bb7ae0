2015-10-17 16:47:38,357 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445062781478_0018_000001
2015-10-17 16:47:38,951 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 16:47:38,951 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 18 cluster_timestamp: 1445062781478 } attemptId: 1 } keyId: 471522253)
2015-10-17 16:47:39,185 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 16:47:40,029 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 16:47:40,107 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 16:47:40,154 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 16:47:40,154 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 16:47:40,154 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 16:47:40,154 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 16:47:40,154 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 16:47:40,154 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 16:47:40,154 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 16:47:40,169 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 16:47:40,216 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 16:47:40,248 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 16:47:40,263 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 16:47:40,294 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 16:47:40,341 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 16:47:40,607 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 16:47:40,669 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 16:47:40,669 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 16:47:40,669 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445062781478_0018 to jobTokenSecretManager
2015-10-17 16:47:40,873 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445062781478_0018 because: not enabled; too many maps; too much input;
2015-10-17 16:47:40,888 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445062781478_0018 = 1256521728. Number of splits = 10
2015-10-17 16:47:40,904 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445062781478_0018 = 1
2015-10-17 16:47:40,904 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0018Job Transitioned from NEW to INITED
2015-10-17 16:47:40,904 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445062781478_0018.
2015-10-17 16:47:40,935 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 16:47:40,951 INFO [Socket Reader #1 for port 53409] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 53409
2015-10-17 16:47:40,966 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 16:47:40,966 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 16:47:40,966 INFO [IPC Server listener on 53409] org.apache.hadoop.ipc.Server: IPC Server listener on 53409: starting
2015-10-17 16:47:40,966 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MININT-75DGDAM1.fareast.corp.microsoft.com/************:53409
2015-10-17 16:47:41,060 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 16:47:41,076 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 16:47:41,076 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 16:47:41,091 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 16:47:41,091 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 16:47:41,091 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 16:47:41,091 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 16:47:41,091 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 53416
2015-10-17 16:47:41,091 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 16:47:41,138 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_53416_mapreduce____.vxgjfm\webapp
2015-10-17 16:47:41,341 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:53416
2015-10-17 16:47:41,341 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 53416
2015-10-17 16:47:41,701 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 16:47:41,701 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445062781478_0018
2015-10-17 16:47:41,701 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 16:47:41,716 INFO [Socket Reader #1 for port 53419] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 53419
2015-10-17 16:47:41,716 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 16:47:41,716 INFO [IPC Server listener on 53419] org.apache.hadoop.ipc.Server: IPC Server listener on 53419: starting
2015-10-17 16:47:41,732 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 16:47:41,732 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 16:47:41,732 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 16:47:41,794 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 16:47:41,904 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 16:47:41,904 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 16:47:41,904 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 16:47:41,904 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 16:47:41,919 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0018Job Transitioned from INITED to SETUP
2015-10-17 16:47:41,919 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 16:47:41,935 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445062781478_0018Job Transitioned from SETUP to RUNNING
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,966 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:41,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:47:41,982 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 16:47:41,998 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 16:47:42,029 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445062781478_0018, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0018/job_1445062781478_0018_1.jhist
2015-10-17 16:47:42,904 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 16:47:42,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:22528, vCores:-13> knownNMs=5
2015-10-17 16:47:42,966 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:22528, vCores:-13>
2015-10-17 16:47:42,966 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:43,982 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 16:47:43,982 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000002 to attempt_1445062781478_0018_m_000000_0
2015-10-17 16:47:43,998 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000003 to attempt_1445062781478_0018_m_000001_0
2015-10-17 16:47:43,998 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:20480, vCores:-15>
2015-10-17 16:47:43,998 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:43,998 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:2 RackLocal:0
2015-10-17 16:47:44,045 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:44,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0018/job.jar
2015-10-17 16:47:44,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445062781478_0018/job.xml
2015-10-17 16:47:44,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 16:47:44,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 16:47:44,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 16:47:44,107 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:44,123 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:44,123 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:44,123 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000002 taskAttempt attempt_1445062781478_0018_m_000000_0
2015-10-17 16:47:44,123 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000003 taskAttempt attempt_1445062781478_0018_m_000001_0
2015-10-17 16:47:44,123 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000000_0
2015-10-17 16:47:44,123 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000001_0
2015-10-17 16:47:44,123 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:47:44,154 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:47:44,201 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000001_0 : 13562
2015-10-17 16:47:44,201 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000000_0 : 13562
2015-10-17 16:47:44,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000001_0] using containerId: [container_1445062781478_0018_01_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 16:47:44,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:44,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000000_0] using containerId: [container_1445062781478_0018_01_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 16:47:44,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:44,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000001
2015-10-17 16:47:44,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:44,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000000
2015-10-17 16:47:44,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:44,998 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:20480, vCores:-15> knownNMs=5
2015-10-17 16:47:44,998 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:20480, vCores:-15>
2015-10-17 16:47:44,998 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:45,998 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 16:47:45,998 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:45,998 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000004 to attempt_1445062781478_0018_m_000002_0
2015-10-17 16:47:45,998 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:46,013 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:46,013 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:46,013 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000004 taskAttempt attempt_1445062781478_0018_m_000002_0
2015-10-17 16:47:46,013 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000002_0
2015-10-17 16:47:46,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000005 to attempt_1445062781478_0018_m_000003_0
2015-10-17 16:47:46,013 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 16:47:46,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:18432, vCores:-17>
2015-10-17 16:47:46,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:46,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:2 RackLocal:2
2015-10-17 16:47:46,013 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:46,013 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:46,013 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000005 taskAttempt attempt_1445062781478_0018_m_000003_0
2015-10-17 16:47:46,013 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000003_0
2015-10-17 16:47:46,013 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 16:47:46,076 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000002_0 : 13562
2015-10-17 16:47:46,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000002_0] using containerId: [container_1445062781478_0018_01_000004 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 16:47:46,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:46,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000002
2015-10-17 16:47:46,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:46,357 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000003_0 : 13562
2015-10-17 16:47:46,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000003_0] using containerId: [container_1445062781478_0018_01_000005 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 16:47:46,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:46,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000003
2015-10-17 16:47:46,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:46,560 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:47:46,560 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:47:46,592 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000003 asked for a task
2015-10-17 16:47:46,592 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000003 given task: attempt_1445062781478_0018_m_000001_0
2015-10-17 16:47:46,592 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000002 asked for a task
2015-10-17 16:47:46,592 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000002 given task: attempt_1445062781478_0018_m_000000_0
2015-10-17 16:47:47,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:15360, vCores:-20> knownNMs=5
2015-10-17 16:47:47,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 16:47:47,013 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:47,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000006 to attempt_1445062781478_0018_m_000004_0
2015-10-17 16:47:47,013 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:47,013 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:47,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000007 to attempt_1445062781478_0018_m_000005_0
2015-10-17 16:47:47,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:15360, vCores:-20>
2015-10-17 16:47:47,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:47,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:2 RackLocal:4
2015-10-17 16:47:47,013 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:47,013 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:47,013 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:47,029 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000006 taskAttempt attempt_1445062781478_0018_m_000004_0
2015-10-17 16:47:47,029 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000004_0
2015-10-17 16:47:47,029 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 16:47:47,029 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000007 taskAttempt attempt_1445062781478_0018_m_000005_0
2015-10-17 16:47:47,029 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000005_0
2015-10-17 16:47:47,029 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 16:47:47,045 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000005_0 : 13562
2015-10-17 16:47:47,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000005_0] using containerId: [container_1445062781478_0018_01_000007 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:51951]
2015-10-17 16:47:47,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:47,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000005
2015-10-17 16:47:47,060 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:47,076 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000004_0 : 13562
2015-10-17 16:47:47,076 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000004_0] using containerId: [container_1445062781478_0018_01_000006 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 16:47:47,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:47,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000004
2015-10-17 16:47:47,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:48,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:13312, vCores:-22> knownNMs=5
2015-10-17 16:47:48,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 16:47:48,029 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:48,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000008 to attempt_1445062781478_0018_m_000006_0
2015-10-17 16:47:48,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:13312, vCores:-22>
2015-10-17 16:47:48,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:48,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:2 RackLocal:5
2015-10-17 16:47:48,029 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:48,029 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:48,029 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000008 taskAttempt attempt_1445062781478_0018_m_000006_0
2015-10-17 16:47:48,029 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000006_0
2015-10-17 16:47:48,029 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 16:47:48,232 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000006_0 : 13562
2015-10-17 16:47:48,232 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000006_0] using containerId: [container_1445062781478_0018_01_000008 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 16:47:48,232 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:48,232 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000006
2015-10-17 16:47:48,232 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:49,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:10240, vCores:-25> knownNMs=5
2015-10-17 16:47:49,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 16:47:49,029 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:49,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000009 to attempt_1445062781478_0018_m_000007_0
2015-10-17 16:47:49,029 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:49,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000010 to attempt_1445062781478_0018_m_000008_0
2015-10-17 16:47:49,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-25>
2015-10-17 16:47:49,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:49,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:2 RackLocal:7
2015-10-17 16:47:49,029 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:49,029 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:49,029 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:49,029 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:49,029 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000010 taskAttempt attempt_1445062781478_0018_m_000008_0
2015-10-17 16:47:49,029 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000009 taskAttempt attempt_1445062781478_0018_m_000007_0
2015-10-17 16:47:49,029 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000008_0
2015-10-17 16:47:49,029 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000007_0
2015-10-17 16:47:49,029 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 16:47:49,029 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 16:47:49,185 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:47:49,201 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000007 asked for a task
2015-10-17 16:47:49,201 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000007 given task: attempt_1445062781478_0018_m_000005_0
2015-10-17 16:47:49,342 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000007_0 : 13562
2015-10-17 16:47:49,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000007_0] using containerId: [container_1445062781478_0018_01_000009 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 16:47:49,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:49,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000007
2015-10-17 16:47:49,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:49,529 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000008_0 : 13562
2015-10-17 16:47:49,529 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000008_0] using containerId: [container_1445062781478_0018_01_000010 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:64642]
2015-10-17 16:47:49,529 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:49,529 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000008
2015-10-17 16:47:49,529 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:50,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:6144, vCores:-29> knownNMs=5
2015-10-17 16:47:50,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 16:47:50,029 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:50,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000011 to attempt_1445062781478_0018_m_000009_0
2015-10-17 16:47:50,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Releasing unassigned and invalid container Container: [ContainerId: container_1445062781478_0018_01_000012, NodeId: MININT-FNANLI5.fareast.corp.microsoft.com:64642, NodeHttpAddress: MININT-FNANLI5.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 20, Token: Token { kind: ContainerToken, service: *************:64642 }, ]. RM may have assignment issues
2015-10-17 16:47:50,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-29>
2015-10-17 16:47:50,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:50,029 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:2 RackLocal:8
2015-10-17 16:47:50,029 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:47:50,029 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:47:50,045 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000011 taskAttempt attempt_1445062781478_0018_m_000009_0
2015-10-17 16:47:50,045 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000009_0
2015-10-17 16:47:50,045 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 16:47:50,545 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000009_0 : 13562
2015-10-17 16:47:50,545 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000009_0] using containerId: [container_1445062781478_0018_01_000011 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 16:47:50,545 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:47:50,545 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000009
2015-10-17 16:47:50,545 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:47:51,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 1 newContainers=1 finishedContainers=1 resourcelimit=<memory:4096, vCores:-31> knownNMs=5
2015-10-17 16:47:51,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000012
2015-10-17 16:47:51,045 ERROR [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Container complete event for unknown container id container_1445062781478_0018_01_000012
2015-10-17 16:47:51,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 16:47:51,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Cannot assign container Container: [ContainerId: container_1445062781478_0018_01_000013, NodeId: MININT-75DGDAM1.fareast.corp.microsoft.com:51951, NodeHttpAddress: MININT-75DGDAM1.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 20, Token: Token { kind: ContainerToken, service: ************:51951 }, ] for a map as either  container memory less than required <memory:1024, vCores:1> or no pending map tasks - maps.isEmpty=true
2015-10-17 16:47:51,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-17 16:47:51,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:51,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:12 ContRel:2 HostLocal:2 RackLocal:8
2015-10-17 16:47:51,092 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:47:51,295 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000004 asked for a task
2015-10-17 16:47:51,295 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000004 given task: attempt_1445062781478_0018_m_000002_0
2015-10-17 16:47:52,060 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=0 release= 1 newContainers=0 finishedContainers=1 resourcelimit=<memory:3072, vCores:-32> knownNMs=5
2015-10-17 16:47:52,060 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000013
2015-10-17 16:47:52,060 ERROR [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Container complete event for unknown container id container_1445062781478_0018_01_000013
2015-10-17 16:47:52,060 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-17 16:47:52,060 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:53,076 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-33>
2015-10-17 16:47:53,076 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:47:53,623 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.10635664
2015-10-17 16:47:53,873 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.1066108
2015-10-17 16:47:56,248 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:47:56,483 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:47:56,514 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000005 asked for a task
2015-10-17 16:47:56,514 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000005 given task: attempt_1445062781478_0018_m_000003_0
2015-10-17 16:47:56,686 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.10635664
2015-10-17 16:47:57,014 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.1066108
2015-10-17 16:47:57,029 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000006 asked for a task
2015-10-17 16:47:57,029 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000006 given task: attempt_1445062781478_0018_m_000004_0
2015-10-17 16:47:57,717 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.049306504
2015-10-17 16:47:58,029 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:47:58,326 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000008 asked for a task
2015-10-17 16:47:58,326 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000008 given task: attempt_1445062781478_0018_m_000006_0
2015-10-17 16:47:59,717 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.10635664
2015-10-17 16:48:00,061 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.1066108
2015-10-17 16:48:00,170 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:48:00,436 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000009 asked for a task
2015-10-17 16:48:00,436 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000009 given task: attempt_1445062781478_0018_m_000007_0
2015-10-17 16:48:01,436 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.09788263
2015-10-17 16:48:02,405 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:48:02,498 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000010 asked for a task
2015-10-17 16:48:02,498 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000010 given task: attempt_1445062781478_0018_m_000008_0
2015-10-17 16:48:02,764 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.14638726
2015-10-17 16:48:03,108 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.14822584
2015-10-17 16:48:04,545 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.10685723
2015-10-17 16:48:05,811 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.19158794
2015-10-17 16:48:06,186 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.19211523
2015-10-17 16:48:06,202 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:48:06,248 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000011 asked for a task
2015-10-17 16:48:06,248 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000011 given task: attempt_1445062781478_0018_m_000009_0
2015-10-17 16:48:07,311 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:48:07,358 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:48:07,592 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.10685723
2015-10-17 16:48:09,155 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.19158794
2015-10-17 16:48:09,217 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.19211523
2015-10-17 16:48:10,608 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.10685723
2015-10-17 16:48:11,327 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.060452625
2015-10-17 16:48:12,233 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.19158794
2015-10-17 16:48:12,311 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.19211523
2015-10-17 16:48:13,655 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.10685723
2015-10-17 16:48:15,218 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.080116555
2015-10-17 16:48:15,280 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.26152176
2015-10-17 16:48:15,358 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.21845187
2015-10-17 16:48:16,186 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.010745567
2015-10-17 16:48:16,686 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.115615755
2015-10-17 16:48:18,311 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.08500097
2015-10-17 16:48:18,311 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.27696857
2015-10-17 16:48:18,390 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.27776006
2015-10-17 16:48:19,061 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.01269811
2015-10-17 16:48:19,577 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.017258082
2015-10-17 16:48:19,796 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.11626836
2015-10-17 16:48:21,343 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.27696857
2015-10-17 16:48:21,437 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.27776006
2015-10-17 16:48:21,515 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.091840126
2015-10-17 16:48:22,218 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.020515734
2015-10-17 16:48:22,312 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.016313624
2015-10-17 16:48:22,390 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.029701253
2015-10-17 16:48:22,733 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.026052043
2015-10-17 16:48:22,827 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.12603655
2015-10-17 16:48:24,390 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.27696857
2015-10-17 16:48:24,499 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.27776006
2015-10-17 16:48:24,655 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.10226287
2015-10-17 16:48:25,359 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.02996117
2015-10-17 16:48:25,515 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.031589396
2015-10-17 16:48:25,546 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.057606872
2015-10-17 16:48:25,921 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.030937036
2015-10-17 16:48:26,140 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.13352951
2015-10-17 16:48:27,421 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.33885273
2015-10-17 16:48:27,531 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.33648884
2015-10-17 16:48:27,734 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.10660437
2015-10-17 16:48:28,640 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.04429082
2015-10-17 16:48:28,687 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.09810935
2015-10-17 16:48:28,749 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.039403982
2015-10-17 16:48:29,062 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.037450608
2015-10-17 16:48:29,202 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.16251455
2015-10-17 16:48:30,484 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.3624012
2015-10-17 16:48:30,609 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.36319977
2015-10-17 16:48:31,453 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.10660437
2015-10-17 16:48:32,031 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.056340747
2015-10-17 16:48:32,124 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.14581972
2015-10-17 16:48:32,187 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.06090051
2015-10-17 16:48:32,406 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.045267243
2015-10-17 16:48:32,484 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.19036797
2015-10-17 16:48:33,390 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:48:33,468 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.09965679
2015-10-17 16:48:33,499 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.3624012
2015-10-17 16:48:33,640 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.36319977
2015-10-17 16:48:34,656 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.10660437
2015-10-17 16:48:35,406 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.07197349
2015-10-17 16:48:35,609 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.06610997
2015-10-17 16:48:35,609 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.050152954
2015-10-17 16:48:35,625 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.2178274
2015-10-17 16:48:35,781 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.19247705
2015-10-17 16:48:36,562 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.3624012
2015-10-17 16:48:36,687 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.36319977
2015-10-17 16:48:37,515 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.10680563
2015-10-17 16:48:37,578 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 16:48:37,578 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:48:38,140 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.10780012
2015-10-17 16:48:38,609 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.09509531
2015-10-17 16:48:38,828 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.061878745
2015-10-17 16:48:38,828 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.05731876
2015-10-17 16:48:38,906 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.19247705
2015-10-17 16:48:39,609 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.27903634
2015-10-17 16:48:39,609 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.40444466
2015-10-17 16:48:39,640 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.08562557
2015-10-17 16:48:39,781 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.39137176
2015-10-17 16:48:41,453 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.10680563
2015-10-17 16:48:41,547 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.11398712
2015-10-17 16:48:42,031 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.19247705
2015-10-17 16:48:42,094 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.10291234
2015-10-17 16:48:42,406 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.068391524
2015-10-17 16:48:42,672 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.44789755
2015-10-17 16:48:42,797 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:48:42,844 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.448704
2015-10-17 16:48:43,062 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.106493875
2015-10-17 16:48:43,531 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.295472
2015-10-17 16:48:43,594 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.10681946
2015-10-17 16:48:44,953 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.1201752
2015-10-17 16:48:45,094 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.19247705
2015-10-17 16:48:45,266 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.10680563
2015-10-17 16:48:46,391 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.07816168
2015-10-17 16:48:46,484 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.106881365
2015-10-17 16:48:46,609 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.44789755
2015-10-17 16:48:46,812 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.448704
2015-10-17 16:48:47,297 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.106493875
2015-10-17 16:48:47,344 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.295472
2015-10-17 16:48:47,422 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.10681946
2015-10-17 16:48:48,172 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.19247705
2015-10-17 16:48:48,703 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.13645892
2015-10-17 16:48:49,688 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.44789755
2015-10-17 16:48:49,922 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.448704
2015-10-17 16:48:50,016 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.10680563
2015-10-17 16:48:50,125 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.09509837
2015-10-17 16:48:50,188 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.106881365
2015-10-17 16:48:51,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 16:48:51,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:48:51,219 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.19247705
2015-10-17 16:48:51,360 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.106493875
2015-10-17 16:48:51,375 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.295472
2015-10-17 16:48:51,453 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.10681946
2015-10-17 16:48:52,719 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.14134519
2015-10-17 16:48:52,735 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.5225607
2015-10-17 16:48:52,985 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.5042407
2015-10-17 16:48:53,828 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.10680563
2015-10-17 16:48:54,000 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.10356494
2015-10-17 16:48:54,141 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.106881365
2015-10-17 16:48:54,328 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.20550399
2015-10-17 16:48:55,250 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.10681946
2015-10-17 16:48:55,500 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.295472
2015-10-17 16:48:55,500 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.106493875
2015-10-17 16:48:55,828 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.53341997
2015-10-17 16:48:56,063 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.53425497
2015-10-17 16:48:56,641 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.15144016
2015-10-17 16:48:57,766 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.10680563
2015-10-17 16:48:57,860 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.2169028
2015-10-17 16:48:58,329 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.106964506
2015-10-17 16:48:58,375 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.106881365
2015-10-17 16:48:58,907 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.53341997
2015-10-17 16:48:59,079 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.10681946
2015-10-17 16:48:59,172 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.53425497
2015-10-17 16:48:59,282 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.295472
2015-10-17 16:48:59,594 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.106493875
2015-10-17 16:49:00,907 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.23449159
2015-10-17 16:49:01,235 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.1703302
2015-10-17 16:49:01,641 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.10680563
2015-10-17 16:49:01,969 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.5714406
2015-10-17 16:49:02,219 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.54343766
2015-10-17 16:49:02,266 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.106964506
2015-10-17 16:49:02,469 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.106881365
2015-10-17 16:49:03,532 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.295472
2015-10-17 16:49:03,532 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.10681946
2015-10-17 16:49:03,688 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.106493875
2015-10-17 16:49:04,032 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.25631213
2015-10-17 16:49:05,016 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.61898744
2015-10-17 16:49:05,063 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.19212553
2015-10-17 16:49:05,266 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.6197233
2015-10-17 16:49:05,329 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.10680563
2015-10-17 16:49:06,516 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.106964506
2015-10-17 16:49:06,532 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.106881365
2015-10-17 16:49:07,235 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.10681946
2015-10-17 16:49:07,235 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.295472
2015-10-17 16:49:07,282 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.27813601
2015-10-17 16:49:07,485 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.106493875
2015-10-17 16:49:08,079 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.61898744
2015-10-17 16:49:08,298 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.6197233
2015-10-17 16:49:09,548 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.19212553
2015-10-17 16:49:10,407 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.27813601
2015-10-17 16:49:10,516 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.106881365
2015-10-17 16:49:11,329 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.6197233
2015-10-17 16:49:11,407 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.106493875
2015-10-17 16:49:11,407 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.64021116
2015-10-17 16:49:11,548 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.295472
2015-10-17 16:49:11,548 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.10681946
2015-10-17 16:49:11,657 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.64021116
2015-10-17 16:49:13,267 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.106964506
2015-10-17 16:49:13,470 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.19212553
2015-10-17 16:49:13,517 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.27813601
2015-10-17 16:49:13,720 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.10680563
2015-10-17 16:49:13,876 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.6197233
2015-10-17 16:49:14,392 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.667
2015-10-17 16:49:14,470 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.12440957
2015-10-17 16:49:14,751 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.667
2015-10-17 16:49:15,345 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.10681946
2015-10-17 16:49:15,392 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.106493875
2015-10-17 16:49:15,564 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.295472
2015-10-17 16:49:16,548 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.27813601
2015-10-17 16:49:17,251 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.19212553
2015-10-17 16:49:17,345 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.106964506
2015-10-17 16:49:18,314 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.1537203
2015-10-17 16:49:18,345 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.667
2015-10-17 16:49:18,689 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.667
2015-10-17 16:49:19,126 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.106493875
2015-10-17 16:49:19,376 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.295472
2015-10-17 16:49:19,392 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.10681946
2015-10-17 16:49:19,689 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.27813601
2015-10-17 16:49:21,314 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.19212553
2015-10-17 16:49:21,314 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.106964506
2015-10-17 16:49:21,376 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.67964876
2015-10-17 16:49:21,736 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.700253
2015-10-17 16:49:22,314 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.18646051
2015-10-17 16:49:22,751 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.27813601
2015-10-17 16:49:22,783 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.106493875
2015-10-17 16:49:23,001 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.295472
2015-10-17 16:49:23,064 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.10681946
2015-10-17 16:49:24,408 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.7220512
2015-10-17 16:49:24,814 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.7354739
2015-10-17 16:49:25,158 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.19212553
2015-10-17 16:49:25,314 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.106964506
2015-10-17 16:49:25,767 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.28529647
2015-10-17 16:49:26,548 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.106493875
2015-10-17 16:49:26,580 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.19258286
2015-10-17 16:49:26,720 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.10680563
2015-10-17 16:49:26,970 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.10681946
2015-10-17 16:49:27,126 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.31131834
2015-10-17 16:49:27,595 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.74853593
2015-10-17 16:49:27,923 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.7758066
2015-10-17 16:49:28,814 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.29474127
2015-10-17 16:49:29,017 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.19212553
2015-10-17 16:49:29,220 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.106964506
2015-10-17 16:49:30,017 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.1276659
2015-10-17 16:49:30,267 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.11887241
2015-10-17 16:49:30,423 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:49:30,423 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.3465471
2015-10-17 16:49:30,455 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.19258286
2015-10-17 16:49:30,705 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.7896128
2015-10-17 16:49:31,048 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.8112899
2015-10-17 16:49:31,080 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.1895472
2015-10-17 16:49:31,830 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.30158222
2015-10-17 16:49:33,049 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.19212553
2015-10-17 16:49:33,064 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.106964506
2015-10-17 16:49:33,689 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.14525224
2015-10-17 16:49:33,830 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.8189684
2015-10-17 16:49:33,924 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.14418478
2015-10-17 16:49:34,158 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.8521036
2015-10-17 16:49:34,361 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.37805337
2015-10-17 16:49:34,533 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.19258286
2015-10-17 16:49:34,549 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.19242907
2015-10-17 16:49:34,877 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.30581617
2015-10-17 16:49:36,721 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.110078745
2015-10-17 16:49:36,814 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.19212553
2015-10-17 16:49:36,908 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.8611896
2015-10-17 16:49:37,361 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.15795512
2015-10-17 16:49:37,517 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.89035136
2015-10-17 16:49:37,783 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.42306334
2015-10-17 16:49:37,861 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.1693519
2015-10-17 16:49:37,908 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.31004813
2015-10-17 16:49:38,064 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.19242907
2015-10-17 16:49:38,096 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.19258286
2015-10-17 16:49:39,986 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.8802515
2015-10-17 16:49:40,174 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.11756985
2015-10-17 16:49:40,252 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.19212553
2015-10-17 16:49:40,611 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.9199827
2015-10-17 16:49:40,939 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.32372862
2015-10-17 16:49:41,939 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.4792121
2015-10-17 16:49:42,096 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.18400896
2015-10-17 16:49:42,408 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.19242907
2015-10-17 16:49:42,721 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.19255035
2015-10-17 16:49:42,955 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.19258286
2015-10-17 16:49:43,111 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.9020077
2015-10-17 16:49:43,502 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.122780934
2015-10-17 16:49:43,596 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.19410597
2015-10-17 16:49:43,721 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.9449715
2015-10-17 16:49:43,971 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.33154353
2015-10-17 16:49:46,033 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.5323719
2015-10-17 16:49:46,174 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.19209063
2015-10-17 16:49:46,190 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.92849404
2015-10-17 16:49:46,377 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.19258286
2015-10-17 16:49:46,502 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.19242907
2015-10-17 16:49:46,768 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 0.9745364
2015-10-17 16:49:46,830 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.12929296
2015-10-17 16:49:46,924 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.19736074
2015-10-17 16:49:47,033 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.34131578
2015-10-17 16:49:47,049 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.19255035
2015-10-17 16:49:49,284 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.963156
2015-10-17 16:49:49,455 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000000_0 is : 1.0
2015-10-17 16:49:49,534 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0018_m_000000_0
2015-10-17 16:49:49,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:49:49,534 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000002 taskAttempt attempt_1445062781478_0018_m_000000_0
2015-10-17 16:49:49,534 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000000_0
2015-10-17 16:49:49,534 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:49:49,737 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.19258286
2015-10-17 16:49:49,909 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.5323719
2015-10-17 16:49:50,112 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.19209063
2015-10-17 16:49:50,190 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.3549935
2015-10-17 16:49:50,237 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.13938949
2015-10-17 16:49:50,284 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.2051781
2015-10-17 16:49:50,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000002
2015-10-17 16:49:50,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 16:49:50,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:49:50,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000000_0: 
2015-10-17 16:49:50,612 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:12 ContRel:2 HostLocal:2 RackLocal:8
2015-10-17 16:49:51,049 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.19255035
2015-10-17 16:49:51,112 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.19242907
2015-10-17 16:49:51,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-33>
2015-10-17 16:49:51,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 16:49:52,643 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 0.99123377
2015-10-17 16:49:52,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:49:52,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0018_m_000000_0
2015-10-17 16:49:52,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:49:52,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 16:49:52,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:2 HostLocal:2 RackLocal:8
2015-10-17 16:49:52,706 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-17 16:49:52,706 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 16:49:52,706 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-17 16:49:52,706 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:2 HostLocal:2 RackLocal:8
2015-10-17 16:49:52,846 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.20127119
2015-10-17 16:49:53,018 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0018_m_000006
2015-10-17 16:49:53,018 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 16:49:53,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0018_m_000006
2015-10-17 16:49:53,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:49:53,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:49:53,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:49:53,377 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.14948592
2015-10-17 16:49:53,424 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.21299462
2015-10-17 16:49:53,440 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.36390656
2015-10-17 16:49:53,706 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:2 HostLocal:2 RackLocal:8
2015-10-17 16:49:53,752 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-32> knownNMs=5
2015-10-17 16:49:53,815 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.5323719
2015-10-17 16:49:53,815 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.19209063
2015-10-17 16:49:54,049 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000001_0 is : 1.0
2015-10-17 16:49:54,081 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0018_m_000001_0
2015-10-17 16:49:54,081 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:49:54,081 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000003 taskAttempt attempt_1445062781478_0018_m_000001_0
2015-10-17 16:49:54,081 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000001_0
2015-10-17 16:49:54,081 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:49:54,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:49:54,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0018_m_000001_0
2015-10-17 16:49:54,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:49:54,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 16:49:54,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:12 ContRel:2 HostLocal:2 RackLocal:8
2015-10-17 16:49:54,831 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 16:49:54,831 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 16:49:54,831 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000014 to attempt_1445062781478_0018_r_000000_0
2015-10-17 16:49:54,831 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:13 ContRel:2 HostLocal:2 RackLocal:8
2015-10-17 16:49:54,846 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:49:54,846 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:49:54,846 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000014 taskAttempt attempt_1445062781478_0018_r_000000_0
2015-10-17 16:49:54,846 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_r_000000_0
2015-10-17 16:49:54,846 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:49:54,924 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.19242907
2015-10-17 16:49:55,127 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_r_000000_0 : 13562
2015-10-17 16:49:55,127 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_r_000000_0] using containerId: [container_1445062781478_0018_01_000014 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 16:49:55,127 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:49:55,127 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_r_000000
2015-10-17 16:49:55,127 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 16:49:55,471 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.19255035
2015-10-17 16:49:55,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=1 release= 0 newContainers=1 finishedContainers=1 resourcelimit=<memory:2048, vCores:-33> knownNMs=5
2015-10-17 16:49:55,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000003
2015-10-17 16:49:55,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 16:49:55,893 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:49:55,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000015 to attempt_1445062781478_0018_m_000006_1
2015-10-17 16:49:55,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:14 ContRel:2 HostLocal:3 RackLocal:8
2015-10-17 16:49:55,893 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:49:55,893 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:49:55,893 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000015 taskAttempt attempt_1445062781478_0018_m_000006_1
2015-10-17 16:49:55,893 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000006_1
2015-10-17 16:49:55,893 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:49:56,253 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.21201676
2015-10-17 16:49:56,534 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000006_1 : 13562
2015-10-17 16:49:56,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000006_1] using containerId: [container_1445062781478_0018_01_000015 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 16:49:56,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:49:56,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000006
2015-10-17 16:49:56,784 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.15665236
2015-10-17 16:49:56,815 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:49:56,846 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.21788049
2015-10-17 16:49:56,862 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.36390656
2015-10-17 16:49:56,878 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_r_000014 asked for a task
2015-10-17 16:49:56,878 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_r_000014 given task: attempt_1445062781478_0018_r_000000_0
2015-10-17 16:49:56,987 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-33> knownNMs=5
2015-10-17 16:49:57,690 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.19209063
2015-10-17 16:49:58,034 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 16:49:58,128 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:49:58,174 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000015 asked for a task
2015-10-17 16:49:58,174 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000015 given task: attempt_1445062781478_0018_m_000006_1
2015-10-17 16:49:58,284 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.5323719
2015-10-17 16:49:58,956 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.19242907
2015-10-17 16:49:59,112 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:49:59,550 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.19255035
2015-10-17 16:49:59,815 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.2178809
2015-10-17 16:50:00,143 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:00,175 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.36390656
2015-10-17 16:50:00,221 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.1651192
2015-10-17 16:50:00,315 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.22211376
2015-10-17 16:50:01,190 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:01,628 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.19209063
2015-10-17 16:50:02,440 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.5323719
2015-10-17 16:50:02,518 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:02,893 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.19242907
2015-10-17 16:50:03,143 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.22406857
2015-10-17 16:50:03,315 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.19255035
2015-10-17 16:50:03,409 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.36390656
2015-10-17 16:50:03,487 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.17130776
2015-10-17 16:50:03,581 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:03,722 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.22960511
2015-10-17 16:50:04,956 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:04,956 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:05,284 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.19209063
2015-10-17 16:50:05,675 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.106964506
2015-10-17 16:50:06,659 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.36390656
2015-10-17 16:50:06,675 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.5323719
2015-10-17 16:50:06,737 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.19242907
2015-10-17 16:50:06,878 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:06,987 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.18531261
2015-10-17 16:50:06,987 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.19255035
2015-10-17 16:50:07,206 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.2309072
2015-10-17 16:50:07,206 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.23741962
2015-10-17 16:50:07,909 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:08,019 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0018_m_000004
2015-10-17 16:50:08,019 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 16:50:08,019 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0018_m_000004
2015-10-17 16:50:08,019 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:50:08,019 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:50:08,019 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000004_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:50:08,019 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:08,034 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:14 ContRel:2 HostLocal:3 RackLocal:8
2015-10-17 16:50:08,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-24> knownNMs=5
2015-10-17 16:50:08,753 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.106964506
2015-10-17 16:50:09,394 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:09,394 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.19209063
2015-10-17 16:50:09,550 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 16:50:09,550 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000016 to attempt_1445062781478_0018_m_000004_1
2015-10-17 16:50:09,550 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:15 ContRel:2 HostLocal:4 RackLocal:8
2015-10-17 16:50:09,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:50:09,550 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000004_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:50:09,550 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000016 taskAttempt attempt_1445062781478_0018_m_000004_1
2015-10-17 16:50:09,550 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000004_1
2015-10-17 16:50:09,550 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:50:09,691 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000004_1 : 13562
2015-10-17 16:50:09,691 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000004_1] using containerId: [container_1445062781478_0018_01_000016 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 16:50:09,691 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000004_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:50:09,691 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000004
2015-10-17 16:50:09,831 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.36390656
2015-10-17 16:50:10,425 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:10,503 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.5323719
2015-10-17 16:50:10,581 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-25> knownNMs=5
2015-10-17 16:50:10,581 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.19266446
2015-10-17 16:50:10,581 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.19242907
2015-10-17 16:50:12,722 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.12969144
2015-10-17 16:50:12,862 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.36574054
2015-10-17 16:50:13,097 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.19255035
2015-10-17 16:50:13,097 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.19209063
2015-10-17 16:50:13,472 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.246216
2015-10-17 16:50:14,394 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.21270116
2015-10-17 16:50:14,472 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.5323719
2015-10-17 16:50:14,644 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.19266446
2015-10-17 16:50:15,613 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:15,613 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:15,706 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.24067803
2015-10-17 16:50:15,800 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.19266446
2015-10-17 16:50:15,894 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.36899686
2015-10-17 16:50:16,535 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:50:16,628 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000016 asked for a task
2015-10-17 16:50:16,628 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000016 given task: attempt_1445062781478_0018_m_000004_1
2015-10-17 16:50:16,706 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:16,863 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.19209063
2015-10-17 16:50:16,863 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.19255035
2015-10-17 16:50:17,363 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.27715582
2015-10-17 16:50:17,785 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:18,456 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.5323719
2015-10-17 16:50:18,660 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:18,675 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.19266446
2015-10-17 16:50:18,707 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.24735752
2015-10-17 16:50:18,785 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:18,847 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.19266446
2015-10-17 16:50:18,925 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.37486154
2015-10-17 16:50:19,738 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.27811313
2015-10-17 16:50:19,832 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:20,550 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.19255035
2015-10-17 16:50:20,738 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.19209063
2015-10-17 16:50:20,894 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:21,722 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:21,878 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.19266446
2015-10-17 16:50:21,894 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:21,972 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.38463125
2015-10-17 16:50:22,254 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.27772525
2015-10-17 16:50:22,425 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.2781602
2015-10-17 16:50:22,566 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.19266446
2015-10-17 16:50:22,957 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:23,035 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0018_m_000003
2015-10-17 16:50:23,035 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 16:50:23,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0018_m_000003
2015-10-17 16:50:23,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:50:23,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:50:23,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000003_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:50:23,191 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.5323719
2015-10-17 16:50:23,504 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.27811313
2015-10-17 16:50:24,004 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:15 ContRel:2 HostLocal:4 RackLocal:8
2015-10-17 16:50:24,035 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:24,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-25> knownNMs=5
2015-10-17 16:50:24,238 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.10680563
2015-10-17 16:50:24,254 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.19255035
2015-10-17 16:50:24,472 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.19209063
2015-10-17 16:50:25,004 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.3973322
2015-10-17 16:50:25,675 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:25,675 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:26,707 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:26,832 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.19266446
2015-10-17 16:50:26,926 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.2781602
2015-10-17 16:50:27,082 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.24038652
2015-10-17 16:50:27,176 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.5323719
2015-10-17 16:50:27,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 16:50:27,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000017 to attempt_1445062781478_0018_m_000003_1
2015-10-17 16:50:27,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:16 ContRel:2 HostLocal:5 RackLocal:8
2015-10-17 16:50:27,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:50:27,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000003_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:50:27,222 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000017 taskAttempt attempt_1445062781478_0018_m_000003_1
2015-10-17 16:50:27,222 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000003_1
2015-10-17 16:50:27,222 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:50:27,238 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.27811313
2015-10-17 16:50:27,285 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.10680563
2015-10-17 16:50:27,472 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000003_1 : 13562
2015-10-17 16:50:27,472 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000003_1] using containerId: [container_1445062781478_0018_01_000017 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 16:50:27,472 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000003_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:50:27,472 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000003
2015-10-17 16:50:27,691 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.27772525
2015-10-17 16:50:27,722 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:28,129 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.21689148
2015-10-17 16:50:28,129 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.19291376
2015-10-17 16:50:28,129 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.40124115
2015-10-17 16:50:28,285 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-26> knownNMs=5
2015-10-17 16:50:28,785 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:28,785 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:29,863 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:30,191 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.2783809
2015-10-17 16:50:30,238 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:50:30,285 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.2781602
2015-10-17 16:50:30,301 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000017 asked for a task
2015-10-17 16:50:30,301 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000017 given task: attempt_1445062781478_0018_m_000003_1
2015-10-17 16:50:30,363 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.10680563
2015-10-17 16:50:30,504 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.57158196
2015-10-17 16:50:31,207 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.40808123
2015-10-17 16:50:31,488 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.23677076
2015-10-17 16:50:31,582 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.21071394
2015-10-17 16:50:31,629 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.27772525
2015-10-17 16:50:31,644 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.27811313
2015-10-17 16:50:31,785 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:31,832 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:31,832 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.19266446
2015-10-17 16:50:32,801 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:33,269 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.2783809
2015-10-17 16:50:33,394 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.10680563
2015-10-17 16:50:33,551 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.2781602
2015-10-17 16:50:33,785 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.6174906
2015-10-17 16:50:33,832 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:34,379 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.40840673
2015-10-17 16:50:35,551 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.19266446
2015-10-17 16:50:35,660 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.25077254
2015-10-17 16:50:35,691 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.22113818
2015-10-17 16:50:35,738 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:35,754 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:36,473 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.27811313
2015-10-17 16:50:36,473 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.27772525
2015-10-17 16:50:36,613 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.36404583
2015-10-17 16:50:36,738 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.16500784
2015-10-17 16:50:36,770 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:37,145 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.106493875
2015-10-17 16:50:37,363 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.2781602
2015-10-17 16:50:37,442 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.667
2015-10-17 16:50:37,442 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.667
2015-10-17 16:50:37,457 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.41524515
2015-10-17 16:50:37,848 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:38,035 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0018_m_000007
2015-10-17 16:50:38,035 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 16:50:38,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0018_m_000007
2015-10-17 16:50:38,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:50:38,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:50:38,035 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000007_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:50:38,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:16 ContRel:2 HostLocal:5 RackLocal:8
2015-10-17 16:50:38,692 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-26> knownNMs=5
2015-10-17 16:50:38,817 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:38,895 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:39,582 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.24085742
2015-10-17 16:50:39,660 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.36404583
2015-10-17 16:50:39,738 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 16:50:39,738 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:50:39,738 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000018 to attempt_1445062781478_0018_m_000007_1
2015-10-17 16:50:39,738 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:17 ContRel:2 HostLocal:5 RackLocal:9
2015-10-17 16:50:39,738 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:50:39,738 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000007_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:50:39,738 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000018 taskAttempt attempt_1445062781478_0018_m_000007_1
2015-10-17 16:50:39,738 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000007_1
2015-10-17 16:50:39,738 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 16:50:39,785 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.19242907
2015-10-17 16:50:39,895 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.19266446
2015-10-17 16:50:39,895 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.27505064
2015-10-17 16:50:39,926 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:40,067 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000007_1 : 13562
2015-10-17 16:50:40,067 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000007_1] using containerId: [container_1445062781478_0018_01_000018 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:51951]
2015-10-17 16:50:40,067 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000007_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:50:40,067 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000007
2015-10-17 16:50:40,176 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.106493875
2015-10-17 16:50:40,457 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.27772525
2015-10-17 16:50:40,614 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.27811313
2015-10-17 16:50:40,801 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:8192, vCores:-27> knownNMs=5
2015-10-17 16:50:40,817 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.43413413
2015-10-17 16:50:40,973 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:41,379 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.2781602
2015-10-17 16:50:41,520 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.667
2015-10-17 16:50:42,692 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.36404583
2015-10-17 16:50:42,754 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:42,754 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:42,848 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.19242907
2015-10-17 16:50:43,207 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.11999471
2015-10-17 16:50:43,582 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.26937807
2015-10-17 16:50:43,801 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:44,051 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.27825075
2015-10-17 16:50:44,114 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.27772525
2015-10-17 16:50:44,192 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.44553408
2015-10-17 16:50:44,770 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.27811313
2015-10-17 16:50:44,848 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:45,036 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.21560144
2015-10-17 16:50:45,364 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.2781602
2015-10-17 16:50:45,661 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.667
2015-10-17 16:50:45,801 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.44980705
2015-10-17 16:50:45,895 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:45,895 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.19242907
2015-10-17 16:50:45,926 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:46,317 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.19209063
2015-10-17 16:50:46,989 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:47,254 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:50:47,286 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000018 asked for a task
2015-10-17 16:50:47,286 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000018 given task: attempt_1445062781478_0018_m_000007_1
2015-10-17 16:50:47,301 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.44950968
2015-10-17 16:50:47,692 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.27825075
2015-10-17 16:50:47,723 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.27765483
2015-10-17 16:50:47,864 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.27772525
2015-10-17 16:50:48,020 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:48,754 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.27811313
2015-10-17 16:50:48,879 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.44980705
2015-10-17 16:50:48,942 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.2781602
2015-10-17 16:50:48,958 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:49,098 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:49,098 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.24502751
2015-10-17 16:50:49,395 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.19209063
2015-10-17 16:50:49,395 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.2781602
2015-10-17 16:50:49,520 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.667
2015-10-17 16:50:50,176 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:50,395 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.44950968
2015-10-17 16:50:51,223 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:51,614 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.27765483
2015-10-17 16:50:51,661 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.27825075
2015-10-17 16:50:51,770 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.27772525
2015-10-17 16:50:51,989 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.44980705
2015-10-17 16:50:52,051 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.2781602
2015-10-17 16:50:52,098 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:52,239 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:52,458 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.19209063
2015-10-17 16:50:52,676 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.27811313
2015-10-17 16:50:53,051 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0018_m_000002
2015-10-17 16:50:53,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0018_m_000002
2015-10-17 16:50:53,051 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 16:50:53,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:50:53,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:50:53,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000002_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:50:53,051 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.27478868
2015-10-17 16:50:53,426 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.44950968
2015-10-17 16:50:53,630 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.667
2015-10-17 16:50:53,661 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:53,708 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.2781602
2015-10-17 16:50:54,083 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:17 ContRel:2 HostLocal:5 RackLocal:9
2015-10-17 16:50:54,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:8192, vCores:-27> knownNMs=5
2015-10-17 16:50:54,692 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:55,067 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.5053482
2015-10-17 16:50:55,145 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.2781602
2015-10-17 16:50:55,161 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 16:50:55,161 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000019 to attempt_1445062781478_0018_m_000002_1
2015-10-17 16:50:55,161 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:18 ContRel:2 HostLocal:6 RackLocal:9
2015-10-17 16:50:55,161 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:50:55,177 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000002_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:50:55,177 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000019 taskAttempt attempt_1445062781478_0018_m_000002_1
2015-10-17 16:50:55,177 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000002_1
2015-10-17 16:50:55,177 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:50:55,364 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:55,473 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000002_1 : 13562
2015-10-17 16:50:55,473 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000002_1] using containerId: [container_1445062781478_0018_01_000019 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 16:50:55,473 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000002_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:50:55,473 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000002
2015-10-17 16:50:55,473 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.27765483
2015-10-17 16:50:55,520 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.27825075
2015-10-17 16:50:55,536 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.27765483
2015-10-17 16:50:55,567 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.27772525
2015-10-17 16:50:55,770 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:55,989 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.025731558
2015-10-17 16:50:56,192 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-28> knownNMs=5
2015-10-17 16:50:56,395 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.27811313
2015-10-17 16:50:56,473 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.44950968
2015-10-17 16:50:56,645 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.2783809
2015-10-17 16:50:56,833 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:57,348 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.667
2015-10-17 16:50:58,567 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.27765483
2015-10-17 16:50:58,708 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.2781602
2015-10-17 16:50:59,052 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.029959897
2015-10-17 16:50:59,505 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.44950968
2015-10-17 16:50:59,520 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:50:59,520 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:50:59,520 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.27765483
2015-10-17 16:50:59,567 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.27825075
2015-10-17 16:50:59,786 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.28171363
2015-10-17 16:50:59,989 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.2963689
2015-10-17 16:51:00,130 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.2783809
2015-10-17 16:51:00,270 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.53543663
2015-10-17 16:51:00,302 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.34676763
2015-10-17 16:51:00,567 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:01,052 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:51:01,099 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000019 asked for a task
2015-10-17 16:51:01,099 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000019 given task: attempt_1445062781478_0018_m_000002_1
2015-10-17 16:51:01,146 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.667
2015-10-17 16:51:01,614 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:01,630 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.27765483
2015-10-17 16:51:02,083 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.037125036
2015-10-17 16:51:02,536 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.2781602
2015-10-17 16:51:02,536 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.4546535
2015-10-17 16:51:02,552 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:02,630 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:03,505 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.2944159
2015-10-17 16:51:03,505 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.3051643
2015-10-17 16:51:03,505 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.27765483
2015-10-17 16:51:03,505 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.27825075
2015-10-17 16:51:03,505 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.53543663
2015-10-17 16:51:03,505 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.36388028
2015-10-17 16:51:03,505 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.2783809
2015-10-17 16:51:03,646 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:04,443 INFO [LeaseRenewer:msrabi@msra-sa-41:9000] org.apache.hadoop.ipc.Client: Retrying connect to server: msra-sa-41/**************:9000. Already tried 0 time(s); maxRetries=45
2015-10-17 16:51:04,708 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.36323506
2015-10-17 16:51:04,708 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:04,943 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.667
2015-10-17 16:51:05,114 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.041684087
2015-10-17 16:51:05,599 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.45953804
2015-10-17 16:51:05,693 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:06,083 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:06,224 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.30791312
2015-10-17 16:51:06,599 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.3700425
2015-10-17 16:51:06,630 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.59326494
2015-10-17 16:51:07,036 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.30679274
2015-10-17 16:51:07,068 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.2783809
2015-10-17 16:51:07,099 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.32079646
2015-10-17 16:51:07,193 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:07,286 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.27765483
2015-10-17 16:51:07,349 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.27825075
2015-10-17 16:51:07,740 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.36323506
2015-10-17 16:51:08,068 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0018_m_000008
2015-10-17 16:51:08,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0018_m_000008
2015-10-17 16:51:08,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:51:08,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:51:08,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000008_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:51:08,068 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 16:51:08,146 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.045919403
2015-10-17 16:51:08,255 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:08,333 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:18 ContRel:2 HostLocal:6 RackLocal:9
2015-10-17 16:51:08,380 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-28> knownNMs=5
2015-10-17 16:51:08,630 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.4667035
2015-10-17 16:51:08,740 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.667
2015-10-17 16:51:08,755 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:09,208 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.10089134
2015-10-17 16:51:09,318 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:09,458 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 16:51:09,458 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000020 to attempt_1445062781478_0018_m_000008_1
2015-10-17 16:51:09,458 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:19 ContRel:2 HostLocal:7 RackLocal:9
2015-10-17 16:51:09,458 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:51:09,458 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000008_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:51:09,458 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000020 taskAttempt attempt_1445062781478_0018_m_000008_1
2015-10-17 16:51:09,458 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000008_1
2015-10-17 16:51:09,458 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:51:09,693 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.4337281
2015-10-17 16:51:09,740 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.6210422
2015-10-17 16:51:09,786 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000008_1 : 13562
2015-10-17 16:51:09,786 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000008_1] using containerId: [container_1445062781478_0018_01_000020 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 16:51:09,786 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000008_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:51:09,786 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000008
2015-10-17 16:51:09,849 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.3430047
2015-10-17 16:51:10,380 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.3175381
2015-10-17 16:51:10,411 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:10,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:6144, vCores:-29> knownNMs=5
2015-10-17 16:51:10,536 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.32958937
2015-10-17 16:51:10,536 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.2783809
2015-10-17 16:51:10,833 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.36323506
2015-10-17 16:51:11,193 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.27765483
2015-10-17 16:51:11,208 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.05145515
2015-10-17 16:51:11,505 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:11,630 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.27825075
2015-10-17 16:51:11,677 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.47386947
2015-10-17 16:51:11,833 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:12,271 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.10660437
2015-10-17 16:51:12,568 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:12,755 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:51:12,755 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.44968578
2015-10-17 16:51:12,787 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.667
2015-10-17 16:51:12,833 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000020 asked for a task
2015-10-17 16:51:12,833 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000020 given task: attempt_1445062781478_0018_m_000008_1
2015-10-17 16:51:12,833 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.6210422
2015-10-17 16:51:13,599 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:13,708 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.36388028
2015-10-17 16:51:13,787 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.331544
2015-10-17 16:51:13,912 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.3865058
2015-10-17 16:51:14,005 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.2783809
2015-10-17 16:51:14,115 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.3374058
2015-10-17 16:51:14,255 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.061550543
2015-10-17 16:51:14,615 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:14,740 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.4839621
2015-10-17 16:51:14,896 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:14,943 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.27765483
2015-10-17 16:51:15,287 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.27825075
2015-10-17 16:51:15,318 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.10660437
2015-10-17 16:51:15,662 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:15,818 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.44968578
2015-10-17 16:51:15,927 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.6210422
2015-10-17 16:51:16,224 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.667
2015-10-17 16:51:16,677 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:16,959 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.4486067
2015-10-17 16:51:17,334 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.07229889
2015-10-17 16:51:17,693 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:17,787 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.49438646
2015-10-17 16:51:17,834 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.36388028
2015-10-17 16:51:17,912 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.3479937
2015-10-17 16:51:17,974 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:18,021 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.2783809
2015-10-17 16:51:18,084 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.34489805
2015-10-17 16:51:18,412 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.10660437
2015-10-17 16:51:18,584 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.27765483
2015-10-17 16:51:18,787 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:18,818 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.6210422
2015-10-17 16:51:18,896 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.44968578
2015-10-17 16:51:18,943 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.27825075
2015-10-17 16:51:18,990 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.667
2015-10-17 16:51:19,849 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:20,052 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.4486067
2015-10-17 16:51:20,349 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.106881365
2015-10-17 16:51:20,365 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.08532747
2015-10-17 16:51:20,459 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.6691635
2015-10-17 16:51:20,818 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.50578636
2015-10-17 16:51:20,927 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:21,068 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:21,459 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.36388028
2015-10-17 16:51:21,506 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.19212553
2015-10-17 16:51:21,678 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.2882277
2015-10-17 16:51:21,787 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.3501061
2015-10-17 16:51:21,787 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.35625336
2015-10-17 16:51:21,959 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.49682483
2015-10-17 16:51:21,974 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:22,084 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.667
2015-10-17 16:51:22,396 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.27765483
2015-10-17 16:51:22,709 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.27825075
2015-10-17 16:51:23,068 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:23,084 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0018_m_000005
2015-10-17 16:51:23,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445062781478_0018_m_000005
2015-10-17 16:51:23,084 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 16:51:23,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:51:23,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:51:23,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000005_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 16:51:23,115 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.4486067
2015-10-17 16:51:23,412 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.106881365
2015-10-17 16:51:23,428 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.09118914
2015-10-17 16:51:23,990 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.5155569
2015-10-17 16:51:23,990 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.6890749
2015-10-17 16:51:24,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:19 ContRel:2 HostLocal:7 RackLocal:9
2015-10-17 16:51:24,115 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:24,553 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:6144, vCores:-29> knownNMs=5
2015-10-17 16:51:24,553 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:25,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 16:51:25,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445062781478_0018_01_000021 to attempt_1445062781478_0018_m_000005_1
2015-10-17 16:51:25,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:15 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:51:25,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 16:51:25,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000005_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 16:51:25,584 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445062781478_0018_01_000021 taskAttempt attempt_1445062781478_0018_m_000005_1
2015-10-17 16:51:25,584 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445062781478_0018_m_000005_1
2015-10-17 16:51:25,584 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:51:25,615 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.3559714
2015-10-17 16:51:25,709 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445062781478_0018_m_000005_1 : 13562
2015-10-17 16:51:25,709 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445062781478_0018_m_000005_1] using containerId: [container_1445062781478_0018_01_000021 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:42313]
2015-10-17 16:51:25,709 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000005_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 16:51:25,709 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445062781478_0018_m_000005
2015-10-17 16:51:25,834 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.29734683
2015-10-17 16:51:25,928 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.5352028
2015-10-17 16:51:25,990 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.3605308
2015-10-17 16:51:26,037 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.667
2015-10-17 16:51:26,053 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:26,162 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.4789938
2015-10-17 16:51:26,225 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.36388028
2015-10-17 16:51:26,256 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.27765483
2015-10-17 16:51:26,428 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.29737276
2015-10-17 16:51:26,459 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.106881365
2015-10-17 16:51:26,568 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.093467705
2015-10-17 16:51:26,646 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445062781478_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:5120, vCores:-30> knownNMs=5
2015-10-17 16:51:26,709 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.19212553
2015-10-17 16:51:27,053 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.5168594
2015-10-17 16:51:27,615 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.7112107
2015-10-17 16:51:27,740 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:51:27,787 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445062781478_0018_m_000021 asked for a task
2015-10-17 16:51:27,787 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445062781478_0018_m_000021 given task: attempt_1445062781478_0018_m_000005_1
2015-10-17 16:51:28,975 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.5352028
2015-10-17 16:51:29,100 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.68464375
2015-10-17 16:51:29,162 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.36313522
2015-10-17 16:51:29,225 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.5343203
2015-10-17 16:51:29,318 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.31982076
2015-10-17 16:51:29,522 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.19258286
2015-10-17 16:51:29,615 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.3637686
2015-10-17 16:51:29,803 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.19212553
2015-10-17 16:51:29,803 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:29,803 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:29,865 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.09965676
2015-10-17 16:51:30,053 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.2908374
2015-10-17 16:51:30,131 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.36388028
2015-10-17 16:51:30,178 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.52467316
2015-10-17 16:51:30,397 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.33161262
2015-10-17 16:51:30,881 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:31,381 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.7303361
2015-10-17 16:51:32,569 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.5343203
2015-10-17 16:51:32,647 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.36317363
2015-10-17 16:51:32,897 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.10681946
2015-10-17 16:51:33,240 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.53249174
2015-10-17 16:51:33,569 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:33,662 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.3637686
2015-10-17 16:51:33,725 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.24625836
2015-10-17 16:51:33,897 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:34,600 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.3403383
2015-10-17 16:51:34,600 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:34,631 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.35996425
2015-10-17 16:51:34,647 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.19258286
2015-10-17 16:51:35,006 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.32546628
2015-10-17 16:51:35,209 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.36388028
2015-10-17 16:51:35,459 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.7499672
2015-10-17 16:51:35,600 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.54507476
2015-10-17 16:51:35,631 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:35,756 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.10685723
2015-10-17 16:51:35,944 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.10681946
2015-10-17 16:51:36,272 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.5352021
2015-10-17 16:51:36,631 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:36,647 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.72951955
2015-10-17 16:51:36,756 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.27772525
2015-10-17 16:51:36,850 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.36317363
2015-10-17 16:51:36,928 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:37,459 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.3637686
2015-10-17 16:51:37,678 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:37,678 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.21803707
2015-10-17 16:51:38,413 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.3638923
2015-10-17 16:51:38,647 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.6199081
2015-10-17 16:51:38,663 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.3599502
2015-10-17 16:51:38,694 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:38,803 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.10685723
2015-10-17 16:51:38,991 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.11522383
2015-10-17 16:51:39,100 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.35726097
2015-10-17 16:51:39,303 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.36388028
2015-10-17 16:51:39,319 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.5352021
2015-10-17 16:51:39,694 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.82172185
2015-10-17 16:51:39,725 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:39,788 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.7701492
2015-10-17 16:51:39,819 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.27772525
2015-10-17 16:51:39,975 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:40,678 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.27811313
2015-10-17 16:51:40,741 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:40,959 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.36317363
2015-10-17 16:51:41,397 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.5352028
2015-10-17 16:51:41,428 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.3637686
2015-10-17 16:51:41,725 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.6199081
2015-10-17 16:51:41,772 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:41,881 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.11506424
2015-10-17 16:51:42,022 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.134505
2015-10-17 16:51:42,272 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.3638923
2015-10-17 16:51:42,350 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.53835404
2015-10-17 16:51:42,772 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.8518145
2015-10-17 16:51:42,819 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.36404583
2015-10-17 16:51:42,835 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:42,881 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.27772525
2015-10-17 16:51:42,960 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.36323506
2015-10-17 16:51:43,053 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:43,256 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.36388028
2015-10-17 16:51:43,553 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.79125774
2015-10-17 16:51:43,725 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.27811313
2015-10-17 16:51:43,913 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:44,616 INFO [Socket Reader #1 for port 53419] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445062781478_0018 (auth:SIMPLE)
2015-10-17 16:51:44,663 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.6208445
2015-10-17 16:51:44,772 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.6199081
2015-10-17 16:51:44,788 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.36317363
2015-10-17 16:51:44,960 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:44,960 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.18258534
2015-10-17 16:51:45,116 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.15013666
2015-10-17 16:51:45,335 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.3637686
2015-10-17 16:51:45,444 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.5500787
2015-10-17 16:51:45,850 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.87485087
2015-10-17 16:51:45,944 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.3638923
2015-10-17 16:51:45,960 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.30219814
2015-10-17 16:51:45,991 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:46,163 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:46,569 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.36323506
2015-10-17 16:51:46,663 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.36404583
2015-10-17 16:51:46,788 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.27811313
2015-10-17 16:51:46,788 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.6208445
2015-10-17 16:51:46,975 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.36388028
2015-10-17 16:51:47,038 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:47,210 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.81114846
2015-10-17 16:51:47,741 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.667
2015-10-17 16:51:47,819 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.6658774
2015-10-17 16:51:47,897 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.6658774
2015-10-17 16:51:48,069 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.19247705
2015-10-17 16:51:48,085 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:48,460 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.18173133
2015-10-17 16:51:48,522 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.36317363
2015-10-17 16:51:48,882 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.57059735
2015-10-17 16:51:48,944 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.8972024
2015-10-17 16:51:49,054 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.36317363
2015-10-17 16:51:49,132 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.3637686
2015-10-17 16:51:49,132 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:49,225 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:49,694 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.3638923
2015-10-17 16:51:49,835 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.3551009
2015-10-17 16:51:50,163 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:50,460 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.36323506
2015-10-17 16:51:50,741 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.667
2015-10-17 16:51:50,850 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.667
2015-10-17 16:51:50,866 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.36388028
2015-10-17 16:51:51,022 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.36404583
2015-10-17 16:51:51,038 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.8330686
2015-10-17 16:51:51,147 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.19247705
2015-10-17 16:51:51,194 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:51,679 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.19255035
2015-10-17 16:51:52,085 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.5878564
2015-10-17 16:51:52,804 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.36317363
2015-10-17 16:51:52,866 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.3637686
2015-10-17 16:51:52,866 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.9374864
2015-10-17 16:51:52,944 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.3637686
2015-10-17 16:51:52,976 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.36317363
2015-10-17 16:51:53,116 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:53,116 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:53,319 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.3638923
2015-10-17 16:51:53,819 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.667
2015-10-17 16:51:53,929 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.667
2015-10-17 16:51:54,647 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:54,663 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.36388028
2015-10-17 16:51:54,913 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.36323506
2015-10-17 16:51:54,944 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.19255035
2015-10-17 16:51:55,116 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.19247705
2015-10-17 16:51:55,147 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.8540519
2015-10-17 16:51:55,444 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.6070898
2015-10-17 16:51:55,663 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.36404583
2015-10-17 16:51:55,679 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:56,757 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:56,757 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:51:56,804 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.3637686
2015-10-17 16:51:56,851 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.6864869
2015-10-17 16:51:56,944 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.69243294
2015-10-17 16:51:57,054 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.36476165
2015-10-17 16:51:57,069 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.3638923
2015-10-17 16:51:57,460 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.36769325
2015-10-17 16:51:57,804 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:58,023 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 0.97366977
2015-10-17 16:51:58,101 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_1 is : 1.0
2015-10-17 16:51:58,148 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0018_m_000006_1
2015-10-17 16:51:58,148 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000006_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:51:58,148 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000015 taskAttempt attempt_1445062781478_0018_m_000006_1
2015-10-17 16:51:58,148 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000006_1
2015-10-17 16:51:58,148 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:51:58,148 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.36317363
2015-10-17 16:51:58,194 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.2661038
2015-10-17 16:51:58,398 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.19255035
2015-10-17 16:51:58,491 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.37323174
2015-10-17 16:51:58,679 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.36323506
2015-10-17 16:51:58,773 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.61553967
2015-10-17 16:51:58,866 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:51:59,148 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.36404583
2015-10-17 16:51:59,148 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.87442565
2015-10-17 16:52:00,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000015
2015-10-17 16:52:00,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:52:00,648 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.06666667
2015-10-17 16:52:00,648 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000006_1: 
2015-10-17 16:52:00,648 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:52:00,648 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.72209394
2015-10-17 16:52:00,648 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.3790504
2015-10-17 16:52:00,648 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.72204506
2015-10-17 16:52:00,757 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.3638923
2015-10-17 16:52:00,929 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.37714085
2015-10-17 16:52:01,195 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.44859612
2015-10-17 16:52:01,351 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000006_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:52:01,351 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0018_m_000006_1
2015-10-17 16:52:01,351 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0018_m_000006_0
2015-10-17 16:52:01,351 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:52:01,351 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 16:52:01,351 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000006_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 16:52:01,351 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000008 taskAttempt attempt_1445062781478_0018_m_000006_0
2015-10-17 16:52:01,351 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000006_0
2015-10-17 16:52:01,351 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 16:52:01,351 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.27813601
2015-10-17 16:52:01,491 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0018_m_000006
2015-10-17 16:52:01,491 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 16:52:01,679 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 16:52:01,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:52:01,804 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.19255035
2015-10-17 16:52:02,085 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.6209487
2015-10-17 16:52:02,648 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.36323506
2015-10-17 16:52:02,648 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.40672186
2015-10-17 16:52:02,726 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:02,882 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.89498514
2015-10-17 16:52:03,195 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.36404583
2015-10-17 16:52:03,617 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.36867037
2015-10-17 16:52:03,663 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.44950172
2015-10-17 16:52:03,695 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.7649475
2015-10-17 16:52:03,695 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.10000001
2015-10-17 16:52:03,695 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.7673383
2015-10-17 16:52:04,679 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:04,726 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.27813601
2015-10-17 16:52:04,945 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.3638923
2015-10-17 16:52:04,960 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.3862597
2015-10-17 16:52:04,992 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.19255035
2015-10-17 16:52:05,132 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.44859612
2015-10-17 16:52:05,398 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.6209487
2015-10-17 16:52:05,726 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:06,476 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.4402393
2015-10-17 16:52:06,585 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.36323506
2015-10-17 16:52:06,742 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.44950172
2015-10-17 16:52:06,757 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.7961482
2015-10-17 16:52:06,757 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.8187411
2015-10-17 16:52:06,789 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:06,789 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.10000001
2015-10-17 16:52:06,820 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.91507196
2015-10-17 16:52:06,835 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.36404583
2015-10-17 16:52:07,773 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.38910967
2015-10-17 16:52:07,773 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.27813601
2015-10-17 16:52:07,820 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:08,179 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.19255035
2015-10-17 16:52:08,445 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.6209487
2015-10-17 16:52:08,820 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:09,570 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.39603034
2015-10-17 16:52:09,789 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.44950172
2015-10-17 16:52:09,789 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.87335664
2015-10-17 16:52:09,804 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.82576686
2015-10-17 16:52:09,836 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:09,836 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.10000001
2015-10-17 16:52:10,320 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.36323506
2015-10-17 16:52:10,320 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.36404583
2015-10-17 16:52:10,336 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.44859612
2015-10-17 16:52:10,414 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.44968578
2015-10-17 16:52:10,711 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.9366807
2015-10-17 16:52:10,836 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.27813601
2015-10-17 16:52:10,867 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:11,054 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.40938312
2015-10-17 16:52:11,257 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.19255035
2015-10-17 16:52:11,507 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.6209487
2015-10-17 16:52:11,601 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.3638923
2015-10-17 16:52:11,961 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:12,883 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.9277077
2015-10-17 16:52:12,883 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.5330913
2015-10-17 16:52:12,898 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.8543418
2015-10-17 16:52:12,929 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.4155694
2015-10-17 16:52:12,929 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.10000001
2015-10-17 16:52:13,039 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:13,461 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.52463275
2015-10-17 16:52:13,914 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.35496134
2015-10-17 16:52:14,086 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:14,195 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.36323506
2015-10-17 16:52:14,304 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.21690392
2015-10-17 16:52:14,429 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.38169977
2015-10-17 16:52:14,445 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.419154
2015-10-17 16:52:14,476 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.44968578
2015-10-17 16:52:14,539 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.6209487
2015-10-17 16:52:14,648 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.9555711
2015-10-17 16:52:15,179 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:15,351 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.3638923
2015-10-17 16:52:15,914 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.53521925
2015-10-17 16:52:15,914 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 0.9828346
2015-10-17 16:52:15,930 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.8820316
2015-10-17 16:52:15,976 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.10000001
2015-10-17 16:52:16,226 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:16,258 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.42468902
2015-10-17 16:52:16,492 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0018_m_000006
2015-10-17 16:52:16,492 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 16:52:16,508 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.5342037
2015-10-17 16:52:16,961 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.36390656
2015-10-17 16:52:17,133 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_1 is : 1.0
2015-10-17 16:52:17,180 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0018_m_000003_1
2015-10-17 16:52:17,195 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000003_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:52:17,195 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000017 taskAttempt attempt_1445062781478_0018_m_000003_1
2015-10-17 16:52:17,195 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000003_1
2015-10-17 16:52:17,195 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:52:17,289 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:17,336 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.22797552
2015-10-17 16:52:17,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000003_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:52:17,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0018_m_000003_1
2015-10-17 16:52:17,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0018_m_000003_0
2015-10-17 16:52:17,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:52:17,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 16:52:17,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000003_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 16:52:17,476 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000005 taskAttempt attempt_1445062781478_0018_m_000003_0
2015-10-17 16:52:17,476 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000003_0
2015-10-17 16:52:17,476 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 16:52:17,555 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.62986976
2015-10-17 16:52:17,851 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000003_0 is : 0.36323506
2015-10-17 16:52:17,898 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.39863366
2015-10-17 16:52:18,008 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.42794546
2015-10-17 16:52:18,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:52:18,320 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.44968578
2015-10-17 16:52:18,320 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 16:52:18,508 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.976827
2015-10-17 16:52:18,914 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000003_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 16:52:18,930 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 16:52:19,023 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.53521925
2015-10-17 16:52:19,102 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.90713453
2015-10-17 16:52:19,102 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.13333334
2015-10-17 16:52:19,242 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.3712022
2015-10-17 16:52:19,336 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445062781478_0018_m_000003_0
2015-10-17 16:52:19,336 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000003_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 16:52:19,398 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000017
2015-10-17 16:52:19,398 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:52:19,398 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000003_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:52:19,430 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 16:52:19,617 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.5342037
2015-10-17 16:52:19,945 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.43022648
2015-10-17 16:52:20,102 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.36390656
2015-10-17 16:52:20,352 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.23513995
2015-10-17 16:52:20,602 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.6383387
2015-10-17 16:52:20,758 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 16:52:20,914 INFO [Socket Reader #1 for port 53419] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 53419: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 16:52:21,352 INFO [ContainerLauncher #0] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:64642. Already tried 0 time(s); maxRetries=45
2015-10-17 16:52:21,711 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.4403228
2015-10-17 16:52:21,805 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 16:52:21,961 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.44968578
2015-10-17 16:52:22,195 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.5923424
2015-10-17 16:52:22,195 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.9386738
2015-10-17 16:52:22,195 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000006_0 is : 0.41264012
2015-10-17 16:52:22,211 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 0.99685377
2015-10-17 16:52:22,211 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.13333334
2015-10-17 16:52:22,320 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000006_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 16:52:22,320 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 16:52:22,414 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445062781478_0018_m_000006_0
2015-10-17 16:52:22,414 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000006_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 16:52:22,836 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 16:52:22,899 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.40818897
2015-10-17 16:52:22,977 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.6046307
2015-10-17 16:52:23,164 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.41947487
2015-10-17 16:52:23,383 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.24263132
2015-10-17 16:52:23,649 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.6458304
2015-10-17 16:52:23,852 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 16:52:23,899 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000005
2015-10-17 16:52:23,899 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:52:23,899 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:52:24,086 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.44292763
2015-10-17 16:52:24,086 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000009_0 is : 1.0
2015-10-17 16:52:24,149 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0018_m_000009_0
2015-10-17 16:52:24,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:52:24,149 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000011 taskAttempt attempt_1445062781478_0018_m_000009_0
2015-10-17 16:52:24,149 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000009_0
2015-10-17 16:52:24,149 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 16:52:24,930 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 16:52:24,961 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000008
2015-10-17 16:52:24,961 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:52:24,961 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:52:25,117 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:52:25,117 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0018_m_000009_0
2015-10-17 16:52:25,117 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:52:25,117 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 16:52:25,274 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.6207798
2015-10-17 16:52:25,274 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 0.97692937
2015-10-17 16:52:25,305 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.13333334
2015-10-17 16:52:25,961 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:52:25,961 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 16:52:25,992 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.44950172
2015-10-17 16:52:26,008 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.44968578
2015-10-17 16:52:26,039 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.6196791
2015-10-17 16:52:26,242 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.44950968
2015-10-17 16:52:26,399 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.24425903
2015-10-17 16:52:26,555 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.4445072
2015-10-17 16:52:26,680 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.6539725
2015-10-17 16:52:27,008 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 16:52:27,383 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_1 is : 1.0
2015-10-17 16:52:27,430 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0018_m_000004_1
2015-10-17 16:52:27,430 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000004_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:52:27,430 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000016 taskAttempt attempt_1445062781478_0018_m_000004_1
2015-10-17 16:52:27,430 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000004_1
2015-10-17 16:52:27,430 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:52:27,633 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000004_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:52:27,633 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0018_m_000004_1
2015-10-17 16:52:27,633 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0018_m_000004_0
2015-10-17 16:52:27,633 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:52:27,633 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 16:52:27,633 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000004_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 16:52:27,633 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000006 taskAttempt attempt_1445062781478_0018_m_000004_0
2015-10-17 16:52:27,633 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000004_0
2015-10-17 16:52:27,633 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 16:52:27,930 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.44859612
2015-10-17 16:52:28,039 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:52:28,039 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 16:52:28,086 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000011
2015-10-17 16:52:28,086 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:52:28,086 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:52:28,352 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.6207798
2015-10-17 16:52:28,399 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.13333334
2015-10-17 16:52:29,430 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.24979614
2015-10-17 16:52:29,696 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.66308975
2015-10-17 16:52:29,743 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.44950172
2015-10-17 16:52:29,977 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.6196791
2015-10-17 16:52:29,977 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:29,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000016
2015-10-17 16:52:29,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:52:29,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000004_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:52:30,039 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000004_0 is : 0.44968578
2015-10-17 16:52:30,180 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.44950968
2015-10-17 16:52:30,305 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000004_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 16:52:30,305 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 16:52:30,321 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445062781478_0018_m_000004_0
2015-10-17 16:52:30,321 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000004_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 16:52:30,430 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.44964966
2015-10-17 16:52:30,914 INFO [Socket Reader #1 for port 53419] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 53419: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 16:52:31,024 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:31,399 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.64686763
2015-10-17 16:52:31,446 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.16666667
2015-10-17 16:52:31,868 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.64686763
2015-10-17 16:52:32,008 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.44859612
2015-10-17 16:52:32,039 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000006
2015-10-17 16:52:32,039 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:32,039 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:52:32,039 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:52:32,493 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.26119432
2015-10-17 16:52:32,743 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.6663469
2015-10-17 16:52:33,086 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.66474164
2015-10-17 16:52:33,118 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:33,118 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.66474164
2015-10-17 16:52:33,227 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.6663469
2015-10-17 16:52:33,258 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.46173838
2015-10-17 16:52:33,336 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.44950172
2015-10-17 16:52:34,196 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:34,196 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.44964966
2015-10-17 16:52:34,790 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.667
2015-10-17 16:52:34,790 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.16666667
2015-10-17 16:52:35,243 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:35,696 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.26998863
2015-10-17 16:52:35,915 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.44859612
2015-10-17 16:52:36,149 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.667
2015-10-17 16:52:36,274 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:36,305 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.5352021
2015-10-17 16:52:36,336 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.667
2015-10-17 16:52:37,071 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.44950172
2015-10-17 16:52:37,352 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:37,743 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.44964966
2015-10-17 16:52:37,868 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.667
2015-10-17 16:52:37,868 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.20000002
2015-10-17 16:52:38,430 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:38,883 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.27825075
2015-10-17 16:52:39,243 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.667
2015-10-17 16:52:39,399 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.5352021
2015-10-17 16:52:39,477 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:39,587 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.667
2015-10-17 16:52:40,743 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.44950172
2015-10-17 16:52:40,774 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:40,884 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.71761817
2015-10-17 16:52:40,915 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.20000002
2015-10-17 16:52:41,040 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.44859612
2015-10-17 16:52:41,759 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.44964966
2015-10-17 16:52:41,821 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:42,118 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.27825075
2015-10-17 16:52:42,727 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.5352021
2015-10-17 16:52:42,821 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:42,946 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.667
2015-10-17 16:52:43,227 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.6770475
2015-10-17 16:52:43,837 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:43,899 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.7754287
2015-10-17 16:52:43,946 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.20000002
2015-10-17 16:52:44,477 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.44950172
2015-10-17 16:52:44,868 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.44859612
2015-10-17 16:52:44,884 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:45,134 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.44964966
2015-10-17 16:52:45,399 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.27825075
2015-10-17 16:52:45,759 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.602622
2015-10-17 16:52:45,899 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:46,181 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.667
2015-10-17 16:52:46,728 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.73331636
2015-10-17 16:52:46,915 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:46,931 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.83319664
2015-10-17 16:52:46,978 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.20000002
2015-10-17 16:52:47,962 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:48,618 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.27825075
2015-10-17 16:52:48,634 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.44964966
2015-10-17 16:52:48,853 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.6209487
2015-10-17 16:52:48,962 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.44859612
2015-10-17 16:52:48,993 INFO [IPC Server handler 8 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:49,103 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.44950172
2015-10-17 16:52:49,353 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.667
2015-10-17 16:52:49,790 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.78234905
2015-10-17 16:52:49,993 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.891122
2015-10-17 16:52:50,024 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:50,024 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.20000002
2015-10-17 16:52:51,087 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:51,853 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.27825075
2015-10-17 16:52:51,946 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.6209487
2015-10-17 16:52:52,103 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.44964966
2015-10-17 16:52:52,134 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:52,587 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.667
2015-10-17 16:52:52,837 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.8050264
2015-10-17 16:52:52,993 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.9495151
2015-10-17 16:52:53,071 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.20000002
2015-10-17 16:52:53,134 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:53,181 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.44950172
2015-10-17 16:52:53,400 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.44859612
2015-10-17 16:52:54,197 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:55,009 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.63968754
2015-10-17 16:52:55,025 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.27825075
2015-10-17 16:52:55,259 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:55,462 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.44964966
2015-10-17 16:52:55,837 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.63968754
2015-10-17 16:52:55,931 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.84356654
2015-10-17 16:52:55,978 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.67559457
2015-10-17 16:52:56,056 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 0.99975395
2015-10-17 16:52:56,165 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.20000002
2015-10-17 16:52:56,181 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_1 is : 1.0
2015-10-17 16:52:56,228 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0018_m_000008_1
2015-10-17 16:52:56,228 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000008_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:52:56,228 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000020 taskAttempt attempt_1445062781478_0018_m_000008_1
2015-10-17 16:52:56,228 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000008_1
2015-10-17 16:52:56,228 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 16:52:56,306 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:56,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000008_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:52:56,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0018_m_000008_1
2015-10-17 16:52:56,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0018_m_000008_0
2015-10-17 16:52:56,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:52:56,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 16:52:56,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000008_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 16:52:56,493 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000010 taskAttempt attempt_1445062781478_0018_m_000008_0
2015-10-17 16:52:56,493 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000008_0
2015-10-17 16:52:56,493 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 16:52:56,915 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0018_m_000008
2015-10-17 16:52:56,915 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 16:52:57,009 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000008_0 is : 0.44950172
2015-10-17 16:52:57,181 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.44859612
2015-10-17 16:52:57,212 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:52:57,353 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 16:52:57,415 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000008_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 16:52:57,494 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 16:52:57,697 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445062781478_0018_m_000008_0
2015-10-17 16:52:57,697 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000008_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 16:52:58,181 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.667
2015-10-17 16:52:58,322 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000020
2015-10-17 16:52:58,322 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:52:58,322 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000008_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:52:58,337 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.31232682
2015-10-17 16:52:58,400 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:52:58,650 INFO [Socket Reader #1 for port 53419] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 53419: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 16:52:58,759 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.44964966
2015-10-17 16:52:58,994 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.86686695
2015-10-17 16:52:59,290 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.23333333
2015-10-17 16:52:59,322 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.6895566
2015-10-17 16:52:59,478 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:00,509 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000010
2015-10-17 16:53:00,509 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:53:00,509 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:53:00,525 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.46019047
2015-10-17 16:53:00,541 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:01,291 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.667
2015-10-17 16:53:01,572 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.33805588
2015-10-17 16:53:01,650 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:02,103 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.8885045
2015-10-17 16:53:02,384 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.23333333
2015-10-17 16:53:02,447 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.4827444
2015-10-17 16:53:02,634 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.7043976
2015-10-17 16:53:02,759 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:03,822 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:03,884 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.48005536
2015-10-17 16:53:04,369 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.67869204
2015-10-17 16:53:04,916 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:05,025 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.36202124
2015-10-17 16:53:05,213 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.9126297
2015-10-17 16:53:05,463 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.23333333
2015-10-17 16:53:05,931 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.71725684
2015-10-17 16:53:05,994 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:06,244 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.51101625
2015-10-17 16:53:07,072 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:07,119 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.4904782
2015-10-17 16:53:07,447 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.7240508
2015-10-17 16:53:08,150 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:08,291 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.9369513
2015-10-17 16:53:08,478 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.3638923
2015-10-17 16:53:08,510 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.23333333
2015-10-17 16:53:09,181 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:09,213 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.7310714
2015-10-17 16:53:10,244 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:10,385 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.5080648
2015-10-17 16:53:10,478 INFO [IPC Server handler 1 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.5348248
2015-10-17 16:53:10,541 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.75401604
2015-10-17 16:53:11,275 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:11,369 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.9600181
2015-10-17 16:53:11,869 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.23333333
2015-10-17 16:53:11,900 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.3638923
2015-10-17 16:53:12,291 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:12,603 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.7456514
2015-10-17 16:53:13,322 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:13,588 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.7735641
2015-10-17 16:53:13,744 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.52239484
2015-10-17 16:53:14,244 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.5352825
2015-10-17 16:53:14,338 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:14,416 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 0.9877192
2015-10-17 16:53:14,947 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.23333333
2015-10-17 16:53:15,166 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.3638923
2015-10-17 16:53:15,369 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:15,447 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_1 is : 1.0
2015-10-17 16:53:15,494 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0018_m_000002_1
2015-10-17 16:53:15,494 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000002_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:53:15,494 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000019 taskAttempt attempt_1445062781478_0018_m_000002_1
2015-10-17 16:53:15,494 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000002_1
2015-10-17 16:53:15,494 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:53:15,900 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.75734407
2015-10-17 16:53:16,025 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000002_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:53:16,025 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0018_m_000002_1
2015-10-17 16:53:16,025 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0018_m_000002_0
2015-10-17 16:53:16,025 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:53:16,025 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 16:53:16,025 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000002_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 16:53:16,025 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445062781478_0018_m_000002
2015-10-17 16:53:16,025 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000004 taskAttempt attempt_1445062781478_0018_m_000002_0
2015-10-17 16:53:16,025 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 16:53:16,025 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000002_0
2015-10-17 16:53:16,025 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:64642
2015-10-17 16:53:16,385 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 16:53:16,541 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:53:16,650 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.79168105
2015-10-17 16:53:17,010 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000002_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 16:53:17,010 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 16:53:17,150 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445062781478_0018_m_000002_0
2015-10-17 16:53:17,150 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000002_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 16:53:17,400 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:17,416 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000002_0 is : 0.5342037
2015-10-17 16:53:17,588 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000019
2015-10-17 16:53:17,588 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:53:17,588 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000002_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:53:17,776 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.5352825
2015-10-17 16:53:18,026 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.26666668
2015-10-17 16:53:18,494 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:18,619 INFO [Socket Reader #1 for port 53419] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 53419: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 16:53:18,666 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.3638923
2015-10-17 16:53:19,307 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.7708605
2015-10-17 16:53:19,526 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:19,713 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.81915814
2015-10-17 16:53:19,729 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000004
2015-10-17 16:53:19,729 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:53:19,729 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:53:20,572 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:21,916 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.3638923
2015-10-17 16:53:21,963 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:21,963 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.26666668
2015-10-17 16:53:22,448 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.5352825
2015-10-17 16:53:22,635 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.7855482
2015-10-17 16:53:22,760 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.8470492
2015-10-17 16:53:22,979 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:23,994 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:25,010 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.26666668
2015-10-17 16:53:25,010 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:25,276 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.3638923
2015-10-17 16:53:25,979 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.79889643
2015-10-17 16:53:26,010 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:26,120 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.87112546
2015-10-17 16:53:26,870 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.5352825
2015-10-17 16:53:27,041 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:28,651 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.3638923
2015-10-17 16:53:28,823 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.26666668
2015-10-17 16:53:28,823 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:29,198 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.8952649
2015-10-17 16:53:29,292 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.8123451
2015-10-17 16:53:29,854 INFO [IPC Server handler 7 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:30,854 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.5352825
2015-10-17 16:53:30,854 INFO [IPC Server handler 22 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:31,885 INFO [IPC Server handler 19 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:31,901 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.26666668
2015-10-17 16:53:31,995 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.39279917
2015-10-17 16:53:32,260 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.914493
2015-10-17 16:53:32,573 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.82547843
2015-10-17 16:53:32,917 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:34,010 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:34,948 INFO [IPC Server handler 27 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.26666668
2015-10-17 16:53:35,057 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:35,182 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.4230593
2015-10-17 16:53:35,307 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.9374869
2015-10-17 16:53:35,557 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.5352825
2015-10-17 16:53:35,761 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.8392882
2015-10-17 16:53:36,073 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:37,104 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:38,011 INFO [IPC Server handler 12 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.26666668
2015-10-17 16:53:38,151 INFO [IPC Server handler 17 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:38,401 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.96123177
2015-10-17 16:53:38,479 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.43120205
2015-10-17 16:53:39,058 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.85490906
2015-10-17 16:53:39,167 INFO [IPC Server handler 18 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:39,417 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.5352825
2015-10-17 16:53:40,214 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:41,104 INFO [IPC Server handler 10 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.26666668
2015-10-17 16:53:41,261 INFO [IPC Server handler 26 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:41,495 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 0.98343486
2015-10-17 16:53:41,823 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.44964966
2015-10-17 16:53:42,292 INFO [IPC Server handler 25 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_0 is : 0.8674502
2015-10-17 16:53:42,308 INFO [IPC Server handler 2 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:43,151 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.5352825
2015-10-17 16:53:43,339 INFO [IPC Server handler 5 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:43,495 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000005_1 is : 1.0
2015-10-17 16:53:43,526 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445062781478_0018_m_000005_1
2015-10-17 16:53:43,526 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000005_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 16:53:43,526 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000021 taskAttempt attempt_1445062781478_0018_m_000005_1
2015-10-17 16:53:43,526 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000005_1
2015-10-17 16:53:43,526 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:42313
2015-10-17 16:53:43,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000005_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 16:53:43,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445062781478_0018_m_000005_1
2015-10-17 16:53:43,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445062781478_0018_m_000005_0
2015-10-17 16:53:43,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445062781478_0018_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 16:53:43,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 16:53:43,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000005_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 16:53:43,698 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445062781478_0018_01_000007 taskAttempt attempt_1445062781478_0018_m_000005_0
2015-10-17 16:53:43,698 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445062781478_0018_m_000005_0
2015-10-17 16:53:43,698 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:51951
2015-10-17 16:53:43,730 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000005_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 16:53:43,730 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 16:53:43,776 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445062781478_0018_m_000005_0
2015-10-17 16:53:43,776 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445062781478_0018_m_000005_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 16:53:44,151 INFO [Socket Reader #1 for port 53419] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 53419: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 16:53:44,151 INFO [IPC Server handler 15 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.26666668
2015-10-17 16:53:44,370 INFO [IPC Server handler 9 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 16:53:44,558 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:53:44,980 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.44964966
2015-10-17 16:53:46,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000007
2015-10-17 16:53:46,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445062781478_0018_01_000021
2015-10-17 16:53:46,042 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:53:46,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:20 ContRel:2 HostLocal:8 RackLocal:9
2015-10-17 16:53:46,042 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445062781478_0018_m_000005_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 16:53:46,323 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 16:53:46,823 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.5352825
2015-10-17 16:53:48,152 INFO [IPC Server handler 4 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.44964966
2015-10-17 16:53:50,433 INFO [IPC Server handler 6 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.5352825
2015-10-17 16:53:51,339 INFO [IPC Server handler 23 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.44964966
2015-10-17 16:53:53,542 INFO [IPC Server handler 0 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.3
2015-10-17 16:53:53,542 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 16:53:54,355 INFO [IPC Server handler 16 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.5352825
2015-10-17 16:53:54,527 INFO [IPC Server handler 29 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.44964966
2015-10-17 16:53:54,574 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 16:53:55,605 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 16:53:56,636 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.3
2015-10-17 16:53:56,636 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 16:53:57,683 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.44964966
2015-10-17 16:53:58,871 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 16:53:58,871 INFO [IPC Server handler 3 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.56348425
2015-10-17 16:53:59,683 INFO [IPC Server handler 14 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.3
2015-10-17 16:53:59,871 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 16:54:00,699 INFO [IPC Server handler 24 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.44964966
2015-10-17 16:54:00,886 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 16:54:01,886 INFO [IPC Server handler 21 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 16:54:02,668 INFO [IPC Server handler 28 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_0 is : 0.6001215
2015-10-17 16:54:02,730 INFO [IPC Server handler 20 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_r_000000_0 is : 0.3
2015-10-17 16:54:02,918 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 16:54:03,746 INFO [IPC Server handler 13 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445062781478_0018_m_000007_1 is : 0.4881991
2015-10-17 16:54:03,933 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 16:54:04,933 INFO [IPC Server handler 11 on 53419] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445062781478_0018_r_000000_0. startIndex 9 maxEvents 10000
