2015-10-17 22:26:56,534 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 22:26:56,925 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 22:26:56,925 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 22:26:57,112 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 22:26:57,112 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445087491445_0010, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@75a61582)
2015-10-17 22:26:57,643 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 22:26:58,378 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445087491445_0010
2015-10-17 22:27:00,893 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 22:27:02,831 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 22:27:03,050 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@333861df
2015-10-17 22:27:05,050 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:939524096+134217728
2015-10-17 22:27:05,206 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 22:27:05,206 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 22:27:05,206 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 22:27:05,206 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 22:27:05,206 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 22:27:05,222 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 22:27:12,831 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:27:12,831 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34176960; bufvoid = 104857600
2015-10-17 22:27:12,831 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13787120(55148480); length = 12427277/6553600
2015-10-17 22:27:12,831 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44662712 kvi 11165672(44662688)
2015-10-17 22:27:43,614 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 22:27:43,785 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44662712 kv 11165672(44662688) kvi 8544244(34176976)
2015-10-17 22:27:48,379 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:27:48,379 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44662712; bufend = 78839169; bufvoid = 104857600
2015-10-17 22:27:48,379 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11165672(44662688); kvend = 24952676(99810704); length = 12427397/6553600
2015-10-17 22:27:48,379 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89324928 kvi 22331228(89324912)
2015-10-17 22:28:18,146 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 22:28:18,193 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 89324928 kv 22331228(89324912) kvi 19709800(78839200)
2015-10-17 22:28:22,271 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:28:22,271 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 89324928; bufend = 18642249; bufvoid = 104857597
2015-10-17 22:28:22,271 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 22331228(89324912); kvend = 9903444(39613776); length = 12427785/6553600
2015-10-17 22:28:22,271 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29128004 kvi 7281996(29127984)
2015-10-17 22:28:52,319 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 22:28:52,428 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29128004 kv 7281996(29127984) kvi 4660568(18642272)
2015-10-17 22:28:56,288 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:28:56,288 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29128004; bufend = 63301749; bufvoid = 104857600
2015-10-17 22:28:56,288 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7281996(29127984); kvend = 21068320(84273280); length = 12428077/6553600
2015-10-17 22:28:56,288 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 73787506 kvi 18446872(73787488)
2015-10-17 22:29:25,632 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 22:29:25,679 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 73787506 kv 18446872(73787488) kvi 15825444(63301776)
2015-10-17 22:29:29,648 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:29:29,648 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 73787506; bufend = 3104955; bufvoid = 104857599
2015-10-17 22:29:29,648 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 18446872(73787488); kvend = 6019116(24076464); length = 12427757/6553600
2015-10-17 22:29:29,648 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 13590701 kvi 3397668(13590672)
2015-10-17 22:29:58,821 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 22:29:58,852 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 13590701 kv 3397668(13590672) kvi 776244(3104976)
2015-10-17 22:30:02,930 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:30:02,930 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 13590701; bufend = 47760726; bufvoid = 104857600
2015-10-17 22:30:02,930 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 3397668(13590672); kvend = 17183064(68732256); length = 12429005/6553600
2015-10-17 22:30:02,930 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 58246483 kvi 14561616(58246464)
2015-10-17 22:30:04,758 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-17 22:30:30,759 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-17 22:30:30,790 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 58246483 kv 14561616(58246464) kvi 12512256(50049024)
2015-10-17 22:30:30,790 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:30:30,790 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 58246483; bufend = 63880244; bufvoid = 104857600
2015-10-17 22:30:30,790 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14561616(58246464); kvend = 12512260(50049040); length = 2049357/6553600
2015-10-17 22:30:35,181 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-17 22:30:35,212 INFO [main] org.apache.hadoop.mapred.Merger: Merging 7 sorted segments
2015-10-17 22:30:35,978 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 7 segments left of total size: 228392207 bytes
2015-10-17 22:32:13,481 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445087491445_0010_m_000007_0 is done. And is in the process of committing
2015-10-17 22:32:14,075 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445087491445_0010_m_000007_0' done.
2015-10-17 22:32:14,184 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Stopping MapTask metrics system...
2015-10-17 22:32:14,184 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system stopped.
2015-10-17 22:32:14,184 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system shutdown complete.
