2015-10-17 22:45:07,696 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 22:45:07,755 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 22:45:07,755 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 22:45:07,771 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 22:45:07,772 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445087491445_0009, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@666adef3)
2015-10-17 22:45:07,873 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 22:45:08,114 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445087491445_0009
2015-10-17 22:45:08,672 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 22:45:09,109 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 22:45:09,136 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@1a8f672c
2015-10-17 22:45:09,333 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:1207959552+105902080
2015-10-17 22:45:09,387 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 22:45:09,387 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 22:45:09,387 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 22:45:09,388 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 22:45:09,388 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 22:45:09,394 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 22:45:11,579 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:45:11,579 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34174575; bufvoid = 104857600
2015-10-17 22:45:11,579 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13786524(55146096); length = 12427873/6553600
2015-10-17 22:45:11,580 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44660327 kvi 11165076(44660304)
2015-10-17 22:45:22,094 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 22:45:22,096 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44660327 kv 11165076(44660304) kvi 8543648(34174592)
2015-10-17 22:45:23,027 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:45:23,027 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44660327; bufend = 78832499; bufvoid = 104857600
2015-10-17 22:45:23,027 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11165076(44660304); kvend = 24951004(99804016); length = 12428473/6553600
2015-10-17 22:45:23,027 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89318249 kvi 22329556(89318224)
2015-10-17 22:45:32,442 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 22:45:32,447 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 89318249 kv 22329556(89318224) kvi 19708132(78832528)
2015-10-17 22:45:33,321 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:45:33,322 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 89318249; bufend = 18637204; bufvoid = 104857600
2015-10-17 22:45:33,322 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 22329556(89318224); kvend = 9902184(39608736); length = 12427373/6553600
2015-10-17 22:45:33,322 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29122962 kvi 7280736(29122944)
2015-10-17 22:45:42,808 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 22:45:42,812 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29122962 kv 7280736(29122944) kvi 4659308(18637232)
2015-10-17 22:45:43,671 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:45:43,671 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29122962; bufend = 63291202; bufvoid = 104857600
2015-10-17 22:45:43,671 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7280736(29122944); kvend = 21065680(84262720); length = 12429457/6553600
2015-10-17 22:45:43,671 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 73776953 kvi 18444232(73776928)
2015-10-17 22:45:54,267 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 22:45:54,270 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 73776953 kv 18444232(73776928) kvi 15822808(63291232)
2015-10-17 22:45:55,367 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-17 22:45:55,368 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:45:55,368 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 73776953; bufend = 103326708; bufvoid = 104857600
2015-10-17 22:45:55,368 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 18444232(73776928); kvend = 7696416(30785664); length = 10747817/6553600
2015-10-17 22:46:03,845 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 22:46:03,861 INFO [main] org.apache.hadoop.mapred.Merger: Merging 5 sorted segments
2015-10-17 22:46:03,870 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 5 segments left of total size: 180080717 bytes
