"""
Core semantic search engine using SBERT and cosine similarity
"""

import json
import numpy as np
import pickle
import os
from typing import List, Dict, Tuple, Optional
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import logging
from tqdm import tqdm

try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    logging.warning("FAISS not available. Using sklearn cosine similarity instead.")

from config import *

# Set up logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL),
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SemanticSearchEngine:
    """
    Offline semantic search engine using SBERT embeddings and cosine similarity
    """

    def __init__(self, model_name: str = SBERT_MODEL_NAME):
        """
        Initialize the semantic search engine

        Args:
            model_name: Name of the SBERT model to use
        """
        self.model_name = model_name
        self.model = None
        self.articles = []
        self.embeddings = None
        self.faiss_index = None

        logger.info(f"Initializing SemanticSearchEngine with model: {model_name}")

    def load_model(self):
        """Load the SBERT model"""
        try:
            logger.info("Loading SBERT model...")
            self.model = SentenceTransformer(self.model_name)
            logger.info("Model loaded successfully")
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise

    def load_articles(self, file_path: str = SAMPLE_DATASET_FILE) -> List[Dict]:
        """
        Load articles from JSON file

        Args:
            file_path: Path to the articles JSON file

        Returns:
            List of article dictionaries
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.articles = json.load(f)
            logger.info(f"Loaded {len(self.articles)} articles")
            return self.articles
        except Exception as e:
            logger.error(f"Error loading articles: {e}")
            raise

    def preprocess_text(self, text: str) -> str:
        """
        Basic text preprocessing

        Args:
            text: Input text

        Returns:
            Preprocessed text
        """
        # Basic preprocessing - can be extended
        text = text.strip()
        text = ' '.join(text.split())  # Remove extra whitespace
        return text

    def generate_embeddings(self, force_regenerate: bool = False) -> np.ndarray:
        """
        Generate embeddings for all articles

        Args:
            force_regenerate: Whether to regenerate embeddings even if they exist

        Returns:
            Array of embeddings
        """
        if not force_regenerate and os.path.exists(EMBEDDINGS_FILE):
            logger.info("Loading existing embeddings...")
            self.embeddings = np.load(EMBEDDINGS_FILE)
            logger.info(f"Loaded embeddings shape: {self.embeddings.shape}")
            return self.embeddings

        if not self.model:
            self.load_model()

        if not self.articles:
            self.load_articles()

        logger.info("Generating embeddings...")

        # Combine title and content for better semantic representation
        texts = []
        for article in self.articles:
            combined_text = f"{article['title']} {article['content']}"
            processed_text = self.preprocess_text(combined_text)
            texts.append(processed_text)

        # Generate embeddings with progress bar
        self.embeddings = self.model.encode(texts,
                                          show_progress_bar=True,
                                          convert_to_numpy=True)

        # Save embeddings
        np.save(EMBEDDINGS_FILE, self.embeddings)
        logger.info(f"Generated and saved embeddings shape: {self.embeddings.shape}")

        return self.embeddings

    def build_faiss_index(self, force_rebuild: bool = False):
        """
        Build FAISS index for faster similarity search

        Args:
            force_rebuild: Whether to rebuild index even if it exists
        """
        if not FAISS_AVAILABLE:
            logger.warning("FAISS not available, skipping index building")
            return

        if not force_rebuild and os.path.exists(FAISS_INDEX_FILE):
            logger.info("Loading existing FAISS index...")
            self.faiss_index = faiss.read_index(FAISS_INDEX_FILE)
            logger.info("FAISS index loaded successfully")
            return

        if self.embeddings is None:
            self.generate_embeddings()

        logger.info("Building FAISS index...")

        # Normalize embeddings for cosine similarity
        embeddings_normalized = self.embeddings / np.linalg.norm(self.embeddings, axis=1, keepdims=True)

        # Create FAISS index
        dimension = embeddings_normalized.shape[1]
        self.faiss_index = faiss.IndexFlatIP(dimension)  # Inner Product for cosine similarity
        self.faiss_index.add(embeddings_normalized.astype('float32'))

        # Save index
        faiss.write_index(self.faiss_index, FAISS_INDEX_FILE)
        logger.info(f"FAISS index built and saved with {self.faiss_index.ntotal} vectors")

    def search(self, query: str, top_k: int = DEFAULT_TOP_K, use_faiss: bool = True) -> List[Dict]:
        """
        Perform semantic search

        Args:
            query: Search query
            top_k: Number of top results to return
            use_faiss: Whether to use FAISS for search (faster)

        Returns:
            List of search results with similarity scores
        """
        if not self.model:
            self.load_model()

        if self.embeddings is None:
            self.generate_embeddings()

        if not self.articles:
            self.load_articles()

        # Preprocess and encode query
        processed_query = self.preprocess_text(query)
        query_embedding = self.model.encode([processed_query])

        if use_faiss and FAISS_AVAILABLE:
            return self._search_with_faiss(query_embedding, top_k)
        else:
            return self._search_with_sklearn(query_embedding, top_k)

    def _search_with_faiss(self, query_embedding: np.ndarray, top_k: int) -> List[Dict]:
        """Search using FAISS index"""
        if self.faiss_index is None:
            self.build_faiss_index()

        # Normalize query embedding
        query_normalized = query_embedding / np.linalg.norm(query_embedding, axis=1, keepdims=True)

        # Search
        similarities, indices = self.faiss_index.search(query_normalized.astype('float32'), top_k)

        results = []
        for i, (similarity, idx) in enumerate(zip(similarities[0], indices[0])):
            if similarity >= SIMILARITY_THRESHOLD:
                result = self.articles[idx].copy()
                result['similarity_score'] = float(similarity)
                result['rank'] = i + 1
                results.append(result)

        return results

    def _search_with_sklearn(self, query_embedding: np.ndarray, top_k: int) -> List[Dict]:
        """Search using sklearn cosine similarity"""
        # Calculate cosine similarities
        similarities = cosine_similarity(query_embedding, self.embeddings)[0]

        # Get top-k results
        top_indices = np.argsort(similarities)[::-1][:top_k]

        results = []
        for idx in top_indices:
            if similarities[idx] >= SIMILARITY_THRESHOLD:
                result = self.articles[idx].copy()
                result['similarity_score'] = float(similarities[idx])
                result['rank'] = len(results) + 1
                results.append(result)

        return results

    def get_article_by_id(self, article_id: int) -> Optional[Dict]:
        """
        Get article by ID

        Args:
            article_id: Article ID

        Returns:
            Article dictionary or None if not found
        """
        for article in self.articles:
            if article['id'] == article_id:
                return article
        return None

    def get_statistics(self) -> Dict:
        """
        Get search engine statistics

        Returns:
            Dictionary with statistics
        """
        stats = {
            'total_articles': len(self.articles),
            'embedding_dimensions': self.embeddings.shape[1] if self.embeddings is not None else 0,
            'model_name': self.model_name,
            'categories': list(set(article['category'] for article in self.articles)),
            'total_categories': len(set(article['category'] for article in self.articles))
        }
        return stats
