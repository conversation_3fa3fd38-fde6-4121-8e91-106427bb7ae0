{"cells": [{"cell_type": "code", "execution_count": 1, "id": "35910bab", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.cluster import KMeans"]}, {"cell_type": "code", "execution_count": 2, "id": "335a7a15", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RHOB</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>DTC</th>\n", "    </tr>\n", "    <tr>\n", "      <th>DEPTH_MD</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>494.528</th>\n", "      <td>1.884186</td>\n", "      <td>80.200851</td>\n", "      <td>NaN</td>\n", "      <td>20.915468</td>\n", "      <td>161.131180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>494.680</th>\n", "      <td>1.889794</td>\n", "      <td>79.262886</td>\n", "      <td>NaN</td>\n", "      <td>19.383013</td>\n", "      <td>160.603470</td>\n", "    </tr>\n", "    <tr>\n", "      <th>494.832</th>\n", "      <td>1.896523</td>\n", "      <td>74.821999</td>\n", "      <td>NaN</td>\n", "      <td>22.591518</td>\n", "      <td>160.173615</td>\n", "    </tr>\n", "    <tr>\n", "      <th>494.984</th>\n", "      <td>1.891913</td>\n", "      <td>72.878922</td>\n", "      <td>NaN</td>\n", "      <td>32.191910</td>\n", "      <td>160.149429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>495.136</th>\n", "      <td>1.880034</td>\n", "      <td>71.729141</td>\n", "      <td>NaN</td>\n", "      <td>38.495632</td>\n", "      <td>160.128342</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3271.416</th>\n", "      <td>2.630211</td>\n", "      <td>19.418915</td>\n", "      <td>0.187811</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3271.568</th>\n", "      <td>2.643114</td>\n", "      <td>21.444370</td>\n", "      <td>0.185574</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3271.720</th>\n", "      <td>2.681300</td>\n", "      <td>22.646879</td>\n", "      <td>0.176074</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3271.872</th>\n", "      <td>2.738337</td>\n", "      <td>22.253584</td>\n", "      <td>0.174617</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3272.024</th>\n", "      <td>2.792922</td>\n", "      <td>19.829920</td>\n", "      <td>0.176627</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>18270 rows × 5 columns</p>\n", "</div>"], "text/plain": ["              RHOB         GR      NPHI        PEF         DTC\n", "DEPTH_MD                                                      \n", "494.528   1.884186  80.200851       NaN  20.915468  161.131180\n", "494.680   1.889794  79.262886       NaN  19.383013  160.603470\n", "494.832   1.896523  74.821999       NaN  22.591518  160.173615\n", "494.984   1.891913  72.878922       NaN  32.191910  160.149429\n", "495.136   1.880034  71.729141       NaN  38.495632  160.128342\n", "...            ...        ...       ...        ...         ...\n", "3271.416  2.630211  19.418915  0.187811        NaN         NaN\n", "3271.568  2.643114  21.444370  0.185574        NaN         NaN\n", "3271.720  2.681300  22.646879  0.176074        NaN         NaN\n", "3271.872  2.738337  22.253584  0.174617        NaN         NaN\n", "3272.024  2.792922  19.829920  0.176627        NaN         NaN\n", "\n", "[18270 rows x 5 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/CSV Data/force2020_data_unsupervised_learning.csv\", index_col = 'DEPTH_MD')\n", "df"]}, {"cell_type": "code", "execution_count": 3, "id": "cbfb381b", "metadata": {}, "outputs": [], "source": ["df.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": 4, "id": "deaab83b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RHOB</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>DTC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>12202.000000</td>\n", "      <td>12202.000000</td>\n", "      <td>12202.000000</td>\n", "      <td>12202.000000</td>\n", "      <td>12202.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2.149947</td>\n", "      <td>61.253852</td>\n", "      <td>0.414572</td>\n", "      <td>3.912313</td>\n", "      <td>121.409905</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.251592</td>\n", "      <td>29.902708</td>\n", "      <td>0.139207</td>\n", "      <td>1.816933</td>\n", "      <td>30.394369</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.493417</td>\n", "      <td>6.191506</td>\n", "      <td>0.037976</td>\n", "      <td>1.126667</td>\n", "      <td>55.726753</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.983767</td>\n", "      <td>42.792794</td>\n", "      <td>0.313797</td>\n", "      <td>2.629141</td>\n", "      <td>89.977041</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>2.059335</td>\n", "      <td>62.886322</td>\n", "      <td>0.466891</td>\n", "      <td>3.365132</td>\n", "      <td>138.477173</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2.389839</td>\n", "      <td>77.726776</td>\n", "      <td>0.513840</td>\n", "      <td>4.686422</td>\n", "      <td>146.242302</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>2.889454</td>\n", "      <td>499.022583</td>\n", "      <td>0.800262</td>\n", "      <td>17.026619</td>\n", "      <td>163.910797</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               RHOB            GR          NPHI           PEF           DTC\n", "count  12202.000000  12202.000000  12202.000000  12202.000000  12202.000000\n", "mean       2.149947     61.253852      0.414572      3.912313    121.409905\n", "std        0.251592     29.902708      0.139207      1.816933     30.394369\n", "min        1.493417      6.191506      0.037976      1.126667     55.726753\n", "25%        1.983767     42.792794      0.313797      2.629141     89.977041\n", "50%        2.059335     62.886322      0.466891      3.365132    138.477173\n", "75%        2.389839     77.726776      0.513840      4.686422    146.242302\n", "max        2.889454    499.022583      0.800262     17.026619    163.910797"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": 5, "id": "02b48ff3", "metadata": {}, "outputs": [], "source": ["scaler = StandardScaler()"]}, {"cell_type": "code", "execution_count": 6, "id": "76f863fe", "metadata": {}, "outputs": [], "source": ["df[['RHOB_T', 'GR_T', 'NPHI_T', 'PEF_T', 'DTC_T']] = scaler.fit_transform(df[['RHOB', 'GR','NPHI', 'PEF', 'DTC']])"]}, {"cell_type": "code", "execution_count": 7, "id": "c61dc3e5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RHOB</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>DTC</th>\n", "      <th>RHOB_T</th>\n", "      <th>GR_T</th>\n", "      <th>NPHI_T</th>\n", "      <th>PEF_T</th>\n", "      <th>DTC_T</th>\n", "    </tr>\n", "    <tr>\n", "      <th>DEPTH_MD</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1138.704</th>\n", "      <td>1.774626</td>\n", "      <td>55.892757</td>\n", "      <td>0.765867</td>\n", "      <td>1.631495</td>\n", "      <td>147.837677</td>\n", "      <td>-1.491843</td>\n", "      <td>-0.179292</td>\n", "      <td>2.523654</td>\n", "      <td>-1.255364</td>\n", "      <td>0.869531</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1138.856</th>\n", "      <td>1.800986</td>\n", "      <td>60.929138</td>\n", "      <td>0.800262</td>\n", "      <td>1.645080</td>\n", "      <td>142.382431</td>\n", "      <td>-1.387067</td>\n", "      <td>-0.010859</td>\n", "      <td>2.770744</td>\n", "      <td>-1.247886</td>\n", "      <td>0.690042</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1139.008</th>\n", "      <td>1.817696</td>\n", "      <td>62.117264</td>\n", "      <td>0.765957</td>\n", "      <td>1.645873</td>\n", "      <td>138.258331</td>\n", "      <td>-1.320646</td>\n", "      <td>0.028875</td>\n", "      <td>2.524300</td>\n", "      <td>-1.247450</td>\n", "      <td>0.554350</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1139.160</th>\n", "      <td>1.829333</td>\n", "      <td>61.010860</td>\n", "      <td>0.702521</td>\n", "      <td>1.620216</td>\n", "      <td>139.198914</td>\n", "      <td>-1.274390</td>\n", "      <td>-0.008126</td>\n", "      <td>2.068584</td>\n", "      <td>-1.261572</td>\n", "      <td>0.585297</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1139.312</th>\n", "      <td>1.813854</td>\n", "      <td>58.501236</td>\n", "      <td>0.639708</td>\n", "      <td>1.504854</td>\n", "      <td>144.290085</td>\n", "      <td>-1.335919</td>\n", "      <td>-0.092056</td>\n", "      <td>1.617342</td>\n", "      <td>-1.325067</td>\n", "      <td>0.752808</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.256</th>\n", "      <td>2.468236</td>\n", "      <td>90.537521</td>\n", "      <td>0.341534</td>\n", "      <td>4.699200</td>\n", "      <td>86.474564</td>\n", "      <td>1.265151</td>\n", "      <td>0.979338</td>\n", "      <td>-0.524699</td>\n", "      <td>0.433103</td>\n", "      <td>-1.149449</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.408</th>\n", "      <td>2.457519</td>\n", "      <td>88.819122</td>\n", "      <td>0.351085</td>\n", "      <td>4.699200</td>\n", "      <td>86.187599</td>\n", "      <td>1.222550</td>\n", "      <td>0.921870</td>\n", "      <td>-0.456081</td>\n", "      <td>0.433103</td>\n", "      <td>-1.158891</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.560</th>\n", "      <td>2.429228</td>\n", "      <td>92.128922</td>\n", "      <td>0.364982</td>\n", "      <td>4.699200</td>\n", "      <td>87.797836</td>\n", "      <td>1.110101</td>\n", "      <td>1.032560</td>\n", "      <td>-0.356250</td>\n", "      <td>0.433103</td>\n", "      <td>-1.105910</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.712</th>\n", "      <td>2.425479</td>\n", "      <td>95.870255</td>\n", "      <td>0.367323</td>\n", "      <td>5.224292</td>\n", "      <td>88.108452</td>\n", "      <td>1.095199</td>\n", "      <td>1.157682</td>\n", "      <td>-0.339430</td>\n", "      <td>0.722114</td>\n", "      <td>-1.095690</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.864</th>\n", "      <td>2.455546</td>\n", "      <td>96.814697</td>\n", "      <td>0.379080</td>\n", "      <td>6.049967</td>\n", "      <td>88.956207</td>\n", "      <td>1.214709</td>\n", "      <td>1.189267</td>\n", "      <td>-0.254974</td>\n", "      <td>1.176566</td>\n", "      <td>-1.067797</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>12202 rows × 10 columns</p>\n", "</div>"], "text/plain": ["              RHOB         GR      NPHI       PEF         DTC    RHOB_T  \\\n", "DEPTH_MD                                                                  \n", "1138.704  1.774626  55.892757  0.765867  1.631495  147.837677 -1.491843   \n", "1138.856  1.800986  60.929138  0.800262  1.645080  142.382431 -1.387067   \n", "1139.008  1.817696  62.117264  0.765957  1.645873  138.258331 -1.320646   \n", "1139.160  1.829333  61.010860  0.702521  1.620216  139.198914 -1.274390   \n", "1139.312  1.813854  58.501236  0.639708  1.504854  144.290085 -1.335919   \n", "...            ...        ...       ...       ...         ...       ...   \n", "2993.256  2.468236  90.537521  0.341534  4.699200   86.474564  1.265151   \n", "2993.408  2.457519  88.819122  0.351085  4.699200   86.187599  1.222550   \n", "2993.560  2.429228  92.128922  0.364982  4.699200   87.797836  1.110101   \n", "2993.712  2.425479  95.870255  0.367323  5.224292   88.108452  1.095199   \n", "2993.864  2.455546  96.814697  0.379080  6.049967   88.956207  1.214709   \n", "\n", "              GR_T    NPHI_T     PEF_T     DTC_T  \n", "DEPTH_MD                                          \n", "1138.704 -0.179292  2.523654 -1.255364  0.869531  \n", "1138.856 -0.010859  2.770744 -1.247886  0.690042  \n", "1139.008  0.028875  2.524300 -1.247450  0.554350  \n", "1139.160 -0.008126  2.068584 -1.261572  0.585297  \n", "1139.312 -0.092056  1.617342 -1.325067  0.752808  \n", "...            ...       ...       ...       ...  \n", "2993.256  0.979338 -0.524699  0.433103 -1.149449  \n", "2993.408  0.921870 -0.456081  0.433103 -1.158891  \n", "2993.560  1.032560 -0.356250  0.433103 -1.105910  \n", "2993.712  1.157682 -0.339430  0.722114 -1.095690  \n", "2993.864  1.189267 -0.254974  1.176566 -1.067797  \n", "\n", "[12202 rows x 10 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 8, "id": "11bc8354", "metadata": {}, "outputs": [], "source": ["def optimise_k_means(data, max_k):\n", "    means = []\n", "    inertias = []\n", "\n", "    for k in range(1, max_k):\n", "        kmeans = KMeans(n_clusters = k)\n", "        kmeans.fit(data)\n", "\n", "        means.append(k)\n", "        inertias.append(kmeans.inertia_)\n", "\n", "    fig = plt.subplots(figsize = (10,5))\n", "    plt.plot(means, inertias, 'o-')\n", "    plt.xlabel('Number of Clusters')\n", "    plt.ylabel('Inertia')\n", "    plt.grid(True)\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 19, "id": "bd009102", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["optimise_k_means(df[['RHOB_T', 'NPHI_T']], 10)"]}, {"cell_type": "markdown", "id": "71354ccf", "metadata": {}, "source": ["# Applying K Means Clustering"]}, {"cell_type": "code", "execution_count": 24, "id": "25913c4f", "metadata": {}, "outputs": [], "source": ["kmeans = KMeans(n_clusters=5)"]}, {"cell_type": "code", "execution_count": 25, "id": "fbee15ae", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-2 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-2 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-2 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-2 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-2 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-2 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-2 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-2 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-2 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-2 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-2 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-2 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-2 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-2 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-2 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-2 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-2 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>KMeans(n_clusters=5)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" checked><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;KMeans<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.cluster.KMeans.html\">?<span>Documentation for KMeans</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>KMeans(n_clusters=5)</pre></div> </div></div></div></div>"], "text/plain": ["KMeans(n_clusters=5)"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["kmeans.fit(df[['NPHI_T', \"RHOB_T\"]])"]}, {"cell_type": "code", "execution_count": 27, "id": "67aa1cc0", "metadata": {}, "outputs": [], "source": ["df['kmeans_5'] = kmeans.labels_"]}, {"cell_type": "code", "execution_count": 28, "id": "daa47d67", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RHOB</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>DTC</th>\n", "      <th>RHOB_T</th>\n", "      <th>GR_T</th>\n", "      <th>NPHI_T</th>\n", "      <th>PEF_T</th>\n", "      <th>DTC_T</th>\n", "      <th>KMeans_1</th>\n", "      <th>KMeans_2</th>\n", "      <th>KMeans_3</th>\n", "      <th>KMeans_4</th>\n", "      <th>KMeans_5</th>\n", "      <th>KMeans_6</th>\n", "      <th>KMeans_7</th>\n", "      <th>KMeans_8</th>\n", "      <th>kmeans_3</th>\n", "      <th>kmeans_5</th>\n", "    </tr>\n", "    <tr>\n", "      <th>DEPTH_MD</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1138.704</th>\n", "      <td>1.774626</td>\n", "      <td>55.892757</td>\n", "      <td>0.765867</td>\n", "      <td>1.631495</td>\n", "      <td>147.837677</td>\n", "      <td>-1.491843</td>\n", "      <td>-0.179292</td>\n", "      <td>2.523654</td>\n", "      <td>-1.255364</td>\n", "      <td>0.869531</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1138.856</th>\n", "      <td>1.800986</td>\n", "      <td>60.929138</td>\n", "      <td>0.800262</td>\n", "      <td>1.645080</td>\n", "      <td>142.382431</td>\n", "      <td>-1.387067</td>\n", "      <td>-0.010859</td>\n", "      <td>2.770744</td>\n", "      <td>-1.247886</td>\n", "      <td>0.690042</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1139.008</th>\n", "      <td>1.817696</td>\n", "      <td>62.117264</td>\n", "      <td>0.765957</td>\n", "      <td>1.645873</td>\n", "      <td>138.258331</td>\n", "      <td>-1.320646</td>\n", "      <td>0.028875</td>\n", "      <td>2.524300</td>\n", "      <td>-1.247450</td>\n", "      <td>0.554350</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1139.160</th>\n", "      <td>1.829333</td>\n", "      <td>61.010860</td>\n", "      <td>0.702521</td>\n", "      <td>1.620216</td>\n", "      <td>139.198914</td>\n", "      <td>-1.274390</td>\n", "      <td>-0.008126</td>\n", "      <td>2.068584</td>\n", "      <td>-1.261572</td>\n", "      <td>0.585297</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1139.312</th>\n", "      <td>1.813854</td>\n", "      <td>58.501236</td>\n", "      <td>0.639708</td>\n", "      <td>1.504854</td>\n", "      <td>144.290085</td>\n", "      <td>-1.335919</td>\n", "      <td>-0.092056</td>\n", "      <td>1.617342</td>\n", "      <td>-1.325067</td>\n", "      <td>0.752808</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.256</th>\n", "      <td>2.468236</td>\n", "      <td>90.537521</td>\n", "      <td>0.341534</td>\n", "      <td>4.699200</td>\n", "      <td>86.474564</td>\n", "      <td>1.265151</td>\n", "      <td>0.979338</td>\n", "      <td>-0.524699</td>\n", "      <td>0.433103</td>\n", "      <td>-1.149449</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.408</th>\n", "      <td>2.457519</td>\n", "      <td>88.819122</td>\n", "      <td>0.351085</td>\n", "      <td>4.699200</td>\n", "      <td>86.187599</td>\n", "      <td>1.222550</td>\n", "      <td>0.921870</td>\n", "      <td>-0.456081</td>\n", "      <td>0.433103</td>\n", "      <td>-1.158891</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.560</th>\n", "      <td>2.429228</td>\n", "      <td>92.128922</td>\n", "      <td>0.364982</td>\n", "      <td>4.699200</td>\n", "      <td>87.797836</td>\n", "      <td>1.110101</td>\n", "      <td>1.032560</td>\n", "      <td>-0.356250</td>\n", "      <td>0.433103</td>\n", "      <td>-1.105910</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.712</th>\n", "      <td>2.425479</td>\n", "      <td>95.870255</td>\n", "      <td>0.367323</td>\n", "      <td>5.224292</td>\n", "      <td>88.108452</td>\n", "      <td>1.095199</td>\n", "      <td>1.157682</td>\n", "      <td>-0.339430</td>\n", "      <td>0.722114</td>\n", "      <td>-1.095690</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.864</th>\n", "      <td>2.455546</td>\n", "      <td>96.814697</td>\n", "      <td>0.379080</td>\n", "      <td>6.049967</td>\n", "      <td>88.956207</td>\n", "      <td>1.214709</td>\n", "      <td>1.189267</td>\n", "      <td>-0.254974</td>\n", "      <td>1.176566</td>\n", "      <td>-1.067797</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>12202 rows × 20 columns</p>\n", "</div>"], "text/plain": ["              RHOB         GR      NPHI       PEF         DTC    RHOB_T  \\\n", "DEPTH_MD                                                                  \n", "1138.704  1.774626  55.892757  0.765867  1.631495  147.837677 -1.491843   \n", "1138.856  1.800986  60.929138  0.800262  1.645080  142.382431 -1.387067   \n", "1139.008  1.817696  62.117264  0.765957  1.645873  138.258331 -1.320646   \n", "1139.160  1.829333  61.010860  0.702521  1.620216  139.198914 -1.274390   \n", "1139.312  1.813854  58.501236  0.639708  1.504854  144.290085 -1.335919   \n", "...            ...        ...       ...       ...         ...       ...   \n", "2993.256  2.468236  90.537521  0.341534  4.699200   86.474564  1.265151   \n", "2993.408  2.457519  88.819122  0.351085  4.699200   86.187599  1.222550   \n", "2993.560  2.429228  92.128922  0.364982  4.699200   87.797836  1.110101   \n", "2993.712  2.425479  95.870255  0.367323  5.224292   88.108452  1.095199   \n", "2993.864  2.455546  96.814697  0.379080  6.049967   88.956207  1.214709   \n", "\n", "              GR_T    NPHI_T     PEF_T     DTC_T  KMeans_1  KMeans_2  \\\n", "DEPTH_MD                                                               \n", "1138.704 -0.179292  2.523654 -1.255364  0.869531         0         1   \n", "1138.856 -0.010859  2.770744 -1.247886  0.690042         0         1   \n", "1139.008  0.028875  2.524300 -1.247450  0.554350         0         1   \n", "1139.160 -0.008126  2.068584 -1.261572  0.585297         0         1   \n", "1139.312 -0.092056  1.617342 -1.325067  0.752808         0         1   \n", "...            ...       ...       ...       ...       ...       ...   \n", "2993.256  0.979338 -0.524699  0.433103 -1.149449         0         0   \n", "2993.408  0.921870 -0.456081  0.433103 -1.158891         0         0   \n", "2993.560  1.032560 -0.356250  0.433103 -1.105910         0         0   \n", "2993.712  1.157682 -0.339430  0.722114 -1.095690         0         0   \n", "2993.864  1.189267 -0.254974  1.176566 -1.067797         0         0   \n", "\n", "          KMeans_3  KMeans_4  KMeans_5  KMeans_6  KMeans_7  KMeans_8  \\\n", "DEPTH_MD                                                               \n", "1138.704         2         2         3         3         6         7   \n", "1138.856         2         2         3         3         6         7   \n", "1139.008         2         2         3         3         6         7   \n", "1139.160         2         2         3         3         6         7   \n", "1139.312         2         2         3         3         6         7   \n", "...            ...       ...       ...       ...       ...       ...   \n", "2993.256         1         1         1         0         3         3   \n", "2993.408         1         1         1         0         3         3   \n", "2993.560         1         1         1         0         3         3   \n", "2993.712         1         1         1         0         3         3   \n", "2993.864         1         1         1         0         3         3   \n", "\n", "          kmeans_3  kmeans_5  \n", "DEPTH_MD                      \n", "1138.704         2         2  \n", "1138.856         2         2  \n", "1139.008         2         2  \n", "1139.160         2         2  \n", "1139.312         2         2  \n", "...            ...       ...  \n", "2993.256         1         1  \n", "2993.408         1         1  \n", "2993.560         1         1  \n", "2993.712         1         1  \n", "2993.864         1         1  \n", "\n", "[12202 rows x 20 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 22, "id": "db101c6a", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(x=df['NPHI'], y=df['RHOB'], c=df['kmeans_3'])\n", "plt.xlim(-0.1, 1)\n", "plt.ylim(3, 1.5)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 15, "id": "1415ae21", "metadata": {}, "outputs": [], "source": ["for k in range(1, 9):\n", "    kmeans=KMeans(n_clusters=k)\n", "    kmeans.fit(df[['RHOB_T', 'NPHI_T']])\n", "    df[f\"KMeans_{k}\"] = kmeans.labels_"]}, {"cell_type": "code", "execution_count": 23, "id": "052d94c3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RHOB</th>\n", "      <th>GR</th>\n", "      <th>NPHI</th>\n", "      <th>PEF</th>\n", "      <th>DTC</th>\n", "      <th>RHOB_T</th>\n", "      <th>GR_T</th>\n", "      <th>NPHI_T</th>\n", "      <th>PEF_T</th>\n", "      <th>DTC_T</th>\n", "      <th>KMeans_1</th>\n", "      <th>KMeans_2</th>\n", "      <th>KMeans_3</th>\n", "      <th>KMeans_4</th>\n", "      <th>KMeans_5</th>\n", "      <th>KMeans_6</th>\n", "      <th>KMeans_7</th>\n", "      <th>KMeans_8</th>\n", "      <th>kmeans_3</th>\n", "    </tr>\n", "    <tr>\n", "      <th>DEPTH_MD</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1138.704</th>\n", "      <td>1.774626</td>\n", "      <td>55.892757</td>\n", "      <td>0.765867</td>\n", "      <td>1.631495</td>\n", "      <td>147.837677</td>\n", "      <td>-1.491843</td>\n", "      <td>-0.179292</td>\n", "      <td>2.523654</td>\n", "      <td>-1.255364</td>\n", "      <td>0.869531</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1138.856</th>\n", "      <td>1.800986</td>\n", "      <td>60.929138</td>\n", "      <td>0.800262</td>\n", "      <td>1.645080</td>\n", "      <td>142.382431</td>\n", "      <td>-1.387067</td>\n", "      <td>-0.010859</td>\n", "      <td>2.770744</td>\n", "      <td>-1.247886</td>\n", "      <td>0.690042</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1139.008</th>\n", "      <td>1.817696</td>\n", "      <td>62.117264</td>\n", "      <td>0.765957</td>\n", "      <td>1.645873</td>\n", "      <td>138.258331</td>\n", "      <td>-1.320646</td>\n", "      <td>0.028875</td>\n", "      <td>2.524300</td>\n", "      <td>-1.247450</td>\n", "      <td>0.554350</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1139.160</th>\n", "      <td>1.829333</td>\n", "      <td>61.010860</td>\n", "      <td>0.702521</td>\n", "      <td>1.620216</td>\n", "      <td>139.198914</td>\n", "      <td>-1.274390</td>\n", "      <td>-0.008126</td>\n", "      <td>2.068584</td>\n", "      <td>-1.261572</td>\n", "      <td>0.585297</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1139.312</th>\n", "      <td>1.813854</td>\n", "      <td>58.501236</td>\n", "      <td>0.639708</td>\n", "      <td>1.504854</td>\n", "      <td>144.290085</td>\n", "      <td>-1.335919</td>\n", "      <td>-0.092056</td>\n", "      <td>1.617342</td>\n", "      <td>-1.325067</td>\n", "      <td>0.752808</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.256</th>\n", "      <td>2.468236</td>\n", "      <td>90.537521</td>\n", "      <td>0.341534</td>\n", "      <td>4.699200</td>\n", "      <td>86.474564</td>\n", "      <td>1.265151</td>\n", "      <td>0.979338</td>\n", "      <td>-0.524699</td>\n", "      <td>0.433103</td>\n", "      <td>-1.149449</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.408</th>\n", "      <td>2.457519</td>\n", "      <td>88.819122</td>\n", "      <td>0.351085</td>\n", "      <td>4.699200</td>\n", "      <td>86.187599</td>\n", "      <td>1.222550</td>\n", "      <td>0.921870</td>\n", "      <td>-0.456081</td>\n", "      <td>0.433103</td>\n", "      <td>-1.158891</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.560</th>\n", "      <td>2.429228</td>\n", "      <td>92.128922</td>\n", "      <td>0.364982</td>\n", "      <td>4.699200</td>\n", "      <td>87.797836</td>\n", "      <td>1.110101</td>\n", "      <td>1.032560</td>\n", "      <td>-0.356250</td>\n", "      <td>0.433103</td>\n", "      <td>-1.105910</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.712</th>\n", "      <td>2.425479</td>\n", "      <td>95.870255</td>\n", "      <td>0.367323</td>\n", "      <td>5.224292</td>\n", "      <td>88.108452</td>\n", "      <td>1.095199</td>\n", "      <td>1.157682</td>\n", "      <td>-0.339430</td>\n", "      <td>0.722114</td>\n", "      <td>-1.095690</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2993.864</th>\n", "      <td>2.455546</td>\n", "      <td>96.814697</td>\n", "      <td>0.379080</td>\n", "      <td>6.049967</td>\n", "      <td>88.956207</td>\n", "      <td>1.214709</td>\n", "      <td>1.189267</td>\n", "      <td>-0.254974</td>\n", "      <td>1.176566</td>\n", "      <td>-1.067797</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>12202 rows × 19 columns</p>\n", "</div>"], "text/plain": ["              RHOB         GR      NPHI       PEF         DTC    RHOB_T  \\\n", "DEPTH_MD                                                                  \n", "1138.704  1.774626  55.892757  0.765867  1.631495  147.837677 -1.491843   \n", "1138.856  1.800986  60.929138  0.800262  1.645080  142.382431 -1.387067   \n", "1139.008  1.817696  62.117264  0.765957  1.645873  138.258331 -1.320646   \n", "1139.160  1.829333  61.010860  0.702521  1.620216  139.198914 -1.274390   \n", "1139.312  1.813854  58.501236  0.639708  1.504854  144.290085 -1.335919   \n", "...            ...        ...       ...       ...         ...       ...   \n", "2993.256  2.468236  90.537521  0.341534  4.699200   86.474564  1.265151   \n", "2993.408  2.457519  88.819122  0.351085  4.699200   86.187599  1.222550   \n", "2993.560  2.429228  92.128922  0.364982  4.699200   87.797836  1.110101   \n", "2993.712  2.425479  95.870255  0.367323  5.224292   88.108452  1.095199   \n", "2993.864  2.455546  96.814697  0.379080  6.049967   88.956207  1.214709   \n", "\n", "              GR_T    NPHI_T     PEF_T     DTC_T  KMeans_1  KMeans_2  \\\n", "DEPTH_MD                                                               \n", "1138.704 -0.179292  2.523654 -1.255364  0.869531         0         1   \n", "1138.856 -0.010859  2.770744 -1.247886  0.690042         0         1   \n", "1139.008  0.028875  2.524300 -1.247450  0.554350         0         1   \n", "1139.160 -0.008126  2.068584 -1.261572  0.585297         0         1   \n", "1139.312 -0.092056  1.617342 -1.325067  0.752808         0         1   \n", "...            ...       ...       ...       ...       ...       ...   \n", "2993.256  0.979338 -0.524699  0.433103 -1.149449         0         0   \n", "2993.408  0.921870 -0.456081  0.433103 -1.158891         0         0   \n", "2993.560  1.032560 -0.356250  0.433103 -1.105910         0         0   \n", "2993.712  1.157682 -0.339430  0.722114 -1.095690         0         0   \n", "2993.864  1.189267 -0.254974  1.176566 -1.067797         0         0   \n", "\n", "          KMeans_3  KMeans_4  KMeans_5  KMeans_6  KMeans_7  KMeans_8  kmeans_3  \n", "DEPTH_MD                                                                        \n", "1138.704         2         2         3         3         6         7         7  \n", "1138.856         2         2         3         3         6         7         7  \n", "1139.008         2         2         3         3         6         7         7  \n", "1139.160         2         2         3         3         6         7         7  \n", "1139.312         2         2         3         3         6         7         7  \n", "...            ...       ...       ...       ...       ...       ...       ...  \n", "2993.256         1         1         1         0         3         3         3  \n", "2993.408         1         1         1         0         3         3         3  \n", "2993.560         1         1         1         0         3         3         3  \n", "2993.712         1         1         1         0         3         3         3  \n", "2993.864         1         1         1         0         3         3         3  \n", "\n", "[12202 rows x 19 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 17, "id": "ea67242e", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 2000x500 with 8 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(nrows=1, ncols=8, figsize=(20,5))\n", "\n", "for i, ax in enumerate(fig.axes, start=1):\n", "    ax.scatter(x=df['NPHI'], y=df['RHOB'], c=df[f'KMeans_{i}'])\n", "    ax.set_xlim(0,1)\n", "    ax.set_ylim(3,1.5)\n", "    ax.set_title(f\"N Clusters: {i}\")"]}, {"cell_type": "code", "execution_count": 18, "id": "ce3531b1", "metadata": {}, "outputs": [], "source": ["# from sklearn.mixture import GaussianMixture\n", "# gmm = GaussianMixture(n_components=5)\n", "# gmm.fit(df[['RHOB_T', 'NPHI_T', \"DTC_T, 'GR_T\"]])\n", "\n", "# df['GMM_5'] = gmm.predict(df[['RHOB_T', 'NPHI_T', 'DTC_T', 'GR_T']])"]}, {"cell_type": "code", "execution_count": null, "id": "06c4366c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b118e5b7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f6e8ca63", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ccb0cc7f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a6d89a3a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7400c06b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0663d350", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5bfcc607", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f344ea01", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}