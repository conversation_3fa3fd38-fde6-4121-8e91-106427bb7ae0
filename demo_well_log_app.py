"""
Comprehensive Demo Script for Well Log Analyzer App
Shows all features and capabilities
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime

def create_demo_datasets():
    """Create multiple demo datasets showcasing different scenarios"""
    
    print("🛢️ Creating Demo Datasets for Well Log Analyzer")
    print("=" * 60)
    
    datasets = []
    
    # Dataset 1: Clean synthetic data
    print("📊 Creating Dataset 1: Clean Synthetic Data")
    depths = np.arange(2000, 2200, 0.5)
    np.random.seed(42)
    
    clean_data = {
        'DEPTH_MD': depths,
        'GR': 60 + 40 * np.sin(depths * 0.01) + np.random.normal(0, 8, len(depths)),
        'RDEP': np.exp(1.5 + 0.8 * np.sin(depths * 0.008) + np.random.normal(0, 0.4, len(depths))),
        'RHOB': 2.4 + 0.3 * np.sin(depths * 0.012) + np.random.normal(0, 0.05, len(depths)),
        'NPHI': 0.18 + 0.12 * np.sin(depths * 0.015) + np.random.normal(0, 0.02, len(depths)),
        'CALI': 8.5 + 1.5 * np.sin(depths * 0.02) + np.random.normal(0, 0.3, len(depths)),
        'DTC': 75 + 25 * np.sin(depths * 0.01) + np.random.normal(0, 5, len(depths)),
        'PEF': 2.8 + 0.6 * np.sin(depths * 0.018) + np.random.normal(0, 0.2, len(depths))
    }
    
    # Ensure realistic ranges
    clean_data['GR'] = np.clip(clean_data['GR'], 0, 200)
    clean_data['RDEP'] = np.clip(clean_data['RDEP'], 0.1, 1000)
    clean_data['RHOB'] = np.clip(clean_data['RHOB'], 1.8, 2.8)
    clean_data['NPHI'] = np.clip(clean_data['NPHI'], 0, 0.5)
    clean_data['CALI'] = np.clip(clean_data['CALI'], 6, 15)
    clean_data['DTC'] = np.clip(clean_data['DTC'], 40, 140)
    clean_data['PEF'] = np.clip(clean_data['PEF'], 1, 6)
    
    df1 = pd.DataFrame(clean_data)
    filename1 = "demo_clean_synthetic_data.csv"
    df1.to_csv(filename1, index=False)
    datasets.append((filename1, "Clean synthetic data with realistic well log curves"))
    
    # Dataset 2: Data with missing values (simulating real-world scenario)
    print("📊 Creating Dataset 2: Data with Missing Values")
    df2 = df1.copy()
    
    # Introduce missing values randomly
    np.random.seed(123)
    missing_indices = np.random.choice(len(df2), size=int(len(df2) * 0.05), replace=False)
    df2.loc[missing_indices[:20], 'RHOB'] = np.nan
    df2.loc[missing_indices[20:35], 'NPHI'] = np.nan
    df2.loc[missing_indices[35:50], 'DTC'] = np.nan
    df2.loc[missing_indices[50:], 'PEF'] = np.nan
    
    filename2 = "demo_data_with_missing_values.csv"
    df2.to_csv(filename2, index=False)
    datasets.append((filename2, "Data with missing values to test data cleaning"))
    
    # Dataset 3: Multi-zone data (different lithologies)
    print("📊 Creating Dataset 3: Multi-Zone Data")
    depths3 = np.arange(1800, 2400, 0.25)
    
    # Create different zones with distinct characteristics
    zone_data = {
        'DEPTH_MD': depths3,
        'GR': [],
        'RDEP': [],
        'RHOB': [],
        'NPHI': [],
        'CALI': [],
        'DTC': [],
        'PEF': []
    }
    
    for depth in depths3:
        if depth < 1950:  # Shale zone
            gr = 120 + np.random.normal(0, 15)
            rdep = np.exp(0.5 + np.random.normal(0, 0.3))
            rhob = 2.2 + np.random.normal(0, 0.05)
            nphi = 0.35 + np.random.normal(0, 0.03)
            pef = 3.2 + np.random.normal(0, 0.2)
        elif depth < 2100:  # Sand zone
            gr = 35 + np.random.normal(0, 10)
            rdep = np.exp(2.5 + np.random.normal(0, 0.5))
            rhob = 2.1 + np.random.normal(0, 0.04)
            nphi = 0.25 + np.random.normal(0, 0.02)
            pef = 1.8 + np.random.normal(0, 0.15)
        elif depth < 2250:  # Limestone zone
            gr = 25 + np.random.normal(0, 8)
            rdep = np.exp(3.0 + np.random.normal(0, 0.4))
            rhob = 2.6 + np.random.normal(0, 0.03)
            nphi = 0.15 + np.random.normal(0, 0.02)
            pef = 4.8 + np.random.normal(0, 0.2)
        else:  # Mixed zone
            gr = 70 + np.random.normal(0, 20)
            rdep = np.exp(1.8 + np.random.normal(0, 0.6))
            rhob = 2.4 + np.random.normal(0, 0.06)
            nphi = 0.20 + np.random.normal(0, 0.03)
            pef = 2.8 + np.random.normal(0, 0.3)
        
        zone_data['GR'].append(max(0, min(200, gr)))
        zone_data['RDEP'].append(max(0.1, rdep))
        zone_data['RHOB'].append(max(1.8, min(2.8, rhob)))
        zone_data['NPHI'].append(max(0, min(0.5, nphi)))
        zone_data['CALI'].append(8.5 + np.random.normal(0, 0.5))
        zone_data['DTC'].append(80 + np.random.normal(0, 10))
        zone_data['PEF'].append(max(1, min(6, pef)))
    
    df3 = pd.DataFrame(zone_data)
    filename3 = "demo_multi_zone_data.csv"
    df3.to_csv(filename3, index=False)
    datasets.append((filename3, "Multi-zone data showing different lithologies"))
    
    return datasets

def display_demo_instructions(datasets):
    """Display comprehensive demo instructions"""
    
    print("\n🚀 WELL LOG ANALYZER - DEMO INSTRUCTIONS")
    print("=" * 60)
    
    print("\n1️⃣ START THE APPLICATION:")
    print("   streamlit run well_log_app.py")
    print("   📱 Open: http://localhost:8501")
    
    print("\n2️⃣ DEMO DATASETS CREATED:")
    for i, (filename, description) in enumerate(datasets, 1):
        print(f"   📁 {filename}")
        print(f"      {description}")
        if i < len(datasets):
            print()
    
    print("\n3️⃣ FEATURES TO EXPLORE:")
    
    features = [
        ("📁 File Upload", "Drag and drop CSV files or use the file browser"),
        ("🔍 Data Validation", "Automatic validation and cleaning of uploaded data"),
        ("📊 Data Summary", "Comprehensive statistics and data quality indicators"),
        ("🟢 Gamma Ray Track", "Interactive GR vs depth plot with hover tooltips"),
        ("🔴 Resistivity Track", "RDEP vs depth with logarithmic scale"),
        ("🔵 Density-Neutron Track", "Combined RHOB and NPHI visualization"),
        ("📈 Crossplot Analysis", "RHOB vs NPHI colored by Gamma Ray"),
        ("📋 Data Table", "Raw data inspection and CSV export"),
        ("💾 Export Options", "Download plots and processed data")
    ]
    
    for feature, description in features:
        print(f"   {feature}: {description}")
    
    print("\n4️⃣ TESTING SCENARIOS:")
    
    scenarios = [
        ("Clean Data Test", "Upload demo_clean_synthetic_data.csv", 
         "Perfect for testing all visualization features"),
        ("Missing Data Test", "Upload demo_data_with_missing_values.csv", 
         "See how the app handles and cleans missing data"),
        ("Multi-Zone Test", "Upload demo_multi_zone_data.csv", 
         "Explore different lithology zones in crossplot"),
        ("Real Data Test", "Upload cleaned_15_9-23.csv (if available)", 
         "Analyze real ONGC well log data")
    ]
    
    for i, (test_name, file_info, description) in enumerate(scenarios, 1):
        print(f"\n   {i}. {test_name}:")
        print(f"      📁 {file_info}")
        print(f"      💡 {description}")
    
    print("\n5️⃣ ADVANCED FEATURES:")
    print("   🎯 Interactive tooltips show depth and log values")
    print("   📏 Y-axis automatically reversed (depth increases downward)")
    print("   📊 Log scale resistivity for better visualization")
    print("   🎨 Professional Oil & Gas color scheme")
    print("   📱 Responsive design works on all devices")
    print("   🌐 Completely offline - no internet required")
    
    print("\n6️⃣ TROUBLESHOOTING:")
    print("   ❌ Missing columns? Check CSV has all required fields")
    print("   ⚠️ Data errors? App will show validation messages")
    print("   🐌 Slow loading? Large files may take time to process")
    print("   🔄 Need refresh? Use browser refresh or restart app")
    
    print("\n✅ READY TO EXPLORE!")
    print("   Your well log analyzer is ready for petrophysical analysis!")

def main():
    """Main demo function"""
    
    # Create demo datasets
    datasets = create_demo_datasets()
    
    # Display instructions
    display_demo_instructions(datasets)
    
    print(f"\n📁 Files created in current directory:")
    for filename, _ in datasets:
        if os.path.exists(filename):
            size = os.path.getsize(filename) / 1024  # KB
            print(f"   ✅ {filename} ({size:.1f} KB)")
    
    print(f"\n🎉 Demo setup complete! Enjoy exploring your well log data!")

if __name__ == "__main__":
    main()
