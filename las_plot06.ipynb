{"cells": [{"cell_type": "code", "execution_count": 29, "id": "a563be6f", "metadata": {}, "outputs": [], "source": ["from welly import Project\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 30, "id": "7895428c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["3it [00:00,  3.62it/s]\n"]}], "source": ["wells = Project.from_las('C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/LAS Data/*.las')"]}, {"cell_type": "code", "execution_count": 17, "id": "fc50f21e", "metadata": {}, "outputs": [{"data": {"text/html": ["<table><tr><th>Index</th><th>UWI</th><th>Data</th><th>Curves</th></tr><tr><td>0</td><td><strong>L05-B-01</strong></td><td>5&nbsp;curves</td><td>GR, DT, RHOB, DRHO, NPHI</td></tr></table>"], "text/plain": ["Project(1 wells: L05-B-01)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["wells"]}, {"cell_type": "code", "execution_count": 18, "id": "a3bf2295", "metadata": {}, "outputs": [{"data": {"text/plain": ["['L05-B-01']"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["wells.uwis"]}, {"cell_type": "code", "execution_count": 19, "id": "72c9a09b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Well(uwi: 'L05-B-01', name: 'L05-B-01', 5 curves: ['GR', 'DT', 'RHOB', 'DRHO', 'NPHI'])\n"]}], "source": ["for well in wells:\n", "    print(well)"]}, {"cell_type": "code", "execution_count": null, "id": "82c4d450", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skipping well due to error: 'DataFrame' object has no attribute 'name'\n"]}], "source": ["# well_dict = {}\n", "\n", "# for well in wells:\n", "#     try:\n", "#         well_dict[well.uwi] = {\n", "#             'Well Name': well.header.name,\n", "#             'Latitude': well.location.latitude,\n", "#             'Longitude': well.location.longitude\n", "#         }\n", "#     except AttributeError as e:\n", "#         print(f\"Skipping well due to error: {e}\")\n", "#         continue\n", "\n", "# wells_df = pd.DataFrame.from_dict(well_dict, orient='index')\n", "# wells_df.reset_index(inplace=True)\n", "# wells_df.rename(columns={'index': 'UWI'}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "0e373feb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>UWI</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [UWI]\n", "Index: []"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# wells_df"]}, {"cell_type": "code", "execution_count": 27, "id": "a171a68c", "metadata": {}, "outputs": [], "source": ["import folium"]}, {"cell_type": "code", "execution_count": null, "id": "d8c79675", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'Latitude'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3805\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3804\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3805\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3806\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON>Erro<PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mindex.pyx:167\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mindex.pyx:196\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7081\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7089\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Latitude'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[28], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m mean_lat \u001b[38;5;241m=\u001b[39m \u001b[43mwells_df\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mLatitude\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mmean()\n\u001b[0;32m      2\u001b[0m mean_long \u001b[38;5;241m=\u001b[39m wells_df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mLongitude\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mmean()\n\u001b[0;32m      4\u001b[0m m \u001b[38;5;241m=\u001b[39m folium\u001b[38;5;241m.\u001b[39mMap()\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\frame.py:4102\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   4100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   4101\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 4102\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   4103\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   4104\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3812\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3807\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   3808\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[0;32m   3809\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[0;32m   3810\u001b[0m     ):\n\u001b[0;32m   3811\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[1;32m-> 3812\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3813\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3814\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3815\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3816\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3817\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Latitude'"]}], "source": ["# mean_lat = wells_df['Latitude'].mean()\n", "# mean_long = wells_df['Longitude'].mean()\n", "\n", "# # m = folium.Map()\n", "# m"]}, {"cell_type": "code", "execution_count": 31, "id": "6c114bb2", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'DataFrame' object has no attribute 'name'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_34584\\4001841223.py\u001b[0m in \u001b[0;36m?\u001b[1;34m()\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[1;32mfor\u001b[0m \u001b[0mi\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m(\u001b[0m\u001b[0max\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mwell\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;32min\u001b[0m \u001b[0menumerate\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mzip\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0maxs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mwells\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      5\u001b[0m     \u001b[0mgr\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mwell\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mget_curve\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m'GR'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      6\u001b[0m     \u001b[1;32mif\u001b[0m \u001b[0mgr\u001b[0m \u001b[1;32mis\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      7\u001b[0m         \u001b[0max\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mgr\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mplot\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0max\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0max\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mc\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;34m'green'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 8\u001b[1;33m     \u001b[0max\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mset_title\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33mf\"\u001b[0m\u001b[1;33mGR for\\n\u001b[0m\u001b[1;33m{\u001b[0m\u001b[0mwell\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mheader\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mname\u001b[0m\u001b[1;33m}\u001b[0m\u001b[1;33m\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      9\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     10\u001b[0m \u001b[0mplt\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtight_layout\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     11\u001b[0m \u001b[0mplt\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mshow\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\generic.py\u001b[0m in \u001b[0;36m?\u001b[1;34m(self, name)\u001b[0m\n\u001b[0;32m   6295\u001b[0m             \u001b[1;32mand\u001b[0m \u001b[0mname\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_accessors\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   6296\u001b[0m             \u001b[1;32mand\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_info_axis\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_can_hold_identifiers_and_holds_name\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mname\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   6297\u001b[0m         \u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   6298\u001b[0m             \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mname\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 6299\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mobject\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__getattribute__\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mname\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m: 'DataFrame' object has no attribute 'name'"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x1000 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Plot GR from all wells\n", "fig, axs = plt.subplots(figsize=(14, 10), ncols=len(wells))\n", "\n", "for i, (ax, well) in enumerate(zip(axs, wells)):\n", "    gr = well.get_curve('GR')\n", "    if gr is not None:\n", "        ax = gr.plot(ax=ax, c='green')\n", "    ax.set_title(f\"GR for\\n{well.header.name}\")\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 32, "id": "a2c3276a", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'DataFrame' object has no attribute 'name'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_34584\\2471953505.py\u001b[0m in \u001b[0;36m?\u001b[1;34m()\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[1;32mfor\u001b[0m \u001b[0mi\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m(\u001b[0m\u001b[0max\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mwell\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;32min\u001b[0m \u001b[0menumerate\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mzip\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0maxs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mwells\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      6\u001b[0m     \u001b[0mgr\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mwell\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mget_curve\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mcurve_name\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      7\u001b[0m     \u001b[1;32mif\u001b[0m \u001b[0mgr\u001b[0m \u001b[1;32mis\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      8\u001b[0m         \u001b[0max\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mgr\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mplot\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0max\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0max\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mc\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;34m'red'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 9\u001b[1;33m     \u001b[0max\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mset_title\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33mf\"\u001b[0m\u001b[1;33m{\u001b[0m\u001b[0mcurve_name\u001b[0m\u001b[1;33m}\u001b[0m\u001b[1;33m for\\n\u001b[0m\u001b[1;33m{\u001b[0m\u001b[0mwell\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mheader\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mname\u001b[0m\u001b[1;33m}\u001b[0m\u001b[1;33m\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     10\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     11\u001b[0m \u001b[0mplt\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtight_layout\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     12\u001b[0m \u001b[0mplt\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mshow\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\generic.py\u001b[0m in \u001b[0;36m?\u001b[1;34m(self, name)\u001b[0m\n\u001b[0;32m   6295\u001b[0m             \u001b[1;32mand\u001b[0m \u001b[0mname\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_accessors\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   6296\u001b[0m             \u001b[1;32mand\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_info_axis\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_can_hold_identifiers_and_holds_name\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mname\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   6297\u001b[0m         \u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   6298\u001b[0m             \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mname\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 6299\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mobject\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__getattribute__\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mname\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m: 'DataFrame' object has no attribute 'name'"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x1000 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(figsize=(14, 10), ncols=len(wells))\n", "\n", "curve_name = 'RHOB'\n", "\n", "for i, (ax, well) in enumerate(zip(axs, wells)):\n", "    gr = well.get_curve(curve_name)\n", "    if gr is not None:\n", "        ax = gr.plot(ax=ax, c='red')\n", "    ax.set_title(f\"{curve_name} for\\n{well.header.name}\")\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "9726e00f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8d171439", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "95a28408", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2b044585", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "23f265eb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "484c7fd1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d9c06cc7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}