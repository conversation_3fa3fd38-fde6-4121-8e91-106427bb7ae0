2015-10-19 14:27:57,783 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 14:27:58,017 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 14:27:58,017 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-19 14:27:58,314 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-19 14:27:58,314 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445182159119_0004, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@3d05ffdb)
2015-10-19 14:27:58,814 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-19 14:28:00,470 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445182159119_0004
2015-10-19 14:28:02,377 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-19 14:28:04,799 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-19 14:28:05,783 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@17a80e30
2015-10-19 14:28:19,659 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:1207959552+105902080
2015-10-19 14:28:20,378 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-19 14:28:20,378 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-19 14:28:20,378 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-19 14:28:20,378 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-19 14:28:20,378 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-19 14:28:20,784 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-19 14:28:49,442 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 14:28:49,442 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34174575; bufvoid = 104857600
2015-10-19 14:28:49,442 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13786524(55146096); length = 12427873/6553600
2015-10-19 14:28:49,442 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44660327 kvi 11165076(44660304)
2015-10-19 14:29:28,960 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-19 14:29:29,085 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44660327 kv 11165076(44660304) kvi 8543648(34174592)
2015-10-19 14:29:41,398 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 14:29:41,398 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44660327; bufend = 78832499; bufvoid = 104857600
2015-10-19 14:29:41,398 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11165076(44660304); kvend = 24951004(99804016); length = 12428473/6553600
2015-10-19 14:29:41,398 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89318249 kvi 22329556(89318224)
2015-10-19 14:30:18,931 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-19 14:30:18,994 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 89318249 kv 22329556(89318224) kvi 19708132(78832528)
2015-10-19 14:30:30,932 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 14:30:30,932 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 89318249; bufend = 18637204; bufvoid = 104857600
2015-10-19 14:30:30,932 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 22329556(89318224); kvend = 9902184(39608736); length = 12427373/6553600
2015-10-19 14:30:30,932 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29122962 kvi 7280736(29122944)
2015-10-19 14:31:08,137 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-19 14:31:08,324 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29122962 kv 7280736(29122944) kvi 4659308(18637232)
2015-10-19 14:31:14,715 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 14:31:14,715 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29122962; bufend = 63291202; bufvoid = 104857600
2015-10-19 14:31:14,715 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7280736(29122944); kvend = 21065680(84262720); length = 12429457/6553600
2015-10-19 14:31:14,715 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 73776953 kvi 18444232(73776928)
2015-10-19 14:31:52,905 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-19 14:31:55,405 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 73776953 kv 18444232(73776928) kvi 15822808(63291232)
2015-10-19 14:32:00,374 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-19 14:32:00,374 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 14:32:00,374 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 73776953; bufend = 103326708; bufvoid = 104857600
2015-10-19 14:32:00,374 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 18444232(73776928); kvend = 7696416(30785664); length = 10747817/6553600
