
# This file was generated by 'versioneer.py' (0.14) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

version_version = '0.44.0'
version_full = '2c67a6b8deaaa21cf23e6cb59cf66905b63281ba'
def get_versions(default={}, verbose=False):
    return {'version': version_version, 'full': version_full}

