2015-10-18 21:47:17,377 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 21:47:17,488 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 21:47:17,488 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-18 21:47:17,519 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-18 21:47:17,519 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445175094696_0005, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@666adef3)
2015-10-18 21:47:17,683 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-18 21:47:18,020 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445175094696_0005
2015-10-18 21:47:18,679 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-18 21:47:19,123 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-18 21:47:19,153 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@1e30bbc5
2015-10-18 21:47:19,184 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@35c7d3d3
2015-10-18 21:47:19,202 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=140928608, maxSingleShuffleLimit=35232152, mergeThreshold=93012888, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-18 21:47:19,204 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0005_r_000000_0 Thread started: EventFetcher for fetching Map Completion Events
2015-10-18 21:47:19,213 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 21:47:19,213 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:47:19,213 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0005_r_000000_0: Got 6 new map-outputs
2015-10-18 21:47:19,234 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0005&reduce=0&map=attempt_1445175094696_0005_m_000001_0 sent hash and received reply
2015-10-18 21:47:19,237 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0005_m_000001_0: Shuffling to disk since 217009502 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:47:19,241 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0005_m_000001_0 decomp: 217009502 len: 217009506 to DISK
2015-10-18 21:47:20,533 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217009506 bytes from map-output for attempt_1445175094696_0005_m_000001_0
2015-10-18 21:47:20,546 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 1333ms
2015-10-18 21:47:20,546 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 5 to fetcher#5
2015-10-18 21:47:20,546 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 5 of 5 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:47:20,791 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0005&reduce=0&map=attempt_1445175094696_0005_m_000000_0,attempt_1445175094696_0005_m_000005_0,attempt_1445175094696_0005_m_000004_0,attempt_1445175094696_0005_m_000002_0,attempt_1445175094696_0005_m_000003_0 sent hash and received reply
2015-10-18 21:47:20,792 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0005_m_000000_0: Shuffling to disk since 216988123 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:47:20,795 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0005_m_000000_0 decomp: 216988123 len: 216988127 to DISK
2015-10-18 21:47:22,009 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216988127 bytes from map-output for attempt_1445175094696_0005_m_000000_0
2015-10-18 21:47:22,020 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0005_m_000005_0: Shuffling to disk since 216990140 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:47:22,023 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0005_m_000005_0 decomp: 216990140 len: 216990144 to DISK
2015-10-18 21:47:23,219 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216990144 bytes from map-output for attempt_1445175094696_0005_m_000005_0
2015-10-18 21:47:23,229 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0005_m_000004_0: Shuffling to disk since 216999709 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:47:23,232 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0005_m_000004_0 decomp: 216999709 len: 216999713 to DISK
2015-10-18 21:47:24,386 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216999713 bytes from map-output for attempt_1445175094696_0005_m_000004_0
2015-10-18 21:47:24,396 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0005_m_000002_0: Shuffling to disk since 216991624 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:47:24,399 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0005_m_000002_0 decomp: 216991624 len: 216991628 to DISK
2015-10-18 21:47:25,545 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216991628 bytes from map-output for attempt_1445175094696_0005_m_000002_0
2015-10-18 21:47:25,556 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0005_m_000003_0: Shuffling to disk since 216972750 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:47:25,559 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0005_m_000003_0 decomp: 216972750 len: 216972754 to DISK
2015-10-18 21:47:26,680 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216972754 bytes from map-output for attempt_1445175094696_0005_m_000003_0
2015-10-18 21:47:26,692 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 6146ms
2015-10-18 21:47:35,239 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0005_r_000000_0: Got 1 new map-outputs
2015-10-18 21:47:35,239 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 21:47:35,239 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:47:35,249 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0005&reduce=0&map=attempt_1445175094696_0005_m_000007_0 sent hash and received reply
2015-10-18 21:47:35,250 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0005_m_000007_0: Shuffling to disk since 216976206 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:47:35,253 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0005_m_000007_0 decomp: 216976206 len: 216976210 to DISK
2015-10-18 21:47:37,380 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216976210 bytes from map-output for attempt_1445175094696_0005_m_000007_0
2015-10-18 21:47:37,386 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 2147ms
2015-10-18 21:48:31,328 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0005_r_000000_0: Got 1 new map-outputs
2015-10-18 21:48:31,328 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning 04DN8IQ.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 21:48:31,328 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to 04DN8IQ.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:48:31,338 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0005&reduce=0&map=attempt_1445175094696_0005_m_000009_0 sent hash and received reply
2015-10-18 21:48:31,342 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0005_m_000009_0: Shuffling to disk since 172334804 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:48:31,345 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0005_m_000009_0 decomp: 172334804 len: 172334808 to DISK
2015-10-18 21:48:40,343 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0005_r_000000_0: Got 1 new map-outputs
2015-10-18 21:48:40,343 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#4
2015-10-18 21:48:40,343 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#4
2015-10-18 21:48:40,354 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0005&reduce=0&map=attempt_1445175094696_0005_m_000008_1 sent hash and received reply
2015-10-18 21:48:40,355 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0005_m_000008_1: Shuffling to disk since 217015228 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:48:40,362 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#4 about to shuffle output of map attempt_1445175094696_0005_m_000008_1 decomp: 217015228 len: 217015232 to DISK
2015-10-18 21:48:41,920 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217015232 bytes from map-output for attempt_1445175094696_0005_m_000008_1
2015-10-18 21:48:41,931 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#4 in 1588ms
2015-10-18 21:48:46,357 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0005_r_000000_0: Got 1 new map-outputs
2015-10-18 21:48:55,555 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 172334808 bytes from map-output for attempt_1445175094696_0005_m_000009_0
2015-10-18 21:48:55,567 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: 04DN8IQ.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 24240ms
2015-10-18 21:48:55,567 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning 04DN8IQ.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 21:48:55,567 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to 04DN8IQ.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:48:55,576 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0005&reduce=0&map=attempt_1445175094696_0005_m_000006_0 sent hash and received reply
2015-10-18 21:48:55,578 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0005_m_000006_0: Shuffling to disk since 217011663 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:48:55,581 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0005_m_000006_0 decomp: 217011663 len: 217011667 to DISK
2015-10-18 21:49:26,099 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217011667 bytes from map-output for attempt_1445175094696_0005_m_000006_0
2015-10-18 21:49:26,112 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: 04DN8IQ.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 30545ms
2015-10-18 21:49:26,112 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: EventFetcher is interrupted.. Returning
2015-10-18 21:49:26,117 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: finalMerge called with 0 in-memory map-outputs and 10 on-disk map-outputs
2015-10-18 21:49:26,131 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 10 files, 2125289789 bytes from disk
2015-10-18 21:49:26,133 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 0 segments, 0 bytes from memory into reduce
2015-10-18 21:49:26,140 INFO [main] org.apache.hadoop.mapred.Merger: Merging 10 sorted segments
2015-10-18 21:49:26,163 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 10 segments left of total size: 2125289689 bytes
2015-10-18 21:49:26,361 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: mapred.skip.on is deprecated. Instead, use mapreduce.job.skiprecords
2015-10-18 21:54:37,761 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445175094696_0005_r_000000_0 is done. And is in the process of committing
2015-10-18 21:54:37,799 INFO [main] org.apache.hadoop.mapred.Task: Task attempt_1445175094696_0005_r_000000_0 is allowed to commit now
2015-10-18 21:54:37,805 INFO [main] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Saved output of task 'attempt_1445175094696_0005_r_000000_0' to hdfs://msra-sa-41:9000/out/out5/_temporary/1/task_1445175094696_0005_r_000000
2015-10-18 21:54:37,836 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445175094696_0005_r_000000_0' done.
