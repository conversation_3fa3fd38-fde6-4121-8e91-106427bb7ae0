<div class="container-feature-summary" id="summary-f{{ feature_dict.order_index }}"
     style="top: {{ layout.summary_spacing * (feature_dict.order_index + 0) }}px">
    <div class="selector selector__body" data-detail-div="detail-f{{ feature_dict.order_index }}" data-rollover-span="rollover-f{{ feature_dict.order_index }}"></div>
    <div class="selector selector__bottom" data-detail-div="detail-f{{ feature_dict.order_index }}" data-rollover-span="rollover-f{{ feature_dict.order_index }}"></div>
    <div class="selector selector__top" data-detail-div="detail-f{{ feature_dict.order_index }}" data-rollover-span="rollover-f{{ feature_dict.order_index }}"></div>
    <span class="bg-tab-summary" style="z-index: {{ -20000 + (2*feature_dict.order_index) }}"></span>
    <span class="bg-tab-summary-rollover" id="rollover-f{{ feature_dict.order_index }}" style="z-index: {{ -19999 + (2*feature_dict.order_index) }}"></span>
    <div class="{{ "summary-number" if feature_dict.order_index < 999 else "summary-number-vertical" }}"
        {{ "style='left:-5px'" if feature_dict.order_index < 999 and feature_dict.order_index >= 99 }}>
        {{ feature_dict.order_index + 1}}
    </div>
    <div class="pos-tab-image ic-cat"></div>
    <span class="text-title-tab color-normal ">{{ feature_dict.name }}</span>
    {% include 'feature_summary_base_stats.html' %}
    <span class="minigraph-f{{ feature_dict.order_index }} pos-minigraph-cat"></span>
</div>