import{r,b as $}from"./index.BTGIlECR.js";import{s as z,i as D,a as J,b as Q,T as U,C as Z,m as ee}from"./index.BqMXJWvY.js";import"./FormClearHelper.DuzI-rET.js";import"./withFullScreenWrapper.DkugyWpW.js";import"./Toolbar.D8wuuqZd.js";import"./checkbox.DCoT7yTn.js";import"./mergeWith.KUX-f18q.js";import"./sprintf.D7DtBTRn.js";import"./createDownloadLinkElement.DZMwyjvU.js";import"./toConsumableArray.DkNUwUhg.js";import"./possibleConstructorReturn.ChgdWjjy.js";import"./createSuper.DIDXPRra.js";import"./FileDownload.esm.ZD5PIl3I.js";const te=()=>t=>t.targetX,re=()=>t=>t.targetY,ie=()=>t=>t.targetWidth,ne=()=>t=>t.targetHeight,se=()=>t=>t.targetY+10,ae=()=>t=>Math.max(0,(t.targetHeight-28)/2),oe=z("div")({name:"DataGridOverlayEditorStyle",class:"gdg-d19meir1",propsAsIs:!1,vars:{"d19meir1-0":[re(),"px"],"d19meir1-1":[te(),"px"],"d19meir1-2":[ie(),"px"],"d19meir1-3":[ne(),"px"],"d19meir1-4":[se(),"px"],"d19meir1-5":[ae(),"px"]}});function le(){const[t,s]=r.useState();return[t??void 0,s]}function de(){const[t,s]=le(),[n,h]=r.useState(0),[f,C]=r.useState(!0);r.useLayoutEffect(()=>{if(t===void 0||!("IntersectionObserver"in window))return;const a=new IntersectionObserver(o=>{o.length!==0&&C(o[0].isIntersecting)},{threshold:1});return a.observe(t),()=>a.disconnect()},[t]),r.useEffect(()=>{if(f||t===void 0)return;let a;const o=()=>{const{right:x}=t.getBoundingClientRect();h(m=>Math.min(m+window.innerWidth-x-10,0)),a=requestAnimationFrame(o)};return a=requestAnimationFrame(o),()=>{a!==void 0&&cancelAnimationFrame(a)}},[t,f]);const v=r.useMemo(()=>({transform:`translateX(${n}px)`}),[n]);return{ref:s,style:v}}const Oe=t=>{const{target:s,content:n,onFinishEditing:h,forceEditMode:f,initialValue:C,imageEditorOverride:v,markdownDivCreateNode:a,highlight:o,className:x,theme:m,id:G,cell:g,bloom:I,validateCell:p,getCellRenderer:w,provideEditor:F,isOutsideClick:H,customEventTarget:W}=t,[l,X]=r.useState(f?n:void 0),O=r.useRef(l??n);O.current=l??n;const[y,S]=r.useState(()=>p===void 0?!0:!(D(n)&&p?.(g,n,O.current)===!1)),u=r.useCallback((e,i)=>{h(y?e:void 0,i)},[y,h]),A=r.useCallback(e=>{if(p!==void 0&&e!==void 0&&D(e)){const i=p(g,e,O.current);i===!1?S(!1):(typeof i=="object"&&(e=i),S(!0))}X(e)},[g,p]),E=r.useRef(!1),c=r.useRef(void 0),K=r.useCallback(()=>{u(l,[0,0]),E.current=!0},[l,u]),Y=r.useCallback((e,i)=>{u(e,i??c.current??[0,0]),E.current=!0},[u]),j=r.useCallback(async e=>{let i=!1;e.key==="Escape"?(e.stopPropagation(),e.preventDefault(),c.current=[0,0]):e.key==="Enter"&&!e.shiftKey?(e.stopPropagation(),e.preventDefault(),c.current=[0,1],i=!0):e.key==="Tab"&&(e.stopPropagation(),e.preventDefault(),c.current=[e.shiftKey?-1:1,0],i=!0),window.setTimeout(()=>{!E.current&&c.current!==void 0&&(u(i?l:void 0,c.current),E.current=!0)},0)},[u,l]),k=l??n,[d,L]=r.useMemo(()=>{if(J(n))return[];const e={...n,location:g},i=F?.(e);return i!==void 0?[i,!1]:[w(n)?.provideEditor?.(e),!1]},[g,n,w,F]),{ref:q,style:B}=de();let P=!0,T,M=!0,b;if(d!==void 0){P=d.disablePadding!==!0,M=d.disableStyling!==!0;const e=Q(d);e&&(b=d.styleOverride);const i=e?d.editor:d;T=r.createElement(i,{isHighlighted:o,onChange:A,value:k,initialValue:C,onFinishedEditing:Y,validatedSelection:D(k)?k.selectionRange:void 0,forceEditMode:f,target:s,imageEditorOverride:v,markdownDivCreateNode:a,isValid:y,theme:m})}b={...b,...B};const _=document.getElementById("portal");if(_===null)return console.error('Cannot open Data Grid overlay editor, because portal not found.  Please add `<div id="portal" />` as the last child of your `<body>`.'),null;let R=M?"gdg-style":"gdg-unstyle";y||(R+=" gdg-invalid"),P&&(R+=" gdg-pad");const N=I?.[0]??1,V=I?.[1]??1;return $.createPortal(r.createElement(U.Provider,{value:m},r.createElement(Z,{style:ee(m),className:x,onClickOutside:K,isOutsideClick:H,customEventTarget:W},r.createElement(oe,{ref:q,id:G,className:R,style:b,as:L===!0?"label":void 0,targetX:s.x-N,targetY:s.y-V,targetWidth:s.width+N*2,targetHeight:s.height+V*2},r.createElement("div",{className:"gdg-clip-region",onKeyDown:j},T)))),_)};export{Oe as default};
