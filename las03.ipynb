{"cells": [{"cell_type": "code", "execution_count": 86, "id": "92a70242", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import lasio as ls"]}, {"cell_type": "code", "execution_count": 87, "id": "5def77f8", "metadata": {}, "outputs": [], "source": ["las = ls.read(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/L0509_comp.las\") # type: ignore"]}, {"cell_type": "code", "execution_count": 88, "id": "0c6a71ca", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['Version', 'Well', 'Curves', 'Parameter', 'Other'])"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["las.sections.keys() # type: ignore"]}, {"cell_type": "code", "execution_count": 89, "id": "7e25d760", "metadata": {}, "outputs": [{"data": {"text/plain": ["[HeaderItem(mnemonic=\"VERS\", unit=\"\", value=\"2.0\", descr=\"CWLS LOG ASCII STANDA\"),\n", " HeaderItem(mnemonic=\"WRAP\", unit=\"\", value=\"NO\", descr=\"ONE LINE PER DEPTH STE\")]"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["las.sections['Version']"]}, {"cell_type": "code", "execution_count": 90, "id": "88e8cc1c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First Index Value (STRT): \t\t 81.0\n", "Last Index Value (STOP): \t\t 4879.7006\n", "Frame Spacing (STEP): \t\t 0.1\n", "Absent Value (NULL): \t\t -999.25\n", "Well Name (WELL): \t\t L05-B-01\n", "Field Name (FLD): \t\t L5\n", "County (CNTY): \t\t GLOMAR ADRIATIC XI\n", "State (STAT): \t\t NETHERLANDS\n", "Country (CTRY): \t\t NETHERLANDS\n", "Location (LOC): \t\t NORTH SEA\n", "API Number (API): \t\t \n", "UWI Number (UWI): \t\t L05-B-01\n", "Date (DATE): \t\t 16-Feb-2002\n", "Company Name (COMP): \t\t WIN\n", "Service Company (SRVC): \t\t \n", "LATITUDE (LATI): \t\t 53.705031\n", "LONGITUDE (LONG): \t\t 4.603479\n"]}], "source": ["for item in las.sections['Well']:\n", "    print(f\"{item.descr} ({item.mnemonic}): \\t\\t {item.value}\")"]}, {"cell_type": "code", "execution_count": 91, "id": "ae7b80d7", "metadata": {}, "outputs": [], "source": ["las.sections['Well']['CTRY']= 'U.S'"]}, {"cell_type": "code", "execution_count": 92, "id": "e2f469d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First Index Value (STRT): \t\t 81.0\n", "Last Index Value (STOP): \t\t 4879.7006\n", "Frame Spacing (STEP): \t\t 0.1\n", "Absent Value (NULL): \t\t -999.25\n", "Well Name (WELL): \t\t L05-B-01\n", "Field Name (FLD): \t\t L5\n", "County (CNTY): \t\t GLOMAR ADRIATIC XI\n", "State (STAT): \t\t NETHERLANDS\n", "Country (CTRY): \t\t U.S\n", "Location (LOC): \t\t NORTH SEA\n", "API Number (API): \t\t \n", "UWI Number (UWI): \t\t L05-B-01\n", "Date (DATE): \t\t 16-Feb-2002\n", "Company Name (COMP): \t\t WIN\n", "Service Company (SRVC): \t\t \n", "LATITUDE (LATI): \t\t 53.705031\n", "LONGITUDE (LONG): \t\t 4.603479\n"]}], "source": ["for item in las.sections['Well']:\n", "    print(f\"{item.descr} ({item.mnemonic}): \\t\\t {item.value}\")"]}, {"cell_type": "code", "execution_count": 93, "id": "62e74c33", "metadata": {}, "outputs": [{"data": {"text/plain": ["'L05-B-01'"]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["las.well.WELL.value"]}, {"cell_type": "code", "execution_count": 94, "id": "7fce30d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DEPT\n", "GR\n", "DT\n", "RHOB\n", "DRHO\n", "NPHI\n"]}], "source": ["for curve in las.curves:\n", "    print(curve.mnemonic)"]}, {"cell_type": "code", "execution_count": 95, "id": "5baaef0a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Curve: DEPT, \t Units: M, \t Description: 1     Index curve\n", "Curve: GR, \t Units: GAPI, \t Description: 2     Gamma Ray\n", "Curve: DT, \t Units: US/F, \t Description: 3     Acoustic Compressional Slowness - DTC\n", "Curve: RHOB, \t Units: G/C3, \t Description: 4     Bulk Density\n", "Curve: DRHO, \t Units: G/C3, \t Description: 5     Density Correction\n", "Curve: NPHI, \t Units: V/V, \t Description: 6     Neutron Porosity\n", "There are a total of: 6 curves present within this file\n"]}], "source": ["for count, curve in enumerate(las.curves):\n", "    print(f\"Curve: {curve.mnemonic}, \\t Units: {curve.unit}, \\t Description: {curve.descr}\")\n", "print(f\"There are a total of: {count+1} curves present within this file\")"]}, {"cell_type": "markdown", "id": "dba822f9", "metadata": {}, "source": ["# Converting LAS File to a Pandas Dataframe"]}, {"cell_type": "code", "execution_count": 96, "id": "b1190ba1", "metadata": {}, "outputs": [], "source": ["well = las.df()"]}, {"cell_type": "code", "execution_count": 97, "id": "e0b14fd1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>GR</th>\n", "      <th>DT</th>\n", "      <th>RHOB</th>\n", "      <th>DRHO</th>\n", "      <th>NPHI</th>\n", "    </tr>\n", "    <tr>\n", "      <th>DEPT</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>81.0</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81.1</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81.2</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81.3</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81.4</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      GR  DT  RHOB  DRHO  NPHI\n", "DEPT                          \n", "81.0 NaN NaN   NaN   NaN   NaN\n", "81.1 NaN NaN   NaN   NaN   NaN\n", "81.2 NaN NaN   NaN   NaN   NaN\n", "81.3 NaN NaN   NaN   NaN   NaN\n", "81.4 NaN NaN   NaN   NaN   NaN"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["well.head()"]}, {"cell_type": "code", "execution_count": 98, "id": "3cd0d838", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>GR</th>\n", "      <th>DT</th>\n", "      <th>RHOB</th>\n", "      <th>DRHO</th>\n", "      <th>NPHI</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>47974.000000</td>\n", "      <td>21709.000000</td>\n", "      <td>2075.000000</td>\n", "      <td>2075.000000</td>\n", "      <td>3095.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>36.018526</td>\n", "      <td>68.421113</td>\n", "      <td>2.563334</td>\n", "      <td>0.017502</td>\n", "      <td>0.163118</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>23.166559</td>\n", "      <td>5.695408</td>\n", "      <td>0.121987</td>\n", "      <td>0.045110</td>\n", "      <td>0.079184</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>12.019911</td>\n", "      <td>45.813599</td>\n", "      <td>1.947107</td>\n", "      <td>-0.132031</td>\n", "      <td>0.033476</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>22.705840</td>\n", "      <td>67.387177</td>\n", "      <td>2.499617</td>\n", "      <td>-0.015359</td>\n", "      <td>0.100890</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>29.765934</td>\n", "      <td>68.658997</td>\n", "      <td>2.570223</td>\n", "      <td>0.002509</td>\n", "      <td>0.130982</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>37.515741</td>\n", "      <td>69.358810</td>\n", "      <td>2.660643</td>\n", "      <td>0.048985</td>\n", "      <td>0.229602</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>218.201477</td>\n", "      <td>120.170410</td>\n", "      <td>2.788836</td>\n", "      <td>0.134148</td>\n", "      <td>0.417384</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 GR            DT         RHOB         DRHO         NPHI\n", "count  47974.000000  21709.000000  2075.000000  2075.000000  3095.000000\n", "mean      36.018526     68.421113     2.563334     0.017502     0.163118\n", "std       23.166559      5.695408     0.121987     0.045110     0.079184\n", "min       12.019911     45.813599     1.947107    -0.132031     0.033476\n", "25%       22.705840     67.387177     2.499617    -0.015359     0.100890\n", "50%       29.765934     68.658997     2.570223     0.002509     0.130982\n", "75%       37.515741     69.358810     2.660643     0.048985     0.229602\n", "max      218.201477    120.170410     2.788836     0.134148     0.417384"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["well.describe()"]}, {"cell_type": "code", "execution_count": 99, "id": "b08df88f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Index: 47988 entries, 81.0 to 4879.7006\n", "Data columns (total 5 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   GR      47974 non-null  float64\n", " 1   DT      21709 non-null  float64\n", " 2   RHOB    2075 non-null   float64\n", " 3   DRHO    2075 non-null   float64\n", " 4   NPHI    3095 non-null   float64\n", "dtypes: float64(5)\n", "memory usage: 2.2 MB\n"]}], "source": ["well.info()"]}, {"cell_type": "code", "execution_count": 100, "id": "ee652dd7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Comparing original dataset with Reduced dataset:\n", "Original shape: (47988, 5), Reduced shape: (40790, 5)\n", "Difference: 7198 rows (15.0%)\n", "\n", "Statistics for GR:\n", "           Original       Reduced  Difference %\n", "count  47974.000000  40778.000000        -15.00\n", "mean      36.018526     36.047470          0.08\n", "std       23.166559     23.183978          0.08\n", "min       12.019911     12.019911          0.00\n", "25%       22.705840     22.716020          0.04\n", "50%       29.765934     29.801640          0.12\n", "75%       37.515741     37.527811          0.03\n", "max      218.201477    218.201477          0.00\n", "\n", "Statistics for DT:\n", "           Original       Reduced  Difference %\n", "count  21709.000000  18415.000000        -15.17\n", "mean      68.421113     68.415949         -0.01\n", "std        5.695408      5.700205          0.08\n", "min       45.813599     45.813599          0.00\n", "25%       67.387177     67.373619         -0.02\n", "50%       68.658997     68.655411         -0.01\n", "75%       69.358810     69.358879          0.00\n", "max      120.170410    120.170410          0.00\n", "\n", "Statistics for RHOB:\n", "          Original      Reduced  Difference %\n", "count  2075.000000  1804.000000        -13.06\n", "mean      2.563334     2.562482         -0.03\n", "std       0.121987     0.122751          0.63\n", "min       1.947107     1.947107          0.00\n", "25%       2.499617     2.498677         -0.04\n", "50%       2.570223     2.570302          0.00\n", "75%       2.660643     2.659725         -0.03\n", "max       2.788836     2.788836          0.00\n", "\n", "Statistics for DRHO:\n", "          Original      Reduced  Difference %\n", "count  2075.000000  1804.000000        -13.06\n", "mean      0.017502     0.017206         -1.69\n", "std       0.045110     0.045056         -0.12\n", "min      -0.132031    -0.132031         -0.00\n", "25%      -0.015359    -0.015664          1.98\n", "50%       0.002509     0.002345         -6.56\n", "75%       0.048985     0.048544         -0.90\n", "max       0.134148     0.134148          0.00\n", "\n", "Statistics for NPHI:\n", "          Original      Reduced  Difference %\n", "count  3095.000000  2681.000000        -13.38\n", "mean      0.163118     0.163315          0.12\n", "std       0.079184     0.078873         -0.39\n", "min       0.033476     0.033570          0.28\n", "25%       0.100890     0.101425          0.53\n", "50%       0.130982     0.131299          0.24\n", "75%       0.229602     0.228824         -0.34\n", "max       0.417384     0.417384          0.00\n"]}], "source": ["# Compare statistics\n", "def compare_datasets(original, modified, label):\n", "    \"\"\"\n", "    Compare statistical properties between two datasets\n", "    \n", "    Parameters:\n", "    -----------\n", "    original : DataFrame\n", "        The original dataset\n", "    modified : DataFrame\n", "        The modified dataset\n", "    label : str\n", "        Label for the modified dataset in output\n", "    \"\"\"\n", "    print(f\"\\nComparing original dataset with {label} dataset:\")\n", "    \n", "    # Compare shapes\n", "    print(f\"Original shape: {original.shape}, {label} shape: {modified.shape}\")\n", "    print(f\"Difference: {original.shape[0] - modified.shape[0]} rows ({(1 - modified.shape[0]/original.shape[0])*100:.1f}%)\")\n", "    \n", "    # Compare basic statistics for each column\n", "    for column in original.columns:\n", "        if column in modified.columns and original[column].dtype.kind in 'ifc':\n", "            print(f\"\\nStatistics for {column}:\")\n", "            orig_stats = original[column].describe()\n", "            mod_stats = modified[column].describe()\n", "            \n", "            # Create comparison DataFrame\n", "            stats_df = pd.DataFrame({\n", "                'Original': orig_stats,\n", "                label: mod_stats,\n", "                'Difference %': ((mod_stats - orig_stats) / orig_stats * 100).round(2)\n", "            })\n", "            \n", "            print(stats_df)\n", "\n", "compare_datasets(well, well_reduced, \"Reduced\")"]}, {"cell_type": "code", "execution_count": 101, "id": "ee0a6b38", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Comparing original dataset with Reduced dataset:\n", "Original shape: (47988, 5), Reduced shape: (40790, 5)\n", "Difference: 7198 rows (15.0%)\n", "\n", "Statistics for GR:\n", "           Original       Reduced  Difference %\n", "count  47974.000000  40778.000000        -15.00\n", "mean      36.018526     36.047470          0.08\n", "std       23.166559     23.183978          0.08\n", "min       12.019911     12.019911          0.00\n", "25%       22.705840     22.716020          0.04\n", "50%       29.765934     29.801640          0.12\n", "75%       37.515741     37.527811          0.03\n", "max      218.201477    218.201477          0.00\n", "\n", "Statistics for DT:\n", "           Original       Reduced  Difference %\n", "count  21709.000000  18415.000000        -15.17\n", "mean      68.421113     68.415949         -0.01\n", "std        5.695408      5.700205          0.08\n", "min       45.813599     45.813599          0.00\n", "25%       67.387177     67.373619         -0.02\n", "50%       68.658997     68.655411         -0.01\n", "75%       69.358810     69.358879          0.00\n", "max      120.170410    120.170410          0.00\n", "\n", "Statistics for RHOB:\n", "          Original      Reduced  Difference %\n", "count  2075.000000  1804.000000        -13.06\n", "mean      2.563334     2.562482         -0.03\n", "std       0.121987     0.122751          0.63\n", "min       1.947107     1.947107          0.00\n", "25%       2.499617     2.498677         -0.04\n", "50%       2.570223     2.570302          0.00\n", "75%       2.660643     2.659725         -0.03\n", "max       2.788836     2.788836          0.00\n", "\n", "Statistics for DRHO:\n", "          Original      Reduced  Difference %\n", "count  2075.000000  1804.000000        -13.06\n", "mean      0.017502     0.017206         -1.69\n", "std       0.045110     0.045056         -0.12\n", "min      -0.132031    -0.132031         -0.00\n", "25%      -0.015359    -0.015664          1.98\n", "50%       0.002509     0.002345         -6.56\n", "75%       0.048985     0.048544         -0.90\n", "max       0.134148     0.134148          0.00\n", "\n", "Statistics for NPHI:\n", "          Original      Reduced  Difference %\n", "count  3095.000000  2681.000000        -13.38\n", "mean      0.163118     0.163315          0.12\n", "std       0.079184     0.078873         -0.39\n", "min       0.033476     0.033570          0.28\n", "25%       0.100890     0.101425          0.53\n", "50%       0.130982     0.131299          0.24\n", "75%       0.229602     0.228824         -0.34\n", "max       0.417384     0.417384          0.00\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Reduced dataset saved as 'well_data_reduced.csv'\n"]}], "source": ["# Complete cross-checking workflow\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)\n", "\n", "# Create reduced dataset (delete 15% randomly)\n", "reduction_percentage = 0.15\n", "n_rows_to_delete = int(len(well) * reduction_percentage)\n", "rows_to_delete = np.random.choice(well.index, size=n_rows_to_delete, replace=False)\n", "\n", "# Create reduced dataset\n", "well_reduced = well.drop(rows_to_delete)\n", "\n", "# Compare statistics\n", "compare_datasets(well, well_reduced, \"Reduced\")\n", "\n", "# Plot comparison for key curves\n", "fig, axes = plt.subplots(2, 2, figsize=(12, 10))\n", "curves_to_plot = ['GR', 'DT', 'RHOB', 'NPHI']\n", "\n", "for i, curve in enumerate(curves_to_plot):\n", "    ax = axes[i//2, i%2]\n", "    if curve in well.columns:\n", "        well[curve].hist(alpha=0.7, bins=50, label='Original', ax=ax)\n", "        well_reduced[curve].hist(alpha=0.7, bins=50, label='Reduced', ax=ax)\n", "        ax.set_title(f'{curve} Distribution Comparison')\n", "        ax.legend()\n", "        ax.set_xlabel(curve)\n", "        ax.set_ylabel('Frequency')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Save the reduced dataset\n", "well_reduced.to_csv('well_data_reduced.csv')\n", "print(f\"\\nReduced dataset saved as 'well_data_reduced.csv'\")"]}, {"cell_type": "markdown", "id": "6ac17653", "metadata": {}, "source": ["# Quick Plot"]}, {"cell_type": "code", "execution_count": 102, "id": "65b467f6", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='DEPT'>"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["well.plot()"]}, {"cell_type": "code", "execution_count": 103, "id": "042020cc", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='DEPT'>"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["well.plot(y='RHOB')"]}, {"cell_type": "code", "execution_count": 104, "id": "4775242a", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='DEPT'>"]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["well.plot(y='DRHO')"]}, {"cell_type": "code", "execution_count": 105, "id": "6dde17f5", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='DEPT'>"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["well.plot(y='NPHI')"]}, {"cell_type": "code", "execution_count": 106, "id": "b4d010b7", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='DEPT'>"]}, "execution_count": 106, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["well.plot(y='GR')"]}, {"cell_type": "code", "execution_count": 107, "id": "c2464768", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='DEPT'>"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["well.plot(y='DT')"]}, {"cell_type": "code", "execution_count": 108, "id": "765a190e", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'SP'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3805\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3804\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3805\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3806\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON>Erro<PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mindex.pyx:167\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mindex.pyx:196\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7081\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7089\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'SP'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[108], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mwell\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mplot\u001b[49m\u001b[43m(\u001b[49m\u001b[43my\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mSP\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\plotting\\_core.py:1016\u001b[0m, in \u001b[0;36mPlotAccessor.__call__\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1013\u001b[0m             \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[0;32m   1015\u001b[0m \u001b[38;5;66;03m# don't overwrite\u001b[39;00m\n\u001b[1;32m-> 1016\u001b[0m data \u001b[38;5;241m=\u001b[39m \u001b[43mdata\u001b[49m\u001b[43m[\u001b[49m\u001b[43my\u001b[49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mcopy()\n\u001b[0;32m   1018\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(data, ABCSeries):\n\u001b[0;32m   1019\u001b[0m     label_name \u001b[38;5;241m=\u001b[39m label_kw \u001b[38;5;129;01mor\u001b[39;00m y\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\frame.py:4102\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   4100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   4101\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 4102\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   4103\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   4104\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3812\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3807\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   3808\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[0;32m   3809\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[0;32m   3810\u001b[0m     ):\n\u001b[0;32m   3811\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[1;32m-> 3812\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3813\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3814\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3815\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3816\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3817\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'SP'"]}], "source": ["\n", "well.plot(y='SP')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}