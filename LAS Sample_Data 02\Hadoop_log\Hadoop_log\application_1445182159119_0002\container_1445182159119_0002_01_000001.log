2015-10-19 14:21:33,595 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445182159119_0002_000001
2015-10-19 14:21:34,174 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-19 14:21:34,174 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 2 cluster_timestamp: 1445182159119 } attemptId: 1 } keyId: 1694045684)
2015-10-19 14:21:34,424 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-19 14:21:35,455 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-19 14:21:35,533 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-19 14:21:35,580 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-19 14:21:35,580 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-19 14:21:35,580 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-19 14:21:35,580 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-19 14:21:35,580 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-19 14:21:35,596 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-19 14:21:35,596 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-19 14:21:35,596 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-19 14:21:35,642 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 14:21:35,658 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 14:21:35,689 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 14:21:35,705 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-19 14:21:35,752 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-19 14:21:36,095 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 14:21:36,158 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 14:21:36,158 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-19 14:21:36,174 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445182159119_0002 to jobTokenSecretManager
2015-10-19 14:21:36,377 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445182159119_0002 because: not enabled; too many maps; too much input;
2015-10-19 14:21:36,408 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445182159119_0002 = 1313861632. Number of splits = 10
2015-10-19 14:21:36,408 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445182159119_0002 = 1
2015-10-19 14:21:36,408 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0002Job Transitioned from NEW to INITED
2015-10-19 14:21:36,408 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445182159119_0002.
2015-10-19 14:21:36,455 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 14:21:36,471 INFO [Socket Reader #1 for port 49782] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 49782
2015-10-19 14:21:36,486 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-19 14:21:36,486 INFO [IPC Server listener on 49782] org.apache.hadoop.ipc.Server: IPC Server listener on 49782: starting
2015-10-19 14:21:36,486 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 14:21:36,486 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at 04DN8IQ.fareast.corp.microsoft.com/*************:49782
2015-10-19 14:21:36,564 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-19 14:21:36,580 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-19 14:21:36,596 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-19 14:21:36,596 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-19 14:21:36,596 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-19 14:21:36,596 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-19 14:21:36,596 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-19 14:21:36,611 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 49789
2015-10-19 14:21:36,611 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-19 14:21:36,658 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_49789_mapreduce____tv81ja\webapp
2015-10-19 14:21:36,877 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:49789
2015-10-19 14:21:36,877 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 49789
2015-10-19 14:21:37,299 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-19 14:21:37,299 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445182159119_0002
2015-10-19 14:21:37,299 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 14:21:37,314 INFO [Socket Reader #1 for port 49792] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 49792
2015-10-19 14:21:37,330 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 14:21:37,330 INFO [IPC Server listener on 49792] org.apache.hadoop.ipc.Server: IPC Server listener on 49792: starting
2015-10-19 14:21:37,346 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-19 14:21:37,346 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-19 14:21:37,346 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-19 14:21:37,377 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-19 14:21:37,486 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-19 14:21:37,486 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-19 14:21:37,486 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-19 14:21:37,502 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-19 14:21:37,502 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0002Job Transitioned from INITED to SETUP
2015-10-19 14:21:37,502 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-19 14:21:37,533 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0002Job Transitioned from SETUP to RUNNING
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:37,580 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:37,580 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-19 14:21:37,596 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-19 14:21:37,611 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445182159119_0002, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0002/job_1445182159119_0002_1.jhist
2015-10-19 14:21:38,486 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 14:21:38,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:24576, vCores:-3> knownNMs=4
2015-10-19 14:21:38,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:24576, vCores:-3>
2015-10-19 14:21:38,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:39,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-19 14:21:39,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000002 to attempt_1445182159119_0002_m_000000_0
2015-10-19 14:21:39,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000003 to attempt_1445182159119_0002_m_000001_0
2015-10-19 14:21:39,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:12288, vCores:-15>
2015-10-19 14:21:39,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:39,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:2 RackLocal:0
2015-10-19 14:21:39,674 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:39,705 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0002/job.jar
2015-10-19 14:21:39,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0002/job.xml
2015-10-19 14:21:39,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-19 14:21:39,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-19 14:21:39,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-19 14:21:39,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:21:39,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:39,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:21:39,783 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000002 taskAttempt attempt_1445182159119_0002_m_000000_0
2015-10-19 14:21:39,783 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000003 taskAttempt attempt_1445182159119_0002_m_000001_0
2015-10-19 14:21:39,783 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000000_0
2015-10-19 14:21:39,783 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000001_0
2015-10-19 14:21:39,783 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:21:39,799 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:21:39,861 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000000_0 : 13562
2015-10-19 14:21:39,861 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000001_0 : 13562
2015-10-19 14:21:39,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000000_0] using containerId: [container_1445182159119_0002_01_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:21:39,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:21:39,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000001_0] using containerId: [container_1445182159119_0002_01_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:21:39,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:21:39,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000000
2015-10-19 14:21:39,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:21:39,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000001
2015-10-19 14:21:39,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:21:40,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:12288, vCores:-15> knownNMs=4
2015-10-19 14:21:40,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:12288, vCores:-15>
2015-10-19 14:21:40,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:41,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:12288, vCores:-15>
2015-10-19 14:21:41,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:42,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:21:42,596 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:42,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000004 to attempt_1445182159119_0002_m_000002_0
2015-10-19 14:21:42,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-17>
2015-10-19 14:21:42,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:42,596 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:2 RackLocal:1
2015-10-19 14:21:42,596 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:42,596 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:21:42,611 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000004 taskAttempt attempt_1445182159119_0002_m_000002_0
2015-10-19 14:21:42,611 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000002_0
2015-10-19 14:21:42,611 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:21:42,643 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000002_0 : 13562
2015-10-19 14:21:42,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000002_0] using containerId: [container_1445182159119_0002_01_000004 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 14:21:42,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:21:42,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000002
2015-10-19 14:21:42,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:21:43,174 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:21:43,189 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000003 asked for a task
2015-10-19 14:21:43,189 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000003 given task: attempt_1445182159119_0002_m_000001_0
2015-10-19 14:21:43,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:9216, vCores:-18> knownNMs=4
2015-10-19 14:21:43,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:21:43,611 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:43,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000005 to attempt_1445182159119_0002_m_000003_0
2015-10-19 14:21:43,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-18>
2015-10-19 14:21:43,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:43,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:2 RackLocal:2
2015-10-19 14:21:43,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:43,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:21:43,642 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000005 taskAttempt attempt_1445182159119_0002_m_000003_0
2015-10-19 14:21:43,642 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000003_0
2015-10-19 14:21:43,642 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:21:43,705 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000003_0 : 13562
2015-10-19 14:21:43,705 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000003_0] using containerId: [container_1445182159119_0002_01_000005 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 14:21:43,705 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:21:43,705 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000003
2015-10-19 14:21:43,705 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:21:44,002 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:21:44,033 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000002 asked for a task
2015-10-19 14:21:44,033 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000002 given task: attempt_1445182159119_0002_m_000000_0
2015-10-19 14:21:44,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:8192, vCores:-19> knownNMs=4
2015-10-19 14:21:44,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-19>
2015-10-19 14:21:44,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:45,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:21:45,689 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:45,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000006 to attempt_1445182159119_0002_m_000004_0
2015-10-19 14:21:45,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-20>
2015-10-19 14:21:45,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:45,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:2 RackLocal:3
2015-10-19 14:21:45,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:45,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:21:45,721 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000006 taskAttempt attempt_1445182159119_0002_m_000004_0
2015-10-19 14:21:45,721 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000004_0
2015-10-19 14:21:45,721 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:21:45,877 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000004_0 : 13562
2015-10-19 14:21:45,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000004_0] using containerId: [container_1445182159119_0002_01_000006 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 14:21:45,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:21:45,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000004
2015-10-19 14:21:45,877 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:21:46,236 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:21:46,252 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000004 asked for a task
2015-10-19 14:21:46,252 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000004 given task: attempt_1445182159119_0002_m_000002_0
2015-10-19 14:21:46,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:3072, vCores:-24> knownNMs=4
2015-10-19 14:21:46,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-19 14:21:46,783 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:46,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000007 to attempt_1445182159119_0002_m_000005_0
2015-10-19 14:21:46,783 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:46,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000008 to attempt_1445182159119_0002_m_000006_0
2015-10-19 14:21:46,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-19 14:21:46,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:46,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:2 RackLocal:5
2015-10-19 14:21:46,783 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:46,783 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:21:46,783 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:46,783 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:21:46,924 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000007 taskAttempt attempt_1445182159119_0002_m_000005_0
2015-10-19 14:21:46,924 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000005_0
2015-10-19 14:21:46,924 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:21:47,002 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000008 taskAttempt attempt_1445182159119_0002_m_000006_0
2015-10-19 14:21:47,002 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000006_0
2015-10-19 14:21:47,002 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:21:47,549 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000005_0 : 13562
2015-10-19 14:21:47,549 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000005_0] using containerId: [container_1445182159119_0002_01_000007 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 14:21:47,549 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:21:47,549 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000005
2015-10-19 14:21:47,549 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:21:47,830 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-19 14:21:47,830 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:21:47,830 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:47,830 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000009 to attempt_1445182159119_0002_m_000007_0
2015-10-19 14:21:47,830 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 14:21:47,830 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:47,830 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:2 RackLocal:6
2015-10-19 14:21:47,830 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:47,846 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:21:47,971 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000009 taskAttempt attempt_1445182159119_0002_m_000007_0
2015-10-19 14:21:47,971 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000007_0
2015-10-19 14:21:47,971 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:21:48,143 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000006_0 : 13562
2015-10-19 14:21:48,143 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000006_0] using containerId: [container_1445182159119_0002_01_000008 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 14:21:48,143 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:21:48,143 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000006
2015-10-19 14:21:48,143 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:21:48,643 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000007_0 : 13562
2015-10-19 14:21:48,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000007_0] using containerId: [container_1445182159119_0002_01_000009 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 14:21:48,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:21:48,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000007
2015-10-19 14:21:48,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:21:48,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:1024, vCores:-26> knownNMs=4
2015-10-19 14:21:48,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:21:48,893 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:48,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000010 to attempt_1445182159119_0002_m_000008_0
2015-10-19 14:21:48,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 14:21:48,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:48,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:2 RackLocal:7
2015-10-19 14:21:48,893 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:48,893 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:21:49,002 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000010 taskAttempt attempt_1445182159119_0002_m_000008_0
2015-10-19 14:21:49,002 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000008_0
2015-10-19 14:21:49,002 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:21:49,658 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000008_0 : 13562
2015-10-19 14:21:49,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000008_0] using containerId: [container_1445182159119_0002_01_000010 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 14:21:49,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:21:49,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000008
2015-10-19 14:21:49,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:21:49,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:21:49,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:21:49,939 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:49,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000011 to attempt_1445182159119_0002_m_000009_0
2015-10-19 14:21:49,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:21:49,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:49,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:2 RackLocal:8
2015-10-19 14:21:49,939 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:49,939 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:21:50,018 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000011 taskAttempt attempt_1445182159119_0002_m_000009_0
2015-10-19 14:21:50,018 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000009_0
2015-10-19 14:21:50,018 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:21:50,205 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:21:50,252 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.13102192
2015-10-19 14:21:50,299 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000005 asked for a task
2015-10-19 14:21:50,299 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000005 given task: attempt_1445182159119_0002_m_000003_0
2015-10-19 14:21:51,002 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:21:51,361 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000009_0 : 13562
2015-10-19 14:21:51,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000009_0] using containerId: [container_1445182159119_0002_01_000011 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 14:21:51,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:21:51,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000009
2015-10-19 14:21:51,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:21:52,627 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.13101934
2015-10-19 14:21:53,330 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.13102192
2015-10-19 14:21:54,705 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:21:55,424 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000006 asked for a task
2015-10-19 14:21:55,424 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000006 given task: attempt_1445182159119_0002_m_000004_0
2015-10-19 14:21:55,752 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.13101934
2015-10-19 14:21:56,361 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.191899
2015-10-19 14:21:57,158 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:21:57,190 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000009 asked for a task
2015-10-19 14:21:57,190 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000009 given task: attempt_1445182159119_0002_m_000007_0
2015-10-19 14:21:57,377 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:21:57,455 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000008 asked for a task
2015-10-19 14:21:57,455 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000008 given task: attempt_1445182159119_0002_m_000006_0
2015-10-19 14:21:58,830 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.23921585
2015-10-19 14:21:59,408 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.23921879
2015-10-19 14:22:00,799 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:22:01,315 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000010 asked for a task
2015-10-19 14:22:01,315 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000010 given task: attempt_1445182159119_0002_m_000008_0
2015-10-19 14:22:01,862 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.23921585
2015-10-19 14:22:02,471 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.23921879
2015-10-19 14:22:02,658 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:22:02,768 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000007 asked for a task
2015-10-19 14:22:02,768 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000007 given task: attempt_1445182159119_0002_m_000005_0
2015-10-19 14:22:04,955 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.23921585
2015-10-19 14:22:05,565 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.26438048
2015-10-19 14:22:06,174 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.009437234
2015-10-19 14:22:06,362 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.014000558
2015-10-19 14:22:06,815 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:22:07,065 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000011 asked for a task
2015-10-19 14:22:07,065 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000011 given task: attempt_1445182159119_0002_m_000009_0
2015-10-19 14:22:07,612 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.013022106
2015-10-19 14:22:07,674 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.05080081
2015-10-19 14:22:08,034 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.3474062
2015-10-19 14:22:08,643 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.34743196
2015-10-19 14:22:09,237 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.017253663
2015-10-19 14:22:09,424 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.028006027
2015-10-19 14:22:10,705 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.021491531
2015-10-19 14:22:10,784 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.08564974
2015-10-19 14:22:11,112 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.3474062
2015-10-19 14:22:11,752 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.34743196
2015-10-19 14:22:12,284 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.030609153
2015-10-19 14:22:12,455 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.040705
2015-10-19 14:22:13,752 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.030933471
2015-10-19 14:22:13,815 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.10193361
2015-10-19 14:22:14,221 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.3474062
2015-10-19 14:22:14,331 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.07197353
2015-10-19 14:22:14,862 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.34743196
2015-10-19 14:22:15,315 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.036797803
2015-10-19 14:22:15,612 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.04591708
2015-10-19 14:22:15,627 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.0078088855
2015-10-19 14:22:16,799 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.047217686
2015-10-19 14:22:16,877 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.11496198
2015-10-19 14:22:17,315 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.45563135
2015-10-19 14:22:17,362 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.025727818
2015-10-19 14:22:17,518 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.0853258
2015-10-19 14:22:17,518 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.054891337
2015-10-19 14:22:17,940 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.4382673
2015-10-19 14:22:18,549 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.044288922
2015-10-19 14:22:18,674 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.059595082
2015-10-19 14:22:18,831 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.0113916155
2015-10-19 14:22:19,862 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.064804904
2015-10-19 14:22:19,971 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.13101135
2015-10-19 14:22:20,409 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.45563135
2015-10-19 14:22:20,534 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.040380254
2015-10-19 14:22:20,690 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.10161017
2015-10-19 14:22:20,690 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.075942434
2015-10-19 14:22:21,049 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.455629
2015-10-19 14:22:21,596 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.05926663
2015-10-19 14:22:21,706 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.073600374
2015-10-19 14:22:22,018 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.017255105
2015-10-19 14:22:22,924 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.09216136
2015-10-19 14:22:23,096 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.13101135
2015-10-19 14:22:23,628 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.049827233
2015-10-19 14:22:23,784 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.50725496
2015-10-19 14:22:23,924 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.11463503
2015-10-19 14:22:23,924 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.10070571
2015-10-19 14:22:24,112 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.455629
2015-10-19 14:22:24,706 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.07099453
2015-10-19 14:22:24,799 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.0853232
2015-10-19 14:22:25,284 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.025071668
2015-10-19 14:22:26,159 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.114963315
2015-10-19 14:22:26,315 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.13101135
2015-10-19 14:22:27,065 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.060249045
2015-10-19 14:22:27,143 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.5505142
2015-10-19 14:22:27,424 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.11432751
2015-10-19 14:22:27,440 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.13104042
2015-10-19 14:22:27,737 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.56380075
2015-10-19 14:22:28,096 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.08051777
2015-10-19 14:22:28,143 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.10291213
2015-10-19 14:22:28,768 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.033539902
2015-10-19 14:22:29,284 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.120170504
2015-10-19 14:22:29,487 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.13101135
2015-10-19 14:22:30,221 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.5638294
2015-10-19 14:22:30,815 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.56380075
2015-10-19 14:22:30,956 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.084355846
2015-10-19 14:22:31,300 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.09541923
2015-10-19 14:22:31,315 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.11463475
2015-10-19 14:22:31,346 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.13104042
2015-10-19 14:22:31,378 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.14528708
2015-10-19 14:22:32,628 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.040706128
2015-10-19 14:22:32,643 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.56380075
2015-10-19 14:22:33,050 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.13103712
2015-10-19 14:22:33,206 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.13101135
2015-10-19 14:22:33,315 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.5638294
2015-10-19 14:22:33,846 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.667
2015-10-19 14:22:34,737 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.131014
2015-10-19 14:22:34,784 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.13102706
2015-10-19 14:22:35,565 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.122855626
2015-10-19 14:22:35,659 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.16604526
2015-10-19 14:22:36,175 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.13104042
2015-10-19 14:22:36,362 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.66549253
2015-10-19 14:22:36,409 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.66549253
2015-10-19 14:22:36,597 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.13103712
2015-10-19 14:22:36,659 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.13101135
2015-10-19 14:22:36,909 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.667
2015-10-19 14:22:37,159 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.07425083
2015-10-19 14:22:38,331 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.131014
2015-10-19 14:22:38,550 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.13102706
2015-10-19 14:22:39,378 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.667
2015-10-19 14:22:39,972 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.667
2015-10-19 14:22:40,065 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.13103712
2015-10-19 14:22:40,206 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.13101135
2015-10-19 14:22:40,456 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.13102318
2015-10-19 14:22:40,628 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.16604526
2015-10-19 14:22:41,159 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.13104042
2015-10-19 14:22:41,800 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.131014
2015-10-19 14:22:42,143 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.13102706
2015-10-19 14:22:42,440 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.667
2015-10-19 14:22:42,518 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.13104132
2015-10-19 14:22:43,018 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.70118344
2015-10-19 14:22:43,581 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.13103712
2015-10-19 14:22:43,612 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.18826662
2015-10-19 14:22:44,815 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.16604526
2015-10-19 14:22:45,362 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.131014
2015-10-19 14:22:45,425 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.13102318
2015-10-19 14:22:45,487 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.667
2015-10-19 14:22:45,628 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.13102706
2015-10-19 14:22:45,956 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.13104042
2015-10-19 14:22:46,034 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.7510041
2015-10-19 14:22:46,831 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.13104132
2015-10-19 14:22:47,237 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.13103712
2015-10-19 14:22:47,440 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.23923388
2015-10-19 14:22:48,503 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.70905215
2015-10-19 14:22:48,815 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.131014
2015-10-19 14:22:49,081 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.79552275
2015-10-19 14:22:49,128 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.16604526
2015-10-19 14:22:49,175 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.13102706
2015-10-19 14:22:49,753 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.13102318
2015-10-19 14:22:50,691 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.13104042
2015-10-19 14:22:50,722 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.13103712
2015-10-19 14:22:50,909 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.23923388
2015-10-19 14:22:51,456 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.13104132
2015-10-19 14:22:51,550 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.75812906
2015-10-19 14:22:52,119 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.84602773
2015-10-19 14:22:52,393 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.131014
2015-10-19 14:22:52,684 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.13102706
2015-10-19 14:22:53,248 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.16604526
2015-10-19 14:22:53,719 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.13102318
2015-10-19 14:22:54,138 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.13103712
2015-10-19 14:22:54,289 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.23923388
2015-10-19 14:22:54,592 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.7831712
2015-10-19 14:22:54,655 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.13104042
2015-10-19 14:22:55,186 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.8962468
2015-10-19 14:22:55,499 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.13104132
2015-10-19 14:22:55,999 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.131014
2015-10-19 14:22:56,249 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.13102706
2015-10-19 14:22:57,577 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.16604526
2015-10-19 14:22:57,624 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.8138703
2015-10-19 14:22:57,639 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.13769323
2015-10-19 14:22:57,796 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.23923388
2015-10-19 14:22:58,093 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.13102318
2015-10-19 14:22:58,218 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.9412476
2015-10-19 14:22:59,124 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.18468271
2015-10-19 14:22:59,451 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.131014
2015-10-19 14:22:59,578 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.13102706
2015-10-19 14:22:59,768 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.13104132
2015-10-19 14:23:00,709 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.8377114
2015-10-19 14:23:00,959 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.1901931
2015-10-19 14:23:01,131 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.23923388
2015-10-19 14:23:01,302 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 0.98764086
2015-10-19 14:23:02,502 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.16604526
2015-10-19 14:23:02,762 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.13743556
2015-10-19 14:23:02,807 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000000_0 is : 1.0
2015-10-19 14:23:02,821 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.13102318
2015-10-19 14:23:02,905 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0002_m_000000_0
2015-10-19 14:23:02,918 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:23:02,919 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000002 taskAttempt attempt_1445182159119_0002_m_000000_0
2015-10-19 14:23:02,919 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000000_0
2015-10-19 14:23:02,921 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:23:02,988 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.13102706
2015-10-19 14:23:03,364 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:23:03,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000000_0
2015-10-19 14:23:03,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:23:03,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-19 14:23:03,489 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:2 RackLocal:8
2015-10-19 14:23:03,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:03,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-19 14:23:03,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-19 14:23:03,629 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:2 RackLocal:8
2015-10-19 14:23:03,786 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0002_m_000003
2015-10-19 14:23:03,786 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0002_m_000003
2015-10-19 14:23:03,786 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:23:03,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:03,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:03,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000003_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:23:03,833 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.8547092
2015-10-19 14:23:03,989 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.23924798
2015-10-19 14:23:04,583 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.13104132
2015-10-19 14:23:04,676 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:2 RackLocal:8
2015-10-19 14:23:04,942 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.22634849
2015-10-19 14:23:04,973 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.23923388
2015-10-19 14:23:05,208 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:23:05,208 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000002
2015-10-19 14:23:05,208 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:2 RackLocal:8
2015-10-19 14:23:05,208 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:23:06,223 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.1592567
2015-10-19 14:23:06,254 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:23:06,254 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-19 14:23:06,254 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000012 to attempt_1445182159119_0002_r_000000_0
2015-10-19 14:23:06,254 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:2 RackLocal:8
2015-10-19 14:23:06,286 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:06,286 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:23:06,286 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000012 taskAttempt attempt_1445182159119_0002_r_000000_0
2015-10-19 14:23:06,286 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_r_000000_0
2015-10-19 14:23:06,286 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:23:06,489 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.13808507
2015-10-19 14:23:06,520 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_r_000000_0 : 13562
2015-10-19 14:23:06,520 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_r_000000_0] using containerId: [container_1445182159119_0002_01_000012 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:23:06,520 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:23:06,520 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_r_000000
2015-10-19 14:23:06,520 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:23:06,895 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.88136375
2015-10-19 14:23:06,895 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.16604526
2015-10-19 14:23:07,270 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.13102318
2015-10-19 14:23:07,379 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:23:08,348 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.23924637
2015-10-19 14:23:08,598 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.23923388
2015-10-19 14:23:08,598 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:23:08,614 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.23924798
2015-10-19 14:23:08,645 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_r_000012 asked for a task
2015-10-19 14:23:08,645 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_r_000012 given task: attempt_1445182159119_0002_r_000000_0
2015-10-19 14:23:09,239 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.13104132
2015-10-19 14:23:09,614 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.18595882
2015-10-19 14:23:09,895 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.15795161
2015-10-19 14:23:10,114 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 0 maxEvents 10000
2015-10-19 14:23:10,239 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.91190535
2015-10-19 14:23:11,130 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 14:23:11,426 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.23924637
2015-10-19 14:23:11,692 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.23923388
2015-10-19 14:23:12,145 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 14:23:12,426 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.20509332
2015-10-19 14:23:12,426 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.13102318
2015-10-19 14:23:12,926 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.18921985
2015-10-19 14:23:13,098 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.23924798
2015-10-19 14:23:13,161 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.17683865
2015-10-19 14:23:13,176 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 14:23:13,286 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 0.96027637
2015-10-19 14:23:13,583 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.13104132
2015-10-19 14:23:14,223 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 14:23:14,676 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.23924637
2015-10-19 14:23:14,989 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.23923388
2015-10-19 14:23:15,255 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 14:23:15,786 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.033333335
2015-10-19 14:23:16,192 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000001_0 is : 1.0
2015-10-19 14:23:16,255 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0002_m_000001_0
2015-10-19 14:23:16,270 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:23:16,270 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000003 taskAttempt attempt_1445182159119_0002_m_000001_0
2015-10-19 14:23:16,270 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000001_0
2015-10-19 14:23:16,270 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:23:16,317 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 14:23:16,536 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.22276321
2015-10-19 14:23:16,583 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.3031575
2015-10-19 14:23:16,583 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:23:16,583 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000001_0
2015-10-19 14:23:16,583 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:23:16,583 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-19 14:23:16,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:2 RackLocal:8
2015-10-19 14:23:16,645 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.20878899
2015-10-19 14:23:16,770 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.14915937
2015-10-19 14:23:17,301 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.23924798
2015-10-19 14:23:17,411 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 14:23:17,770 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000003
2015-10-19 14:23:17,770 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:23:17,770 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:23:17,770 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000013 to attempt_1445182159119_0002_m_000003_1
2015-10-19 14:23:17,770 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:3 RackLocal:8
2015-10-19 14:23:17,770 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:17,770 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000003_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:23:17,770 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000013 taskAttempt attempt_1445182159119_0002_m_000003_1
2015-10-19 14:23:17,770 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000003_1
2015-10-19 14:23:17,770 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:23:17,864 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.13104132
2015-10-19 14:23:18,052 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000003_1 : 13562
2015-10-19 14:23:18,052 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000003_1] using containerId: [container_1445182159119_0002_01_000013 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:23:18,052 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000003_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:23:18,052 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000003
2015-10-19 14:23:18,161 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.23924637
2015-10-19 14:23:18,458 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:18,473 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.25402987
2015-10-19 14:23:18,802 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0002_m_000005
2015-10-19 14:23:18,802 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:23:18,802 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0002_m_000005
2015-10-19 14:23:18,802 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:18,802 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:18,802 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000005_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:23:18,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:23:18,864 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:23:19,505 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:19,864 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:3 RackLocal:8
2015-10-19 14:23:19,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:23:19,973 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.23919508
2015-10-19 14:23:20,114 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:23:20,177 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000013 asked for a task
2015-10-19 14:23:20,177 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000013 given task: attempt_1445182159119_0002_m_000003_1
2015-10-19 14:23:20,208 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.2325349
2015-10-19 14:23:20,239 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.3031575
2015-10-19 14:23:20,552 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:20,755 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.18433233
2015-10-19 14:23:21,083 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.23924798
2015-10-19 14:23:21,598 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:21,614 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.17000109
2015-10-19 14:23:21,739 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.23924637
2015-10-19 14:23:21,958 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:23:22,036 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.29474062
2015-10-19 14:23:22,645 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:23,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:23:23,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000014 to attempt_1445182159119_0002_m_000005_1
2015-10-19 14:23:23,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:4 RackLocal:8
2015-10-19 14:23:23,114 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:23,114 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000005_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:23:23,114 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000014 taskAttempt attempt_1445182159119_0002_m_000005_1
2015-10-19 14:23:23,114 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000005_1
2015-10-19 14:23:23,114 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:23:23,427 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.23919508
2015-10-19 14:23:23,630 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.23922287
2015-10-19 14:23:23,677 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:24,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:23:24,333 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.3031575
2015-10-19 14:23:24,708 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:24,911 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.23924798
2015-10-19 14:23:24,989 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.21331903
2015-10-19 14:23:24,989 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:23:25,223 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.23924637
2015-10-19 14:23:25,567 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.34743717
2015-10-19 14:23:26,130 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.23496306
2015-10-19 14:23:26,614 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:26,927 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.23919508
2015-10-19 14:23:27,161 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.23922287
2015-10-19 14:23:27,286 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.13102706
2015-10-19 14:23:27,614 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:28,020 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:23:28,661 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:28,661 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.23924637
2015-10-19 14:23:29,099 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.34743717
2015-10-19 14:23:29,099 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000005_1 : 13562
2015-10-19 14:23:29,099 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000005_1] using containerId: [container_1445182159119_0002_01_000014 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:23:29,099 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000005_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:23:29,099 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000005
2015-10-19 14:23:29,161 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.3031575
2015-10-19 14:23:29,364 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.23924798
2015-10-19 14:23:29,708 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:29,739 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.23921506
2015-10-19 14:23:30,317 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.13102706
2015-10-19 14:23:30,380 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.23919508
2015-10-19 14:23:30,520 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.23922287
2015-10-19 14:23:30,692 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.23922269
2015-10-19 14:23:30,739 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:31,052 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:23:31,786 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:32,099 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.23924637
2015-10-19 14:23:32,614 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.34743717
2015-10-19 14:23:32,833 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:33,349 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.23922287
2015-10-19 14:23:33,427 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.3031575
2015-10-19 14:23:33,833 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0002_m_000002
2015-10-19 14:23:33,833 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0002_m_000002
2015-10-19 14:23:33,833 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:23:33,833 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:33,833 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:33,833 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000002_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:23:33,864 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:33,927 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.23919508
2015-10-19 14:23:34,021 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.23924798
2015-10-19 14:23:34,036 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.23922287
2015-10-19 14:23:34,083 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:23:34,333 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.23921506
2015-10-19 14:23:34,567 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:4 RackLocal:8
2015-10-19 14:23:34,567 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:23:34,880 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:35,489 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.23922269
2015-10-19 14:23:35,489 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.2432176
2015-10-19 14:23:35,896 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:36,146 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.34743717
2015-10-19 14:23:36,396 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.23922287
2015-10-19 14:23:36,927 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:37,130 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:23:37,411 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.23919508
2015-10-19 14:23:37,583 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.23922287
2015-10-19 14:23:37,833 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.3031575
2015-10-19 14:23:37,974 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:38,692 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.25696254
2015-10-19 14:23:38,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:23:38,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000015 to attempt_1445182159119_0002_m_000002_1
2015-10-19 14:23:38,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:5 RackLocal:8
2015-10-19 14:23:38,739 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:38,739 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000002_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:23:38,739 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000015 taskAttempt attempt_1445182159119_0002_m_000002_1
2015-10-19 14:23:38,739 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000002_1
2015-10-19 14:23:38,739 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:23:38,911 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.31330237
2015-10-19 14:23:39,021 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:39,317 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.23921506
2015-10-19 14:23:39,474 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.23922287
2015-10-19 14:23:39,474 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000002_1 : 13562
2015-10-19 14:23:39,474 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000002_1] using containerId: [container_1445182159119_0002_01_000015 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:23:39,489 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000002_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:23:39,489 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000002
2015-10-19 14:23:39,755 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.34743717
2015-10-19 14:23:39,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:23:39,927 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.23922269
2015-10-19 14:23:40,052 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:40,474 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:23:41,083 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:41,099 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.23922287
2015-10-19 14:23:41,114 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.23919508
2015-10-19 14:23:42,068 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.3031575
2015-10-19 14:23:42,130 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:42,521 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.3473985
2015-10-19 14:23:42,739 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.34743145
2015-10-19 14:23:43,271 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.34743717
2015-10-19 14:23:43,380 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.3390199
2015-10-19 14:23:43,443 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:43,505 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:23:44,458 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:44,505 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.23921506
2015-10-19 14:23:44,739 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.23922269
2015-10-19 14:23:45,036 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.23922287
2015-10-19 14:23:45,083 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.23919508
2015-10-19 14:23:45,536 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:45,583 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.3473985
2015-10-19 14:23:45,755 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:23:45,802 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000015 asked for a task
2015-10-19 14:23:45,802 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000015 given task: attempt_1445182159119_0002_m_000002_1
2015-10-19 14:23:45,864 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:23:45,927 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000014 asked for a task
2015-10-19 14:23:45,927 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000014 given task: attempt_1445182159119_0002_m_000005_1
2015-10-19 14:23:46,271 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.34743145
2015-10-19 14:23:46,521 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.3031575
2015-10-19 14:23:46,536 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:46,536 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:23:46,864 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.34743717
2015-10-19 14:23:47,568 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:47,568 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.34742972
2015-10-19 14:23:48,536 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.23922287
2015-10-19 14:23:48,583 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:48,630 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.23919508
2015-10-19 14:23:48,630 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.3473985
2015-10-19 14:23:48,865 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0002_m_000008
2015-10-19 14:23:48,865 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:23:48,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0002_m_000008
2015-10-19 14:23:48,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:48,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:48,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000008_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:23:49,411 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.23921506
2015-10-19 14:23:49,599 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:23:49,599 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:49,693 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:5 RackLocal:8
2015-10-19 14:23:49,708 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:23:49,818 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.34743145
2015-10-19 14:23:49,833 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.23922269
2015-10-19 14:23:50,349 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.34743717
2015-10-19 14:23:50,630 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:51,599 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.30655545
2015-10-19 14:23:51,661 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:51,661 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.45559394
2015-10-19 14:23:52,068 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.23922287
2015-10-19 14:23:52,130 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.2560594
2015-10-19 14:23:52,583 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.34742972
2015-10-19 14:23:52,646 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:23:52,708 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:53,286 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.131014
2015-10-19 14:23:53,318 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.34743145
2015-10-19 14:23:53,646 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.13104132
2015-10-19 14:23:53,724 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.34743717
2015-10-19 14:23:53,755 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:54,740 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.45559394
2015-10-19 14:23:54,771 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.23922269
2015-10-19 14:23:54,802 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:54,802 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.23921506
2015-10-19 14:23:55,333 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.27292788
2015-10-19 14:23:55,412 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.31428033
2015-10-19 14:23:56,302 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.131014
2015-10-19 14:23:56,427 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.3952758
2015-10-19 14:23:56,568 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:23:56,568 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:56,583 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.34743145
2015-10-19 14:23:56,708 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.13104132
2015-10-19 14:23:57,115 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.34743717
2015-10-19 14:23:57,380 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.34742972
2015-10-19 14:23:57,599 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:57,802 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.45559394
2015-10-19 14:23:58,443 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.29962316
2015-10-19 14:23:58,615 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.33740318
2015-10-19 14:23:58,662 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:59,255 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.23922269
2015-10-19 14:23:59,365 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.131014
2015-10-19 14:23:59,630 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:23:59,662 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.23921506
2015-10-19 14:23:59,677 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:23:59,724 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.13104132
2015-10-19 14:23:59,833 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.34743145
2015-10-19 14:24:00,208 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.37029934
2015-10-19 14:24:00,693 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:00,849 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.5301122
2015-10-19 14:24:01,427 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.4402952
2015-10-19 14:24:01,583 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.30972153
2015-10-19 14:24:01,833 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.3474061
2015-10-19 14:24:02,412 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.34742972
2015-10-19 14:24:02,412 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.23919508
2015-10-19 14:24:02,615 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:02,677 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:02,771 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.233708
2015-10-19 14:24:03,084 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.34743145
2015-10-19 14:24:03,334 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.3973329
2015-10-19 14:24:03,615 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.23922269
2015-10-19 14:24:03,646 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:03,787 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.23921506
2015-10-19 14:24:03,927 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.5637838
2015-10-19 14:24:04,740 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:05,052 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.33708018
2015-10-19 14:24:05,396 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.3474061
2015-10-19 14:24:05,459 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.23919508
2015-10-19 14:24:05,771 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:05,818 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:05,818 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.23922269
2015-10-19 14:24:05,849 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.4402952
2015-10-19 14:24:06,740 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.34743145
2015-10-19 14:24:06,880 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:06,927 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.34742972
2015-10-19 14:24:07,005 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.5637838
2015-10-19 14:24:07,115 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.44547126
2015-10-19 14:24:07,927 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:07,927 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.26314706
2015-10-19 14:24:08,099 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.24621491
2015-10-19 14:24:08,521 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.23919508
2015-10-19 14:24:08,630 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.3473985
2015-10-19 14:24:08,849 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:08,896 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.23922269
2015-10-19 14:24:08,974 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:09,162 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.3474061
2015-10-19 14:24:09,818 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.4402952
2015-10-19 14:24:10,021 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:10,052 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.61454743
2015-10-19 14:24:10,193 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.34743145
2015-10-19 14:24:10,615 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.455643
2015-10-19 14:24:10,724 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.61454743
2015-10-19 14:24:10,896 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.34742972
2015-10-19 14:24:11,068 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:11,615 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.29924428
2015-10-19 14:24:11,865 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.25695986
2015-10-19 14:24:11,881 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:11,896 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.259892
2015-10-19 14:24:11,990 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.2914812
2015-10-19 14:24:12,099 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:12,115 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.3473985
2015-10-19 14:24:12,584 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.3474061
2015-10-19 14:24:13,084 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.667
2015-10-19 14:24:13,162 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:13,396 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.4402952
2015-10-19 14:24:13,865 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.385035
2015-10-19 14:24:14,177 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:14,224 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.455643
2015-10-19 14:24:14,631 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.3474061
2015-10-19 14:24:14,709 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.34742972
2015-10-19 14:24:14,912 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:14,943 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.3474054
2015-10-19 14:24:15,224 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:15,537 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.3473985
2015-10-19 14:24:16,099 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.3474061
2015-10-19 14:24:16,256 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:16,256 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.27682886
2015-10-19 14:24:16,412 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.667
2015-10-19 14:24:16,959 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.32437596
2015-10-19 14:24:17,303 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:17,646 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.42110586
2015-10-19 14:24:17,678 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.3474061
2015-10-19 14:24:17,693 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.455643
2015-10-19 14:24:17,818 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.4402952
2015-10-19 14:24:17,990 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:17,990 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.3474054
2015-10-19 14:24:18,318 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:18,740 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.34742972
2015-10-19 14:24:18,896 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.3473985
2015-10-19 14:24:19,396 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:19,537 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.6673433
2015-10-19 14:24:19,584 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.3474061
2015-10-19 14:24:20,490 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:20,615 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.30711836
2015-10-19 14:24:20,756 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.3474061
2015-10-19 14:24:21,006 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.3474054
2015-10-19 14:24:21,021 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:21,068 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.3474054
2015-10-19 14:24:21,537 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:21,537 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.455643
2015-10-19 14:24:21,553 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.45169106
2015-10-19 14:24:21,662 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:24:21,662 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000016 to attempt_1445182159119_0002_m_000008_1
2015-10-19 14:24:21,662 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:6 RackLocal:8
2015-10-19 14:24:21,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:24:21,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000008_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:24:21,678 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000016 taskAttempt attempt_1445182159119_0002_m_000008_1
2015-10-19 14:24:21,678 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000008_1
2015-10-19 14:24:21,678 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:24:21,881 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000008_1 : 13562
2015-10-19 14:24:21,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000008_1] using containerId: [container_1445182159119_0002_01_000016 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:24:21,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000008_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:24:21,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000008
2015-10-19 14:24:22,287 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.4402952
2015-10-19 14:24:22,349 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.3473985
2015-10-19 14:24:22,584 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:22,584 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.36313272
2015-10-19 14:24:22,584 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.70266485
2015-10-19 14:24:22,646 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0002_m_000004
2015-10-19 14:24:22,646 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:24:22,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0002_m_000004
2015-10-19 14:24:22,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:24:22,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:24:22,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000004_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:24:22,693 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:6 RackLocal:8
2015-10-19 14:24:22,693 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:24:23,068 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.3474061
2015-10-19 14:24:23,615 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:23,787 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.4481362
2015-10-19 14:24:24,037 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.4395271
2015-10-19 14:24:24,084 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:24,506 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:24:24,553 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000016 asked for a task
2015-10-19 14:24:24,553 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000016 given task: attempt_1445182159119_0002_m_000008_1
2015-10-19 14:24:24,646 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:24,678 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.33724743
2015-10-19 14:24:25,021 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.4556257
2015-10-19 14:24:25,193 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.455643
2015-10-19 14:24:25,303 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.3474054
2015-10-19 14:24:25,615 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.73578286
2015-10-19 14:24:25,678 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:25,865 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.3473985
2015-10-19 14:24:26,506 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.3474061
2015-10-19 14:24:26,506 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.4402952
2015-10-19 14:24:26,662 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.38593215
2015-10-19 14:24:26,709 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:26,787 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.45561612
2015-10-19 14:24:27,037 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.45560944
2015-10-19 14:24:27,131 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:27,740 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:28,537 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.4556257
2015-10-19 14:24:28,646 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.455643
2015-10-19 14:24:28,678 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.7601977
2015-10-19 14:24:28,787 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:28,834 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.3474145
2015-10-19 14:24:29,381 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.3473985
2015-10-19 14:24:29,506 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.3474054
2015-10-19 14:24:29,818 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:29,818 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.45561612
2015-10-19 14:24:29,943 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.38750884
2015-10-19 14:24:30,053 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.45560944
2015-10-19 14:24:30,193 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:30,584 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.4402952
2015-10-19 14:24:30,662 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.4077544
2015-10-19 14:24:30,850 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:31,725 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.78016025
2015-10-19 14:24:31,865 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:32,053 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.13102318
2015-10-19 14:24:32,100 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.4556257
2015-10-19 14:24:32,209 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.455643
2015-10-19 14:24:32,803 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.3474145
2015-10-19 14:24:32,850 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.50663877
2015-10-19 14:24:32,912 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:32,943 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.3473985
2015-10-19 14:24:33,084 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.46702734
2015-10-19 14:24:33,256 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:33,318 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.3474054
2015-10-19 14:24:33,553 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.45355985
2015-10-19 14:24:33,943 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:34,272 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.46311492
2015-10-19 14:24:34,287 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.4272927
2015-10-19 14:24:34,772 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.81045246
2015-10-19 14:24:34,975 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:35,084 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.13102318
2015-10-19 14:24:35,537 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.4556257
2015-10-19 14:24:35,568 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.455643
2015-10-19 14:24:35,897 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.56381613
2015-10-19 14:24:36,006 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:36,147 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.56381226
2015-10-19 14:24:36,287 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:36,459 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.3473985
2015-10-19 14:24:36,928 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.3474145
2015-10-19 14:24:37,037 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:37,100 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.45561612
2015-10-19 14:24:37,256 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:24:37,256 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000017 to attempt_1445182159119_0002_m_000004_1
2015-10-19 14:24:37,256 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-19 14:24:37,256 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:24:37,256 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000004_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:24:37,256 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000017 taskAttempt attempt_1445182159119_0002_m_000004_1
2015-10-19 14:24:37,256 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000004_1
2015-10-19 14:24:37,256 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:24:37,443 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000004_1 : 13562
2015-10-19 14:24:37,443 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000004_1] using containerId: [container_1445182159119_0002_01_000017 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:24:37,443 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000004_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:24:37,443 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000004
2015-10-19 14:24:37,600 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.3474054
2015-10-19 14:24:37,678 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0002_m_000006
2015-10-19 14:24:37,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0002_m_000006
2015-10-19 14:24:37,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:24:37,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:24:37,678 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:24:37,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:24:37,818 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.8459163
2015-10-19 14:24:38,068 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:38,131 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.13102318
2015-10-19 14:24:38,287 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:8
2015-10-19 14:24:38,303 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:24:38,834 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.4556257
2015-10-19 14:24:38,834 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.46051326
2015-10-19 14:24:39,256 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.56381613
2015-10-19 14:24:39,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:24:39,334 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000018 to attempt_1445182159119_0002_m_000006_1
2015-10-19 14:24:39,334 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-19 14:24:39,334 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:24:39,334 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:24:39,334 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000018 taskAttempt attempt_1445182159119_0002_m_000006_1
2015-10-19 14:24:39,334 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000006_1
2015-10-19 14:24:39,334 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:24:39,381 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:39,381 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:39,475 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.56381226
2015-10-19 14:24:39,490 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000006_1 : 13562
2015-10-19 14:24:39,490 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000006_1] using containerId: [container_1445182159119_0002_01_000018 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:24:39,490 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:24:39,490 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000006
2015-10-19 14:24:39,506 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.4556279
2015-10-19 14:24:39,725 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.4973725
2015-10-19 14:24:39,819 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.40883267
2015-10-19 14:24:40,100 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:24:40,131 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000017 asked for a task
2015-10-19 14:24:40,131 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000017 given task: attempt_1445182159119_0002_m_000004_1
2015-10-19 14:24:40,287 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.45561612
2015-10-19 14:24:40,381 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:24:40,428 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:40,897 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.8796936
2015-10-19 14:24:41,162 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.22825627
2015-10-19 14:24:41,459 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.3474145
2015-10-19 14:24:41,475 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:41,850 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.3474054
2015-10-19 14:24:42,178 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.4816816
2015-10-19 14:24:42,178 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:24:42,178 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.4556257
2015-10-19 14:24:42,240 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000018 asked for a task
2015-10-19 14:24:42,240 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000018 given task: attempt_1445182159119_0002_m_000006_1
2015-10-19 14:24:42,319 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.56381613
2015-10-19 14:24:42,490 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:42,522 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:42,522 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.56381226
2015-10-19 14:24:43,287 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.4458581
2015-10-19 14:24:43,553 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.45561612
2015-10-19 14:24:45,256 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:45,256 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.566631
2015-10-19 14:24:45,256 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.23921506
2015-10-19 14:24:45,350 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.6563823
2015-10-19 14:24:45,444 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.56381226
2015-10-19 14:24:45,459 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.4960153
2015-10-19 14:24:45,522 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.4556257
2015-10-19 14:24:45,569 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:45,569 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.667
2015-10-19 14:24:45,850 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.6563823
2015-10-19 14:24:46,069 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.9145082
2015-10-19 14:24:46,100 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.3474145
2015-10-19 14:24:46,272 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:46,459 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.3474054
2015-10-19 14:24:46,631 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.45559394
2015-10-19 14:24:46,725 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.45565325
2015-10-19 14:24:46,944 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.45561612
2015-10-19 14:24:47,287 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:48,319 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.23921506
2015-10-19 14:24:48,319 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:48,334 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.13104042
2015-10-19 14:24:48,694 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.52532655
2015-10-19 14:24:48,850 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.4556257
2015-10-19 14:24:49,334 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.667
2015-10-19 14:24:49,522 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.667
2015-10-19 14:24:49,522 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:49,522 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:49,600 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.13101135
2015-10-19 14:24:49,850 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.45559394
2015-10-19 14:24:49,897 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.5773621
2015-10-19 14:24:50,209 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.45561612
2015-10-19 14:24:50,553 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:51,131 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.3474145
2015-10-19 14:24:51,241 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.3474054
2015-10-19 14:24:51,397 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.3474145
2015-10-19 14:24:51,412 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.13104042
2015-10-19 14:24:51,553 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.45565325
2015-10-19 14:24:51,616 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:51,819 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.53248954
2015-10-19 14:24:51,975 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.47940183
2015-10-19 14:24:52,412 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.667
2015-10-19 14:24:52,584 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.667
2015-10-19 14:24:52,631 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:52,678 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0002_m_000007
2015-10-19 14:24:52,678 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:24:52,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0002_m_000007
2015-10-19 14:24:52,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:24:52,694 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:24:52,694 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000007_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:24:52,694 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:52,694 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.13101135
2015-10-19 14:24:53,069 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.45559394
2015-10-19 14:24:53,459 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.45561612
2015-10-19 14:24:53,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-19 14:24:53,616 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:24:53,631 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 0.9692826
2015-10-19 14:24:53,709 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_1 is : 1.0
2015-10-19 14:24:53,725 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:53,756 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0002_m_000003_1
2015-10-19 14:24:53,756 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000003_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:24:53,756 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000013 taskAttempt attempt_1445182159119_0002_m_000003_1
2015-10-19 14:24:53,756 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000003_1
2015-10-19 14:24:53,756 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:24:54,163 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.5773621
2015-10-19 14:24:54,475 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.3474145
2015-10-19 14:24:54,475 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.23924798
2015-10-19 14:24:54,788 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:55,522 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.667
2015-10-19 14:24:55,553 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.5543138
2015-10-19 14:24:55,616 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.49568847
2015-10-19 14:24:55,631 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.6764411
2015-10-19 14:24:55,819 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.38430423
2015-10-19 14:24:55,819 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.23923388
2015-10-19 14:24:55,819 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.45565325
2015-10-19 14:24:55,819 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:55,819 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.3474145
2015-10-19 14:24:55,834 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:55,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000013
2015-10-19 14:24:55,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:24:55,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000003_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:24:55,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000019 to attempt_1445182159119_0002_m_000007_1
2015-10-19 14:24:55,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000003_1
2015-10-19 14:24:55,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-19 14:24:55,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0002_m_000003_0
2015-10-19 14:24:55,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:24:55,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000003_1: 
2015-10-19 14:24:55,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:24:55,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000007_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:24:55,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-19 14:24:55,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000003_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 14:24:55,850 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000019 taskAttempt attempt_1445182159119_0002_m_000007_1
2015-10-19 14:24:55,850 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000005 taskAttempt attempt_1445182159119_0002_m_000003_0
2015-10-19 14:24:55,850 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000007_1
2015-10-19 14:24:55,866 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000003_0
2015-10-19 14:24:55,866 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:24:55,866 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:24:56,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000003_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 14:24:56,788 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 14:24:56,897 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 14:24:56,897 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-19 14:24:56,959 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:24:57,116 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000003_0 is : 0.45559394
2015-10-19 14:24:57,209 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.45561612
2015-10-19 14:24:57,522 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.23924798
2015-10-19 14:24:57,522 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.3474145
2015-10-19 14:24:57,944 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:24:58,241 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.5773621
2015-10-19 14:24:58,428 INFO [Socket Reader #1 for port 49792] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 49792: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 14:24:58,584 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.7109114
2015-10-19 14:24:58,678 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.7262576
2015-10-19 14:24:58,866 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.23923388
2015-10-19 14:24:58,866 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.06666667
2015-10-19 14:24:58,975 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:24:59,006 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.5638263
2015-10-19 14:24:59,131 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.5152301
2015-10-19 14:24:59,522 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000007_1 : 13562
2015-10-19 14:24:59,522 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000007_1] using containerId: [container_1445182159119_0002_01_000019 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:24:59,522 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000007_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:24:59,538 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000007
2015-10-19 14:25:00,038 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:00,053 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.3474145
2015-10-19 14:25:00,053 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445182159119_0002_m_000003_0
2015-10-19 14:25:00,053 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000003_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 14:25:00,069 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.43269616
2015-10-19 14:25:00,225 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.45565325
2015-10-19 14:25:00,366 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000005
2015-10-19 14:25:00,366 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-19 14:25:00,366 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:25:00,553 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.45561612
2015-10-19 14:25:00,553 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.45562187
2015-10-19 14:25:00,553 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.23924798
2015-10-19 14:25:01,335 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:01,600 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.7493531
2015-10-19 14:25:01,710 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:25:01,772 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.77720976
2015-10-19 14:25:01,772 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000019 asked for a task
2015-10-19 14:25:01,772 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000019 given task: attempt_1445182159119_0002_m_000007_1
2015-10-19 14:25:01,897 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:01,897 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.23923388
2015-10-19 14:25:02,381 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:02,553 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.5638263
2015-10-19 14:25:02,741 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.5773621
2015-10-19 14:25:02,803 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.5621274
2015-10-19 14:25:03,413 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:03,600 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.45562187
2015-10-19 14:25:03,600 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.34742972
2015-10-19 14:25:03,944 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.45560944
2015-10-19 14:25:04,038 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.50207365
2015-10-19 14:25:04,194 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.45565325
2015-10-19 14:25:04,256 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.39765537
2015-10-19 14:25:04,444 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:04,631 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.7816144
2015-10-19 14:25:04,803 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.8253107
2015-10-19 14:25:04,960 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.343034
2015-10-19 14:25:04,960 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:05,460 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:05,991 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.5638263
2015-10-19 14:25:06,256 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.56380385
2015-10-19 14:25:06,475 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:06,631 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.5773621
2015-10-19 14:25:06,631 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.45562187
2015-10-19 14:25:06,631 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.34742972
2015-10-19 14:25:07,506 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:07,522 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.56147426
2015-10-19 14:25:07,678 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.8116337
2015-10-19 14:25:07,694 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0002_m_000009
2015-10-19 14:25:07,694 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:25:07,694 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0002_m_000009
2015-10-19 14:25:07,694 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:25:07,694 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:25:07,694 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000009_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:25:07,725 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:9 RackLocal:8
2015-10-19 14:25:07,725 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:25:07,819 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.8789347
2015-10-19 14:25:07,991 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:07,991 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.34743717
2015-10-19 14:25:08,413 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.45560944
2015-10-19 14:25:08,553 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:08,553 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.45565325
2015-10-19 14:25:08,928 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.44801432
2015-10-19 14:25:09,428 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.13103712
2015-10-19 14:25:09,428 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.5638263
2015-10-19 14:25:09,585 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:09,663 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.56384325
2015-10-19 14:25:09,678 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.34742972
2015-10-19 14:25:09,694 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.56380385
2015-10-19 14:25:10,600 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:10,741 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.8439948
2015-10-19 14:25:10,819 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.93248516
2015-10-19 14:25:10,882 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.56381613
2015-10-19 14:25:11,007 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.34743717
2015-10-19 14:25:11,053 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:11,241 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.5773621
2015-10-19 14:25:11,616 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:12,460 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.13103712
2015-10-19 14:25:12,632 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:12,694 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.56384325
2015-10-19 14:25:12,725 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.45565325
2015-10-19 14:25:12,897 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.5638263
2015-10-19 14:25:12,991 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.45565325
2015-10-19 14:25:13,069 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.56380385
2015-10-19 14:25:13,116 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.45560944
2015-10-19 14:25:13,257 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.45562187
2015-10-19 14:25:13,741 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:13,741 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.8764175
2015-10-19 14:25:13,835 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 0.9747123
2015-10-19 14:25:14,053 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.455643
2015-10-19 14:25:14,100 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:14,413 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.56381613
2015-10-19 14:25:14,772 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:15,522 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.23924637
2015-10-19 14:25:15,600 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.5773621
2015-10-19 14:25:15,725 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.56384325
2015-10-19 14:25:15,757 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.45565325
2015-10-19 14:25:15,788 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:16,335 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.5638263
2015-10-19 14:25:16,444 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.56380385
2015-10-19 14:25:16,788 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.91036266
2015-10-19 14:25:16,804 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:16,850 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 1.0
2015-10-19 14:25:17,100 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.455643
2015-10-19 14:25:17,147 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:17,257 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.45560944
2015-10-19 14:25:17,382 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.45562187
2015-10-19 14:25:17,429 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.46392885
2015-10-19 14:25:17,819 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:17,897 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.56381613
2015-10-19 14:25:18,413 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.56384325
2015-10-19 14:25:18,554 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.23924637
2015-10-19 14:25:18,788 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.667
2015-10-19 14:25:18,788 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.45565325
2015-10-19 14:25:18,835 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:19,491 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.5773621
2015-10-19 14:25:19,772 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.5638263
2015-10-19 14:25:19,804 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.95184946
2015-10-19 14:25:19,835 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:19,866 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.56380385
2015-10-19 14:25:19,866 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 1.0
2015-10-19 14:25:20,179 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:20,429 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.455643
2015-10-19 14:25:20,850 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:21,319 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.45560944
2015-10-19 14:25:21,522 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.56381613
2015-10-19 14:25:21,585 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.23924637
2015-10-19 14:25:21,616 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.45562187
2015-10-19 14:25:21,694 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.49536335
2015-10-19 14:25:21,819 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.667
2015-10-19 14:25:21,819 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.5638328
2015-10-19 14:25:21,850 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:22,835 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 0.9840064
2015-10-19 14:25:22,913 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:23,179 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.5914892
2015-10-19 14:25:23,210 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:23,241 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:25:23,241 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0002_01_000020 to attempt_1445182159119_0002_m_000009_1
2015-10-19 14:25:23,241 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:25:23,241 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:25:23,241 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000009_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:25:23,241 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0002_01_000020 taskAttempt attempt_1445182159119_0002_m_000009_1
2015-10-19 14:25:23,241 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0002_m_000009_1
2015-10-19 14:25:23,241 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:25:23,413 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0002_m_000009_1 : 13562
2015-10-19 14:25:23,413 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0002_m_000009_1] using containerId: [container_1445182159119_0002_01_000020 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:25:23,413 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000009_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:25:23,413 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0002_m_000009
2015-10-19 14:25:23,476 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.5638263
2015-10-19 14:25:23,569 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.5638263
2015-10-19 14:25:23,710 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.56380385
2015-10-19 14:25:23,944 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:24,288 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:25:24,663 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.34743145
2015-10-19 14:25:24,835 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.56381613
2015-10-19 14:25:24,897 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.5638328
2015-10-19 14:25:24,897 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.667
2015-10-19 14:25:24,976 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:25,429 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.45560944
2015-10-19 14:25:25,538 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.45562187
2015-10-19 14:25:25,632 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.5295564
2015-10-19 14:25:25,929 INFO [Socket Reader #1 for port 49792] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0002 (auth:SIMPLE)
2015-10-19 14:25:25,960 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 1.0
2015-10-19 14:25:25,991 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0002_m_000020 asked for a task
2015-10-19 14:25:25,991 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0002_m_000020 given task: attempt_1445182159119_0002_m_000009_1
2015-10-19 14:25:26,022 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:26,304 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:26,632 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.5638263
2015-10-19 14:25:27,116 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:27,257 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.59339464
2015-10-19 14:25:27,272 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.56380385
2015-10-19 14:25:27,382 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.619977
2015-10-19 14:25:27,741 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.34743145
2015-10-19 14:25:28,054 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.5638328
2015-10-19 14:25:28,069 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.6706347
2015-10-19 14:25:28,179 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:28,288 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.56381613
2015-10-19 14:25:29,022 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 1.0
2015-10-19 14:25:29,226 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:29,397 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:29,726 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.5638263
2015-10-19 14:25:29,804 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.45562187
2015-10-19 14:25:29,897 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.45560944
2015-10-19 14:25:30,179 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.5638328
2015-10-19 14:25:30,194 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.5638328
2015-10-19 14:25:30,288 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:30,538 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.622052
2015-10-19 14:25:30,569 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.56380385
2015-10-19 14:25:30,819 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.34743145
2015-10-19 14:25:31,351 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.56381613
2015-10-19 14:25:31,413 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.667
2015-10-19 14:25:31,413 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.69686514
2015-10-19 14:25:31,523 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.667
2015-10-19 14:25:31,538 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.667
2015-10-19 14:25:31,601 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:32,476 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:32,663 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:32,788 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.6129918
2015-10-19 14:25:33,023 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.16604526
2015-10-19 14:25:33,460 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.6129918
2015-10-19 14:25:33,694 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:33,741 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.63899046
2015-10-19 14:25:33,804 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.5754799
2015-10-19 14:25:33,882 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.39301616
2015-10-19 14:25:34,226 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_0 is : 0.45560944
2015-10-19 14:25:34,273 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.45562187
2015-10-19 14:25:34,444 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.5648164
2015-10-19 14:25:34,476 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.7278291
2015-10-19 14:25:34,476 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.667
2015-10-19 14:25:34,569 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000005_1 is : 1.0
2015-10-19 14:25:34,632 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0002_m_000005_1
2015-10-19 14:25:34,632 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000005_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:25:34,632 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000014 taskAttempt attempt_1445182159119_0002_m_000005_1
2015-10-19 14:25:34,648 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000005_1
2015-10-19 14:25:34,648 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:25:34,757 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:34,757 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.5638328
2015-10-19 14:25:34,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000005_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:25:34,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000005_1
2015-10-19 14:25:34,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0002_m_000005_0
2015-10-19 14:25:34,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:25:34,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-19 14:25:34,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000005_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 14:25:34,882 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000007 taskAttempt attempt_1445182159119_0002_m_000005_0
2015-10-19 14:25:34,882 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000005_0
2015-10-19 14:25:34,882 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:25:35,023 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0002_m_000005
2015-10-19 14:25:35,023 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:25:35,554 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:35,819 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 14:25:35,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:14 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:25:35,866 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.667
2015-10-19 14:25:35,898 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000005_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 14:25:35,913 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000014
2015-10-19 14:25:35,913 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:25:35,913 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000005_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:25:35,944 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 14:25:36,069 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.667
2015-10-19 14:25:36,101 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.16604526
2015-10-19 14:25:36,132 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445182159119_0002_m_000005_0
2015-10-19 14:25:36,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000005_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 14:25:36,882 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 14:25:36,960 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.6552736
2015-10-19 14:25:36,960 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.4556257
2015-10-19 14:25:37,054 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.58362186
2015-10-19 14:25:37,835 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.7626699
2015-10-19 14:25:37,835 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.59632564
2015-10-19 14:25:37,851 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.667
2015-10-19 14:25:37,929 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 14:25:38,570 INFO [Socket Reader #1 for port 49792] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 49792: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 14:25:38,601 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:38,710 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.45562187
2015-10-19 14:25:38,898 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.5638328
2015-10-19 14:25:38,945 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.667
2015-10-19 14:25:38,976 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 14:25:39,132 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.16604526
2015-10-19 14:25:39,148 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.6552736
2015-10-19 14:25:40,038 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 14:25:40,038 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.4556257
2015-10-19 14:25:40,101 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000007
2015-10-19 14:25:40,101 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:25:40,101 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:25:40,148 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.667
2015-10-19 14:25:40,273 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.5927426
2015-10-19 14:25:40,351 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.667
2015-10-19 14:25:40,882 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.78931785
2015-10-19 14:25:40,913 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.667
2015-10-19 14:25:40,976 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.6285669
2015-10-19 14:25:41,054 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 14:25:41,663 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:41,991 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.667
2015-10-19 14:25:42,101 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 14:25:42,179 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.16604526
2015-10-19 14:25:43,116 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.4556257
2015-10-19 14:25:43,382 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.45562187
2015-10-19 14:25:43,445 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.5638328
2015-10-19 14:25:43,445 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 14:25:43,554 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.667
2015-10-19 14:25:43,710 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.6021882
2015-10-19 14:25:43,960 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.80948037
2015-10-19 14:25:43,991 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.6807813
2015-10-19 14:25:44,257 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.6663917
2015-10-19 14:25:44,476 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 14:25:44,507 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.6663917
2015-10-19 14:25:44,679 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.667
2015-10-19 14:25:44,710 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:45,054 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.667
2015-10-19 14:25:45,476 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 14:25:46,163 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.4556257
2015-10-19 14:25:46,460 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_1 is : 1.0
2015-10-19 14:25:46,507 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0002_m_000002_1
2015-10-19 14:25:46,507 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000002_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:25:46,507 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000015 taskAttempt attempt_1445182159119_0002_m_000002_1
2015-10-19 14:25:46,507 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000002_1
2015-10-19 14:25:46,507 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 14:25:46,507 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:25:46,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000002_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:25:46,695 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000002_1
2015-10-19 14:25:46,695 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0002_m_000002_0
2015-10-19 14:25:46,695 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:25:46,695 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-19 14:25:46,695 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000002_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 14:25:46,695 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000004 taskAttempt attempt_1445182159119_0002_m_000002_0
2015-10-19 14:25:46,695 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000002_0
2015-10-19 14:25:46,695 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:25:46,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:25:46,851 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.667
2015-10-19 14:25:46,976 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.62009954
2015-10-19 14:25:47,101 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000002_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 14:25:47,148 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 14:25:47,460 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445182159119_0002_m_000002_0
2015-10-19 14:25:47,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000002_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 14:25:47,492 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000002_0 is : 0.667
2015-10-19 14:25:47,523 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 14:25:47,663 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.5638328
2015-10-19 14:25:47,663 INFO [Socket Reader #1 for port 49792] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 49792: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 14:25:47,710 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.4953706
2015-10-19 14:25:47,788 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:47,913 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.82722616
2015-10-19 14:25:47,929 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.6978073
2015-10-19 14:25:48,085 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.68102735
2015-10-19 14:25:48,554 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:48,554 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.3031575
2015-10-19 14:25:48,867 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000015
2015-10-19 14:25:48,867 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000004
2015-10-19 14:25:48,867 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000002_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:25:48,867 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:25:48,867 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:25:49,085 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.667
2015-10-19 14:25:49,195 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.56380385
2015-10-19 14:25:49,585 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:49,960 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.667
2015-10-19 14:25:50,085 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.63573426
2015-10-19 14:25:50,601 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:50,835 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.10000001
2015-10-19 14:25:50,976 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.8588652
2015-10-19 14:25:50,976 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.7286521
2015-10-19 14:25:51,132 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.7023026
2015-10-19 14:25:51,601 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.3031575
2015-10-19 14:25:51,617 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:52,210 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.56384325
2015-10-19 14:25:52,226 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.5638328
2015-10-19 14:25:52,273 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.56380385
2015-10-19 14:25:52,663 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:53,226 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.667
2015-10-19 14:25:53,289 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.6451797
2015-10-19 14:25:53,695 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:53,726 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.667
2015-10-19 14:25:53,882 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.13333334
2015-10-19 14:25:54,039 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.76674813
2015-10-19 14:25:54,039 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.89749455
2015-10-19 14:25:54,195 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.7321278
2015-10-19 14:25:54,648 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.3031575
2015-10-19 14:25:54,726 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:55,335 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.56380385
2015-10-19 14:25:56,539 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.667
2015-10-19 14:25:56,617 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.56384325
2015-10-19 14:25:56,632 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:56,710 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.6572264
2015-10-19 14:25:56,960 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.16666667
2015-10-19 14:25:57,070 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.935946
2015-10-19 14:25:57,101 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.80448174
2015-10-19 14:25:57,164 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.5638328
2015-10-19 14:25:57,242 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.76628196
2015-10-19 14:25:57,679 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:57,679 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.3845082
2015-10-19 14:25:58,398 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.64428216
2015-10-19 14:25:58,601 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.667
2015-10-19 14:25:58,742 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:59,789 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:59,867 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.667
2015-10-19 14:26:00,023 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.16666667
2015-10-19 14:26:00,039 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.66146046
2015-10-19 14:26:00,070 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.64428216
2015-10-19 14:26:00,117 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.9564058
2015-10-19 14:26:00,211 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.82444584
2015-10-19 14:26:00,320 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.785896
2015-10-19 14:26:00,742 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.4402952
2015-10-19 14:26:00,804 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:26:01,007 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.56384325
2015-10-19 14:26:01,476 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.667
2015-10-19 14:26:01,836 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:26:01,836 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.5638328
2015-10-19 14:26:02,164 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.66146046
2015-10-19 14:26:02,789 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.667
2015-10-19 14:26:02,867 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:26:03,117 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.16666667
2015-10-19 14:26:03,179 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 0.9777678
2015-10-19 14:26:03,257 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.84629613
2015-10-19 14:26:03,414 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.667
2015-10-19 14:26:03,414 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.80888194
2015-10-19 14:26:03,632 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.667
2015-10-19 14:26:03,804 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.4402952
2015-10-19 14:26:03,898 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:26:04,554 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.667
2015-10-19 14:26:04,945 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:26:05,023 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_1 is : 1.0
2015-10-19 14:26:05,101 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0002_m_000008_1
2015-10-19 14:26:05,101 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000008_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:26:05,101 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000016 taskAttempt attempt_1445182159119_0002_m_000008_1
2015-10-19 14:26:05,101 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000008_1
2015-10-19 14:26:05,101 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:26:05,320 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000008_0 is : 0.56384325
2015-10-19 14:26:05,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000008_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:26:05,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000008_1
2015-10-19 14:26:05,382 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0002_m_000008_0
2015-10-19 14:26:05,382 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:26:05,382 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-19 14:26:05,382 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000008_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 14:26:05,382 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000010 taskAttempt attempt_1445182159119_0002_m_000008_0
2015-10-19 14:26:05,382 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000008_0
2015-10-19 14:26:05,382 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:26:05,492 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0002_m_000008
2015-10-19 14:26:05,492 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:26:05,992 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:26:06,117 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.5638328
2015-10-19 14:26:06,164 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.16666667
2015-10-19 14:26:06,164 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:26:06,336 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000008_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 14:26:06,398 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.8826803
2015-10-19 14:26:06,445 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.846275
2015-10-19 14:26:06,554 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 14:26:06,726 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445182159119_0002_m_000008_0
2015-10-19 14:26:06,726 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000008_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 14:26:06,836 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.667
2015-10-19 14:26:06,836 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.4402952
2015-10-19 14:26:07,023 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:26:07,070 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.6671132
2015-10-19 14:26:07,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000016
2015-10-19 14:26:07,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:26:07,289 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000008_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:26:07,414 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.667
2015-10-19 14:26:08,523 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.667
2015-10-19 14:26:08,929 INFO [IPC Server handler 2 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:26:09,211 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.16666667
2015-10-19 14:26:09,461 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.9183992
2015-10-19 14:26:09,461 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.5839501
2015-10-19 14:26:09,508 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.875849
2015-10-19 14:26:09,867 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.4843121
2015-10-19 14:26:09,961 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:26:10,336 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.6951402
2015-10-19 14:26:10,383 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.6782121
2015-10-19 14:26:10,836 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.667
2015-10-19 14:26:10,992 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:26:11,586 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.6693041
2015-10-19 14:26:12,008 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:26:12,273 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.20000002
2015-10-19 14:26:12,508 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.9406049
2015-10-19 14:26:12,570 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.8986322
2015-10-19 14:26:12,898 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.5773621
2015-10-19 14:26:13,023 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:26:13,461 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.5920907
2015-10-19 14:26:13,679 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.7303487
2015-10-19 14:26:13,914 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.6908905
2015-10-19 14:26:14,070 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:26:14,304 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.667
2015-10-19 14:26:14,633 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.6961676
2015-10-19 14:26:15,039 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000010
2015-10-19 14:26:15,039 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:26:15,039 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:26:15,117 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:26:15,336 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.20000002
2015-10-19 14:26:15,586 INFO [IPC Server handler 28 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.97128785
2015-10-19 14:26:15,633 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.9302417
2015-10-19 14:26:15,961 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.5773621
2015-10-19 14:26:16,180 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:26:16,617 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.6087018
2015-10-19 14:26:17,148 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.765214
2015-10-19 14:26:17,211 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:26:17,445 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.70431376
2015-10-19 14:26:17,695 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.7238686
2015-10-19 14:26:17,711 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.667
2015-10-19 14:26:18,633 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 0.9952771
2015-10-19 14:26:18,695 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.9542718
2015-10-19 14:26:18,976 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.5773621
2015-10-19 14:26:19,133 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:26:19,133 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.20000002
2015-10-19 14:26:19,383 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_1 is : 1.0
2015-10-19 14:26:19,430 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0002_m_000004_1
2015-10-19 14:26:19,430 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000004_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:26:19,430 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000017 taskAttempt attempt_1445182159119_0002_m_000004_1
2015-10-19 14:26:19,430 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000004_1
2015-10-19 14:26:19,430 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:26:19,586 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000004_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:26:19,586 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000004_1
2015-10-19 14:26:19,586 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0002_m_000004_0
2015-10-19 14:26:19,586 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:26:19,586 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-19 14:26:19,586 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000004_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 14:26:19,586 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000006 taskAttempt attempt_1445182159119_0002_m_000004_0
2015-10-19 14:26:19,586 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000004_0
2015-10-19 14:26:19,586 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:26:19,586 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:26:20,164 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:26:20,242 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000004_0 is : 0.6376849
2015-10-19 14:26:20,367 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.5773621
2015-10-19 14:26:20,508 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0002_m_000004
2015-10-19 14:26:20,508 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:26:20,617 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000017
2015-10-19 14:26:20,617 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:26:20,617 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000004_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:26:20,680 INFO [IPC Server handler 0 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.71650386
2015-10-19 14:26:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000004_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 14:26:20,711 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 14:26:20,742 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445182159119_0002_m_000004_0
2015-10-19 14:26:20,742 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000004_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 14:26:20,773 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.7484795
2015-10-19 14:26:20,773 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.7952013
2015-10-19 14:26:21,023 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.667
2015-10-19 14:26:21,195 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:21,742 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 0.98457456
2015-10-19 14:26:21,898 INFO [Socket Reader #1 for port 49792] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 49792: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 14:26:22,039 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.667
2015-10-19 14:26:22,195 INFO [IPC Server handler 16 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.20000002
2015-10-19 14:26:22,242 INFO [IPC Server handler 4 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:22,930 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_1 is : 1.0
2015-10-19 14:26:22,977 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0002_m_000006_1
2015-10-19 14:26:22,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000006_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:26:22,977 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000018 taskAttempt attempt_1445182159119_0002_m_000006_1
2015-10-19 14:26:22,977 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000006_1
2015-10-19 14:26:22,977 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:26:23,211 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000006_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:26:23,211 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000006_1
2015-10-19 14:26:23,211 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0002_m_000006_0
2015-10-19 14:26:23,227 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:26:23,227 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-19 14:26:23,227 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000006_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 14:26:23,227 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000008 taskAttempt attempt_1445182159119_0002_m_000006_0
2015-10-19 14:26:23,227 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000006_0
2015-10-19 14:26:23,227 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:26:23,336 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:23,602 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000006_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 14:26:23,602 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 14:26:23,648 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445182159119_0002_m_000006_0
2015-10-19 14:26:23,648 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000006_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 14:26:23,742 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:26:23,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000006
2015-10-19 14:26:23,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:26:23,789 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:26:23,883 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.788748
2015-10-19 14:26:24,055 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000006_0 is : 0.72890955
2015-10-19 14:26:24,273 INFO [IPC Server handler 1 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.82539535
2015-10-19 14:26:24,320 INFO [Socket Reader #1 for port 49792] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 49792: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 14:26:24,383 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 14:26:24,617 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.667
2015-10-19 14:26:24,852 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000008
2015-10-19 14:26:24,852 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000018
2015-10-19 14:26:24,852 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:26:24,852 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:26:24,852 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000006_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:26:25,086 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.667
2015-10-19 14:26:25,305 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.23333333
2015-10-19 14:26:25,414 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 14:26:26,461 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 14:26:26,914 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.8292821
2015-10-19 14:26:27,508 INFO [IPC Server handler 21 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 14:26:27,836 INFO [IPC Server handler 22 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.667
2015-10-19 14:26:27,914 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.8601805
2015-10-19 14:26:28,117 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.6670674
2015-10-19 14:26:28,352 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.26666668
2015-10-19 14:26:28,539 INFO [IPC Server handler 29 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 14:26:28,867 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_0 is : 0.667
2015-10-19 14:26:28,977 FATAL [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Task: attempt_1445182159119_0002_m_000007_0 - exited : org.apache.hadoop.util.DiskChecker$DiskErrorException: Could not find any valid local directory for output/attempt_1445182159119_0002_m_000007_0/file.out
	at org.apache.hadoop.fs.LocalDirAllocator$AllocatorPerContext.getLocalPathForWrite(LocalDirAllocator.java:402)
	at org.apache.hadoop.fs.LocalDirAllocator.getLocalPathForWrite(LocalDirAllocator.java:150)
	at org.apache.hadoop.fs.LocalDirAllocator.getLocalPathForWrite(LocalDirAllocator.java:131)
	at org.apache.hadoop.mapred.YarnOutputFiles.getOutputFileForWrite(YarnOutputFiles.java:84)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.mergeParts(MapTask.java:1833)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.flush(MapTask.java:1504)
	at org.apache.hadoop.mapred.MapTask$NewOutputCollector.close(MapTask.java:720)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:790)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)

2015-10-19 14:26:28,977 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Diagnostics report from attempt_1445182159119_0002_m_000007_0: Error: org.apache.hadoop.util.DiskChecker$DiskErrorException: Could not find any valid local directory for output/attempt_1445182159119_0002_m_000007_0/file.out
	at org.apache.hadoop.fs.LocalDirAllocator$AllocatorPerContext.getLocalPathForWrite(LocalDirAllocator.java:402)
	at org.apache.hadoop.fs.LocalDirAllocator.getLocalPathForWrite(LocalDirAllocator.java:150)
	at org.apache.hadoop.fs.LocalDirAllocator.getLocalPathForWrite(LocalDirAllocator.java:131)
	at org.apache.hadoop.mapred.YarnOutputFiles.getOutputFileForWrite(YarnOutputFiles.java:84)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.mergeParts(MapTask.java:1833)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.flush(MapTask.java:1504)
	at org.apache.hadoop.mapred.MapTask$NewOutputCollector.close(MapTask.java:720)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:790)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)

2015-10-19 14:26:28,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000007_0: Error: org.apache.hadoop.util.DiskChecker$DiskErrorException: Could not find any valid local directory for output/attempt_1445182159119_0002_m_000007_0/file.out
	at org.apache.hadoop.fs.LocalDirAllocator$AllocatorPerContext.getLocalPathForWrite(LocalDirAllocator.java:402)
	at org.apache.hadoop.fs.LocalDirAllocator.getLocalPathForWrite(LocalDirAllocator.java:150)
	at org.apache.hadoop.fs.LocalDirAllocator.getLocalPathForWrite(LocalDirAllocator.java:131)
	at org.apache.hadoop.mapred.YarnOutputFiles.getOutputFileForWrite(YarnOutputFiles.java:84)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.mergeParts(MapTask.java:1833)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.flush(MapTask.java:1504)
	at org.apache.hadoop.mapred.MapTask$NewOutputCollector.close(MapTask.java:720)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:790)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)

2015-10-19 14:26:28,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000007_0 TaskAttempt Transitioned from RUNNING to FAIL_CONTAINER_CLEANUP
2015-10-19 14:26:28,977 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000009 taskAttempt attempt_1445182159119_0002_m_000007_0
2015-10-19 14:26:28,977 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000007_0
2015-10-19 14:26:28,977 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:26:29,164 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000007_0 TaskAttempt Transitioned from FAIL_CONTAINER_CLEANUP to FAIL_TASK_CLEANUP
2015-10-19 14:26:29,164 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 14:26:29,180 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445182159119_0002_m_000007_0
2015-10-19 14:26:29,180 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000007_0 TaskAttempt Transitioned from FAIL_TASK_CLEANUP to FAILED
2015-10-19 14:26:29,195 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: 1 failures on node 04DN8IQ.fareast.corp.microsoft.com
2015-10-19 14:26:29,570 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 14:26:29,961 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.86576575
2015-10-19 14:26:30,586 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:26:31,149 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.7270379
2015-10-19 14:26:31,164 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000009
2015-10-19 14:26:31,164 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:26:31,164 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:26:31,383 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.26666668
2015-10-19 14:26:31,586 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.88753957
2015-10-19 14:26:31,617 INFO [IPC Server handler 24 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:26:32,930 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:26:33,008 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.88476634
2015-10-19 14:26:33,945 INFO [IPC Server handler 10 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:26:34,180 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.784527
2015-10-19 14:26:34,414 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.26666668
2015-10-19 14:26:34,961 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:26:35,977 INFO [IPC Server handler 19 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:26:36,055 INFO [IPC Server handler 26 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.9112638
2015-10-19 14:26:36,055 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.9157617
2015-10-19 14:26:36,992 INFO [IPC Server handler 13 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:26:37,195 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.8505682
2015-10-19 14:26:37,446 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.26666668
2015-10-19 14:26:38,305 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:26:39,086 INFO [IPC Server handler 5 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.9528328
2015-10-19 14:26:39,321 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:26:40,211 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.9182308
2015-10-19 14:26:40,336 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:26:40,477 INFO [IPC Server handler 12 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.26666668
2015-10-19 14:26:40,633 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.9241921
2015-10-19 14:26:41,367 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:26:42,133 INFO [IPC Server handler 17 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 0.98883355
2015-10-19 14:26:42,399 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:26:43,227 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000007_1 is : 1.0
2015-10-19 14:26:43,289 INFO [IPC Server handler 25 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0002_m_000007_1
2015-10-19 14:26:43,289 INFO [IPC Server handler 9 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 0.98230094
2015-10-19 14:26:43,289 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000007_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:26:43,289 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000019 taskAttempt attempt_1445182159119_0002_m_000007_1
2015-10-19 14:26:43,289 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000007_1
2015-10-19 14:26:43,289 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:26:43,430 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:26:43,430 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000007_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:26:43,430 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000007_1
2015-10-19 14:26:43,430 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:26:43,430 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-19 14:26:43,539 INFO [IPC Server handler 20 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.26666668
2015-10-19 14:26:44,414 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:26:44,430 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 14:26:44,946 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_1 is : 1.0
2015-10-19 14:26:44,961 INFO [IPC Server handler 27 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0002_m_000009_1
2015-10-19 14:26:44,961 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000009_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:26:44,961 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000020 taskAttempt attempt_1445182159119_0002_m_000009_1
2015-10-19 14:26:44,961 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000009_1
2015-10-19 14:26:44,961 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:26:44,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000009_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:26:44,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0002_m_000009_1
2015-10-19 14:26:44,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0002_m_000009_0
2015-10-19 14:26:44,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0002_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:26:44,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-19 14:26:44,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000009_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 14:26:44,977 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0002_01_000011 taskAttempt attempt_1445182159119_0002_m_000009_0
2015-10-19 14:26:44,977 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0002_m_000009_0
2015-10-19 14:26:44,977 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:26:45,414 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:26:45,414 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000019
2015-10-19 14:26:45,414 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:26:45,414 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000007_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:26:45,430 INFO [IPC Server handler 6 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 14:26:45,789 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000009_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 14:26:45,789 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 14:26:45,789 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out2/_temporary/1/_temporary/attempt_1445182159119_0002_m_000009_0
2015-10-19 14:26:45,789 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0002_m_000009_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 14:26:45,899 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_m_000009_0 is : 0.9393731
2015-10-19 14:26:46,430 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000020
2015-10-19 14:26:46,430 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:26:46,430 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000009_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:26:46,446 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 14:26:46,586 INFO [IPC Server handler 23 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.26666668
2015-10-19 14:26:47,446 INFO [IPC Server handler 18 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 14:26:47,977 INFO [Socket Reader #1 for port 49792] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 49792: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 14:26:48,446 INFO [IPC Server handler 3 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 14:26:49,493 INFO [IPC Server handler 14 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0002_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 14:26:49,649 INFO [IPC Server handler 8 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.26666668
2015-10-19 14:26:49,883 INFO [IPC Server handler 15 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.26666668
2015-10-19 14:26:49,914 INFO [IPC Server handler 7 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.26666668
2015-10-19 14:26:50,571 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0002_01_000011
2015-10-19 14:26:50,571 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:19 ContRel:0 HostLocal:10 RackLocal:8
2015-10-19 14:26:50,571 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0002_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:26:52,696 INFO [IPC Server handler 11 on 49792] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0002_r_000000_0 is : 0.66992116
