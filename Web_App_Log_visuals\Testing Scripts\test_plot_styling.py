"""
Test script to verify the improved plot styling and color contrast
"""

import sys
import os

# Add the Core Application directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Core Application'))

try:
    from well_log_app import (
        create_gamma_ray_track,
        create_resistivity_track,
        create_density_neutron_track,
        create_density_neutron_crossplot
    )
    print("✅ Successfully imported plotting functions")
except ImportError as e:
    print(f"❌ Failed to import functions: {e}")
    sys.exit(1)

import pandas as pd
import numpy as np

def create_test_data():
    """Create sample well log data for testing"""
    np.random.seed(42)
    n_points = 100

    depth = np.linspace(1500, 1600, n_points)
    gr = 50 + 30 * np.random.randn(n_points)
    rdep = np.exp(2 + 0.5 * np.random.randn(n_points))  # Log-normal distribution
    rhob = 2.3 + 0.2 * np.random.randn(n_points)
    nphi = 0.15 + 0.05 * np.random.randn(n_points)

    # Add some lithology data
    lithologies = ['Sandstone', 'Shale', 'Limestone']
    lith = np.random.choice(lithologies, n_points)

    df = pd.DataFrame({
        'DEPTH_MD': depth,
        'GR': np.clip(gr, 0, 200),
        'RDEP': np.clip(rdep, 0.1, 1000),
        'RHOB': np.clip(rhob, 1.8, 2.8),
        'NPHI': np.clip(nphi, 0.05, 0.45),
        'LITH': lith
    })

    return df

def test_plot_styling():
    """Test the improved plot styling"""
    print("\n🎨 Testing Improved Plot Styling")
    print("=" * 50)

    # Create test data
    df = create_test_data()
    print(f"✅ Created test data: {df.shape}")

    # Test each plotting function
    try:
        # Test Gamma Ray plot
        print("📊 Testing Gamma Ray plot...")
        fig_gr = create_gamma_ray_track(df, color_by_lithology=False)
        print("   ✅ Standard GR plot created")

        fig_gr_lith = create_gamma_ray_track(df, color_by_lithology=True)
        print("   ✅ Lithology-colored GR plot created")

        # Test Resistivity plot
        print("📊 Testing Resistivity plot...")
        fig_res = create_resistivity_track(df, color_by_lithology=False)
        print("   ✅ Standard resistivity plot created")

        fig_res_lith = create_resistivity_track(df, color_by_lithology=True)
        print("   ✅ Lithology-colored resistivity plot created")

        # Test Density-Neutron plot
        print("📊 Testing Density-Neutron plot...")
        fig_dn = create_density_neutron_track(df)
        print("   ✅ Density-Neutron track created")

        # Test Crossplot
        print("📊 Testing Crossplot...")
        fig_cross_gr = create_density_neutron_crossplot(df, color_by='GR')
        print("   ✅ GR-colored crossplot created")

        fig_cross_lith = create_density_neutron_crossplot(df, color_by='LITH')
        print("   ✅ Lithology-colored crossplot created")

        print("\n🎉 All plot styling tests passed!")
        return True

    except Exception as e:
        print(f"❌ Error in plot styling test: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_styling_improvements():
    """Verify the specific styling improvements"""
    print("\n🔍 Verifying Styling Improvements")
    print("=" * 50)

    df = create_test_data()

    # Create a sample plot to check styling
    fig = create_gamma_ray_track(df, color_by_lithology=False)

    # Check layout properties
    layout = fig.layout

    improvements = []

    # Check background colors
    if layout.plot_bgcolor == '#f8f9fa':
        improvements.append("✅ Plot background: Light gray (#f8f9fa)")
    else:
        improvements.append(f"❌ Plot background: {layout.plot_bgcolor}")

    if layout.paper_bgcolor == '#ffffff':
        improvements.append("✅ Paper background: White (#ffffff)")
    else:
        improvements.append(f"❌ Paper background: {layout.paper_bgcolor}")

    # Check title styling
    if hasattr(layout.title, 'font') and layout.title.font.color == '#2c3e50':
        improvements.append("✅ Title color: Dark blue-gray (#2c3e50)")
    else:
        improvements.append("❌ Title color not properly set")

    # Check grid colors
    if layout.xaxis.gridcolor == '#e1e8ed':
        improvements.append("✅ Grid color: Light blue-gray (#e1e8ed)")
    else:
        improvements.append(f"❌ Grid color: {layout.xaxis.gridcolor}")

    # Check axis text colors
    if hasattr(layout.xaxis, 'tickfont') and layout.xaxis.tickfont.color == '#2c3e50':
        improvements.append("✅ Axis text color: Dark blue-gray (#2c3e50)")
    else:
        improvements.append("❌ Axis text color not properly set")

    print("\n📋 Styling Verification Results:")
    for improvement in improvements:
        print(f"   {improvement}")

    passed = sum(1 for imp in improvements if imp.startswith("✅"))
    total = len(improvements)

    print(f"\n📊 Styling Score: {passed}/{total} improvements verified")

    return passed == total

def main():
    """Main test function"""
    print("🎨 PLOT STYLING IMPROVEMENT TEST")
    print("=" * 60)

    # Test plot creation
    plot_test = test_plot_styling()

    # Verify styling improvements
    styling_test = verify_styling_improvements()

    print(f"\n📊 TEST RESULTS SUMMARY:")
    print("=" * 40)

    print(f"✅ Plot Creation: {'PASS' if plot_test else 'FAIL'}")
    print(f"✅ Styling Verification: {'PASS' if styling_test else 'FAIL'}")

    if plot_test and styling_test:
        print(f"\n🎉 ALL STYLING TESTS PASSED!")
        print(f"🎨 The plots now have:")
        print(f"   • Better background contrast (#f8f9fa vs white)")
        print(f"   • Improved text visibility (#2c3e50 dark text)")
        print(f"   • Enhanced grid lines (#e1e8ed light gray)")
        print(f"   • Professional color scheme")
        print(f"   • Thicker, more visible plot lines")
        print(f"\n🚀 The app is ready with improved visual clarity!")
    else:
        print(f"\n⚠️ Some styling tests failed - check the errors above")

    return plot_test and styling_test

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
