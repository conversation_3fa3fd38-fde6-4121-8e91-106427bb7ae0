"""
Results for VAR miscellaneous tests

Results from Stata using script `test_var_misc_stata.do`.

Data from:

http://www.jmulti.de/download/datasets/e1.dat

Author: <PERSON>
License: Simplified-BSD
"""

# See http://www.jmulti.de/download/datasets/e1.dat
# 1960:Q1 - 1982Q4
lutkepohl_data = [
    [180, 451, 415], [179, 465, 421], [185, 485, 434], [192, 493, 448],
    [211, 509, 459], [202, 520, 458], [207, 521, 479], [214, 540, 487],
    [231, 548, 497], [229, 558, 510], [234, 574, 516], [237, 583, 525],
    [206, 591, 529], [250, 599, 538], [259, 610, 546], [263, 627, 555],
    [264, 642, 574], [280, 653, 574], [282, 660, 586], [292, 694, 602],
    [286, 709, 617], [302, 734, 639], [304, 751, 653], [307, 763, 668],
    [317, 766, 679], [314, 779, 686], [306, 808, 697], [304, 785, 688],
    [292, 794, 704], [275, 799, 699], [273, 799, 709], [301, 812, 715],
    [280, 837, 724], [289, 853, 746], [303, 876, 758], [322, 897, 779],
    [315, 922, 798], [339, 949, 816], [364, 979, 837], [371, 988, 858],
    [375, 1025, 881], [432, 1063, 905], [453, 1104, 934], [460, 1131, 968],
    [475, 1137, 983], [496, 1178, 1013], [494, 1211, 1034], [498, 1256, 1064],
    [526, 1290, 1101], [519, 1314, 1102], [516, 1346, 1145], [531, 1385, 1173],
    [573, 1416, 1216], [551, 1436, 1229], [538, 1462, 1242], [532, 1493, 1267],
    [558, 1516, 1295], [524, 1557, 1317], [525, 1613, 1355], [519, 1642, 1371],
    [526, 1690, 1402], [510, 1759, 1452], [519, 1756, 1485], [538, 1780, 1516],
    [549, 1807, 1549], [570, 1831, 1567], [559, 1873, 1588], [584, 1897, 1631],
    [611, 1910, 1650], [597, 1943, 1685], [603, 1976, 1722], [619, 2018, 1752],
    [635, 2040, 1774], [658, 2070, 1807], [675, 2121, 1831], [700, 2132, 1842],
    [692, 2199, 1890], [759, 2253, 1958], [782, 2276, 1948], [816, 2318, 1994],
    [844, 2369, 2061], [830, 2423, 2056], [853, 2457, 2102], [852, 2470, 2121],
    [833, 2521, 2145], [860, 2545, 2164], [870, 2580, 2206], [830, 2620, 2225],
    [801, 2639, 2235], [824, 2618, 2237], [831, 2628, 2250], [830, 2651, 2271],
]

lutkepohl_ar1 = {
    # intercept, AR, sigma^2
    'params': [.02193629, -.19639897, .0020982049304637],
    'loglike': 123.1654530510318,
    'aic': -3.274741974352211,
    'bic': -3.212469944806153,
    'hqic': -3.249900896155949,
    'fpe': .0022147718710451,
    'estat_aic': -242.33091,
    'estat_bic': -237.72278
}

lutkepohl_ar1_lustats = {
    # intercept, AR, sigma^2
    'params': [.02193629, -.19639897, .0020982049304637],
    'loglike': 123.1654530510318,
    'aic': -6.139646067788584,
    'bic': -6.108510053015554,
    'hqic': -6.127225528690452,
    'fpe': .0022147718710451,
    'estat_aic': -242.33091,
    'estat_bic': -237.72278
}

lutkepohl_ar1_lustats_dfk = {
    'params': [.02193629, -.19639897, .00215649],  # intercept, AR, sigma^2
    'loglike': 123.1654530510318,
    'loglike_dfk': 122.1516910060716,
    'aic': -6.139646067788584,
    'bic': -6.108510053015554,
    'hqic': -6.127225528690452,
    'fpe': .0022147718710451,
    'estat_aic': -242.33091,
    'estat_bic': -237.72278
}

lutkepohl_var1 = {
    'params': [
        -.25155231, .27806257, .8357748,  # Phi, row 1
        .0226746, .1936755, .66011984,    # Phi, row 2
        -.01493575, .54067685, .2807106,  # Phi, row 3
        # 0.002010307675,                       # Covariance, lower triangle
        # 0.000063517318, 0.000184048720,
        # 0.000129337591, 0.000117178971, 0.000162533784
        # Note: the following are the Cholesky of the covariance
        # matrix defined just above
        0.0448364548,                             # Cholesky, lower triangle
        0.0014166445, 0.0134922881,
        0.0028846525, 0.0083820063, 0.0091626707
    ],
    'loglike': 580.6167823218026,
    'aic': -15.44910222491358,
    'bic': -15.16887809195632,
    'hqic': -15.33731737303041,
    'fpe': 3.91899792595e-11,
    'estat_aic': -1143.2336,
    'estat_bic': -1122.497
}

lutkepohl_var1_lustats = {
    'params': [
        -.25155231, .27806257, .8357748,  # Phi, row 1
        .0226746, .1936755, .66011984,    # Phi, row 2
        -.01493575, .54067685, .2807106,  # Phi, row 3
        # 0.002010307675,                         # Covariance, lower triangle
        # 0.000063517318, 0.000184048720,
        # 0.000129337591, 0.000117178971, 0.000162533784
        # Note: the following are the Cholesky of the covariance
        # matrix defined just above
        0.0448364548,                             # Cholesky, lower triangle
        0.0014166445, 0.0134922881,
        0.0028846525, 0.0083820063, 0.0091626707
    ],
    'loglike': 580.6167823218026,
    'aic': -23.96273342414162,
    'bic': -23.68250929118436,
    'hqic': -23.85094857225844,
    'fpe': 3.91899792595e-11,
    'estat_aic': -1143.2336,
    'estat_bic': -1122.497
}

lutkepohl_var1_lustats_dfk = {
    'params': [
        -.25155231, .27806257, .8357748,  # Phi, row 1
        .0226746, .1936755, .66011984,    # Phi, row 2
        -.01493575, .54067685, .2807106,  # Phi, row 3
        # .00209525,                              # Covariance, lower triangle
        # .0000662, .00019183,
        # .0001348, .00012213, .0001694
        # Note: the following are the Cholesky of the covariance
        # matrix defined just above
        0.0457739,                             # Cholesky, lower triangle
        0.0014462, 0.0137746,
        0.0029449, 0.0085572, 0.0093543
    ],
    'loglike': 580.6167823218026,
    'loglike_dfk': 576.0230233277257,
    'aic': -23.96273342414162,
    'bic': -23.68250929118436,
    'hqic': -23.85094857225844,
    'fpe': 3.91899792595e-11,
    'estat_aic': -1143.2336,
    'estat_bic': -1122.497
}
