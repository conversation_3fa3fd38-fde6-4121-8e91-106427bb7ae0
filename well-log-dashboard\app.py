from flask import Flask, render_template, jsonify
import pandas as pd

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/logdata')
def log_data():
    df = pd.read_csv('data/sample_well_log.csv')    # Drop NaNs and convert to dictionary for JS
    return jsonify(df.dropna().to_dict(orient='list'))

if __name__ == '__main__':
    app.run(debug=True)

