{"cells": [{"cell_type": "code", "execution_count": 4, "id": "dd03cf53", "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 80\u001b[0m\n\u001b[0;32m     77\u001b[0m X_train, X_test, y_train, y_test \u001b[38;5;241m=\u001b[39m train_test_split(X, y, stratify\u001b[38;5;241m=\u001b[39my, test_size\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.2\u001b[39m, random_state\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m42\u001b[39m)\n\u001b[0;32m     79\u001b[0m model \u001b[38;5;241m=\u001b[39m get_pipeline()\n\u001b[1;32m---> 80\u001b[0m \u001b[43mmodel\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX_train\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_train\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     81\u001b[0m y_pred \u001b[38;5;241m=\u001b[39m model\u001b[38;5;241m.\u001b[39mpredict(X_test)\n\u001b[0;32m     83\u001b[0m acc \u001b[38;5;241m=\u001b[39m accuracy_score(y_test, y_pred)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\base.py:1473\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[1;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1466\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[0;32m   1468\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m   1469\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m   1470\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m   1471\u001b[0m     )\n\u001b[0;32m   1472\u001b[0m ):\n\u001b[1;32m-> 1473\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfit_method\u001b[49m\u001b[43m(\u001b[49m\u001b[43mestimator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\pipeline.py:473\u001b[0m, in \u001b[0;36mPipeline.fit\u001b[1;34m(self, X, y, **params)\u001b[0m\n\u001b[0;32m    471\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_final_estimator \u001b[38;5;241m!=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpassthrough\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m    472\u001b[0m         last_step_params \u001b[38;5;241m=\u001b[39m routed_params[\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msteps[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m][\u001b[38;5;241m0\u001b[39m]]\n\u001b[1;32m--> 473\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_final_estimator\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mXt\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mlast_step_params\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfit\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    475\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\base.py:1473\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[1;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1466\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[0;32m   1468\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m   1469\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m   1470\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m   1471\u001b[0m     )\n\u001b[0;32m   1472\u001b[0m ):\n\u001b[1;32m-> 1473\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfit_method\u001b[49m\u001b[43m(\u001b[49m\u001b[43mestimator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\ensemble\\_forest.py:489\u001b[0m, in \u001b[0;36mBaseForest.fit\u001b[1;34m(self, X, y, sample_weight)\u001b[0m\n\u001b[0;32m    478\u001b[0m trees \u001b[38;5;241m=\u001b[39m [\n\u001b[0;32m    479\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_make_estimator(append\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m, random_state\u001b[38;5;241m=\u001b[39mrandom_state)\n\u001b[0;32m    480\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(n_more_estimators)\n\u001b[0;32m    481\u001b[0m ]\n\u001b[0;32m    483\u001b[0m \u001b[38;5;66;03m# Parallel loop: we prefer the threading backend as the Cython code\u001b[39;00m\n\u001b[0;32m    484\u001b[0m \u001b[38;5;66;03m# for fitting the trees is internally releasing the Python GIL\u001b[39;00m\n\u001b[0;32m    485\u001b[0m \u001b[38;5;66;03m# making threading more efficient than multiprocessing in\u001b[39;00m\n\u001b[0;32m    486\u001b[0m \u001b[38;5;66;03m# that case. However, for joblib 0.12+ we respect any\u001b[39;00m\n\u001b[0;32m    487\u001b[0m \u001b[38;5;66;03m# parallel_backend contexts set at a higher level,\u001b[39;00m\n\u001b[0;32m    488\u001b[0m \u001b[38;5;66;03m# since correctness does not rely on using threads.\u001b[39;00m\n\u001b[1;32m--> 489\u001b[0m trees \u001b[38;5;241m=\u001b[39m \u001b[43mParallel\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    490\u001b[0m \u001b[43m    \u001b[49m\u001b[43mn_jobs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mn_jobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    491\u001b[0m \u001b[43m    \u001b[49m\u001b[43mverbose\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mverbose\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    492\u001b[0m \u001b[43m    \u001b[49m\u001b[43mprefer\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mthreads\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    493\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    494\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdelayed\u001b[49m\u001b[43m(\u001b[49m\u001b[43m_parallel_build_trees\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    495\u001b[0m \u001b[43m        \u001b[49m\u001b[43mt\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    496\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbootstrap\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    497\u001b[0m \u001b[43m        \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    498\u001b[0m \u001b[43m        \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    499\u001b[0m \u001b[43m        \u001b[49m\u001b[43msample_weight\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    500\u001b[0m \u001b[43m        \u001b[49m\u001b[43mi\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    501\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mtrees\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    502\u001b[0m \u001b[43m        \u001b[49m\u001b[43mverbose\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mverbose\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    503\u001b[0m \u001b[43m        \u001b[49m\u001b[43mclass_weight\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mclass_weight\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    504\u001b[0m \u001b[43m        \u001b[49m\u001b[43mn_samples_bootstrap\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mn_samples_bootstrap\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    505\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmissing_values_in_feature_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmissing_values_in_feature_mask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    506\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    507\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mi\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43menumerate\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mtrees\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    508\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    510\u001b[0m \u001b[38;5;66;03m# Collect newly grown trees\u001b[39;00m\n\u001b[0;32m    511\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mestimators_\u001b[38;5;241m.\u001b[39mextend(trees)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\utils\\parallel.py:74\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m     69\u001b[0m config \u001b[38;5;241m=\u001b[39m get_config()\n\u001b[0;32m     70\u001b[0m iterable_with_config \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m     71\u001b[0m     (_with_config(delayed_func, config), args, kwargs)\n\u001b[0;32m     72\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m delayed_func, args, kwargs \u001b[38;5;129;01min\u001b[39;00m iterable\n\u001b[0;32m     73\u001b[0m )\n\u001b[1;32m---> 74\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__call__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43miterable_with_config\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\joblib\\parallel.py:1986\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m   1984\u001b[0m     output \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_sequential_output(iterable)\n\u001b[0;32m   1985\u001b[0m     \u001b[38;5;28mnext\u001b[39m(output)\n\u001b[1;32m-> 1986\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m output \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mreturn_generator \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43moutput\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1988\u001b[0m \u001b[38;5;66;03m# Let's create an ID that uniquely identifies the current call. If the\u001b[39;00m\n\u001b[0;32m   1989\u001b[0m \u001b[38;5;66;03m# call is interrupted early and that the same instance is immediately\u001b[39;00m\n\u001b[0;32m   1990\u001b[0m \u001b[38;5;66;03m# reused, this id will be used to prevent workers that were\u001b[39;00m\n\u001b[0;32m   1991\u001b[0m \u001b[38;5;66;03m# concurrently finalizing a task from the previous call to run the\u001b[39;00m\n\u001b[0;32m   1992\u001b[0m \u001b[38;5;66;03m# callback.\u001b[39;00m\n\u001b[0;32m   1993\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_lock:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\joblib\\parallel.py:1914\u001b[0m, in \u001b[0;36mParallel._get_sequential_output\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m   1912\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_dispatched_batches \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[0;32m   1913\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_dispatched_tasks \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m-> 1914\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1915\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_completed_tasks \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[0;32m   1916\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprint_progress()\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\utils\\parallel.py:136\u001b[0m, in \u001b[0;36m_FuncWrapper.__call__\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m    134\u001b[0m     config \u001b[38;5;241m=\u001b[39m {}\n\u001b[0;32m    135\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mconfig):\n\u001b[1;32m--> 136\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfunction\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\ensemble\\_forest.py:192\u001b[0m, in \u001b[0;36m_parallel_build_trees\u001b[1;34m(tree, bootstrap, X, y, sample_weight, tree_idx, n_trees, verbose, class_weight, n_samples_bootstrap, missing_values_in_feature_mask)\u001b[0m\n\u001b[0;32m    189\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m class_weight \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbalanced_subsample\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m    190\u001b[0m         curr_sample_weight \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m=\u001b[39m compute_sample_weight(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbalanced\u001b[39m\u001b[38;5;124m\"\u001b[39m, y, indices\u001b[38;5;241m=\u001b[39mindices)\n\u001b[1;32m--> 192\u001b[0m     \u001b[43mtree\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_fit\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    193\u001b[0m \u001b[43m        \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    194\u001b[0m \u001b[43m        \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    195\u001b[0m \u001b[43m        \u001b[49m\u001b[43msample_weight\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcurr_sample_weight\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    196\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcheck_input\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    197\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmissing_values_in_feature_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmissing_values_in_feature_mask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    198\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    199\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    200\u001b[0m     tree\u001b[38;5;241m.\u001b[39m_fit(\n\u001b[0;32m    201\u001b[0m         X,\n\u001b[0;32m    202\u001b[0m         y,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    205\u001b[0m         missing_values_in_feature_mask\u001b[38;5;241m=\u001b[39mmissing_values_in_feature_mask,\n\u001b[0;32m    206\u001b[0m     )\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\sklearn\\tree\\_classes.py:472\u001b[0m, in \u001b[0;36mBaseDecisionTree._fit\u001b[1;34m(self, X, y, sample_weight, check_input, missing_values_in_feature_mask)\u001b[0m\n\u001b[0;32m    461\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    462\u001b[0m     builder \u001b[38;5;241m=\u001b[39m BestFirstTreeBuilder(\n\u001b[0;32m    463\u001b[0m         splitter,\n\u001b[0;32m    464\u001b[0m         min_samples_split,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    469\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmin_impurity_decrease,\n\u001b[0;32m    470\u001b[0m     )\n\u001b[1;32m--> 472\u001b[0m \u001b[43mbuilder\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbuild\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtree_\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msample_weight\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmissing_values_in_feature_mask\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    474\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_outputs_ \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m is_classifier(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m    475\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_classes_ \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_classes_[\u001b[38;5;241m0\u001b[39m]\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# Xeek ML Pipeline - Full Jupyter Notebook Version\n", "\n", "# 📦 Step 1: Imports\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.impute import SimpleImputer, KNNImputer\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.preprocessing import StandardScaler, OrdinalEncoder\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import classification_report, accuracy_score\n", "from copy import deepcopy\n", "\n", "sns.set(style=\"whitegrid\")\n", "\n", "# 📂 Step 2: Load Data\n", "df_original = pd.read_csv(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/CSV Data/Xeek_train_subset_clean.csv\")\n", "df_original = df_original.drop(columns=['WELL'])  # Drop high-cardinality\n", "\n", "# 🔧 Step 3: Corrupt Data\n", "def corrupt_data(df, corruption_rate=0.05, numerical_cols=None):\n", "    df_corrupt = df.copy()\n", "    for col in numerical_cols:\n", "        mask = np.random.rand(len(df)) < corruption_rate\n", "        df_corrupt.loc[mask, col] = np.nan\n", "    return df_corrupt\n", "\n", "numerical_cols = ['DEP<PERSON>_<PERSON>', 'CALI', 'RDEP', 'RHOB', 'GR', 'NPHI', 'PEF', 'DTC']\n", "categorical_cols = ['GROUP', 'FORMATION']\n", "target_col = 'LITH'\n", "\n", "# Create corrupted dataset\n", "df_corrupt = corrupt_data(df_original, numerical_cols=numerical_cols)\n", "\n", "# 🔄 Step 4: Imputation Methods\n", "def impute(df, method):\n", "    df_copy = df.copy()\n", "    if method == \"knn\":\n", "        imputer = KNNImputer(n_neighbors=5)\n", "    else:\n", "        imputer = SimpleImputer(strategy=method)\n", "    df_copy[numerical_cols] = imputer.fit_transform(df_copy[numerical_cols])\n", "    return df_copy\n", "\n", "# 🧠 Step 5: Train & Evaluate\n", "def get_pipeline():\n", "    numeric_transformer = Pipeline([\n", "        ('imputer', SimpleImputer(strategy='mean')),\n", "        ('scaler', StandardScaler())\n", "    ])\n", "    categorical_transformer = Pipeline([\n", "        ('imputer', SimpleImputer(strategy='most_frequent')),\n", "        ('encoder', OrdinalEncoder(handle_unknown='use_encoded_value', unknown_value=-1))\n", "    ])\n", "    preprocessor = ColumnTransformer([\n", "        ('num', numeric_transformer, numerical_cols),\n", "        ('cat', categorical_transformer, categorical_cols)\n", "    ])\n", "    model = Pipeline([\n", "        ('preprocessor', preprocessor),\n", "        ('classifier', RandomForestClassifier(n_estimators=50, random_state=42))\n", "    ])\n", "    return model\n", "\n", "results = {}\n", "\n", "# List of imputation methods\n", "for method in [\"mean\", \"median\", \"knn\"]:\n", "    df_imputed = impute(df_corrupt, method)\n", "    df_imputed = df_imputed.dropna(subset=[target_col])\n", "    X = df_imputed[numerical_cols + categorical_cols]\n", "    y = df_imputed[target_col]\n", "\n", "    X_train, X_test, y_train, y_test = train_test_split(X, y, stratify=y, test_size=0.2, random_state=42)\n", "\n", "    model = get_pipeline()\n", "    model.fit(X_train, y_train)\n", "    y_pred = model.predict(X_test)\n", "\n", "    acc = accuracy_score(y_test, y_pred)\n", "    report = classification_report(y_test, y_pred, output_dict=True)\n", "    results[method] = {\"accuracy\": acc, \"report\": report}\n", "\n", "# Baseline on clean data\n", "df_clean = df_original.dropna(subset=[target_col])\n", "X = df_clean[numerical_cols + categorical_cols]\n", "y = df_clean[target_col]\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, stratify=y, test_size=0.2, random_state=42)\n", "model = get_pipeline()\n", "model.fit(X_train, y_train)\n", "y_pred = model.predict(X_test)\n", "acc = accuracy_score(y_test, y_pred)\n", "report = classification_report(y_test, y_pred, output_dict=True)\n", "results['clean'] = {\"accuracy\": acc, \"report\": report}\n", "\n", "# 📊 Step 6: <PERSON>lot Comparison\n", "acc_scores = {k: v['accuracy'] for k, v in results.items()}\n", "plt.figure(figsize=(8, 5))\n", "sns.barplot(x=list(acc_scores.keys()), y=list(acc_scores.values()))\n", "plt.title(\"Model Accuracy: Clean vs Corrupted (Different Imputations)\")\n", "plt.ylabel(\"Accuracy\")\n", "plt.ylim(0, 1)\n", "plt.show()\n", "\n", "# Optional: Display Classification Reports\n", "import pprint\n", "pp = pprint.PrettyPrinter(indent=2)\n", "for k, v in results.items():\n", "    print(f\"\\n==== {k.upper()} ====\")\n", "    print(f\"Accuracy: {v['accuracy']:.4f}\")\n", "    pp.pprint(v['report'])\n"]}, {"cell_type": "code", "execution_count": 3, "id": "83ed2745", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Baseline MSE: 132.6479\n", "Baseline R²: 0.9850\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "import matplotlib.pyplot as plt\n", "\n", "# Example dummy dataset (replace with your actual DataFrame)\n", "# df = pd.read_csv('your_data.csv')  # Uncomment and use your real dataset\n", "# X = df.drop(columns='target_column')\n", "# y = df['target_column']\n", "\n", "# For demonstration: simulate data with missing values\n", "from sklearn.datasets import make_regression\n", "X, y = make_regression(n_samples=200, n_features=3, noise=10, random_state=42)\n", "X[np.random.choice(<PERSON>.shape[0], 20), 1] = np.nan  # Introduce some NaNs\n", "\n", "# Split into train and test sets\n", "X_train_baseline, X_test_baseline, y_train_baseline, y_test_baseline = train_test_split(\n", "    X, y, test_size=0.2, random_state=42\n", ")\n", "\n", "# Impute missing values using mean strategy\n", "imputer = SimpleImputer(strategy='mean')\n", "X_train_baseline_imputed = imputer.fit_transform(X_train_baseline)\n", "X_test_baseline_imputed = imputer.transform(X_test_baseline)\n", "\n", "# Train Linear Regression model\n", "model_baseline = LinearRegression()\n", "model_baseline.fit(X_train_baseline_imputed, y_train_baseline)\n", "y_pred_baseline = model_baseline.predict(X_test_baseline_imputed)\n", "\n", "# Evaluate performance\n", "mse_baseline = mean_squared_error(y_test_baseline, y_pred_baseline)\n", "r2_baseline = r2_score(y_test_baseline, y_pred_baseline)\n", "\n", "print(f\"Baseline MSE: {mse_baseline:.4f}\")\n", "print(f\"Baseline R²: {r2_baseline:.4f}\")\n", "\n", "# Plot actual vs predicted\n", "plt.figure(figsize=(8, 5))\n", "plt.scatter(y_test_baseline, y_pred_baseline, alpha=0.6, color='blue', label='Predicted')\n", "plt.plot([y_test_baseline.min(), y_test_baseline.max()],\n", "         [y_test_baseline.min(), y_test_baseline.max()], 'r--', label='Ideal')\n", "plt.xlabel(\"Actual\")\n", "plt.ylabel(\"Predicted\")\n", "plt.title(\"Actual vs Predicted (Linear Regression)\")\n", "plt.legend()\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9961f0e6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f433d48d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "84571688", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3de08bfc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "93702d29", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}