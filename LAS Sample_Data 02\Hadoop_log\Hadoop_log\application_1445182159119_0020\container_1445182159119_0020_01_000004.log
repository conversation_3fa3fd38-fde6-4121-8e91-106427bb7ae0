2015-10-19 17:47:47,503 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 17:47:47,769 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 17:47:47,769 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-19 17:47:47,816 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-19 17:47:47,816 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445182159119_0020, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@c5f5deb)
2015-10-19 17:47:48,175 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-19 17:47:48,956 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445182159119_0020
2015-10-19 17:47:50,519 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-19 17:47:52,691 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-19 17:47:52,878 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@428d7ac3
2015-10-19 17:48:14,582 WARN [main] org.apache.hadoop.hdfs.BlockReaderFactory: I/O error constructing remote block reader.
java.net.ConnectException: Connection timed out: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:735)
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:206)
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:530)
	at org.apache.hadoop.hdfs.DFSClient.newConnectedPeer(DFSClient.java:3101)
	at org.apache.hadoop.hdfs.BlockReaderFactory.nextTcpPeer(BlockReaderFactory.java:755)
	at org.apache.hadoop.hdfs.BlockReaderFactory.getRemoteBlockReaderFromTcp(BlockReaderFactory.java:670)
	at org.apache.hadoop.hdfs.BlockReaderFactory.build(BlockReaderFactory.java:337)
	at org.apache.hadoop.hdfs.DFSInputStream.blockSeekTo(DFSInputStream.java:576)
	at org.apache.hadoop.hdfs.DFSInputStream.readWithStrategy(DFSInputStream.java:800)
	at org.apache.hadoop.hdfs.DFSInputStream.read(DFSInputStream.java:847)
	at org.apache.hadoop.hdfs.DFSInputStream.read(DFSInputStream.java:655)
	at java.io.DataInputStream.readByte(DataInputStream.java:265)
	at org.apache.hadoop.io.WritableUtils.readVLong(WritableUtils.java:308)
	at org.apache.hadoop.io.WritableUtils.readVIntInRange(WritableUtils.java:348)
	at org.apache.hadoop.io.Text.readString(Text.java:471)
	at org.apache.hadoop.io.Text.readString(Text.java:464)
	at org.apache.hadoop.mapred.MapTask.getSplitDetails(MapTask.java:358)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:751)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
2015-10-19 17:48:14,582 WARN [main] org.apache.hadoop.hdfs.DFSClient: Failed to connect to /10.86.165.66:50010 for block, add to deadNodes and continue. java.net.ConnectException: Connection timed out: no further information
java.net.ConnectException: Connection timed out: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:735)
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:206)
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:530)
	at org.apache.hadoop.hdfs.DFSClient.newConnectedPeer(DFSClient.java:3101)
	at org.apache.hadoop.hdfs.BlockReaderFactory.nextTcpPeer(BlockReaderFactory.java:755)
	at org.apache.hadoop.hdfs.BlockReaderFactory.getRemoteBlockReaderFromTcp(BlockReaderFactory.java:670)
	at org.apache.hadoop.hdfs.BlockReaderFactory.build(BlockReaderFactory.java:337)
	at org.apache.hadoop.hdfs.DFSInputStream.blockSeekTo(DFSInputStream.java:576)
	at org.apache.hadoop.hdfs.DFSInputStream.readWithStrategy(DFSInputStream.java:800)
	at org.apache.hadoop.hdfs.DFSInputStream.read(DFSInputStream.java:847)
	at org.apache.hadoop.hdfs.DFSInputStream.read(DFSInputStream.java:655)
	at java.io.DataInputStream.readByte(DataInputStream.java:265)
	at org.apache.hadoop.io.WritableUtils.readVLong(WritableUtils.java:308)
	at org.apache.hadoop.io.WritableUtils.readVIntInRange(WritableUtils.java:348)
	at org.apache.hadoop.io.Text.readString(Text.java:471)
	at org.apache.hadoop.io.Text.readString(Text.java:464)
	at org.apache.hadoop.mapred.MapTask.getSplitDetails(MapTask.java:358)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:751)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
2015-10-19 17:48:16,300 INFO [main] org.apache.hadoop.hdfs.DFSClient: Successfully connected to /10.86.169.121:50010 for BP-1347369012-10.190.173.170-1444972147527:blk_1073744042_3267
2015-10-19 17:48:16,347 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:268435456+134217728
2015-10-19 17:48:16,472 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-19 17:48:16,472 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-19 17:48:16,472 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-19 17:48:16,472 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-19 17:48:16,472 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-19 17:48:16,519 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-19 17:48:22,738 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:48:22,738 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48249276; bufvoid = 104857600
2015-10-19 17:48:22,738 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17305200(69220800); length = 8909197/6553600
2015-10-19 17:48:22,738 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57318028 kvi 14329500(57318000)
2015-10-19 17:48:52,676 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-19 17:48:52,692 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57318028 kv 14329500(57318000) kvi 12129788(48519152)
2015-10-19 17:48:57,426 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:48:57,426 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57318028; bufend = 686843; bufvoid = 104857600
2015-10-19 17:48:57,426 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14329500(57318000); kvend = 5414592(21658368); length = 8914909/6553600
2015-10-19 17:48:57,426 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9755595 kvi 2438892(9755568)
2015-10-19 17:49:26,927 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-19 17:49:26,942 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9755595 kv 2438892(9755568) kvi 240952(963808)
2015-10-19 17:49:31,442 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:49:31,442 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9755595; bufend = 58006021; bufvoid = 104857600
2015-10-19 17:49:31,442 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2438892(9755568); kvend = 19744380(78977520); length = 8908913/6553600
2015-10-19 17:49:31,442 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67074757 kvi 16768684(67074736)
2015-10-19 17:50:01,443 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-19 17:50:01,490 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67074757 kv 16768684(67074736) kvi 14558340(58233360)
2015-10-19 17:50:06,021 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:50:06,021 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67074757; bufend = 10447270; bufvoid = 104857600
2015-10-19 17:50:06,021 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16768684(67074736); kvend = 7854692(31418768); length = 8913993/6553600
2015-10-19 17:50:06,021 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19516006 kvi 4878996(19515984)
2015-10-19 17:50:35,272 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-19 17:50:35,287 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 19516006 kv 4878996(19515984) kvi 2677056(10708224)
2015-10-19 17:50:39,912 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:50:39,912 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 19516006; bufend = 67756598; bufvoid = 104857600
2015-10-19 17:50:39,912 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 4878996(19515984); kvend = 22182024(88728096); length = 8911373/6553600
2015-10-19 17:50:39,912 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 76825334 kvi 19206328(76825312)
2015-10-19 17:51:08,163 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-19 17:51:08,256 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 76825334 kv 19206328(76825312) kvi 17012764(68051056)
2015-10-19 17:51:12,647 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:51:12,647 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 76825334; bufend = 20217188; bufvoid = 104857598
2015-10-19 17:51:12,647 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 19206328(76825312); kvend = 10297172(41188688); length = 8909157/6553600
2015-10-19 17:51:12,647 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29285924 kvi 7321476(29285904)
2015-10-19 17:51:41,101 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-19 17:51:41,116 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29285924 kv 7321476(29285904) kvi 5114320(20457280)
2015-10-19 17:51:46,382 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:51:46,382 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29285924; bufend = 77503154; bufvoid = 104857600
2015-10-19 17:51:46,382 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7321476(29285904); kvend = 24618672(98474688); length = 8917205/6553600
2015-10-19 17:51:46,382 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 86571906 kvi 21642972(86571888)
2015-10-19 17:52:12,976 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-19 17:52:12,992 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 86571906 kv 21642972(86571888) kvi 19445800(77783200)
2015-10-19 17:52:16,539 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-19 17:52:16,539 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:52:16,539 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 86571906; bufend = 20324632; bufvoid = 104857600
2015-10-19 17:52:16,539 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 21642972(86571888); kvend = 14513800(58055200); length = 7129173/6553600
2015-10-19 17:52:38,774 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 7
2015-10-19 17:52:38,852 INFO [main] org.apache.hadoop.mapred.Merger: Merging 8 sorted segments
2015-10-19 17:52:39,195 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 8 segments left of total size: 288688442 bytes
2015-10-19 17:54:23,713 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445182159119_0020_m_000002_0 is done. And is in the process of committing
2015-10-19 17:54:26,213 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445182159119_0020_m_000002_0' done.
2015-10-19 17:54:26,322 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Stopping MapTask metrics system...
2015-10-19 17:54:26,322 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system stopped.
2015-10-19 17:54:26,322 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system shutdown complete.
