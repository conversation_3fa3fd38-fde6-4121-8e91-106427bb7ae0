2015-10-19 14:21:34,474 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445182159119_0003_000001
2015-10-19 14:21:35,235 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-19 14:21:35,235 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 3 cluster_timestamp: 1445182159119 } attemptId: 1 } keyId: 1694045684)
2015-10-19 14:21:35,382 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-19 14:21:36,280 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-19 14:21:36,327 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-19 14:21:36,352 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-19 14:21:36,353 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-19 14:21:36,354 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-19 14:21:36,355 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-19 14:21:36,355 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-19 14:21:36,361 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-19 14:21:36,362 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-19 14:21:36,363 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-19 14:21:36,400 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 14:21:36,421 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 14:21:36,440 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 14:21:36,449 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-19 14:21:36,492 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-19 14:21:36,712 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 14:21:36,760 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 14:21:36,760 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-19 14:21:36,767 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445182159119_0003 to jobTokenSecretManager
2015-10-19 14:21:36,900 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445182159119_0003 because: not enabled; too many maps; too much input;
2015-10-19 14:21:36,917 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445182159119_0003 = 1313861632. Number of splits = 10
2015-10-19 14:21:36,918 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445182159119_0003 = 1
2015-10-19 14:21:36,918 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0003Job Transitioned from NEW to INITED
2015-10-19 14:21:36,919 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445182159119_0003.
2015-10-19 14:21:36,949 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 14:21:36,959 INFO [Socket Reader #1 for port 43568] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 43568
2015-10-19 14:21:37,105 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-19 14:21:37,105 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 14:21:37,105 INFO [IPC Server listener on 43568] org.apache.hadoop.ipc.Server: IPC Server listener on 43568: starting
2015-10-19 14:21:37,106 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-41.fareast.corp.microsoft.com/**************:43568
2015-10-19 14:21:37,202 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-19 14:21:37,206 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-19 14:21:37,215 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-19 14:21:37,220 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-19 14:21:37,220 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-19 14:21:37,223 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-19 14:21:37,223 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-19 14:21:37,231 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 43575
2015-10-19 14:21:37,231 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-19 14:21:37,269 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_43575_mapreduce____.7zo62j\webapp
2015-10-19 14:21:37,671 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:43575
2015-10-19 14:21:37,672 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 43575
2015-10-19 14:21:38,363 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-19 14:21:38,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445182159119_0003
2015-10-19 14:21:38,371 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 14:21:38,376 INFO [Socket Reader #1 for port 43581] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 43581
2015-10-19 14:21:38,386 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 14:21:38,386 INFO [IPC Server listener on 43581] org.apache.hadoop.ipc.Server: IPC Server listener on 43581: starting
2015-10-19 14:21:38,419 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-19 14:21:38,419 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-19 14:21:38,419 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-19 14:21:38,521 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at MSRA-SA-41/**************:8030
2015-10-19 14:21:38,632 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-19 14:21:38,632 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-19 14:21:38,639 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-19 14:21:38,642 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-19 14:21:38,657 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0003Job Transitioned from INITED to SETUP
2015-10-19 14:21:38,659 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-19 14:21:38,672 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0003Job Transitioned from SETUP to RUNNING
2015-10-19 14:21:38,702 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,703 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,707 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:38,708 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,708 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,708 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:38,709 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,709 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,709 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:38,710 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,710 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,710 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:38,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:38,712 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,712 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,712 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:38,713 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,713 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,713 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:38,713 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:38,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:38,715 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,716 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:38,716 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:38,717 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 14:21:38,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:38,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:38,720 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:38,720 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:38,720 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:38,720 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:38,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:38,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:38,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:38,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:38,722 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:21:38,724 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-19 14:21:38,737 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-19 14:21:38,813 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445182159119_0003, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0003/job_1445182159119_0003_1.jhist
2015-10-19 14:21:39,636 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 14:21:39,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:12288, vCores:-15> knownNMs=4
2015-10-19 14:21:39,692 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:12288, vCores:-15>
2015-10-19 14:21:39,693 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:40,696 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:12288, vCores:-15>
2015-10-19 14:21:40,697 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:41,700 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:12288, vCores:-15>
2015-10-19 14:21:41,700 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:42,716 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:21:42,717 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:42,719 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000002 to attempt_1445182159119_0003_m_000000_0
2015-10-19 14:21:42,721 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-17>
2015-10-19 14:21:42,721 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:42,721 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:0 RackLocal:1
2015-10-19 14:21:42,802 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:42,826 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0003/job.jar
2015-10-19 14:21:42,831 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0003/job.xml
2015-10-19 14:21:42,833 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-19 14:21:42,833 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-19 14:21:42,833 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-19 14:21:42,913 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:21:42,935 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000002 taskAttempt attempt_1445182159119_0003_m_000000_0
2015-10-19 14:21:42,941 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000000_0
2015-10-19 14:21:42,942 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:21:43,079 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000000_0 : 13562
2015-10-19 14:21:43,083 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000000_0] using containerId: [container_1445182159119_0003_01_000002 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 14:21:43,090 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:21:43,091 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000000
2015-10-19 14:21:43,091 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:21:43,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-18> knownNMs=4
2015-10-19 14:21:43,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-18>
2015-10-19 14:21:43,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:44,741 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:21:44,742 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:44,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000003 to attempt_1445182159119_0003_m_000001_0
2015-10-19 14:21:44,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-19>
2015-10-19 14:21:44,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:44,744 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:0 RackLocal:2
2015-10-19 14:21:44,744 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:44,745 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:21:44,747 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000003 taskAttempt attempt_1445182159119_0003_m_000001_0
2015-10-19 14:21:44,747 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000001_0
2015-10-19 14:21:44,748 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:21:45,168 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000001_0 : 13562
2015-10-19 14:21:45,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000001_0] using containerId: [container_1445182159119_0003_01_000003 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 14:21:45,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:21:45,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000001
2015-10-19 14:21:45,170 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:21:45,749 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-20> knownNMs=4
2015-10-19 14:21:45,749 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-20>
2015-10-19 14:21:45,750 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:46,758 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:21:46,758 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:46,758 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000004 to attempt_1445182159119_0003_m_000002_0
2015-10-19 14:21:46,759 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-19 14:21:46,759 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:46,759 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:0 RackLocal:3
2015-10-19 14:21:46,759 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:21:46,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:21:46,762 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000004 taskAttempt attempt_1445182159119_0003_m_000002_0
2015-10-19 14:21:46,762 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000002_0
2015-10-19 14:21:46,762 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:21:46,814 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000002_0 : 13562
2015-10-19 14:21:46,815 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000002_0] using containerId: [container_1445182159119_0003_01_000004 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 14:21:46,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:21:46,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000002
2015-10-19 14:21:46,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:21:47,761 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-19 14:21:47,761 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 14:21:47,761 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:48,763 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 14:21:48,763 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:49,764 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:21:49,764 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:50,650 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:21:50,765 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:21:50,765 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:51,088 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:21:51,123 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000003 asked for a task
2015-10-19 14:21:51,123 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000003 given task: attempt_1445182159119_0003_m_000001_0
2015-10-19 14:21:51,219 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000002 asked for a task
2015-10-19 14:21:51,219 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000002 given task: attempt_1445182159119_0003_m_000000_0
2015-10-19 14:21:51,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:21:51,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:52,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:21:52,769 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:53,771 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:21:53,772 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:54,354 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:21:54,595 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000004 asked for a task
2015-10-19 14:21:54,595 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000004 given task: attempt_1445182159119_0003_m_000002_0
2015-10-19 14:21:54,776 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:21:54,776 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:55,777 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:21:55,777 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:56,778 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:21:56,778 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:57,779 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:21:57,779 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:58,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:21:58,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:21:59,781 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:21:59,781 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:00,782 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:00,782 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:01,785 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:01,785 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:02,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:02,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:03,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:03,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:04,023 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.09932966
2015-10-19 14:22:04,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:04,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:05,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:05,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:06,792 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:06,792 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:07,474 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.10812413
2015-10-19 14:22:07,793 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:07,793 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:08,794 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:08,794 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:09,796 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:09,796 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:10,004 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.033269748
2015-10-19 14:22:10,583 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.114962086
2015-10-19 14:22:10,797 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:10,797 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:11,798 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:11,798 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:12,799 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:12,799 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:13,171 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.057386875
2015-10-19 14:22:13,693 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.12212906
2015-10-19 14:22:13,801 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:13,801 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:14,233 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.030283928
2015-10-19 14:22:14,802 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:14,802 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:15,803 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:15,803 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:16,318 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.07066923
2015-10-19 14:22:16,804 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:16,804 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:16,869 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.13101934
2015-10-19 14:22:17,364 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.03907764
2015-10-19 14:22:17,805 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:17,805 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:18,806 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:18,806 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:19,414 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.082391545
2015-10-19 14:22:19,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:19,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:19,983 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.13101934
2015-10-19 14:22:20,449 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.05112694
2015-10-19 14:22:20,810 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:20,810 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:21,812 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:21,813 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:22,552 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.09672252
2015-10-19 14:22:22,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:22,815 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:23,108 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.13101934
2015-10-19 14:22:23,537 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.063503295
2015-10-19 14:22:23,817 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:23,817 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:24,820 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:24,820 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:25,758 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.103561014
2015-10-19 14:22:25,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:25,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:26,377 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.13101934
2015-10-19 14:22:26,667 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.0762058
2015-10-19 14:22:26,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:26,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:27,824 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:27,824 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:28,825 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:28,825 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:29,294 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.11952222
2015-10-19 14:22:29,826 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:29,826 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:30,163 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.15665144
2015-10-19 14:22:30,221 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.090537295
2015-10-19 14:22:30,827 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:30,827 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:31,828 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:31,828 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:32,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:32,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:33,056 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.131014
2015-10-19 14:22:33,748 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.17912556
2015-10-19 14:22:33,830 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:33,830 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:34,728 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.118872784
2015-10-19 14:22:34,831 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:34,831 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:35,832 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:35,832 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:36,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:36,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:37,225 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.23756991
2015-10-19 14:22:37,750 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.131014
2015-10-19 14:22:37,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:37,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:38,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:38,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:39,010 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.13102192
2015-10-19 14:22:39,837 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:39,837 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:40,785 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.23921585
2015-10-19 14:22:40,838 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:40,838 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:41,839 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:41,839 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:42,603 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.131014
2015-10-19 14:22:42,840 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:42,840 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:43,492 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.13102192
2015-10-19 14:22:43,841 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:43,841 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:44,255 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.23921585
2015-10-19 14:22:44,842 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:44,842 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:45,843 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:45,843 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:46,844 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:46,844 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:47,050 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.131014
2015-10-19 14:22:47,845 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:47,845 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:47,849 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.23921585
2015-10-19 14:22:47,970 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.13102192
2015-10-19 14:22:48,846 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:48,846 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:49,847 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:49,847 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:50,848 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:50,848 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:51,252 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.131014
2015-10-19 14:22:51,400 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.23921585
2015-10-19 14:22:51,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:22:51,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000005 to attempt_1445182159119_0003_m_000003_0
2015-10-19 14:22:51,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:51,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:51,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:1 RackLocal:3
2015-10-19 14:22:51,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:22:51,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:22:51,856 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000005 taskAttempt attempt_1445182159119_0003_m_000003_0
2015-10-19 14:22:51,856 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000003_0
2015-10-19 14:22:51,856 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:22:51,878 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000003_0 : 13562
2015-10-19 14:22:51,878 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000003_0] using containerId: [container_1445182159119_0003_01_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:22:51,878 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:22:51,878 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000003
2015-10-19 14:22:51,878 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:22:52,288 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.13102192
2015-10-19 14:22:52,855 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:22:52,855 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:52,855 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:53,761 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:22:53,773 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000005 asked for a task
2015-10-19 14:22:53,773 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000005 given task: attempt_1445182159119_0003_m_000003_0
2015-10-19 14:22:53,856 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:53,856 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:54,858 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:54,858 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:55,035 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.23921585
2015-10-19 14:22:55,348 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.131014
2015-10-19 14:22:55,859 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:55,859 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:56,532 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.13102192
2015-10-19 14:22:56,860 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:56,860 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:57,861 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:57,861 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:58,547 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.23921585
2015-10-19 14:22:58,862 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:58,862 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:59,863 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:22:59,863 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:22:59,901 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.131014
2015-10-19 14:23:00,864 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.13102706
2015-10-19 14:23:00,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:00,866 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:01,288 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.13102192
2015-10-19 14:23:01,867 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:01,867 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:02,079 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.23921585
2015-10-19 14:23:02,869 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:02,869 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:03,872 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:23:03,873 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000006 to attempt_1445182159119_0003_m_000004_0
2015-10-19 14:23:03,873 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:03,873 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:03,873 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:2 RackLocal:3
2015-10-19 14:23:03,873 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:03,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:23:03,875 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000006 taskAttempt attempt_1445182159119_0003_m_000004_0
2015-10-19 14:23:03,875 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000004_0
2015-10-19 14:23:03,875 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:23:03,897 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000004_0 : 13562
2015-10-19 14:23:03,897 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000004_0] using containerId: [container_1445182159119_0003_01_000006 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:23:03,898 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:23:03,898 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000004
2015-10-19 14:23:03,898 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:23:03,899 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.13102706
2015-10-19 14:23:04,545 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.16967657
2015-10-19 14:23:04,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:23:04,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:23:04,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000007 to attempt_1445182159119_0003_m_000005_0
2015-10-19 14:23:04,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:04,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:04,881 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:3 RackLocal:3
2015-10-19 14:23:04,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:04,882 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:23:04,884 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000007 taskAttempt attempt_1445182159119_0003_m_000005_0
2015-10-19 14:23:04,884 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000005_0
2015-10-19 14:23:04,884 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:23:04,914 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000005_0 : 13562
2015-10-19 14:23:04,915 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000005_0] using containerId: [container_1445182159119_0003_01_000007 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 14:23:04,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:23:04,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000005
2015-10-19 14:23:04,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:23:05,681 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.23921585
2015-10-19 14:23:05,688 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.13102192
2015-10-19 14:23:05,883 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:23:05,883 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:05,883 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:05,982 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:23:05,995 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000006 asked for a task
2015-10-19 14:23:05,995 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000006 given task: attempt_1445182159119_0003_m_000004_0
2015-10-19 14:23:06,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:06,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:06,930 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.23922287
2015-10-19 14:23:06,996 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:23:07,008 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000007 asked for a task
2015-10-19 14:23:07,008 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000007 given task: attempt_1445182159119_0003_m_000005_0
2015-10-19 14:23:07,886 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:07,886 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:08,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:08,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:09,249 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.23921585
2015-10-19 14:23:09,601 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.23919508
2015-10-19 14:23:09,888 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:09,888 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:09,961 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.23922287
2015-10-19 14:23:10,058 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.13102192
2015-10-19 14:23:10,890 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:10,890 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:11,891 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:11,891 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:12,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:12,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:12,981 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.23922287
2015-10-19 14:23:13,086 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.25110048
2015-10-19 14:23:13,086 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.13104042
2015-10-19 14:23:13,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:13,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:13,988 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.23919508
2015-10-19 14:23:14,266 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.13104132
2015-10-19 14:23:14,487 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.14655617
2015-10-19 14:23:14,894 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:14,894 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:15,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:15,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:16,011 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.3473985
2015-10-19 14:23:16,118 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.13104042
2015-10-19 14:23:16,652 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.2888226
2015-10-19 14:23:16,896 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:16,896 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:17,286 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.13104132
2015-10-19 14:23:17,897 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:17,897 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:18,207 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.23919508
2015-10-19 14:23:18,434 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.1817146
2015-10-19 14:23:18,898 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:18,898 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:19,042 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.3473985
2015-10-19 14:23:19,148 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.23924798
2015-10-19 14:23:19,899 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:19,899 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:20,316 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.23922269
2015-10-19 14:23:20,317 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.32437974
2015-10-19 14:23:20,900 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:20,900 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:21,893 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.23919508
2015-10-19 14:23:21,901 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:21,901 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:22,074 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.39314857
2015-10-19 14:23:22,139 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.19834
2015-10-19 14:23:22,181 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.23924798
2015-10-19 14:23:22,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:22,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:23,346 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.23922269
2015-10-19 14:23:23,820 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.3474062
2015-10-19 14:23:23,903 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:23,903 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:24,904 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:24,904 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:25,103 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.45559394
2015-10-19 14:23:25,211 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.25244132
2015-10-19 14:23:25,906 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:25,906 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:26,377 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.23949616
2015-10-19 14:23:26,614 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.23919508
2015-10-19 14:23:26,907 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:26,907 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:27,286 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.3474062
2015-10-19 14:23:27,354 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.23383602
2015-10-19 14:23:27,908 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:27,908 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:28,134 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.45559394
2015-10-19 14:23:28,240 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.34742972
2015-10-19 14:23:28,909 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:28,909 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:29,405 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.3474054
2015-10-19 14:23:29,910 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:29,910 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:30,758 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.3474062
2015-10-19 14:23:30,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:30,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:31,066 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.23919508
2015-10-19 14:23:31,164 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.5156941
2015-10-19 14:23:31,277 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.34742972
2015-10-19 14:23:31,841 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.23921879
2015-10-19 14:23:31,912 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:31,912 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:32,435 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.3474054
2015-10-19 14:23:32,913 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:32,913 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:33,914 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:33,914 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:34,194 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.5637838
2015-10-19 14:23:34,244 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.3474062
2015-10-19 14:23:34,310 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.3763736
2015-10-19 14:23:34,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:34,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:35,466 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.39173692
2015-10-19 14:23:35,527 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.23919508
2015-10-19 14:23:35,916 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:35,916 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:36,173 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.23921879
2015-10-19 14:23:36,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:36,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:37,223 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.5637838
2015-10-19 14:23:37,343 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.45565325
2015-10-19 14:23:37,821 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.3474062
2015-10-19 14:23:37,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:23:37,922 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000008 to attempt_1445182159119_0003_m_000006_0
2015-10-19 14:23:37,922 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:37,922 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:37,922 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:4 RackLocal:3
2015-10-19 14:23:37,923 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:37,923 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:23:37,924 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000008 taskAttempt attempt_1445182159119_0003_m_000006_0
2015-10-19 14:23:37,924 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000006_0
2015-10-19 14:23:37,924 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:23:37,942 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000006_0 : 13562
2015-10-19 14:23:37,943 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000006_0] using containerId: [container_1445182159119_0003_01_000008 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:23:37,943 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:23:37,943 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000006
2015-10-19 14:23:37,943 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:23:38,496 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.45560944
2015-10-19 14:23:38,923 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:23:38,923 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:38,923 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:39,912 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.5637838
2015-10-19 14:23:39,924 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:39,924 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:40,253 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.667
2015-10-19 14:23:40,291 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.23919508
2015-10-19 14:23:40,372 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.45565325
2015-10-19 14:23:40,574 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.23921879
2015-10-19 14:23:40,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:40,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:41,527 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.45560944
2015-10-19 14:23:41,759 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.3474062
2015-10-19 14:23:41,929 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:23:41,929 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000009 to attempt_1445182159119_0003_m_000007_0
2015-10-19 14:23:41,930 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:41,930 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:41,930 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:41,930 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:5 RackLocal:3
2015-10-19 14:23:41,930 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:23:41,931 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000009 taskAttempt attempt_1445182159119_0003_m_000007_0
2015-10-19 14:23:41,931 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000007_0
2015-10-19 14:23:41,931 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:23:41,948 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000007_0 : 13562
2015-10-19 14:23:41,948 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000007_0] using containerId: [container_1445182159119_0003_01_000009 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:23:41,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:23:41,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000007
2015-10-19 14:23:41,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:23:42,936 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:23:42,936 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-19 14:23:42,937 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000010 to attempt_1445182159119_0003_m_000008_0
2015-10-19 14:23:42,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:42,937 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000011 to attempt_1445182159119_0003_m_000009_0
2015-10-19 14:23:42,938 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:23:42,938 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 14:23:42,938 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-19 14:23:42,938 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:23:42,938 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:23:42,939 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:23:42,940 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000010 taskAttempt attempt_1445182159119_0003_m_000008_0
2015-10-19 14:23:42,940 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000011 taskAttempt attempt_1445182159119_0003_m_000009_0
2015-10-19 14:23:42,940 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000008_0
2015-10-19 14:23:42,940 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000009_0
2015-10-19 14:23:42,940 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:23:42,941 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:23:42,957 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000008_0 : 13562
2015-10-19 14:23:42,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000008_0] using containerId: [container_1445182159119_0003_01_000010 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:23:42,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:23:42,959 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000008
2015-10-19 14:23:42,959 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:23:42,959 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000009_0 : 13562
2015-10-19 14:23:42,960 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000009_0] using containerId: [container_1445182159119_0003_01_000011 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:23:42,960 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:23:42,960 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000009
2015-10-19 14:23:42,960 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:23:43,281 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.667
2015-10-19 14:23:43,403 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.5027841
2015-10-19 14:23:43,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:23:44,557 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.53561246
2015-10-19 14:23:44,761 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.2502947
2015-10-19 14:23:45,096 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.23921879
2015-10-19 14:23:45,510 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.3474062
2015-10-19 14:23:45,862 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:23:45,883 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000008 asked for a task
2015-10-19 14:23:45,883 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000008 given task: attempt_1445182159119_0003_m_000006_0
2015-10-19 14:23:45,915 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:23:45,943 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000009 asked for a task
2015-10-19 14:23:45,944 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000009 given task: attempt_1445182159119_0003_m_000007_0
2015-10-19 14:23:46,309 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.667
2015-10-19 14:23:46,433 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.5638328
2015-10-19 14:23:46,590 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:23:46,616 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000011 asked for a task
2015-10-19 14:23:46,616 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000011 given task: attempt_1445182159119_0003_m_000009_0
2015-10-19 14:23:47,014 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:23:47,040 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000010 asked for a task
2015-10-19 14:23:47,041 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000010 given task: attempt_1445182159119_0003_m_000008_0
2015-10-19 14:23:47,587 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.56381226
2015-10-19 14:23:49,115 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.3474062
2015-10-19 14:23:49,338 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.6762047
2015-10-19 14:23:49,436 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.33203226
2015-10-19 14:23:49,463 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.5638328
2015-10-19 14:23:50,111 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.23921879
2015-10-19 14:23:50,617 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.56381226
2015-10-19 14:23:52,368 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.7086449
2015-10-19 14:23:52,494 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.65523005
2015-10-19 14:23:52,601 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.3474062
2015-10-19 14:23:52,874 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.65523005
2015-10-19 14:23:53,598 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.13101135
2015-10-19 14:23:53,656 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.6494113
2015-10-19 14:23:53,679 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.13103712
2015-10-19 14:23:54,144 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.16604526
2015-10-19 14:23:54,528 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.3474061
2015-10-19 14:23:54,615 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.13102318
2015-10-19 14:23:54,626 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.6494113
2015-10-19 14:23:55,279 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.23921879
2015-10-19 14:23:55,387 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.72767526
2015-10-19 14:23:55,514 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.667
2015-10-19 14:23:55,742 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.3530399
2015-10-19 14:23:56,603 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.13101135
2015-10-19 14:23:56,681 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.13103712
2015-10-19 14:23:56,689 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.667
2015-10-19 14:23:57,149 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.16604526
2015-10-19 14:23:57,618 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.13102318
2015-10-19 14:23:58,418 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.76105416
2015-10-19 14:23:58,545 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.667
2015-10-19 14:23:59,401 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.37030086
2015-10-19 14:23:59,442 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.3474061
2015-10-19 14:23:59,602 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.13101135
2015-10-19 14:23:59,681 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.13103712
2015-10-19 14:23:59,719 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.667
2015-10-19 14:23:59,934 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.23921879
2015-10-19 14:24:00,149 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.16604526
2015-10-19 14:24:00,619 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.13102318
2015-10-19 14:24:01,452 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.79364735
2015-10-19 14:24:01,578 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.667
2015-10-19 14:24:02,608 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.23923388
2015-10-19 14:24:02,685 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.23439975
2015-10-19 14:24:02,697 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.38658506
2015-10-19 14:24:02,752 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.667
2015-10-19 14:24:03,151 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.29474464
2015-10-19 14:24:03,605 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.3474061
2015-10-19 14:24:03,623 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.23606785
2015-10-19 14:24:03,945 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.23921879
2015-10-19 14:24:04,483 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.82316035
2015-10-19 14:24:04,607 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.67568946
2015-10-19 14:24:05,618 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.23923388
2015-10-19 14:24:05,696 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.23924637
2015-10-19 14:24:05,783 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.6773076
2015-10-19 14:24:06,148 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.3031575
2015-10-19 14:24:06,245 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.42305997
2015-10-19 14:24:06,633 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.23921506
2015-10-19 14:24:07,516 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.8578537
2015-10-19 14:24:07,637 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.7117794
2015-10-19 14:24:07,841 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.3474061
2015-10-19 14:24:08,049 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.24328212
2015-10-19 14:24:08,618 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.23923388
2015-10-19 14:24:08,696 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.23924637
2015-10-19 14:24:08,812 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.7125435
2015-10-19 14:24:09,153 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.3031575
2015-10-19 14:24:09,637 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.23921506
2015-10-19 14:24:09,807 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.45563135
2015-10-19 14:24:10,546 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.8931714
2015-10-19 14:24:10,667 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.7484952
2015-10-19 14:24:11,623 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.31200242
2015-10-19 14:24:11,701 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.26178467
2015-10-19 14:24:11,725 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.3474061
2015-10-19 14:24:11,797 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.26543072
2015-10-19 14:24:11,842 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.74685955
2015-10-19 14:24:12,160 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.3274269
2015-10-19 14:24:12,641 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.2710242
2015-10-19 14:24:13,431 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.45563135
2015-10-19 14:24:13,576 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.9268582
2015-10-19 14:24:13,696 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.7836632
2015-10-19 14:24:14,624 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.34743717
2015-10-19 14:24:14,701 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.34743145
2015-10-19 14:24:14,882 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.7802392
2015-10-19 14:24:15,175 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.4402952
2015-10-19 14:24:15,655 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.3474145
2015-10-19 14:24:15,674 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.3474061
2015-10-19 14:24:16,134 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.28692505
2015-10-19 14:24:16,606 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.95870435
2015-10-19 14:24:16,726 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.8169117
2015-10-19 14:24:16,995 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.45563135
2015-10-19 14:24:17,622 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.34743717
2015-10-19 14:24:17,700 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.34743145
2015-10-19 14:24:17,913 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.8138658
2015-10-19 14:24:18,184 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.4402952
2015-10-19 14:24:18,654 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.3474145
2015-10-19 14:24:19,635 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 0.9942726
2015-10-19 14:24:19,757 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.85408443
2015-10-19 14:24:20,137 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.3474061
2015-10-19 14:24:20,229 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000003_0 is : 1.0
2015-10-19 14:24:20,231 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0003_m_000003_0
2015-10-19 14:24:20,232 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:24:20,233 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000005 taskAttempt attempt_1445182159119_0003_m_000003_0
2015-10-19 14:24:20,233 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000003_0
2015-10-19 14:24:20,233 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:24:20,257 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:24:20,263 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0003_m_000003_0
2015-10-19 14:24:20,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:24:20,267 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-19 14:24:20,419 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.30548885
2015-10-19 14:24:20,496 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0003_m_000001
2015-10-19 14:24:20,496 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:24:20,496 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0003_m_000001
2015-10-19 14:24:20,497 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:24:20,497 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:24:20,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000001_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:24:20,622 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.34743717
2015-10-19 14:24:20,701 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.34743145
2015-10-19 14:24:20,701 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.45563135
2015-10-19 14:24:20,944 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.84910744
2015-10-19 14:24:21,000 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-19 14:24:21,001 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:24:21,002 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 14:24:21,002 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-19 14:24:21,002 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:10240, vCores:-17> finalMapResourceLimit:<memory:9216, vCores:-16> finalReduceResourceLimit:<memory:1024, vCores:-1> netScheduledMapResource:<memory:11264, vCores:11> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 14:24:21,002 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-19 14:24:21,002 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-19 14:24:21,183 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.4402952
2015-10-19 14:24:21,655 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.3474145
2015-10-19 14:24:22,008 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=1 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:24:22,008 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000005
2015-10-19 14:24:22,010 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-19 14:24:22,010 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:24:22,787 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.88775396
2015-10-19 14:24:23,630 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.455643
2015-10-19 14:24:23,707 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.44261256
2015-10-19 14:24:23,974 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.88102055
2015-10-19 14:24:24,156 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.3474061
2015-10-19 14:24:24,191 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.5282958
2015-10-19 14:24:24,260 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.45563135
2015-10-19 14:24:24,514 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.32340032
2015-10-19 14:24:24,655 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.45562187
2015-10-19 14:24:25,817 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.9206866
2015-10-19 14:24:26,638 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.455643
2015-10-19 14:24:26,716 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.4556257
2015-10-19 14:24:27,014 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.911466
2015-10-19 14:24:27,204 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.5773621
2015-10-19 14:24:27,655 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.45562187
2015-10-19 14:24:27,665 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.45563135
2015-10-19 14:24:28,225 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.36964703
2015-10-19 14:24:28,573 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.33870786
2015-10-19 14:24:28,854 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.9430095
2015-10-19 14:24:29,639 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.455643
2015-10-19 14:24:29,717 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.4556257
2015-10-19 14:24:30,055 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.9299114
2015-10-19 14:24:30,218 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.5773621
2015-10-19 14:24:30,654 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.45562187
2015-10-19 14:24:31,440 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.45563135
2015-10-19 14:24:31,887 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.9632689
2015-10-19 14:24:32,318 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.40644753
2015-10-19 14:24:32,586 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.34743196
2015-10-19 14:24:32,642 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.5638263
2015-10-19 14:24:32,718 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.4630761
2015-10-19 14:24:33,086 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.9531333
2015-10-19 14:24:33,224 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.5890276
2015-10-19 14:24:33,661 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.52699673
2015-10-19 14:24:34,306 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.5890276
2015-10-19 14:24:34,763 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.45563135
2015-10-19 14:24:34,918 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 0.99439466
2015-10-19 14:24:35,491 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000004_0 is : 1.0
2015-10-19 14:24:35,492 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0003_m_000004_0
2015-10-19 14:24:35,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:24:35,493 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000006 taskAttempt attempt_1445182159119_0003_m_000004_0
2015-10-19 14:24:35,493 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000004_0
2015-10-19 14:24:35,493 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:24:35,509 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:24:35,509 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0003_m_000004_0
2015-10-19 14:24:35,510 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:24:35,510 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-19 14:24:35,654 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.5638263
2015-10-19 14:24:35,733 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.56380385
2015-10-19 14:24:36,028 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-19 14:24:36,115 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 0.9870776
2015-10-19 14:24:36,232 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.667
2015-10-19 14:24:36,495 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.4370628
2015-10-19 14:24:36,670 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.56384325
2015-10-19 14:24:36,878 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.34743196
2015-10-19 14:24:37,030 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000006
2015-10-19 14:24:37,030 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-19 14:24:37,030 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:24:37,402 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000005_0 is : 1.0
2015-10-19 14:24:37,403 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0003_m_000005_0
2015-10-19 14:24:37,404 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:24:37,404 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000007 taskAttempt attempt_1445182159119_0003_m_000005_0
2015-10-19 14:24:37,404 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000005_0
2015-10-19 14:24:37,404 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 14:24:37,422 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:24:37,422 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0003_m_000005_0
2015-10-19 14:24:37,422 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:24:37,423 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-19 14:24:38,030 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:8 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-19 14:24:38,165 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.45563135
2015-10-19 14:24:38,658 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.5638263
2015-10-19 14:24:38,732 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.56380385
2015-10-19 14:24:39,032 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000007
2015-10-19 14:24:39,032 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:7 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-19 14:24:39,032 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:24:39,231 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.667
2015-10-19 14:24:39,670 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.56384325
2015-10-19 14:24:40,845 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.45561612
2015-10-19 14:24:41,198 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.34743196
2015-10-19 14:24:41,308 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.47288963
2015-10-19 14:24:41,670 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.6652241
2015-10-19 14:24:41,713 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.6652241
2015-10-19 14:24:41,737 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.56380385
2015-10-19 14:24:42,235 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.667
2015-10-19 14:24:42,672 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.56384325
2015-10-19 14:24:44,555 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.48982596
2015-10-19 14:24:44,562 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.56384325
2015-10-19 14:24:44,677 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.667
2015-10-19 14:24:44,752 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.62746334
2015-10-19 14:24:45,235 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.70883876
2015-10-19 14:24:45,484 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.62746334
2015-10-19 14:24:45,671 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.667
2015-10-19 14:24:45,694 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.45561612
2015-10-19 14:24:45,999 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.34743196
2015-10-19 14:24:47,685 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.667
2015-10-19 14:24:47,765 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.667
2015-10-19 14:24:48,234 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.7645717
2015-10-19 14:24:48,671 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.667
2015-10-19 14:24:48,962 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.52076733
2015-10-19 14:24:50,196 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.45561612
2015-10-19 14:24:50,686 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.667
2015-10-19 14:24:50,765 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.667
2015-10-19 14:24:50,997 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.34743196
2015-10-19 14:24:51,233 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.83178884
2015-10-19 14:24:51,671 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.667
2015-10-19 14:24:53,688 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.71592176
2015-10-19 14:24:53,771 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.667
2015-10-19 14:24:54,237 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.8915504
2015-10-19 14:24:54,301 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.45561612
2015-10-19 14:24:54,675 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.67753947
2015-10-19 14:24:55,149 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.54845476
2015-10-19 14:24:55,300 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.34743196
2015-10-19 14:24:56,693 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.75861263
2015-10-19 14:24:56,781 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.6974055
2015-10-19 14:24:57,252 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.9427445
2015-10-19 14:24:57,687 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.72788167
2015-10-19 14:24:58,519 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.45561612
2015-10-19 14:24:58,859 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.56380075
2015-10-19 14:24:59,496 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.34743196
2015-10-19 14:24:59,704 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.80727637
2015-10-19 14:24:59,787 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.747251
2015-10-19 14:25:00,059 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:25:00,060 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-19 14:25:00,060 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000012 to attempt_1445182159119_0003_r_000000_0
2015-10-19 14:25:00,060 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:7 RackLocal:3
2015-10-19 14:25:00,069 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:25:00,070 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:25:00,070 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000012 taskAttempt attempt_1445182159119_0003_r_000000_0
2015-10-19 14:25:00,070 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_r_000000_0
2015-10-19 14:25:00,070 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:25:00,236 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_r_000000_0 : 13562
2015-10-19 14:25:00,236 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_r_000000_0] using containerId: [container_1445182159119_0003_01_000012 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 14:25:00,237 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:25:00,237 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_r_000000
2015-10-19 14:25:00,237 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 14:25:00,267 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 0.993111
2015-10-19 14:25:00,691 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.77862716
2015-10-19 14:25:00,757 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000009_0 is : 1.0
2015-10-19 14:25:00,760 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0003_m_000009_0
2015-10-19 14:25:00,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:25:00,760 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000011 taskAttempt attempt_1445182159119_0003_m_000009_0
2015-10-19 14:25:00,760 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000009_0
2015-10-19 14:25:00,761 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:25:00,773 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:25:00,773 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0003_m_000009_0
2015-10-19 14:25:00,774 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:25:00,774 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-19 14:25:01,060 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:7 RackLocal:3
2015-10-19 14:25:01,062 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:25:02,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000011
2015-10-19 14:25:02,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:25:02,066 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:25:02,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000013 to attempt_1445182159119_0003_m_000001_1
2015-10-19 14:25:02,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:8 RackLocal:3
2015-10-19 14:25:02,066 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:25:02,067 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000001_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:25:02,067 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000013 taskAttempt attempt_1445182159119_0003_m_000001_1
2015-10-19 14:25:02,067 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000001_1
2015-10-19 14:25:02,068 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:25:02,081 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000001_1 : 13562
2015-10-19 14:25:02,082 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000001_1] using containerId: [container_1445182159119_0003_01_000013 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:25:02,082 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000001_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:25:02,082 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000001
2015-10-19 14:25:02,506 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0003_m_000002
2015-10-19 14:25:02,507 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:25:02,507 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0003_m_000002
2015-10-19 14:25:02,507 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:25:02,508 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:25:02,508 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000002_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:25:02,512 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.56380075
2015-10-19 14:25:02,707 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.8597938
2015-10-19 14:25:02,791 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.45561612
2015-10-19 14:25:02,807 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.80231
2015-10-19 14:25:03,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:8 RackLocal:3
2015-10-19 14:25:03,067 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:25:03,634 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.35433894
2015-10-19 14:25:03,707 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.8287134
2015-10-19 14:25:05,230 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:25:05,255 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000013 asked for a task
2015-10-19 14:25:05,256 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000013 given task: attempt_1445182159119_0003_m_000001_1
2015-10-19 14:25:05,714 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.9062507
2015-10-19 14:25:05,817 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.8543403
2015-10-19 14:25:05,969 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.56380075
2015-10-19 14:25:06,664 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.45561612
2015-10-19 14:25:06,714 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.88409746
2015-10-19 14:25:08,196 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.3823491
2015-10-19 14:25:08,733 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.9559282
2015-10-19 14:25:08,817 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.9090341
2015-10-19 14:25:09,729 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.93957746
2015-10-19 14:25:09,892 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.56380075
2015-10-19 14:25:10,956 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.45561612
2015-10-19 14:25:11,748 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 0.99696374
2015-10-19 14:25:11,819 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 0.9542106
2015-10-19 14:25:12,325 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.4175254
2015-10-19 14:25:12,600 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.13102192
2015-10-19 14:25:12,738 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 0.9832519
2015-10-19 14:25:13,212 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.56380075
2015-10-19 14:25:14,754 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 1.0
2015-10-19 14:25:14,834 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 1.0
2015-10-19 14:25:15,393 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.45561612
2015-10-19 14:25:15,616 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.13102192
2015-10-19 14:25:15,744 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 1.0
2015-10-19 14:25:16,544 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.455629
2015-10-19 14:25:16,623 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.56380075
2015-10-19 14:25:17,756 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 1.0
2015-10-19 14:25:17,840 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 1.0
2015-10-19 14:25:18,634 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.13102192
2015-10-19 14:25:18,763 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 1.0
2015-10-19 14:25:19,512 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.49666533
2015-10-19 14:25:20,038 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.56380075
2015-10-19 14:25:20,658 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.455629
2015-10-19 14:25:21,272 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000006_0 is : 1.0
2015-10-19 14:25:21,274 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0003_m_000006_0
2015-10-19 14:25:21,275 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:25:21,275 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000008 taskAttempt attempt_1445182159119_0003_m_000006_0
2015-10-19 14:25:21,275 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000006_0
2015-10-19 14:25:21,275 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:25:21,289 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:25:21,290 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0003_m_000006_0
2015-10-19 14:25:21,290 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:25:21,290 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-19 14:25:21,658 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.15541106
2015-10-19 14:25:22,087 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:8 RackLocal:3
2015-10-19 14:25:23,091 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000008
2015-10-19 14:25:23,092 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:8 RackLocal:3
2015-10-19 14:25:23,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:25:23,124 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.5318372
2015-10-19 14:25:23,602 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.56380075
2015-10-19 14:25:24,609 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.455629
2015-10-19 14:25:24,669 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.23921879
2015-10-19 14:25:25,354 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:25:25,486 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_r_000012 asked for a task
2015-10-19 14:25:25,486 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_r_000012 given task: attempt_1445182159119_0003_r_000000_0
2015-10-19 14:25:27,101 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.5940458
2015-10-19 14:25:27,664 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.56381613
2015-10-19 14:25:27,687 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.23921879
2015-10-19 14:25:29,121 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.455629
2015-10-19 14:25:30,389 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.6233565
2015-10-19 14:25:30,648 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 0 maxEvents 10000
2015-10-19 14:25:30,697 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.23921879
2015-10-19 14:25:31,712 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:31,970 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.56381613
2015-10-19 14:25:32,742 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:33,434 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.455629
2015-10-19 14:25:33,639 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.6588569
2015-10-19 14:25:33,704 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.34743196
2015-10-19 14:25:33,801 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:34,628 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.6588569
2015-10-19 14:25:34,835 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:35,908 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:36,113 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:25:36,113 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000014 to attempt_1445182159119_0003_m_000002_1
2015-10-19 14:25:36,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:9 RackLocal:3
2015-10-19 14:25:36,114 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:25:36,115 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000002_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:25:36,115 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000014 taskAttempt attempt_1445182159119_0003_m_000002_1
2015-10-19 14:25:36,115 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000002_1
2015-10-19 14:25:36,116 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:25:36,129 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000002_1 : 13562
2015-10-19 14:25:36,129 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000002_1] using containerId: [container_1445182159119_0003_01_000014 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:25:36,130 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000002_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:25:36,130 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000002
2015-10-19 14:25:36,380 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.56381613
2015-10-19 14:25:36,460 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:25:36,532 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0003_m_000000
2015-10-19 14:25:36,532 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:25:36,532 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0003_m_000000
2015-10-19 14:25:36,533 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:25:36,533 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:25:36,533 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:25:36,719 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.34743196
2015-10-19 14:25:36,908 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.667
2015-10-19 14:25:36,983 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:37,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:9 RackLocal:3
2015-10-19 14:25:37,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:25:37,618 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.455629
2015-10-19 14:25:38,060 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:39,094 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:39,628 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:25:39,736 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.34743196
2015-10-19 14:25:40,121 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.667
2015-10-19 14:25:40,121 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:40,126 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:25:40,127 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:25:40,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000015 to attempt_1445182159119_0003_m_000000_1
2015-10-19 14:25:40,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:25:40,128 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:25:40,130 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:25:40,131 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000015 taskAttempt attempt_1445182159119_0003_m_000000_1
2015-10-19 14:25:40,131 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000000_1
2015-10-19 14:25:40,131 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:25:40,590 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.56381613
2015-10-19 14:25:41,131 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:41,133 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 14:25:41,235 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000000_1 : 13562
2015-10-19 14:25:41,236 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000000_1] using containerId: [container_1445182159119_0003_01_000015 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 14:25:41,236 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:25:41,237 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000000
2015-10-19 14:25:41,807 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.455629
2015-10-19 14:25:42,437 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000007_0 is : 1.0
2015-10-19 14:25:42,440 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0003_m_000007_0
2015-10-19 14:25:42,440 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:25:42,441 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000009 taskAttempt attempt_1445182159119_0003_m_000007_0
2015-10-19 14:25:42,441 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000007_0
2015-10-19 14:25:42,441 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:25:42,458 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:25:42,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0003_m_000007_0
2015-10-19 14:25:42,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:25:42,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-19 14:25:42,508 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 14:25:42,754 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.34743196
2015-10-19 14:25:42,869 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:25:43,135 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:25:43,520 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.667
2015-10-19 14:25:43,573 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:25:44,109 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:25:44,123 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000014 asked for a task
2015-10-19 14:25:44,124 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000014 given task: attempt_1445182159119_0003_m_000002_1
2015-10-19 14:25:44,140 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000009
2015-10-19 14:25:44,141 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:25:44,141 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:25:44,607 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.56381613
2015-10-19 14:25:44,623 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:25:45,678 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:25:45,771 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.34743196
2015-10-19 14:25:45,871 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.455629
2015-10-19 14:25:46,088 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:25:46,712 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:25:46,963 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.667
2015-10-19 14:25:47,720 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:25:47,725 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000008_0 is : 1.0
2015-10-19 14:25:47,728 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0003_m_000008_0
2015-10-19 14:25:47,729 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:25:47,730 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000010 taskAttempt attempt_1445182159119_0003_m_000008_0
2015-10-19 14:25:47,730 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000008_0
2015-10-19 14:25:47,730 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:25:47,751 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:25:47,751 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0003_m_000008_0
2015-10-19 14:25:47,751 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:25:47,752 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-19 14:25:48,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:25:48,686 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 14:25:48,785 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.34743196
2015-10-19 14:25:48,962 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.56381613
2015-10-19 14:25:49,150 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000010
2015-10-19 14:25:49,151 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:25:49,151 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:25:49,233 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:25:49,718 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:25:49,869 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.455629
2015-10-19 14:25:50,375 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.667
2015-10-19 14:25:50,772 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:25:51,035 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.131014
2015-10-19 14:25:51,820 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:25:52,518 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:25:52,862 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:25:53,377 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.56381613
2015-10-19 14:25:53,770 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.667
2015-10-19 14:25:53,907 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:25:54,041 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.131014
2015-10-19 14:25:54,081 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.48298675
2015-10-19 14:25:54,796 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.455629
2015-10-19 14:25:54,948 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:25:55,816 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:25:55,976 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:25:57,054 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.131014
2015-10-19 14:25:57,139 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.667
2015-10-19 14:25:57,274 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:25:57,808 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.455629
2015-10-19 14:25:58,144 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.56381613
2015-10-19 14:25:58,342 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:25:58,375 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.5403063
2015-10-19 14:25:59,399 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:25:59,661 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:00,055 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.20597392
2015-10-19 14:26:00,382 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.667
2015-10-19 14:26:00,437 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:00,820 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.455629
2015-10-19 14:26:01,527 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:02,178 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.56381613
2015-10-19 14:26:02,618 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:02,659 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.5638294
2015-10-19 14:26:02,754 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:26:03,054 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.23919508
2015-10-19 14:26:03,210 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000015 asked for a task
2015-10-19 14:26:03,210 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000015 given task: attempt_1445182159119_0003_m_000000_1
2015-10-19 14:26:03,387 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:03,695 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:03,732 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.6744419
2015-10-19 14:26:03,824 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.5638294
2015-10-19 14:26:04,752 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:05,872 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:06,054 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.23919508
2015-10-19 14:26:06,114 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.601534
2015-10-19 14:26:06,376 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.5638294
2015-10-19 14:26:06,837 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.5638294
2015-10-19 14:26:06,955 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:07,138 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:07,431 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.6855403
2015-10-19 14:26:07,992 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:09,056 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.23919508
2015-10-19 14:26:09,367 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:09,689 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.6126083
2015-10-19 14:26:09,838 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.5638294
2015-10-19 14:26:09,891 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.5638294
2015-10-19 14:26:10,424 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:10,447 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:11,460 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:11,614 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.7015839
2015-10-19 14:26:12,059 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.3474061
2015-10-19 14:26:12,494 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:12,840 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.65396905
2015-10-19 14:26:13,142 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.65396905
2015-10-19 14:26:13,145 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.5638294
2015-10-19 14:26:13,552 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.6217286
2015-10-19 14:26:13,616 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:14,085 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:14,743 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:14,913 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.71385753
2015-10-19 14:26:15,073 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.3474061
2015-10-19 14:26:15,823 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:15,855 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.667
2015-10-19 14:26:16,342 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.5638294
2015-10-19 14:26:16,914 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:16,956 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.6396403
2015-10-19 14:26:17,664 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:17,955 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:18,073 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.3474061
2015-10-19 14:26:18,216 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.72557676
2015-10-19 14:26:18,856 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.667
2015-10-19 14:26:19,344 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:19,978 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.56766295
2015-10-19 14:26:20,396 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:20,558 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.66504914
2015-10-19 14:26:20,934 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:21,072 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.4373787
2015-10-19 14:26:21,458 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:21,856 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.667
2015-10-19 14:26:22,148 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.7402097
2015-10-19 14:26:22,499 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.66504914
2015-10-19 14:26:22,510 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:23,332 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.59469485
2015-10-19 14:26:23,570 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:24,021 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.667
2015-10-19 14:26:24,074 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.45561612
2015-10-19 14:26:24,530 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:24,643 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:24,855 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.69428146
2015-10-19 14:26:25,426 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.75126964
2015-10-19 14:26:25,729 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:26,797 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:26,876 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.618144
2015-10-19 14:26:27,072 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.45561612
2015-10-19 14:26:27,619 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.667
2015-10-19 14:26:27,774 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:27,836 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:27,854 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.7423823
2015-10-19 14:26:28,655 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.7652275
2015-10-19 14:26:28,883 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:29,927 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.019865144
2015-10-19 14:26:29,932 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:30,077 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.45561612
2015-10-19 14:26:30,855 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.7944052
2015-10-19 14:26:30,858 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:30,956 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:31,053 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.64289844
2015-10-19 14:26:31,304 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.667
2015-10-19 14:26:31,772 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.78174275
2015-10-19 14:26:31,970 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:32,996 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:33,091 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.5598919
2015-10-19 14:26:33,853 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.8420248
2015-10-19 14:26:33,939 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:34,047 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:34,357 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.044568148
2015-10-19 14:26:34,910 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.8006351
2015-10-19 14:26:35,073 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:35,778 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.667
2015-10-19 14:26:35,824 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.667
2015-10-19 14:26:35,826 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.667
2015-10-19 14:26:36,092 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:36,104 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.56381613
2015-10-19 14:26:36,854 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.8968696
2015-10-19 14:26:37,005 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:37,119 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:38,039 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.82261497
2015-10-19 14:26:38,121 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:39,103 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.56381613
2015-10-19 14:26:39,134 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:39,375 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.08532854
2015-10-19 14:26:39,852 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 0.9530276
2015-10-19 14:26:40,066 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:40,140 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:40,452 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.667
2015-10-19 14:26:40,505 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.667
2015-10-19 14:26:41,179 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:41,193 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_0 is : 0.8421768
2015-10-19 14:26:42,103 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.61209846
2015-10-19 14:26:42,209 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:42,854 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 1.0
2015-10-19 14:26:43,205 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:43,416 FATAL [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Task: attempt_1445182159119_0003_m_000000_0 - failed due to FSError: java.io.IOException: There is not enough space on the disk
2015-10-19 14:26:43,416 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Diagnostics report from attempt_1445182159119_0003_m_000000_0: FSError: java.io.IOException: There is not enough space on the disk
2015-10-19 14:26:43,416 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000000_0: FSError: java.io.IOException: There is not enough space on the disk
2015-10-19 14:26:43,416 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_0 TaskAttempt Transitioned from RUNNING to FAIL_CONTAINER_CLEANUP
2015-10-19 14:26:43,417 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000002 taskAttempt attempt_1445182159119_0003_m_000000_0
2015-10-19 14:26:43,417 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000000_0
2015-10-19 14:26:43,418 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:26:43,511 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.61209846
2015-10-19 14:26:43,554 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 14:26:43,554 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_0 is : 0.0
2015-10-19 14:26:43,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_0 TaskAttempt Transitioned from FAIL_CONTAINER_CLEANUP to FAIL_TASK_CLEANUP
2015-10-19 14:26:43,590 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 14:26:43,596 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445182159119_0003_m_000000_0
2015-10-19 14:26:43,597 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_0 TaskAttempt Transitioned from FAIL_TASK_CLEANUP to FAILED
2015-10-19 14:26:43,598 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: 1 failures on node 04DN8IQ.fareast.corp.microsoft.com
2015-10-19 14:26:43,612 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0003_m_000000
2015-10-19 14:26:43,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0003_m_000000
2015-10-19 14:26:43,612 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 14:26:43,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:26:43,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:26:43,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:26:43,614 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Added attempt_1445182159119_0003_m_000000_2 to list of failed maps
2015-10-19 14:26:43,668 FATAL [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Task: attempt_1445182159119_0003_r_000000_0 - exited : org.apache.hadoop.mapreduce.task.reduce.Shuffle$ShuffleError: error in shuffle in fetcher#3
	at org.apache.hadoop.mapreduce.task.reduce.Shuffle.run(Shuffle.java:134)
	at org.apache.hadoop.mapred.ReduceTask.run(ReduceTask.java:376)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
Caused by: org.apache.hadoop.fs.FSError: java.io.IOException: There is not enough space on the disk
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:248)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.write(BufferedOutputStream.java:126)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.write(FSDataOutputStream.java:58)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.fs.ChecksumFileSystem$ChecksumFSOutputSummer.writeChunk(ChecksumFileSystem.java:414)
	at org.apache.hadoop.fs.FSOutputSummer.writeChecksumChunks(FSOutputSummer.java:206)
	at org.apache.hadoop.fs.FSOutputSummer.write1(FSOutputSummer.java:124)
	at org.apache.hadoop.fs.FSOutputSummer.write(FSOutputSummer.java:110)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.write(FSDataOutputStream.java:58)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput.shuffle(OnDiskMapOutput.java:103)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.copyMapOutput(Fetcher.java:534)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.copyFromHost(Fetcher.java:329)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.run(Fetcher.java:193)
Caused by: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	... 14 more

2015-10-19 14:26:43,668 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Diagnostics report from attempt_1445182159119_0003_r_000000_0: Error: org.apache.hadoop.mapreduce.task.reduce.Shuffle$ShuffleError: error in shuffle in fetcher#3
	at org.apache.hadoop.mapreduce.task.reduce.Shuffle.run(Shuffle.java:134)
	at org.apache.hadoop.mapred.ReduceTask.run(ReduceTask.java:376)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
Caused by: org.apache.hadoop.fs.FSError: java.io.IOException: There is not enough space on the disk
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:248)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.write(BufferedOutputStream.java:126)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.write(FSDataOutputStream.java:58)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.fs.ChecksumFileSystem$ChecksumFSOutputSummer.writeChunk(ChecksumFileSystem.java:414)
	at org.apache.hadoop.fs.FSOutputSummer.writeChecksumChunks(FSOutputSummer.java:206)
	at org.apache.hadoop.fs.FSOutputSummer.write1(FSOutputSummer.java:124)
	at org.apache.hadoop.fs.FSOutputSummer.write(FSOutputSummer.java:110)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.write(FSDataOutputStream.java:58)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput.shuffle(OnDiskMapOutput.java:103)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.copyMapOutput(Fetcher.java:534)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.copyFromHost(Fetcher.java:329)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.run(Fetcher.java:193)
Caused by: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	... 14 more

2015-10-19 14:26:43,668 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_r_000000_0: Error: org.apache.hadoop.mapreduce.task.reduce.Shuffle$ShuffleError: error in shuffle in fetcher#3
	at org.apache.hadoop.mapreduce.task.reduce.Shuffle.run(Shuffle.java:134)
	at org.apache.hadoop.mapred.ReduceTask.run(ReduceTask.java:376)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
Caused by: org.apache.hadoop.fs.FSError: java.io.IOException: There is not enough space on the disk
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:248)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.write(BufferedOutputStream.java:126)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.write(FSDataOutputStream.java:58)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.fs.ChecksumFileSystem$ChecksumFSOutputSummer.writeChunk(ChecksumFileSystem.java:414)
	at org.apache.hadoop.fs.FSOutputSummer.writeChecksumChunks(FSOutputSummer.java:206)
	at org.apache.hadoop.fs.FSOutputSummer.write1(FSOutputSummer.java:124)
	at org.apache.hadoop.fs.FSOutputSummer.write(FSOutputSummer.java:110)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.write(FSDataOutputStream.java:58)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput.shuffle(OnDiskMapOutput.java:103)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.copyMapOutput(Fetcher.java:534)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.copyFromHost(Fetcher.java:329)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.run(Fetcher.java:193)
Caused by: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	... 14 more

2015-10-19 14:26:43,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_r_000000_0 TaskAttempt Transitioned from RUNNING to FAIL_CONTAINER_CLEANUP
2015-10-19 14:26:43,669 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000012 taskAttempt attempt_1445182159119_0003_r_000000_0
2015-10-19 14:26:43,669 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_r_000000_0
2015-10-19 14:26:43,669 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:26:43,743 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_r_000000_0 TaskAttempt Transitioned from FAIL_CONTAINER_CLEANUP to FAIL_TASK_CLEANUP
2015-10-19 14:26:43,743 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 14:26:43,746 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445182159119_0003_r_000000_0
2015-10-19 14:26:43,746 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_r_000000_0 TaskAttempt Transitioned from FAIL_TASK_CLEANUP to FAILED
2015-10-19 14:26:43,746 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: 2 failures on node 04DN8IQ.fareast.corp.microsoft.com
2015-10-19 14:26:43,747 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_r_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:26:44,221 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:26:44,222 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-19 14:26:44,223 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 14:26:44,223 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.7 totalResourceLimit:<memory:9216, vCores:-18> finalMapResourceLimit:<memory:4608, vCores:-9> finalReduceResourceLimit:<memory:4608, vCores:-9> netScheduledMapResource:<memory:7168, vCores:7> netScheduledReduceResource:<memory:1024, vCores:1>
2015-10-19 14:26:44,223 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-19 14:26:44,223 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:26:44,657 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.12812921
2015-10-19 14:26:45,108 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.667
2015-10-19 14:26:45,230 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=1 release= 0 newContainers=1 finishedContainers=2 resourcelimit=<memory:5120, vCores:-22> knownNMs=4
2015-10-19 14:26:45,231 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000002
2015-10-19 14:26:45,231 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000012
2015-10-19 14:26:45,231 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:26:45,231 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:26:45,231 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:26:45,232 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigning container Container: [ContainerId: container_1445182159119_0003_01_000016, NodeId: 04DN8IQ.fareast.corp.microsoft.com:64260, NodeHttpAddress: 04DN8IQ.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 5, Token: Token { kind: ContainerToken, service: *************:64260 }, ] to fast fail map
2015-10-19 14:26:45,232 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned from earlierFailedMaps
2015-10-19 14:26:45,232 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000016 to attempt_1445182159119_0003_m_000000_2
2015-10-19 14:26:45,233 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:6 AssignedReds:0 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:26:45,233 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:26:45,238 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:26:45,238 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000016 taskAttempt attempt_1445182159119_0003_m_000000_2
2015-10-19 14:26:45,239 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000000_2
2015-10-19 14:26:45,239 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:26:45,338 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000000_2 : 13562
2015-10-19 14:26:45,338 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000000_2] using containerId: [container_1445182159119_0003_01_000016 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 14:26:45,339 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:26:45,339 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000000
2015-10-19 14:26:45,473 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.667
2015-10-19 14:26:45,473 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.667
2015-10-19 14:26:45,858 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 1.0
2015-10-19 14:26:46,240 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=1 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:4096, vCores:-23> knownNMs=4
2015-10-19 14:26:46,240 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:26:46,240 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-19 14:26:46,240 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000017 to attempt_1445182159119_0003_r_000000_1
2015-10-19 14:26:46,241 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:26:46,242 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:26:46,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_r_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:26:46,243 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000017 taskAttempt attempt_1445182159119_0003_r_000000_1
2015-10-19 14:26:46,244 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_r_000000_1
2015-10-19 14:26:46,244 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:26:46,264 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_r_000000_1 : 13562
2015-10-19 14:26:46,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_r_000000_1] using containerId: [container_1445182159119_0003_01_000017 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 14:26:46,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_r_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:26:46,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_r_000000
2015-10-19 14:26:47,032 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_1 is : 1.0
2015-10-19 14:26:47,034 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0003_m_000001_1
2015-10-19 14:26:47,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000001_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:26:47,035 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000013 taskAttempt attempt_1445182159119_0003_m_000001_1
2015-10-19 14:26:47,035 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000001_1
2015-10-19 14:26:47,035 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:26:47,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000001_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:26:47,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0003_m_000001_1
2015-10-19 14:26:47,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0003_m_000001_0
2015-10-19 14:26:47,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:26:47,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-19 14:26:47,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000001_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 14:26:47,050 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000003 taskAttempt attempt_1445182159119_0003_m_000001_0
2015-10-19 14:26:47,051 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000001_0
2015-10-19 14:26:47,052 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:26:47,241 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:26:47,244 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-24> knownNMs=4
2015-10-19 14:26:47,663 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000001_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 14:26:47,664 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 14:26:47,666 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445182159119_0003_m_000001_0
2015-10-19 14:26:47,666 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000001_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 14:26:48,133 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.667
2015-10-19 14:26:48,245 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000013
2015-10-19 14:26:48,246 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:26:48,246 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000001_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:26:48,458 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.13101934
2015-10-19 14:26:48,740 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:26:48,768 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000016 asked for a task
2015-10-19 14:26:48,769 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000016 given task: attempt_1445182159119_0003_m_000000_2
2015-10-19 14:26:49,359 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:26:49,372 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.667
2015-10-19 14:26:49,376 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_r_000017 asked for a task
2015-10-19 14:26:49,376 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_r_000017 given task: attempt_1445182159119_0003_r_000000_1
2015-10-19 14:26:49,422 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000001_0 is : 0.667
2015-10-19 14:26:49,489 INFO [Socket Reader #1 for port 43581] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 43581: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 14:26:50,270 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 0 maxEvents 10000
2015-10-19 14:26:51,133 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.667
2015-10-19 14:26:51,249 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000003
2015-10-19 14:26:51,249 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:26:51,249 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:26:51,277 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 14:26:52,004 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.13101934
2015-10-19 14:26:52,277 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 14:26:52,729 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.6840816
2015-10-19 14:26:53,277 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 14:26:54,149 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.667
2015-10-19 14:26:54,277 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 14:26:55,255 INFO [Socket Reader #1 for port 43581] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 43581: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 14:26:55,259 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445182159119_0003_m_000000_2 because it is running on unusable node:04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:26:55,259 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000016
2015-10-19 14:26:55,259 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_2 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 14:26:55,259 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:26:55,260 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000000_2: Container released on a *lost* node
2015-10-19 14:26:55,260 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000016 taskAttempt attempt_1445182159119_0003_m_000000_2
2015-10-19 14:26:55,260 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000000_2
2015-10-19 14:26:55,260 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 14:26:55,276 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 14:26:55,306 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.13101934
2015-10-19 14:26:55,379 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_2 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 14:26:55,379 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 14:26:55,381 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445182159119_0003_m_000000_2
2015-10-19 14:26:55,381 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_2 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 14:26:55,382 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:26:55,382 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:26:55,382 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_3 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 14:26:55,382 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Added attempt_1445182159119_0003_m_000000_3 to list of failed maps
2015-10-19 14:26:55,947 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.7123673
2015-10-19 14:26:56,219 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.10000001
2015-10-19 14:26:56,260 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:26:56,261 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:4096, vCores:-16> knownNMs=3
2015-10-19 14:26:56,277 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 14:26:57,147 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.667
2015-10-19 14:26:57,264 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 14:26:57,264 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigning container Container: [ContainerId: container_1445182159119_0003_01_000018, NodeId: MININT-FNANLI5.fareast.corp.microsoft.com:59190, NodeHttpAddress: MININT-FNANLI5.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 5, Token: Token { kind: ContainerToken, service: *************:59190 }, ] to fast fail map
2015-10-19 14:26:57,264 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned from earlierFailedMaps
2015-10-19 14:26:57,264 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0003_01_000018 to attempt_1445182159119_0003_m_000000_3
2015-10-19 14:26:57,265 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:26:57,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 14:26:57,267 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_3 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 14:26:57,267 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0003_01_000018 taskAttempt attempt_1445182159119_0003_m_000000_3
2015-10-19 14:26:57,267 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0003_m_000000_3
2015-10-19 14:26:57,267 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:26:57,276 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:26:57,677 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0003_m_000000_3 : 13562
2015-10-19 14:26:57,677 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0003_m_000000_3] using containerId: [container_1445182159119_0003_01_000018 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 14:26:57,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_3 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 14:26:57,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0003_m_000000
2015-10-19 14:26:58,268 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0003: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-18> knownNMs=3
2015-10-19 14:26:58,277 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:26:59,025 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.13101934
2015-10-19 14:26:59,226 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.13333334
2015-10-19 14:26:59,277 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:00,061 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.7281528
2015-10-19 14:27:00,148 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.667
2015-10-19 14:27:00,278 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:01,278 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:02,229 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.20000002
2015-10-19 14:27:02,277 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:02,390 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.13101934
2015-10-19 14:27:03,277 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:03,693 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.74235725
2015-10-19 14:27:04,278 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:05,250 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.20000002
2015-10-19 14:27:05,277 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:06,155 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.71830463
2015-10-19 14:27:06,278 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:07,277 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:07,286 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.13101934
2015-10-19 14:27:08,115 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.7552349
2015-10-19 14:27:08,261 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.20000002
2015-10-19 14:27:08,277 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:08,904 INFO [Socket Reader #1 for port 43581] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0003 (auth:SIMPLE)
2015-10-19 14:27:09,168 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.74836946
2015-10-19 14:27:09,277 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:09,385 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0003_m_000018 asked for a task
2015-10-19 14:27:09,385 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0003_m_000018 given task: attempt_1445182159119_0003_m_000000_3
2015-10-19 14:27:10,277 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:11,277 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.20000002
2015-10-19 14:27:11,277 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:12,048 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.14394787
2015-10-19 14:27:12,184 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.7793102
2015-10-19 14:27:12,193 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.76485395
2015-10-19 14:27:12,277 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:13,277 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:14,277 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:14,290 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.20000002
2015-10-19 14:27:15,195 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.81219435
2015-10-19 14:27:15,277 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:15,790 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.7786617
2015-10-19 14:27:15,868 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.19508351
2015-10-19 14:27:16,278 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:17,278 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:17,292 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.20000002
2015-10-19 14:27:18,203 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.8517257
2015-10-19 14:27:18,278 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:19,278 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:19,671 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.7933624
2015-10-19 14:27:19,715 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.23921585
2015-10-19 14:27:20,278 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:21,212 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.8804382
2015-10-19 14:27:21,278 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:22,278 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:23,278 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:23,383 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.8092228
2015-10-19 14:27:23,518 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.23921585
2015-10-19 14:27:24,219 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.91813046
2015-10-19 14:27:24,278 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:25,278 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:26,279 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:27,229 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.9493637
2015-10-19 14:27:27,243 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.23921585
2015-10-19 14:27:27,270 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.8245679
2015-10-19 14:27:27,279 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:28,279 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:29,278 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:30,236 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 0.98229724
2015-10-19 14:27:30,279 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:31,280 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:31,619 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.23921585
2015-10-19 14:27:31,685 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.84040666
2015-10-19 14:27:32,280 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:33,255 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 1.0
2015-10-19 14:27:33,283 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:33,946 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.06936927
2015-10-19 14:27:34,283 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:35,283 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:35,677 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.23921585
2015-10-19 14:27:35,791 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.85313416
2015-10-19 14:27:36,270 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 1.0
2015-10-19 14:27:36,282 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:37,283 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:38,264 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.13101934
2015-10-19 14:27:38,283 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:39,132 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_1 is : 1.0
2015-10-19 14:27:39,134 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0003_m_000002_1
2015-10-19 14:27:39,134 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000002_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:27:39,135 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000014 taskAttempt attempt_1445182159119_0003_m_000002_1
2015-10-19 14:27:39,135 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000002_1
2015-10-19 14:27:39,136 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:27:39,153 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000002_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:27:39,153 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0003_m_000002_1
2015-10-19 14:27:39,153 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0003_m_000002_0
2015-10-19 14:27:39,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:27:39,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-19 14:27:39,155 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000002_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 14:27:39,155 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000004 taskAttempt attempt_1445182159119_0003_m_000002_0
2015-10-19 14:27:39,155 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000002_0
2015-10-19 14:27:39,156 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:27:39,282 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 10 maxEvents 10000
2015-10-19 14:27:39,320 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:27:39,838 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000002_0 is : 0.8661755
2015-10-19 14:27:39,889 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.23921585
2015-10-19 14:27:40,287 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000002_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 14:27:40,288 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 14:27:40,290 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445182159119_0003_m_000002_0
2015-10-19 14:27:40,290 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000002_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 14:27:40,325 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000014
2015-10-19 14:27:40,325 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:27:40,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000002_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:27:42,294 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.13101934
2015-10-19 14:27:42,541 INFO [Socket Reader #1 for port 43581] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 43581: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 14:27:43,733 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.23921585
2015-10-19 14:27:44,330 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000004
2015-10-19 14:27:44,330 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:27:44,330 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:27:46,735 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.13101934
2015-10-19 14:27:47,677 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.23921585
2015-10-19 14:27:50,737 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.13101934
2015-10-19 14:27:50,805 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:27:51,201 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.24947362
2015-10-19 14:27:51,799 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:27:52,799 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:27:53,319 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:27:53,798 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:27:54,506 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.13101934
2015-10-19 14:27:54,799 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:27:54,962 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.2794086
2015-10-19 14:27:55,799 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:27:56,334 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:27:56,799 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:27:57,799 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:27:58,620 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.13101934
2015-10-19 14:27:58,799 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:27:59,293 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.31753772
2015-10-19 14:27:59,349 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:27:59,798 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:00,799 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:01,800 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:02,361 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:02,799 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:03,328 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.13101934
2015-10-19 14:28:03,800 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:04,018 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.3474062
2015-10-19 14:28:04,799 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:05,388 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:05,806 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:06,807 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:07,662 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.13101934
2015-10-19 14:28:07,807 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:08,039 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.3474062
2015-10-19 14:28:08,405 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:08,808 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:09,807 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:10,807 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:11,201 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.13763198
2015-10-19 14:28:11,419 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:11,742 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.3474062
2015-10-19 14:28:11,807 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:12,807 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:13,807 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:14,436 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:14,807 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:15,088 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.20753045
2015-10-19 14:28:15,772 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.3474062
2015-10-19 14:28:15,807 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:16,807 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:17,453 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:17,808 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:18,808 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:18,895 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.23921585
2015-10-19 14:28:19,808 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:20,315 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.3474062
2015-10-19 14:28:20,467 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:20,807 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:21,808 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:22,808 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:22,980 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.23921585
2015-10-19 14:28:23,483 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:23,808 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:24,414 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.3474062
2015-10-19 14:28:24,808 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:25,808 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:26,499 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:26,771 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.23921585
2015-10-19 14:28:26,807 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:27,808 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:28,378 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.3474062
2015-10-19 14:28:28,808 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:29,514 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:29,808 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:30,552 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.23921585
2015-10-19 14:28:30,808 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:31,808 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:32,155 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.3474062
2015-10-19 14:28:32,531 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:32,809 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:33,809 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:34,154 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.23921585
2015-10-19 14:28:34,808 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:35,546 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:35,739 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.37648684
2015-10-19 14:28:35,809 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:36,809 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:37,710 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.23921585
2015-10-19 14:28:37,808 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:38,561 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:38,809 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:39,383 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.4035195
2015-10-19 14:28:39,809 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:40,808 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:41,577 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:41,614 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.23921585
2015-10-19 14:28:41,809 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:42,809 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:43,117 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.42305997
2015-10-19 14:28:43,808 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:44,589 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:44,809 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:45,409 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.24719004
2015-10-19 14:28:45,810 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:46,810 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:46,857 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.45074528
2015-10-19 14:28:47,610 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:47,810 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:48,811 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:49,757 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.27454844
2015-10-19 14:28:49,810 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:50,627 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:50,812 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:51,564 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.45563135
2015-10-19 14:28:51,812 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:52,812 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:53,643 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:53,812 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:54,812 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:54,876 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.3406613
2015-10-19 14:28:55,812 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:56,196 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.45563135
2015-10-19 14:28:56,661 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:56,813 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:57,813 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:58,813 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:28:59,553 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.3474062
2015-10-19 14:28:59,674 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:28:59,813 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:00,665 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.45563135
2015-10-19 14:29:00,813 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:01,816 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:02,698 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:02,820 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:03,685 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.3474062
2015-10-19 14:29:03,820 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:04,821 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:05,039 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.45563135
2015-10-19 14:29:05,723 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:05,829 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:06,829 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:07,829 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:08,089 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.3474062
2015-10-19 14:29:08,738 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:08,828 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:09,562 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.45563135
2015-10-19 14:29:09,829 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:10,829 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:11,753 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:11,828 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:12,716 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.3474062
2015-10-19 14:29:12,829 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:13,829 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:14,000 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.45563135
2015-10-19 14:29:14,770 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:14,829 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:15,829 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:16,829 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:17,073 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.3474062
2015-10-19 14:29:17,785 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:17,828 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:18,727 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.45563135
2015-10-19 14:29:18,830 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:19,830 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:20,802 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:20,829 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:21,431 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.3474062
2015-10-19 14:29:21,830 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:22,830 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:23,482 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.4776002
2015-10-19 14:29:23,825 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:23,837 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:24,838 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:25,837 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:26,093 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.3474062
2015-10-19 14:29:26,837 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:26,840 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:27,729 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.5497791
2015-10-19 14:29:27,838 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:28,837 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:29,838 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:29,856 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:30,292 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.3474062
2015-10-19 14:29:30,839 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:31,838 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:31,894 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.56380075
2015-10-19 14:29:32,838 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:32,873 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:33,838 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:34,058 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.35661992
2015-10-19 14:29:34,838 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:35,696 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.56380075
2015-10-19 14:29:35,839 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:35,887 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:36,838 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:37,753 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.3745329
2015-10-19 14:29:37,838 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:38,839 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:38,905 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:39,689 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.56380075
2015-10-19 14:29:39,839 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:40,838 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:41,671 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.39733002
2015-10-19 14:29:41,839 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:41,920 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:42,839 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:43,839 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:44,093 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.56380075
2015-10-19 14:29:44,854 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:44,933 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:45,854 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:46,063 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.4253407
2015-10-19 14:29:46,854 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:47,855 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:47,951 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:48,464 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.56380075
2015-10-19 14:29:48,855 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:49,856 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:50,651 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.45204872
2015-10-19 14:29:50,856 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:50,972 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:51,855 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:52,855 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.56380075
2015-10-19 14:29:52,856 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:53,855 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:53,984 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:54,855 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:55,131 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.45563135
2015-10-19 14:29:55,856 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:56,856 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:56,999 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:29:57,134 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.56380075
2015-10-19 14:29:57,856 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:58,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:29:59,242 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.45563135
2015-10-19 14:29:59,856 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:00,014 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:00,856 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:01,415 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.5653053
2015-10-19 14:30:01,856 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:02,856 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:03,032 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:03,511 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.45563135
2015-10-19 14:30:03,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:04,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:05,747 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.638337
2015-10-19 14:30:05,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:06,048 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:06,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:07,858 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:08,259 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.45563135
2015-10-19 14:30:08,636 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.638337
2015-10-19 14:30:08,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:09,064 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:09,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:10,288 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.667
2015-10-19 14:30:10,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:11,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:12,079 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:12,589 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.45563135
2015-10-19 14:30:12,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:13,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:14,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:14,871 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.667
2015-10-19 14:30:15,093 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:15,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:16,840 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.45563135
2015-10-19 14:30:16,858 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:17,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:18,110 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:18,858 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:19,026 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.667
2015-10-19 14:30:19,858 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:20,726 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.45563135
2015-10-19 14:30:20,857 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:21,126 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:21,858 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:22,862 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:23,249 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.667
2015-10-19 14:30:23,863 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:24,147 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:24,862 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:25,319 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.4618149
2015-10-19 14:30:25,862 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:26,863 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:27,162 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:27,393 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.667
2015-10-19 14:30:27,862 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:28,862 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:29,493 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.4881989
2015-10-19 14:30:29,862 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:30,178 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:30,863 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:31,560 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.667
2015-10-19 14:30:31,863 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:32,863 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:33,194 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:33,577 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.50871474
2015-10-19 14:30:33,862 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:34,863 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:35,671 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.667
2015-10-19 14:30:35,863 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:36,209 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:36,862 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:37,707 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.5360512
2015-10-19 14:30:37,862 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:38,863 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:39,225 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:39,862 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:39,871 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.667
2015-10-19 14:30:40,862 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:41,864 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:42,240 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:42,496 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.56380075
2015-10-19 14:30:42,871 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:43,871 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:44,544 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.667
2015-10-19 14:30:44,871 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:45,265 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:45,871 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:46,763 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.56380075
2015-10-19 14:30:46,871 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:47,870 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:48,280 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:48,826 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.667
2015-10-19 14:30:48,871 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:49,870 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:50,871 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:50,921 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.56380075
2015-10-19 14:30:51,294 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:51,871 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:52,722 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.6670232
2015-10-19 14:30:52,870 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:53,871 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:54,312 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:54,871 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:55,174 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.56380075
2015-10-19 14:30:55,870 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:56,871 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:57,233 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.6796006
2015-10-19 14:30:57,328 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:30:57,872 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:58,873 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:30:59,529 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.56380075
2015-10-19 14:30:59,873 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:00,344 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:00,873 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:01,333 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.6920262
2015-10-19 14:31:01,872 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:02,873 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:03,359 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:03,644 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.56380075
2015-10-19 14:31:03,873 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:04,872 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:05,456 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.7043255
2015-10-19 14:31:05,873 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:06,375 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:06,873 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:07,872 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:08,066 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.56380075
2015-10-19 14:31:08,873 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:09,392 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:09,873 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:09,932 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.717186
2015-10-19 14:31:10,872 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:11,874 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:12,246 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.56380075
2015-10-19 14:31:12,407 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:12,874 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:13,873 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:14,621 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.7312124
2015-10-19 14:31:14,874 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:15,423 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:15,873 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:16,728 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.5692905
2015-10-19 14:31:16,873 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:17,873 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:18,438 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:18,635 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.74216837
2015-10-19 14:31:18,873 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:19,873 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:20,780 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.6253078
2015-10-19 14:31:20,873 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:21,454 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:21,873 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:22,660 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.75609595
2015-10-19 14:31:22,875 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:23,875 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:24,472 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:24,875 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:25,158 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.667
2015-10-19 14:31:25,158 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.667
2015-10-19 14:31:25,875 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:26,876 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:27,280 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.7692051
2015-10-19 14:31:27,489 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:27,876 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:28,876 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:29,554 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.667
2015-10-19 14:31:29,876 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:30,504 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:30,876 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:31,609 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.78257644
2015-10-19 14:31:31,876 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:32,876 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:33,519 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:33,875 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:34,124 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.667
2015-10-19 14:31:34,875 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:35,876 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:35,937 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.7954552
2015-10-19 14:31:36,535 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:36,876 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:37,876 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:38,482 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.667
2015-10-19 14:31:38,876 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:39,552 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:39,876 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:40,408 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.80976146
2015-10-19 14:31:40,876 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:41,877 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:42,567 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:42,876 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:42,967 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.667
2015-10-19 14:31:43,876 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:44,688 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.8215006
2015-10-19 14:31:44,877 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:45,583 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:45,876 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:46,876 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:47,160 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.667
2015-10-19 14:31:47,876 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:48,599 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:48,876 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:49,358 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.8354457
2015-10-19 14:31:49,876 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:50,876 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:51,359 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.667
2015-10-19 14:31:51,614 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:51,876 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:52,876 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:53,302 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.84837735
2015-10-19 14:31:53,876 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:54,630 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:54,876 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:55,035 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.667
2015-10-19 14:31:55,877 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:56,876 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:57,087 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.862674
2015-10-19 14:31:57,646 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:31:57,877 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:58,877 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:31:59,382 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.667
2015-10-19 14:31:59,876 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:00,662 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:00,876 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:01,145 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.8770442
2015-10-19 14:32:01,877 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:02,878 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:03,656 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.667
2015-10-19 14:32:03,679 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:03,878 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:04,878 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:05,232 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.89044356
2015-10-19 14:32:05,880 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:06,696 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:06,882 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:07,882 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:08,146 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.6798454
2015-10-19 14:32:08,881 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:09,667 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.90290374
2015-10-19 14:32:09,713 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:09,881 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:10,889 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:11,891 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:12,283 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.6934705
2015-10-19 14:32:12,738 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:12,891 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:13,890 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:13,920 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.91730976
2015-10-19 14:32:14,891 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:15,708 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.70533824
2015-10-19 14:32:15,753 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:15,891 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:16,890 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:17,891 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:17,913 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.93121535
2015-10-19 14:32:18,769 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:18,890 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:19,680 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.7186069
2015-10-19 14:32:19,891 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:20,892 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:21,786 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:21,891 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:22,035 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.9443259
2015-10-19 14:32:22,892 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:23,876 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.7316958
2015-10-19 14:32:23,893 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:24,802 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:24,892 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:25,892 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:26,078 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.9580449
2015-10-19 14:32:26,892 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:27,820 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:27,895 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:28,212 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.7468906
2015-10-19 14:32:28,899 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:29,901 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:30,080 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.97147244
2015-10-19 14:32:30,843 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:30,900 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:31,901 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:32,510 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.7617595
2015-10-19 14:32:32,901 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:33,858 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:33,901 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:34,200 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 0.9857557
2015-10-19 14:32:34,901 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:35,888 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.7739729
2015-10-19 14:32:35,901 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:36,874 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:36,901 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:37,901 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:38,223 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 1.0
2015-10-19 14:32:38,901 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:39,507 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.78734285
2015-10-19 14:32:39,697 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_1 is : 1.0
2015-10-19 14:32:39,701 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0003_m_000000_1
2015-10-19 14:32:39,701 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:32:39,702 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000015 taskAttempt attempt_1445182159119_0003_m_000000_1
2015-10-19 14:32:39,703 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000000_1
2015-10-19 14:32:39,704 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:32:39,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:32:39,764 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0003_m_000000_1
2015-10-19 14:32:39,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0003_m_000000_3
2015-10-19 14:32:39,766 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:32:39,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-19 14:32:39,767 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_3 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 14:32:39,768 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000018 taskAttempt attempt_1445182159119_0003_m_000000_3
2015-10-19 14:32:39,769 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_m_000000_3
2015-10-19 14:32:39,769 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 14:32:39,807 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_3 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 14:32:39,807 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 14:32:39,810 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out3/_temporary/1/_temporary/attempt_1445182159119_0003_m_000000_3
2015-10-19 14:32:39,811 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_m_000000_3 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 14:32:39,820 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:32:39,882 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:39,901 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 11 maxEvents 10000
2015-10-19 14:32:40,901 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:41,902 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:42,902 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:42,905 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:43,198 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_m_000000_3 is : 0.80444777
2015-10-19 14:32:43,569 INFO [Socket Reader #1 for port 43581] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 43581: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 14:32:43,902 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:44,902 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:45,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000015
2015-10-19 14:32:45,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0003_01_000018
2015-10-19 14:32:45,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000000_1: Container killed by the ApplicationMaster.

2015-10-19 14:32:45,835 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:32:45,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0003_m_000000_3: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 14:32:45,902 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:45,914 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:46,902 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:47,903 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:48,903 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:48,938 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:49,903 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:50,903 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:51,903 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:51,954 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:52,910 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:53,911 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:54,911 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:54,977 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:55,911 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:56,910 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:57,914 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:57,996 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:32:58,914 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:32:59,914 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:00,914 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:01,013 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:01,915 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:02,915 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:03,915 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:04,020 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:04,915 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:05,915 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:06,915 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:07,028 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:07,915 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:08,915 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:09,916 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:10,048 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:10,916 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:11,915 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:12,915 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:13,057 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:13,915 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:14,915 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:15,918 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:16,077 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:16,920 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:17,920 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:18,920 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:19,095 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:19,920 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:20,935 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:21,937 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:22,113 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:22,937 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:23,937 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:24,937 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:25,128 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:25,938 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:26,939 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:27,940 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:28,147 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:28,940 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:29,940 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:30,940 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:31,162 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:31,940 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:32,940 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:33,941 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:34,179 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:34,940 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:35,942 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:36,942 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:37,192 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:37,940 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:38,940 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:39,941 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:40,210 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:40,941 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:41,941 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:42,942 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:43,226 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:43,940 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:44,941 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:45,941 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:46,243 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:46,940 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:47,941 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:48,942 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:49,258 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:49,941 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:50,942 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:51,942 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:52,272 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:52,941 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:53,941 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:54,942 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:55,287 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:55,941 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:56,941 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:57,942 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:58,305 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:33:58,942 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:33:59,941 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:00,944 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:01,323 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:01,943 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:02,944 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:03,944 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:04,339 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:04,943 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:05,943 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:06,950 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:07,356 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:07,949 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:08,950 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:09,950 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:10,375 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:10,950 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:11,950 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:12,951 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:13,392 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:13,949 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:14,950 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:15,957 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:16,413 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:16,957 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:17,957 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:18,957 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:19,430 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:19,957 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:20,957 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:21,957 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:22,443 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:22,957 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:23,957 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:24,956 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:25,459 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:25,957 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:26,957 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:27,957 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:28,475 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:28,957 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:29,957 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:30,957 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:31,492 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:31,957 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:32,957 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:33,957 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:34,508 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:34,958 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:35,957 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:36,957 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:37,524 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:37,957 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:38,958 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:39,958 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:40,544 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:40,959 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:41,959 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:42,960 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:43,558 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:43,960 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:44,960 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:45,959 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:46,573 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:46,960 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:47,960 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:48,960 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:49,588 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:49,961 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:50,960 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:51,961 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:52,604 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:52,960 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:53,960 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:54,960 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:55,620 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:55,961 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:56,961 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:57,960 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:58,635 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:34:58,960 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:34:59,960 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:35:00,961 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:35:01,650 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:35:01,961 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:35:02,960 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:35:03,960 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0003_r_000000_1. startIndex 12 maxEvents 10000
2015-10-19 14:35:04,493 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:35:04,520 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.3
2015-10-19 14:35:04,666 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.6666667
2015-10-19 14:35:07,685 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.6688757
2015-10-19 14:35:10,706 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.66958535
2015-10-19 14:35:13,714 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.6702639
2015-10-19 14:35:16,721 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.6705291
2015-10-19 14:35:19,725 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.6713043
2015-10-19 14:35:22,754 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.6731969
2015-10-19 14:35:25,771 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.6758898
2015-10-19 14:35:28,780 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.6784183
2015-10-19 14:35:31,802 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.68063647
2015-10-19 14:35:34,818 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.6829553
2015-10-19 14:35:37,833 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.6848821
2015-10-19 14:35:40,850 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.6867977
2015-10-19 14:35:43,865 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.6892523
2015-10-19 14:35:46,877 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.69149494
2015-10-19 14:35:49,894 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.69423753
2015-10-19 14:35:52,906 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.6968075
2015-10-19 14:35:55,925 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.6996212
2015-10-19 14:35:58,944 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7009767
2015-10-19 14:36:01,954 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7023847
2015-10-19 14:36:04,970 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.70439094
2015-10-19 14:36:07,987 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7061371
2015-10-19 14:36:11,005 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7081381
2015-10-19 14:36:14,021 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7110281
2015-10-19 14:36:17,033 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7124707
2015-10-19 14:36:20,046 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7138832
2015-10-19 14:36:23,053 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.71590286
2015-10-19 14:36:26,070 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7170874
2015-10-19 14:36:29,086 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.71907777
2015-10-19 14:36:32,101 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7212076
2015-10-19 14:36:35,117 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7218264
2015-10-19 14:36:38,131 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7236618
2015-10-19 14:36:41,155 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7249052
2015-10-19 14:36:44,172 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7260286
2015-10-19 14:36:47,188 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7276483
2015-10-19 14:36:50,203 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.72968644
2015-10-19 14:36:53,220 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7316415
2015-10-19 14:36:56,236 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.73292214
2015-10-19 14:36:59,248 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.73552686
2015-10-19 14:37:02,269 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7381913
2015-10-19 14:37:05,284 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.74134576
2015-10-19 14:37:08,299 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7445352
2015-10-19 14:37:11,315 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7475349
2015-10-19 14:37:14,333 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.75015986
2015-10-19 14:37:17,341 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.75312287
2015-10-19 14:37:20,365 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7558707
2015-10-19 14:37:23,374 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.75840276
2015-10-19 14:37:26,386 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7608371
2015-10-19 14:37:29,390 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7635684
2015-10-19 14:37:32,410 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.76620346
2015-10-19 14:37:35,427 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.76882696
2015-10-19 14:37:38,442 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7715712
2015-10-19 14:37:41,455 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7744453
2015-10-19 14:37:44,473 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.77740467
2015-10-19 14:37:47,489 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7799399
2015-10-19 14:37:50,505 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7823702
2015-10-19 14:37:53,517 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.78516906
2015-10-19 14:37:56,534 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.78772575
2015-10-19 14:37:59,557 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.79047495
2015-10-19 14:38:02,573 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.79323554
2015-10-19 14:38:05,585 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.79617476
2015-10-19 14:38:08,605 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.79863393
2015-10-19 14:38:11,620 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.7999213
2015-10-19 14:38:14,636 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8013482
2015-10-19 14:38:17,652 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.80296206
2015-10-19 14:38:20,668 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.80411994
2015-10-19 14:38:23,689 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8052896
2015-10-19 14:38:26,700 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8065354
2015-10-19 14:38:29,718 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8079183
2015-10-19 14:38:32,735 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8102268
2015-10-19 14:38:35,754 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.81229764
2015-10-19 14:38:38,772 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8144257
2015-10-19 14:38:41,790 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8162037
2015-10-19 14:38:44,805 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.81715775
2015-10-19 14:38:47,825 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8191873
2015-10-19 14:38:50,843 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.82099485
2015-10-19 14:38:53,860 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8229746
2015-10-19 14:38:56,877 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.82492596
2015-10-19 14:38:59,894 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.82670945
2015-10-19 14:39:02,909 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8286005
2015-10-19 14:39:05,929 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.83048993
2015-10-19 14:39:08,942 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8330031
2015-10-19 14:39:11,961 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8364033
2015-10-19 14:39:14,978 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8393749
2015-10-19 14:39:17,997 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.84268713
2015-10-19 14:39:21,013 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8460039
2015-10-19 14:39:24,021 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.848999
2015-10-19 14:39:27,037 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.85233194
2015-10-19 14:39:30,055 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8557689
2015-10-19 14:39:33,070 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8589375
2015-10-19 14:39:36,082 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8622165
2015-10-19 14:39:39,090 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8652506
2015-10-19 14:39:42,106 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8669336
2015-10-19 14:39:45,127 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.86907643
2015-10-19 14:39:48,145 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8709988
2015-10-19 14:39:51,162 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.87300754
2015-10-19 14:39:54,179 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.87440395
2015-10-19 14:39:57,197 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8763917
2015-10-19 14:40:00,213 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.87828624
2015-10-19 14:40:03,233 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.87992984
2015-10-19 14:40:06,248 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8813498
2015-10-19 14:40:09,269 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8826893
2015-10-19 14:40:12,289 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.88502014
2015-10-19 14:40:15,299 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.88762033
2015-10-19 14:40:18,318 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8906117
2015-10-19 14:40:21,337 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.89326924
2015-10-19 14:40:24,346 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.89654064
2015-10-19 14:40:27,354 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.8991117
2015-10-19 14:40:30,373 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9008122
2015-10-19 14:40:33,388 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9025656
2015-10-19 14:40:36,408 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9041133
2015-10-19 14:40:39,429 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.90564156
2015-10-19 14:40:42,447 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9072602
2015-10-19 14:40:45,461 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.90848273
2015-10-19 14:40:48,481 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9101218
2015-10-19 14:40:51,498 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9116219
2015-10-19 14:40:54,514 INFO [IPC Server handler 12 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.91301787
2015-10-19 14:40:57,525 INFO [IPC Server handler 29 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.914272
2015-10-19 14:41:00,535 INFO [IPC Server handler 10 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.91510844
2015-10-19 14:41:03,551 INFO [IPC Server handler 26 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.91583335
2015-10-19 14:41:06,569 INFO [IPC Server handler 21 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9169846
2015-10-19 14:41:09,586 INFO [IPC Server handler 23 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.91838926
2015-10-19 14:41:12,604 INFO [IPC Server handler 24 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9194484
2015-10-19 14:41:15,621 INFO [IPC Server handler 7 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9205812
2015-10-19 14:41:18,640 INFO [IPC Server handler 25 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.921622
2015-10-19 14:41:21,657 INFO [IPC Server handler 8 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9228201
2015-10-19 14:41:24,674 INFO [IPC Server handler 4 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.92394096
2015-10-19 14:41:27,691 INFO [IPC Server handler 0 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.92537147
2015-10-19 14:41:30,709 INFO [IPC Server handler 2 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9266702
2015-10-19 14:41:33,727 INFO [IPC Server handler 27 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9271298
2015-10-19 14:41:36,745 INFO [IPC Server handler 11 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9278085
2015-10-19 14:41:39,761 INFO [IPC Server handler 15 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.92949843
2015-10-19 14:41:42,777 INFO [IPC Server handler 16 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9312246
2015-10-19 14:41:45,798 INFO [IPC Server handler 20 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9339272
2015-10-19 14:41:48,816 INFO [IPC Server handler 14 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9360055
2015-10-19 14:41:51,836 INFO [IPC Server handler 17 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.93845034
2015-10-19 14:41:54,853 INFO [IPC Server handler 9 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9410076
2015-10-19 14:41:57,877 INFO [IPC Server handler 3 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.94334006
2015-10-19 14:42:00,899 INFO [IPC Server handler 28 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.94567496
2015-10-19 14:42:03,914 INFO [IPC Server handler 5 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9481062
2015-10-19 14:42:06,927 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9519272
2015-10-19 14:42:09,929 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9534889
2015-10-19 14:42:12,930 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.95463955
2015-10-19 14:42:15,931 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.95625275
2015-10-19 14:42:18,934 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.95775884
2015-10-19 14:42:21,935 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.95950985
2015-10-19 14:42:24,939 INFO [IPC Server handler 22 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.96225274
2015-10-19 14:42:27,938 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.96581995
2015-10-19 14:42:30,940 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9697586
2015-10-19 14:42:33,942 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.97373104
2015-10-19 14:42:36,944 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9772379
2015-10-19 14:42:39,947 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.98028654
2015-10-19 14:42:42,947 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.98490655
2015-10-19 14:42:45,950 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9895279
2015-10-19 14:42:48,951 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.99326396
2015-10-19 14:42:51,953 INFO [IPC Server handler 18 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 0.9967432
2015-10-19 14:42:54,540 INFO [IPC Server handler 13 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445182159119_0003_r_000000_1
2015-10-19 14:42:54,541 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_r_000000_1 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-19 14:42:54,541 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445182159119_0003_r_000000_1 given a go for committing the task output.
2015-10-19 14:42:54,542 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445182159119_0003_r_000000_1
2015-10-19 14:42:54,543 INFO [IPC Server handler 1 on 43581] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445182159119_0003_r_000000_1:true
2015-10-19 14:42:54,564 INFO [IPC Server handler 6 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0003_r_000000_1 is : 1.0
2015-10-19 14:42:54,566 INFO [IPC Server handler 19 on 43581] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0003_r_000000_1
2015-10-19 14:42:54,566 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_r_000000_1 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 14:42:54,567 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0003_01_000017 taskAttempt attempt_1445182159119_0003_r_000000_1
2015-10-19 14:42:54,567 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0003_r_000000_1
2015-10-19 14:42:54,569 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 14:42:54,586 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0003_r_000000_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 14:42:54,586 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0003_r_000000_1
2015-10-19 14:42:54,586 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0003_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 14:42:54,587 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-19 14:42:54,588 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0003Job Transitioned from RUNNING to COMMITTING
2015-10-19 14:42:54,588 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-19 14:42:54,659 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-19 14:42:54,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0003Job Transitioned from COMMITTING to SUCCEEDED
2015-10-19 14:42:54,662 INFO [Thread-141] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-19 14:42:54,662 INFO [Thread-141] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-19 14:42:54,662 INFO [Thread-141] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-19 14:42:54,663 INFO [Thread-141] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-19 14:42:54,663 INFO [Thread-141] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-19 14:42:54,663 INFO [Thread-141] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-19 14:42:54,664 INFO [Thread-141] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-19 14:42:54,824 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0003/job_1445182159119_0003_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0003-1445235687728-msrabi-word+count-1445236974655-10-1-SUCCEEDED-default-1445235698649.jhist_tmp
2015-10-19 14:42:55,005 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0003-1445235687728-msrabi-word+count-1445236974655-10-1-SUCCEEDED-default-1445235698649.jhist_tmp
2015-10-19 14:42:55,010 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0003/job_1445182159119_0003_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0003_conf.xml_tmp
2015-10-19 14:42:55,026 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:17 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:42:55,105 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0003_conf.xml_tmp
2015-10-19 14:42:55,111 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0003.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0003.summary
2015-10-19 14:42:55,114 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0003_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0003_conf.xml
2015-10-19 14:42:55,118 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0003-1445235687728-msrabi-word+count-1445236974655-10-1-SUCCEEDED-default-1445235698649.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0003-1445235687728-msrabi-word+count-1445236974655-10-1-SUCCEEDED-default-1445235698649.jhist
2015-10-19 14:42:55,119 INFO [Thread-141] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-19 14:42:55,125 INFO [Thread-141] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-19 14:42:55,127 INFO [Thread-141] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-41.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445182159119_0003
2015-10-19 14:42:55,138 INFO [Thread-141] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-19 14:42:56,139 INFO [Thread-141] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:17 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 14:42:56,140 INFO [Thread-141] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0003
2015-10-19 14:42:56,144 INFO [Thread-141] org.apache.hadoop.ipc.Server: Stopping server on 43581
2015-10-19 14:42:56,146 INFO [IPC Server listener on 43581] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 43581
2015-10-19 14:42:56,146 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-19 14:42:56,147 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
