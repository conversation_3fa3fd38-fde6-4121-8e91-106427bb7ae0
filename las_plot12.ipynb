{"cells": [{"cell_type": "code", "execution_count": 2, "id": "754eb96b", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "370ad4ec4490484f83d2bc3730088e8d", "version_major": 2, "version_minor": 0}, "text/plain": ["                                             |          | [  0%]   00:00 -> (? left)"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Report sweetviz_report.html was generated! NOTEBOOK/COLAB USERS: the web browser MAY not pop up, regardless, the report IS saved in your notebook/colab files.\n"]}], "source": ["import sweetviz as sv\n", "import pandas as pd\n", "\n", "df = pd.read_csv(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/CSV Data/force2020_data_unsupervised_learning.csv\", na_values=-999)\n", "report = sv.analyze(df)\n", "report.show_html(\"sweetviz_report.html\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "124fdfdf", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'show' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[3], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mshow\u001b[49m\n", "\u001b[1;31mNameError\u001b[0m: name 'show' is not defined"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3a065661", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d7dea21f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}