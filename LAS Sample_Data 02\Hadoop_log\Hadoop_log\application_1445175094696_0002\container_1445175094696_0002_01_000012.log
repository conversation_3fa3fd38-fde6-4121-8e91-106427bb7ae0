2015-10-18 21:36:00,667 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 21:36:00,739 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 21:36:00,739 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-18 21:36:00,759 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-18 21:36:00,759 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445175094696_0002, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@1ebe6739)
2015-10-18 21:36:00,876 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-18 21:36:01,130 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445175094696_0002
2015-10-18 21:36:01,647 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-18 21:36:02,093 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-18 21:36:02,112 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@4112054e
2015-10-18 21:36:02,133 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@309134f7
2015-10-18 21:36:02,154 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=140928608, maxSingleShuffleLimit=35232152, mergeThreshold=93012888, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-18 21:36:02,156 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0002_r_000000_0 Thread started: EventFetcher for fetching Map Completion Events
2015-10-18 21:36:02,163 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 21:36:02,163 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:36:02,163 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0002_r_000000_0: Got 5 new map-outputs
2015-10-18 21:36:02,187 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0002&reduce=0&map=attempt_1445175094696_0002_m_000009_0 sent hash and received reply
2015-10-18 21:36:02,190 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0002_m_000009_0: Shuffling to disk since 172334804 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:36:02,194 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0002_m_000009_0 decomp: 172334804 len: 172334808 to DISK
2015-10-18 21:36:03,370 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 172334808 bytes from map-output for attempt_1445175094696_0002_m_000009_0
2015-10-18 21:36:03,378 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 1215ms
2015-10-18 21:36:03,378 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 4 to fetcher#5
2015-10-18 21:36:03,378 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 4 of 4 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:36:03,386 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0002&reduce=0&map=attempt_1445175094696_0002_m_000005_0,attempt_1445175094696_0002_m_000008_0,attempt_1445175094696_0002_m_000003_0,attempt_1445175094696_0002_m_000007_0 sent hash and received reply
2015-10-18 21:36:03,386 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0002_m_000005_0: Shuffling to disk since 216990140 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:36:03,390 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0002_m_000005_0 decomp: 216990140 len: 216990144 to DISK
2015-10-18 21:36:04,804 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216990144 bytes from map-output for attempt_1445175094696_0002_m_000005_0
2015-10-18 21:36:04,810 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0002_m_000008_0: Shuffling to disk since 217015228 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:36:04,813 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0002_m_000008_0 decomp: 217015228 len: 217015232 to DISK
2015-10-18 21:36:06,146 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217015232 bytes from map-output for attempt_1445175094696_0002_m_000008_0
2015-10-18 21:36:06,244 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0002_m_000003_0: Shuffling to disk since 216972750 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:36:06,247 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0002_m_000003_0 decomp: 216972750 len: 216972754 to DISK
2015-10-18 21:36:07,494 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216972754 bytes from map-output for attempt_1445175094696_0002_m_000003_0
2015-10-18 21:36:07,500 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0002_m_000007_0: Shuffling to disk since 216976206 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:36:07,503 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0002_m_000007_0 decomp: 216976206 len: 216976210 to DISK
2015-10-18 21:36:08,696 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216976210 bytes from map-output for attempt_1445175094696_0002_m_000007_0
2015-10-18 21:36:08,702 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 5325ms
2015-10-18 21:37:17,431 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0002_r_000000_0: Got 1 new map-outputs
2015-10-18 21:37:17,431 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 21:37:17,431 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:37:17,439 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0002&reduce=0&map=attempt_1445175094696_0002_m_000000_1 sent hash and received reply
2015-10-18 21:37:17,439 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0002_m_000000_1: Shuffling to disk since 216988123 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:37:17,442 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0002_m_000000_1 decomp: 216988123 len: 216988127 to DISK
2015-10-18 21:37:18,434 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0002_r_000000_0: Got 1 new map-outputs
2015-10-18 21:37:18,789 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216988127 bytes from map-output for attempt_1445175094696_0002_m_000000_1
2015-10-18 21:37:18,795 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 1364ms
2015-10-18 21:37:18,795 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 21:37:18,795 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:37:18,803 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0002&reduce=0&map=attempt_1445175094696_0002_m_000001_1 sent hash and received reply
2015-10-18 21:37:18,803 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0002_m_000001_1: Shuffling to disk since 217009502 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:37:18,806 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0002_m_000001_1 decomp: 217009502 len: 217009506 to DISK
2015-10-18 21:37:19,991 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217009506 bytes from map-output for attempt_1445175094696_0002_m_000001_1
2015-10-18 21:37:19,996 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 1201ms
2015-10-18 21:38:31,751 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0002_r_000000_0: Got 1 new map-outputs
2015-10-18 21:38:31,752 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 21:38:31,752 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:38:31,760 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0002&reduce=0&map=attempt_1445175094696_0002_m_000002_1 sent hash and received reply
2015-10-18 21:38:31,761 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0002_m_000002_1: Shuffling to disk since 216991624 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:38:31,764 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0002_m_000002_1 decomp: 216991624 len: 216991628 to DISK
2015-10-18 21:38:32,754 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning 04DN8IQ.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-18 21:38:32,754 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0002_r_000000_0: Got 1 new map-outputs
2015-10-18 21:38:32,754 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to 04DN8IQ.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-18 21:38:32,929 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0002&reduce=0&map=attempt_1445175094696_0002_m_000006_0 sent hash and received reply
2015-10-18 21:38:32,933 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0002_m_000006_0: Shuffling to disk since 217011663 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:38:32,936 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445175094696_0002_m_000006_0 decomp: 217011663 len: 217011667 to DISK
2015-10-18 21:38:33,275 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216991628 bytes from map-output for attempt_1445175094696_0002_m_000002_1
2015-10-18 21:38:33,281 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 1529ms
2015-10-18 21:38:39,761 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0002_r_000000_0: Got 1 new map-outputs
2015-10-18 21:38:56,631 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217011667 bytes from map-output for attempt_1445175094696_0002_m_000006_0
2015-10-18 21:38:56,637 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: 04DN8IQ.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 23884ms
2015-10-18 21:38:56,637 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning 04DN8IQ.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-18 21:38:56,637 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to 04DN8IQ.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-18 21:38:56,666 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0002&reduce=0&map=attempt_1445175094696_0002_m_000004_0 sent hash and received reply
2015-10-18 21:38:56,673 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0002_m_000004_0: Shuffling to disk since 216999709 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:38:56,676 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445175094696_0002_m_000004_0 decomp: 216999709 len: 216999713 to DISK
2015-10-18 21:39:37,766 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216999713 bytes from map-output for attempt_1445175094696_0002_m_000004_0
2015-10-18 21:39:37,772 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: 04DN8IQ.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 41134ms
2015-10-18 21:39:37,772 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: EventFetcher is interrupted.. Returning
2015-10-18 21:39:37,775 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: finalMerge called with 0 in-memory map-outputs and 10 on-disk map-outputs
2015-10-18 21:39:37,781 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 10 files, 2125289789 bytes from disk
2015-10-18 21:39:37,782 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 0 segments, 0 bytes from memory into reduce
2015-10-18 21:39:37,785 INFO [main] org.apache.hadoop.mapred.Merger: Merging 10 sorted segments
2015-10-18 21:39:37,794 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 10 segments left of total size: 2125289689 bytes
2015-10-18 21:39:37,893 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: mapred.skip.on is deprecated. Instead, use mapreduce.job.skiprecords
2015-10-18 21:40:49,201 INFO [DataStreamer for file /out/out3/_temporary/1/_temporary/attempt_1445175094696_0002_r_000000_0/part-r-00000] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-18 21:40:49,206 INFO [DataStreamer for file /out/out3/_temporary/1/_temporary/attempt_1445175094696_0002_r_000000_0/part-r-00000] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073743665_2890
2015-10-18 21:40:49,230 INFO [DataStreamer for file /out/out3/_temporary/1/_temporary/attempt_1445175094696_0002_r_000000_0/part-r-00000] org.apache.hadoop.hdfs.DFSClient: Excluding datanode *************:50010
2015-10-18 21:45:14,416 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445175094696_0002_r_000000_0 is done. And is in the process of committing
2015-10-18 21:45:14,456 INFO [main] org.apache.hadoop.mapred.Task: Task attempt_1445175094696_0002_r_000000_0 is allowed to commit now
2015-10-18 21:45:14,483 INFO [main] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Saved output of task 'attempt_1445175094696_0002_r_000000_0' to hdfs://msra-sa-41:9000/out/out3/_temporary/1/task_1445175094696_0002_r_000000
2015-10-18 21:45:14,512 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445175094696_0002_r_000000_0' done.
