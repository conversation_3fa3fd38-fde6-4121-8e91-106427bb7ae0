2015-10-19 15:55:30,701 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445182159119_0015_000001
2015-10-19 15:55:33,920 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-19 15:55:33,920 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 15 cluster_timestamp: 1445182159119 } attemptId: 1 } keyId: 1694045684)
2015-10-19 15:55:34,702 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-19 15:55:38,717 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-19 15:55:39,124 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-19 15:55:39,249 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-19 15:55:39,249 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-19 15:55:39,249 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-19 15:55:39,264 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-19 15:55:39,264 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-19 15:55:39,264 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-19 15:55:39,264 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-19 15:55:39,280 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-19 15:55:39,561 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 15:55:39,702 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 15:55:39,827 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 15:55:39,889 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-19 15:55:40,046 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-19 15:55:41,452 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 15:55:41,890 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 15:55:42,249 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-19 15:55:42,249 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445182159119_0015 to jobTokenSecretManager
2015-10-19 15:55:54,328 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445182159119_0015 because: not enabled; too many maps; too much input;
2015-10-19 15:55:54,343 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445182159119_0015 = 1256521728. Number of splits = 10
2015-10-19 15:55:54,343 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445182159119_0015 = 1
2015-10-19 15:55:54,343 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0015Job Transitioned from NEW to INITED
2015-10-19 15:55:54,343 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445182159119_0015.
2015-10-19 15:55:54,625 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 15:55:54,875 INFO [Socket Reader #1 for port 63271] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 63271
2015-10-19 15:55:55,062 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-19 15:55:55,078 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MININT-FNANLI5.fareast.corp.microsoft.com/*************:63271
2015-10-19 15:55:55,734 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 15:55:55,734 INFO [IPC Server listener on 63271] org.apache.hadoop.ipc.Server: IPC Server listener on 63271: starting
2015-10-19 15:55:55,968 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-19 15:55:56,031 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-19 15:55:56,047 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-19 15:55:56,047 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-19 15:55:56,047 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-19 15:55:56,062 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-19 15:55:56,062 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-19 15:55:56,062 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 63278
2015-10-19 15:55:56,062 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-19 15:55:56,297 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_63278_mapreduce____ub2dhf\webapp
2015-10-19 15:55:58,078 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:63278
2015-10-19 15:55:58,078 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 63278
2015-10-19 15:55:59,312 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-19 15:55:59,312 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 15:55:59,562 INFO [IPC Server listener on 63282] org.apache.hadoop.ipc.Server: IPC Server listener on 63282: starting
2015-10-19 15:55:59,562 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 15:55:59,562 INFO [Socket Reader #1 for port 63282] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 63282
2015-10-19 15:55:59,578 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445182159119_0015
2015-10-19 15:55:59,594 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-19 15:55:59,594 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-19 15:55:59,594 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-19 15:55:59,859 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-19 15:56:00,406 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-19 15:56:00,406 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-19 15:56:00,406 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-19 15:56:00,422 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-19 15:56:01,172 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0015Job Transitioned from INITED to SETUP
2015-10-19 15:56:01,547 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-19 15:56:02,000 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0015Job Transitioned from SETUP to RUNNING
2015-10-19 15:56:02,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445182159119_0015, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0015/job_1445182159119_0015_1.jhist
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:56:02,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:56:02,734 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:56:02,734 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:56:02,734 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:56:02,734 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:56:02,734 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:56:03,047 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-19 15:56:03,063 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-19 15:56:03,906 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 15:56:04,000 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-20> knownNMs=4
2015-10-19 15:56:04,016 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-20>
2015-10-19 15:56:04,016 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:05,110 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 3
2015-10-19 15:56:05,110 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000002 to attempt_1445182159119_0015_m_000000_0
2015-10-19 15:56:05,110 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000003 to attempt_1445182159119_0015_m_000001_0
2015-10-19 15:56:05,110 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000004 to attempt_1445182159119_0015_m_000002_0
2015-10-19 15:56:05,110 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-19 15:56:05,110 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:05,110 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:3 RackLocal:0
2015-10-19 15:56:05,250 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:05,485 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0015/job.jar
2015-10-19 15:56:05,547 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0015/job.xml
2015-10-19 15:56:05,563 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-19 15:56:05,563 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-19 15:56:05,563 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-19 15:56:05,703 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:56:05,703 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:05,703 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:56:05,703 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:05,703 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:56:05,844 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000002 taskAttempt attempt_1445182159119_0015_m_000000_0
2015-10-19 15:56:05,969 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000004 taskAttempt attempt_1445182159119_0015_m_000002_0
2015-10-19 15:56:05,969 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000000_0
2015-10-19 15:56:05,969 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000003 taskAttempt attempt_1445182159119_0015_m_000001_0
2015-10-19 15:56:05,969 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000001_0
2015-10-19 15:56:05,969 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000002_0
2015-10-19 15:56:05,969 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:56:06,016 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:56:06,156 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:56:06,188 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:4096, vCores:-23> knownNMs=4
2015-10-19 15:56:06,188 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-19 15:56:06,188 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:06,656 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000000_0 : 13562
2015-10-19 15:56:06,656 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000000_0] using containerId: [container_1445182159119_0015_01_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:56:06,656 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:56:06,656 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000000
2015-10-19 15:56:06,656 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:56:06,688 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000001_0 : 13562
2015-10-19 15:56:06,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000001_0] using containerId: [container_1445182159119_0015_01_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:56:06,688 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000002_0 : 13562
2015-10-19 15:56:06,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:56:06,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000002_0] using containerId: [container_1445182159119_0015_01_000004 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:56:06,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:56:06,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000001
2015-10-19 15:56:06,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:56:06,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000002
2015-10-19 15:56:06,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:56:07,297 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:56:07,297 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:07,297 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000005 to attempt_1445182159119_0015_m_000003_0
2015-10-19 15:56:07,297 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-19 15:56:07,297 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:07,297 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:3 RackLocal:1
2015-10-19 15:56:07,297 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:07,297 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:56:07,532 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000005 taskAttempt attempt_1445182159119_0015_m_000003_0
2015-10-19 15:56:07,532 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000003_0
2015-10-19 15:56:07,532 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:56:08,297 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000003_0 : 13562
2015-10-19 15:56:08,297 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000003_0] using containerId: [container_1445182159119_0015_01_000005 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 15:56:08,297 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:56:08,297 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000003
2015-10-19 15:56:08,297 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:56:08,375 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-24> knownNMs=4
2015-10-19 15:56:08,375 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-19 15:56:08,375 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:09,422 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:56:09,422 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:09,422 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000006 to attempt_1445182159119_0015_m_000004_0
2015-10-19 15:56:09,422 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 15:56:09,422 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:09,422 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:3 RackLocal:2
2015-10-19 15:56:09,422 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:09,422 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:56:09,563 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000006 taskAttempt attempt_1445182159119_0015_m_000004_0
2015-10-19 15:56:09,563 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000004_0
2015-10-19 15:56:09,563 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:56:10,079 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:56:10,141 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000004 asked for a task
2015-10-19 15:56:10,141 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000004 given task: attempt_1445182159119_0015_m_000002_0
2015-10-19 15:56:10,329 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:56:10,329 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000004_0 : 13562
2015-10-19 15:56:10,329 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000004_0] using containerId: [container_1445182159119_0015_01_000006 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 15:56:10,329 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:56:10,329 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000004
2015-10-19 15:56:10,329 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:56:10,375 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000003 asked for a task
2015-10-19 15:56:10,375 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000003 given task: attempt_1445182159119_0015_m_000001_0
2015-10-19 15:56:10,391 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:56:10,438 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000002 asked for a task
2015-10-19 15:56:10,438 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000002 given task: attempt_1445182159119_0015_m_000000_0
2015-10-19 15:56:10,500 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-19 15:56:10,500 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 15:56:10,500 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:11,563 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 15:56:11,563 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:12,594 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 15:56:12,594 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:13,641 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:56:13,641 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:13,641 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000007 to attempt_1445182159119_0015_m_000005_0
2015-10-19 15:56:13,641 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 15:56:13,641 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:13,641 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:3 RackLocal:3
2015-10-19 15:56:13,641 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:13,641 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:56:13,797 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000007 taskAttempt attempt_1445182159119_0015_m_000005_0
2015-10-19 15:56:13,797 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000005_0
2015-10-19 15:56:13,797 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:56:14,298 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000005_0 : 13562
2015-10-19 15:56:14,298 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000005_0] using containerId: [container_1445182159119_0015_01_000007 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 15:56:14,298 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:56:14,298 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000005
2015-10-19 15:56:14,298 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:56:14,688 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:56:14,688 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:56:14,688 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:14,688 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000008 to attempt_1445182159119_0015_m_000006_0
2015-10-19 15:56:14,688 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:14,688 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:14,688 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:3 RackLocal:4
2015-10-19 15:56:14,688 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:14,704 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:56:14,829 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000008 taskAttempt attempt_1445182159119_0015_m_000006_0
2015-10-19 15:56:14,829 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000006_0
2015-10-19 15:56:14,829 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:56:15,235 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000006_0 : 13562
2015-10-19 15:56:15,235 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000006_0] using containerId: [container_1445182159119_0015_01_000008 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 15:56:15,235 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:56:15,235 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000006
2015-10-19 15:56:15,235 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:56:15,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:56:15,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:15,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:16,938 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:16,938 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:17,516 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.10660437
2015-10-19 15:56:18,079 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:18,079 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:18,173 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.1066108
2015-10-19 15:56:18,173 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.10635664
2015-10-19 15:56:19,251 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:19,267 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:20,329 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:20,329 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:20,563 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.10660437
2015-10-19 15:56:21,220 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.10635664
2015-10-19 15:56:21,220 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.1066108
2015-10-19 15:56:21,423 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:56:21,470 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:21,470 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:22,079 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000005 asked for a task
2015-10-19 15:56:22,079 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000005 given task: attempt_1445182159119_0015_m_000003_0
2015-10-19 15:56:22,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:22,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:23,611 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.10660437
2015-10-19 15:56:23,829 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:56:23,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:23,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:23,923 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000007 asked for a task
2015-10-19 15:56:23,923 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000007 given task: attempt_1445182159119_0015_m_000005_0
2015-10-19 15:56:24,017 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:56:24,220 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000008 asked for a task
2015-10-19 15:56:24,220 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000008 given task: attempt_1445182159119_0015_m_000006_0
2015-10-19 15:56:24,282 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.10635664
2015-10-19 15:56:24,282 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.1066108
2015-10-19 15:56:24,626 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:56:24,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:24,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:25,392 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000006 asked for a task
2015-10-19 15:56:25,392 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000006 given task: attempt_1445182159119_0015_m_000004_0
2015-10-19 15:56:26,064 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:26,064 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:26,642 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.11691946
2015-10-19 15:56:27,220 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:27,220 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:27,329 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.1066108
2015-10-19 15:56:27,329 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.10635664
2015-10-19 15:56:28,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:28,298 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:29,376 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:29,376 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:29,658 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.19212553
2015-10-19 15:56:30,345 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.16547354
2015-10-19 15:56:30,361 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.16732255
2015-10-19 15:56:30,439 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:30,439 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:31,502 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:56:31,502 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000009 to attempt_1445182159119_0015_m_000007_0
2015-10-19 15:56:31,502 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:31,502 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:31,502 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:4 RackLocal:4
2015-10-19 15:56:31,502 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:31,502 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:56:31,752 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000009 taskAttempt attempt_1445182159119_0015_m_000007_0
2015-10-19 15:56:31,752 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000007_0
2015-10-19 15:56:31,752 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:56:32,048 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000007_0 : 13562
2015-10-19 15:56:32,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000007_0] using containerId: [container_1445182159119_0015_01_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:56:32,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:56:32,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000007
2015-10-19 15:56:32,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:56:32,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:56:32,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:32,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:32,689 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.19212553
2015-10-19 15:56:33,502 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.19211523
2015-10-19 15:56:33,502 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.19158794
2015-10-19 15:56:33,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 15:56:33,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:34,205 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.061225235
2015-10-19 15:56:35,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:56:35,689 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:35,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000010 to attempt_1445182159119_0015_m_000008_0
2015-10-19 15:56:35,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:35,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:35,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:4 RackLocal:5
2015-10-19 15:56:35,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:35,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:56:35,736 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000010 taskAttempt attempt_1445182159119_0015_m_000008_0
2015-10-19 15:56:35,736 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000008_0
2015-10-19 15:56:35,736 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:56:35,767 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.19212553
2015-10-19 15:56:35,846 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000008_0 : 13562
2015-10-19 15:56:35,846 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000008_0] using containerId: [container_1445182159119_0015_01_000010 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 15:56:35,846 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:56:35,846 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000008
2015-10-19 15:56:35,846 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:56:36,283 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:56:36,408 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000009 asked for a task
2015-10-19 15:56:36,408 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000009 given task: attempt_1445182159119_0015_m_000007_0
2015-10-19 15:56:36,580 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.19211523
2015-10-19 15:56:36,580 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.19158794
2015-10-19 15:56:36,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:56:36,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:36,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:37,330 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.052694067
2015-10-19 15:56:37,971 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:37,971 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:38,158 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.1029965
2015-10-19 15:56:38,830 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.19212553
2015-10-19 15:56:39,252 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:56:39,252 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000011 to attempt_1445182159119_0015_m_000009_0
2015-10-19 15:56:39,252 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:56:39,252 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:39,252 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:5 RackLocal:5
2015-10-19 15:56:39,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:56:39,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:56:39,564 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000011 taskAttempt attempt_1445182159119_0015_m_000009_0
2015-10-19 15:56:39,564 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000009_0
2015-10-19 15:56:39,564 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:56:39,658 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.19158794
2015-10-19 15:56:39,690 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.19211523
2015-10-19 15:56:41,611 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.106964506
2015-10-19 15:56:41,768 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.08858521
2015-10-19 15:56:41,830 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:56:41,846 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.21638435
2015-10-19 15:56:42,502 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000009_0 : 13562
2015-10-19 15:56:42,518 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000009_0] using containerId: [container_1445182159119_0015_01_000011 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:56:42,518 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:56:42,518 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000009
2015-10-19 15:56:42,518 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:56:42,705 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.24942556
2015-10-19 15:56:42,768 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.24181287
2015-10-19 15:56:42,783 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:56:42,862 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000010 asked for a task
2015-10-19 15:56:42,862 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000010 given task: attempt_1445182159119_0015_m_000008_0
2015-10-19 15:56:43,002 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 15:56:43,002 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:43,549 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:56:43,752 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000011 asked for a task
2015-10-19 15:56:43,752 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000011 given task: attempt_1445182159119_0015_m_000009_0
2015-10-19 15:56:44,690 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.10681946
2015-10-19 15:56:44,737 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.0065125413
2015-10-19 15:56:44,815 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.106964506
2015-10-19 15:56:44,955 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.27772525
2015-10-19 15:56:44,955 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.10685723
2015-10-19 15:56:45,252 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-19 15:56:45,252 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:45,830 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.27696857
2015-10-19 15:56:45,846 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.27776006
2015-10-19 15:56:47,674 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.024565823
2015-10-19 15:56:47,846 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.10681946
2015-10-19 15:56:48,034 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.27772525
2015-10-19 15:56:48,049 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.106964506
2015-10-19 15:56:48,206 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.10685723
2015-10-19 15:56:48,721 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.016249934
2015-10-19 15:56:48,940 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.27696857
2015-10-19 15:56:48,987 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.27776006
2015-10-19 15:56:51,018 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.042664018
2015-10-19 15:56:51,690 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.106964506
2015-10-19 15:56:51,800 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.10681946
2015-10-19 15:56:51,878 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.10685723
2015-10-19 15:56:52,143 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.27696857
2015-10-19 15:56:52,143 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.27772525
2015-10-19 15:56:52,159 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000009_0 is : 0.295472
2015-10-19 15:56:52,159 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.27776006
2015-10-19 15:56:52,159 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:5120, vCores:-22>
2015-10-19 15:56:52,159 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:52,425 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.02735332
2015-10-19 15:56:53,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-21>
2015-10-19 15:56:53,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:54,597 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.06090146
2015-10-19 15:56:55,534 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.3485022
2015-10-19 15:56:55,581 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.35892734
2015-10-19 15:56:55,847 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.106964506
2015-10-19 15:56:55,940 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.03679919
2015-10-19 15:56:56,144 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.10685723
2015-10-19 15:56:56,972 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.19255035
2015-10-19 15:56:57,331 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.3320115
2015-10-19 15:56:57,347 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000009_0 is : 0.295472
2015-10-19 15:56:58,050 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-20>
2015-10-19 15:56:58,050 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:56:58,284 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.07230069
2015-10-19 15:56:58,628 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.36319977
2015-10-19 15:56:58,628 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.3624012
2015-10-19 15:56:59,112 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.106964506
2015-10-19 15:56:59,269 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.053735707
2015-10-19 15:56:59,347 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.10685723
2015-10-19 15:57:00,066 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.19255035
2015-10-19 15:57:00,378 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.36317363
2015-10-19 15:57:00,472 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000009_0 is : 0.5323719
2015-10-19 15:57:01,800 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.3624012
2015-10-19 15:57:01,800 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.36319977
2015-10-19 15:57:02,316 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.1390184
2015-10-19 15:57:02,316 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.08076857
2015-10-19 15:57:02,503 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.10685723
2015-10-19 15:57:03,128 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.06285498
2015-10-19 15:57:03,503 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.19406821
2015-10-19 15:57:03,503 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000009_0 is : 0.5323719
2015-10-19 15:57:03,691 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.36317363
2015-10-19 15:57:04,847 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.41709623
2015-10-19 15:57:04,847 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.38652855
2015-10-19 15:57:05,410 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.17009263
2015-10-19 15:57:05,691 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.1345065
2015-10-19 15:57:06,363 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.09184191
2015-10-19 15:57:06,566 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000009_0 is : 0.5323719
2015-10-19 15:57:06,566 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.27825075
2015-10-19 15:57:06,816 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.44859612
2015-10-19 15:57:06,816 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.01633759
2015-10-19 15:57:07,800 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.07099523
2015-10-19 15:57:07,957 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.448704
2015-10-19 15:57:07,957 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.44789755
2015-10-19 15:57:08,550 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.18726674
2015-10-19 15:57:08,722 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000009_0 is : 0.5323719
2015-10-19 15:57:08,879 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.16039705
2015-10-19 15:57:09,644 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.27825075
2015-10-19 15:57:09,660 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000009_0 is : 0.667
2015-10-19 15:57:09,832 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.44859612
2015-10-19 15:57:10,347 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.105521254
2015-10-19 15:57:10,519 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.02442439
2015-10-19 15:57:11,066 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.448704
2015-10-19 15:57:11,066 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.44789755
2015-10-19 15:57:11,504 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.08011459
2015-10-19 15:57:11,801 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.19266446
2015-10-19 15:57:12,051 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.18042754
2015-10-19 15:57:12,816 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000009_0 is : 0.667
2015-10-19 15:57:12,816 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.27825075
2015-10-19 15:57:12,988 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.44859612
2015-10-19 15:57:14,113 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.448704
2015-10-19 15:57:14,144 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.44789755
2015-10-19 15:57:14,301 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.10680563
2015-10-19 15:57:14,598 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.028329905
2015-10-19 15:57:15,176 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.19266446
2015-10-19 15:57:15,848 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.091190316
2015-10-19 15:57:15,910 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.3520393
2015-10-19 15:57:15,910 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000009_0 is : 0.667
2015-10-19 15:57:15,941 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.19247705
2015-10-19 15:57:16,035 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.48273695
2015-10-19 15:57:17,551 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.4949946
2015-10-19 15:57:18,004 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.10680563
2015-10-19 15:57:18,082 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.45556444
2015-10-19 15:57:18,317 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.19266446
2015-10-19 15:57:18,473 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.033583645
2015-10-19 15:57:18,957 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-19>
2015-10-19 15:57:18,957 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:57:19,067 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.3638923
2015-10-19 15:57:19,067 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000009_0 is : 0.71217024
2015-10-19 15:57:19,114 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.5342037
2015-10-19 15:57:19,254 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.19247705
2015-10-19 15:57:19,723 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.101610556
2015-10-19 15:57:20,660 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.53341997
2015-10-19 15:57:21,129 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.53425497
2015-10-19 15:57:21,489 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.19266446
2015-10-19 15:57:21,911 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.10680563
2015-10-19 15:57:21,942 FATAL [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Task: attempt_1445182159119_0015_m_000005_0 - exited : java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)

2015-10-19 15:57:21,942 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Diagnostics report from attempt_1445182159119_0015_m_000005_0: Error: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)

2015-10-19 15:57:21,942 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000005_0: Error: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)

2015-10-19 15:57:21,957 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_0 TaskAttempt Transitioned from RUNNING to FAIL_CONTAINER_CLEANUP
2015-10-19 15:57:21,957 FATAL [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Task: attempt_1445182159119_0015_m_000006_0 - exited : java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)

2015-10-19 15:57:21,957 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Diagnostics report from attempt_1445182159119_0015_m_000006_0: Error: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)

2015-10-19 15:57:21,957 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000006_0: Error: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)

2015-10-19 15:57:21,957 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000006_0 TaskAttempt Transitioned from RUNNING to FAIL_CONTAINER_CLEANUP
2015-10-19 15:57:21,957 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000007 taskAttempt attempt_1445182159119_0015_m_000005_0
2015-10-19 15:57:21,957 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000005_0
2015-10-19 15:57:21,957 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:57:21,973 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000008 taskAttempt attempt_1445182159119_0015_m_000006_0
2015-10-19 15:57:21,973 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000006_0
2015-10-19 15:57:21,973 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:57:22,036 FATAL [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Task: attempt_1445182159119_0015_m_000005_0 - exited : java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)

2015-10-19 15:57:22,036 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Diagnostics report from attempt_1445182159119_0015_m_000005_0: Error: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)

2015-10-19 15:57:22,036 FATAL [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Task: attempt_1445182159119_0015_m_000006_0 - exited : java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)

2015-10-19 15:57:22,036 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000005_0: Error: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)

2015-10-19 15:57:22,036 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Diagnostics report from attempt_1445182159119_0015_m_000006_0: Error: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)

2015-10-19 15:57:22,036 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000006_0: Error: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)

2015-10-19 15:57:22,520 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000009_0 is : 0.79114634
2015-10-19 15:57:22,723 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.5342037
2015-10-19 15:57:22,723 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_0 is : 0.19266446
2015-10-19 15:57:22,520 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.19247705
2015-10-19 15:57:22,739 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.051453523
2015-10-19 15:57:22,739 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_0 is : 0.19247705
2015-10-19 15:57:22,520 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.3638923
2015-10-19 15:57:22,848 FATAL [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Task: attempt_1445182159119_0015_m_000005_0 - exited : java.io.IOException: Spill failed
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.checkSpillException(MapTask.java:1555)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$300(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$Buffer.write(MapTask.java:1369)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.io.Text.write(Text.java:331)
	at org.apache.hadoop.io.serializer.WritableSerialization$WritableSerializer.serialize(WritableSerialization.java:98)
	at org.apache.hadoop.io.serializer.WritableSerialization$WritableSerializer.serialize(WritableSerialization.java:82)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.collect(MapTask.java:1154)
	at org.apache.hadoop.mapred.MapTask$NewOutputCollector.write(MapTask.java:712)
	at org.apache.hadoop.mapreduce.task.TaskInputOutputContextImpl.write(TaskInputOutputContextImpl.java:89)
	at org.apache.hadoop.mapreduce.lib.map.WrappedMapper$Context.write(WrappedMapper.java:112)
	at PageRank$MyMapper.map(PageRank.java:45)
	at PageRank$MyMapper.map(PageRank.java:1)
	at org.apache.hadoop.mapreduce.Mapper.run(Mapper.java:145)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:784)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
Caused by: org.apache.hadoop.fs.FSError: java.io.IOException: There is not enough space on the disk
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:248)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)
Caused by: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	... 8 more

2015-10-19 15:57:22,848 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Diagnostics report from attempt_1445182159119_0015_m_000005_0: Error: java.io.IOException: Spill failed
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.checkSpillException(MapTask.java:1555)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$300(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$Buffer.write(MapTask.java:1369)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.io.Text.write(Text.java:331)
	at org.apache.hadoop.io.serializer.WritableSerialization$WritableSerializer.serialize(WritableSerialization.java:98)
	at org.apache.hadoop.io.serializer.WritableSerialization$WritableSerializer.serialize(WritableSerialization.java:82)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.collect(MapTask.java:1154)
	at org.apache.hadoop.mapred.MapTask$NewOutputCollector.write(MapTask.java:712)
	at org.apache.hadoop.mapreduce.task.TaskInputOutputContextImpl.write(TaskInputOutputContextImpl.java:89)
	at org.apache.hadoop.mapreduce.lib.map.WrappedMapper$Context.write(WrappedMapper.java:112)
	at PageRank$MyMapper.map(PageRank.java:45)
	at PageRank$MyMapper.map(PageRank.java:1)
	at org.apache.hadoop.mapreduce.Mapper.run(Mapper.java:145)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:784)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
Caused by: org.apache.hadoop.fs.FSError: java.io.IOException: There is not enough space on the disk
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:248)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)
Caused by: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	... 8 more

2015-10-19 15:57:22,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000005_0: Error: java.io.IOException: Spill failed
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.checkSpillException(MapTask.java:1555)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$300(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$Buffer.write(MapTask.java:1369)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.io.Text.write(Text.java:331)
	at org.apache.hadoop.io.serializer.WritableSerialization$WritableSerializer.serialize(WritableSerialization.java:98)
	at org.apache.hadoop.io.serializer.WritableSerialization$WritableSerializer.serialize(WritableSerialization.java:82)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.collect(MapTask.java:1154)
	at org.apache.hadoop.mapred.MapTask$NewOutputCollector.write(MapTask.java:712)
	at org.apache.hadoop.mapreduce.task.TaskInputOutputContextImpl.write(TaskInputOutputContextImpl.java:89)
	at org.apache.hadoop.mapreduce.lib.map.WrappedMapper$Context.write(WrappedMapper.java:112)
	at PageRank$MyMapper.map(PageRank.java:45)
	at PageRank$MyMapper.map(PageRank.java:1)
	at org.apache.hadoop.mapreduce.Mapper.run(Mapper.java:145)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:784)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
Caused by: org.apache.hadoop.fs.FSError: java.io.IOException: There is not enough space on the disk
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:248)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)
Caused by: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	... 8 more

2015-10-19 15:57:22,864 FATAL [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Task: attempt_1445182159119_0015_m_000006_0 - exited : java.io.IOException: Spill failed
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.checkSpillException(MapTask.java:1555)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$300(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$Buffer.write(MapTask.java:1369)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.io.Text.write(Text.java:331)
	at org.apache.hadoop.io.serializer.WritableSerialization$WritableSerializer.serialize(WritableSerialization.java:98)
	at org.apache.hadoop.io.serializer.WritableSerialization$WritableSerializer.serialize(WritableSerialization.java:82)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.collect(MapTask.java:1154)
	at org.apache.hadoop.mapred.MapTask$NewOutputCollector.write(MapTask.java:712)
	at org.apache.hadoop.mapreduce.task.TaskInputOutputContextImpl.write(TaskInputOutputContextImpl.java:89)
	at org.apache.hadoop.mapreduce.lib.map.WrappedMapper$Context.write(WrappedMapper.java:112)
	at PageRank$MyMapper.map(PageRank.java:45)
	at PageRank$MyMapper.map(PageRank.java:1)
	at org.apache.hadoop.mapreduce.Mapper.run(Mapper.java:145)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:784)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
Caused by: org.apache.hadoop.fs.FSError: java.io.IOException: There is not enough space on the disk
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:248)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)
Caused by: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	... 8 more

2015-10-19 15:57:22,864 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Diagnostics report from attempt_1445182159119_0015_m_000006_0: Error: java.io.IOException: Spill failed
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.checkSpillException(MapTask.java:1555)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$300(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$Buffer.write(MapTask.java:1369)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.io.Text.write(Text.java:331)
	at org.apache.hadoop.io.serializer.WritableSerialization$WritableSerializer.serialize(WritableSerialization.java:98)
	at org.apache.hadoop.io.serializer.WritableSerialization$WritableSerializer.serialize(WritableSerialization.java:82)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.collect(MapTask.java:1154)
	at org.apache.hadoop.mapred.MapTask$NewOutputCollector.write(MapTask.java:712)
	at org.apache.hadoop.mapreduce.task.TaskInputOutputContextImpl.write(TaskInputOutputContextImpl.java:89)
	at org.apache.hadoop.mapreduce.lib.map.WrappedMapper$Context.write(WrappedMapper.java:112)
	at PageRank$MyMapper.map(PageRank.java:45)
	at PageRank$MyMapper.map(PageRank.java:1)
	at org.apache.hadoop.mapreduce.Mapper.run(Mapper.java:145)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:784)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
Caused by: org.apache.hadoop.fs.FSError: java.io.IOException: There is not enough space on the disk
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:248)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)
Caused by: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	... 8 more

2015-10-19 15:57:22,864 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000006_0: Error: java.io.IOException: Spill failed
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.checkSpillException(MapTask.java:1555)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$300(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$Buffer.write(MapTask.java:1369)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.io.Text.write(Text.java:331)
	at org.apache.hadoop.io.serializer.WritableSerialization$WritableSerializer.serialize(WritableSerialization.java:98)
	at org.apache.hadoop.io.serializer.WritableSerialization$WritableSerializer.serialize(WritableSerialization.java:82)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.collect(MapTask.java:1154)
	at org.apache.hadoop.mapred.MapTask$NewOutputCollector.write(MapTask.java:712)
	at org.apache.hadoop.mapreduce.task.TaskInputOutputContextImpl.write(TaskInputOutputContextImpl.java:89)
	at org.apache.hadoop.mapreduce.lib.map.WrappedMapper$Context.write(WrappedMapper.java:112)
	at PageRank$MyMapper.map(PageRank.java:45)
	at PageRank$MyMapper.map(PageRank.java:1)
	at org.apache.hadoop.mapreduce.Mapper.run(Mapper.java:145)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:784)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
Caused by: org.apache.hadoop.fs.FSError: java.io.IOException: There is not enough space on the disk
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:248)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at java.io.FilterOutputStream.close(FilterOutputStream.java:157)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:106)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.sortAndSpill(MapTask.java:1663)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer.access$900(MapTask.java:873)
	at org.apache.hadoop.mapred.MapTask$MapOutputBuffer$SpillThread.run(MapTask.java:1525)
Caused by: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	... 8 more

2015-10-19 15:57:22,864 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_0 TaskAttempt Transitioned from FAIL_CONTAINER_CLEANUP to FAIL_TASK_CLEANUP
2015-10-19 15:57:22,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000006_0 TaskAttempt Transitioned from FAIL_CONTAINER_CLEANUP to FAIL_TASK_CLEANUP
2015-10-19 15:57:23,129 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:57:23,286 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:57:23,661 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.106493875
2015-10-19 15:57:23,723 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445182159119_0015_m_000006_0
2015-10-19 15:57:23,723 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445182159119_0015_m_000005_0
2015-10-19 15:57:23,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000006_0 TaskAttempt Transitioned from FAIL_TASK_CLEANUP to FAILED
2015-10-19 15:57:23,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_0 TaskAttempt Transitioned from FAIL_TASK_CLEANUP to FAILED
2015-10-19 15:57:23,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:23,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:23,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:23,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:23,754 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: 1 failures on node 04DN8IQ.fareast.corp.microsoft.com
2015-10-19 15:57:23,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:57:23,754 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:57:23,754 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: 2 failures on node 04DN8IQ.fareast.corp.microsoft.com
2015-10-19 15:57:23,754 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Added attempt_1445182159119_0015_m_000006_1 to list of failed maps
2015-10-19 15:57:23,754 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Added attempt_1445182159119_0015_m_000005_1 to list of failed maps
2015-10-19 15:57:23,817 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.53341997
2015-10-19 15:57:23,864 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:5 RackLocal:5
2015-10-19 15:57:23,989 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=1 release= 0 newContainers=0 finishedContainers=2 resourcelimit=<memory:10240, vCores:-17> knownNMs=4
2015-10-19 15:57:23,989 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000007
2015-10-19 15:57:23,989 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000008
2015-10-19 15:57:23,989 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-17>
2015-10-19 15:57:23,989 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:57:23,989 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:5 RackLocal:5
2015-10-19 15:57:23,989 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:57:23,989 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:57:24,223 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.53425497
2015-10-19 15:57:25,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-19 15:57:25,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigning container Container: [ContainerId: container_1445182159119_0015_01_000012, NodeId: MSRA-SA-41.fareast.corp.microsoft.com:10769, NodeHttpAddress: MSRA-SA-41.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 5, Token: Token { kind: ContainerToken, service: **************:10769 }, ] to fast fail map
2015-10-19 15:57:25,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned from earlierFailedMaps
2015-10-19 15:57:25,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000012 to attempt_1445182159119_0015_m_000006_1
2015-10-19 15:57:25,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigning container Container: [ContainerId: container_1445182159119_0015_01_000013, NodeId: MSRA-SA-39.fareast.corp.microsoft.com:28345, NodeHttpAddress: MSRA-SA-39.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 5, Token: Token { kind: ContainerToken, service: **************:28345 }, ] to fast fail map
2015-10-19 15:57:25,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned from earlierFailedMaps
2015-10-19 15:57:25,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000013 to attempt_1445182159119_0015_m_000005_1
2015-10-19 15:57:25,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-18>
2015-10-19 15:57:25,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:57:25,129 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:5 RackLocal:5
2015-10-19 15:57:25,129 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:25,129 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:57:25,129 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:25,129 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:57:25,129 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000012 taskAttempt attempt_1445182159119_0015_m_000006_1
2015-10-19 15:57:25,129 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000006_1
2015-10-19 15:57:25,129 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:57:25,145 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000013 taskAttempt attempt_1445182159119_0015_m_000005_1
2015-10-19 15:57:25,145 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000005_1
2015-10-19 15:57:25,145 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:57:25,676 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.10680563
2015-10-19 15:57:25,723 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000006_1 : 13562
2015-10-19 15:57:25,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000006_1] using containerId: [container_1445182159119_0015_01_000012 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:57:25,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:57:25,723 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000006
2015-10-19 15:57:25,770 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000005_1 : 13562
2015-10-19 15:57:25,786 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000005_1] using containerId: [container_1445182159119_0015_01_000013 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:57:25,786 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:57:25,786 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000005
2015-10-19 15:57:25,817 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.5470254
2015-10-19 15:57:25,817 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.41337267
2015-10-19 15:57:25,817 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000009_0 is : 0.8822054
2015-10-19 15:57:26,208 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-18> knownNMs=4
2015-10-19 15:57:26,661 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.083697446
2015-10-19 15:57:26,989 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.53341997
2015-10-19 15:57:27,317 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.53425497
2015-10-19 15:57:27,692 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:57:27,739 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000012 asked for a task
2015-10-19 15:57:27,739 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000012 given task: attempt_1445182159119_0015_m_000006_1
2015-10-19 15:57:27,895 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.106493875
2015-10-19 15:57:28,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:11264, vCores:-17>
2015-10-19 15:57:28,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:57:28,911 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.6100424
2015-10-19 15:57:28,911 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.44964966
2015-10-19 15:57:28,911 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000009_0 is : 0.9616173
2015-10-19 15:57:29,223 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:57:29,333 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000013 asked for a task
2015-10-19 15:57:29,333 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000013 given task: attempt_1445182159119_0015_m_000005_1
2015-10-19 15:57:30,177 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.5998116
2015-10-19 15:57:30,505 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.574817
2015-10-19 15:57:30,505 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000009_0 is : 1.0
2015-10-19 15:57:30,567 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0015_m_000009_0
2015-10-19 15:57:30,567 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:57:30,567 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000011 taskAttempt attempt_1445182159119_0015_m_000009_0
2015-10-19 15:57:30,567 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000009_0
2015-10-19 15:57:30,567 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:57:31,255 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:57:31,270 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0015_m_000009_0
2015-10-19 15:57:31,270 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:57:31,270 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-19 15:57:31,302 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.10680563
2015-10-19 15:57:31,677 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0015_m_000004
2015-10-19 15:57:31,677 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:57:31,724 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0015_m_000004
2015-10-19 15:57:31,724 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:31,724 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:31,724 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000004_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:57:31,942 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.6196791
2015-10-19 15:57:31,974 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.44964966
2015-10-19 15:57:32,380 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:5 RackLocal:5
2015-10-19 15:57:32,411 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:12288, vCores:-16> knownNMs=4
2015-10-19 15:57:32,411 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000011
2015-10-19 15:57:32,411 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:12288, vCores:-16>
2015-10-19 15:57:32,411 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000009_0: 
2015-10-19 15:57:32,411 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-19 15:57:32,411 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:21504, vCores:-7> finalMapResourceLimit:<memory:10240, vCores:10> finalReduceResourceLimit:<memory:11264, vCores:-17> netScheduledMapResource:<memory:10240, vCores:10> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:57:32,411 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-19 15:57:32,411 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:5 RackLocal:5
2015-10-19 15:57:32,442 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.106881365
2015-10-19 15:57:32,927 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.106493875
2015-10-19 15:57:33,208 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.61898744
2015-10-19 15:57:33,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=1 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:11264, vCores:-17> knownNMs=4
2015-10-19 15:57:33,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:57:33,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000014 to attempt_1445182159119_0015_m_000004_1
2015-10-19 15:57:33,536 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:6 RackLocal:5
2015-10-19 15:57:33,536 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:33,536 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000004_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:57:33,536 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000014 taskAttempt attempt_1445182159119_0015_m_000004_1
2015-10-19 15:57:33,536 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000004_1
2015-10-19 15:57:33,536 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:57:33,583 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.6197233
2015-10-19 15:57:33,974 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000004_1 : 13562
2015-10-19 15:57:33,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000004_1] using containerId: [container_1445182159119_0015_01_000014 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:57:33,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000004_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:57:33,974 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000004
2015-10-19 15:57:34,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:10240, vCores:-18> knownNMs=4
2015-10-19 15:57:34,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:57:34,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-19 15:57:34,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000015 to attempt_1445182159119_0015_r_000000_0
2015-10-19 15:57:34,614 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:6 RackLocal:5
2015-10-19 15:57:34,630 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:34,630 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:57:34,630 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000015 taskAttempt attempt_1445182159119_0015_r_000000_0
2015-10-19 15:57:34,630 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_r_000000_0
2015-10-19 15:57:34,630 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:57:34,958 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_r_000000_0 : 13562
2015-10-19 15:57:34,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_r_000000_0] using containerId: [container_1445182159119_0015_01_000015 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:57:34,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:57:34,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_r_000000
2015-10-19 15:57:34,958 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:57:35,052 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.6196791
2015-10-19 15:57:35,052 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.106964506
2015-10-19 15:57:35,052 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.4505467
2015-10-19 15:57:35,521 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.10680563
2015-10-19 15:57:35,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:12288, vCores:-17> knownNMs=4
2015-10-19 15:57:35,911 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:57:36,052 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000014 asked for a task
2015-10-19 15:57:36,052 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000014 given task: attempt_1445182159119_0015_m_000004_1
2015-10-19 15:57:36,318 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.61898744
2015-10-19 15:57:36,614 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.6197233
2015-10-19 15:57:36,661 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.106881365
2015-10-19 15:57:37,146 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.10685723
2015-10-19 15:57:37,286 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.106493875
2015-10-19 15:57:37,818 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:57:37,896 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_r_000015 asked for a task
2015-10-19 15:57:37,896 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_r_000015 given task: attempt_1445182159119_0015_r_000000_0
2015-10-19 15:57:38,083 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.6196791
2015-10-19 15:57:38,099 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.106964506
2015-10-19 15:57:38,099 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.5352825
2015-10-19 15:57:39,318 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 0 maxEvents 10000
2015-10-19 15:57:39,333 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.61898744
2015-10-19 15:57:39,661 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.6197233
2015-10-19 15:57:40,162 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.10680563
2015-10-19 15:57:40,177 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.10685723
2015-10-19 15:57:40,412 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:40,927 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.106881365
2015-10-19 15:57:41,193 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.6615083
2015-10-19 15:57:41,193 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.106964506
2015-10-19 15:57:41,193 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.5352825
2015-10-19 15:57:41,380 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.6615083
2015-10-19 15:57:41,490 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:41,896 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.106493875
2015-10-19 15:57:42,365 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.61898744
2015-10-19 15:57:42,505 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:42,740 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.6197233
2015-10-19 15:57:43,193 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.10685723
2015-10-19 15:57:43,521 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:43,630 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.10680563
2015-10-19 15:57:44,287 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.667
2015-10-19 15:57:44,287 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.19266446
2015-10-19 15:57:44,287 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.5352825
2015-10-19 15:57:44,537 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:44,740 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.11854808
2015-10-19 15:57:45,396 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.106881365
2015-10-19 15:57:45,396 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.61898744
2015-10-19 15:57:45,396 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:57:45,677 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:45,834 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.6197233
2015-10-19 15:57:46,287 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.106493875
2015-10-19 15:57:46,287 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.15440577
2015-10-19 15:57:46,646 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.10680563
2015-10-19 15:57:46,662 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:46,802 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0015_m_000003
2015-10-19 15:57:46,802 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0015_m_000003
2015-10-19 15:57:46,802 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:46,802 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:46,802 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:57:46,802 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:57:47,365 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.19266446
2015-10-19 15:57:47,365 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.5871855
2015-10-19 15:57:47,365 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.667
2015-10-19 15:57:47,709 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:47,896 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:6 RackLocal:5
2015-10-19 15:57:47,912 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:14336, vCores:-15> knownNMs=4
2015-10-19 15:57:48,428 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:57:48,818 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:49,006 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:57:49,006 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:49,006 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000016 to attempt_1445182159119_0015_m_000003_1
2015-10-19 15:57:49,006 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:6 RackLocal:6
2015-10-19 15:57:49,006 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:49,006 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:57:49,006 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000016 taskAttempt attempt_1445182159119_0015_m_000003_1
2015-10-19 15:57:49,006 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000003_1
2015-10-19 15:57:49,006 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:57:49,490 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.16575526
2015-10-19 15:57:49,490 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.19247705
2015-10-19 15:57:49,521 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000003_1 : 13562
2015-10-19 15:57:49,521 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000003_1] using containerId: [container_1445182159119_0015_01_000016 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 15:57:49,521 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:57:49,521 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000003
2015-10-19 15:57:49,646 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.10680563
2015-10-19 15:57:49,678 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.106881365
2015-10-19 15:57:49,803 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:50,146 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-16> knownNMs=4
2015-10-19 15:57:50,396 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.667
2015-10-19 15:57:50,443 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.620844
2015-10-19 15:57:50,443 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.19266446
2015-10-19 15:57:50,662 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.106493875
2015-10-19 15:57:50,771 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.61898744
2015-10-19 15:57:50,896 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:51,506 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:57:51,865 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:57:51,897 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000016 asked for a task
2015-10-19 15:57:51,897 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000016 given task: attempt_1445182159119_0015_m_000003_1
2015-10-19 15:57:51,975 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:52,568 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.19247705
2015-10-19 15:57:52,693 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.10680563
2015-10-19 15:57:52,975 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:53,350 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.6197233
2015-10-19 15:57:53,412 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.667
2015-10-19 15:57:53,459 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.19266446
2015-10-19 15:57:53,475 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.620844
2015-10-19 15:57:53,553 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.19242907
2015-10-19 15:57:53,897 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.106881365
2015-10-19 15:57:53,990 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:54,569 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.667
2015-10-19 15:57:54,569 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:57:54,662 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.11677172
2015-10-19 15:57:55,100 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:55,647 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.19247705
2015-10-19 15:57:55,772 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.10680563
2015-10-19 15:57:56,084 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:56,506 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.2783809
2015-10-19 15:57:56,522 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.620844
2015-10-19 15:57:57,162 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:57,631 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.667
2015-10-19 15:57:57,631 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:57:57,725 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.19242907
2015-10-19 15:57:57,881 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.667
2015-10-19 15:57:58,100 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.106881365
2015-10-19 15:57:58,147 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:58,272 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.620844
2015-10-19 15:57:58,709 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.19247705
2015-10-19 15:57:58,709 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.16281645
2015-10-19 15:57:58,788 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.10680563
2015-10-19 15:57:59,178 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:57:59,538 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.2783809
2015-10-19 15:57:59,538 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.09710652
2015-10-19 15:57:59,538 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.667
2015-10-19 15:58:00,522 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:00,647 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:58:00,647 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.667
2015-10-19 15:58:01,006 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.667
2015-10-19 15:58:01,553 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:01,741 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.19247705
2015-10-19 15:58:01,772 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.19242907
2015-10-19 15:58:01,897 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0015_m_000008
2015-10-19 15:58:01,897 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0015_m_000008
2015-10-19 15:58:01,897 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:58:01,897 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:58:01,897 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:58:01,897 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000008_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:58:02,225 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:6 RackLocal:6
2015-10-19 15:58:02,225 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-16> knownNMs=4
2015-10-19 15:58:02,303 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.106881365
2015-10-19 15:58:02,553 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:02,600 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.2783809
2015-10-19 15:58:02,600 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.667
2015-10-19 15:58:02,616 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.106493875
2015-10-19 15:58:02,913 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.19209063
2015-10-19 15:58:03,272 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:58:03,272 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000017 to attempt_1445182159119_0015_m_000008_1
2015-10-19 15:58:03,272 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:6
2015-10-19 15:58:03,272 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:58:03,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000008_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:58:03,288 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000017 taskAttempt attempt_1445182159119_0015_m_000008_1
2015-10-19 15:58:03,288 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000008_1
2015-10-19 15:58:03,288 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:58:03,460 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000008_1 : 13562
2015-10-19 15:58:03,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000008_1] using containerId: [container_1445182159119_0015_01_000017 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:58:03,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000008_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:58:03,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000008
2015-10-19 15:58:03,538 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:03,678 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:58:03,678 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.667
2015-10-19 15:58:04,022 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.6761902
2015-10-19 15:58:04,491 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:12288, vCores:-17> knownNMs=4
2015-10-19 15:58:04,585 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:05,569 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:05,647 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.2783809
2015-10-19 15:58:05,647 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.106493875
2015-10-19 15:58:05,647 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.667
2015-10-19 15:58:05,694 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.19242907
2015-10-19 15:58:06,350 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.106881365
2015-10-19 15:58:06,569 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:06,710 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:58:07,054 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.19209063
2015-10-19 15:58:07,054 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.70128745
2015-10-19 15:58:07,194 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:58:07,335 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000017 asked for a task
2015-10-19 15:58:07,335 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000017 given task: attempt_1445182159119_0015_m_000008_1
2015-10-19 15:58:07,647 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:08,632 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:08,663 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.36404583
2015-10-19 15:58:08,679 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.106493875
2015-10-19 15:58:08,679 INFO [IPC Server handler 29 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.6876395
2015-10-19 15:58:09,679 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:09,726 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.19242907
2015-10-19 15:58:09,772 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:58:09,991 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.12310473
2015-10-19 15:58:10,085 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.7285441
2015-10-19 15:58:10,773 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:10,851 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.27813601
2015-10-19 15:58:10,866 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.19242907
2015-10-19 15:58:11,085 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.19209063
2015-10-19 15:58:11,616 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.7234382
2015-10-19 15:58:11,710 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.36404583
2015-10-19 15:58:11,757 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.7211558
2015-10-19 15:58:11,757 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.11072977
2015-10-19 15:58:11,788 INFO [IPC Server handler 29 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:12,788 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.7010634
2015-10-19 15:58:12,835 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:58:12,835 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:13,101 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.76832056
2015-10-19 15:58:13,757 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.19242907
2015-10-19 15:58:13,898 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:13,898 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.27813601
2015-10-19 15:58:13,898 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.19242907
2015-10-19 15:58:14,148 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.16840461
2015-10-19 15:58:14,601 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.106881365
2015-10-19 15:58:14,679 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.77591604
2015-10-19 15:58:15,288 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.19209063
2015-10-19 15:58:15,695 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.36404583
2015-10-19 15:58:15,695 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.7558361
2015-10-19 15:58:15,757 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.18205136
2015-10-19 15:58:15,788 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.7391982
2015-10-19 15:58:15,851 INFO [IPC Server handler 29 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:15,851 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:58:16,148 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.81108594
2015-10-19 15:58:17,007 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0015_m_000005
2015-10-19 15:58:17,007 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0015_m_000005
2015-10-19 15:58:17,007 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:58:17,007 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:58:17,007 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:58:17,007 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:58:17,664 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.106881365
2015-10-19 15:58:17,695 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.8058965
2015-10-19 15:58:17,710 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.19242907
2015-10-19 15:58:17,757 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:17,882 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.19242907
2015-10-19 15:58:17,882 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.27813601
2015-10-19 15:58:18,054 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Added attempt_1445182159119_0015_m_000005_2 to list of failed maps
2015-10-19 15:58:18,179 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.19258286
2015-10-19 15:58:18,726 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.44980705
2015-10-19 15:58:18,757 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.79912853
2015-10-19 15:58:18,820 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:18,820 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.19209063
2015-10-19 15:58:18,851 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.77139735
2015-10-19 15:58:18,882 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:58:19,117 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:7 RackLocal:6
2015-10-19 15:58:19,148 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:12288, vCores:-17> knownNMs=4
2015-10-19 15:58:19,164 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.8421193
2015-10-19 15:58:19,586 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.19209063
2015-10-19 15:58:19,882 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:20,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:58:20,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigning container Container: [ContainerId: container_1445182159119_0015_01_000018, NodeId: MSRA-SA-41.fareast.corp.microsoft.com:10769, NodeHttpAddress: MSRA-SA-41.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 5, Token: Token { kind: ContainerToken, service: **************:10769 }, ] to fast fail map
2015-10-19 15:58:20,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned from earlierFailedMaps
2015-10-19 15:58:20,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000018 to attempt_1445182159119_0015_m_000005_2
2015-10-19 15:58:20,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:7 RackLocal:6
2015-10-19 15:58:20,226 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:58:20,242 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:58:20,242 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000018 taskAttempt attempt_1445182159119_0015_m_000005_2
2015-10-19 15:58:20,242 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000005_2
2015-10-19 15:58:20,242 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:58:20,711 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000005_2 : 13562
2015-10-19 15:58:20,711 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.106881365
2015-10-19 15:58:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000005_2] using containerId: [container_1445182159119_0015_01_000018 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:58:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:58:20,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000005
2015-10-19 15:58:20,742 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.8508237
2015-10-19 15:58:20,976 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:20,976 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.19242907
2015-10-19 15:58:20,976 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.3031325
2015-10-19 15:58:21,367 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-18> knownNMs=4
2015-10-19 15:58:21,929 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.19242907
2015-10-19 15:58:22,414 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.19258286
2015-10-19 15:58:22,601 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.8712868
2015-10-19 15:58:22,695 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.44980705
2015-10-19 15:58:22,695 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.832906
2015-10-19 15:58:22,883 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.19209063
2015-10-19 15:58:22,883 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.80093944
2015-10-19 15:58:22,883 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:58:22,883 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:23,789 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.9045025
2015-10-19 15:58:23,804 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.106881365
2015-10-19 15:58:23,929 INFO [IPC Server handler 28 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:24,023 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.24567157
2015-10-19 15:58:24,023 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.36390656
2015-10-19 15:58:24,117 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.19209063
2015-10-19 15:58:24,242 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:58:24,336 INFO [IPC Server handler 29 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000018 asked for a task
2015-10-19 15:58:24,336 INFO [IPC Server handler 29 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000018 given task: attempt_1445182159119_0015_m_000005_2
2015-10-19 15:58:24,992 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:25,617 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.91158175
2015-10-19 15:58:25,711 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.44980705
2015-10-19 15:58:25,726 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.87872976
2015-10-19 15:58:25,930 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:58:25,930 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.8449346
2015-10-19 15:58:25,930 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.22180732
2015-10-19 15:58:25,930 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.19638191
2015-10-19 15:58:26,070 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:26,461 INFO [IPC Server handler 29 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.19258286
2015-10-19 15:58:26,836 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.15702836
2015-10-19 15:58:26,836 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 0.9632765
2015-10-19 15:58:28,117 INFO [IPC Server handler 26 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.19209063
2015-10-19 15:58:28,633 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 0.9629123
2015-10-19 15:58:28,758 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.90580726
2015-10-19 15:58:28,758 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.51304245
2015-10-19 15:58:28,961 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.89605665
2015-10-19 15:58:28,977 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.27765483
2015-10-19 15:58:29,180 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.2781602
2015-10-19 15:58:29,180 INFO [IPC Server handler 26 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.36390656
2015-10-19 15:58:29,211 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:29,211 INFO [IPC Server handler 28 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.033333335
2015-10-19 15:58:29,445 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000002_0 is : 1.0
2015-10-19 15:58:29,539 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0015_m_000002_0
2015-10-19 15:58:29,539 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:58:29,539 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000004 taskAttempt attempt_1445182159119_0015_m_000002_0
2015-10-19 15:58:29,539 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000002_0
2015-10-19 15:58:29,539 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:58:29,945 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.19258286
2015-10-19 15:58:29,992 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:58:29,992 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0015_m_000002_0
2015-10-19 15:58:29,992 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:58:29,992 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-19 15:58:30,055 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.24473976
2015-10-19 15:58:30,242 INFO [IPC Server handler 26 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:58:30,477 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.19258286
2015-10-19 15:58:30,789 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000001_0 is : 1.0
2015-10-19 15:58:30,945 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0015_m_000001_0
2015-10-19 15:58:30,945 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:58:30,945 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000003 taskAttempt attempt_1445182159119_0015_m_000001_0
2015-10-19 15:58:30,945 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000001_0
2015-10-19 15:58:30,945 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:58:31,039 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:13 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:7 RackLocal:6
2015-10-19 15:58:31,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000004
2015-10-19 15:58:31,055 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:7 RackLocal:6
2015-10-19 15:58:31,055 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:58:31,539 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 15:58:31,539 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:58:31,539 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0015_m_000001_0
2015-10-19 15:58:31,539 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:58:31,539 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-19 15:58:31,899 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.53543663
2015-10-19 15:58:31,899 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.10685723
2015-10-19 15:58:31,899 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.9308089
2015-10-19 15:58:31,977 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.9495983
2015-10-19 15:58:31,992 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.27765483
2015-10-19 15:58:32,149 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:7 RackLocal:6
2015-10-19 15:58:32,196 INFO [IPC Server handler 28 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.2781602
2015-10-19 15:58:32,196 INFO [IPC Server handler 28 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.39973408
2015-10-19 15:58:32,227 INFO [IPC Server handler 26 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.06666667
2015-10-19 15:58:32,446 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.19209063
2015-10-19 15:58:32,633 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 15:58:33,055 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.19258286
2015-10-19 15:58:33,274 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000003
2015-10-19 15:58:33,274 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:7 RackLocal:6
2015-10-19 15:58:33,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:58:33,711 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:58:34,164 INFO [IPC Server handler 29 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.27765426
2015-10-19 15:58:34,649 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.19258286
2015-10-19 15:58:34,774 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:58:34,961 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.10685723
2015-10-19 15:58:34,961 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 0.966514
2015-10-19 15:58:34,961 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.53543663
2015-10-19 15:58:34,993 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 0.9918934
2015-10-19 15:58:35,024 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.27765483
2015-10-19 15:58:35,211 INFO [IPC Server handler 28 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.44950968
2015-10-19 15:58:35,211 INFO [IPC Server handler 29 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.32233363
2015-10-19 15:58:35,243 INFO [IPC Server handler 26 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.10000001
2015-10-19 15:58:35,727 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000000_0 is : 1.0
2015-10-19 15:58:35,727 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0015_m_000000_0
2015-10-19 15:58:35,727 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:58:35,727 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000002 taskAttempt attempt_1445182159119_0015_m_000000_0
2015-10-19 15:58:35,727 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000000_0
2015-10-19 15:58:35,727 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:58:35,836 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:58:36,102 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.19258286
2015-10-19 15:58:36,586 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.1949518
2015-10-19 15:58:36,915 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:58:37,618 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000002
2015-10-19 15:58:37,618 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:7 RackLocal:6
2015-10-19 15:58:37,618 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000000_0: 
2015-10-19 15:58:37,883 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000007_0 is : 1.0
2015-10-19 15:58:37,883 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0015_m_000007_0
2015-10-19 15:58:37,883 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:58:37,883 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000009 taskAttempt attempt_1445182159119_0015_m_000007_0
2015-10-19 15:58:37,883 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000007_0
2015-10-19 15:58:37,883 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:58:38,071 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:58:38,071 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.53543663
2015-10-19 15:58:38,071 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.10685723
2015-10-19 15:58:38,165 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.2781602
2015-10-19 15:58:38,274 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.44950968
2015-10-19 15:58:38,274 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.36388028
2015-10-19 15:58:38,274 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.10000001
2015-10-19 15:58:38,446 INFO [IPC Server handler 28 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.29430413
2015-10-19 15:58:38,758 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.19258286
2015-10-19 15:58:39,180 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:58:39,180 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.26866847
2015-10-19 15:58:39,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:58:39,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0015_m_000000_0
2015-10-19 15:58:39,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:58:39,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-19 15:58:39,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:7 RackLocal:6
2015-10-19 15:58:39,774 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000009
2015-10-19 15:58:39,774 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:7 RackLocal:6
2015-10-19 15:58:39,774 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000007_0: 
2015-10-19 15:58:40,227 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:58:40,727 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.23900987
2015-10-19 15:58:41,352 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:58:41,352 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0015_m_000007_0
2015-10-19 15:58:41,352 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:58:41,352 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-19 15:58:41,384 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:58:41,384 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.44950968
2015-10-19 15:58:41,384 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.36388028
2015-10-19 15:58:41,384 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.13333334
2015-10-19 15:58:41,384 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.583496
2015-10-19 15:58:41,384 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.15031305
2015-10-19 15:58:41,477 INFO [IPC Server handler 28 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.36323506
2015-10-19 15:58:41,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:7 RackLocal:6
2015-10-19 15:58:42,102 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.2781602
2015-10-19 15:58:42,587 INFO [IPC Server handler 29 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:42,587 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.27811313
2015-10-19 15:58:42,899 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.19258286
2015-10-19 15:58:43,649 INFO [IPC Server handler 28 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:44,399 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.36388028
2015-10-19 15:58:44,399 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.6210422
2015-10-19 15:58:44,399 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:58:44,415 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.45164642
2015-10-19 15:58:44,415 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.19247705
2015-10-19 15:58:44,509 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.36323506
2015-10-19 15:58:44,727 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:45,087 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.27699396
2015-10-19 15:58:45,587 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.27811313
2015-10-19 15:58:45,790 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:46,134 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.2781602
2015-10-19 15:58:46,853 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:46,853 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.19258286
2015-10-19 15:58:47,415 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.4456677
2015-10-19 15:58:47,431 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:58:47,431 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.6210422
2015-10-19 15:58:47,431 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.5210648
2015-10-19 15:58:47,446 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.19247705
2015-10-19 15:58:47,540 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.36323506
2015-10-19 15:58:47,900 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:48,618 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.32554036
2015-10-19 15:58:48,993 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:49,087 INFO [IPC Server handler 26 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.27765483
2015-10-19 15:58:50,056 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:50,056 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.2781602
2015-10-19 15:58:50,447 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.5352021
2015-10-19 15:58:50,447 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:58:50,447 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.44968578
2015-10-19 15:58:50,447 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.6210422
2015-10-19 15:58:50,462 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.19247705
2015-10-19 15:58:50,587 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.36323506
2015-10-19 15:58:50,853 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.20284693
2015-10-19 15:58:51,056 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:51,665 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.3637686
2015-10-19 15:58:52,119 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.6210422
2015-10-19 15:58:52,119 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:52,947 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.27765483
2015-10-19 15:58:53,181 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:53,494 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.44968578
2015-10-19 15:58:53,494 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.5352021
2015-10-19 15:58:53,494 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:58:53,509 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.667
2015-10-19 15:58:53,509 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.27032062
2015-10-19 15:58:53,697 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_1 is : 0.43743223
2015-10-19 15:58:53,884 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.2781602
2015-10-19 15:58:54,275 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:54,603 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.24824381
2015-10-19 15:58:54,681 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.3637686
2015-10-19 15:58:54,837 INFO [Socket Reader #1 for port 63282] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 63282: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 15:58:54,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445182159119_0015_m_000003_1 because it is running on unusable node:04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:58:54,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000016
2015-10-19 15:58:54,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:7 RackLocal:6
2015-10-19 15:58:54,853 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 15:58:54,853 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000003_1: Container released on a *lost* node
2015-10-19 15:58:54,853 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000016 taskAttempt attempt_1445182159119_0015_m_000003_1
2015-10-19 15:58:54,853 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000003_1
2015-10-19 15:58:54,853 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:58:55,337 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:56,416 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:56,572 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:58:56,572 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.5022842
2015-10-19 15:58:56,572 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.54701823
2015-10-19 15:58:56,619 INFO [IPC Server handler 26 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.667
2015-10-19 15:58:56,619 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.27813601
2015-10-19 15:58:57,275 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.27765483
2015-10-19 15:58:57,525 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:57,759 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.3637686
2015-10-19 15:58:58,322 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.2781602
2015-10-19 15:58:58,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 15:58:58,588 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:58,634 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:58:58,728 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.27811313
2015-10-19 15:58:59,635 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:58:59,635 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.5352028
2015-10-19 15:58:59,635 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.6194213
2015-10-19 15:58:59,635 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:58:59,635 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.6670033
2015-10-19 15:58:59,635 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.27813601
2015-10-19 15:59:00,650 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:59:00,760 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.42141846
2015-10-19 15:59:00,900 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.27765483
2015-10-19 15:59:01,666 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:59:01,775 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445182159119_0015_m_000003_1
2015-10-19 15:59:01,775 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 15:59:01,775 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:59:01,775 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:59:01,775 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:59:01,916 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.2781602
2015-10-19 15:59:02,244 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.27811313
2015-10-19 15:59:02,525 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:7 RackLocal:6
2015-10-19 15:59:02,525 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-10> knownNMs=3
2015-10-19 15:59:02,650 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:59:02,650 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.5352028
2015-10-19 15:59:02,650 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.6209487
2015-10-19 15:59:02,682 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:59:02,682 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.27813601
2015-10-19 15:59:02,682 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.7005226
2015-10-19 15:59:03,557 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:59:03,557 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0015_01_000019 to attempt_1445182159119_0015_m_000003_2
2015-10-19 15:59:03,557 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:8 RackLocal:6
2015-10-19 15:59:03,557 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:59:03,557 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:59:03,557 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0015_01_000019 taskAttempt attempt_1445182159119_0015_m_000003_2
2015-10-19 15:59:03,557 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0015_m_000003_2
2015-10-19 15:59:03,557 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:59:03,697 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0015_m_000003_2 : 13562
2015-10-19 15:59:03,697 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0015_m_000003_2] using containerId: [container_1445182159119_0015_01_000019 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:59:03,697 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:59:03,697 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0015_m_000003
2015-10-19 15:59:03,713 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:03,791 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.44950172
2015-10-19 15:59:04,291 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.27765483
2015-10-19 15:59:04,604 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0015: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-11> knownNMs=3
2015-10-19 15:59:04,729 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:05,447 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.31147277
2015-10-19 15:59:05,682 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.5576619
2015-10-19 15:59:05,682 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:59:05,682 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.6209487
2015-10-19 15:59:05,713 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.36390656
2015-10-19 15:59:05,713 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.7365432
2015-10-19 15:59:05,744 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:05,854 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.27811313
2015-10-19 15:59:06,104 INFO [Socket Reader #1 for port 63282] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0015 (auth:SIMPLE)
2015-10-19 15:59:06,182 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0015_m_000019 asked for a task
2015-10-19 15:59:06,182 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0015_m_000019 given task: attempt_1445182159119_0015_m_000003_2
2015-10-19 15:59:06,776 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:06,807 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.44950172
2015-10-19 15:59:07,776 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:08,119 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.27765483
2015-10-19 15:59:08,698 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.6208445
2015-10-19 15:59:08,698 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:59:08,698 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.6332213
2015-10-19 15:59:08,744 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.36390656
2015-10-19 15:59:08,744 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.7718672
2015-10-19 15:59:08,791 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:08,854 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.36374962
2015-10-19 15:59:09,401 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.27811313
2015-10-19 15:59:09,823 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.4523503
2015-10-19 15:59:09,823 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:10,010 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.6332213
2015-10-19 15:59:10,838 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:11,713 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.6208445
2015-10-19 15:59:11,713 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:59:11,713 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.667
2015-10-19 15:59:11,760 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.36390656
2015-10-19 15:59:11,791 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.807252
2015-10-19 15:59:11,885 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:11,885 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.29759327
2015-10-19 15:59:12,354 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.36388028
2015-10-19 15:59:12,854 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.53521925
2015-10-19 15:59:12,917 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:13,057 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.27811313
2015-10-19 15:59:13,917 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:14,073 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.106493875
2015-10-19 15:59:14,729 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.667
2015-10-19 15:59:14,729 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:59:14,729 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.6208445
2015-10-19 15:59:14,792 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.36390656
2015-10-19 15:59:14,807 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.82861614
2015-10-19 15:59:14,948 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:15,495 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.35819876
2015-10-19 15:59:15,870 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.53521925
2015-10-19 15:59:15,995 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:16,073 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.36388028
2015-10-19 15:59:16,620 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.27811313
2015-10-19 15:59:16,979 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:17,104 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.106493875
2015-10-19 15:59:17,182 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.6208445
2015-10-19 15:59:17,745 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:59:17,761 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.667
2015-10-19 15:59:17,761 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.667
2015-10-19 15:59:17,854 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.40672797
2015-10-19 15:59:17,854 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.8566617
2015-10-19 15:59:18,042 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:18,932 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.53521925
2015-10-19 15:59:19,167 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:19,307 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.36323506
2015-10-19 15:59:19,854 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.36388028
2015-10-19 15:59:20,136 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.106493875
2015-10-19 15:59:20,151 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:20,573 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.27811313
2015-10-19 15:59:20,761 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:59:20,776 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.68035704
2015-10-19 15:59:20,776 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.667
2015-10-19 15:59:20,933 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.8921957
2015-10-19 15:59:20,933 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.44950968
2015-10-19 15:59:21,198 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:21,964 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.60381514
2015-10-19 15:59:22,245 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:23,292 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.13091706
2015-10-19 15:59:23,292 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:23,511 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.36323506
2015-10-19 15:59:23,558 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.36388028
2015-10-19 15:59:23,776 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:59:23,792 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.667
2015-10-19 15:59:23,792 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.7082198
2015-10-19 15:59:23,964 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.92798126
2015-10-19 15:59:23,980 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.44950968
2015-10-19 15:59:24,073 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.3104705
2015-10-19 15:59:24,339 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:25,105 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.6207798
2015-10-19 15:59:25,464 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:26,777 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:26,870 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.6997961
2015-10-19 15:59:26,870 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.74849993
2015-10-19 15:59:26,870 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:59:27,042 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.9633889
2015-10-19 15:59:27,042 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.44950968
2015-10-19 15:59:27,230 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.1909667
2015-10-19 15:59:27,417 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.36388028
2015-10-19 15:59:27,574 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.36323506
2015-10-19 15:59:27,839 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:28,136 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.6207798
2015-10-19 15:59:28,277 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.35991853
2015-10-19 15:59:28,902 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:29,886 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.74617213
2015-10-19 15:59:29,886 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.16666667
2015-10-19 15:59:29,886 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.79189956
2015-10-19 15:59:29,933 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:30,089 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.473768
2015-10-19 15:59:30,089 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 0.99881065
2015-10-19 15:59:30,277 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.19209063
2015-10-19 15:59:30,511 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000006_1 is : 1.0
2015-10-19 15:59:30,511 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0015_m_000006_1
2015-10-19 15:59:30,511 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000006_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:59:30,511 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000012 taskAttempt attempt_1445182159119_0015_m_000006_1
2015-10-19 15:59:30,511 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000006_1
2015-10-19 15:59:30,511 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:59:30,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000006_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:59:30,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0015_m_000006_1
2015-10-19 15:59:30,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:59:30,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-19 15:59:30,933 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.36388028
2015-10-19 15:59:30,933 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:59:31,089 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.36323506
2015-10-19 15:59:31,199 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.6207798
2015-10-19 15:59:31,246 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:8 RackLocal:6
2015-10-19 15:59:31,746 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.3637686
2015-10-19 15:59:31,933 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:32,277 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000012
2015-10-19 15:59:32,277 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:8 RackLocal:6
2015-10-19 15:59:32,277 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000006_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:59:32,902 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.8197625
2015-10-19 15:59:32,918 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.7925926
2015-10-19 15:59:32,918 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.20000002
2015-10-19 15:59:32,933 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:33,136 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.5352021
2015-10-19 15:59:33,308 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.19209063
2015-10-19 15:59:33,636 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.6207798
2015-10-19 15:59:33,965 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:34,199 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.667
2015-10-19 15:59:34,496 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.3912556
2015-10-19 15:59:34,590 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.36323506
2015-10-19 15:59:34,996 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:35,543 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.3637686
2015-10-19 15:59:36,012 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.8395661
2015-10-19 15:59:36,012 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.20000002
2015-10-19 15:59:36,012 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.8511333
2015-10-19 15:59:36,012 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:36,183 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.5352021
2015-10-19 15:59:36,387 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.19209063
2015-10-19 15:59:37,090 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:37,293 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.667
2015-10-19 15:59:38,137 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:38,355 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.4449257
2015-10-19 15:59:38,371 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.36323506
2015-10-19 15:59:39,059 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.880305
2015-10-19 15:59:39,059 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.884549
2015-10-19 15:59:39,059 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.3637686
2015-10-19 15:59:39,215 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.5352021
2015-10-19 15:59:39,324 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.20000002
2015-10-19 15:59:39,324 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:39,418 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.26936963
2015-10-19 15:59:40,309 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.667
2015-10-19 15:59:40,371 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:41,356 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:41,856 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.44968578
2015-10-19 15:59:41,949 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.3883815
2015-10-19 15:59:42,074 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.908587
2015-10-19 15:59:42,074 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.91685236
2015-10-19 15:59:42,262 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.5352021
2015-10-19 15:59:42,371 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:42,371 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.20000002
2015-10-19 15:59:42,481 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.27765483
2015-10-19 15:59:42,559 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.3637686
2015-10-19 15:59:43,324 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.68092036
2015-10-19 15:59:43,418 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:44,481 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:45,121 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.9385358
2015-10-19 15:59:45,121 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.95449173
2015-10-19 15:59:45,325 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.58238554
2015-10-19 15:59:45,403 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.20000002
2015-10-19 15:59:45,543 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.27765483
2015-10-19 15:59:45,809 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:45,981 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.44968578
2015-10-19 15:59:46,215 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.44534165
2015-10-19 15:59:46,325 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.7126515
2015-10-19 15:59:46,512 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.3637686
2015-10-19 15:59:47,246 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:48,184 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.9678005
2015-10-19 15:59:48,184 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 0.98619944
2015-10-19 15:59:48,418 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.6209487
2015-10-19 15:59:48,418 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.20000002
2015-10-19 15:59:48,418 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:48,575 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.27765483
2015-10-19 15:59:49,262 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_1 is : 1.0
2015-10-19 15:59:49,278 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0015_m_000004_1
2015-10-19 15:59:49,278 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000004_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:59:49,278 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000014 taskAttempt attempt_1445182159119_0015_m_000004_1
2015-10-19 15:59:49,278 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000004_1
2015-10-19 15:59:49,278 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:59:49,418 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000004_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:59:49,418 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0015_m_000004_1
2015-10-19 15:59:49,418 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0015_m_000004_0
2015-10-19 15:59:49,418 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:59:49,418 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-19 15:59:49,418 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000004_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 15:59:49,418 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000006 taskAttempt attempt_1445182159119_0015_m_000004_0
2015-10-19 15:59:49,418 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000004_0
2015-10-19 15:59:49,434 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:59:49,465 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:59:49,465 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.74774015
2015-10-19 15:59:49,606 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000004_0 is : 0.44968578
2015-10-19 15:59:49,731 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0015_m_000004
2015-10-19 15:59:49,731 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:59:49,809 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000004_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 15:59:49,856 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.4486067
2015-10-19 15:59:49,950 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:59:49,950 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445182159119_0015_m_000004_0
2015-10-19 15:59:49,950 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000004_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 15:59:50,044 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:8 RackLocal:6
2015-10-19 15:59:50,044 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.3637686
2015-10-19 15:59:50,544 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 15:59:51,075 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000014
2015-10-19 15:59:51,075 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:8 RackLocal:6
2015-10-19 15:59:51,075 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000004_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:59:51,200 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 0.9985828
2015-10-19 15:59:51,325 INFO [Socket Reader #1 for port 63282] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 63282: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 15:59:51,434 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.23333333
2015-10-19 15:59:51,465 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_2 is : 0.6209487
2015-10-19 15:59:51,497 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000005_1 is : 1.0
2015-10-19 15:59:51,512 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0015_m_000005_1
2015-10-19 15:59:51,512 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:59:51,512 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000013 taskAttempt attempt_1445182159119_0015_m_000005_1
2015-10-19 15:59:51,512 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000005_1
2015-10-19 15:59:51,512 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:59:51,669 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 15:59:51,669 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.34725112
2015-10-19 15:59:51,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:59:51,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0015_m_000005_1
2015-10-19 15:59:51,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0015_m_000005_2
2015-10-19 15:59:51,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:59:51,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-19 15:59:51,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_2 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 15:59:51,700 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000018 taskAttempt attempt_1445182159119_0015_m_000005_2
2015-10-19 15:59:51,700 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000005_2
2015-10-19 15:59:51,700 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:59:51,872 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_2 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 15:59:51,872 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:59:51,887 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445182159119_0015_m_000005_2
2015-10-19 15:59:51,887 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000005_2 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 15:59:51,950 INFO [Socket Reader #1 for port 63282] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 63282: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 15:59:52,090 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:8 RackLocal:6
2015-10-19 15:59:52,106 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000006
2015-10-19 15:59:52,106 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:8 RackLocal:6
2015-10-19 15:59:52,122 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:59:52,497 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.7810486
2015-10-19 15:59:52,653 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 15:59:53,153 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000013
2015-10-19 15:59:53,153 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000018
2015-10-19 15:59:53,153 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:8 RackLocal:6
2015-10-19 15:59:53,153 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000005_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:59:53,153 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000005_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:59:53,309 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.4486067
2015-10-19 15:59:53,716 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 15:59:53,716 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.41588968
2015-10-19 15:59:54,513 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.26666668
2015-10-19 15:59:54,763 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.36323506
2015-10-19 15:59:54,763 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 15:59:55,575 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.8186901
2015-10-19 15:59:55,794 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 15:59:56,591 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.4486067
2015-10-19 15:59:56,841 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 15:59:57,138 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.44950172
2015-10-19 15:59:57,528 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.26666668
2015-10-19 15:59:57,810 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.36323506
2015-10-19 15:59:57,825 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 15:59:58,606 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.8501881
2015-10-19 15:59:58,841 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 15:59:59,810 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.4486067
2015-10-19 15:59:59,825 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:00,388 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.44950172
2015-10-19 16:00:00,544 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.26666668
2015-10-19 16:00:00,825 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:00,841 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.36323506
2015-10-19 16:00:01,654 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.8821919
2015-10-19 16:00:01,825 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:02,857 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:03,075 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.45229465
2015-10-19 16:00:03,591 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.44950172
2015-10-19 16:00:03,591 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.26666668
2015-10-19 16:00:03,872 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:03,888 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.38818955
2015-10-19 16:00:04,669 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.91083086
2015-10-19 16:00:04,888 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:05,951 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:06,576 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.52770644
2015-10-19 16:00:06,669 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.26666668
2015-10-19 16:00:07,013 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:07,013 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.4429348
2015-10-19 16:00:07,013 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.44950172
2015-10-19 16:00:07,701 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.9424485
2015-10-19 16:00:07,998 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:09,013 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:09,685 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.26666668
2015-10-19 16:00:09,841 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.5343203
2015-10-19 16:00:09,998 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:10,045 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.4486067
2015-10-19 16:00:10,295 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.46255228
2015-10-19 16:00:10,732 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 0.9784974
2015-10-19 16:00:11,029 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:12,092 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:12,763 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.26666668
2015-10-19 16:00:12,888 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_1 is : 1.0
2015-10-19 16:00:12,920 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0015_m_000008_1
2015-10-19 16:00:12,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000008_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 16:00:12,920 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000017 taskAttempt attempt_1445182159119_0015_m_000008_1
2015-10-19 16:00:12,920 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000008_1
2015-10-19 16:00:12,920 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 16:00:13,248 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.4486067
2015-10-19 16:00:13,248 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:13,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000008_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 16:00:13,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0015_m_000008_1
2015-10-19 16:00:13,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0015_m_000008_0
2015-10-19 16:00:13,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 16:00:13,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-19 16:00:13,326 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000008_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 16:00:13,326 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000010 taskAttempt attempt_1445182159119_0015_m_000008_0
2015-10-19 16:00:13,326 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000008_0
2015-10-19 16:00:13,326 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 16:00:13,357 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.5343203
2015-10-19 16:00:13,732 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000008_0 is : 0.53521925
2015-10-19 16:00:13,779 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000008_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 16:00:13,779 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 16:00:13,951 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0015_m_000008
2015-10-19 16:00:13,951 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 16:00:13,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:8 RackLocal:6
2015-10-19 16:00:14,014 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445182159119_0015_m_000008_0
2015-10-19 16:00:14,014 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000008_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 16:00:14,326 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 11 maxEvents 10000
2015-10-19 16:00:14,935 INFO [Socket Reader #1 for port 63282] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 63282: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 16:00:15,014 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000017
2015-10-19 16:00:15,014 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000010
2015-10-19 16:00:15,014 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:8 RackLocal:6
2015-10-19 16:00:15,014 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000008_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 16:00:15,014 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 16:00:15,342 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:15,779 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:16,311 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.4486067
2015-10-19 16:00:16,342 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:16,404 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.5343203
2015-10-19 16:00:17,357 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:18,389 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:18,811 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:19,342 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.5343203
2015-10-19 16:00:19,404 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:19,483 INFO [IPC Server handler 18 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.5343203
2015-10-19 16:00:20,717 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:21,748 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:21,842 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:22,561 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.5858985
2015-10-19 16:00:22,795 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:22,873 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.5343203
2015-10-19 16:00:23,811 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:24,795 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:24,858 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:25,592 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.6199081
2015-10-19 16:00:25,795 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:25,905 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.5343203
2015-10-19 16:00:26,795 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:27,796 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:27,889 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:28,624 INFO [IPC Server handler 13 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.6199081
2015-10-19 16:00:28,796 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:28,936 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.5343203
2015-10-19 16:00:29,796 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:30,796 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:30,889 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:31,655 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.6199081
2015-10-19 16:00:31,811 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:31,968 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.6199081
2015-10-19 16:00:32,796 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:33,796 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:33,890 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:34,687 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.6199081
2015-10-19 16:00:34,812 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:35,030 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.6199081
2015-10-19 16:00:35,843 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:36,859 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:36,921 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:37,640 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.6199081
2015-10-19 16:00:37,718 INFO [IPC Server handler 6 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.667
2015-10-19 16:00:37,859 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:38,062 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.6199081
2015-10-19 16:00:38,874 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:39,859 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:39,937 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:40,749 INFO [IPC Server handler 23 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.667
2015-10-19 16:00:40,859 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:41,093 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.653662
2015-10-19 16:00:41,546 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.653662
2015-10-19 16:00:41,859 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:42,875 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:42,937 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:43,781 INFO [IPC Server handler 5 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.667
2015-10-19 16:00:43,859 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:44,140 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.667
2015-10-19 16:00:44,859 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:45,859 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:45,953 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:46,812 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.67177254
2015-10-19 16:00:46,859 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:47,172 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.667
2015-10-19 16:00:47,859 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:48,859 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:48,969 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:49,844 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.70883244
2015-10-19 16:00:49,859 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:50,219 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.67285043
2015-10-19 16:00:50,859 INFO [IPC Server handler 0 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:51,859 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:52,000 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:52,875 INFO [IPC Server handler 11 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.7461427
2015-10-19 16:00:52,906 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:53,516 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.69286704
2015-10-19 16:00:54,422 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:55,016 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:55,422 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:55,906 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.782709
2015-10-19 16:00:56,422 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:56,563 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.721921
2015-10-19 16:00:57,422 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:58,032 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:00:58,422 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:58,938 INFO [IPC Server handler 7 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.81952167
2015-10-19 16:00:59,438 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:00:59,594 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.7459801
2015-10-19 16:01:00,438 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:01,047 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:01:01,454 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:01,985 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.85606265
2015-10-19 16:01:02,454 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:02,626 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.77830607
2015-10-19 16:01:03,454 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:04,063 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:01:04,454 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:05,110 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.89345837
2015-10-19 16:01:05,454 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:05,673 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.7974596
2015-10-19 16:01:06,454 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:07,079 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:01:07,454 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:08,141 INFO [IPC Server handler 16 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.93089706
2015-10-19 16:01:08,454 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:08,704 INFO [IPC Server handler 28 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.830658
2015-10-19 16:01:09,454 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:10,095 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:01:10,454 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:11,173 INFO [IPC Server handler 4 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 0.9670336
2015-10-19 16:01:11,454 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:11,735 INFO [IPC Server handler 28 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_2 is : 0.8512903
2015-10-19 16:01:12,454 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:13,110 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:01:13,454 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:14,204 INFO [IPC Server handler 19 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 1.0
2015-10-19 16:01:14,267 INFO [IPC Server handler 28 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_m_000003_0 is : 1.0
2015-10-19 16:01:14,267 INFO [IPC Server handler 12 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0015_m_000003_0
2015-10-19 16:01:14,267 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 16:01:14,267 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000005 taskAttempt attempt_1445182159119_0015_m_000003_0
2015-10-19 16:01:14,267 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000003_0
2015-10-19 16:01:14,267 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 16:01:14,298 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 16:01:14,298 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0015_m_000003_0
2015-10-19 16:01:14,298 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0015_m_000003_2
2015-10-19 16:01:14,298 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 16:01:14,298 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-19 16:01:14,298 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_2 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 16:01:14,298 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000019 taskAttempt attempt_1445182159119_0015_m_000003_2
2015-10-19 16:01:14,298 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_m_000003_2
2015-10-19 16:01:14,298 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 16:01:14,329 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_2 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 16:01:14,329 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 16:01:14,345 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/_temporary/attempt_1445182159119_0015_m_000003_2
2015-10-19 16:01:14,345 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_m_000003_2 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 16:01:14,392 INFO [Socket Reader #1 for port 63282] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 63282: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 16:01:14,454 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 12 maxEvents 10000
2015-10-19 16:01:14,845 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:8 RackLocal:6
2015-10-19 16:01:15,470 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:15,861 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000005
2015-10-19 16:01:15,861 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0015_01_000019
2015-10-19 16:01:15,861 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:18 ContRel:0 HostLocal:8 RackLocal:6
2015-10-19 16:01:15,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 16:01:15,861 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0015_m_000003_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 16:01:16,126 INFO [IPC Server handler 9 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:01:16,454 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:17,486 INFO [IPC Server handler 15 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:18,501 INFO [IPC Server handler 29 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:19,517 INFO [IPC Server handler 26 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:19,517 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:01:20,533 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:21,548 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:22,548 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:22,564 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:01:23,564 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:24,564 INFO [IPC Server handler 17 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:25,580 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:25,580 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:01:26,611 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:27,627 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:28,596 INFO [IPC Server handler 3 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:01:28,627 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:29,627 INFO [IPC Server handler 8 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:30,643 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0015_r_000000_0. startIndex 13 maxEvents 10000
2015-10-19 16:01:30,752 INFO [IPC Server handler 1 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:01:30,815 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.3
2015-10-19 16:01:31,643 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.6681117
2015-10-19 16:01:34,643 INFO [IPC Server handler 24 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.6845107
2015-10-19 16:01:37,659 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.70074564
2015-10-19 16:01:40,659 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.71618605
2015-10-19 16:01:43,675 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.734731
2015-10-19 16:01:46,690 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.7513056
2015-10-19 16:01:49,706 INFO [IPC Server handler 26 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.7687461
2015-10-19 16:01:52,722 INFO [IPC Server handler 26 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.78582823
2015-10-19 16:01:55,738 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.80023164
2015-10-19 16:01:58,754 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.81599027
2015-10-19 16:02:01,769 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.83195156
2015-10-19 16:02:04,785 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.8472697
2015-10-19 16:02:07,801 INFO [IPC Server handler 2 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.8635596
2015-10-19 16:02:10,817 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.87999624
2015-10-19 16:02:13,833 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.8955437
2015-10-19 16:02:16,848 INFO [IPC Server handler 27 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.9097612
2015-10-19 16:02:19,864 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.9246205
2015-10-19 16:02:22,880 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.94022745
2015-10-19 16:02:25,911 INFO [IPC Server handler 26 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.9554022
2015-10-19 16:02:28,927 INFO [IPC Server handler 22 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.9721118
2015-10-19 16:02:31,958 INFO [IPC Server handler 14 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 0.9891529
2015-10-19 16:02:34,209 INFO [IPC Server handler 25 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445182159119_0015_r_000000_0
2015-10-19 16:02:34,209 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-19 16:02:34,209 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445182159119_0015_r_000000_0 given a go for committing the task output.
2015-10-19 16:02:34,209 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445182159119_0015_r_000000_0
2015-10-19 16:02:34,209 INFO [IPC Server handler 20 on 63282] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445182159119_0015_r_000000_0:true
2015-10-19 16:02:34,256 INFO [IPC Server handler 21 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0015_r_000000_0 is : 1.0
2015-10-19 16:02:34,271 INFO [IPC Server handler 10 on 63282] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0015_r_000000_0
2015-10-19 16:02:34,271 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 16:02:34,271 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0015_01_000015 taskAttempt attempt_1445182159119_0015_r_000000_0
2015-10-19 16:02:34,271 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0015_r_000000_0
2015-10-19 16:02:34,271 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 16:02:34,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0015_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 16:02:34,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0015_r_000000_0
2015-10-19 16:02:34,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0015_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 16:02:34,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-19 16:02:34,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0015Job Transitioned from RUNNING to COMMITTING
2015-10-19 16:02:34,318 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-19 16:02:34,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-19 16:02:34,459 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0015Job Transitioned from COMMITTING to SUCCEEDED
2015-10-19 16:02:34,459 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-19 16:02:34,459 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-19 16:02:34,459 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-19 16:02:34,459 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-19 16:02:34,459 INFO [Thread-115] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-19 16:02:34,459 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-19 16:02:34,474 INFO [Thread-115] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-19 16:02:34,709 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0015/job_1445182159119_0015_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0015-1445240991382-msrabi-pagerank-1445241754459-10-1-SUCCEEDED-default-1445241361172.jhist_tmp
2015-10-19 16:02:34,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:18 ContRel:0 HostLocal:8 RackLocal:6
2015-10-19 16:02:34,943 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0015-1445240991382-msrabi-pagerank-1445241754459-10-1-SUCCEEDED-default-1445241361172.jhist_tmp
2015-10-19 16:02:34,959 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0015/job_1445182159119_0015_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0015_conf.xml_tmp
2015-10-19 16:02:35,224 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0015_conf.xml_tmp
2015-10-19 16:02:35,240 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0015.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0015.summary
2015-10-19 16:02:35,256 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0015_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0015_conf.xml
2015-10-19 16:02:35,568 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0015-1445240991382-msrabi-pagerank-1445241754459-10-1-SUCCEEDED-default-1445241361172.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0015-1445240991382-msrabi-pagerank-1445241754459-10-1-SUCCEEDED-default-1445241361172.jhist
2015-10-19 16:02:35,646 INFO [Thread-115] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-19 16:02:35,646 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-19 16:02:35,646 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MININT-FNANLI5.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445182159119_0015
2015-10-19 16:02:35,677 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-19 16:02:36,693 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:18 ContRel:0 HostLocal:8 RackLocal:6
2015-10-19 16:02:36,693 INFO [Thread-115] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0015
2015-10-19 16:02:36,724 INFO [Thread-115] org.apache.hadoop.ipc.Server: Stopping server on 63282
2015-10-19 16:02:36,724 INFO [IPC Server listener on 63282] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 63282
2015-10-19 16:02:36,724 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-19 16:02:36,724 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
