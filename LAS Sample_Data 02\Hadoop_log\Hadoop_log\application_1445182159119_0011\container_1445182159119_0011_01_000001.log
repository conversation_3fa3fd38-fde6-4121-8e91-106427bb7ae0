2015-10-19 15:49:51,154 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445182159119_0011_000001
2015-10-19 15:49:52,128 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-19 15:49:52,128 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 11 cluster_timestamp: 1445182159119 } attemptId: 1 } keyId: 1694045684)
2015-10-19 15:49:52,537 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-19 15:49:53,874 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-19 15:49:54,057 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-19 15:49:54,136 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-19 15:49:54,141 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-19 15:49:54,143 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-19 15:49:54,145 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-19 15:49:54,146 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-19 15:49:54,161 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-19 15:49:54,162 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-19 15:49:54,165 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-19 15:49:54,300 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 15:49:54,367 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 15:49:54,412 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 15:49:54,446 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-19 15:49:54,575 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-19 15:49:55,130 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 15:49:55,289 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 15:49:55,289 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-19 15:49:55,312 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445182159119_0011 to jobTokenSecretManager
2015-10-19 15:49:55,657 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445182159119_0011 because: not enabled; too many maps; too much input;
2015-10-19 15:49:55,687 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445182159119_0011 = 1256521728. Number of splits = 10
2015-10-19 15:49:55,689 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445182159119_0011 = 1
2015-10-19 15:49:55,690 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0011Job Transitioned from NEW to INITED
2015-10-19 15:49:55,692 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445182159119_0011.
2015-10-19 15:49:55,758 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 15:49:55,785 INFO [Socket Reader #1 for port 51053] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 51053
2015-10-19 15:49:55,831 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-19 15:49:55,832 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 15:49:55,832 INFO [IPC Server listener on 51053] org.apache.hadoop.ipc.Server: IPC Server listener on 51053: starting
2015-10-19 15:49:55,834 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at 04DN8IQ.fareast.corp.microsoft.com/*************:51053
2015-10-19 15:49:55,995 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-19 15:49:56,002 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-19 15:49:56,017 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-19 15:49:56,024 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-19 15:49:56,025 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-19 15:49:56,029 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-19 15:49:56,029 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-19 15:49:56,048 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 51063
2015-10-19 15:49:56,048 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-19 15:49:56,118 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_51063_mapreduce____.z8iut4\webapp
2015-10-19 15:49:56,434 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:51063
2015-10-19 15:49:56,434 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 51063
2015-10-19 15:49:57,008 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-19 15:49:57,017 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 15:49:57,050 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445182159119_0011
2015-10-19 15:49:57,061 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 15:49:57,061 INFO [IPC Server listener on 51066] org.apache.hadoop.ipc.Server: IPC Server listener on 51066: starting
2015-10-19 15:49:57,061 INFO [Socket Reader #1 for port 51066] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 51066
2015-10-19 15:49:57,088 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-19 15:49:57,088 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-19 15:49:57,088 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-19 15:49:57,185 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-19 15:49:57,328 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-19 15:49:57,328 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-19 15:49:57,335 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-19 15:49:57,340 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-19 15:49:57,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0011Job Transitioned from INITED to SETUP
2015-10-19 15:49:57,358 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-19 15:49:57,375 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0011Job Transitioned from SETUP to RUNNING
2015-10-19 15:49:57,409 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,414 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,421 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,422 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,422 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,422 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,423 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,423 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,423 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,424 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,424 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,424 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,424 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,424 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,425 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,425 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,425 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,427 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,427 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,427 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,428 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,428 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,428 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,429 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,429 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,429 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,430 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,432 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,432 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,432 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,433 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,433 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,433 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,433 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,433 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,434 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,434 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,434 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,436 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-19 15:49:57,449 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-19 15:49:57,489 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445182159119_0011, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0011/job_1445182159119_0011_1.jhist
2015-10-19 15:49:58,333 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 15:49:58,386 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:14336, vCores:-13> knownNMs=4
2015-10-19 15:49:58,388 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:14336, vCores:-13>
2015-10-19 15:49:58,388 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:49:59,414 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-19 15:49:59,417 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000002 to attempt_1445182159119_0011_m_000000_0
2015-10-19 15:49:59,420 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000003 to attempt_1445182159119_0011_m_000001_0
2015-10-19 15:49:59,421 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:12288, vCores:-15>
2015-10-19 15:49:59,421 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:49:59,421 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:2 RackLocal:0
2015-10-19 15:49:59,498 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:59,530 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0011/job.jar
2015-10-19 15:49:59,537 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0011/job.xml
2015-10-19 15:49:59,538 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-19 15:49:59,538 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-19 15:49:59,538 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-19 15:49:59,606 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:49:59,610 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:59,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:49:59,612 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000002 taskAttempt attempt_1445182159119_0011_m_000000_0
2015-10-19 15:49:59,612 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000003 taskAttempt attempt_1445182159119_0011_m_000001_0
2015-10-19 15:49:59,616 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000001_0
2015-10-19 15:49:59,616 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000000_0
2015-10-19 15:49:59,618 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:49:59,650 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:49:59,711 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000000_0 : 13562
2015-10-19 15:49:59,711 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000001_0 : 13562
2015-10-19 15:49:59,713 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000000_0] using containerId: [container_1445182159119_0011_01_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:49:59,717 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:49:59,717 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000001_0] using containerId: [container_1445182159119_0011_01_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:49:59,717 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:49:59,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000000
2015-10-19 15:49:59,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:49:59,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000001
2015-10-19 15:49:59,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:50:00,427 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:12288, vCores:-15> knownNMs=4
2015-10-19 15:50:00,428 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:12288, vCores:-15>
2015-10-19 15:50:00,428 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:01,436 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:50:01,436 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:01,437 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000004 to attempt_1445182159119_0011_m_000002_0
2015-10-19 15:50:01,437 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-17>
2015-10-19 15:50:01,437 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:01,437 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:2 RackLocal:1
2015-10-19 15:50:01,437 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:01,439 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:50:01,440 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000004 taskAttempt attempt_1445182159119_0011_m_000002_0
2015-10-19 15:50:01,440 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000002_0
2015-10-19 15:50:01,440 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:50:01,461 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000002_0 : 13562
2015-10-19 15:50:01,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000002_0] using containerId: [container_1445182159119_0011_01_000004 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 15:50:01,462 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:50:01,463 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000002
2015-10-19 15:50:01,463 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:50:02,464 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:7168, vCores:-20> knownNMs=4
2015-10-19 15:50:02,464 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:50:02,486 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:02,487 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000005 to attempt_1445182159119_0011_m_000003_0
2015-10-19 15:50:02,487 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-20>
2015-10-19 15:50:02,487 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:02,487 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:2 RackLocal:2
2015-10-19 15:50:02,487 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:02,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:50:02,500 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000005 taskAttempt attempt_1445182159119_0011_m_000003_0
2015-10-19 15:50:02,501 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000003_0
2015-10-19 15:50:02,501 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:50:02,559 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000003_0 : 13562
2015-10-19 15:50:02,559 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000003_0] using containerId: [container_1445182159119_0011_01_000005 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 15:50:02,560 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:50:02,560 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000003
2015-10-19 15:50:02,560 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:50:03,501 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-20> knownNMs=4
2015-10-19 15:50:03,501 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-20>
2015-10-19 15:50:03,501 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:03,510 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:50:03,549 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000002 asked for a task
2015-10-19 15:50:03,550 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000002 given task: attempt_1445182159119_0011_m_000000_0
2015-10-19 15:50:03,695 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:50:03,729 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000003 asked for a task
2015-10-19 15:50:03,730 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000003 given task: attempt_1445182159119_0011_m_000001_0
2015-10-19 15:50:04,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-19 15:50:04,512 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:04,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000006 to attempt_1445182159119_0011_m_000004_0
2015-10-19 15:50:04,512 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:04,513 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000007 to attempt_1445182159119_0011_m_000005_0
2015-10-19 15:50:04,513 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:04,513 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-19 15:50:04,513 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:04,513 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:2 RackLocal:4
2015-10-19 15:50:04,514 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:50:04,514 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:04,515 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:50:04,517 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000006 taskAttempt attempt_1445182159119_0011_m_000004_0
2015-10-19 15:50:04,517 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000004_0
2015-10-19 15:50:04,517 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:50:04,518 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000007 taskAttempt attempt_1445182159119_0011_m_000005_0
2015-10-19 15:50:04,518 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000005_0
2015-10-19 15:50:04,519 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:50:04,532 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:50:04,572 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000004 asked for a task
2015-10-19 15:50:04,572 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000004 given task: attempt_1445182159119_0011_m_000002_0
2015-10-19 15:50:04,661 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000004_0 : 13562
2015-10-19 15:50:04,661 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000005_0 : 13562
2015-10-19 15:50:04,662 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000004_0] using containerId: [container_1445182159119_0011_01_000006 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 15:50:04,662 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:50:04,663 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000005_0] using containerId: [container_1445182159119_0011_01_000007 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 15:50:04,663 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:50:04,663 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000004
2015-10-19 15:50:04,663 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:50:04,664 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000005
2015-10-19 15:50:04,664 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:50:05,534 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-19 15:50:05,534 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:50:05,534 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:05,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000008 to attempt_1445182159119_0011_m_000006_0
2015-10-19 15:50:05,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 15:50:05,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:05,535 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:2 RackLocal:5
2015-10-19 15:50:05,535 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:05,536 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:50:05,596 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000008 taskAttempt attempt_1445182159119_0011_m_000006_0
2015-10-19 15:50:05,596 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000006_0
2015-10-19 15:50:05,597 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:50:05,911 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000006_0 : 13562
2015-10-19 15:50:05,911 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000006_0] using containerId: [container_1445182159119_0011_01_000008 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 15:50:05,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:50:05,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000006
2015-10-19 15:50:05,912 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:50:06,557 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-19 15:50:06,557 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 15:50:06,557 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:07,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 15:50:07,568 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:08,418 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:50:08,526 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000005 asked for a task
2015-10-19 15:50:08,526 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000005 given task: attempt_1445182159119_0011_m_000003_0
2015-10-19 15:50:08,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-19 15:50:08,650 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:08,651 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000009 to attempt_1445182159119_0011_m_000007_0
2015-10-19 15:50:08,651 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:08,651 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000010 to attempt_1445182159119_0011_m_000008_0
2015-10-19 15:50:08,651 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:08,651 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:08,651 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:08,651 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:2 RackLocal:7
2015-10-19 15:50:08,652 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:50:08,653 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:08,654 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:50:08,837 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000009 taskAttempt attempt_1445182159119_0011_m_000007_0
2015-10-19 15:50:08,837 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000007_0
2015-10-19 15:50:08,837 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:50:08,843 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000010 taskAttempt attempt_1445182159119_0011_m_000008_0
2015-10-19 15:50:08,844 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000008_0
2015-10-19 15:50:08,844 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:50:09,684 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:50:09,687 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:09,687 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:10,154 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000007_0 : 13562
2015-10-19 15:50:10,154 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000007_0] using containerId: [container_1445182159119_0011_01_000009 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 15:50:10,155 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:50:10,156 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000007
2015-10-19 15:50:10,156 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:50:10,244 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000008_0 : 13562
2015-10-19 15:50:10,245 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000008_0] using containerId: [container_1445182159119_0011_01_000010 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 15:50:10,245 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:50:10,246 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000008
2015-10-19 15:50:10,246 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:50:10,725 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:10,725 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:11,090 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.10635664
2015-10-19 15:50:11,310 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.1066108
2015-10-19 15:50:11,730 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:11,730 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:12,163 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:50:12,387 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000006 asked for a task
2015-10-19 15:50:12,387 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000006 given task: attempt_1445182159119_0011_m_000004_0
2015-10-19 15:50:12,426 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:50:12,516 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000008 asked for a task
2015-10-19 15:50:12,517 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000008 given task: attempt_1445182159119_0011_m_000006_0
2015-10-19 15:50:12,933 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:12,933 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:14,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:14,045 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:14,136 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.10635664
2015-10-19 15:50:14,336 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.1066108
2015-10-19 15:50:15,100 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:15,100 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:16,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:16,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:17,013 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.043799527
2015-10-19 15:50:17,156 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:17,156 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:17,157 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.10635664
2015-10-19 15:50:17,380 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.1066108
2015-10-19 15:50:18,255 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:18,256 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:19,323 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:19,323 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:20,059 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.07783586
2015-10-19 15:50:20,239 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.10635664
2015-10-19 15:50:20,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:20,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:20,482 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.1196146
2015-10-19 15:50:21,748 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:21,748 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:22,478 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.02475067
2015-10-19 15:50:22,848 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:22,848 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:22,939 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.032565646
2015-10-19 15:50:23,158 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.09737154
2015-10-19 15:50:23,300 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.18799508
2015-10-19 15:50:23,556 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.19211523
2015-10-19 15:50:23,918 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:23,919 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:24,815 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:50:25,020 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:25,020 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:25,095 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000007 asked for a task
2015-10-19 15:50:25,095 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000007 given task: attempt_1445182159119_0011_m_000005_0
2015-10-19 15:50:25,530 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.03907969
2015-10-19 15:50:25,980 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.04949992
2015-10-19 15:50:26,094 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:26,094 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:26,200 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.10660437
2015-10-19 15:50:26,379 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.19158794
2015-10-19 15:50:26,632 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.19211523
2015-10-19 15:50:27,174 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:27,175 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:28,279 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:28,279 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:28,585 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.052108407
2015-10-19 15:50:29,038 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.07425456
2015-10-19 15:50:29,285 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.10660437
2015-10-19 15:50:29,365 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:29,367 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:29,450 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.19158794
2015-10-19 15:50:29,595 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:50:29,697 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.19211523
2015-10-19 15:50:29,794 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.06267243
2015-10-19 15:50:29,904 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000010 asked for a task
2015-10-19 15:50:29,904 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000010 given task: attempt_1445182159119_0011_m_000008_0
2015-10-19 15:50:30,475 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:30,475 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:31,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:31,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:31,736 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.07009807
2015-10-19 15:50:32,207 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.103559315
2015-10-19 15:50:32,441 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.10660437
2015-10-19 15:50:32,534 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.19158794
2015-10-19 15:50:32,618 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:32,618 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:32,780 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.21377033
2015-10-19 15:50:33,439 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.09927732
2015-10-19 15:50:33,680 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:33,680 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:33,745 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:50:33,904 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000009 asked for a task
2015-10-19 15:50:33,904 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000009 given task: attempt_1445182159119_0011_m_000007_0
2015-10-19 15:50:34,740 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:34,740 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:34,947 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.10419792
2015-10-19 15:50:35,445 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.106964506
2015-10-19 15:50:35,558 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.25821903
2015-10-19 15:50:35,630 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.10660437
2015-10-19 15:50:35,756 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:35,756 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:35,792 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.27776006
2015-10-19 15:50:36,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:36,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:36,928 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.106493875
2015-10-19 15:50:37,777 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:37,777 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:38,090 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.10680563
2015-10-19 15:50:38,572 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.106964506
2015-10-19 15:50:38,640 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.27696857
2015-10-19 15:50:38,746 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.10660437
2015-10-19 15:50:38,959 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.27776006
2015-10-19 15:50:38,968 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:38,968 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:40,185 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:40,185 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:40,814 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.106493875
2015-10-19 15:50:41,211 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:41,211 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:41,291 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.10680563
2015-10-19 15:50:41,674 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.27696857
2015-10-19 15:50:41,829 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.106964506
2015-10-19 15:50:41,949 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.10660437
2015-10-19 15:50:41,988 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.27776006
2015-10-19 15:50:42,229 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:42,229 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:43,280 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:43,281 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:44,303 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:44,303 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:44,457 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.10680563
2015-10-19 15:50:44,628 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.106493875
2015-10-19 15:50:44,690 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.27696857
2015-10-19 15:50:45,006 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.106964506
2015-10-19 15:50:45,011 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.27776006
2015-10-19 15:50:45,161 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.14628091
2015-10-19 15:50:45,280 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.01530493
2015-10-19 15:50:45,323 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:45,324 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:46,361 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:46,361 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:47,386 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:47,386 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:47,693 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.10680563
2015-10-19 15:50:47,708 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.32750285
2015-10-19 15:50:48,037 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.35597754
2015-10-19 15:50:48,336 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.106964506
2015-10-19 15:50:48,425 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:48,425 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:48,470 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.19176641
2015-10-19 15:50:49,446 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:49,447 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:49,717 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.038429566
2015-10-19 15:50:50,029 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.106493875
2015-10-19 15:50:50,457 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:50,457 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:50,730 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.3624012
2015-10-19 15:50:50,827 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.10680563
2015-10-19 15:50:51,041 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.36319977
2015-10-19 15:50:51,477 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:51,477 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:51,513 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.106964506
2015-10-19 15:50:51,636 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.19212553
2015-10-19 15:50:52,494 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:52,495 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:53,543 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:53,543 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:53,743 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.3624012
2015-10-19 15:50:53,988 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.10680563
2015-10-19 15:50:54,063 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.36319977
2015-10-19 15:50:54,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:50:54,561 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000011 to attempt_1445182159119_0011_m_000009_0
2015-10-19 15:50:54,562 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:54,562 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:54,562 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:3 RackLocal:7
2015-10-19 15:50:54,566 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:54,567 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:50:54,600 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000011 taskAttempt attempt_1445182159119_0011_m_000009_0
2015-10-19 15:50:54,600 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000009_0
2015-10-19 15:50:54,600 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:50:54,700 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.106964506
2015-10-19 15:50:54,802 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.05536549
2015-10-19 15:50:54,803 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.19212553
2015-10-19 15:50:55,184 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000009_0 : 13562
2015-10-19 15:50:55,185 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000009_0] using containerId: [container_1445182159119_0011_01_000011 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:50:55,185 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:50:55,186 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000009
2015-10-19 15:50:55,186 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:50:55,239 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.106493875
2015-10-19 15:50:55,594 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:50:56,792 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.3624012
2015-10-19 15:50:57,075 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.10680563
2015-10-19 15:50:57,128 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.36319977
2015-10-19 15:50:57,753 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.1335273
2015-10-19 15:50:57,865 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.19212553
2015-10-19 15:50:58,234 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:50:58,294 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000011 asked for a task
2015-10-19 15:50:58,295 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000011 given task: attempt_1445182159119_0011_m_000009_0
2015-10-19 15:50:59,437 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.07490596
2015-10-19 15:50:59,847 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.41965222
2015-10-19 15:51:00,038 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.106493875
2015-10-19 15:51:00,171 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.13613394
2015-10-19 15:51:00,179 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.43026468
2015-10-19 15:51:00,405 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.009765966
2015-10-19 15:51:00,727 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.037197795
2015-10-19 15:51:01,082 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.1745664
2015-10-19 15:51:01,284 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.19212553
2015-10-19 15:51:02,866 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.44789755
2015-10-19 15:51:03,198 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.448704
2015-10-19 15:51:03,332 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.17093073
2015-10-19 15:51:03,969 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.099674605
2015-10-19 15:51:04,347 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.19266446
2015-10-19 15:51:04,583 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.19212553
2015-10-19 15:51:04,968 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.106493875
2015-10-19 15:51:05,119 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.027166743
2015-10-19 15:51:05,480 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.06480621
2015-10-19 15:51:05,682 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 0.295472
2015-10-19 15:51:05,880 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.44789755
2015-10-19 15:51:06,222 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.448704
2015-10-19 15:51:06,466 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.19242907
2015-10-19 15:51:07,573 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.19266446
2015-10-19 15:51:07,761 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.19212553
2015-10-19 15:51:08,417 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.10685723
2015-10-19 15:51:08,682 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 0.295472
2015-10-19 15:51:08,886 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.44789755
2015-10-19 15:51:09,229 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.448704
2015-10-19 15:51:09,542 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.12961935
2015-10-19 15:51:09,542 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.03907866
2015-10-19 15:51:09,667 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.19242907
2015-10-19 15:51:10,223 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.08499993
2015-10-19 15:51:10,707 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.19266446
2015-10-19 15:51:10,920 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.19600937
2015-10-19 15:51:11,710 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 0.295472
2015-10-19 15:51:11,915 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.5029271
2015-10-19 15:51:12,260 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.49787349
2015-10-19 15:51:12,902 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.19242907
2015-10-19 15:51:13,586 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.10685723
2015-10-19 15:51:14,045 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.19266446
2015-10-19 15:51:14,270 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.24885534
2015-10-19 15:51:14,720 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 0.34266463
2015-10-19 15:51:14,774 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.16317457
2015-10-19 15:51:15,010 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.53341997
2015-10-19 15:51:15,295 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.53425497
2015-10-19 15:51:15,640 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.054387096
2015-10-19 15:51:16,009 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.10583903
2015-10-19 15:51:16,277 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.19242907
2015-10-19 15:51:17,371 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.19266446
2015-10-19 15:51:17,608 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.27772525
2015-10-19 15:51:17,752 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 0.5323719
2015-10-19 15:51:18,041 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.53341997
2015-10-19 15:51:18,296 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.53425497
2015-10-19 15:51:18,667 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.10685723
2015-10-19 15:51:19,513 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.19242907
2015-10-19 15:51:20,757 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.19266446
2015-10-19 15:51:20,768 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 0.5323719
2015-10-19 15:51:20,947 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.19209063
2015-10-19 15:51:20,971 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.27772525
2015-10-19 15:51:21,053 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.53341997
2015-10-19 15:51:21,297 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.53425497
2015-10-19 15:51:22,224 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.10681946
2015-10-19 15:51:22,483 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.09495758
2015-10-19 15:51:22,804 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.19242907
2015-10-19 15:51:23,791 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 0.5323719
2015-10-19 15:51:23,973 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.19266446
2015-10-19 15:51:24,066 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.5848647
2015-10-19 15:51:24,194 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.27772525
2015-10-19 15:51:24,301 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.6158779
2015-10-19 15:51:24,336 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.10685723
2015-10-19 15:51:25,970 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.19209063
2015-10-19 15:51:26,006 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 0.5323719
2015-10-19 15:51:26,030 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.19242907
2015-10-19 15:51:26,801 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 0.667
2015-10-19 15:51:27,074 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.10681946
2015-10-19 15:51:27,079 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.61898744
2015-10-19 15:51:27,225 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.19266446
2015-10-19 15:51:27,313 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.6197233
2015-10-19 15:51:27,463 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.27772525
2015-10-19 15:51:28,079 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.106881365
2015-10-19 15:51:29,290 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.19242907
2015-10-19 15:51:29,395 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.10685723
2015-10-19 15:51:29,883 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 0.667
2015-10-19 15:51:30,133 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.61898744
2015-10-19 15:51:30,352 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.22765124
2015-10-19 15:51:30,367 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.6197233
2015-10-19 15:51:30,586 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.27772525
2015-10-19 15:51:31,399 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.19209063
2015-10-19 15:51:32,274 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.10681946
2015-10-19 15:51:32,399 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.22016151
2015-10-19 15:51:32,977 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 0.667
2015-10-19 15:51:33,211 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.61898744
2015-10-19 15:51:33,430 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.64743507
2015-10-19 15:51:33,477 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.106881365
2015-10-19 15:51:33,586 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.26771095
2015-10-19 15:51:33,789 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.27772525
2015-10-19 15:51:33,883 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.10685723
2015-10-19 15:51:34,227 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.64743507
2015-10-19 15:51:35,430 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.61898744
2015-10-19 15:51:35,571 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.25012234
2015-10-19 15:51:36,071 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 0.72733223
2015-10-19 15:51:36,258 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.667
2015-10-19 15:51:36,367 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.19209063
2015-10-19 15:51:36,477 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.667
2015-10-19 15:51:36,867 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.2783809
2015-10-19 15:51:37,086 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.27772525
2015-10-19 15:51:37,289 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.10681946
2015-10-19 15:51:38,571 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.10685723
2015-10-19 15:51:38,618 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.106881365
2015-10-19 15:51:38,743 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.2781602
2015-10-19 15:51:39,086 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 0.8337293
2015-10-19 15:51:39,258 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.667
2015-10-19 15:51:39,493 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.667
2015-10-19 15:51:40,118 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.2783809
2015-10-19 15:51:40,336 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.27772525
2015-10-19 15:51:41,555 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.19209063
2015-10-19 15:51:41,977 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.2781602
2015-10-19 15:51:42,118 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 0.93183255
2015-10-19 15:51:42,321 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.667
2015-10-19 15:51:42,524 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.66831696
2015-10-19 15:51:42,665 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.10681946
2015-10-19 15:51:43,352 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.2783809
2015-10-19 15:51:43,540 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.32918158
2015-10-19 15:51:43,555 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.106881365
2015-10-19 15:51:44,211 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.10685723
2015-10-19 15:51:45,071 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000009_0 is : 1.0
2015-10-19 15:51:45,086 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0011_m_000009_0
2015-10-19 15:51:45,086 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:51:45,086 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000011 taskAttempt attempt_1445182159119_0011_m_000009_0
2015-10-19 15:51:45,086 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000009_0
2015-10-19 15:51:45,086 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:51:45,180 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:51:45,196 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0011_m_000009_0
2015-10-19 15:51:45,196 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:51:45,196 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-19 15:51:45,196 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.2781602
2015-10-19 15:51:45,336 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.6855569
2015-10-19 15:51:45,571 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:3 RackLocal:7
2015-10-19 15:51:45,571 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:45,571 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-19 15:51:45,571 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-19 15:51:45,571 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:3 RackLocal:7
2015-10-19 15:51:45,571 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.6967995
2015-10-19 15:51:45,836 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0011_m_000005
2015-10-19 15:51:45,836 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:51:45,836 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0011_m_000005
2015-10-19 15:51:45,836 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:51:45,836 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:51:45,836 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000005_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:51:46,524 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.19209063
2015-10-19 15:51:46,571 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:3 RackLocal:7
2015-10-19 15:51:46,602 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.2783809
2015-10-19 15:51:46,618 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:51:46,618 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000011
2015-10-19 15:51:46,633 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:3 RackLocal:7
2015-10-19 15:51:46,633 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:51:46,727 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.36317363
2015-10-19 15:51:47,915 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.10681946
2015-10-19 15:51:48,352 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.71590894
2015-10-19 15:51:48,462 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.2781602
2015-10-19 15:51:48,508 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.106881365
2015-10-19 15:51:48,602 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.7251127
2015-10-19 15:51:49,727 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.10685723
2015-10-19 15:51:49,868 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.2783809
2015-10-19 15:51:50,055 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.36317363
2015-10-19 15:51:51,368 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.74828553
2015-10-19 15:51:51,508 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.19209063
2015-10-19 15:51:51,602 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.75915414
2015-10-19 15:51:51,633 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.2781602
2015-10-19 15:51:53,259 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.2783809
2015-10-19 15:51:53,415 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.10681946
2015-10-19 15:51:53,430 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.36317363
2015-10-19 15:51:54,165 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.106881365
2015-10-19 15:51:54,384 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.7809541
2015-10-19 15:51:54,634 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.8047365
2015-10-19 15:51:54,884 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.2781602
2015-10-19 15:51:55,134 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.10685723
2015-10-19 15:51:56,430 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.30656084
2015-10-19 15:51:56,587 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.36317363
2015-10-19 15:51:56,712 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.19209063
2015-10-19 15:51:57,430 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.8151027
2015-10-19 15:51:57,696 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.85862744
2015-10-19 15:51:58,024 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.2781602
2015-10-19 15:51:58,306 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.10681946
2015-10-19 15:51:59,134 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.106881365
2015-10-19 15:51:59,696 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.35785177
2015-10-19 15:51:59,868 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.36317363
2015-10-19 15:52:00,087 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.10685723
2015-10-19 15:52:00,493 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.85262746
2015-10-19 15:52:00,774 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.8928052
2015-10-19 15:52:01,352 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.3221006
2015-10-19 15:52:01,509 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.19209063
2015-10-19 15:52:03,165 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.36404583
2015-10-19 15:52:03,306 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.36317363
2015-10-19 15:52:03,306 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.10681946
2015-10-19 15:52:03,571 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.8856076
2015-10-19 15:52:03,821 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.9318129
2015-10-19 15:52:03,821 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.106881365
2015-10-19 15:52:04,306 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:52:04,306 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-19 15:52:04,306 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000012 to attempt_1445182159119_0011_r_000000_0
2015-10-19 15:52:04,306 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:3 RackLocal:7
2015-10-19 15:52:04,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:04,337 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:52:04,337 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000012 taskAttempt attempt_1445182159119_0011_r_000000_0
2015-10-19 15:52:04,337 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_r_000000_0
2015-10-19 15:52:04,337 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:52:04,540 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_r_000000_0 : 13562
2015-10-19 15:52:04,540 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_r_000000_0] using containerId: [container_1445182159119_0011_01_000012 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:52:04,540 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:52:04,540 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_r_000000
2015-10-19 15:52:04,540 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:52:04,759 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.36288428
2015-10-19 15:52:05,056 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.13234818
2015-10-19 15:52:05,337 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:52:06,274 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.19209063
2015-10-19 15:52:06,446 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.36404583
2015-10-19 15:52:06,556 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.36317363
2015-10-19 15:52:06,587 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.9171617
2015-10-19 15:52:06,821 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 0.97474104
2015-10-19 15:52:07,368 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:52:07,368 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000013 to attempt_1445182159119_0011_m_000005_1
2015-10-19 15:52:07,368 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:4 RackLocal:7
2015-10-19 15:52:07,368 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:07,368 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000005_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:52:07,368 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000013 taskAttempt attempt_1445182159119_0011_m_000005_1
2015-10-19 15:52:07,368 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000005_1
2015-10-19 15:52:07,368 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:52:07,446 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:52:07,446 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000005_1 : 13562
2015-10-19 15:52:07,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000005_1] using containerId: [container_1445182159119_0011_01_000013 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:52:07,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000005_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:52:07,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000005
2015-10-19 15:52:07,478 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_r_000012 asked for a task
2015-10-19 15:52:07,478 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_r_000012 given task: attempt_1445182159119_0011_r_000000_0
2015-10-19 15:52:07,868 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.10681946
2015-10-19 15:52:07,962 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0011_m_000007
2015-10-19 15:52:07,962 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0011_m_000007
2015-10-19 15:52:07,962 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:52:07,962 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:07,962 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:07,962 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000007_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:52:07,978 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.36388028
2015-10-19 15:52:08,368 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:4 RackLocal:7
2015-10-19 15:52:08,384 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:52:08,399 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.106881365
2015-10-19 15:52:08,868 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000001_0 is : 1.0
2015-10-19 15:52:08,915 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0011_m_000001_0
2015-10-19 15:52:08,915 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:52:08,915 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000003 taskAttempt attempt_1445182159119_0011_m_000001_0
2015-10-19 15:52:08,915 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000001_0
2015-10-19 15:52:08,915 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:52:09,071 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:52:09,071 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0011_m_000001_0
2015-10-19 15:52:09,071 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:52:09,071 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-19 15:52:09,400 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:4 RackLocal:7
2015-10-19 15:52:09,400 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:52:09,400 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000014 to attempt_1445182159119_0011_m_000007_1
2015-10-19 15:52:09,400 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:5 RackLocal:7
2015-10-19 15:52:09,400 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:09,400 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000007_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:52:09,400 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000014 taskAttempt attempt_1445182159119_0011_m_000007_1
2015-10-19 15:52:09,400 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000007_1
2015-10-19 15:52:09,400 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:52:09,493 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000007_1 : 13562
2015-10-19 15:52:09,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000007_1] using containerId: [container_1445182159119_0011_01_000014 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:52:09,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000007_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:52:09,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000007
2015-10-19 15:52:09,603 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.95198274
2015-10-19 15:52:09,696 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.36404583
2015-10-19 15:52:09,775 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 0 maxEvents 10000
2015-10-19 15:52:09,790 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.41353014
2015-10-19 15:52:10,353 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.16657135
2015-10-19 15:52:10,431 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:52:10,431 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000003
2015-10-19 15:52:10,431 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:5 RackLocal:7
2015-10-19 15:52:10,431 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:52:10,681 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:52:10,728 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000013 asked for a task
2015-10-19 15:52:10,728 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000013 given task: attempt_1445182159119_0011_m_000005_1
2015-10-19 15:52:10,806 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 15:52:11,103 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.19209063
2015-10-19 15:52:11,181 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.36388028
2015-10-19 15:52:11,837 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 15:52:12,650 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 0.9843791
2015-10-19 15:52:12,837 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 15:52:12,931 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.36404583
2015-10-19 15:52:12,946 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:52:12,978 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000014 asked for a task
2015-10-19 15:52:12,978 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000014 given task: attempt_1445182159119_0011_m_000007_1
2015-10-19 15:52:13,071 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.44859612
2015-10-19 15:52:13,228 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.11412561
2015-10-19 15:52:13,587 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.106881365
2015-10-19 15:52:13,868 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 15:52:14,056 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000000_0 is : 1.0
2015-10-19 15:52:14,056 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0011_m_000000_0
2015-10-19 15:52:14,056 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:52:14,056 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000002 taskAttempt attempt_1445182159119_0011_m_000000_0
2015-10-19 15:52:14,056 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000000_0
2015-10-19 15:52:14,056 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:52:14,150 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:52:14,150 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0011_m_000000_0
2015-10-19 15:52:14,150 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:52:14,150 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-19 15:52:14,384 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.36388028
2015-10-19 15:52:14,509 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:5 RackLocal:7
2015-10-19 15:52:14,509 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000002
2015-10-19 15:52:14,509 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:5 RackLocal:7
2015-10-19 15:52:14,509 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:52:14,868 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 15:52:15,603 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.19247705
2015-10-19 15:52:15,697 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.06666667
2015-10-19 15:52:15,900 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:16,165 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.36404583
2015-10-19 15:52:16,353 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.44859612
2015-10-19 15:52:16,368 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.19964348
2015-10-19 15:52:16,915 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:17,587 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.36388028
2015-10-19 15:52:17,931 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:18,025 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.14627728
2015-10-19 15:52:18,150 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.10685723
2015-10-19 15:52:18,697 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.106881365
2015-10-19 15:52:18,728 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:52:18,931 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:19,447 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.36404583
2015-10-19 15:52:19,540 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.44859612
2015-10-19 15:52:19,947 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:20,525 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.097044505
2015-10-19 15:52:20,775 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.36388028
2015-10-19 15:52:20,869 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.19247705
2015-10-19 15:52:20,884 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.21853215
2015-10-19 15:52:20,947 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:21,212 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.10685723
2015-10-19 15:52:21,775 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:52:21,962 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:22,697 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.39504832
2015-10-19 15:52:22,806 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.44859612
2015-10-19 15:52:22,962 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0011_m_000008
2015-10-19 15:52:22,962 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0011_m_000008
2015-10-19 15:52:22,962 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:52:22,962 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:22,962 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:22,962 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000008_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:52:23,009 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:23,165 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.1787968
2015-10-19 15:52:23,494 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.12896802
2015-10-19 15:52:23,540 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.10681946
2015-10-19 15:52:23,603 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:5 RackLocal:7
2015-10-19 15:52:23,603 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:52:24,009 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.36388028
2015-10-19 15:52:24,040 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:24,290 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.10685723
2015-10-19 15:52:24,853 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:52:25,087 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:26,009 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.44366688
2015-10-19 15:52:26,072 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.19247705
2015-10-19 15:52:26,072 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.44859612
2015-10-19 15:52:26,103 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:26,541 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.25891602
2015-10-19 15:52:26,587 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.10681946
2015-10-19 15:52:27,087 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.3842216
2015-10-19 15:52:27,134 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:27,369 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.10685723
2015-10-19 15:52:27,931 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:52:28,181 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:28,900 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.19255035
2015-10-19 15:52:29,009 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.15744439
2015-10-19 15:52:29,056 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.44980705
2015-10-19 15:52:29,103 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.44859612
2015-10-19 15:52:29,212 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:29,666 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.10681946
2015-10-19 15:52:30,150 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.42436472
2015-10-19 15:52:30,244 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:30,478 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.13550867
2015-10-19 15:52:30,994 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:52:31,119 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.19247705
2015-10-19 15:52:31,291 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:31,869 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.27765483
2015-10-19 15:52:32,338 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.44980705
2015-10-19 15:52:32,369 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:32,478 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.44956806
2015-10-19 15:52:33,025 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.11865779
2015-10-19 15:52:33,291 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.44968578
2015-10-19 15:52:33,400 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:33,541 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.19247705
2015-10-19 15:52:34,056 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:52:34,431 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:34,447 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.19255035
2015-10-19 15:52:35,150 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.1909517
2015-10-19 15:52:35,447 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:35,510 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.44980705
2015-10-19 15:52:35,666 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.50284415
2015-10-19 15:52:36,072 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.19255035
2015-10-19 15:52:36,338 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.19247705
2015-10-19 15:52:36,478 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:36,588 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.19247705
2015-10-19 15:52:36,619 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.44968578
2015-10-19 15:52:37,135 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:52:37,228 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.27765483
2015-10-19 15:52:37,525 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:38,541 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:38,697 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.44980705
2015-10-19 15:52:38,885 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.5342037
2015-10-19 15:52:39,103 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.19255035
2015-10-19 15:52:39,275 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.19255035
2015-10-19 15:52:39,541 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:39,619 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.19247705
2015-10-19 15:52:39,838 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.44968578
2015-10-19 15:52:40,166 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:52:40,275 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.19258286
2015-10-19 15:52:40,541 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:41,557 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:41,744 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.19247705
2015-10-19 15:52:41,900 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.44980705
2015-10-19 15:52:41,978 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.27765483
2015-10-19 15:52:42,088 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.5342037
2015-10-19 15:52:42,119 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.19255035
2015-10-19 15:52:42,572 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:42,666 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.27119228
2015-10-19 15:52:43,010 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.44968578
2015-10-19 15:52:43,228 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:52:43,588 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:43,932 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.19255035
2015-10-19 15:52:44,588 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:45,119 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.44980705
2015-10-19 15:52:45,150 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.22432517
2015-10-19 15:52:45,307 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.5342037
2015-10-19 15:52:45,588 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:45,603 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.19258286
2015-10-19 15:52:45,697 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.27813601
2015-10-19 15:52:46,166 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.44968578
2015-10-19 15:52:46,260 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:52:46,619 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:46,682 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.19247705
2015-10-19 15:52:46,807 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.27765483
2015-10-19 15:52:47,635 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:48,182 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.27825075
2015-10-19 15:52:48,385 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.47507173
2015-10-19 15:52:48,525 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.5342037
2015-10-19 15:52:48,666 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:48,775 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.27813601
2015-10-19 15:52:49,307 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:52:49,338 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.44968578
2015-10-19 15:52:49,682 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:49,713 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.19255035
2015-10-19 15:52:50,697 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:50,822 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.19258286
2015-10-19 15:52:51,244 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.27825075
2015-10-19 15:52:51,541 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.19247705
2015-10-19 15:52:51,635 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.27765483
2015-10-19 15:52:51,729 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:51,854 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.27813601
2015-10-19 15:52:51,932 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.5287355
2015-10-19 15:52:51,947 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.5342037
2015-10-19 15:52:52,369 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:52:52,682 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.44968578
2015-10-19 15:52:52,729 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:53,744 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:54,088 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.19255035
2015-10-19 15:52:54,260 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.27825075
2015-10-19 15:52:54,744 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:54,916 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.36390656
2015-10-19 15:52:55,072 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.53543663
2015-10-19 15:52:55,104 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.5342037
2015-10-19 15:52:55,432 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:52:55,526 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.19258286
2015-10-19 15:52:55,807 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:56,010 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.47712594
2015-10-19 15:52:56,276 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.19247705
2015-10-19 15:52:56,838 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:56,838 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.27765483
2015-10-19 15:52:57,323 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.32670102
2015-10-19 15:52:57,901 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:57,979 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.36390656
2015-10-19 15:52:58,432 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.53543663
2015-10-19 15:52:58,526 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:52:58,557 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.5342037
2015-10-19 15:52:58,994 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:52:59,151 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.19255035
2015-10-19 15:52:59,291 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.5206042
2015-10-19 15:52:59,885 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.19258286
2015-10-19 15:53:00,041 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:00,432 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.3638923
2015-10-19 15:53:00,901 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.19247705
2015-10-19 15:53:01,088 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.36390656
2015-10-19 15:53:01,135 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:01,635 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:01,651 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.53543663
2015-10-19 15:53:01,776 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.54901826
2015-10-19 15:53:01,963 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.27765483
2015-10-19 15:53:02,198 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:02,526 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.5352028
2015-10-19 15:53:03,260 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:03,479 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.3638923
2015-10-19 15:53:04,120 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.19255035
2015-10-19 15:53:04,182 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.36390656
2015-10-19 15:53:04,323 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:04,745 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:04,932 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.53543663
2015-10-19 15:53:04,963 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.5917665
2015-10-19 15:53:05,057 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.19258286
2015-10-19 15:53:05,370 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:05,932 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.5352028
2015-10-19 15:53:05,932 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.19247705
2015-10-19 15:53:06,401 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:06,510 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.3638923
2015-10-19 15:53:06,885 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.27765483
2015-10-19 15:53:07,260 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.44950968
2015-10-19 15:53:07,463 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:07,823 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:08,354 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.6196791
2015-10-19 15:53:08,354 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.53543663
2015-10-19 15:53:08,510 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:08,573 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.19255035
2015-10-19 15:53:09,229 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.5352028
2015-10-19 15:53:09,542 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:09,542 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.4448123
2015-10-19 15:53:09,948 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.19258286
2015-10-19 15:53:10,292 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.44950968
2015-10-19 15:53:10,542 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:10,870 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:10,979 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.19905278
2015-10-19 15:53:11,573 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:11,588 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.6196791
2015-10-19 15:53:11,776 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.53543663
2015-10-19 15:53:11,823 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.27765483
2015-10-19 15:53:12,432 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.5352028
2015-10-19 15:53:12,557 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.44964966
2015-10-19 15:53:12,604 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:13,339 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.44950968
2015-10-19 15:53:13,620 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:13,667 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.19255035
2015-10-19 15:53:13,901 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:14,620 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:14,714 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.6196791
2015-10-19 15:53:14,870 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.19258286
2015-10-19 15:53:15,010 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.53543663
2015-10-19 15:53:15,573 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.44964966
2015-10-19 15:53:15,651 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:15,651 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.23102416
2015-10-19 15:53:15,729 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.5352028
2015-10-19 15:53:16,370 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.5264496
2015-10-19 15:53:16,495 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.27765483
2015-10-19 15:53:16,573 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:53:16,573 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000015 to attempt_1445182159119_0011_m_000008_1
2015-10-19 15:53:16,573 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:6 RackLocal:7
2015-10-19 15:53:16,573 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:53:16,573 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000008_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:53:16,573 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000015 taskAttempt attempt_1445182159119_0011_m_000008_1
2015-10-19 15:53:16,573 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000008_1
2015-10-19 15:53:16,573 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:53:16,651 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0011_m_000003
2015-10-19 15:53:16,651 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:53:16,651 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0011_m_000003
2015-10-19 15:53:16,651 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:53:16,651 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:53:16,651 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000003_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:53:16,698 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:16,948 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:17,604 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:6 RackLocal:7
2015-10-19 15:53:17,651 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:53:17,698 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:17,948 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000008_1 : 13562
2015-10-19 15:53:17,948 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000008_1] using containerId: [container_1445182159119_0011_01_000015 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:53:17,948 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000008_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:53:17,948 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000008
2015-10-19 15:53:18,073 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.6196791
2015-10-19 15:53:18,089 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.2005287
2015-10-19 15:53:18,276 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.5643932
2015-10-19 15:53:18,620 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.46457794
2015-10-19 15:53:18,761 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:18,886 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.5352028
2015-10-19 15:53:19,467 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.5352021
2015-10-19 15:53:19,468 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.19258286
2015-10-19 15:53:19,774 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:19,993 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:20,368 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.265755
2015-10-19 15:53:20,856 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:21,346 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.6196791
2015-10-19 15:53:21,499 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.61341226
2015-10-19 15:53:21,515 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:53:21,530 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000015 asked for a task
2015-10-19 15:53:21,530 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000015 given task: attempt_1445182159119_0011_m_000008_1
2015-10-19 15:53:21,593 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.27765483
2015-10-19 15:53:21,640 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.5352825
2015-10-19 15:53:21,858 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:22,200 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.5352028
2015-10-19 15:53:22,517 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.5352021
2015-10-19 15:53:22,751 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.23188366
2015-10-19 15:53:22,889 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:23,026 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:23,885 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:23,901 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.19258286
2015-10-19 15:53:24,682 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.5352825
2015-10-19 15:53:24,713 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.6196791
2015-10-19 15:53:24,901 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.6210422
2015-10-19 15:53:24,901 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:24,901 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:53:24,901 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000016 to attempt_1445182159119_0011_m_000003_1
2015-10-19 15:53:24,901 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:7 RackLocal:7
2015-10-19 15:53:24,901 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:53:24,901 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000003_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:53:24,901 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000016 taskAttempt attempt_1445182159119_0011_m_000003_1
2015-10-19 15:53:24,901 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000003_1
2015-10-19 15:53:24,901 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:53:25,041 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000003_1 : 13562
2015-10-19 15:53:25,041 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000003_1] using containerId: [container_1445182159119_0011_01_000016 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:53:25,041 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000003_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:53:25,041 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000003
2015-10-19 15:53:25,104 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.27813601
2015-10-19 15:53:25,510 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.5352028
2015-10-19 15:53:25,557 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.5352021
2015-10-19 15:53:25,901 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:25,916 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-28> knownNMs=4
2015-10-19 15:53:25,932 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.29995403
2015-10-19 15:53:26,073 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:26,932 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:27,448 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.25007236
2015-10-19 15:53:27,682 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.5352825
2015-10-19 15:53:27,932 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:53:27,963 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:27,963 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000016 asked for a task
2015-10-19 15:53:27,963 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000016 given task: attempt_1445182159119_0011_m_000003_1
2015-10-19 15:53:28,057 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.6196791
2015-10-19 15:53:28,291 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.6210422
2015-10-19 15:53:28,604 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.6209222
2015-10-19 15:53:28,604 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.21950817
2015-10-19 15:53:28,979 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.5793504
2015-10-19 15:53:29,041 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:29,120 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:29,151 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.106881365
2015-10-19 15:53:29,526 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.27813601
2015-10-19 15:53:30,073 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:30,729 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.5352825
2015-10-19 15:53:30,729 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.3188435
2015-10-19 15:53:31,088 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:31,495 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.6196791
2015-10-19 15:53:31,588 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.6210422
2015-10-19 15:53:31,651 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.6209487
2015-10-19 15:53:31,698 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0011_m_000004
2015-10-19 15:53:31,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0011_m_000004
2015-10-19 15:53:31,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:53:31,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:53:31,698 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000004_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:53:31,698 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:53:32,104 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:7 RackLocal:7
2015-10-19 15:53:32,104 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:32,104 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-28> knownNMs=4
2015-10-19 15:53:32,182 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:32,198 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.106881365
2015-10-19 15:53:32,417 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.61874807
2015-10-19 15:53:32,479 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.2714762
2015-10-19 15:53:32,917 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.24263285
2015-10-19 15:53:33,120 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:33,823 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.5665033
2015-10-19 15:53:34,167 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:34,385 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.27813601
2015-10-19 15:53:34,729 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.6209487
2015-10-19 15:53:34,838 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.6460092
2015-10-19 15:53:34,917 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.6210422
2015-10-19 15:53:35,276 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:35,276 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.11713131
2015-10-19 15:53:35,276 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:35,292 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.106493875
2015-10-19 15:53:35,698 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.6208445
2015-10-19 15:53:36,307 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:36,495 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.6460092
2015-10-19 15:53:36,838 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.620844
2015-10-19 15:53:36,917 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.34526896
2015-10-19 15:53:37,307 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:37,792 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.65441966
2015-10-19 15:53:37,870 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.27825075
2015-10-19 15:53:38,010 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.65441966
2015-10-19 15:53:38,245 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.6210422
2015-10-19 15:53:38,260 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.667
2015-10-19 15:53:38,292 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.106493875
2015-10-19 15:53:38,307 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:38,307 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.19258286
2015-10-19 15:53:38,323 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:39,073 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.6208445
2015-10-19 15:53:39,354 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:39,542 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.27345255
2015-10-19 15:53:39,870 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.620844
2015-10-19 15:53:40,385 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:40,573 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.27813601
2015-10-19 15:53:40,823 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.667
2015-10-19 15:53:41,339 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.106493875
2015-10-19 15:53:41,339 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.19258286
2015-10-19 15:53:41,339 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:41,385 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:41,479 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.667
2015-10-19 15:53:41,510 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.6210422
2015-10-19 15:53:42,245 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.6208445
2015-10-19 15:53:42,401 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:42,885 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.620844
2015-10-19 15:53:43,354 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.36323506
2015-10-19 15:53:43,417 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:43,854 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.667
2015-10-19 15:53:43,901 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.27825075
2015-10-19 15:53:44,370 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.16355944
2015-10-19 15:53:44,370 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:44,370 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.19258286
2015-10-19 15:53:44,417 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:44,839 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.667
2015-10-19 15:53:44,932 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.6210422
2015-10-19 15:53:44,979 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.27811313
2015-10-19 15:53:45,261 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.620844
2015-10-19 15:53:45,417 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.6208445
2015-10-19 15:53:45,417 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:45,901 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.667
2015-10-19 15:53:45,932 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.27813601
2015-10-19 15:53:46,432 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:46,932 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.66782546
2015-10-19 15:53:47,401 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.19209063
2015-10-19 15:53:47,432 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:47,432 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.2131846
2015-10-19 15:53:47,432 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:48,182 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.667
2015-10-19 15:53:48,198 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.6492036
2015-10-19 15:53:48,229 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.27825075
2015-10-19 15:53:48,448 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:48,495 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.36323506
2015-10-19 15:53:48,620 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.6208445
2015-10-19 15:53:48,917 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.667
2015-10-19 15:53:49,261 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.6492036
2015-10-19 15:53:49,464 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:49,698 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.27811313
2015-10-19 15:53:49,964 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.7009735
2015-10-19 15:53:50,433 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.19209063
2015-10-19 15:53:50,464 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:50,464 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.27811313
2015-10-19 15:53:50,479 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:51,104 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.27813601
2015-10-19 15:53:51,354 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.667
2015-10-19 15:53:51,417 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.667
2015-10-19 15:53:51,464 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:51,761 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.6208445
2015-10-19 15:53:51,948 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.667
2015-10-19 15:53:52,479 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:53,104 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.73423433
2015-10-19 15:53:53,401 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.27825075
2015-10-19 15:53:53,714 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.19209063
2015-10-19 15:53:53,714 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.36323506
2015-10-19 15:53:53,714 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.27811313
2015-10-19 15:53:53,714 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:53,714 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:54,573 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.667
2015-10-19 15:53:54,683 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.667
2015-10-19 15:53:54,839 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.6208445
2015-10-19 15:53:54,964 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:54,964 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.27811313
2015-10-19 15:53:54,964 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.6907413
2015-10-19 15:53:55,776 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.27813601
2015-10-19 15:53:56,151 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.75427777
2015-10-19 15:53:56,230 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:56,730 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.19879268
2015-10-19 15:53:56,761 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:53:56,761 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.27811313
2015-10-19 15:53:57,261 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:57,792 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.6799982
2015-10-19 15:53:57,839 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.667
2015-10-19 15:53:57,980 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.667
2015-10-19 15:53:57,980 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.667
2015-10-19 15:53:58,276 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:58,276 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.7178591
2015-10-19 15:53:58,683 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.36323506
2015-10-19 15:53:58,792 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.27825075
2015-10-19 15:53:59,183 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.7812927
2015-10-19 15:53:59,292 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:53:59,730 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.2656778
2015-10-19 15:53:59,777 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.30033186
2015-10-19 15:53:59,808 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:54:00,308 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:00,573 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.27813601
2015-10-19 15:54:00,573 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.27811313
2015-10-19 15:54:00,948 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.69645613
2015-10-19 15:54:01,073 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.667
2015-10-19 15:54:01,183 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.667
2015-10-19 15:54:01,292 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.7574252
2015-10-19 15:54:01,308 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:02,261 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.8085735
2015-10-19 15:54:02,355 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:02,745 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.27765483
2015-10-19 15:54:02,839 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.3637686
2015-10-19 15:54:02,870 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:54:03,417 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:03,870 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.36323506
2015-10-19 15:54:04,073 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.7120331
2015-10-19 15:54:04,105 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.27825075
2015-10-19 15:54:04,261 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.667
2015-10-19 15:54:04,308 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.8116423
2015-10-19 15:54:04,339 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.667
2015-10-19 15:54:04,417 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:05,292 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.84425485
2015-10-19 15:54:05,324 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.27813601
2015-10-19 15:54:05,417 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:05,464 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.27811313
2015-10-19 15:54:05,761 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.27765483
2015-10-19 15:54:05,870 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.3637686
2015-10-19 15:54:05,902 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:54:06,433 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:07,339 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.85409266
2015-10-19 15:54:07,464 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:07,464 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.728927
2015-10-19 15:54:07,511 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.6678772
2015-10-19 15:54:07,589 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.667
2015-10-19 15:54:08,324 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.8795631
2015-10-19 15:54:08,495 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:08,792 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.27765483
2015-10-19 15:54:08,933 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.3637686
2015-10-19 15:54:08,949 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:54:09,136 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.36323506
2015-10-19 15:54:09,511 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:09,527 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.27825075
2015-10-19 15:54:10,246 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.27813601
2015-10-19 15:54:10,371 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.88763404
2015-10-19 15:54:10,558 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.27811313
2015-10-19 15:54:10,714 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.74640757
2015-10-19 15:54:10,746 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.68450075
2015-10-19 15:54:10,839 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:10,839 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.667
2015-10-19 15:54:11,355 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.91484976
2015-10-19 15:54:11,808 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.29911834
2015-10-19 15:54:11,855 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:11,996 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.41564626
2015-10-19 15:54:11,996 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:54:12,886 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:13,386 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.92408895
2015-10-19 15:54:13,917 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:13,917 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.7627543
2015-10-19 15:54:13,964 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.70082927
2015-10-19 15:54:14,058 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.667
2015-10-19 15:54:14,230 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.27825075
2015-10-19 15:54:14,386 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.94939595
2015-10-19 15:54:14,793 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.36323506
2015-10-19 15:54:14,824 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.36323506
2015-10-19 15:54:14,933 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:15,011 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:54:15,027 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.44950172
2015-10-19 15:54:15,152 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.27813601
2015-10-19 15:54:15,871 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.27811313
2015-10-19 15:54:15,933 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:16,386 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 0.9711206
2015-10-19 15:54:16,964 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:17,168 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.7782467
2015-10-19 15:54:17,183 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.71593374
2015-10-19 15:54:17,308 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.6704303
2015-10-19 15:54:17,418 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 0.9843166
2015-10-19 15:54:17,839 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.36323506
2015-10-19 15:54:17,980 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:18,058 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.10000001
2015-10-19 15:54:18,074 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.44950172
2015-10-19 15:54:18,511 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_1 is : 1.0
2015-10-19 15:54:18,511 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0011_m_000007_1
2015-10-19 15:54:18,511 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000007_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:54:18,511 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000014 taskAttempt attempt_1445182159119_0011_m_000007_1
2015-10-19 15:54:18,511 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000007_1
2015-10-19 15:54:18,511 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:54:18,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000007_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:54:18,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0011_m_000007_1
2015-10-19 15:54:18,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0011_m_000007_0
2015-10-19 15:54:18,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:54:18,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-19 15:54:18,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000007_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 15:54:18,574 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000009 taskAttempt attempt_1445182159119_0011_m_000007_0
2015-10-19 15:54:18,574 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000007_0
2015-10-19 15:54:18,589 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:54:18,683 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000007_0 is : 0.27825075
2015-10-19 15:54:18,933 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_1 is : 1.0
2015-10-19 15:54:18,933 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0011_m_000005_1
2015-10-19 15:54:18,933 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000005_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:54:18,933 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000013 taskAttempt attempt_1445182159119_0011_m_000005_1
2015-10-19 15:54:18,933 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000005_1
2015-10-19 15:54:18,949 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:54:18,996 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:18,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000005_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:54:18,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0011_m_000005_1
2015-10-19 15:54:18,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0011_m_000005_0
2015-10-19 15:54:18,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:54:18,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-19 15:54:18,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000005_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 15:54:18,996 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000007 taskAttempt attempt_1445182159119_0011_m_000005_0
2015-10-19 15:54:18,996 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000005_0
2015-10-19 15:54:18,996 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:54:19,371 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:7 RackLocal:7
2015-10-19 15:54:19,418 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.29101187
2015-10-19 15:54:19,839 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.36323506
2015-10-19 15:54:20,011 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 15:54:20,293 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.7317102
2015-10-19 15:54:20,371 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.79470813
2015-10-19 15:54:20,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000014
2015-10-19 15:54:20,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000013
2015-10-19 15:54:20,402 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000007_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:54:20,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:54:20,402 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000005_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:54:20,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0011_01_000017 to attempt_1445182159119_0011_m_000004_1
2015-10-19 15:54:20,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:54:20,402 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:54:20,402 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000004_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:54:20,402 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0011_01_000017 taskAttempt attempt_1445182159119_0011_m_000004_1
2015-10-19 15:54:20,402 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0011_m_000004_1
2015-10-19 15:54:20,402 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:54:20,496 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0011_m_000004_1 : 13562
2015-10-19 15:54:20,496 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0011_m_000004_1] using containerId: [container_1445182159119_0011_01_000017 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:54:20,496 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000004_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:54:20,496 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0011_m_000004
2015-10-19 15:54:20,527 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.6864463
2015-10-19 15:54:20,543 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000007_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 15:54:20,543 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000005_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 15:54:20,574 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:54:20,621 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:54:20,621 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out2/_temporary/1/_temporary/attempt_1445182159119_0011_m_000005_0
2015-10-19 15:54:20,621 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out2/_temporary/1/_temporary/attempt_1445182159119_0011_m_000007_0
2015-10-19 15:54:20,621 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000005_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 15:54:20,621 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000007_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 15:54:20,652 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.27811313
2015-10-19 15:54:20,886 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.36323506
2015-10-19 15:54:21,027 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:21,105 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.13333334
2015-10-19 15:54:21,105 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.44950172
2015-10-19 15:54:21,433 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0011: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-28> knownNMs=4
2015-10-19 15:54:22,058 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:23,074 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:23,136 INFO [Socket Reader #1 for port 51066] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 51066: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 15:54:23,465 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.74730515
2015-10-19 15:54:23,496 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.81039983
2015-10-19 15:54:23,605 INFO [Socket Reader #1 for port 51066] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0011 (auth:SIMPLE)
2015-10-19 15:54:23,621 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.7008418
2015-10-19 15:54:23,636 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0011_m_000017 asked for a task
2015-10-19 15:54:23,636 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0011_m_000017 given task: attempt_1445182159119_0011_m_000004_1
2015-10-19 15:54:23,949 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.36323506
2015-10-19 15:54:24,090 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:24,152 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:54:24,168 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.44950172
2015-10-19 15:54:24,215 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000005_0 is : 0.32568184
2015-10-19 15:54:24,808 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.36323506
2015-10-19 15:54:25,090 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:25,933 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.27811313
2015-10-19 15:54:26,105 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:26,699 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.76260686
2015-10-19 15:54:26,808 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.82620215
2015-10-19 15:54:26,871 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.71597254
2015-10-19 15:54:26,965 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.41187334
2015-10-19 15:54:27,121 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:27,215 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.49461052
2015-10-19 15:54:27,215 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:54:28,121 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:29,058 INFO [Socket Reader #1 for port 51066] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 51066: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 15:54:29,137 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:29,699 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.36323506
2015-10-19 15:54:29,918 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.7782871
2015-10-19 15:54:29,980 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.84211934
2015-10-19 15:54:29,996 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.4486067
2015-10-19 15:54:30,074 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.7313868
2015-10-19 15:54:30,152 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:30,277 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:54:30,277 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.53521925
2015-10-19 15:54:30,465 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.27811313
2015-10-19 15:54:31,137 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.10662154
2015-10-19 15:54:31,152 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:31,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000009
2015-10-19 15:54:31,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:54:31,512 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:54:32,152 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:33,012 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.4486067
2015-10-19 15:54:33,074 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.79345167
2015-10-19 15:54:33,168 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.85768324
2015-10-19 15:54:33,168 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:33,293 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.7469245
2015-10-19 15:54:33,324 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.53521925
2015-10-19 15:54:33,527 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000007
2015-10-19 15:54:33,527 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:54:33,527 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:54:33,605 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:54:33,637 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.3784416
2015-10-19 15:54:34,152 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.10680563
2015-10-19 15:54:34,168 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:34,434 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.3097404
2015-10-19 15:54:35,184 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:36,059 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.4486067
2015-10-19 15:54:36,184 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:36,309 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.8084794
2015-10-19 15:54:36,356 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.53521925
2015-10-19 15:54:36,402 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.87321204
2015-10-19 15:54:36,449 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.7614907
2015-10-19 15:54:36,637 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:54:37,168 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.10680563
2015-10-19 15:54:37,199 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:37,527 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.39830872
2015-10-19 15:54:38,199 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:38,637 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.34835753
2015-10-19 15:54:39,074 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.4486067
2015-10-19 15:54:39,199 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:39,402 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.53521925
2015-10-19 15:54:39,512 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.82448083
2015-10-19 15:54:39,652 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.88987327
2015-10-19 15:54:39,668 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:54:39,793 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.77749306
2015-10-19 15:54:40,184 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.10680563
2015-10-19 15:54:40,215 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:41,246 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:42,090 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.49957326
2015-10-19 15:54:42,262 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:42,699 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.839063
2015-10-19 15:54:42,715 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:54:42,746 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.558971
2015-10-19 15:54:42,824 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.9042743
2015-10-19 15:54:42,949 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.4345466
2015-10-19 15:54:42,965 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.7917241
2015-10-19 15:54:43,199 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.10735287
2015-10-19 15:54:43,293 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:43,434 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.3637686
2015-10-19 15:54:44,340 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:45,121 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.5343203
2015-10-19 15:54:45,356 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:45,778 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:54:45,793 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.6207798
2015-10-19 15:54:46,074 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.85331243
2015-10-19 15:54:46,231 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.17086765
2015-10-19 15:54:46,309 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.9187076
2015-10-19 15:54:46,371 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:46,418 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.8057553
2015-10-19 15:54:47,387 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:47,621 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.4486067
2015-10-19 15:54:48,137 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.5343203
2015-10-19 15:54:48,168 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.3637686
2015-10-19 15:54:48,387 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:48,809 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:54:48,856 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.6207798
2015-10-19 15:54:49,262 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.19242907
2015-10-19 15:54:49,371 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.8655126
2015-10-19 15:54:49,418 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:49,637 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.9311862
2015-10-19 15:54:49,793 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.8182274
2015-10-19 15:54:50,450 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:51,153 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.5343203
2015-10-19 15:54:51,465 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:51,856 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:54:51,934 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.6207798
2015-10-19 15:54:52,278 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.19242907
2015-10-19 15:54:52,340 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.4486067
2015-10-19 15:54:52,481 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:52,684 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.87662935
2015-10-19 15:54:52,793 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.3637686
2015-10-19 15:54:52,981 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.9427165
2015-10-19 15:54:53,184 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.82929134
2015-10-19 15:54:53,497 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:54,200 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.5343203
2015-10-19 15:54:54,262 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.6207798
2015-10-19 15:54:54,528 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:54,918 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:54:54,981 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.667
2015-10-19 15:54:55,293 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.19242907
2015-10-19 15:54:55,543 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:55,887 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.8891916
2015-10-19 15:54:56,215 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.9562429
2015-10-19 15:54:56,387 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.84213585
2015-10-19 15:54:56,559 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:56,918 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.4486067
2015-10-19 15:54:57,200 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.5902203
2015-10-19 15:54:57,465 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.3637686
2015-10-19 15:54:57,559 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:57,934 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:54:58,012 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.667
2015-10-19 15:54:58,294 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.21816747
2015-10-19 15:54:58,590 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:59,200 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.9033918
2015-10-19 15:54:59,403 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.9698851
2015-10-19 15:54:59,622 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:59,669 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.8556595
2015-10-19 15:55:00,215 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.6199081
2015-10-19 15:55:00,622 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:55:00,981 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:55:01,044 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.667
2015-10-19 15:55:01,309 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.2781602
2015-10-19 15:55:01,434 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.4486067
2015-10-19 15:55:01,622 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:55:01,919 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.3637686
2015-10-19 15:55:02,325 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.91730684
2015-10-19 15:55:02,512 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 0.98401046
2015-10-19 15:55:02,637 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:55:02,794 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.87008446
2015-10-19 15:55:03,231 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.6199081
2015-10-19 15:55:03,669 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:55:04,012 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:55:04,091 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.6950592
2015-10-19 15:55:04,325 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.2781602
2015-10-19 15:55:04,684 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:55:05,512 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.93319696
2015-10-19 15:55:05,700 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 1.0
2015-10-19 15:55:05,700 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:55:05,872 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.4486067
2015-10-19 15:55:05,950 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.8848367
2015-10-19 15:55:06,091 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.3637686
2015-10-19 15:55:06,247 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.6199081
2015-10-19 15:55:06,262 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000002_0 is : 1.0
2015-10-19 15:55:06,262 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0011_m_000002_0
2015-10-19 15:55:06,262 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:55:06,262 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000004 taskAttempt attempt_1445182159119_0011_m_000002_0
2015-10-19 15:55:06,262 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000002_0
2015-10-19 15:55:06,262 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:55:06,700 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:55:06,700 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0011_m_000002_0
2015-10-19 15:55:06,700 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:55:06,700 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-19 15:55:06,731 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:55:07,091 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:55:07,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:55:07,137 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.7273299
2015-10-19 15:55:07,356 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.2781602
2015-10-19 15:55:07,763 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:08,231 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000004
2015-10-19 15:55:08,231 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:55:08,231 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:55:08,809 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:08,872 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.9459431
2015-10-19 15:55:09,225 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.89627755
2015-10-19 15:55:09,318 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.6360644
2015-10-19 15:55:09,858 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:10,159 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:55:10,189 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.7606351
2015-10-19 15:55:10,357 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.6360644
2015-10-19 15:55:10,358 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.4486067
2015-10-19 15:55:10,388 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.2781602
2015-10-19 15:55:10,490 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.3637686
2015-10-19 15:55:10,858 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:11,874 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:12,249 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.957573
2015-10-19 15:55:12,358 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.667
2015-10-19 15:55:12,655 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.90879303
2015-10-19 15:55:12,905 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:13,187 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:55:13,265 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.7946527
2015-10-19 15:55:13,405 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.2781602
2015-10-19 15:55:13,952 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:14,968 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:15,171 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.3637686
2015-10-19 15:55:15,312 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.4486067
2015-10-19 15:55:15,405 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.667
2015-10-19 15:55:15,530 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.96894395
2015-10-19 15:55:15,999 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:16,062 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.9199283
2015-10-19 15:55:16,265 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.16666667
2015-10-19 15:55:16,296 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.82823294
2015-10-19 15:55:16,468 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.31959486
2015-10-19 15:55:17,030 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:18,046 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:18,452 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.667
2015-10-19 15:55:18,905 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.9811089
2015-10-19 15:55:19,077 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:19,296 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.20000002
2015-10-19 15:55:19,327 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.9312312
2015-10-19 15:55:19,343 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.8611145
2015-10-19 15:55:19,468 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.36388028
2015-10-19 15:55:19,827 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.4486067
2015-10-19 15:55:20,077 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.3637686
2015-10-19 15:55:20,077 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:21,109 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:21,515 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.6828735
2015-10-19 15:55:22,140 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:22,296 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 0.99362886
2015-10-19 15:55:22,359 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.20000002
2015-10-19 15:55:22,390 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.8930999
2015-10-19 15:55:22,468 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.94264305
2015-10-19 15:55:22,499 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.36388028
2015-10-19 15:55:23,140 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:24,109 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.38662538
2015-10-19 15:55:24,156 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:24,359 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.4486067
2015-10-19 15:55:24,562 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.72192234
2015-10-19 15:55:24,765 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000006_0 is : 1.0
2015-10-19 15:55:24,765 INFO [IPC Server handler 15 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0011_m_000006_0
2015-10-19 15:55:24,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:55:24,765 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000008 taskAttempt attempt_1445182159119_0011_m_000006_0
2015-10-19 15:55:24,765 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000006_0
2015-10-19 15:55:24,765 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:55:25,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:55:25,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0011_m_000006_0
2015-10-19 15:55:25,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:55:25,109 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-19 15:55:25,156 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:55:25,452 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.20000002
2015-10-19 15:55:25,468 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.92588115
2015-10-19 15:55:25,531 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.36388028
2015-10-19 15:55:25,687 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:55:25,812 INFO [IPC Server handler 5 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.95549583
2015-10-19 15:55:26,187 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:26,734 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000008
2015-10-19 15:55:26,734 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:55:26,734 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:55:27,218 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:27,577 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.7546004
2015-10-19 15:55:28,234 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:28,484 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.4234792
2015-10-19 15:55:28,531 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.20000002
2015-10-19 15:55:28,531 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.9591197
2015-10-19 15:55:28,562 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.38138944
2015-10-19 15:55:28,687 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.46702844
2015-10-19 15:55:28,968 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.966464
2015-10-19 15:55:29,249 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:30,281 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:30,624 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.7881613
2015-10-19 15:55:31,296 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:31,593 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.20000002
2015-10-19 15:55:31,593 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.44968578
2015-10-19 15:55:31,593 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 0.99175936
2015-10-19 15:55:32,312 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:32,328 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.9787536
2015-10-19 15:55:32,453 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_1 is : 1.0
2015-10-19 15:55:32,468 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0011_m_000008_1
2015-10-19 15:55:32,468 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000008_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:55:32,468 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000015 taskAttempt attempt_1445182159119_0011_m_000008_1
2015-10-19 15:55:32,468 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000008_1
2015-10-19 15:55:32,468 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:55:32,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000008_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:55:32,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0011_m_000008_1
2015-10-19 15:55:32,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0011_m_000008_0
2015-10-19 15:55:32,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:55:32,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-19 15:55:32,718 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000008_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 15:55:32,718 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000010 taskAttempt attempt_1445182159119_0011_m_000008_0
2015-10-19 15:55:32,718 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000008_0
2015-10-19 15:55:32,734 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:55:32,937 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:55:33,343 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:33,499 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0011_m_000008
2015-10-19 15:55:33,499 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:55:33,640 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.82363707
2015-10-19 15:55:33,749 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000008_0 is : 0.44950172
2015-10-19 15:55:33,984 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000015
2015-10-19 15:55:33,984 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:55:33,984 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000008_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:55:34,218 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.49373415
2015-10-19 15:55:34,359 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:55:34,609 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.44968578
2015-10-19 15:55:34,624 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.23333333
2015-10-19 15:55:34,656 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000008_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 15:55:34,921 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:55:35,265 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out2/_temporary/1/_temporary/attempt_1445182159119_0011_m_000008_0
2015-10-19 15:55:35,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000008_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 15:55:35,484 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:55:36,296 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.989055
2015-10-19 15:55:36,484 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:55:36,656 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.8654189
2015-10-19 15:55:36,999 INFO [Socket Reader #1 for port 51066] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 51066: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 15:55:37,531 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:55:37,640 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.44968578
2015-10-19 15:55:37,671 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.23333333
2015-10-19 15:55:38,203 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.49601415
2015-10-19 15:55:38,437 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000010
2015-10-19 15:55:38,437 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:55:38,437 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:55:38,562 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:55:39,578 INFO [IPC Server handler 18 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 0.9995053
2015-10-19 15:55:39,578 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:55:39,687 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.90612197
2015-10-19 15:55:40,312 INFO [IPC Server handler 4 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_0 is : 1.0
2015-10-19 15:55:40,312 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0011_m_000004_0
2015-10-19 15:55:40,312 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:55:40,312 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000006 taskAttempt attempt_1445182159119_0011_m_000004_0
2015-10-19 15:55:40,312 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000004_0
2015-10-19 15:55:40,312 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:55:40,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:55:40,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0011_m_000004_0
2015-10-19 15:55:40,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0011_m_000004_1
2015-10-19 15:55:40,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:55:40,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-19 15:55:40,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000004_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 15:55:40,468 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000017 taskAttempt attempt_1445182159119_0011_m_000004_1
2015-10-19 15:55:40,468 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000004_1
2015-10-19 15:55:40,468 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:55:40,484 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:55:40,656 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:55:40,734 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000004_1 is : 0.4540429
2015-10-19 15:55:40,781 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.23333333
2015-10-19 15:55:40,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000004_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 15:55:40,875 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:55:40,906 INFO [Socket Reader #1 for port 51066] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 51066: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 15:55:40,937 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out2/_temporary/1/_temporary/attempt_1445182159119_0011_m_000004_1
2015-10-19 15:55:40,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000004_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 15:55:41,640 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000006
2015-10-19 15:55:41,640 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:55:41,640 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:55:41,750 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:42,703 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000017
2015-10-19 15:55:42,703 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:55:42,703 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000004_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:55:42,812 INFO [IPC Server handler 9 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.9418042
2015-10-19 15:55:42,828 INFO [IPC Server handler 17 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:43,218 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.53022873
2015-10-19 15:55:43,890 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.23333333
2015-10-19 15:55:43,937 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:44,984 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:45,875 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 0.9820465
2015-10-19 15:55:46,078 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:46,984 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.23333333
2015-10-19 15:55:46,984 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_1 is : 1.0
2015-10-19 15:55:47,093 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0011_m_000003_1
2015-10-19 15:55:47,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000003_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:55:47,093 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000016 taskAttempt attempt_1445182159119_0011_m_000003_1
2015-10-19 15:55:47,093 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000003_1
2015-10-19 15:55:47,093 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:55:47,125 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:47,328 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000003_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:55:47,328 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0011_m_000003_1
2015-10-19 15:55:47,328 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0011_m_000003_0
2015-10-19 15:55:47,328 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:55:47,328 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-19 15:55:47,328 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000003_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 15:55:47,328 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000005 taskAttempt attempt_1445182159119_0011_m_000003_0
2015-10-19 15:55:47,328 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_m_000003_0
2015-10-19 15:55:47,328 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:55:47,422 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.5343203
2015-10-19 15:55:47,968 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:55:48,234 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:48,422 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000003_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 15:55:48,422 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:55:48,500 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0011_m_000003
2015-10-19 15:55:48,500 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:55:48,609 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out2/_temporary/1/_temporary/attempt_1445182159119_0011_m_000003_0
2015-10-19 15:55:48,609 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_m_000003_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 15:55:49,515 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 15:55:49,687 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000016
2015-10-19 15:55:49,687 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:55:49,687 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000003_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:55:50,250 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.3
2015-10-19 15:55:50,750 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 15:55:51,500 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_m_000003_0 is : 0.5343203
2015-10-19 15:55:51,922 INFO [IPC Server handler 3 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 15:55:52,984 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 15:55:53,500 INFO [Socket Reader #1 for port 51066] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 51066: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 15:55:53,969 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.3
2015-10-19 15:55:54,281 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 15:55:55,328 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 15:55:55,609 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000005
2015-10-19 15:55:55,609 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:55:55,609 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:55:56,375 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 15:55:57,109 INFO [IPC Server handler 25 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.3
2015-10-19 15:55:57,500 INFO [IPC Server handler 14 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 15:55:58,547 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 15:55:59,687 INFO [IPC Server handler 13 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 15:56:00,359 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.3
2015-10-19 15:56:00,750 INFO [IPC Server handler 19 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0011_r_000000_0. startIndex 10 maxEvents 10000
2015-10-19 15:56:01,266 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.3
2015-10-19 15:56:01,344 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.3
2015-10-19 15:56:03,453 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.67563456
2015-10-19 15:56:06,547 INFO [IPC Server handler 27 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.6955242
2015-10-19 15:56:09,625 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.7164373
2015-10-19 15:56:12,688 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.73716915
2015-10-19 15:56:15,766 INFO [IPC Server handler 20 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.7523251
2015-10-19 15:56:18,813 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.7725005
2015-10-19 15:56:21,844 INFO [IPC Server handler 24 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.7910883
2015-10-19 15:56:24,953 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.81084394
2015-10-19 15:56:27,985 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.83094
2015-10-19 15:56:31,032 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.85079205
2015-10-19 15:56:34,079 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.87068105
2015-10-19 15:56:37,125 INFO [IPC Server handler 6 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.89103186
2015-10-19 15:56:40,204 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.91067755
2015-10-19 15:56:43,266 INFO [IPC Server handler 10 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.9181271
2015-10-19 15:56:46,344 INFO [IPC Server handler 12 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.9213128
2015-10-19 15:56:49,454 INFO [IPC Server handler 8 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.9290966
2015-10-19 15:56:52,501 INFO [IPC Server handler 0 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.93590385
2015-10-19 15:56:55,657 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.9438654
2015-10-19 15:56:58,751 INFO [IPC Server handler 11 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.9542379
2015-10-19 15:57:01,782 INFO [IPC Server handler 28 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.9653466
2015-10-19 15:57:04,860 INFO [IPC Server handler 7 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.97340024
2015-10-19 15:57:07,939 INFO [IPC Server handler 2 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.98686725
2015-10-19 15:57:11,017 INFO [IPC Server handler 23 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 0.99666286
2015-10-19 15:57:14,079 INFO [IPC Server handler 16 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 1.0
2015-10-19 15:57:17,111 INFO [IPC Server handler 21 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 1.0
2015-10-19 15:57:22,783 INFO [IPC Server handler 29 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445182159119_0011_r_000000_0
2015-10-19 15:57:22,783 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-19 15:57:22,783 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445182159119_0011_r_000000_0 given a go for committing the task output.
2015-10-19 15:57:22,783 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445182159119_0011_r_000000_0
2015-10-19 15:57:22,783 INFO [IPC Server handler 26 on 51066] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445182159119_0011_r_000000_0:true
2015-10-19 15:57:22,829 INFO [IPC Server handler 22 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0011_r_000000_0 is : 1.0
2015-10-19 15:57:22,845 INFO [IPC Server handler 1 on 51066] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0011_r_000000_0
2015-10-19 15:57:22,845 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:57:22,845 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0011_01_000012 taskAttempt attempt_1445182159119_0011_r_000000_0
2015-10-19 15:57:22,845 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0011_r_000000_0
2015-10-19 15:57:22,845 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:57:22,892 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0011_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:57:22,892 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0011_r_000000_0
2015-10-19 15:57:22,892 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0011_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:57:22,892 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-19 15:57:22,892 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0011Job Transitioned from RUNNING to COMMITTING
2015-10-19 15:57:22,892 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-19 15:57:23,033 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-19 15:57:23,033 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0011Job Transitioned from COMMITTING to SUCCEEDED
2015-10-19 15:57:23,064 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-19 15:57:23,064 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-19 15:57:23,064 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-19 15:57:23,064 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-19 15:57:23,064 INFO [Thread-112] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-19 15:57:23,064 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-19 15:57:23,064 INFO [Thread-112] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-19 15:57:23,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:57:24,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0011_01_000012
2015-10-19 15:57:24,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:57:24,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0011_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:57:24,704 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0011/job_1445182159119_0011_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0011-1445240987301-msrabi-pagerank-1445241443017-10-1-SUCCEEDED-default-1445240997344.jhist_tmp
2015-10-19 15:57:25,142 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0011-1445240987301-msrabi-pagerank-1445241443017-10-1-SUCCEEDED-default-1445240997344.jhist_tmp
2015-10-19 15:57:25,142 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0011/job_1445182159119_0011_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0011_conf.xml_tmp
2015-10-19 15:57:27,704 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0011_conf.xml_tmp
2015-10-19 15:57:27,720 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0011.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0011.summary
2015-10-19 15:57:27,720 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0011_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0011_conf.xml
2015-10-19 15:57:27,720 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0011-1445240987301-msrabi-pagerank-1445241443017-10-1-SUCCEEDED-default-1445240997344.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0011-1445240987301-msrabi-pagerank-1445241443017-10-1-SUCCEEDED-default-1445240997344.jhist
2015-10-19 15:57:27,720 INFO [Thread-112] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-19 15:57:27,736 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-19 15:57:27,736 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://04DN8IQ.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445182159119_0011
2015-10-19 15:57:27,767 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-19 15:57:28,767 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-19 15:57:28,767 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0011
2015-10-19 15:57:28,783 INFO [Thread-112] org.apache.hadoop.ipc.Server: Stopping server on 51066
2015-10-19 15:57:28,783 INFO [IPC Server listener on 51066] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 51066
2015-10-19 15:57:28,783 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
2015-10-19 15:57:28,783 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
