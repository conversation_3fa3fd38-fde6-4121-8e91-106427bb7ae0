# flake8: noqa
# file is mostly autogenerated

import numpy as np


class Bunch(dict):
    def __init__(self, **kw):
        dict.__init__(self, kw)
        self.__dict__ = self


table = np.array([
    -239.63921146434,   23.82402100183, -10.058722305774,  8.408247034e-24,
    -286.33343459485, -192.94498833383, np.nan,  1.9599639845401,
                   0,  3403.2422719355,  9.5252071796205,  357.28800515928,
                   0,  3384.5732089181,  3421.9113349528, np.nan,
     1.9599639845401,                0,  64.408589362646,  27.526987262221,
     2.3398343141983,  .01929229642928,  10.456685725799,  118.36049299949,
    np.nan,  1.9599639845401,                0,  160.95125844367,
     26.616203359744,  6.0471155960247,  1.474619593e-09,  108.78445845338,
     213.11805843396, np.nan,  1.9599639845401,                0,
     2.5468279240787,  2.0843242435815,   1.221896224602,  .22174687335763,
    -1.5383725254448,  6.6320283736022, np.nan,  1.9599639845401,
                   0, -71.328597501646,   19.64700882358, -3.6305067169329,
     .00028286534285,  -109.8360271998, -32.821167803489, np.nan,
     1.9599639845401,                0,  3202.7457127058,  54.010820719402,
     59.298223393878,                0,  3096.8864493203,  3308.6049760912,
    np.nan,  1.9599639845401,                0,  25.111330718591,
     40.375410330977,  .62194614278197,   .5339772824573, -54.023019391151,
     104.24568082833, np.nan,  1.9599639845401,                0,
     133.66170465247,   40.86442742188,  3.2708571509534,  .00107222056235,
     53.568898656735,  213.75451064821, np.nan,  1.9599639845401,
                   0, -7.3708810422984,  4.2181700723084, -1.7474120094605,
     .08056589577917, -15.638342464688,  .89658038009082, np.nan,
     1.9599639845401,                0,  41.439913440842,   39.70711783121,
     1.0436394204434,  .29665224735146, -36.384607438218,   119.2644343199,
    np.nan,  1.9599639845401,                0,   3227.169253308,
     104.40590209261,  30.909835446329,  8.81095776e-210,   3022.537445433,
     3431.8010611829, np.nan,  1.9599639845401,                0
    ]).reshape(12,9)

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = 'r1vs0.mbsmoke 0.mbsmoke prenatal1 mmarried mage fbaby _cons prenatal1 mmarried mage fbaby _cons'.split()

table_t = np.array([
    -223.30165083857,  22.742195167601, -9.8188257198974,  9.342544938e-23,
    -267.87553429645, -178.72776738069, np.nan,  1.9599639845401,
                   0,  3360.9613730608,  12.757489072993,  263.45006872676,
                   0,  3335.9571539446,   3385.965592177, np.nan,
     1.9599639845401,                0,  64.408589362646,  27.526987262221,
     2.3398343141983,  .01929229642928,  10.456685725799,  118.36049299949,
    np.nan,  1.9599639845401,                0,  160.95125844367,
     26.616203359744,  6.0471155960247,  1.474619593e-09,  108.78445845338,
     213.11805843396, np.nan,  1.9599639845401,                0,
     2.5468279240787,  2.0843242435815,   1.221896224602,  .22174687335761,
    -1.5383725254448,  6.6320283736022, np.nan,  1.9599639845401,
                   0, -71.328597501645,   19.64700882358, -3.6305067169329,
     .00028286534285,  -109.8360271998, -32.821167803489, np.nan,
     1.9599639845401,                0,  3202.7457127058,  54.010820719402,
     59.298223393877,                0,  3096.8864493203,  3308.6049760912,
    np.nan,  1.9599639845401,                0,  25.111330718591,
     40.375410330977,  .62194614278197,   .5339772824573, -54.023019391151,
     104.24568082833, np.nan,  1.9599639845401,                0,
     133.66170465247,   40.86442742188,  3.2708571509534,  .00107222056235,
     53.568898656735,   213.7545106482, np.nan,  1.9599639845401,
                   0, -7.3708810422983,  4.2181700723084, -1.7474120094604,
     .08056589577917, -15.638342464688,  .89658038009089, np.nan,
     1.9599639845401,                0,  41.439913440842,   39.70711783121,
     1.0436394204434,  .29665224735146, -36.384607438218,   119.2644343199,
    np.nan,  1.9599639845401,                0,   3227.169253308,
     104.40590209261,  30.909835446328,  8.81095776e-210,   3022.537445433,
     3431.8010611829, np.nan,  1.9599639845401,                0
    ]).reshape(12,9)

table_t_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_t_rownames = 'r1vs0.mbsmoke 0.mbsmoke prenatal1 mmarried mage fbaby _cons prenatal1 mmarried mage fbaby _cons'.split()

results_ra = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                table_t=table_t,
                table_t_colnames=table_t_colnames,
                table_t_rownames=table_t_rownames,
                )

table = np.array([
    -230.68863779526,  25.815243801554, -8.9361401956379,  4.030006076e-19,
    -281.28558589843, -180.09168969209, np.nan,  1.9599639845401,
                   0,  3403.4627086846,  9.5713688979534,  355.58787305882,
                   0,  3384.7031703618,  3422.2222470073, np.nan,
     1.9599639845401,                0, -.64848213804682,  .05541728623223,
     -11.70180249046,  1.247746040e-31, -.75709802318294, -.53986625291071,
    np.nan,  1.9599639845401,                0,  .17443269674281,
     .03637184725595,  4.7958162673267,  1.620137296e-06,  .10314518606995,
     .24572020741566, np.nan,  1.9599639845401,                0,
    -.00325591262232,  .00066777909855, -4.8757330521042,  1.084051264e-06,
    -.00456473560511, -.00194708963953, np.nan,  1.9599639845401,
                   0,  -.2175961616731,  .04956043775733, -4.3905213819646,
     .00001130791946, -.31473283473551, -.12045948861068, np.nan,
     1.9599639845401,                0, -.08636308703575,  .01001479395511,
    -8.6235510608446,  6.490921364e-18, -.10599172250036, -.06673445157114,
    np.nan,  1.9599639845401,                0, -1.5582552629264,
     .46396908940251, -3.3585324939062,  .00078357508242, -2.4676179680951,
    -.64889255775762, np.nan,  1.9599639845401,                0
    ]).reshape(8,9)

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = 'r1vs0.mbsmoke 0.mbsmoke mmarried mage c.mage#c.mage fbaby medu _cons'.split()

table_t = np.array([
    -225.17726054799,  23.664582726945, -9.5153700002322,  1.810673837e-21,
    -271.55899040197,   -178.795530694, np.nan,  1.9599639845401,
                   0,  3362.8369827702,   14.20149077868,   236.7946460817,
                   0,  3335.0025723172,  3390.6713932232, np.nan,
     1.9599639845401,                0, -.64848213804682,  .05541728623191,
    -11.701802490528,  1.247746039e-31, -.75709802318231, -.53986625291133,
    np.nan,  1.9599639845401,                0,  .17443269674281,
     .03637184724264,  4.7958162690822,  1.620137282e-06,  .10314518609605,
     .24572020738957, np.nan,  1.9599639845401,                0,
    -.00325591262232,  .00066777909835, -4.8757330536115,  1.084051255e-06,
     -.0045647356047, -.00194708963993, np.nan,  1.9599639845401,
                   0,  -.2175961616731,  .04956043776041, -4.3905213816925,
     .00001130791948, -.31473283474153, -.12045948860466, np.nan,
     1.9599639845401,                0, -.08636308703575,  .01001479395498,
      -8.62355106096,  6.490921358e-18,  -.1059917225001, -.06673445157141,
    np.nan,  1.9599639845401,                0, -1.5582552629264,
     .46396908922586, -3.3585324951848,  .00078357507879, -2.4676179677489,
    -.64889255810383, np.nan,  1.9599639845401,                0
    ]).reshape(8,9)

table_t_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_t_rownames = 'r1vs0.mbsmoke 0.mbsmoke mmarried mage c.mage#c.mage fbaby medu _cons'.split()


results_ipw = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                table_t=table_t,
                table_t_colnames=table_t_colnames,
                table_t_rownames=table_t_rownames,
                )

table = np.array([
    -230.98920111258,  26.210564625435, -8.8128281253592,  1.220275657e-18,
    -282.36096379289, -179.61743843227, np.nan,  1.9599639845401,
                   0,  3403.3552531738,  9.5684720516903,  355.68429680187,
                   0,  3384.6013925654,  3422.1091137822, np.nan,
     1.9599639845401,                0,  64.408589362646,  27.526987262221,
     2.3398343141983,  .01929229642928,  10.456685725799,  118.36049299949,
    np.nan,  1.9599639845401,                0,  160.95125844367,
     26.616203359739,  6.0471155960257,  1.474619593e-09,  108.78445845339,
     213.11805843395, np.nan,  1.9599639845401,                0,
     2.5468279240787,  2.0843242435815,  1.2218962246021,   .2217468733576,
    -1.5383725254446,  6.6320283736021, np.nan,  1.9599639845401,
                   0, -71.328597501645,   19.64700882358, -3.6305067169329,
     .00028286534285,  -109.8360271998, -32.821167803489, np.nan,
     1.9599639845401,                0,  3202.7457127058,  54.010820719402,
     59.298223393878,                0,  3096.8864493203,  3308.6049760912,
    np.nan,  1.9599639845401,                0,  25.111330718591,
     40.375410330977,  .62194614278197,   .5339772824573, -54.023019391151,
     104.24568082833, np.nan,  1.9599639845401,                0,
     133.66170465247,   40.86442742188,  3.2708571509534,  .00107222056235,
     53.568898656735,  213.75451064821, np.nan,  1.9599639845401,
                   0, -7.3708810422983,  4.2181700723084, -1.7474120094604,
     .08056589577917, -15.638342464688,  .89658038009083, np.nan,
     1.9599639845401,                0,  41.439913440842,   39.70711783121,
     1.0436394204434,  .29665224735146, -36.384607438218,   119.2644343199,
    np.nan,  1.9599639845401,                0,   3227.169253308,
     104.40590209261,  30.909835446329,  8.81095776e-210,   3022.537445433,
     3431.8010611829, np.nan,  1.9599639845401,                0,
    -.64848213804682,  .05541728623246, -11.701802490411,  1.247746040e-31,
    -.75709802318339, -.53986625291025, np.nan,  1.9599639845401,
                   0,  .17443269674281,  .03637184725678,  4.7958162672167,
     1.620137297e-06,  .10314518606832,   .2457202074173, np.nan,
     1.9599639845401,                0, -.00325591262232,  .00066777909857,
    -4.8757330520018,  1.084051264e-06, -.00456473560514,  -.0019470896395,
    np.nan,  1.9599639845401,                0,  -.2175961616731,
     .04956043775735, -4.3905213819631,  .00001130791946, -.31473283473554,
    -.12045948861065, np.nan,  1.9599639845401,                0,
    -.08636308703575,  .01001479395507, -8.6235510608833,  6.490921362e-18,
    -.10599172250028, -.06673445157123, np.nan,  1.9599639845401,
                   0, -1.5582552629264,  .46396908941501, -3.3585324938157,
     .00078357508267, -2.4676179681196, -.64889255773313, np.nan,
     1.9599639845401,                0]).reshape(18,9)

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = 'r1vs0.mbsmoke 0.mbsmoke prenatal1 mmarried mage fbaby _cons prenatal1 mmarried mage fbaby _cons mmarried mage c.mage#c.mage fbaby medu _cons'.split()


results_aipw = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
    -227.19561818675,  27.347935624441, -8.3075966430061,  9.765983730e-17,
    -280.79658706217, -173.59464931133, np.nan,  1.9599639845401,
                   0,  3403.2506509758,   9.596621896179,   354.6300654328,
                   0,   3384.441617686,  3422.0596842655, np.nan,
     1.9599639845401,                0,  82.181272088395,  58.620922322422,
     1.4019102537553,   .1609420481386, -32.713624404072,  197.07616858086,
    np.nan,  1.9599639845401,                0,  118.76459672483,
      35.33781015982,  3.3608363446321,  .00077706853832,  49.503761519072,
     188.02543193059, np.nan,  1.9599639845401,                0,
     5.5147235203491,  4.0179588195471,  1.3725186762792,  .16990202893509,
    -2.3603310573283,  13.389778098026, np.nan,  1.9599639845401,
                   0, -111.48446442703,  55.345523286143, -2.0143357187291,
     .04397429826544, -219.95969677339, -3.0092320806668, np.nan,
     1.9599639845401,                0,  3154.0113942013,  88.314554758981,
     35.713381591617,  2.45059114e-279,   2980.918047563,  3327.1047408395,
    np.nan,  1.9599639845401,                0,  95.066642475617,
     77.968463953496,  1.2192960801731,  .22273183586194, -57.748738803144,
     247.88202375438, np.nan,  1.9599639845401,                0,
      96.67302454497,  48.157019046638,  2.0074544990284,  .04470129085208,
     2.2870016107499,  191.05904747919, np.nan,  1.9599639845401,
                   0, -3.4220909568961,  11.838775715374, -.28905784172025,
     .77253711451199, -26.625664980076,  19.781483066284, np.nan,
     1.9599639845401,                0, -9.8880616310089,  130.98498723825,
    -.07549003774779,  .93982482364584, -266.61391913341,  246.83779587139,
    np.nan,  1.9599639845401,                0,  3142.5011834805,
     343.51518833541,  9.1480705662777,  5.795943393e-20,  2469.2237862006,
     3815.7785807604, np.nan,  1.9599639845401,                0,
    -.64848213804682,  .05541728623247,  -11.70180249041,  1.247746040e-31,
     -.7570980231834, -.53986625291024, np.nan,  1.9599639845401,
                   0,  .17443269674281,  .03637184725647,  4.7958162672578,
     1.620137296e-06,  .10314518606893,  .24572020741669, np.nan,
     1.9599639845401,                0, -.00325591262232,  .00066777909856,
    -4.8757330520397,  1.084051264e-06, -.00456473560513, -.00194708963951,
    np.nan,  1.9599639845401,                0,  -.2175961616731,
     .04956043775737, -4.3905213819612,  .00001130791946, -.31473283473559,
    -.12045948861061, np.nan,  1.9599639845401,                0,
    -.08636308703575,  .01001479395509, -8.6235510608616,  6.490921363e-18,
    -.10599172250032, -.06673445157118, np.nan,  1.9599639845401,
                   0, -1.5582552629264,  .46396908941001, -3.3585324938518,
     .00078357508257, -2.4676179681098, -.64889255774291, np.nan,
     1.9599639845401,                0]).reshape(18,9)

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = 'r1vs0.mbsmoke 0.mbsmoke prenatal1 mmarried mage fbaby _cons prenatal1 mmarried mage fbaby _cons mmarried mage c.mage#c.mage fbaby medu _cons'.split()


results_aipw_wls = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                )

table = np.array([
    -229.96707793513,  26.626675723594, -8.6367175655861,  5.785117089e-18,
     -282.1544033814, -177.77975248886, np.nan,  1.9599639845401,
                   0,  3403.3356393074,    9.57125961016,  355.57865713879,
                   0,  3384.5763151848,    3422.09496343, np.nan,
     1.9599639845401,                0,  67.985490604489,  28.784283742461,
     2.3618962074155,  .01818173182612,  11.569331148484,  124.40165006049,
    np.nan,  1.9599639845401,                0,  155.58930106521,
     26.469032273847,  5.8781635631969,  4.148429157e-09,  103.71095110284,
     207.46765102758, np.nan,  1.9599639845401,                0,
      2.893051083978,  2.1347878922477,  1.3551936913657,  .17535585413018,
    -1.2910562994597,  7.0771584674157, np.nan,  1.9599639845401,
                   0, -71.921496436385,  20.393170550443, -3.5267442234392,
     .00042070296711, -111.89137624584, -31.951616626934, np.nan,
     1.9599639845401,                0,  3194.8075652619,  55.049108896772,
     58.035590934864,                0,  3086.9132944432,  3302.7018360806,
    np.nan,  1.9599639845401,                0,  34.769226459004,
     43.185336354577,  .80511649078124,  .42075246042071, -49.872477456214,
     119.41093037422, np.nan,  1.9599639845401,                0,
     124.09407253083,  40.297750215194,    3.07942929489,  .00207397592636,
     45.111933451056,   203.0762116106, np.nan,  1.9599639845401,
                   0, -5.0688328301824,   5.954425242531, -.85127155413371,
     .39461852267686, -16.739291854179,  6.6016261938146, np.nan,
     1.9599639845401,                0,  39.896915302387,  56.820722936052,
     .70215430640135,  .48258293810814, -71.469655227805,  151.26348583258,
    np.nan,  1.9599639845401,                0,  3175.5506552136,
     153.83122218596,  20.643082789623,  1.126438084e-94,  2874.0470000313,
     3477.0543103958, np.nan,  1.9599639845401,                0,
    -.64848213804682,   .0554172862325, -11.701802490402,  1.247746040e-31,
    -.75709802318348, -.53986625291016, np.nan,  1.9599639845401,
                   0,  .17443269674281,  .03637184725653,  4.7958162672504,
     1.620137296e-06,  .10314518606882,   .2457202074168, np.nan,
     1.9599639845401,                0, -.00325591262232,  .00066777909856,
    -4.8757330520424,  1.084051264e-06, -.00456473560512, -.00194708963951,
    np.nan,  1.9599639845401,                0,  -.2175961616731,
     .04956043775739, -4.3905213819596,  .00001130791946, -.31473283473562,
    -.12045948861057, np.nan,  1.9599639845401,                0,
    -.08636308703575,  .01001479395511, -8.6235510608441,  6.490921364e-18,
    -.10599172250036, -.06673445157114, np.nan,  1.9599639845401,
                   0, -1.5582552629264,  .46396908941052, -3.3585324938482,
     .00078357508258, -2.4676179681108, -.64889255774192, np.nan,
     1.9599639845401,                0]).reshape(18,9)

table_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_rownames = 'r1vs0.mbsmoke 0.mbsmoke prenatal1 mmarried mage fbaby _cons prenatal1 mmarried mage fbaby _cons mmarried mage c.mage#c.mage fbaby medu _cons'.split()

table_t = np.array([
    -223.54526198669,   23.79401566757, -9.3950203744454,  5.720675031e-21,
    -270.18067574271, -176.90984823067, np.nan,  1.9599639845401,
                   0,  3361.2049842089,  14.465011372588,  232.36794618624,
                   0,  3332.8540828827,  3389.5558855351, np.nan,
     1.9599639845401,                0,  78.699052591474,  39.923632779925,
     1.9712397673151,   .0486964572387,  .45017021081746,  156.94793497213,
    np.nan,  1.9599639845401,                0,  138.08012522442,
     29.391214670734,  4.6980067605684,  2.627127381e-06,    80.4744030079,
     195.68584744095, np.nan,  1.9599639845401,                0,
     4.4536627925561,  3.0377853203718,  1.4660887201901,  .14262411828721,
    -1.5002870281371,  10.407612613249, np.nan,  1.9599639845401,
                   0, -74.283881352771,  32.351302431096, -2.2961635473869,
      .0216665319432, -137.69126897068, -10.876493734861, np.nan,
     1.9599639845401,                0,  3157.0675716992,  72.926678364308,
     43.290982703585,                0,   3014.133908593,  3300.0012348054,
    np.nan,  1.9599639845401,                0,  25.111330718591,
     40.375410330977,  .62194614278197,   .5339772824573, -54.023019391152,
     104.24568082833, np.nan,  1.9599639845401,                0,
     133.66170465247,   40.86442742188,  3.2708571509534,  .00107222056235,
     53.568898656735,  213.75451064821, np.nan,  1.9599639845401,
                   0, -7.3708810422984,  4.2181700723084, -1.7474120094604,
     .08056589577917, -15.638342464688,  .89658038009091, np.nan,
     1.9599639845401,                0,  41.439913440842,   39.70711783121,
     1.0436394204434,  .29665224735146, -36.384607438218,   119.2644343199,
    np.nan,  1.9599639845401,                0,   3227.169253308,
     104.40590209261,  30.909835446329,  8.81095776e-210,   3022.537445433,
     3431.8010611829, np.nan,  1.9599639845401,                0,
    -.64848213804682,  .05541728623085, -11.701802490751,  1.247746035e-31,
    -.75709802318024,  -.5398662529134, np.nan,  1.9599639845401,
                   0,  .17443269674281,  .03637184726815,  4.7958162657175,
     1.620137309e-06,  .10314518604603,  .24572020743958, np.nan,
     1.9599639845401,                0, -.00325591262232,  .00066777909873,
     -4.875733050792,  1.084051271e-06, -.00456473560546, -.00194708963917,
    np.nan,  1.9599639845401,                0,  -.2175961616731,
     .04956043776294, -4.3905213814679,  .00001130791949,  -.3147328347465,
     -.1204594885997, np.nan,  1.9599639845401,                0,
    -.08636308703575,  .01001479395705, -8.6235510591753,  6.490921459e-18,
    -.10599172250416, -.06673445156734, np.nan,  1.9599639845401,
                   0, -1.5582552629264,  .46396908950784, -3.3585324931437,
     .00078357508458, -2.4676179683016, -.64889255755117, np.nan,
     1.9599639845401,                0]).reshape(18,9)

table_t_colnames = 'b se z pvalue ll ul df crit eform'.split()

table_t_rownames = 'r1vs0.mbsmoke 0.mbsmoke prenatal1 mmarried mage fbaby _cons prenatal1 mmarried mage fbaby _cons mmarried mage c.mage#c.mage fbaby medu _cons'.split()


results_ipwra = Bunch(
                table=table,
                table_colnames=table_colnames,
                table_rownames=table_rownames,
                table_t=table_t,
                table_t_colnames=table_t_colnames,
                table_t_rownames=table_t_rownames,
                )
