2015-10-18 18:04:20,163 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 18:04:20,272 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 18:04:20,272 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-18 18:04:20,304 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-18 18:04:20,304 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445144423722_0022, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@1623b78d)
2015-10-18 18:04:20,491 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-18 18:04:21,025 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445144423722_0022
2015-10-18 18:04:21,525 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-18 18:04:22,322 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-18 18:04:22,353 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@db57326
2015-10-18 18:04:22,385 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@7862f56
2015-10-18 18:04:22,404 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=130652568, maxSingleShuffleLimit=32663142, mergeThreshold=86230696, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-18 18:04:22,404 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0022_r_000000_0 Thread started: EventFetcher for fetching Map Completion Events
2015-10-18 18:04:22,420 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:04:22,420 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:04:22,420 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0022_r_000000_0: Got 4 new map-outputs
2015-10-18 18:04:22,561 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0022&reduce=0&map=attempt_1445144423722_0022_m_000003_0 sent hash and received reply
2015-10-18 18:04:22,561 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0022_m_000003_0: Shuffling to disk since 60515787 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 18:04:22,561 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0022_m_000003_0 decomp: 60515787 len: 60515791 to DISK
2015-10-18 18:04:23,233 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515791 bytes from map-output for attempt_1445144423722_0022_m_000003_0
2015-10-18 18:04:23,248 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 823ms
2015-10-18 18:04:23,248 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 3 to fetcher#3
2015-10-18 18:04:23,248 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 3 of 3 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:04:23,248 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0022&reduce=0&map=attempt_1445144423722_0022_m_000002_0,attempt_1445144423722_0022_m_000000_0,attempt_1445144423722_0022_m_000001_0 sent hash and received reply
2015-10-18 18:04:23,264 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0022_m_000002_0: Shuffling to disk since 60514392 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 18:04:23,264 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0022_m_000002_0 decomp: 60514392 len: 60514396 to DISK
2015-10-18 18:04:23,842 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60514396 bytes from map-output for attempt_1445144423722_0022_m_000002_0
2015-10-18 18:04:23,904 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0022_m_000000_0: Shuffling to disk since 60515385 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 18:04:23,904 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0022_m_000000_0 decomp: 60515385 len: 60515389 to DISK
2015-10-18 18:04:24,467 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515389 bytes from map-output for attempt_1445144423722_0022_m_000000_0
2015-10-18 18:04:24,483 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0022_m_000001_0: Shuffling to disk since 60515836 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 18:04:24,483 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0022_m_000001_0 decomp: 60515836 len: 60515840 to DISK
2015-10-18 18:04:25,061 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515840 bytes from map-output for attempt_1445144423722_0022_m_000001_0
2015-10-18 18:04:25,076 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 1840ms
2015-10-18 18:05:11,438 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0022_r_000000_0: Got 1 new map-outputs
2015-10-18 18:05:11,438 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning 04DN8IQ.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:05:11,438 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to 04DN8IQ.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:05:11,500 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0022&reduce=0&map=attempt_1445144423722_0022_m_000009_0 sent hash and received reply
2015-10-18 18:05:11,531 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0022_m_000009_0: Shuffling to disk since 56695786 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 18:05:11,547 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0022_m_000009_0 decomp: 56695786 len: 56695790 to DISK
2015-10-18 18:05:28,673 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 56695790 bytes from map-output for attempt_1445144423722_0022_m_000009_0
2015-10-18 18:05:28,688 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: 04DN8IQ.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 17251ms
2015-10-18 18:06:14,440 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0022_r_000000_0: Got 1 new map-outputs
2015-10-18 18:06:14,440 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:06:14,440 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:06:14,440 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0022&reduce=0&map=attempt_1445144423722_0022_m_000007_1 sent hash and received reply
2015-10-18 18:06:14,440 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0022_m_000007_1: Shuffling to disk since 60517368 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 18:06:14,456 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0022_m_000007_1 decomp: 60517368 len: 60517372 to DISK
2015-10-18 18:06:14,753 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60517372 bytes from map-output for attempt_1445144423722_0022_m_000007_1
2015-10-18 18:06:14,768 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 333ms
2015-10-18 18:06:29,441 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0022_r_000000_0: Got 1 new map-outputs
2015-10-18 18:06:29,441 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:06:29,441 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:06:29,441 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0022&reduce=0&map=attempt_1445144423722_0022_m_000005_1 sent hash and received reply
2015-10-18 18:06:29,441 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0022_m_000005_1: Shuffling to disk since 60514806 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 18:06:29,456 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0022_m_000005_1 decomp: 60514806 len: 60514810 to DISK
2015-10-18 18:06:30,144 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60514810 bytes from map-output for attempt_1445144423722_0022_m_000005_1
2015-10-18 18:06:30,160 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 718ms
2015-10-18 18:07:10,442 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0022_r_000000_0: Got 1 new map-outputs
2015-10-18 18:07:10,442 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:07:10,442 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:07:10,536 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0022&reduce=0&map=attempt_1445144423722_0022_m_000008_1 sent hash and received reply
2015-10-18 18:07:10,536 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0022_m_000008_1: Shuffling to disk since 60516677 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 18:07:10,552 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0022_m_000008_1 decomp: 60516677 len: 60516681 to DISK
2015-10-18 18:07:11,427 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60516681 bytes from map-output for attempt_1445144423722_0022_m_000008_1
2015-10-18 18:07:11,443 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 1003ms
2015-10-18 18:09:29,448 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0022_r_000000_0: Got 1 new map-outputs
2015-10-18 18:09:29,448 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning 04DN8IQ.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:09:29,448 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to 04DN8IQ.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:09:29,448 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0022&reduce=0&map=attempt_1445144423722_0022_m_000004_1 sent hash and received reply
2015-10-18 18:09:29,448 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0022_m_000004_1: Shuffling to disk since 60513765 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 18:09:29,464 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0022_m_000004_1 decomp: 60513765 len: 60513769 to DISK
2015-10-18 18:09:31,448 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0022_r_000000_0: Got 1 new map-outputs
2015-10-18 18:09:43,121 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60513769 bytes from map-output for attempt_1445144423722_0022_m_000004_1
2015-10-18 18:09:43,167 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: 04DN8IQ.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 13721ms
2015-10-18 18:09:43,167 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning 04DN8IQ.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-18 18:09:43,167 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to 04DN8IQ.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-18 18:09:43,183 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0022&reduce=0&map=attempt_1445144423722_0022_m_000006_1 sent hash and received reply
2015-10-18 18:09:43,183 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0022_m_000006_1: Shuffling to disk since 60515100 is greater than maxSingleShuffleLimit (32663142)
2015-10-18 18:09:43,183 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445144423722_0022_m_000006_1 decomp: 60515100 len: 60515104 to DISK
2015-10-18 18:09:52,183 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515104 bytes from map-output for attempt_1445144423722_0022_m_000006_1
2015-10-18 18:09:52,199 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: 04DN8IQ.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 9032ms
2015-10-18 18:09:52,199 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: EventFetcher is interrupted.. Returning
2015-10-18 18:09:52,199 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: finalMerge called with 0 in-memory map-outputs and 10 on-disk map-outputs
2015-10-18 18:09:52,215 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 10 files, 601334942 bytes from disk
2015-10-18 18:09:52,215 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 0 segments, 0 bytes from memory into reduce
2015-10-18 18:09:52,215 INFO [main] org.apache.hadoop.mapred.Merger: Merging 10 sorted segments
2015-10-18 18:09:52,230 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 10 segments left of total size: 601334842 bytes
2015-10-18 18:09:52,340 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: mapred.skip.on is deprecated. Instead, use mapreduce.job.skiprecords
2015-10-18 18:10:13,841 INFO [Thread-125] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-18 18:10:13,856 INFO [Thread-125] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073743517_2738
2015-10-18 18:10:13,872 INFO [Thread-125] org.apache.hadoop.hdfs.DFSClient: Excluding datanode *************:50010
2015-10-18 18:10:53,670 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445144423722_0022_r_000000_0 is done. And is in the process of committing
2015-10-18 18:10:53,702 INFO [main] org.apache.hadoop.mapred.Task: Task attempt_1445144423722_0022_r_000000_0 is allowed to commit now
2015-10-18 18:10:53,702 INFO [main] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Saved output of task 'attempt_1445144423722_0022_r_000000_0' to hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/task_1445144423722_0022_r_000000
2015-10-18 18:10:53,717 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445144423722_0022_r_000000_0' done.
