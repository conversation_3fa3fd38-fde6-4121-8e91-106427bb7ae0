2015-10-17 15:54:48,025 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 15:54:48,181 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 15:54:48,181 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-17 15:54:48,212 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 15:54:48,212 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445062781478_0013, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@54e0a229)
2015-10-17 15:54:48,369 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 15:54:48,775 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445062781478_0013
2015-10-17 15:54:49,853 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 15:54:50,665 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 15:54:50,697 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@20c50279
2015-10-17 15:54:50,744 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@4da4c44b
2015-10-17 15:54:50,775 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=130652568, maxSingleShuffleLimit=32663142, mergeThreshold=86230696, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-17 15:54:50,775 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0013_r_000000_1000 Thread started: EventFetcher for fetching Map Completion Events
2015-10-17 15:54:50,790 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-17 15:54:50,790 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0013_r_000000_1000: Got 1 new map-outputs
2015-10-17 15:54:50,790 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 15:54:50,837 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445062781478_0013&reduce=0&map=attempt_1445062781478_0013_m_000009_1000 sent hash and received reply
2015-10-17 15:54:50,837 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0013_m_000009_1000: Shuffling to disk since 56695786 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 15:54:50,853 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0013_m_000009_1000 decomp: 56695786 len: 56695790 to DISK
2015-10-17 15:55:00,525 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 56695790 bytes from map-output for attempt_1445062781478_0013_m_000009_1000
2015-10-17 15:55:00,541 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 9748ms
2015-10-17 15:55:54,917 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0013_r_000000_1000: Got 1 new map-outputs
2015-10-17 15:55:54,917 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-17 15:55:54,917 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 15:55:54,996 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445062781478_0013&reduce=0&map=attempt_1445062781478_0013_m_000004_1000 sent hash and received reply
2015-10-17 15:55:54,996 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0013_m_000004_1000: Shuffling to disk since 60513765 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 15:55:54,996 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0013_m_000004_1000 decomp: 60513765 len: 60513769 to DISK
2015-10-17 15:55:57,964 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0013_r_000000_1000: Got 2 new map-outputs
2015-10-17 15:56:03,965 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0013_r_000000_1000: Got 1 new map-outputs
2015-10-17 15:56:04,965 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0013_r_000000_1000: Got 1 new map-outputs
2015-10-17 15:56:06,965 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0013_r_000000_1000: Got 2 new map-outputs
2015-10-17 15:56:07,230 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60513769 bytes from map-output for attempt_1445062781478_0013_m_000004_1000
2015-10-17 15:56:07,262 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 12355ms
2015-10-17 15:56:07,262 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 6 to fetcher#1
2015-10-17 15:56:07,262 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 6 of 6 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 15:56:07,293 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445062781478_0013&reduce=0&map=attempt_1445062781478_0013_m_000006_1000,attempt_1445062781478_0013_m_000007_1000,attempt_1445062781478_0013_m_000001_1000,attempt_1445062781478_0013_m_000002_1000,attempt_1445062781478_0013_m_000000_1000,attempt_1445062781478_0013_m_000003_1000 sent hash and received reply
2015-10-17 15:56:07,293 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0013_m_000006_1000: Shuffling to disk since 60515100 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 15:56:07,433 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0013_m_000006_1000 decomp: 60515100 len: 60515104 to DISK
2015-10-17 15:56:14,965 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515104 bytes from map-output for attempt_1445062781478_0013_m_000006_1000
2015-10-17 15:56:14,965 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0013_m_000007_1000: Shuffling to disk since 60517368 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 15:56:14,981 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0013_m_000007_1000 decomp: 60517368 len: 60517372 to DISK
2015-10-17 15:56:17,199 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0013_r_000000_1000: Got 1 new map-outputs
2015-10-17 15:56:25,465 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60517372 bytes from map-output for attempt_1445062781478_0013_m_000007_1000
2015-10-17 15:56:25,481 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0013_m_000001_1000: Shuffling to disk since 60515836 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 15:56:25,481 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0013_m_000001_1000 decomp: 60515836 len: 60515840 to DISK
2015-10-17 15:56:33,247 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515840 bytes from map-output for attempt_1445062781478_0013_m_000001_1000
2015-10-17 15:56:33,262 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0013_m_000002_1000: Shuffling to disk since 60514392 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 15:56:33,262 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0013_m_000002_1000 decomp: 60514392 len: 60514396 to DISK
2015-10-17 15:56:43,778 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60514396 bytes from map-output for attempt_1445062781478_0013_m_000002_1000
2015-10-17 15:56:43,825 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0013_m_000000_1000: Shuffling to disk since 60515385 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 15:56:43,825 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0013_m_000000_1000 decomp: 60515385 len: 60515389 to DISK
2015-10-17 15:56:47,231 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445062781478_0013_r_000000_1000: Got 1 new map-outputs
2015-10-17 15:56:47,231 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#3
2015-10-17 15:56:47,294 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#3
2015-10-17 15:56:47,388 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445062781478_0013&reduce=0&map=attempt_1445062781478_0013_m_000008_1000 sent hash and received reply
2015-10-17 15:56:47,388 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0013_m_000008_1000: Shuffling to disk since 60516677 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 15:56:47,388 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#3 about to shuffle output of map attempt_1445062781478_0013_m_000008_1000 decomp: 60516677 len: 60516681 to DISK
2015-10-17 15:56:57,888 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515389 bytes from map-output for attempt_1445062781478_0013_m_000000_1000
2015-10-17 15:56:57,904 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0013_m_000003_1000: Shuffling to disk since 60515787 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 15:56:57,904 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0013_m_000003_1000 decomp: 60515787 len: 60515791 to DISK
2015-10-17 15:57:02,435 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60516681 bytes from map-output for attempt_1445062781478_0013_m_000008_1000
2015-10-17 15:57:02,451 INFO [fetcher#3] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#3 in 15159ms
2015-10-17 15:57:07,592 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515791 bytes from map-output for attempt_1445062781478_0013_m_000003_1000
2015-10-17 15:57:07,592 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 60331ms
2015-10-17 15:57:07,592 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-17 15:57:07,592 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 15:57:07,607 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445062781478_0013&reduce=0&map=attempt_1445062781478_0013_m_000005_1000 sent hash and received reply
2015-10-17 15:57:07,607 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445062781478_0013_m_000005_1000: Shuffling to disk since 60514806 is greater than maxSingleShuffleLimit (32663142)
2015-10-17 15:57:07,623 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445062781478_0013_m_000005_1000 decomp: 60514806 len: 60514810 to DISK
2015-10-17 15:57:15,857 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60514810 bytes from map-output for attempt_1445062781478_0013_m_000005_1000
2015-10-17 15:57:15,857 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: EventFetcher is interrupted.. Returning
2015-10-17 15:57:15,857 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 8266ms
2015-10-17 15:57:15,873 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: finalMerge called with 0 in-memory map-outputs and 10 on-disk map-outputs
2015-10-17 15:57:15,889 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 10 files, 601334942 bytes from disk
2015-10-17 15:57:15,889 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 0 segments, 0 bytes from memory into reduce
2015-10-17 15:57:15,889 INFO [main] org.apache.hadoop.mapred.Merger: Merging 10 sorted segments
2015-10-17 15:57:15,920 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 10 segments left of total size: 601334842 bytes
2015-10-17 15:57:16,014 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: mapred.skip.on is deprecated. Instead, use mapreduce.job.skiprecords
2015-10-17 15:58:02,671 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445062781478_0013_r_000000_1000 is done. And is in the process of committing
2015-10-17 15:58:02,718 INFO [main] org.apache.hadoop.mapred.Task: Task attempt_1445062781478_0013_r_000000_1000 is allowed to commit now
2015-10-17 15:58:02,734 INFO [main] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Saved output of task 'attempt_1445062781478_0013_r_000000_1000' to hdfs://msra-sa-41:9000/pageout/out3/_temporary/2/task_1445062781478_0013_r_000000
2015-10-17 15:58:02,765 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445062781478_0013_r_000000_1000' done.
