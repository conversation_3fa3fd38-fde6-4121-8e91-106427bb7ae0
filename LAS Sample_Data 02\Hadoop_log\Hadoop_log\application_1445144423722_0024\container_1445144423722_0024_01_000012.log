2015-10-18 18:17:08,400 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 18:17:08,469 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 18:17:08,469 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-18 18:17:08,483 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-18 18:17:08,484 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445144423722_0024, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@1ebe6739)
2015-10-18 18:17:08,584 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-18 18:17:08,861 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445144423722_0024
2015-10-18 18:17:09,326 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-18 18:17:09,742 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-18 18:17:09,772 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@f32bade
2015-10-18 18:17:09,804 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@4537c278
2015-10-18 18:17:09,822 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=140928608, maxSingleShuffleLimit=35232152, mergeThreshold=93012888, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-18 18:17:09,824 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0024_r_000000_0 Thread started: EventFetcher for fetching Map Completion Events
2015-10-18 18:17:09,834 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 18:17:09,834 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 18:17:09,834 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0024_r_000000_0: Got 2 new map-outputs
2015-10-18 18:17:09,862 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0024&reduce=0&map=attempt_1445144423722_0024_m_000000_0 sent hash and received reply
2015-10-18 18:17:09,865 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0024_m_000000_0: Shuffling to disk since 60515385 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:17:09,870 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445144423722_0024_m_000000_0 decomp: 60515385 len: 60515389 to DISK
2015-10-18 18:17:10,376 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515389 bytes from map-output for attempt_1445144423722_0024_m_000000_0
2015-10-18 18:17:10,389 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 555ms
2015-10-18 18:17:10,390 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 18:17:10,390 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 18:17:10,398 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0024&reduce=0&map=attempt_1445144423722_0024_m_000001_0 sent hash and received reply
2015-10-18 18:17:10,399 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0024_m_000001_0: Shuffling to disk since 60515836 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:17:10,402 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445144423722_0024_m_000001_0 decomp: 60515836 len: 60515840 to DISK
2015-10-18 18:17:10,910 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515840 bytes from map-output for attempt_1445144423722_0024_m_000001_0
2015-10-18 18:17:10,920 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 531ms
2015-10-18 18:17:13,856 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0024_r_000000_0: Got 1 new map-outputs
2015-10-18 18:17:13,856 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 18:17:13,857 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 18:17:13,864 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0024&reduce=0&map=attempt_1445144423722_0024_m_000002_0 sent hash and received reply
2015-10-18 18:17:13,866 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0024_m_000002_0: Shuffling to disk since 60514392 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:17:13,869 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445144423722_0024_m_000002_0 decomp: 60514392 len: 60514396 to DISK
2015-10-18 18:17:14,377 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60514396 bytes from map-output for attempt_1445144423722_0024_m_000002_0
2015-10-18 18:17:14,401 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 544ms
2015-10-18 18:18:01,568 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0024_r_000000_0: Got 1 new map-outputs
2015-10-18 18:18:01,568 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 18:18:01,568 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 18:18:01,575 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0024&reduce=0&map=attempt_1445144423722_0024_m_000009_0 sent hash and received reply
2015-10-18 18:18:01,576 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0024_m_000009_0: Shuffling to disk since 56695786 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:18:01,579 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445144423722_0024_m_000009_0 decomp: 56695786 len: 56695790 to DISK
2015-10-18 18:18:02,055 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 56695790 bytes from map-output for attempt_1445144423722_0024_m_000009_0
2015-10-18 18:18:02,061 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 493ms
2015-10-18 18:19:15,377 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0024_r_000000_0: Got 1 new map-outputs
2015-10-18 18:19:15,377 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 18:19:15,377 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 18:19:15,385 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0024&reduce=0&map=attempt_1445144423722_0024_m_000008_0 sent hash and received reply
2015-10-18 18:19:15,387 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0024_m_000008_0: Shuffling to disk since 60516677 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:19:15,390 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445144423722_0024_m_000008_0 decomp: 60516677 len: 60516681 to DISK
2015-10-18 18:19:15,959 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60516681 bytes from map-output for attempt_1445144423722_0024_m_000008_0
2015-10-18 18:19:15,971 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 594ms
2015-10-18 18:19:16,487 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 18:19:16,487 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 18:19:16,487 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0024_r_000000_0: Got 2 new map-outputs
2015-10-18 18:19:16,495 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0024&reduce=0&map=attempt_1445144423722_0024_m_000004_0 sent hash and received reply
2015-10-18 18:19:16,496 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0024_m_000004_0: Shuffling to disk since 60513765 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:19:16,500 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445144423722_0024_m_000004_0 decomp: 60513765 len: 60513769 to DISK
2015-10-18 18:19:17,047 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60513769 bytes from map-output for attempt_1445144423722_0024_m_000004_0
2015-10-18 18:19:17,124 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 637ms
2015-10-18 18:19:17,125 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 18:19:17,125 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 18:19:17,133 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0024&reduce=0&map=attempt_1445144423722_0024_m_000007_0 sent hash and received reply
2015-10-18 18:19:17,134 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0024_m_000007_0: Shuffling to disk since 60517368 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:19:17,137 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445144423722_0024_m_000007_0 decomp: 60517368 len: 60517372 to DISK
2015-10-18 18:19:17,670 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60517372 bytes from map-output for attempt_1445144423722_0024_m_000007_0
2015-10-18 18:19:17,681 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 557ms
2015-10-18 18:19:21,683 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0024_r_000000_0: Got 1 new map-outputs
2015-10-18 18:19:21,684 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 18:19:21,684 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 18:19:21,691 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0024&reduce=0&map=attempt_1445144423722_0024_m_000006_0 sent hash and received reply
2015-10-18 18:19:21,692 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0024_m_000006_0: Shuffling to disk since 60515100 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:19:21,695 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445144423722_0024_m_000006_0 decomp: 60515100 len: 60515104 to DISK
2015-10-18 18:19:22,220 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515104 bytes from map-output for attempt_1445144423722_0024_m_000006_0
2015-10-18 18:19:22,226 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 542ms
2015-10-18 18:19:22,721 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0024_r_000000_0: Got 1 new map-outputs
2015-10-18 18:19:22,721 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 18:19:22,721 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 18:19:22,731 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0024&reduce=0&map=attempt_1445144423722_0024_m_000005_0 sent hash and received reply
2015-10-18 18:19:22,733 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0024_m_000005_0: Shuffling to disk since 60514806 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:19:22,736 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445144423722_0024_m_000005_0 decomp: 60514806 len: 60514810 to DISK
2015-10-18 18:19:23,259 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60514810 bytes from map-output for attempt_1445144423722_0024_m_000005_0
2015-10-18 18:19:23,270 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 548ms
2015-10-18 18:19:24,769 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445144423722_0024_r_000000_0: Got 1 new map-outputs
2015-10-18 18:19:24,769 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 18:19:24,770 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 18:19:24,778 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445144423722_0024&reduce=0&map=attempt_1445144423722_0024_m_000003_0 sent hash and received reply
2015-10-18 18:19:24,780 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445144423722_0024_m_000003_0: Shuffling to disk since 60515787 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 18:19:24,783 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445144423722_0024_m_000003_0 decomp: 60515787 len: 60515791 to DISK
2015-10-18 18:19:25,419 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 60515791 bytes from map-output for attempt_1445144423722_0024_m_000003_0
2015-10-18 18:19:25,432 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 662ms
2015-10-18 18:19:25,432 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: EventFetcher is interrupted.. Returning
2015-10-18 18:19:25,500 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: finalMerge called with 0 in-memory map-outputs and 10 on-disk map-outputs
2015-10-18 18:19:25,514 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 10 files, 601334942 bytes from disk
2015-10-18 18:19:25,516 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 0 segments, 0 bytes from memory into reduce
2015-10-18 18:19:25,523 INFO [main] org.apache.hadoop.mapred.Merger: Merging 10 sorted segments
2015-10-18 18:19:25,545 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 10 segments left of total size: 601334842 bytes
2015-10-18 18:19:25,983 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: mapred.skip.on is deprecated. Instead, use mapreduce.job.skiprecords
2015-10-18 18:20:17,990 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445144423722_0024_r_000000_0 is done. And is in the process of committing
2015-10-18 18:20:18,093 INFO [main] org.apache.hadoop.mapred.Task: Task attempt_1445144423722_0024_r_000000_0 is allowed to commit now
2015-10-18 18:20:18,102 INFO [main] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Saved output of task 'attempt_1445144423722_0024_r_000000_0' to hdfs://msra-sa-41:9000/pageout/out5/_temporary/1/task_1445144423722_0024_r_000000
2015-10-18 18:20:18,170 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445144423722_0024_r_000000_0' done.
2015-10-18 18:20:18,270 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Stopping ReduceTask metrics system...
2015-10-18 18:20:18,270 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system stopped.
2015-10-18 18:20:18,270 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system shutdown complete.
