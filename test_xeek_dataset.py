"""
Test script for the Enhanced Well Log Analyzer with Xeek Dataset
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def prepare_xeek_data():
    """Prepare Xeek dataset for the enhanced well log analyzer"""
    
    print("🛢️ XEEK DATASET PREPARATION FOR WELL LOG ANALYZER")
    print("=" * 60)
    
    # Try to find the Xeek dataset
    possible_paths = [
        "../Dataset/CSV Data/Xeek_train_subset_clean.csv",
        "C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/CSV Data/Xeek_train_subset_clean.csv",
        "Xeek_train_subset_clean.csv"
    ]
    
    xeek_file = None
    for path in possible_paths:
        if os.path.exists(path):
            xeek_file = path
            break
    
    if not xeek_file:
        print("❌ Xeek dataset not found. Please ensure the file exists in one of these locations:")
        for path in possible_paths:
            print(f"   📁 {path}")
        return None
    
    print(f"📁 Found Xeek dataset: {xeek_file}")
    
    # Load the dataset
    try:
        df = pd.read_csv(xeek_file)
        print(f"✅ Dataset loaded successfully!")
        print(f"📊 Shape: {df.shape}")
        print(f"📋 Columns: {list(df.columns)}")
        
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return None
    
    # Analyze dataset characteristics
    print(f"\n🔍 DATASET ANALYSIS:")
    print(f"📏 Depth range: {df['DEPTH_MD'].min():.1f} - {df['DEPTH_MD'].max():.1f} m")
    print(f"🏗️ Number of wells: {df['WELL'].nunique()}")
    print(f"🪨 Number of lithologies: {df['LITH'].nunique()}")
    print(f"📊 Total data points: {len(df):,}")
    
    # Well information
    print(f"\n🏗️ WELLS IN DATASET:")
    well_counts = df['WELL'].value_counts()
    for well, count in well_counts.items():
        depth_range = df[df['WELL'] == well]['DEPTH_MD']
        print(f"   📍 {well}: {count:,} points ({depth_range.min():.1f} - {depth_range.max():.1f} m)")
    
    # Lithology information
    print(f"\n🪨 LITHOLOGY DISTRIBUTION:")
    lith_counts = df['LITH'].value_counts()
    for lith, count in lith_counts.items():
        pct = (count / len(df)) * 100
        print(f"   🔸 {lith}: {count:,} points ({pct:.1f}%)")
    
    # Data quality check
    print(f"\n🔍 DATA QUALITY:")
    missing_info = []
    required_cols = ['DEPTH_MD', 'GR', 'RDEP', 'RHOB', 'NPHI', 'CALI', 'DTC', 'PEF']
    
    for col in required_cols:
        missing_count = df[col].isna().sum()
        missing_pct = (missing_count / len(df)) * 100
        status = "✅" if missing_count == 0 else "⚠️" if missing_pct < 5 else "❌"
        print(f"   {status} {col}: {missing_count:,} missing ({missing_pct:.1f}%)")
        if missing_count > 0:
            missing_info.append((col, missing_count, missing_pct))
    
    # Create sample subsets for testing
    print(f"\n📦 CREATING TEST SUBSETS:")
    
    # 1. Single well subset
    primary_well = well_counts.index[0]  # Well with most data
    single_well_df = df[df['WELL'] == primary_well].copy()
    single_well_file = f"xeek_single_well_{primary_well.replace('/', '_')}.csv"
    single_well_df.to_csv(single_well_file, index=False)
    print(f"   📁 Single well ({primary_well}): {single_well_file}")
    print(f"      📊 {len(single_well_df):,} points, {single_well_df['LITH'].nunique()} lithologies")
    
    # 2. Multi-well subset (top 3 wells)
    top_wells = well_counts.head(3).index.tolist()
    multi_well_df = df[df['WELL'].isin(top_wells)].copy()
    multi_well_file = "xeek_multi_well_subset.csv"
    multi_well_df.to_csv(multi_well_file, index=False)
    print(f"   📁 Multi-well subset: {multi_well_file}")
    print(f"      📊 {len(multi_well_df):,} points, {len(top_wells)} wells, {multi_well_df['LITH'].nunique()} lithologies")
    
    # 3. Lithology-focused subset (balanced lithologies)
    print(f"\n🪨 CREATING LITHOLOGY-BALANCED SUBSET:")
    balanced_df_list = []
    target_samples = 1000  # samples per lithology
    
    for lith in lith_counts.head(6).index:  # Top 6 lithologies
        lith_data = df[df['LITH'] == lith]
        if len(lith_data) > target_samples:
            # Sample randomly
            sampled = lith_data.sample(n=target_samples, random_state=42)
        else:
            sampled = lith_data
        balanced_df_list.append(sampled)
        print(f"   🔸 {lith}: {len(sampled):,} samples")
    
    balanced_df = pd.concat(balanced_df_list, ignore_index=True)
    balanced_file = "xeek_lithology_balanced.csv"
    balanced_df.to_csv(balanced_file, index=False)
    print(f"   📁 Balanced subset: {balanced_file}")
    print(f"      📊 {len(balanced_df):,} points, {balanced_df['WELL'].nunique()} wells")
    
    return [single_well_file, multi_well_file, balanced_file]

def display_usage_instructions(test_files):
    """Display comprehensive usage instructions"""
    
    print(f"\n🚀 ENHANCED WELL LOG ANALYZER - XEEK DATASET READY!")
    print("=" * 60)
    
    print(f"\n1️⃣ START THE ENHANCED APPLICATION:")
    print(f"   streamlit run well_log_app.py")
    print(f"   📱 Open: http://localhost:8501")
    
    print(f"\n2️⃣ TEST FILES CREATED:")
    for i, filename in enumerate(test_files, 1):
        if os.path.exists(filename):
            size = os.path.getsize(filename) / 1024  # KB
            print(f"   📁 {filename} ({size:.1f} KB)")
    
    print(f"\n3️⃣ NEW FEATURES FOR XEEK DATASET:")
    features = [
        ("🏗️ Well Selection", "Filter and analyze specific wells from the sidebar"),
        ("🪨 Lithology Coloring", "Color track plots by rock type for geological insight"),
        ("📊 Enhanced Crossplot", "Choose between GR and lithology color scales"),
        ("🌍 Geological Info", "View formation and group information"),
        ("📈 Lithology Analysis", "Dedicated tab for rock type distribution"),
        ("🎯 Multi-Well Support", "Compare logs across different wells"),
        ("📋 Enhanced Data Table", "View well and lithology information"),
        ("🔍 Advanced Statistics", "Geological context and data quality metrics")
    ]
    
    for feature, description in features:
        print(f"   {feature}: {description}")
    
    print(f"\n4️⃣ TESTING SCENARIOS:")
    
    scenarios = [
        ("Single Well Analysis", test_files[0] if len(test_files) > 0 else "N/A",
         "Focus on one well for detailed log analysis"),
        ("Multi-Well Comparison", test_files[1] if len(test_files) > 1 else "N/A",
         "Compare logs across multiple wells"),
        ("Lithology Study", test_files[2] if len(test_files) > 2 else "N/A",
         "Balanced dataset for lithology classification"),
        ("Full Dataset", "Original Xeek_train_subset_clean.csv",
         "Complete dataset with all wells and lithologies")
    ]
    
    for i, (test_name, filename, description) in enumerate(scenarios, 1):
        print(f"\n   {i}. {test_name}:")
        print(f"      📁 Upload: {filename}")
        print(f"      💡 {description}")
    
    print(f"\n5️⃣ WORKFLOW RECOMMENDATIONS:")
    print(f"   1. Start with single well data to understand the interface")
    print(f"   2. Enable 'Color by Lithology' to see geological patterns")
    print(f"   3. Use well selection to focus on specific intervals")
    print(f"   4. Explore crossplot with lithology coloring for facies analysis")
    print(f"   5. Check lithology tab for rock type distribution")
    print(f"   6. Export processed data for further analysis")
    
    print(f"\n✅ READY FOR ADVANCED PETROPHYSICAL ANALYSIS!")
    print(f"   Your enhanced well log analyzer is optimized for the Xeek dataset!")

def main():
    """Main function"""
    
    # Prepare Xeek data
    test_files = prepare_xeek_data()
    
    if test_files:
        # Display usage instructions
        display_usage_instructions(test_files)
        
        print(f"\n📁 Files created in current directory:")
        for filename in test_files:
            if os.path.exists(filename):
                size = os.path.getsize(filename) / 1024  # KB
                print(f"   ✅ {filename} ({size:.1f} KB)")
        
        print(f"\n🎉 Xeek dataset preparation complete!")
        print(f"   Upload any of the created files to test the enhanced features!")
    else:
        print(f"\n❌ Could not prepare Xeek dataset. Please check file paths.")

if __name__ == "__main__":
    main()
