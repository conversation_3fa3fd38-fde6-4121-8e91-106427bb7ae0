2015-10-17 22:26:38,533 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445087491445_0010_000001
2015-10-17 22:26:39,330 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 22:26:39,330 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 10 cluster_timestamp: 1445087491445 } attemptId: 1 } keyId: -1547346236)
2015-10-17 22:26:39,534 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 22:26:41,627 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 22:26:41,721 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 22:26:41,768 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 22:26:41,768 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 22:26:41,768 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 22:26:41,768 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 22:26:41,768 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 22:26:41,784 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 22:26:41,784 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 22:26:41,784 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 22:26:41,893 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 22:26:41,909 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 22:26:41,940 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 22:26:41,955 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 22:26:42,002 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 22:26:42,315 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 22:26:42,424 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 22:26:42,424 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 22:26:42,440 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445087491445_0010 to jobTokenSecretManager
2015-10-17 22:26:42,674 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445087491445_0010 because: not enabled; too many maps; too much input;
2015-10-17 22:26:42,690 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445087491445_0010 = 1313861632. Number of splits = 10
2015-10-17 22:26:42,706 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445087491445_0010 = 1
2015-10-17 22:26:42,706 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0010Job Transitioned from NEW to INITED
2015-10-17 22:26:42,706 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445087491445_0010.
2015-10-17 22:26:42,752 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 22:26:42,768 INFO [Socket Reader #1 for port 57683] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 57683
2015-10-17 22:26:42,799 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 22:26:42,799 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 22:26:42,799 INFO [IPC Server listener on 57683] org.apache.hadoop.ipc.Server: IPC Server listener on 57683: starting
2015-10-17 22:26:42,799 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at 04DN8IQ.fareast.corp.microsoft.com/***********:57683
2015-10-17 22:26:42,924 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 22:26:42,924 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 22:26:42,940 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 22:26:42,955 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 22:26:42,955 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 22:26:42,955 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 22:26:42,955 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 22:26:42,971 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 57690
2015-10-17 22:26:42,971 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 22:26:43,034 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_57690_mapreduce____67ihm6\webapp
2015-10-17 22:26:43,268 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:57690
2015-10-17 22:26:43,268 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 57690
2015-10-17 22:26:43,737 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 22:26:43,737 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445087491445_0010
2015-10-17 22:26:43,737 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 22:26:43,752 INFO [Socket Reader #1 for port 57693] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 57693
2015-10-17 22:26:43,752 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 22:26:43,752 INFO [IPC Server listener on 57693] org.apache.hadoop.ipc.Server: IPC Server listener on 57693: starting
2015-10-17 22:26:43,784 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 22:26:43,784 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 22:26:43,784 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 22:26:43,831 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 22:26:43,956 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 22:26:43,956 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 22:26:43,956 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 22:26:43,956 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 22:26:43,971 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0010Job Transitioned from INITED to SETUP
2015-10-17 22:26:43,971 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 22:26:43,987 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445087491445_0010Job Transitioned from SETUP to RUNNING
2015-10-17 22:26:44,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,034 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:44,049 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:26:44,049 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 22:26:44,065 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 22:26:44,096 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445087491445_0010, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0010/job_1445087491445_0010_1.jhist
2015-10-17 22:26:44,971 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 22:26:45,034 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=6 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:36864, vCores:-14> knownNMs=7
2015-10-17 22:26:45,034 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:36864, vCores:-14>
2015-10-17 22:26:45,034 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:46,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 6
2015-10-17 22:26:46,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000002 to attempt_1445087491445_0010_m_000000_0
2015-10-17 22:26:46,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000003 to attempt_1445087491445_0010_m_000003_0
2015-10-17 22:26:46,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000004 to attempt_1445087491445_0010_m_000006_0
2015-10-17 22:26:46,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000005 to attempt_1445087491445_0010_m_000002_0
2015-10-17 22:26:46,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000006 to attempt_1445087491445_0010_m_000004_0
2015-10-17 22:26:46,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000007 to attempt_1445087491445_0010_m_000007_0
2015-10-17 22:26:46,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:20480, vCores:-30>
2015-10-17 22:26:46,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:46,065 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:6 RackLocal:0
2015-10-17 22:26:46,190 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0010/job.jar
2015-10-17 22:26:46,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0010/job.xml
2015-10-17 22:26:46,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 22:26:46,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 22:26:46,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 22:26:46,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:46,362 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,377 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:46,377 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,377 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:46,377 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,377 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:46,377 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,377 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:46,377 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:46,377 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:46,377 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000002 taskAttempt attempt_1445087491445_0010_m_000000_0
2015-10-17 22:26:46,377 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000005 taskAttempt attempt_1445087491445_0010_m_000002_0
2015-10-17 22:26:46,377 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000007 taskAttempt attempt_1445087491445_0010_m_000007_0
2015-10-17 22:26:46,377 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000004 taskAttempt attempt_1445087491445_0010_m_000006_0
2015-10-17 22:26:46,377 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000006 taskAttempt attempt_1445087491445_0010_m_000004_0
2015-10-17 22:26:46,377 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000003 taskAttempt attempt_1445087491445_0010_m_000003_0
2015-10-17 22:26:46,393 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000003_0
2015-10-17 22:26:46,393 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000006_0
2015-10-17 22:26:46,393 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000004_0
2015-10-17 22:26:46,393 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000007_0
2015-10-17 22:26:46,393 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000000_0
2015-10-17 22:26:46,393 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000002_0
2015-10-17 22:26:46,393 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:26:46,440 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:26:46,440 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:26:46,440 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:26:46,440 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:26:46,440 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:26:46,815 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000004_0 : 13562
2015-10-17 22:26:46,815 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000007_0 : 13562
2015-10-17 22:26:46,815 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000004_0] using containerId: [container_1445087491445_0010_01_000006 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 22:26:46,815 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:46,815 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000002_0 : 13562
2015-10-17 22:26:46,815 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000007_0] using containerId: [container_1445087491445_0010_01_000007 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 22:26:46,815 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:46,831 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000004
2015-10-17 22:26:46,831 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:46,831 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000002_0] using containerId: [container_1445087491445_0010_01_000005 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 22:26:46,831 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:46,831 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000007
2015-10-17 22:26:46,831 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:46,831 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000002
2015-10-17 22:26:46,831 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:46,971 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000000_0 : 13562
2015-10-17 22:26:46,971 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000003_0 : 13562
2015-10-17 22:26:46,971 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000000_0] using containerId: [container_1445087491445_0010_01_000002 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 22:26:46,971 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:46,971 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000003_0] using containerId: [container_1445087491445_0010_01_000003 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 22:26:46,971 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:46,971 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000000
2015-10-17 22:26:46,971 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:46,971 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000003
2015-10-17 22:26:46,971 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:46,971 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000006_0 : 13562
2015-10-17 22:26:46,971 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000006_0] using containerId: [container_1445087491445_0010_01_000004 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:57365]
2015-10-17 22:26:46,971 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:46,971 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000006
2015-10-17 22:26:46,971 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:47,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=5 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:19456, vCores:-31> knownNMs=7
2015-10-17 22:26:47,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:26:47,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000008 to attempt_1445087491445_0010_m_000001_0
2015-10-17 22:26:47,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:19456, vCores:-31>
2015-10-17 22:26:47,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:47,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:7 RackLocal:0
2015-10-17 22:26:47,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:47,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:47,174 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000008 taskAttempt attempt_1445087491445_0010_m_000001_0
2015-10-17 22:26:47,174 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000001_0
2015-10-17 22:26:47,174 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 22:26:47,315 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000001_0 : 13562
2015-10-17 22:26:47,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000001_0] using containerId: [container_1445087491445_0010_01_000008 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 22:26:47,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:47,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000001
2015-10-17 22:26:47,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:48,159 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:19456, vCores:-31> knownNMs=7
2015-10-17 22:26:48,159 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:19456, vCores:-31>
2015-10-17 22:26:48,159 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:49,268 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:19456, vCores:-31>
2015-10-17 22:26:49,268 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:50,299 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:26:50,315 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:50,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000009 to attempt_1445087491445_0010_m_000005_0
2015-10-17 22:26:50,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:18432, vCores:-32>
2015-10-17 22:26:50,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:50,315 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:7 RackLocal:1
2015-10-17 22:26:50,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:50,315 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:50,362 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000009 taskAttempt attempt_1445087491445_0010_m_000005_0
2015-10-17 22:26:50,362 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000005_0
2015-10-17 22:26:50,362 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:26:50,924 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000005_0 : 13562
2015-10-17 22:26:50,940 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000005_0] using containerId: [container_1445087491445_0010_01_000009 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:55135]
2015-10-17 22:26:50,940 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:50,940 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000005
2015-10-17 22:26:50,940 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:51,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:17408, vCores:-33> knownNMs=7
2015-10-17 22:26:51,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:26:51,409 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:51,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000010 to attempt_1445087491445_0010_m_000008_0
2015-10-17 22:26:51,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:17408, vCores:-33>
2015-10-17 22:26:51,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:51,409 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:7 RackLocal:2
2015-10-17 22:26:51,409 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:51,409 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:51,721 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:26:52,065 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000010 taskAttempt attempt_1445087491445_0010_m_000008_0
2015-10-17 22:26:52,065 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000008_0
2015-10-17 22:26:52,065 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:26:52,065 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000008 asked for a task
2015-10-17 22:26:52,065 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000008 given task: attempt_1445087491445_0010_m_000001_0
2015-10-17 22:26:52,518 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:16384, vCores:-34> knownNMs=7
2015-10-17 22:26:52,518 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:26:52,518 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:52,518 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000011 to attempt_1445087491445_0010_m_000009_0
2015-10-17 22:26:52,518 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:26:52,518 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 22:26:52,518 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-17 22:26:52,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:26:52,534 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:26:52,925 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000008_0 : 13562
2015-10-17 22:26:52,925 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000008_0] using containerId: [container_1445087491445_0010_01_000010 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:55135]
2015-10-17 22:26:52,925 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:52,925 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000008
2015-10-17 22:26:52,925 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:52,925 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000011 taskAttempt attempt_1445087491445_0010_m_000009_0
2015-10-17 22:26:52,925 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000009_0
2015-10-17 22:26:52,925 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:26:53,378 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000009_0 : 13562
2015-10-17 22:26:53,378 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000009_0] using containerId: [container_1445087491445_0010_01_000011 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:55135]
2015-10-17 22:26:53,378 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:26:53,378 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000009
2015-10-17 22:26:53,378 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:26:53,565 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-34> knownNMs=7
2015-10-17 22:26:57,940 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:26:57,987 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000007 asked for a task
2015-10-17 22:26:57,987 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000007 given task: attempt_1445087491445_0010_m_000007_0
2015-10-17 22:26:58,190 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:26:58,393 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:26:58,440 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000006 asked for a task
2015-10-17 22:26:58,440 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000006 given task: attempt_1445087491445_0010_m_000004_0
2015-10-17 22:26:58,940 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000005 asked for a task
2015-10-17 22:26:58,940 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000005 given task: attempt_1445087491445_0010_m_000002_0
2015-10-17 22:26:59,159 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:26:59,487 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000011 asked for a task
2015-10-17 22:26:59,487 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000011 given task: attempt_1445087491445_0010_m_000009_0
2015-10-17 22:26:59,909 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:26:59,925 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:26:59,940 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.13102192
2015-10-17 22:26:59,987 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000002 asked for a task
2015-10-17 22:26:59,987 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000002 given task: attempt_1445087491445_0010_m_000000_0
2015-10-17 22:26:59,987 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000004 asked for a task
2015-10-17 22:26:59,987 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000004 given task: attempt_1445087491445_0010_m_000006_0
2015-10-17 22:27:00,050 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:27:00,237 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000003 asked for a task
2015-10-17 22:27:00,237 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000003 given task: attempt_1445087491445_0010_m_000003_0
2015-10-17 22:27:02,972 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.13102192
2015-10-17 22:27:05,894 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:27:05,987 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.13102192
2015-10-17 22:27:06,128 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000009 asked for a task
2015-10-17 22:27:06,128 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000009 given task: attempt_1445087491445_0010_m_000005_0
2015-10-17 22:27:09,066 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.22127049
2015-10-17 22:27:09,972 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.050248135
2015-10-17 22:27:10,144 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:27:10,316 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000010 asked for a task
2015-10-17 22:27:10,316 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000010 given task: attempt_1445087491445_0010_m_000008_0
2015-10-17 22:27:12,081 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.23921879
2015-10-17 22:27:12,159 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.051050477
2015-10-17 22:27:12,613 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.038360532
2015-10-17 22:27:12,659 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.0417327
2015-10-17 22:27:12,753 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.08044106
2015-10-17 22:27:12,894 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.08754885
2015-10-17 22:27:13,550 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.1207352
2015-10-17 22:27:15,097 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.23921879
2015-10-17 22:27:15,613 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.117973894
2015-10-17 22:27:16,222 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.13104042
2015-10-17 22:27:16,394 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.11056468
2015-10-17 22:27:16,441 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.131014
2015-10-17 22:27:16,738 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.118798494
2015-10-17 22:27:17,050 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.13103712
2015-10-17 22:27:18,113 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.23921879
2015-10-17 22:27:19,238 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.13101934
2015-10-17 22:27:19,785 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.13104042
2015-10-17 22:27:19,941 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.13102706
2015-10-17 22:27:20,019 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.131014
2015-10-17 22:27:20,519 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.13101135
2015-10-17 22:27:20,550 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.057808816
2015-10-17 22:27:20,628 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.13103712
2015-10-17 22:27:21,191 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.34743196
2015-10-17 22:27:22,504 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.029631535
2015-10-17 22:27:22,738 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.13101934
2015-10-17 22:27:23,175 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.06318094
2015-10-17 22:27:23,254 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.13104042
2015-10-17 22:27:23,535 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.131014
2015-10-17 22:27:23,660 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.09327739
2015-10-17 22:27:23,769 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.13102706
2015-10-17 22:27:23,941 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.13101135
2015-10-17 22:27:24,129 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.13103712
2015-10-17 22:27:24,191 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.34743196
2015-10-17 22:27:25,629 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.04265794
2015-10-17 22:27:26,316 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.13101934
2015-10-17 22:27:26,332 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.10063189
2015-10-17 22:27:26,816 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.13104042
2015-10-17 22:27:26,847 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.11598102
2015-10-17 22:27:26,988 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.131014
2015-10-17 22:27:27,222 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.34743196
2015-10-17 22:27:27,222 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.13102706
2015-10-17 22:27:27,457 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.13101135
2015-10-17 22:27:27,629 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.13103712
2015-10-17 22:27:28,769 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.05438308
2015-10-17 22:27:29,457 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.11952485
2015-10-17 22:27:29,785 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.13101934
2015-10-17 22:27:30,082 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.13414106
2015-10-17 22:27:30,222 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.40343994
2015-10-17 22:27:30,582 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.131014
2015-10-17 22:27:30,769 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.13104042
2015-10-17 22:27:30,988 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.13102706
2015-10-17 22:27:31,269 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.13103712
2015-10-17 22:27:31,613 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.13101135
2015-10-17 22:27:32,035 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.0651324
2015-10-17 22:27:32,738 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.13102318
2015-10-17 22:27:33,238 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.455629
2015-10-17 22:27:33,285 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.13101934
2015-10-17 22:27:33,394 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.15395631
2015-10-17 22:27:34,223 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.131014
2015-10-17 22:27:34,316 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.13104042
2015-10-17 22:27:34,520 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.13102706
2015-10-17 22:27:34,723 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.13103712
2015-10-17 22:27:35,223 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.13101135
2015-10-17 22:27:35,473 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.083045505
2015-10-17 22:27:36,176 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.13102318
2015-10-17 22:27:36,270 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.455629
2015-10-17 22:27:36,785 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.13101934
2015-10-17 22:27:36,832 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.16604526
2015-10-17 22:27:37,645 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.131014
2015-10-17 22:27:37,738 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.13104042
2015-10-17 22:27:38,051 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.13102706
2015-10-17 22:27:38,254 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.13103712
2015-10-17 22:27:38,785 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.13101135
2015-10-17 22:27:39,098 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.13104132
2015-10-17 22:27:39,285 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.455629
2015-10-17 22:27:39,660 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.13102318
2015-10-17 22:27:40,207 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.13101934
2015-10-17 22:27:40,317 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.16604526
2015-10-17 22:27:41,082 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.131014
2015-10-17 22:27:41,145 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.13104042
2015-10-17 22:27:41,567 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.13102706
2015-10-17 22:27:41,707 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.13103712
2015-10-17 22:27:42,192 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.13101135
2015-10-17 22:27:42,332 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.5605582
2015-10-17 22:27:42,504 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.13104132
2015-10-17 22:27:43,035 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.13102318
2015-10-17 22:27:43,629 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.13101934
2015-10-17 22:27:43,739 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.16604526
2015-10-17 22:27:44,801 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.131014
2015-10-17 22:27:44,973 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.13104042
2015-10-17 22:27:45,020 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.13102706
2015-10-17 22:27:45,379 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.5638294
2015-10-17 22:27:45,598 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.15494466
2015-10-17 22:27:46,223 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.13104132
2015-10-17 22:27:46,223 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.13101135
2015-10-17 22:27:46,535 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.13102318
2015-10-17 22:27:47,161 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.16604526
2015-10-17 22:27:47,473 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.16135874
2015-10-17 22:27:48,270 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.20065723
2015-10-17 22:27:48,286 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.18519905
2015-10-17 22:27:48,379 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.5638294
2015-10-17 22:27:48,536 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.15092647
2015-10-17 22:27:49,114 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.22772059
2015-10-17 22:27:49,598 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.13104132
2015-10-17 22:27:49,645 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.18856578
2015-10-17 22:27:49,942 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.15262987
2015-10-17 22:27:50,489 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.16604526
2015-10-17 22:27:50,911 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.23432451
2015-10-17 22:27:51,379 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.6138379
2015-10-17 22:27:51,754 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.23919508
2015-10-17 22:27:51,942 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.23924798
2015-10-17 22:27:52,114 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.21852647
2015-10-17 22:27:52,333 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.6138379
2015-10-17 22:27:52,583 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.23924637
2015-10-17 22:27:53,098 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.13104132
2015-10-17 22:27:53,379 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.21205467
2015-10-17 22:27:53,442 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.23923388
2015-10-17 22:27:54,208 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.16604526
2015-10-17 22:27:54,411 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.667
2015-10-17 22:27:54,473 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.23921585
2015-10-17 22:27:55,270 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.23919508
2015-10-17 22:27:55,458 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.23924798
2015-10-17 22:27:55,755 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.23922287
2015-10-17 22:27:55,958 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.23924637
2015-10-17 22:27:56,567 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.13104132
2015-10-17 22:27:56,911 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.23921506
2015-10-17 22:27:57,083 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.23923388
2015-10-17 22:27:57,426 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.667
2015-10-17 22:27:57,598 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.2257752
2015-10-17 22:27:57,926 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.23921585
2015-10-17 22:27:58,692 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.23919508
2015-10-17 22:27:58,880 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.23924798
2015-10-17 22:27:59,176 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.23922287
2015-10-17 22:27:59,442 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.23924637
2015-10-17 22:28:00,208 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.13104132
2015-10-17 22:28:00,427 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.667
2015-10-17 22:28:00,536 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.23921506
2015-10-17 22:28:00,645 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.23923388
2015-10-17 22:28:01,083 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.3031575
2015-10-17 22:28:01,442 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.23921585
2015-10-17 22:28:02,052 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.23919508
2015-10-17 22:28:02,270 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.23924798
2015-10-17 22:28:02,583 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.23922287
2015-10-17 22:28:03,005 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.23924637
2015-10-17 22:28:03,442 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.6846083
2015-10-17 22:28:03,630 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.13104132
2015-10-17 22:28:04,005 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.23921506
2015-10-17 22:28:04,239 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.23923388
2015-10-17 22:28:04,520 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.3031575
2015-10-17 22:28:04,942 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.23921585
2015-10-17 22:28:05,552 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.23919508
2015-10-17 22:28:05,739 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.23924798
2015-10-17 22:28:06,208 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.23922287
2015-10-17 22:28:06,427 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.23924637
2015-10-17 22:28:06,442 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.72345775
2015-10-17 22:28:07,020 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.14427146
2015-10-17 22:28:07,427 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.23921506
2015-10-17 22:28:07,833 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.23923388
2015-10-17 22:28:07,864 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.3031575
2015-10-17 22:28:08,567 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.23921585
2015-10-17 22:28:09,317 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.23919508
2015-10-17 22:28:09,317 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.23924798
2015-10-17 22:28:09,458 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.76469785
2015-10-17 22:28:09,755 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.23922287
2015-10-17 22:28:09,942 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.23924637
2015-10-17 22:28:10,677 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.22895105
2015-10-17 22:28:11,083 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.23921506
2015-10-17 22:28:11,317 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.23923388
2015-10-17 22:28:11,411 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.3031575
2015-10-17 22:28:12,130 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.23921585
2015-10-17 22:28:12,489 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.81621855
2015-10-17 22:28:12,786 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.23924798
2015-10-17 22:28:12,818 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.23919508
2015-10-17 22:28:13,255 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.23922287
2015-10-17 22:28:13,333 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.23924637
2015-10-17 22:28:14,161 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.23922269
2015-10-17 22:28:14,599 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.23921506
2015-10-17 22:28:14,724 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.23923388
2015-10-17 22:28:14,927 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.3031575
2015-10-17 22:28:15,521 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.86787397
2015-10-17 22:28:15,552 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.23921585
2015-10-17 22:28:16,271 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.23924798
2015-10-17 22:28:16,271 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.23919508
2015-10-17 22:28:16,349 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.23922287
2015-10-17 22:28:16,865 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.23924637
2015-10-17 22:28:17,552 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.23922269
2015-10-17 22:28:18,068 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.23921506
2015-10-17 22:28:18,240 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.23923388
2015-10-17 22:28:18,380 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.3031575
2015-10-17 22:28:18,521 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.9131679
2015-10-17 22:28:19,052 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.25310183
2015-10-17 22:28:19,786 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.25544614
2015-10-17 22:28:19,880 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.23922287
2015-10-17 22:28:19,927 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.25961474
2015-10-17 22:28:20,255 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.27638164
2015-10-17 22:28:20,990 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.23922269
2015-10-17 22:28:21,443 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.2618448
2015-10-17 22:28:21,521 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 0.96173656
2015-10-17 22:28:21,708 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.28545085
2015-10-17 22:28:21,865 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.3031575
2015-10-17 22:28:22,615 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.33106154
2015-10-17 22:28:23,474 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.27199313
2015-10-17 22:28:23,599 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.33351478
2015-10-17 22:28:23,771 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.33925867
2015-10-17 22:28:24,318 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.34743145
2015-10-17 22:28:24,443 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.23922269
2015-10-17 22:28:24,552 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 1.0
2015-10-17 22:28:24,943 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.33738822
2015-10-17 22:28:25,209 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.3452857
2015-10-17 22:28:25,505 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.3031575
2015-10-17 22:28:26,271 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.3474062
2015-10-17 22:28:27,115 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.34742972
2015-10-17 22:28:27,209 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.34587342
2015-10-17 22:28:27,302 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.3474061
2015-10-17 22:28:27,568 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 1.0
2015-10-17 22:28:27,802 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.34743145
2015-10-17 22:28:27,849 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.23922269
2015-10-17 22:28:28,349 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.3474145
2015-10-17 22:28:28,646 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.34743717
2015-10-17 22:28:28,912 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.3694199
2015-10-17 22:28:29,756 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.3474062
2015-10-17 22:28:30,599 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.34742972
2015-10-17 22:28:30,802 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.3474061
2015-10-17 22:28:30,802 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.3473985
2015-10-17 22:28:31,349 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.34743145
2015-10-17 22:28:31,427 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.23922269
2015-10-17 22:28:31,881 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.3474145
2015-10-17 22:28:32,068 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.34743717
2015-10-17 22:28:32,396 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.4402952
2015-10-17 22:28:32,427 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000001_0 is : 1.0
2015-10-17 22:28:32,443 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0010_m_000001_0
2015-10-17 22:28:32,443 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:28:32,443 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000008 taskAttempt attempt_1445087491445_0010_m_000001_0
2015-10-17 22:28:32,443 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000001_0
2015-10-17 22:28:32,443 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 22:28:32,599 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:28:32,615 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0010_m_000001_0
2015-10-17 22:28:32,615 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:28:32,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 22:28:32,959 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-17 22:28:32,959 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-34>
2015-10-17 22:28:32,959 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 22:28:32,959 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-17 22:28:32,959 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-17 22:28:33,099 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.3474062
2015-10-17 22:28:33,240 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0010_m_000005
2015-10-17 22:28:33,240 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 22:28:33,240 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0010_m_000005
2015-10-17 22:28:33,240 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:28:33,240 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:28:33,240 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:28:34,006 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-17 22:28:34,006 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:16384, vCores:-34> knownNMs=7
2015-10-17 22:28:34,006 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000008
2015-10-17 22:28:34,006 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-17 22:28:34,006 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:28:34,053 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.34742972
2015-10-17 22:28:34,287 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.3473985
2015-10-17 22:28:34,334 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.3474061
2015-10-17 22:28:34,849 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.34743145
2015-10-17 22:28:34,896 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.23922269
2015-10-17 22:28:35,224 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.3474145
2015-10-17 22:28:35,553 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.34743717
2015-10-17 22:28:35,693 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.4402952
2015-10-17 22:28:36,599 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.3474062
2015-10-17 22:28:37,584 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.34742972
2015-10-17 22:28:37,771 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.3473985
2015-10-17 22:28:37,896 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.3474061
2015-10-17 22:28:38,287 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.25012204
2015-10-17 22:28:38,396 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.34743145
2015-10-17 22:28:38,600 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.3474145
2015-10-17 22:28:39,037 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.34743717
2015-10-17 22:28:39,115 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.4402952
2015-10-17 22:28:40,006 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.3474062
2015-10-17 22:28:41,100 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.34742972
2015-10-17 22:28:41,271 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.3473985
2015-10-17 22:28:41,350 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.3474061
2015-10-17 22:28:41,850 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.34743145
2015-10-17 22:28:41,865 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.29669252
2015-10-17 22:28:42,068 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.3474145
2015-10-17 22:28:42,490 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.4402952
2015-10-17 22:28:42,600 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.34743717
2015-10-17 22:28:43,412 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.3474062
2015-10-17 22:28:44,522 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.34742972
2015-10-17 22:28:44,756 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.3474061
2015-10-17 22:28:44,756 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.3473985
2015-10-17 22:28:45,225 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.34743145
2015-10-17 22:28:45,397 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.3474054
2015-10-17 22:28:45,537 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.3474145
2015-10-17 22:28:45,959 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.4402952
2015-10-17 22:28:46,053 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.34743717
2015-10-17 22:28:46,756 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.3474062
2015-10-17 22:28:47,834 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.3473985
2015-10-17 22:28:48,022 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.34742972
2015-10-17 22:28:48,303 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.3474061
2015-10-17 22:28:48,631 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.34743145
2015-10-17 22:28:48,865 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.3474054
2015-10-17 22:28:49,006 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.3474145
2015-10-17 22:28:49,334 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.4402952
2015-10-17 22:28:49,412 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.34743717
2015-10-17 22:28:50,522 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.36759982
2015-10-17 22:28:51,147 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.3473985
2015-10-17 22:28:51,553 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.34742972
2015-10-17 22:28:51,803 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.3474061
2015-10-17 22:28:52,225 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.34743145
2015-10-17 22:28:52,225 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.3474054
2015-10-17 22:28:52,428 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.39244536
2015-10-17 22:28:52,850 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.4402952
2015-10-17 22:28:53,022 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.38528183
2015-10-17 22:28:53,881 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.4338076
2015-10-17 22:28:54,584 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.3699552
2015-10-17 22:28:54,928 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.41088226
2015-10-17 22:28:55,256 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.4140479
2015-10-17 22:28:55,600 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.41562036
2015-10-17 22:28:55,834 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.3474054
2015-10-17 22:28:55,975 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.45562187
2015-10-17 22:28:56,225 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.44866765
2015-10-17 22:28:56,538 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.45087165
2015-10-17 22:28:57,444 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.45563135
2015-10-17 22:28:58,241 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.4477688
2015-10-17 22:28:58,334 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.45565325
2015-10-17 22:28:58,725 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.45561612
2015-10-17 22:28:59,022 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.4556257
2015-10-17 22:28:59,288 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.3474054
2015-10-17 22:28:59,522 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.45562187
2015-10-17 22:28:59,631 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.5374166
2015-10-17 22:29:00,100 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.455643
2015-10-17 22:29:00,881 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.45563135
2015-10-17 22:29:01,694 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.45559394
2015-10-17 22:29:01,866 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.45565325
2015-10-17 22:29:02,272 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.45561612
2015-10-17 22:29:02,506 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.4556257
2015-10-17 22:29:02,913 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.3474054
2015-10-17 22:29:02,975 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.45562187
2015-10-17 22:29:03,194 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.5773621
2015-10-17 22:29:03,647 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.455643
2015-10-17 22:29:04,335 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.45563135
2015-10-17 22:29:05,147 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.45559394
2015-10-17 22:29:05,288 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.45565325
2015-10-17 22:29:05,694 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.45561612
2015-10-17 22:29:06,069 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.4556257
2015-10-17 22:29:06,241 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.45562187
2015-10-17 22:29:06,350 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.3474054
2015-10-17 22:29:06,553 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.5773621
2015-10-17 22:29:07,241 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.455643
2015-10-17 22:29:07,757 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.45563135
2015-10-17 22:29:08,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0010_m_000000_0 because it is running on unusable node:MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:29:08,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0010_m_000003_0 because it is running on unusable node:MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:29:08,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0010_m_000006_0 because it is running on unusable node:MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:29:08,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000000_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 22:29:08,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000003
2015-10-17 22:29:08,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 22:29:08,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000002
2015-10-17 22:29:08,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000004
2015-10-17 22:29:08,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 22:29:08,147 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000002 taskAttempt attempt_1445087491445_0010_m_000000_0
2015-10-17 22:29:08,147 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:6 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-17 22:29:08,147 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000003 taskAttempt attempt_1445087491445_0010_m_000003_0
2015-10-17 22:29:08,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000003_0: Container released on a *lost* node
2015-10-17 22:29:08,147 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000000_0
2015-10-17 22:29:08,147 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000003_0
2015-10-17 22:29:08,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000000_0: Container released on a *lost* node
2015-10-17 22:29:08,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000006_0: Container released on a *lost* node
2015-10-17 22:29:08,147 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000004 taskAttempt attempt_1445087491445_0010_m_000006_0
2015-10-17 22:29:08,147 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000006_0
2015-10-17 22:29:08,147 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:29:08,147 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:29:08,147 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:57365
2015-10-17 22:29:08,632 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.45559394
2015-10-17 22:29:08,929 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.45565325
2015-10-17 22:29:09,163 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.45561612
2015-10-17 22:29:09,382 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000000_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 22:29:09,382 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 22:29:09,382 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 22:29:09,522 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 22:29:09,538 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 22:29:09,741 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 22:29:09,741 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.4556257
2015-10-17 22:29:09,741 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.45562187
2015-10-17 22:29:09,741 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out5/_temporary/1/_temporary/attempt_1445087491445_0010_m_000006_0
2015-10-17 22:29:09,741 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out5/_temporary/1/_temporary/attempt_1445087491445_0010_m_000003_0
2015-10-17 22:29:09,741 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out5/_temporary/1/_temporary/attempt_1445087491445_0010_m_000000_0
2015-10-17 22:29:09,741 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 22:29:09,741 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 22:29:09,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000000_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 22:29:09,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:29:09,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:29:09,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:29:09,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:29:09,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:29:09,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:29:09,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:29:09,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:29:09,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:29:09,757 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.3690477
2015-10-17 22:29:10,210 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:4 ScheduledReds:1 AssignedMaps:6 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-17 22:29:10,210 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:15360, vCores:-27> knownNMs=6
2015-10-17 22:29:10,272 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.5773621
2015-10-17 22:29:10,679 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.455643
2015-10-17 22:29:11,335 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_0 is : 0.45563135
2015-10-17 22:29:12,101 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_0 is : 0.45559394
2015-10-17 22:29:12,444 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.45565325
2015-10-17 22:29:12,882 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.45561612
2015-10-17 22:29:13,272 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.45562187
2015-10-17 22:29:13,288 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.4556257
2015-10-17 22:29:13,554 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.4494411
2015-10-17 22:29:13,866 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.5773621
2015-10-17 22:29:14,022 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_0 is : 0.455643
2015-10-17 22:29:14,288 INFO [Socket Reader #1 for port 57693] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 57693: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:29:14,476 INFO [Socket Reader #1 for port 57693] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 57693: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:29:14,647 INFO [Socket Reader #1 for port 57693] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 57693: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:29:15,866 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.45565325
2015-10-17 22:29:16,398 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.45561612
2015-10-17 22:29:16,788 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.45562187
2015-10-17 22:29:16,788 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.4556257
2015-10-17 22:29:17,038 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.45560944
2015-10-17 22:29:17,335 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.5773621
2015-10-17 22:29:19,273 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.45565325
2015-10-17 22:29:19,788 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.45561612
2015-10-17 22:29:20,179 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.46474862
2015-10-17 22:29:20,210 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.4556257
2015-10-17 22:29:20,445 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.45560944
2015-10-17 22:29:20,741 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.5773621
2015-10-17 22:29:22,757 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.45565325
2015-10-17 22:29:23,320 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.45561612
2015-10-17 22:29:23,601 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.5383527
2015-10-17 22:29:23,726 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.4556257
2015-10-17 22:29:23,820 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.45560944
2015-10-17 22:29:24,413 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.5773621
2015-10-17 22:29:26,288 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.49424666
2015-10-17 22:29:26,835 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.49153048
2015-10-17 22:29:27,038 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.56384325
2015-10-17 22:29:27,163 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.48473367
2015-10-17 22:29:27,304 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.45560944
2015-10-17 22:29:27,773 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.6352507
2015-10-17 22:29:28,679 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.6352507
2015-10-17 22:29:29,976 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.55948347
2015-10-17 22:29:30,507 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.56384325
2015-10-17 22:29:30,601 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.5582854
2015-10-17 22:29:30,914 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.45560944
2015-10-17 22:29:30,914 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.55723995
2015-10-17 22:29:31,226 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.667
2015-10-17 22:29:33,429 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.5638328
2015-10-17 22:29:33,929 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.56384325
2015-10-17 22:29:34,070 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.56381613
2015-10-17 22:29:34,336 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.45560944
2015-10-17 22:29:34,617 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.56380385
2015-10-17 22:29:34,695 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.667
2015-10-17 22:29:36,820 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.5638328
2015-10-17 22:29:37,351 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.56384325
2015-10-17 22:29:37,586 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.56381613
2015-10-17 22:29:37,711 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.45560944
2015-10-17 22:29:38,070 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.56380385
2015-10-17 22:29:38,195 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.667
2015-10-17 22:29:40,289 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.5638328
2015-10-17 22:29:40,742 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.56384325
2015-10-17 22:29:41,008 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.56381613
2015-10-17 22:29:41,086 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.51001877
2015-10-17 22:29:41,476 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.56380385
2015-10-17 22:29:41,492 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.667
2015-10-17 22:29:43,758 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.5638328
2015-10-17 22:29:44,242 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.56384325
2015-10-17 22:29:44,523 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.56381226
2015-10-17 22:29:44,523 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.56381613
2015-10-17 22:29:44,945 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.667
2015-10-17 22:29:45,039 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.56380385
2015-10-17 22:29:47,305 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.5638328
2015-10-17 22:29:47,867 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.56384325
2015-10-17 22:29:48,008 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.56381613
2015-10-17 22:29:48,133 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.56381226
2015-10-17 22:29:48,477 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.56380385
2015-10-17 22:29:48,492 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.667
2015-10-17 22:29:50,602 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.5638328
2015-10-17 22:29:51,461 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.56381613
2015-10-17 22:29:51,555 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.59632516
2015-10-17 22:29:51,633 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.56381226
2015-10-17 22:29:51,930 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.56380385
2015-10-17 22:29:52,070 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.6720247
2015-10-17 22:29:54,024 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.5638328
2015-10-17 22:29:54,555 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.59632516
2015-10-17 22:29:54,914 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.56381613
2015-10-17 22:29:55,102 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.667
2015-10-17 22:29:55,258 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.56381226
2015-10-17 22:29:55,352 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.56380385
2015-10-17 22:29:55,602 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.69014263
2015-10-17 22:29:57,571 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.5833571
2015-10-17 22:29:58,414 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.5874731
2015-10-17 22:29:58,618 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.667
2015-10-17 22:29:58,758 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.56381226
2015-10-17 22:29:59,024 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.56655324
2015-10-17 22:29:59,024 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.7122156
2015-10-17 22:30:00,899 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.6487602
2015-10-17 22:30:01,961 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.6582456
2015-10-17 22:30:02,149 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.667
2015-10-17 22:30:02,352 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.56381226
2015-10-17 22:30:02,383 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.6487602
2015-10-17 22:30:02,571 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.6401004
2015-10-17 22:30:02,665 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.7364689
2015-10-17 22:30:02,883 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.6582456
2015-10-17 22:30:04,461 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.667
2015-10-17 22:30:04,758 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.6401004
2015-10-17 22:30:05,477 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.667
2015-10-17 22:30:05,524 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.667
2015-10-17 22:30:05,821 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.56381226
2015-10-17 22:30:06,024 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.7568529
2015-10-17 22:30:06,087 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.667
2015-10-17 22:30:08,008 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.667
2015-10-17 22:30:08,883 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.667
2015-10-17 22:30:09,008 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.667
2015-10-17 22:30:09,274 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.56381226
2015-10-17 22:30:09,352 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.7802766
2015-10-17 22:30:09,540 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.667
2015-10-17 22:30:11,462 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.667
2015-10-17 22:30:12,384 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.667
2015-10-17 22:30:12,430 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.667
2015-10-17 22:30:12,712 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.6357308
2015-10-17 22:30:12,852 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.8013611
2015-10-17 22:30:13,102 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.667
2015-10-17 22:30:14,368 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.6357308
2015-10-17 22:30:15,009 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.667
2015-10-17 22:30:15,821 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.667
2015-10-17 22:30:16,009 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.667
2015-10-17 22:30:16,181 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.667
2015-10-17 22:30:16,446 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.8202226
2015-10-17 22:30:16,587 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.667
2015-10-17 22:30:18,509 INFO [IPC Server handler 25 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.667
2015-10-17 22:30:19,384 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.667
2015-10-17 22:30:19,431 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.667
2015-10-17 22:30:19,556 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.667
2015-10-17 22:30:19,743 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.8407636
2015-10-17 22:30:20,071 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.667
2015-10-17 22:30:22,071 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.667
2015-10-17 22:30:22,665 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.667
2015-10-17 22:30:22,696 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.667
2015-10-17 22:30:22,915 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.667
2015-10-17 22:30:23,118 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.86278886
2015-10-17 22:30:23,540 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.667
2015-10-17 22:30:25,525 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.667
2015-10-17 22:30:25,837 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.67816764
2015-10-17 22:30:26,181 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.667
2015-10-17 22:30:26,431 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.667
2015-10-17 22:30:26,743 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.88582194
2015-10-17 22:30:26,822 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.667
2015-10-17 22:30:29,056 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.69522864
2015-10-17 22:30:29,103 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.667
2015-10-17 22:30:29,697 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.667
2015-10-17 22:30:29,759 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.667
2015-10-17 22:30:30,103 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.9097699
2015-10-17 22:30:30,243 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.667
2015-10-17 22:30:32,431 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.667
2015-10-17 22:30:32,431 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.7149821
2015-10-17 22:30:33,134 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.667
2015-10-17 22:30:33,134 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.667
2015-10-17 22:30:33,462 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.9355257
2015-10-17 22:30:33,790 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.667
2015-10-17 22:30:35,806 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.7319908
2015-10-17 22:30:35,978 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.6746509
2015-10-17 22:30:36,556 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.667
2015-10-17 22:30:36,759 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.6739144
2015-10-17 22:30:37,197 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.9557972
2015-10-17 22:30:37,244 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.6692064
2015-10-17 22:30:39,197 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.7455107
2015-10-17 22:30:39,541 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.6868478
2015-10-17 22:30:40,291 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.6859239
2015-10-17 22:30:40,416 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.667
2015-10-17 22:30:40,713 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.9738699
2015-10-17 22:30:40,759 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.6813539
2015-10-17 22:30:42,650 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.7581115
2015-10-17 22:30:42,978 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.69780946
2015-10-17 22:30:43,822 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.6966191
2015-10-17 22:30:44,150 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.67644763
2015-10-17 22:30:44,213 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 0.9950415
2015-10-17 22:30:44,228 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.6917609
2015-10-17 22:30:45,947 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.77190477
2015-10-17 22:30:46,291 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_0 is : 1.0
2015-10-17 22:30:46,338 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0010_m_000009_0
2015-10-17 22:30:46,338 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:30:46,338 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000011 taskAttempt attempt_1445087491445_0010_m_000009_0
2015-10-17 22:30:46,338 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000009_0
2015-10-17 22:30:46,338 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:30:46,385 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.7085073
2015-10-17 22:30:46,697 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:30:46,697 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0010_m_000009_0
2015-10-17 22:30:46,697 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:30:46,697 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 22:30:46,838 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:4 ScheduledReds:1 AssignedMaps:6 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-17 22:30:47,244 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.706732
2015-10-17 22:30:47,494 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.6936784
2015-10-17 22:30:47,666 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.702901
2015-10-17 22:30:47,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000011
2015-10-17 22:30:47,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:30:47,885 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:30:47,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 22:30:47,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000012 to attempt_1445087491445_0010_r_000000_0
2015-10-17 22:30:47,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:7 RackLocal:3
2015-10-17 22:30:47,900 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:30:47,900 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:30:47,900 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000012 taskAttempt attempt_1445087491445_0010_r_000000_0
2015-10-17 22:30:47,900 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_r_000000_0
2015-10-17 22:30:47,900 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:30:48,369 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_r_000000_0 : 13562
2015-10-17 22:30:48,369 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_r_000000_0] using containerId: [container_1445087491445_0010_01_000012 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:55135]
2015-10-17 22:30:48,385 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:30:48,385 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_r_000000
2015-10-17 22:30:48,385 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:30:48,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-26> knownNMs=6
2015-10-17 22:30:49,510 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.78861225
2015-10-17 22:30:49,760 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.7195953
2015-10-17 22:30:50,588 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.71809995
2015-10-17 22:30:50,697 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.71009254
2015-10-17 22:30:51,072 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.71370053
2015-10-17 22:30:53,041 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.8066596
2015-10-17 22:30:53,150 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.731842
2015-10-17 22:30:54,010 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.729177
2015-10-17 22:30:54,354 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.7244832
2015-10-17 22:30:54,447 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.72544986
2015-10-17 22:30:55,385 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:30:56,088 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_r_000012 asked for a task
2015-10-17 22:30:56,088 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_r_000012 given task: attempt_1445087491445_0010_r_000000_0
2015-10-17 22:30:56,744 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.7434187
2015-10-17 22:30:57,494 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.7402908
2015-10-17 22:30:57,588 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.82338756
2015-10-17 22:30:57,885 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.737273
2015-10-17 22:30:57,916 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.7393975
2015-10-17 22:30:59,651 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 22:31:00,151 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.7552486
2015-10-17 22:31:00,729 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:00,932 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.8424499
2015-10-17 22:31:01,010 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.75209016
2015-10-17 22:31:01,182 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.7570967
2015-10-17 22:31:01,307 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.74972993
2015-10-17 22:31:01,807 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:02,869 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:03,651 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.7674084
2015-10-17 22:31:03,948 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:04,229 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.8595023
2015-10-17 22:31:04,463 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.77688324
2015-10-17 22:31:04,463 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.76352274
2015-10-17 22:31:04,838 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.7615269
2015-10-17 22:31:05,041 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:05,338 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.0
2015-10-17 22:31:06,088 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:07,041 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.7790737
2015-10-17 22:31:07,182 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:07,479 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.8832848
2015-10-17 22:31:07,588 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.8007501
2015-10-17 22:31:08,026 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.77532053
2015-10-17 22:31:08,260 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:08,291 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.77380717
2015-10-17 22:31:08,620 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.033333335
2015-10-17 22:31:09,338 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:10,432 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:10,432 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.7903518
2015-10-17 22:31:10,729 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.9115973
2015-10-17 22:31:10,854 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.82837653
2015-10-17 22:31:11,479 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.78561467
2015-10-17 22:31:11,510 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:11,760 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.7849911
2015-10-17 22:31:11,885 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.033333335
2015-10-17 22:31:12,604 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:13,682 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:13,823 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.8014636
2015-10-17 22:31:13,979 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.93658006
2015-10-17 22:31:14,151 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.85396385
2015-10-17 22:31:14,745 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:14,995 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.7968988
2015-10-17 22:31:15,151 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.033333335
2015-10-17 22:31:15,198 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.7967126
2015-10-17 22:31:15,823 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:16,854 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:17,261 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.81357324
2015-10-17 22:31:17,276 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.9652332
2015-10-17 22:31:17,432 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.8814323
2015-10-17 22:31:17,917 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:18,354 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.033333335
2015-10-17 22:31:18,479 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.80817956
2015-10-17 22:31:18,604 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.8087543
2015-10-17 22:31:18,979 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:20,042 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:20,526 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 0.9934162
2015-10-17 22:31:20,745 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.90948665
2015-10-17 22:31:20,792 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.8256319
2015-10-17 22:31:21,089 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:21,526 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.033333335
2015-10-17 22:31:21,886 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.81956214
2015-10-17 22:31:21,995 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_0 is : 1.0
2015-10-17 22:31:22,011 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.8204184
2015-10-17 22:31:22,058 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0010_m_000008_0
2015-10-17 22:31:22,058 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:31:22,058 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000010 taskAttempt attempt_1445087491445_0010_m_000008_0
2015-10-17 22:31:22,058 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000008_0
2015-10-17 22:31:22,058 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:31:22,136 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:22,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:31:22,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0010_m_000008_0
2015-10-17 22:31:22,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:31:22,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 22:31:23,183 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 22:31:23,401 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:7 RackLocal:3
2015-10-17 22:31:23,886 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.93100846
2015-10-17 22:31:24,229 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 22:31:24,292 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.83741134
2015-10-17 22:31:24,448 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000010
2015-10-17 22:31:24,448 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:31:24,448 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:31:24,448 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:31:24,448 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000013 to attempt_1445087491445_0010_m_000005_1
2015-10-17 22:31:24,448 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:7 RackLocal:4
2015-10-17 22:31:24,448 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:31:24,448 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:31:24,448 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000013 taskAttempt attempt_1445087491445_0010_m_000005_1
2015-10-17 22:31:24,448 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000005_1
2015-10-17 22:31:24,448 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:31:24,651 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.033333335
2015-10-17 22:31:24,964 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000005_1 : 13562
2015-10-17 22:31:24,964 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000005_1] using containerId: [container_1445087491445_0010_01_000013 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:55135]
2015-10-17 22:31:24,964 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:31:24,964 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000005
2015-10-17 22:31:25,308 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 22:31:25,308 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.8298936
2015-10-17 22:31:25,354 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.83138657
2015-10-17 22:31:25,479 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-26> knownNMs=6
2015-10-17 22:31:26,433 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 22:31:27,198 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.9446851
2015-10-17 22:31:27,480 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 22:31:27,745 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.8494675
2015-10-17 22:31:27,855 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.06666667
2015-10-17 22:31:28,573 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 22:31:28,651 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.8412468
2015-10-17 22:31:28,823 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.8431476
2015-10-17 22:31:29,667 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 22:31:30,698 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.96901685
2015-10-17 22:31:30,714 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 22:31:31,042 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.06666667
2015-10-17 22:31:31,167 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.8610033
2015-10-17 22:31:31,745 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:31:31,823 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 22:31:31,870 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000013 asked for a task
2015-10-17 22:31:31,870 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000013 given task: attempt_1445087491445_0010_m_000005_1
2015-10-17 22:31:32,073 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.85226333
2015-10-17 22:31:32,370 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.8550559
2015-10-17 22:31:32,902 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 22:31:33,870 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 0.9881742
2015-10-17 22:31:33,980 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 22:31:34,136 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.06666667
2015-10-17 22:31:34,589 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.8733349
2015-10-17 22:31:35,011 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 22:31:35,699 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_0 is : 1.0
2015-10-17 22:31:35,745 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.86304605
2015-10-17 22:31:35,761 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0010_m_000005_0
2015-10-17 22:31:35,761 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:31:35,761 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000009 taskAttempt attempt_1445087491445_0010_m_000005_0
2015-10-17 22:31:35,761 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000005_0
2015-10-17 22:31:35,761 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:31:35,949 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.86689633
2015-10-17 22:31:36,136 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 22:31:36,261 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:31:36,261 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0010_m_000005_0
2015-10-17 22:31:36,261 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0010_m_000005_1
2015-10-17 22:31:36,261 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:31:36,261 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 22:31:36,261 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 22:31:36,261 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000013 taskAttempt attempt_1445087491445_0010_m_000005_1
2015-10-17 22:31:36,261 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000005_1
2015-10-17 22:31:36,261 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:31:36,933 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:7 RackLocal:4
2015-10-17 22:31:36,995 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 22:31:37,074 INFO [Socket Reader #1 for port 57693] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 57693: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:31:37,120 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 22:31:37,261 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out5/_temporary/1/_temporary/attempt_1445087491445_0010_m_000005_1
2015-10-17 22:31:37,261 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 22:31:37,261 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 22:31:37,386 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.06666667
2015-10-17 22:31:37,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000009
2015-10-17 22:31:37,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:31:37,980 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:31:37,980 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:31:37,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000014 to attempt_1445087491445_0010_m_000006_1
2015-10-17 22:31:37,980 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:7 RackLocal:5
2015-10-17 22:31:37,980 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:31:37,980 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:31:37,980 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000014 taskAttempt attempt_1445087491445_0010_m_000006_1
2015-10-17 22:31:37,980 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000006_1
2015-10-17 22:31:37,980 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:31:38,027 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.88403416
2015-10-17 22:31:38,292 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:38,464 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000006_1 : 13562
2015-10-17 22:31:38,464 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000006_1] using containerId: [container_1445087491445_0010_01_000014 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:55135]
2015-10-17 22:31:38,464 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:31:38,464 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000006
2015-10-17 22:31:38,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:17408, vCores:-25> knownNMs=6
2015-10-17 22:31:38,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000013
2015-10-17 22:31:38,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:7 RackLocal:5
2015-10-17 22:31:38,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000005_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:31:39,167 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.8730767
2015-10-17 22:31:39,324 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.8779028
2015-10-17 22:31:39,324 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:40,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:31:40,042 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:31:40,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000015 to attempt_1445087491445_0010_m_000003_1
2015-10-17 22:31:40,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:7 RackLocal:6
2015-10-17 22:31:40,042 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:31:40,042 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:31:40,042 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000015 taskAttempt attempt_1445087491445_0010_m_000003_1
2015-10-17 22:31:40,042 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000003_1
2015-10-17 22:31:40,042 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:31:40,386 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:40,386 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000003_1 : 13562
2015-10-17 22:31:40,386 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000003_1] using containerId: [container_1445087491445_0010_01_000015 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:55135]
2015-10-17 22:31:40,386 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:31:40,386 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000003
2015-10-17 22:31:40,636 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.10000001
2015-10-17 22:31:41,089 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-26> knownNMs=6
2015-10-17 22:31:41,433 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:41,449 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.89583826
2015-10-17 22:31:42,589 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:42,589 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.884292
2015-10-17 22:31:42,918 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.890475
2015-10-17 22:31:43,043 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:31:43,152 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000014 asked for a task
2015-10-17 22:31:43,152 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000014 given task: attempt_1445087491445_0010_m_000006_1
2015-10-17 22:31:43,949 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:44,730 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.90699095
2015-10-17 22:31:44,980 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.10000001
2015-10-17 22:31:45,121 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:46,089 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.89504313
2015-10-17 22:31:46,261 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:46,480 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.90260243
2015-10-17 22:31:46,511 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:31:46,636 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000015 asked for a task
2015-10-17 22:31:46,636 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000015 given task: attempt_1445087491445_0010_m_000003_1
2015-10-17 22:31:47,590 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:48,168 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.10000001
2015-10-17 22:31:48,308 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.91919243
2015-10-17 22:31:48,668 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:49,527 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.90620565
2015-10-17 22:31:49,746 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:49,855 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.91439295
2015-10-17 22:31:50,855 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:51,340 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.10000001
2015-10-17 22:31:51,762 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.931575
2015-10-17 22:31:51,949 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:52,668 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.005206999
2015-10-17 22:31:53,043 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:53,074 INFO [IPC Server handler 25 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.9178757
2015-10-17 22:31:53,355 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.92698634
2015-10-17 22:31:54,121 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:54,543 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.10000001
2015-10-17 22:31:55,199 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.9436803
2015-10-17 22:31:55,199 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:55,434 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.041680586
2015-10-17 22:31:55,855 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.010741775
2015-10-17 22:31:56,293 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:56,512 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.9290774
2015-10-17 22:31:56,965 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.9396347
2015-10-17 22:31:57,402 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:57,637 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.10000001
2015-10-17 22:31:58,480 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:58,574 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.95528775
2015-10-17 22:31:58,574 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.053081937
2015-10-17 22:31:58,980 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.018885914
2015-10-17 22:31:59,590 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:31:59,996 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.93991315
2015-10-17 22:32:00,527 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.9514927
2015-10-17 22:32:00,684 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:00,731 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.10000001
2015-10-17 22:32:00,731 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:32:00,746 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:32:00,746 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000016 to attempt_1445087491445_0010_m_000000_1
2015-10-17 22:32:00,746 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:7 RackLocal:7
2015-10-17 22:32:00,746 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:32:00,746 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:32:00,746 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000016 taskAttempt attempt_1445087491445_0010_m_000000_1
2015-10-17 22:32:00,746 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000000_1
2015-10-17 22:32:00,746 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 22:32:00,949 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0010_m_000006
2015-10-17 22:32:00,949 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 22:32:00,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0010_m_000006
2015-10-17 22:32:00,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:32:00,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:32:00,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:32:00,965 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000000_1 : 13562
2015-10-17 22:32:00,965 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000000_1] using containerId: [container_1445087491445_0010_01_000016 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 22:32:00,965 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:32:00,965 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000000
2015-10-17 22:32:01,715 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.05753274
2015-10-17 22:32:01,762 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:01,777 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:7 RackLocal:7
2015-10-17 22:32:01,777 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-26> knownNMs=6
2015-10-17 22:32:02,012 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.9670658
2015-10-17 22:32:02,652 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.03517149
2015-10-17 22:32:02,824 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:03,403 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.9513043
2015-10-17 22:32:03,856 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.9638568
2015-10-17 22:32:03,887 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.10000001
2015-10-17 22:32:03,903 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:04,403 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:32:04,434 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000016 asked for a task
2015-10-17 22:32:04,434 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000016 given task: attempt_1445087491445_0010_m_000000_1
2015-10-17 22:32:04,965 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.06382899
2015-10-17 22:32:04,981 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:05,403 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.980165
2015-10-17 22:32:05,824 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.048848696
2015-10-17 22:32:05,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:32:05,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000017 to attempt_1445087491445_0010_m_000006_2
2015-10-17 22:32:05,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-17 22:32:05,887 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:32:05,887 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:32:05,887 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000017 taskAttempt attempt_1445087491445_0010_m_000006_2
2015-10-17 22:32:05,887 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000006_2
2015-10-17 22:32:05,887 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:32:06,059 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:06,074 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000006_2 : 13562
2015-10-17 22:32:06,074 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000006_2] using containerId: [container_1445087491445_0010_01_000017 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:32:06,074 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:32:06,074 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000006
2015-10-17 22:32:06,934 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-26> knownNMs=6
2015-10-17 22:32:06,965 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.963027
2015-10-17 22:32:06,981 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.10000001
2015-10-17 22:32:07,168 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:07,356 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.9759406
2015-10-17 22:32:08,106 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.07001359
2015-10-17 22:32:08,278 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:08,621 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:32:08,653 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000017 asked for a task
2015-10-17 22:32:08,653 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000017 given task: attempt_1445087491445_0010_m_000006_2
2015-10-17 22:32:08,840 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 0.9917846
2015-10-17 22:32:08,934 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.05666488
2015-10-17 22:32:09,356 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:10,090 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.10000001
2015-10-17 22:32:10,434 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:10,512 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.97522646
2015-10-17 22:32:10,762 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 0.98884284
2015-10-17 22:32:11,231 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.07555324
2015-10-17 22:32:11,528 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:11,856 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.13101934
2015-10-17 22:32:11,887 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000004_0 is : 1.0
2015-10-17 22:32:11,887 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0010_m_000004_0
2015-10-17 22:32:11,903 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:32:11,903 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000006 taskAttempt attempt_1445087491445_0010_m_000004_0
2015-10-17 22:32:11,903 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000004_0
2015-10-17 22:32:11,903 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:32:12,075 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.06480449
2015-10-17 22:32:12,387 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:32:12,403 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0010_m_000004_0
2015-10-17 22:32:12,403 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:32:12,403 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 22:32:12,637 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 22:32:13,153 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-17 22:32:13,231 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.10000001
2015-10-17 22:32:13,684 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 0.9874542
2015-10-17 22:32:13,715 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 22:32:13,903 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 1.0
2015-10-17 22:32:14,059 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000007_0 is : 1.0
2015-10-17 22:32:14,059 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0010_m_000007_0
2015-10-17 22:32:14,059 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:32:14,059 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000007 taskAttempt attempt_1445087491445_0010_m_000007_0
2015-10-17 22:32:14,075 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000007_0
2015-10-17 22:32:14,075 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:32:14,168 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000006
2015-10-17 22:32:14,168 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-17 22:32:14,168 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:32:14,215 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:32:14,215 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0010_m_000007_0
2015-10-17 22:32:14,215 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:32:14,215 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 22:32:14,325 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.0788083
2015-10-17 22:32:14,793 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 22:32:14,887 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.13101934
2015-10-17 22:32:15,168 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-17 22:32:15,200 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.07197263
2015-10-17 22:32:15,872 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:32:15,872 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.13101135
2015-10-17 22:32:15,981 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0010_m_000003
2015-10-17 22:32:15,981 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0010_m_000003
2015-10-17 22:32:15,981 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:32:15,981 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:32:15,981 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:32:15,981 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 22:32:16,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-17 22:32:16,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:21504, vCores:-21> knownNMs=6
2015-10-17 22:32:16,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000007
2015-10-17 22:32:16,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:32:16,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-17 22:32:16,372 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.10000001
2015-10-17 22:32:16,700 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000002_0 is : 1.0
2015-10-17 22:32:16,700 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0010_m_000002_0
2015-10-17 22:32:16,700 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:32:16,700 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000005 taskAttempt attempt_1445087491445_0010_m_000002_0
2015-10-17 22:32:16,700 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000002_0
2015-10-17 22:32:16,700 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:32:16,731 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:32:16,731 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0010_m_000002_0
2015-10-17 22:32:16,731 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:32:16,731 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 22:32:16,919 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 22:32:17,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:16 ContRel:0 HostLocal:8 RackLocal:7
2015-10-17 22:32:17,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:32:17,169 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:32:17,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000018 to attempt_1445087491445_0010_m_000003_2
2015-10-17 22:32:17,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-17 22:32:17,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:32:17,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:32:17,169 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000018 taskAttempt attempt_1445087491445_0010_m_000003_2
2015-10-17 22:32:17,169 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000003_2
2015-10-17 22:32:17,169 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:32:17,231 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000003_2 : 13562
2015-10-17 22:32:17,231 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000003_2] using containerId: [container_1445087491445_0010_01_000018 on NM: [04DN8IQ.fareast.corp.microsoft.com:55452]
2015-10-17 22:32:17,231 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:32:17,231 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000003
2015-10-17 22:32:17,450 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.08434536
2015-10-17 22:32:17,887 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.13101934
2015-10-17 22:32:17,997 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:18,184 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:23552, vCores:-19> knownNMs=6
2015-10-17 22:32:18,184 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000005
2015-10-17 22:32:18,184 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-17 22:32:18,184 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:32:18,309 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.07718401
2015-10-17 22:32:18,903 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.13101135
2015-10-17 22:32:19,090 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:19,497 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.10000001
2015-10-17 22:32:20,216 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:20,544 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.09281251
2015-10-17 22:32:20,934 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.23237695
2015-10-17 22:32:21,294 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:21,434 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.08174208
2015-10-17 22:32:21,919 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.23550618
2015-10-17 22:32:22,169 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:32:22,216 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000018 asked for a task
2015-10-17 22:32:22,216 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000018 given task: attempt_1445087491445_0010_m_000003_2
2015-10-17 22:32:22,341 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:22,637 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:32:23,434 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:23,653 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.1038882
2015-10-17 22:32:23,934 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.23921585
2015-10-17 22:32:24,481 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:24,544 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.090862334
2015-10-17 22:32:25,028 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.23923388
2015-10-17 22:32:25,591 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:25,825 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:32:26,653 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:26,778 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.11659008
2015-10-17 22:32:26,997 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.23921585
2015-10-17 22:32:27,684 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.10421314
2015-10-17 22:32:27,763 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:28,122 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.23923388
2015-10-17 22:32:28,935 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:29,231 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:32:30,091 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:30,138 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.24782422
2015-10-17 22:32:30,138 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.13026921
2015-10-17 22:32:31,028 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.11984777
2015-10-17 22:32:31,216 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.34027097
2015-10-17 22:32:31,263 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:31,544 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.04775498
2015-10-17 22:32:32,419 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:32,575 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:32:33,216 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.3474062
2015-10-17 22:32:33,450 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.13102706
2015-10-17 22:32:33,544 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:34,310 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.34743717
2015-10-17 22:32:34,341 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.13101135
2015-10-17 22:32:34,607 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.08369504
2015-10-17 22:32:34,685 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:35,810 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:35,857 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:32:36,294 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.3474062
2015-10-17 22:32:36,747 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.13102706
2015-10-17 22:32:36,982 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:37,450 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.34743717
2015-10-17 22:32:37,685 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.13101135
2015-10-17 22:32:37,716 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.12538314
2015-10-17 22:32:38,154 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:39,169 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:32:39,232 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:39,341 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.3474062
2015-10-17 22:32:39,982 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.13102706
2015-10-17 22:32:40,404 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:40,497 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.34743717
2015-10-17 22:32:40,794 INFO [IPC Server handler 25 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.13102706
2015-10-17 22:32:40,810 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.13101135
2015-10-17 22:32:41,497 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:42,263 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:32:42,388 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.44357747
2015-10-17 22:32:42,622 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:43,154 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.15306494
2015-10-17 22:32:43,544 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.44562325
2015-10-17 22:32:43,685 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:43,857 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.13102706
2015-10-17 22:32:43,904 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.1351555
2015-10-17 22:32:44,794 INFO [IPC Server handler 25 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:45,388 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:32:45,419 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.45563135
2015-10-17 22:32:45,919 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:46,232 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.17130502
2015-10-17 22:32:46,607 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.455643
2015-10-17 22:32:46,919 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.13102706
2015-10-17 22:32:46,982 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:47,029 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.14785823
2015-10-17 22:32:48,060 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:48,451 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.45563135
2015-10-17 22:32:48,498 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:32:49,138 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:49,373 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.18400513
2015-10-17 22:32:49,654 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.455643
2015-10-17 22:32:49,966 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.13102706
2015-10-17 22:32:50,170 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.15795374
2015-10-17 22:32:50,170 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:51,279 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:51,498 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.45563135
2015-10-17 22:32:51,638 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:32:52,341 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:52,513 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.19931087
2015-10-17 22:32:52,732 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.5638263
2015-10-17 22:32:53,013 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.13102706
2015-10-17 22:32:53,310 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.17341787
2015-10-17 22:32:53,420 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:54,513 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:54,545 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.56380075
2015-10-17 22:32:54,732 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:32:55,560 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:55,623 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.21136132
2015-10-17 22:32:55,779 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.5638263
2015-10-17 22:32:56,076 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.13943268
2015-10-17 22:32:56,498 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.18237802
2015-10-17 22:32:56,685 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:57,638 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.56380075
2015-10-17 22:32:57,826 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:57,904 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:32:58,764 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.2214612
2015-10-17 22:32:58,873 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.5638263
2015-10-17 22:32:58,951 INFO [IPC Server handler 25 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:32:59,123 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.17391162
2015-10-17 22:32:59,701 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.19247314
2015-10-17 22:33:00,076 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:00,639 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.5638263
2015-10-17 22:33:00,764 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.56380075
2015-10-17 22:33:01,029 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:33:01,248 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:01,935 INFO [IPC Server handler 25 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.23383677
2015-10-17 22:33:01,982 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.667
2015-10-17 22:33:02,170 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.19996646
2015-10-17 22:33:02,404 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:03,061 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.20420137
2015-10-17 22:33:03,529 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:03,826 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.56380075
2015-10-17 22:33:04,201 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:33:04,654 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:05,092 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.667
2015-10-17 22:33:05,139 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.23922287
2015-10-17 22:33:05,264 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.2292747
2015-10-17 22:33:05,779 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:06,279 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.21527278
2015-10-17 22:33:06,311 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.56380075
2015-10-17 22:33:06,998 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:06,998 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.667
2015-10-17 22:33:07,451 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:33:08,045 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:08,154 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.667
2015-10-17 22:33:08,311 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.23922287
2015-10-17 22:33:08,420 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.23922287
2015-10-17 22:33:09,139 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:09,498 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.22634684
2015-10-17 22:33:10,014 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.667
2015-10-17 22:33:10,233 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:10,608 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:33:11,201 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.667
2015-10-17 22:33:11,311 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:11,373 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.23922287
2015-10-17 22:33:11,561 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.2449386
2015-10-17 22:33:12,405 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:12,655 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.23872338
2015-10-17 22:33:13,061 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.667
2015-10-17 22:33:13,467 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:13,717 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:33:14,264 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.6752565
2015-10-17 22:33:14,436 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.23922287
2015-10-17 22:33:14,576 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:14,655 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.25598317
2015-10-17 22:33:15,670 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:15,764 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.23923388
2015-10-17 22:33:16,092 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.667
2015-10-17 22:33:16,748 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:16,811 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:33:17,280 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.70684713
2015-10-17 22:33:17,514 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.23922287
2015-10-17 22:33:17,748 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.264778
2015-10-17 22:33:17,780 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:18,842 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:18,858 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.23923388
2015-10-17 22:33:19,108 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.6812933
2015-10-17 22:33:19,889 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:33:19,905 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:20,311 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.7373123
2015-10-17 22:33:20,577 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.23922287
2015-10-17 22:33:20,905 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.27356926
2015-10-17 22:33:20,952 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:21,967 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.24751619
2015-10-17 22:33:22,045 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:22,155 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.723017
2015-10-17 22:33:22,983 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:33:23,124 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:23,342 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.7672882
2015-10-17 22:33:23,624 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.27096623
2015-10-17 22:33:23,999 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.28269085
2015-10-17 22:33:24,171 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:25,092 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.25240156
2015-10-17 22:33:25,202 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.7621329
2015-10-17 22:33:25,311 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:26,092 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:33:26,374 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:26,374 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.79709363
2015-10-17 22:33:26,655 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.297557
2015-10-17 22:33:27,139 INFO [IPC Server handler 25 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.28920025
2015-10-17 22:33:27,468 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:28,218 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.25956836
2015-10-17 22:33:28,264 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.80265373
2015-10-17 22:33:28,546 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:29,233 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_r_000000_0 is : 0.13333334
2015-10-17 22:33:29,436 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.8274619
2015-10-17 22:33:29,655 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:29,733 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.3273068
2015-10-17 22:33:30,249 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_1 is : 0.29311132
2015-10-17 22:33:30,764 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445087491445_0010_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 22:33:31,327 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.83196634
2015-10-17 22:33:31,358 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_1 is : 0.26607996
2015-10-17 22:33:31,890 INFO [Socket Reader #1 for port 57693] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 57693: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:33:31,905 INFO [Socket Reader #1 for port 57693] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 57693: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:33:31,905 INFO [Socket Reader #1 for port 57693] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 57693: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:33:32,030 WARN [ResponseProcessor for block BP-1347369012-**************-1444972147527:blk_1073743025_2237] org.apache.hadoop.hdfs.DFSClient: DFSOutputStream ResponseProcessor exception  for block BP-1347369012-**************-1444972147527:blk_1073743025_2237
java.io.IOException: Bad response ERROR for block BP-1347369012-**************-1444972147527:blk_1073743025_2237 from datanode *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer$ResponseProcessor.run(DFSOutputStream.java:898)
2015-10-17 22:33:32,030 WARN [DataStreamer for file /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445087491445_0010/job_1445087491445_0010_1.jhist block BP-1347369012-**************-1444972147527:blk_1073743025_2237] org.apache.hadoop.hdfs.DFSClient: Error Recovery for block BP-1347369012-**************-1444972147527:blk_1073743025_2237 in pipeline ***********:50010, *************:50010: bad datanode *************:50010
2015-10-17 22:33:32,452 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.85822266
2015-10-17 22:33:32,749 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.3473985
2015-10-17 22:33:34,343 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.863845
2015-10-17 22:33:35,483 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.88082993
2015-10-17 22:33:35,765 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.3473985
2015-10-17 22:33:37,358 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.906953
2015-10-17 22:33:38,515 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.90663373
2015-10-17 22:33:38,827 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.3473985
2015-10-17 22:33:40,437 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.9387334
2015-10-17 22:33:41,577 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.9305537
2015-10-17 22:33:41,874 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.3722876
2015-10-17 22:33:43,468 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.97102314
2015-10-17 22:33:44,656 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.94639313
2015-10-17 22:33:44,890 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.3983088
2015-10-17 22:33:46,531 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.9999999
2015-10-17 22:33:47,734 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.96503353
2015-10-17 22:33:47,952 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.42957276
2015-10-17 22:33:49,578 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 0.9999999
2015-10-17 22:33:50,812 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 0.9933437
2015-10-17 22:33:50,984 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.45559394
2015-10-17 22:33:51,484 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000006_2 is : 1.0
2015-10-17 22:33:51,500 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0010_m_000006_2
2015-10-17 22:33:51,500 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_2 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:33:51,500 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000017 taskAttempt attempt_1445087491445_0010_m_000006_2
2015-10-17 22:33:51,500 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000006_2
2015-10-17 22:33:51,500 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:33:51,593 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_2 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:33:51,593 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0010_m_000006_2
2015-10-17 22:33:51,593 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0010_m_000006_1
2015-10-17 22:33:51,593 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:33:51,593 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 22:33:51,593 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000006_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 22:33:51,593 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000014 taskAttempt attempt_1445087491445_0010_m_000006_1
2015-10-17 22:33:51,593 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000006_1
2015-10-17 22:33:51,593 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:33:51,624 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-17 22:33:52,109 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0010_m_000006
2015-10-17 22:33:52,109 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 22:33:52,640 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000017
2015-10-17 22:33:52,640 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-17 22:33:52,640 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000006_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:33:54,000 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.45559394
2015-10-17 22:33:57,047 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.45559394
2015-10-17 22:33:57,672 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000000_1 is : 1.0
2015-10-17 22:33:57,687 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0010_m_000000_1
2015-10-17 22:33:57,687 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000000_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:33:57,687 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000016 taskAttempt attempt_1445087491445_0010_m_000000_1
2015-10-17 22:33:57,703 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000000_1
2015-10-17 22:33:57,718 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 22:33:57,843 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000000_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:33:57,843 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0010_m_000000_1
2015-10-17 22:33:57,843 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:33:57,843 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 22:33:57,984 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-17 22:33:59,015 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000016
2015-10-17 22:33:59,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-17 22:33:59,031 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000000_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:34:00,078 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.4728909
2015-10-17 22:34:03,125 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.4955526
2015-10-17 22:34:06,156 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.5223922
2015-10-17 22:34:07,109 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0010_m_000006
2015-10-17 22:34:07,109 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 22:34:09,187 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.5510527
2015-10-17 22:34:11,594 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 0 time(s); maxRetries=45
2015-10-17 22:34:12,203 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.5637838
2015-10-17 22:34:15,235 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.5637838
2015-10-17 22:34:18,266 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.57191503
2015-10-17 22:34:21,313 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.5921662
2015-10-17 22:34:24,344 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.6122819
2015-10-17 22:34:27,360 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.6357308
2015-10-17 22:34:30,391 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.6617875
2015-10-17 22:34:30,954 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.6617875
2015-10-17 22:34:31,594 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 1 time(s); maxRetries=45
2015-10-17 22:34:33,423 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.667
2015-10-17 22:34:36,454 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.667
2015-10-17 22:34:39,485 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.667
2015-10-17 22:34:42,517 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.68902326
2015-10-17 22:34:45,548 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.72919554
2015-10-17 22:34:48,579 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.76936585
2015-10-17 22:34:51,595 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 2 time(s); maxRetries=45
2015-10-17 22:34:51,611 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.80934525
2015-10-17 22:34:54,626 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.84949696
2015-10-17 22:34:57,658 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.892317
2015-10-17 22:35:00,673 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.9345531
2015-10-17 22:35:03,705 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 0.97705084
2015-10-17 22:35:05,642 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000003_2 is : 1.0
2015-10-17 22:35:05,642 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0010_m_000003_2
2015-10-17 22:35:05,642 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_2 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:35:05,642 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000018 taskAttempt attempt_1445087491445_0010_m_000003_2
2015-10-17 22:35:05,642 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000003_2
2015-10-17 22:35:05,642 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:55452
2015-10-17 22:35:05,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_2 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:35:05,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0010_m_000003_2
2015-10-17 22:35:05,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0010_m_000003_1
2015-10-17 22:35:05,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:35:05,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 22:35:05,658 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000003_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 22:35:05,658 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000015 taskAttempt attempt_1445087491445_0010_m_000003_1
2015-10-17 22:35:05,658 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000003_1
2015-10-17 22:35:05,658 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:35:05,799 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-17 22:35:05,799 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0010_m_000003
2015-10-17 22:35:05,799 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 22:35:06,814 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000018
2015-10-17 22:35:06,814 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-17 22:35:06,814 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000003_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:35:11,596 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 3 time(s); maxRetries=45
2015-10-17 22:35:20,815 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0010_m_000003
2015-10-17 22:35:20,815 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 22:35:25,674 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 0 time(s); maxRetries=45
2015-10-17 22:35:31,596 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 4 time(s); maxRetries=45
2015-10-17 22:35:45,675 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 1 time(s); maxRetries=45
2015-10-17 22:35:51,597 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 5 time(s); maxRetries=45
2015-10-17 22:36:05,675 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 2 time(s); maxRetries=45
2015-10-17 22:36:11,597 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 6 time(s); maxRetries=45
2015-10-17 22:36:25,676 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 3 time(s); maxRetries=45
2015-10-17 22:36:31,598 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 7 time(s); maxRetries=45
2015-10-17 22:36:45,677 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 4 time(s); maxRetries=45
2015-10-17 22:36:51,599 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 8 time(s); maxRetries=45
2015-10-17 22:37:05,677 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 5 time(s); maxRetries=45
2015-10-17 22:37:11,599 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 9 time(s); maxRetries=45
2015-10-17 22:37:25,678 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 6 time(s); maxRetries=45
2015-10-17 22:37:31,600 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 10 time(s); maxRetries=45
2015-10-17 22:37:45,678 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 7 time(s); maxRetries=45
2015-10-17 22:37:51,600 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 11 time(s); maxRetries=45
2015-10-17 22:38:05,679 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 8 time(s); maxRetries=45
2015-10-17 22:38:11,601 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 12 time(s); maxRetries=45
2015-10-17 22:38:25,680 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 9 time(s); maxRetries=45
2015-10-17 22:38:31,602 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 13 time(s); maxRetries=45
2015-10-17 22:38:45,680 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 10 time(s); maxRetries=45
2015-10-17 22:38:51,602 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 14 time(s); maxRetries=45
2015-10-17 22:39:05,681 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 11 time(s); maxRetries=45
2015-10-17 22:39:11,603 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 15 time(s); maxRetries=45
2015-10-17 22:39:25,681 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 12 time(s); maxRetries=45
2015-10-17 22:39:31,603 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 16 time(s); maxRetries=45
2015-10-17 22:39:45,682 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 13 time(s); maxRetries=45
2015-10-17 22:39:51,604 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 17 time(s); maxRetries=45
2015-10-17 22:40:05,683 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 14 time(s); maxRetries=45
2015-10-17 22:40:11,605 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 18 time(s); maxRetries=45
2015-10-17 22:40:25,683 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 15 time(s); maxRetries=45
2015-10-17 22:40:31,605 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 19 time(s); maxRetries=45
2015-10-17 22:40:45,684 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 16 time(s); maxRetries=45
2015-10-17 22:40:51,606 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 20 time(s); maxRetries=45
2015-10-17 22:41:05,684 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 17 time(s); maxRetries=45
2015-10-17 22:41:11,606 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 21 time(s); maxRetries=45
2015-10-17 22:41:25,685 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 18 time(s); maxRetries=45
2015-10-17 22:41:31,607 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 22 time(s); maxRetries=45
2015-10-17 22:41:45,686 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 19 time(s); maxRetries=45
2015-10-17 22:41:51,608 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 23 time(s); maxRetries=45
2015-10-17 22:42:05,686 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 20 time(s); maxRetries=45
2015-10-17 22:42:11,608 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 24 time(s); maxRetries=45
2015-10-17 22:42:25,687 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 21 time(s); maxRetries=45
2015-10-17 22:42:31,609 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 25 time(s); maxRetries=45
2015-10-17 22:42:45,687 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 22 time(s); maxRetries=45
2015-10-17 22:42:51,609 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 26 time(s); maxRetries=45
2015-10-17 22:43:05,688 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 23 time(s); maxRetries=45
2015-10-17 22:43:11,610 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 27 time(s); maxRetries=45
2015-10-17 22:43:25,688 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 24 time(s); maxRetries=45
2015-10-17 22:43:31,611 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 28 time(s); maxRetries=45
2015-10-17 22:43:44,142 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_r_000000_0: AttemptID:attempt_1445087491445_0010_r_000000_0 Timed out after 600 secs
2015-10-17 22:43:44,142 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_r_000000_0 TaskAttempt Transitioned from RUNNING to FAIL_CONTAINER_CLEANUP
2015-10-17 22:43:44,142 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000012 taskAttempt attempt_1445087491445_0010_r_000000_0
2015-10-17 22:43:44,142 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_r_000000_0
2015-10-17 22:43:44,142 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:43:45,689 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 25 time(s); maxRetries=45
2015-10-17 22:43:51,611 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 29 time(s); maxRetries=45
2015-10-17 22:44:04,143 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 0 time(s); maxRetries=45
2015-10-17 22:44:05,690 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 26 time(s); maxRetries=45
2015-10-17 22:44:11,612 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 30 time(s); maxRetries=45
2015-10-17 22:44:24,143 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 1 time(s); maxRetries=45
2015-10-17 22:44:25,690 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 27 time(s); maxRetries=45
2015-10-17 22:44:31,612 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 31 time(s); maxRetries=45
2015-10-17 22:44:44,144 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 2 time(s); maxRetries=45
2015-10-17 22:44:45,691 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 28 time(s); maxRetries=45
2015-10-17 22:44:51,613 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 32 time(s); maxRetries=45
2015-10-17 22:44:52,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0010_m_000006_1 because it is running on unusable node:MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: TaskAttempt killed because it ran on unusable node MININT-FNANLI5.fareast.corp.microsoft.com:55135. AttemptId:attempt_1445087491445_0010_m_000009_0
2015-10-17 22:44:52,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0010_m_000003_1 because it is running on unusable node:MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: TaskAttempt killed because it ran on unusable node MININT-FNANLI5.fareast.corp.microsoft.com:55135. AttemptId:attempt_1445087491445_0010_m_000008_0
2015-10-17 22:44:52,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Killing taskAttempt:attempt_1445087491445_0010_r_000000_0 because it is running on unusable node:MININT-FNANLI5.fareast.corp.microsoft.com:55135
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: TaskAttempt killed because it ran on unusable node MININT-FNANLI5.fareast.corp.microsoft.com:55135. AttemptId:attempt_1445087491445_0010_m_000005_0
2015-10-17 22:44:52,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000014
2015-10-17 22:44:52,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000012
2015-10-17 22:44:52,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000015
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_0 TaskAttempt Transitioned from SUCCEEDED to KILLED
2015-10-17 22:44:52,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000008_0 TaskAttempt Transitioned from SUCCEEDED to KILLED
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_0 TaskAttempt Transitioned from SUCCEEDED to KILLED
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000006_1: Container released on a *lost* node
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_r_000000_0: Container released on a *lost* node
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000009 Task Transitioned from SUCCEEDED to SCHEDULED
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000003_1: Container released on a *lost* node
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:44:52,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000008 Task Transitioned from SUCCEEDED to SCHEDULED
2015-10-17 22:44:52,222 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:44:52,222 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:44:52,222 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000005 Task Transitioned from SUCCEEDED to SCHEDULED
2015-10-17 22:44:52,222 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:44:52,222 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000008_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:44:52,222 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:44:53,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:7 CompletedReds:0 ContAlloc:17 ContRel:0 HostLocal:8 RackLocal:8
2015-10-17 22:44:53,222 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:17408, vCores:-3> knownNMs=3
2015-10-17 22:44:54,254 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 3
2015-10-17 22:44:54,254 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000019 to attempt_1445087491445_0010_m_000009_1
2015-10-17 22:44:54,254 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000020 to attempt_1445087491445_0010_m_000008_1
2015-10-17 22:44:54,254 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:44:54,254 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000021 to attempt_1445087491445_0010_m_000005_2
2015-10-17 22:44:54,254 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:7 CompletedReds:0 ContAlloc:20 ContRel:0 HostLocal:11 RackLocal:8
2015-10-17 22:44:54,254 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:44:54,254 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:44:54,254 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000008_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:44:54,254 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:44:54,254 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:44:54,254 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000019 taskAttempt attempt_1445087491445_0010_m_000009_1
2015-10-17 22:44:54,254 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000020 taskAttempt attempt_1445087491445_0010_m_000008_1
2015-10-17 22:44:54,254 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000009_1
2015-10-17 22:44:54,254 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000021 taskAttempt attempt_1445087491445_0010_m_000005_2
2015-10-17 22:44:54,254 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000008_1
2015-10-17 22:44:54,254 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000005_2
2015-10-17 22:44:54,254 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:44:54,254 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:44:54,254 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:44:54,285 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000008_1 : 13562
2015-10-17 22:44:54,285 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000008_1] using containerId: [container_1445087491445_0010_01_000020 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:44:54,285 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000008_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:44:54,285 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000008
2015-10-17 22:44:54,285 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:44:54,285 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000009_1 : 13562
2015-10-17 22:44:54,285 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000009_1] using containerId: [container_1445087491445_0010_01_000019 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:44:54,285 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:44:54,285 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000009
2015-10-17 22:44:54,285 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:44:54,300 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000005_2 : 13562
2015-10-17 22:44:54,300 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000005_2] using containerId: [container_1445087491445_0010_01_000021 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:44:54,300 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:44:54,300 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000005
2015-10-17 22:44:54,300 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 22:44:55,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:14336, vCores:-6> knownNMs=3
2015-10-17 22:44:57,410 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:44:57,410 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:44:57,426 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000021 asked for a task
2015-10-17 22:44:57,426 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000021 given task: attempt_1445087491445_0010_m_000005_2
2015-10-17 22:44:57,426 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000019 asked for a task
2015-10-17 22:44:57,426 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000019 given task: attempt_1445087491445_0010_m_000009_1
2015-10-17 22:44:58,019 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:44:58,051 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000020 asked for a task
2015-10-17 22:44:58,051 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000020 given task: attempt_1445087491445_0010_m_000008_1
2015-10-17 22:45:04,144 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 3 time(s); maxRetries=45
2015-10-17 22:45:04,566 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445087491445_0010_m_000009
2015-10-17 22:45:04,566 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 22:45:04,566 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445087491445_0010_m_000009
2015-10-17 22:45:04,566 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:45:04,566 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:45:04,566 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_2 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 22:45:05,051 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:7 CompletedReds:0 ContAlloc:20 ContRel:0 HostLocal:11 RackLocal:8
2015-10-17 22:45:05,051 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:13312, vCores:-7> knownNMs=3
2015-10-17 22:45:05,645 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.16604526
2015-10-17 22:45:05,660 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.13104132
2015-10-17 22:45:05,691 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 29 time(s); maxRetries=45
2015-10-17 22:45:05,832 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.13102318
2015-10-17 22:45:06,051 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 22:45:06,051 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445087491445_0010_01_000022 to attempt_1445087491445_0010_m_000009_2
2015-10-17 22:45:06,051 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:7 CompletedReds:0 ContAlloc:21 ContRel:0 HostLocal:12 RackLocal:8
2015-10-17 22:45:06,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 22:45:06,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_2 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 22:45:06,051 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445087491445_0010_01_000022 taskAttempt attempt_1445087491445_0010_m_000009_2
2015-10-17 22:45:06,051 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445087491445_0010_m_000009_2
2015-10-17 22:45:06,051 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:45:06,066 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445087491445_0010_m_000009_2 : 13562
2015-10-17 22:45:06,066 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445087491445_0010_m_000009_2] using containerId: [container_1445087491445_0010_01_000022 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:30535]
2015-10-17 22:45:06,066 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_2 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 22:45:06,066 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445087491445_0010_m_000009
2015-10-17 22:45:07,066 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445087491445_0010: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-9> knownNMs=3
2015-10-17 22:45:08,598 INFO [Socket Reader #1 for port 57693] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445087491445_0010 (auth:SIMPLE)
2015-10-17 22:45:08,629 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445087491445_0010_m_000022 asked for a task
2015-10-17 22:45:08,629 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445087491445_0010_m_000022 given task: attempt_1445087491445_0010_m_000009_2
2015-10-17 22:45:08,692 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.13104132
2015-10-17 22:45:08,707 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.16604526
2015-10-17 22:45:08,863 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.13102318
2015-10-17 22:45:11,613 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 33 time(s); maxRetries=45
2015-10-17 22:45:11,738 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.13104132
2015-10-17 22:45:11,738 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.16604526
2015-10-17 22:45:11,910 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.13102318
2015-10-17 22:45:14,785 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.16316138
2015-10-17 22:45:14,785 INFO [IPC Server handler 21 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.20037866
2015-10-17 22:45:14,942 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.13102318
2015-10-17 22:45:16,207 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.16604526
2015-10-17 22:45:17,832 INFO [IPC Server handler 25 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.23922269
2015-10-17 22:45:17,832 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.3031575
2015-10-17 22:45:17,989 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.14621964
2015-10-17 22:45:19,239 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.16604526
2015-10-17 22:45:20,864 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.3031575
2015-10-17 22:45:20,864 INFO [IPC Server handler 25 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.23922269
2015-10-17 22:45:21,020 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.23921506
2015-10-17 22:45:22,254 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.16604526
2015-10-17 22:45:23,879 INFO [IPC Server handler 25 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.23922269
2015-10-17 22:45:23,895 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.31748804
2015-10-17 22:45:24,067 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.23921506
2015-10-17 22:45:24,145 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 4 time(s); maxRetries=45
2015-10-17 22:45:25,286 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.21634366
2015-10-17 22:45:25,692 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 30 time(s); maxRetries=45
2015-10-17 22:45:26,911 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.3474054
2015-10-17 22:45:26,926 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.4402952
2015-10-17 22:45:27,114 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.23921506
2015-10-17 22:45:28,348 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.3031575
2015-10-17 22:45:29,973 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.3474054
2015-10-17 22:45:29,973 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.4402952
2015-10-17 22:45:30,192 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.3474145
2015-10-17 22:45:31,380 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.3031575
2015-10-17 22:45:31,614 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 34 time(s); maxRetries=45
2015-10-17 22:45:33,005 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.4402952
2015-10-17 22:45:33,005 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.3474054
2015-10-17 22:45:33,239 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.3474145
2015-10-17 22:45:34,427 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.3031575
2015-10-17 22:45:36,036 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.45560944
2015-10-17 22:45:36,036 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.5773621
2015-10-17 22:45:36,255 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.3474145
2015-10-17 22:45:37,458 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.3031575
2015-10-17 22:45:39,052 INFO [IPC Server handler 26 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.5773621
2015-10-17 22:45:39,067 INFO [IPC Server handler 3 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.45560944
2015-10-17 22:45:39,333 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.3474145
2015-10-17 22:45:40,521 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.4402952
2015-10-17 22:45:42,099 INFO [IPC Server handler 8 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.5773621
2015-10-17 22:45:42,130 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.45560944
2015-10-17 22:45:42,411 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.44894952
2015-10-17 22:45:43,568 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.4402952
2015-10-17 22:45:44,146 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 5 time(s); maxRetries=45
2015-10-17 22:45:44,583 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.5773621
2015-10-17 22:45:45,146 INFO [IPC Server handler 23 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.667
2015-10-17 22:45:45,161 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.5262174
2015-10-17 22:45:45,458 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.45562187
2015-10-17 22:45:45,693 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 31 time(s); maxRetries=45
2015-10-17 22:45:46,583 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.4402952
2015-10-17 22:45:48,161 INFO [IPC Server handler 14 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.667
2015-10-17 22:45:48,193 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.56381226
2015-10-17 22:45:48,490 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.45562187
2015-10-17 22:45:49,615 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.4402952
2015-10-17 22:45:51,193 INFO [IPC Server handler 5 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.667
2015-10-17 22:45:51,208 INFO [IPC Server handler 1 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.56381226
2015-10-17 22:45:51,505 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.45562187
2015-10-17 22:45:51,615 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 35 time(s); maxRetries=45
2015-10-17 22:45:52,630 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.5773621
2015-10-17 22:45:54,255 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.67650294
2015-10-17 22:45:54,271 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.56381226
2015-10-17 22:45:54,568 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.56384325
2015-10-17 22:45:55,693 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.5773621
2015-10-17 22:45:57,099 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.56381226
2015-10-17 22:45:57,271 INFO [IPC Server handler 10 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.70927066
2015-10-17 22:45:57,302 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.667
2015-10-17 22:45:57,599 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.56384325
2015-10-17 22:45:58,724 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.5773621
2015-10-17 22:45:59,865 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.5773621
2015-10-17 22:46:00,302 INFO [IPC Server handler 0 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.74557906
2015-10-17 22:46:00,334 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.667
2015-10-17 22:46:00,615 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.56384325
2015-10-17 22:46:01,756 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.667
2015-10-17 22:46:02,756 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.56384325
2015-10-17 22:46:03,334 INFO [IPC Server handler 27 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.77407444
2015-10-17 22:46:03,349 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.667
2015-10-17 22:46:03,646 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.667
2015-10-17 22:46:04,146 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 6 time(s); maxRetries=45
2015-10-17 22:46:04,771 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.667
2015-10-17 22:46:05,693 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 32 time(s); maxRetries=45
2015-10-17 22:46:06,350 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.820628
2015-10-17 22:46:06,365 INFO [IPC Server handler 17 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.667
2015-10-17 22:46:06,662 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.667
2015-10-17 22:46:07,787 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.6820199
2015-10-17 22:46:09,365 INFO [IPC Server handler 22 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.8707931
2015-10-17 22:46:09,412 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.7029893
2015-10-17 22:46:09,678 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.667
2015-10-17 22:46:10,818 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.7368379
2015-10-17 22:46:11,615 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 36 time(s); maxRetries=45
2015-10-17 22:46:12,381 INFO [IPC Server handler 13 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.9214246
2015-10-17 22:46:12,428 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.7444358
2015-10-17 22:46:12,693 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.68816125
2015-10-17 22:46:13,834 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.79219157
2015-10-17 22:46:15,412 INFO [IPC Server handler 9 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 0.97224724
2015-10-17 22:46:15,443 INFO [IPC Server handler 28 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.7860347
2015-10-17 22:46:15,725 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.73004997
2015-10-17 22:46:16,850 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_2 is : 0.84753853
2015-10-17 22:46:17,115 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000009_1 is : 1.0
2015-10-17 22:46:17,131 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0010_m_000009_1
2015-10-17 22:46:17,131 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:46:17,131 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000019 taskAttempt attempt_1445087491445_0010_m_000009_1
2015-10-17 22:46:17,131 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000009_1
2015-10-17 22:46:17,131 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:46:17,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:46:17,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0010_m_000009_1
2015-10-17 22:46:17,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445087491445_0010_m_000009_2
2015-10-17 22:46:17,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:46:17,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 22:46:17,147 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_2 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 22:46:17,147 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000022 taskAttempt attempt_1445087491445_0010_m_000009_2
2015-10-17 22:46:17,147 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000009_2
2015-10-17 22:46:17,147 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:46:17,162 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_2 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 22:46:17,162 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 22:46:17,178 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out5/_temporary/1/_temporary/attempt_1445087491445_0010_m_000009_2
2015-10-17 22:46:17,178 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000009_2 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 22:46:17,225 INFO [Socket Reader #1 for port 57693] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 57693: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 22:46:17,662 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:8 CompletedReds:0 ContAlloc:21 ContRel:0 HostLocal:12 RackLocal:8
2015-10-17 22:46:18,475 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.82654697
2015-10-17 22:46:18,662 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000019
2015-10-17 22:46:18,662 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000022
2015-10-17 22:46:18,662 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000009_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:46:18,662 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:8 CompletedReds:0 ContAlloc:21 ContRel:0 HostLocal:12 RackLocal:8
2015-10-17 22:46:18,662 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000009_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:46:18,740 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.76993454
2015-10-17 22:46:21,491 INFO [IPC Server handler 15 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.86509126
2015-10-17 22:46:21,756 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.8029784
2015-10-17 22:46:24,147 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 7 time(s); maxRetries=45
2015-10-17 22:46:24,522 INFO [IPC Server handler 16 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.9031898
2015-10-17 22:46:24,788 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.83250767
2015-10-17 22:46:25,694 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 33 time(s); maxRetries=45
2015-10-17 22:46:27,553 INFO [IPC Server handler 29 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.9372846
2015-10-17 22:46:27,819 INFO [IPC Server handler 4 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.8525048
2015-10-17 22:46:30,585 INFO [IPC Server handler 7 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 0.9717996
2015-10-17 22:46:30,850 INFO [IPC Server handler 12 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.86975145
2015-10-17 22:46:31,616 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 37 time(s); maxRetries=45
2015-10-17 22:46:33,163 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000005_2 is : 1.0
2015-10-17 22:46:33,163 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0010_m_000005_2
2015-10-17 22:46:33,163 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_2 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:46:33,163 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000021 taskAttempt attempt_1445087491445_0010_m_000005_2
2015-10-17 22:46:33,163 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000005_2
2015-10-17 22:46:33,163 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:46:33,194 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000005_2 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:46:33,194 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0010_m_000005_2
2015-10-17 22:46:33,194 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:46:33,194 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 22:46:33,663 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:9 CompletedReds:0 ContAlloc:21 ContRel:0 HostLocal:12 RackLocal:8
2015-10-17 22:46:33,882 INFO [IPC Server handler 2 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.88691103
2015-10-17 22:46:34,663 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000021
2015-10-17 22:46:34,663 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:9 CompletedReds:0 ContAlloc:21 ContRel:0 HostLocal:12 RackLocal:8
2015-10-17 22:46:34,663 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000005_2: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:46:36,929 INFO [IPC Server handler 20 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.90393454
2015-10-17 22:46:39,960 INFO [IPC Server handler 11 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.92105925
2015-10-17 22:46:42,991 INFO [IPC Server handler 24 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.9381219
2015-10-17 22:46:44,148 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 8 time(s); maxRetries=45
2015-10-17 22:46:45,694 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 34 time(s); maxRetries=45
2015-10-17 22:46:46,023 INFO [IPC Server handler 25 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.95516014
2015-10-17 22:46:49,070 INFO [IPC Server handler 19 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.9724257
2015-10-17 22:46:51,617 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 38 time(s); maxRetries=45
2015-10-17 22:46:52,101 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 0.9895693
2015-10-17 22:46:54,101 INFO [IPC Server handler 18 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445087491445_0010_m_000008_1 is : 1.0
2015-10-17 22:46:54,117 INFO [IPC Server handler 6 on 57693] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445087491445_0010_m_000008_1
2015-10-17 22:46:54,117 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000008_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 22:46:54,117 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445087491445_0010_01_000020 taskAttempt attempt_1445087491445_0010_m_000008_1
2015-10-17 22:46:54,117 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445087491445_0010_m_000008_1
2015-10-17 22:46:54,117 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:30535
2015-10-17 22:46:54,195 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445087491445_0010_m_000008_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 22:46:54,195 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445087491445_0010_m_000008_1
2015-10-17 22:46:54,195 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445087491445_0010_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 22:46:54,195 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 22:46:54,757 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:10 CompletedReds:0 ContAlloc:21 ContRel:0 HostLocal:12 RackLocal:8
2015-10-17 22:46:55,757 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445087491445_0010_01_000020
2015-10-17 22:46:55,757 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:0 ContAlloc:21 ContRel:0 HostLocal:12 RackLocal:8
2015-10-17 22:46:55,757 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445087491445_0010_m_000008_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 22:47:04,148 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 9 time(s); maxRetries=45
2015-10-17 22:47:05,695 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 35 time(s); maxRetries=45
2015-10-17 22:47:11,617 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 39 time(s); maxRetries=45
2015-10-17 22:47:24,149 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 10 time(s); maxRetries=45
2015-10-17 22:47:25,696 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 36 time(s); maxRetries=45
2015-10-17 22:47:31,618 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 40 time(s); maxRetries=45
2015-10-17 22:47:44,149 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 11 time(s); maxRetries=45
2015-10-17 22:47:45,696 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 37 time(s); maxRetries=45
2015-10-17 22:47:51,618 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 41 time(s); maxRetries=45
2015-10-17 22:48:04,150 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 12 time(s); maxRetries=45
2015-10-17 22:48:05,697 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 38 time(s); maxRetries=45
2015-10-17 22:48:11,619 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 42 time(s); maxRetries=45
2015-10-17 22:48:24,150 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 13 time(s); maxRetries=45
2015-10-17 22:48:25,697 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 39 time(s); maxRetries=45
2015-10-17 22:48:31,619 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 43 time(s); maxRetries=45
2015-10-17 22:48:44,151 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 14 time(s); maxRetries=45
2015-10-17 22:48:45,698 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 40 time(s); maxRetries=45
2015-10-17 22:48:51,620 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 44 time(s); maxRetries=45
2015-10-17 22:49:04,152 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 15 time(s); maxRetries=45
2015-10-17 22:49:05,699 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 41 time(s); maxRetries=45
2015-10-17 22:49:24,152 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 16 time(s); maxRetries=45
2015-10-17 22:49:25,699 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 42 time(s); maxRetries=45
2015-10-17 22:49:41,622 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 0 time(s); maxRetries=45
2015-10-17 22:49:44,153 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 17 time(s); maxRetries=45
2015-10-17 22:49:45,700 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 43 time(s); maxRetries=45
2015-10-17 22:50:01,622 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 1 time(s); maxRetries=45
2015-10-17 22:50:04,153 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 18 time(s); maxRetries=45
2015-10-17 22:50:05,700 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 44 time(s); maxRetries=45
2015-10-17 22:50:21,623 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 2 time(s); maxRetries=45
2015-10-17 22:50:24,154 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 19 time(s); maxRetries=45
2015-10-17 22:50:41,623 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 3 time(s); maxRetries=45
2015-10-17 22:50:44,155 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 20 time(s); maxRetries=45
2015-10-17 22:50:55,702 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 0 time(s); maxRetries=45
2015-10-17 22:51:01,624 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 4 time(s); maxRetries=45
2015-10-17 22:51:04,155 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 21 time(s); maxRetries=45
2015-10-17 22:51:15,702 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 1 time(s); maxRetries=45
2015-10-17 22:51:21,625 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 5 time(s); maxRetries=45
2015-10-17 22:51:24,156 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 22 time(s); maxRetries=45
2015-10-17 22:51:35,703 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 2 time(s); maxRetries=45
2015-10-17 22:51:41,625 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 6 time(s); maxRetries=45
2015-10-17 22:51:44,156 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 23 time(s); maxRetries=45
2015-10-17 22:51:55,704 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 3 time(s); maxRetries=45
2015-10-17 22:52:01,626 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 7 time(s); maxRetries=45
2015-10-17 22:52:04,157 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 24 time(s); maxRetries=45
2015-10-17 22:52:15,704 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 4 time(s); maxRetries=45
2015-10-17 22:52:21,626 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 8 time(s); maxRetries=45
2015-10-17 22:52:24,158 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 25 time(s); maxRetries=45
2015-10-17 22:52:35,705 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 5 time(s); maxRetries=45
2015-10-17 22:52:41,627 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 9 time(s); maxRetries=45
2015-10-17 22:52:44,158 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 26 time(s); maxRetries=45
2015-10-17 22:52:54,205 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 0 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:52:55,705 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 6 time(s); maxRetries=45
2015-10-17 22:53:01,628 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 10 time(s); maxRetries=45
2015-10-17 22:53:13,737 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 1 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:53:15,237 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 0 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:53:21,159 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 0 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:53:33,269 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 2 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:53:34,769 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 1 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:53:40,676 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 1 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:53:52,832 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 3 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:53:54,363 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 2 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:54:00,192 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 2 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:54:12,348 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 4 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:54:13,927 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 3 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:54:19,724 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 3 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:54:31,880 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 5 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:54:33,443 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 4 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:54:39,255 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 4 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:54:51,412 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 6 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:54:52,975 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 5 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:54:58,819 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 5 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:55:10,959 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 7 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:55:12,491 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 6 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:55:18,382 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 6 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:55:30,507 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 8 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:55:32,023 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 7 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:55:37,929 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 7 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:55:50,023 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 9 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:55:51,898 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 8 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:55:57,445 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 8 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:56:11,430 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 9 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:56:16,961 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 9 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:56:38,071 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 0 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:56:57,603 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 1 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:56:59,463 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 0 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:57:04,994 INFO [ContainerLauncher #9] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 0 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:57:17,119 INFO [ContainerLauncher #3] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 2 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-17 22:57:18,995 INFO [ContainerLauncher #1] org.apache.hadoop.ipc.Client: Retrying connect to server: MININT-FNANLI5.fareast.corp.microsoft.com/*************:55135. Already tried 1 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
