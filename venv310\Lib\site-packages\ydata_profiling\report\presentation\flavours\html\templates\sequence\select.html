{% if tabs | length > 0 %}
    {# if nested `row` should have no border and the grid divs should be 3-9 #}
    {% if nested %}
        {% set c1 = 3 %}
        {% set c2 = 9 %}
        {% set border = ' style="border: 0px;"' %}
    {% else %}
        {% set c1 = 2 %}
        {% set c2 = 10 %}
        {% set border = '' %}
    {% endif %}
    <div class="row {% if classes %}{{ classes }}{% else %}variable{% endif %}"{% if id %} id="{{ id }}"{% endif %}{{ border }}>
        <div class="col-sm-{{ c1 }}">
            <select class="form-select tab-select"
                    role="tablist"
                    id="select-{{ id }}"
                    multiple>
                {% for tab in tabs %}
                    <option class="tab-select-option {% if loop.first %}active{% endif %}"
                            data-bs-toggle="tab"
                            data-bs-target="#tab-pane-{{ anchor_id }}-{{ tab.anchor_id }}"
                            role="tab"
                            aria-controls="tab-pane-{{ anchor_id }}-{{ tab.anchor_id }}"
                            aria-selected="{% if loop.first %}true{% else %}false{% endif %}">
                        {{ tab.name | fmt_badge }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="tab-content col-sm-{{ c2 }}">
            {% for tab in tabs %}
                <div class="tab-pane col-sm-12 {% if loop.first %}show active{% endif %}"
                     id="tab-pane-{{ anchor_id }}-{{ tab.anchor_id }}"
                     role="tabpanel">
                    {{ tab.render() }}
                </div>
            {% endfor %}
        </div>
    </div>
{% endif %}
