@echo off
echo 🔍 ONGC Knowledge Management System Setup
echo ==========================================

echo.
echo Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH!
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

echo.
echo 📦 Installing dependencies...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt

echo.
echo 🚀 Running setup...
python setup.py

echo.
echo 🧪 Running tests...
python test_search.py

echo.
echo ✅ Setup complete!
echo.
echo Choose how to run the application:
echo 1. Web Interface (Streamlit)
echo 2. Command Line Interface
echo 3. Exit
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo Starting Streamlit web interface...
    streamlit run streamlit_app.py
) else if "%choice%"=="2" (
    echo Starting CLI interface...
    python cli_app.py search --interactive
) else (
    echo Goodbye!
)

pause
