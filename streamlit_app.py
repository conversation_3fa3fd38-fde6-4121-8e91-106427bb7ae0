"""
Streamlit web interface for the Offline Semantic Search Application
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import time
import json

from semantic_search_engine import SemanticSearchEngine
from data_generator import create_sample_dataset
from custom_dataset_manager import CustomDatasetManager
from config import *

# Page configuration
st.set_page_config(
    page_title=STREAMLIT_PAGE_TITLE,
    page_icon=STREAMLIT_PAGE_ICON,
    layout=STREAMLIT_LAYOUT,
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .search-box {
        font-size: 1.2rem;
        padding: 1rem;
        border-radius: 10px;
        border: 2px solid #1f77b4;
    }
    .result-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #1f77b4;
        margin-bottom: 1rem;
    }
    .similarity-score {
        background-color: #e3f2fd;
        padding: 0.2rem 0.5rem;
        border-radius: 15px;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def initialize_search_engine():
    """Initialize and cache the search engine"""
    engine = SemanticSearchEngine()

    # Create sample dataset if it doesn't exist
    if not os.path.exists(SAMPLE_DATASET_FILE):
        create_sample_dataset()

    # Load articles and generate embeddings
    engine.load_articles()
    engine.load_model()
    engine.generate_embeddings()

    # Build FAISS index if available
    try:
        engine.build_faiss_index()
    except Exception as e:
        st.warning(f"FAISS index building failed: {e}")

    return engine

def display_search_results(results, query_time):
    """Display search results in a formatted way"""
    if not results:
        st.warning("No results found. Try adjusting your search query or reducing the similarity threshold.")
        return

    st.success(f"Found {len(results)} results in {query_time:.3f} seconds")

    for result in results:
        with st.container():
            # Create expandable section for each result
            with st.expander(f"📄 {result['rank']}. {result['title']} (Score: {result['similarity_score']:.3f})", expanded=False):

                # Article metadata
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.markdown(f"**📂 Category:** {result['category']}")
                with col2:
                    st.markdown(f"**👤 Author:** {result['author']}")
                with col3:
                    st.markdown(f"**📅 Date:** {result['date']}")

                # Similarity score with color coding
                score = result['similarity_score']
                if score > 0.7:
                    score_color = "🟢"
                    score_text = "High Relevance"
                elif score > 0.5:
                    score_color = "🟡"
                    score_text = "Medium Relevance"
                else:
                    score_color = "🔴"
                    score_text = "Low Relevance"

                st.markdown(f"**🎯 Relevance:** {score_color} {score:.3f} ({score_text})")

                # Tags
                if result.get('tags'):
                    tags_html = " ".join([f"<span style='background-color: #e3f2fd; padding: 4px 8px; border-radius: 12px; margin: 2px; font-size: 0.8em; border: 1px solid #1976d2;'>{tag}</span>" for tag in result['tags']])
                    st.markdown(f"**🏷️ Tags:** {tags_html}", unsafe_allow_html=True)

                # Source information (if available)
                if result.get('source'):
                    st.markdown(f"**📁 Source:** {result['source']}")

                st.markdown("---")

                # Full content with better formatting
                st.markdown("**📝 Full Content:**")

                # Display content in a nice text area
                content = result['content']
                if len(content) > 2000:
                    # For very long content, show in expandable text area
                    st.text_area(
                        "Article Content",
                        value=content,
                        height=300,
                        key=f"content_{result['id']}_{result['rank']}",
                        help="Full article content"
                    )
                else:
                    # For shorter content, display directly
                    st.markdown(f"<div style='background-color: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #1976d2; margin: 10px 0;'>{content}</div>", unsafe_allow_html=True)

                # Action buttons
                col1, col2, col3 = st.columns(3)
                with col1:
                    if st.button(f"📋 Copy Content", key=f"copy_{result['id']}_{result['rank']}"):
                        st.write("Content copied to clipboard!")
                        # Note: Actual clipboard functionality would need additional JS

                with col2:
                    if st.button(f"🔍 Similar Articles", key=f"similar_{result['id']}_{result['rank']}"):
                        st.info("Feature coming soon: Find similar articles")

                with col3:
                    if st.button(f"⭐ Bookmark", key=f"bookmark_{result['id']}_{result['rank']}"):
                        st.success("Article bookmarked!")

            st.markdown("<br>", unsafe_allow_html=True)

def create_analytics_dashboard(engine):
    """Create analytics dashboard"""
    st.header("📊 Knowledge Base Analytics")

    stats = engine.get_statistics()

    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Total Articles", stats['total_articles'])
    with col2:
        st.metric("Categories", stats['total_categories'])
    with col3:
        st.metric("Embedding Dimensions", stats['embedding_dimensions'])
    with col4:
        st.metric("Model", stats['model_name'].split('/')[-1])

    # Category distribution
    if engine.articles:
        category_counts = {}
        for article in engine.articles:
            category = article['category']
            category_counts[category] = category_counts.get(category, 0) + 1

        fig = px.pie(
            values=list(category_counts.values()),
            names=list(category_counts.keys()),
            title="Articles by Category"
        )
        st.plotly_chart(fig, use_container_width=True)

def main():
    """Main Streamlit application"""

    # Header
    st.markdown('<h1 class="main-header">🔍 ONGC Knowledge Management System</h1>', unsafe_allow_html=True)
    st.markdown("### Offline AI-Powered Semantic Search for Technical Knowledge")

    # Initialize search engine
    with st.spinner("Initializing search engine..."):
        engine = initialize_search_engine()

    # Sidebar
    st.sidebar.header("⚙️ Search Configuration")

    # Search parameters
    top_k = st.sidebar.slider("Number of Results", min_value=1, max_value=MAX_TOP_K, value=DEFAULT_TOP_K)
    similarity_threshold = st.sidebar.slider("Similarity Threshold", min_value=0.0, max_value=1.0, value=SIMILARITY_THRESHOLD, step=0.05)
    use_faiss = st.sidebar.checkbox("Use FAISS (Faster)", value=True)

    # Update threshold in engine
    import config
    config.SIMILARITY_THRESHOLD = similarity_threshold

    # Main search interface
    st.header("🔍 Search Knowledge Base")

    # Search examples
    with st.expander("💡 Example Queries"):
        st.markdown("""
        - "drilling fluid optimization high temperature"
        - "reservoir characterization well logs"
        - "corrosion prevention offshore production"
        - "seismic interpretation structural analysis"
        - "enhanced oil recovery techniques"
        - "wellbore stability shale formations"
        """)

    # Search input
    query = st.text_input(
        "Enter your search query:",
        placeholder="e.g., drilling fluid optimization for high temperature wells",
        help="Enter keywords or a natural language question about technical topics"
    )

    # Search button and results
    if st.button("🔍 Search", type="primary") or query:
        if query.strip():
            with st.spinner("Searching..."):
                start_time = time.time()
                results = engine.search(query, top_k=top_k, use_faiss=use_faiss)
                query_time = time.time() - start_time

            display_search_results(results, query_time)
        else:
            st.warning("Please enter a search query.")

    # Tabs for additional features
    tab1, tab2, tab3, tab4 = st.tabs(["📊 Analytics", "📚 Browse Articles", "➕ Manage Dataset", "ℹ️ About"])

    with tab1:
        create_analytics_dashboard(engine)

    with tab2:
        st.header("📚 Browse All Articles")

        # Category filter
        categories = ["All"] + engine.get_statistics()['categories']
        selected_category = st.selectbox("Filter by Category", categories)

        # Display articles
        articles_to_show = engine.articles
        if selected_category != "All":
            articles_to_show = [a for a in engine.articles if a['category'] == selected_category]

        for article in articles_to_show:
            with st.expander(f"{article['title']} ({article['category']})"):
                st.markdown(f"**Author:** {article['author']}")
                st.markdown(f"**Date:** {article['date']}")
                st.markdown(f"**Content:** {article['content']}")
                if article.get('tags'):
                    st.markdown(f"**Tags:** {', '.join(article['tags'])}")

    with tab3:
        st.header("➕ Manage Your Dataset")
        st.markdown("Add your own articles and documents to the search system")

        # Initialize dataset manager
        dataset_manager = CustomDatasetManager()

        # Dataset statistics
        stats = dataset_manager.get_statistics()
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Articles", stats.get('total', 0))
        with col2:
            st.metric("Categories", len(stats.get('categories', {})))
        with col3:
            avg_length = stats.get('avg_content_length', 0)
            st.metric("Avg Length", f"{avg_length:.0f} chars")

        # Add new article section
        st.subheader("📝 Add New Article")

        with st.form("add_article_form"):
            col1, col2 = st.columns(2)
            with col1:
                new_title = st.text_input("Article Title*", placeholder="Enter article title")
                new_category = st.text_input("Category", value="General", placeholder="e.g., Technical, Research")
            with col2:
                new_author = st.text_input("Author", value="User", placeholder="Author name")
                new_tags = st.text_input("Tags", placeholder="tag1, tag2, tag3")

            new_content = st.text_area("Article Content*", height=200, placeholder="Enter the full article content here...")

            submitted = st.form_submit_button("➕ Add Article")

            if submitted:
                if new_title and new_content:
                    tags_list = [tag.strip() for tag in new_tags.split(",")] if new_tags else []

                    article = dataset_manager.add_article(
                        title=new_title,
                        content=new_content,
                        category=new_category,
                        author=new_author,
                        tags=tags_list
                    )

                    dataset_manager.save_articles()
                    st.success(f"✅ Article '{new_title}' added successfully!")

                    # Clear the search engine cache to reload data
                    if 'search_engine' in st.session_state:
                        del st.session_state['search_engine']

                    st.rerun()
                else:
                    st.error("❌ Please fill in both title and content fields.")

        # File upload section
        st.subheader("📁 Upload Files")

        uploaded_file = st.file_uploader(
            "Upload text files or CSV",
            type=['txt', 'csv'],
            help="Upload .txt files (one article per file) or .csv files with article data"
        )

        if uploaded_file is not None:
            file_type = uploaded_file.name.split('.')[-1].lower()

            if file_type == 'txt':
                # Handle text file
                content = str(uploaded_file.read(), "utf-8")
                filename = uploaded_file.name

                col1, col2 = st.columns(2)
                with col1:
                    file_title = st.text_input("Title for uploaded file", value=f"Document: {filename}")
                    file_category = st.text_input("Category for file", value="Uploaded")
                with col2:
                    file_author = st.text_input("Author for file", value="File Upload")

                if st.button("📄 Add Text File"):
                    dataset_manager.add_article(
                        title=file_title,
                        content=content,
                        category=file_category,
                        author=file_author,
                        tags=["uploaded", "text_file"],
                        source=filename
                    )
                    dataset_manager.save_articles()
                    st.success(f"✅ Text file '{filename}' added successfully!")
                    st.rerun()

            elif file_type == 'csv':
                # Handle CSV file
                try:
                    df = pd.read_csv(uploaded_file)
                    st.write("📊 CSV Preview:")
                    st.dataframe(df.head())

                    col1, col2 = st.columns(2)
                    with col1:
                        title_col = st.selectbox("Title Column", df.columns)
                        content_col = st.selectbox("Content Column", df.columns)
                    with col2:
                        category_col = st.selectbox("Category Column (optional)", ["None"] + list(df.columns))
                        author_col = st.selectbox("Author Column (optional)", ["None"] + list(df.columns))

                    if st.button("📊 Import CSV Data"):
                        category_col = None if category_col == "None" else category_col
                        author_col = None if author_col == "None" else author_col

                        added_count = dataset_manager.add_articles_from_csv(
                            uploaded_file.name,
                            title_col,
                            content_col,
                            category_col,
                            author_col
                        )

                        dataset_manager.save_articles()
                        st.success(f"✅ Added {added_count} articles from CSV!")
                        st.rerun()

                except Exception as e:
                    st.error(f"❌ Error reading CSV: {e}")

        # Current articles management
        st.subheader("📚 Current Articles")

        if stats.get('total', 0) > 0:
            # Show recent articles
            recent_articles = dataset_manager.articles[-5:]  # Last 5 articles

            for article in reversed(recent_articles):
                with st.expander(f"📄 {article['title']} (ID: {article['id']})"):
                    col1, col2 = st.columns(2)
                    with col1:
                        st.write(f"**Category:** {article['category']}")
                        st.write(f"**Author:** {article['author']}")
                    with col2:
                        st.write(f"**Date:** {article['date']}")
                        st.write(f"**Source:** {article.get('source', 'Manual')}")

                    content_preview = article['content'][:200] + "..." if len(article['content']) > 200 else article['content']
                    st.write(f"**Content:** {content_preview}")

                    if st.button(f"🗑️ Remove", key=f"remove_{article['id']}"):
                        if dataset_manager.remove_article(article['id']):
                            dataset_manager.save_articles()
                            st.success("Article removed!")
                            st.rerun()

            # Clear all button
            st.markdown("---")
            if st.button("🗑️ Clear All Articles", type="secondary"):
                if st.checkbox("I understand this will delete all articles"):
                    dataset_manager.clear_all_articles()
                    dataset_manager.save_articles()
                    st.success("All articles cleared!")
                    st.rerun()
        else:
            st.info("No articles in dataset. Add some articles above to get started!")

    with tab4:
        st.header("ℹ️ About This Application")
        st.markdown("""
        This is an **offline AI-powered semantic search application** designed for technical knowledge management.

        **Key Features:**
        - 🔍 **Semantic Search**: Uses SBERT embeddings to understand meaning, not just keywords
        - ⚡ **Fast Search**: Optional FAISS integration for lightning-fast similarity search
        - 🌐 **Completely Offline**: No internet connection required after initial setup
        - 📊 **Analytics Dashboard**: Insights into your knowledge base
        - 🎯 **Relevance Scoring**: Shows similarity scores for each result

        **Technology Stack:**
        - **SBERT**: Sentence-BERT for text embeddings
        - **FAISS**: Facebook AI Similarity Search (optional)
        - **Streamlit**: Web interface
        - **Cosine Similarity**: For measuring text similarity

        **Perfect for:**
        - Technical documentation search
        - Research paper discovery
        - Knowledge base exploration
        - Expert advice retrieval
        """)

if __name__ == "__main__":
    main()
