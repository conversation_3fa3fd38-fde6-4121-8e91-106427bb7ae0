2015-10-17 22:45:05,547 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 22:45:05,605 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 22:45:05,605 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-17 22:45:05,621 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 22:45:05,621 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445087491445_0008, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@78093f60)
2015-10-17 22:45:05,721 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 22:45:05,963 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445087491445_0008
2015-10-17 22:45:06,471 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 22:45:06,905 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 22:45:06,933 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@1e30bbc5
2015-10-17 22:45:06,964 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@35c7d3d3
2015-10-17 22:45:06,982 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=140928608, maxSingleShuffleLimit=35232152, mergeThreshold=93012888, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-17 22:45:06,984 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445087491445_0008_r_000000_1000 Thread started: EventFetcher for fetching Map Completion Events
2015-10-17 22:45:06,993 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-17 22:45:06,993 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-17 22:45:06,993 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-17 22:45:06,993 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 22:45:06,994 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445087491445_0008_r_000000_1000: Got 10 new map-outputs
2015-10-17 22:45:07,014 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445087491445_0008&reduce=0&map=attempt_1445087491445_0008_m_000000_0 sent hash and received reply
2015-10-17 22:45:07,016 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445087491445_0008&reduce=0&map=attempt_1445087491445_0008_m_000001_0 sent hash and received reply
2015-10-17 22:45:07,017 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0008_m_000000_0: Shuffling to disk since 216988123 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 22:45:07,024 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0008_m_000001_0: Shuffling to disk since 217009502 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 22:45:07,024 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445087491445_0008_m_000000_0 decomp: 216988123 len: 216988127 to DISK
2015-10-17 22:45:07,028 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445087491445_0008_m_000001_0 decomp: 217009502 len: 217009506 to DISK
2015-10-17 22:45:08,711 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216988127 bytes from map-output for attempt_1445087491445_0008_m_000000_0
2015-10-17 22:45:08,722 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 1730ms
2015-10-17 22:45:08,723 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 5 to fetcher#4
2015-10-17 22:45:08,723 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 5 of 5 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#4
2015-10-17 22:45:08,729 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445087491445_0008&reduce=0&map=attempt_1445087491445_0008_m_000002_0,attempt_1445087491445_0008_m_000003_0,attempt_1445087491445_0008_m_000004_0,attempt_1445087491445_0008_m_000006_0,attempt_1445087491445_0008_m_000007_0 sent hash and received reply
2015-10-17 22:45:08,729 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0008_m_000002_0: Shuffling to disk since 216991624 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 22:45:08,906 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#4 about to shuffle output of map attempt_1445087491445_0008_m_000002_0 decomp: 216991624 len: 216991628 to DISK
2015-10-17 22:45:09,198 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217009506 bytes from map-output for attempt_1445087491445_0008_m_000001_0
2015-10-17 22:45:09,208 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 2215ms
2015-10-17 22:45:09,208 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 3 to fetcher#1
2015-10-17 22:45:09,208 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 3 of 3 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 22:45:09,218 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445087491445_0008&reduce=0&map=attempt_1445087491445_0008_m_000005_0,attempt_1445087491445_0008_m_000008_0,attempt_1445087491445_0008_m_000009_0 sent hash and received reply
2015-10-17 22:45:09,219 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0008_m_000005_0: Shuffling to disk since 216990140 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 22:45:09,222 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445087491445_0008_m_000005_0 decomp: 216990140 len: 216990144 to DISK
2015-10-17 22:45:11,339 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216991628 bytes from map-output for attempt_1445087491445_0008_m_000002_0
2015-10-17 22:45:12,161 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0008_m_000003_0: Shuffling to disk since 216972750 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 22:45:12,167 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#4 about to shuffle output of map attempt_1445087491445_0008_m_000003_0 decomp: 216972750 len: 216972754 to DISK
2015-10-17 22:45:13,811 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216990144 bytes from map-output for attempt_1445087491445_0008_m_000005_0
2015-10-17 22:45:13,823 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0008_m_000008_0: Shuffling to disk since 217015228 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 22:45:13,830 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445087491445_0008_m_000008_0 decomp: 217015228 len: 217015232 to DISK
2015-10-17 22:45:17,137 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216972754 bytes from map-output for attempt_1445087491445_0008_m_000003_0
2015-10-17 22:45:17,145 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0008_m_000004_0: Shuffling to disk since 216999709 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 22:45:17,148 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#4 about to shuffle output of map attempt_1445087491445_0008_m_000004_0 decomp: 216999709 len: 216999713 to DISK
2015-10-17 22:45:17,815 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217015232 bytes from map-output for attempt_1445087491445_0008_m_000008_0
2015-10-17 22:45:17,821 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0008_m_000009_0: Shuffling to disk since 172334804 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 22:45:17,824 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445087491445_0008_m_000009_0 decomp: 172334804 len: 172334808 to DISK
2015-10-17 22:45:20,242 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216999713 bytes from map-output for attempt_1445087491445_0008_m_000004_0
2015-10-17 22:45:20,248 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0008_m_000006_0: Shuffling to disk since 217011663 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 22:45:20,251 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#4 about to shuffle output of map attempt_1445087491445_0008_m_000006_0 decomp: 217011663 len: 217011667 to DISK
2015-10-17 22:45:20,925 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 172334808 bytes from map-output for attempt_1445087491445_0008_m_000009_0
2015-10-17 22:45:20,931 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 11722ms
2015-10-17 22:45:23,743 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217011667 bytes from map-output for attempt_1445087491445_0008_m_000006_0
2015-10-17 22:45:23,757 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445087491445_0008_m_000007_0: Shuffling to disk since 216976206 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 22:45:23,760 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#4 about to shuffle output of map attempt_1445087491445_0008_m_000007_0 decomp: 216976206 len: 216976210 to DISK
2015-10-17 22:45:27,373 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216976210 bytes from map-output for attempt_1445087491445_0008_m_000007_0
2015-10-17 22:45:27,379 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#4 in 18657ms
2015-10-17 22:45:27,379 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: EventFetcher is interrupted.. Returning
2015-10-17 22:45:27,386 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: finalMerge called with 0 in-memory map-outputs and 10 on-disk map-outputs
2015-10-17 22:45:37,428 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 10 files, 2125289789 bytes from disk
2015-10-17 22:45:37,431 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 0 segments, 0 bytes from memory into reduce
2015-10-17 22:45:37,439 INFO [main] org.apache.hadoop.mapred.Merger: Merging 10 sorted segments
2015-10-17 22:45:38,274 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 10 segments left of total size: 2125289689 bytes
2015-10-17 22:45:38,629 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: mapred.skip.on is deprecated. Instead, use mapreduce.job.skiprecords
2015-10-17 22:46:00,107 INFO [Thread-24] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as *************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-17 22:46:00,112 INFO [Thread-24] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073743071_2287
2015-10-17 22:46:00,128 INFO [Thread-24] org.apache.hadoop.hdfs.DFSClient: Excluding datanode *************:50010
2015-10-17 22:50:28,704 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445087491445_0008_r_000000_1000 is done. And is in the process of committing
2015-10-17 22:50:28,743 INFO [main] org.apache.hadoop.mapred.Task: Task attempt_1445087491445_0008_r_000000_1000 is allowed to commit now
2015-10-17 22:50:28,749 INFO [main] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Saved output of task 'attempt_1445087491445_0008_r_000000_1000' to hdfs://msra-sa-41:9000/out/out4/_temporary/2/task_1445087491445_0008_r_000000
2015-10-17 22:50:28,777 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445087491445_0008_r_000000_1000' done.
