'''temporary location for enhancements to scipy.stats

includes
^^^^^^^^

* Per Brodtkorb's estimation enhancements to scipy.stats.distributions
  - distributions_per.py is copy of scipy.stats.distributions.py with changes
  - distributions_profile.py partially extracted classes and functions to
    separate code into more managable pieces
* jose<PERSON>'s extra distribution and helper functions
  - moment helpers
  - goodness of fit test
  - fitting distributions with some fixed parameters
  - find best distribution that fits data: working script
* example and test folders to keep all together

status
^^^^^^

mixed status : from not-working to well-tested


'''
