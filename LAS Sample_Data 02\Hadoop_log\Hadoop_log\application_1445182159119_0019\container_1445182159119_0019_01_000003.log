2015-10-19 17:39:42,415 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 17:39:42,587 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 17:39:42,587 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-19 17:39:42,618 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-19 17:39:42,618 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445182159119_0019, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@5d3cb6cf)
2015-10-19 17:39:42,837 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-19 17:39:47,447 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445182159119_0019
2015-10-19 17:39:48,681 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-19 17:39:49,291 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-19 17:39:49,322 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@4d90b2ca
2015-10-19 17:39:49,697 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:134217728+134217728
2015-10-19 17:39:49,775 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-19 17:39:49,775 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-19 17:39:49,775 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-19 17:39:49,775 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-19 17:39:49,775 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-19 17:39:49,775 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-19 17:40:55,638 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:40:55,638 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48254386; bufvoid = 104857600
2015-10-19 17:40:55,638 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17306472(69225888); length = 8907925/6553600
2015-10-19 17:40:55,638 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57323122 kvi 14330776(57323104)
2015-10-19 17:41:28,999 INFO [communication thread] org.apache.hadoop.mapred.Task: Communication exception: java.io.IOException: Failed on local exception: java.io.IOException: An existing connection was forcibly closed by the remote host; Host Details : local host is: "MININT-FNANLI5/*************"; destination host is: "minint-75dgdam1.fareast.corp.microsoft.com":58957; 
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:772)
	at org.apache.hadoop.ipc.Client.call(Client.java:1472)
	at org.apache.hadoop.ipc.Client.call(Client.java:1399)
	at org.apache.hadoop.ipc.WritableRpcEngine$Invoker.invoke(WritableRpcEngine.java:244)
	at com.sun.proxy.$Proxy9.statusUpdate(Unknown Source)
	at org.apache.hadoop.mapred.Task$TaskReporter.run(Task.java:737)
	at java.lang.Thread.run(Thread.java:724)
Caused by: java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.net.SocketInputStream$Reader.performIO(SocketInputStream.java:57)
	at org.apache.hadoop.net.SocketIOWithTimeout.doIO(SocketIOWithTimeout.java:142)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:161)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:131)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at org.apache.hadoop.ipc.Client$Connection$PingInputStream.read(Client.java:513)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:235)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:254)
	at java.io.DataInputStream.readInt(DataInputStream.java:387)
	at org.apache.hadoop.ipc.Client$Connection.receiveRpcResponse(Client.java:1071)
	at org.apache.hadoop.ipc.Client$Connection.run(Client.java:966)

2015-10-19 17:41:36,968 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-19 17:41:36,968 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57323122 kv 14330776(57323104) kvi 12127800(48511200)
2015-10-19 17:41:43,875 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:41:43,875 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57323122; bufend = 699496; bufvoid = 104857600
2015-10-19 17:41:43,875 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14330776(57323104); kvend = 5417752(21671008); length = 8913025/6553600
2015-10-19 17:41:43,875 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9768248 kvi 2442056(9768224)
2015-10-19 17:41:53,766 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 0 time(s); maxRetries=45
2015-10-19 17:42:13,877 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 1 time(s); maxRetries=45
2015-10-19 17:42:27,002 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-19 17:42:27,174 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9768248 kv 2442056(9768224) kvi 241544(966176)
2015-10-19 17:42:33,878 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 2 time(s); maxRetries=45
2015-10-19 17:42:34,346 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:42:34,346 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9768248; bufend = 57981481; bufvoid = 104857600
2015-10-19 17:42:34,346 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2442056(9768224); kvend = 19738252(78953008); length = 8918205/6553600
2015-10-19 17:42:34,346 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67050233 kvi 16762552(67050208)
2015-10-19 17:42:53,879 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 3 time(s); maxRetries=45
2015-10-19 17:43:13,880 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 4 time(s); maxRetries=45
2015-10-19 17:43:16,255 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-19 17:43:16,317 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67050233 kv 16762552(67050208) kvi 14554320(58217280)
2015-10-19 17:43:23,380 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:43:23,380 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67050233; bufend = 10441503; bufvoid = 104857600
2015-10-19 17:43:23,380 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16762552(67050208); kvend = 7853256(31413024); length = 8909297/6553600
2015-10-19 17:43:23,380 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19510255 kvi 4877556(19510224)
2015-10-19 17:43:33,881 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 5 time(s); maxRetries=45
2015-10-19 17:43:53,882 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 6 time(s); maxRetries=45
2015-10-19 17:44:07,601 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-19 17:44:07,695 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 19510255 kv 4877556(19510224) kvi 2674180(10696720)
2015-10-19 17:44:13,883 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 7 time(s); maxRetries=45
2015-10-19 17:44:16,289 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:44:16,289 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 19510255; bufend = 67733940; bufvoid = 104857600
2015-10-19 17:44:16,289 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 4877556(19510224); kvend = 22176360(88705440); length = 8915597/6553600
2015-10-19 17:44:16,289 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 76802676 kvi 19200664(76802656)
2015-10-19 17:44:33,884 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 8 time(s); maxRetries=45
2015-10-19 17:44:53,885 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 9 time(s); maxRetries=45
2015-10-19 17:45:03,933 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-19 17:45:04,230 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 76802676 kv 19200664(76802656) kvi 17001428(68005712)
2015-10-19 17:45:11,902 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:45:11,902 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 76802676; bufend = 20189504; bufvoid = 104857600
2015-10-19 17:45:11,902 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 19200664(76802656); kvend = 10290256(41161024); length = 8910409/6553600
2015-10-19 17:45:11,902 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29258256 kvi 7314560(29258240)
2015-10-19 17:45:13,886 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 10 time(s); maxRetries=45
2015-10-19 17:45:33,887 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 11 time(s); maxRetries=45
2015-10-19 17:45:53,888 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 12 time(s); maxRetries=45
2015-10-19 17:45:55,060 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-19 17:45:55,154 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29258256 kv 7314560(29258240) kvi 5109580(20438320)
2015-10-19 17:46:01,936 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:46:01,936 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29258256; bufend = 77515779; bufvoid = 104857600
2015-10-19 17:46:01,936 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7314560(29258240); kvend = 24621820(98487280); length = 8907141/6553600
2015-10-19 17:46:01,936 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 86584515 kvi 21646124(86584496)
2015-10-19 17:46:13,890 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 13 time(s); maxRetries=45
2015-10-19 17:46:33,891 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 14 time(s); maxRetries=45
2015-10-19 17:46:40,954 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-19 17:46:41,016 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 86584515 kv 21646124(86584496) kvi 19440748(77762992)
2015-10-19 17:46:53,892 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 15 time(s); maxRetries=45
2015-10-19 17:47:13,893 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 16 time(s); maxRetries=45
2015-10-19 17:47:33,894 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 17 time(s); maxRetries=45
2015-10-19 17:47:53,895 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 18 time(s); maxRetries=45
2015-10-19 17:48:13,896 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 19 time(s); maxRetries=45
2015-10-19 17:48:33,897 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 20 time(s); maxRetries=45
2015-10-19 17:48:53,898 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 21 time(s); maxRetries=45
2015-10-19 17:49:13,899 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 22 time(s); maxRetries=45
2015-10-19 17:49:33,619 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 0 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-19 17:49:53,417 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 1 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-19 17:50:12,949 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 2 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-19 17:50:32,638 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 3 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-19 17:50:52,248 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 4 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-19 17:51:11,921 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 5 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-19 17:51:31,735 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 6 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-19 17:51:51,423 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 7 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-19 17:52:11,049 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 8 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
2015-10-19 17:52:30,738 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: minint-75dgdam1.fareast.corp.microsoft.com/************:58957. Already tried 9 time(s); retry policy is RetryUpToMaximumCountWithFixedSleep(maxRetries=10, sleepTime=1000 MILLISECONDS)
