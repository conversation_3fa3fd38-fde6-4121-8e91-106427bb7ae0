2015-10-17 23:08:01,032 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445094324383_0001_000001
2015-10-17 23:08:01,868 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 23:08:01,868 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 1 cluster_timestamp: 1445094324383 } attemptId: 1 } keyId: -411019364)
2015-10-17 23:08:02,061 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 23:08:03,646 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 23:08:03,768 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 23:08:03,868 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 23:08:03,871 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 23:08:03,874 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 23:08:03,877 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 23:08:03,878 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 23:08:03,896 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 23:08:03,897 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 23:08:03,900 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 23:08:03,995 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 23:08:04,046 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 23:08:04,095 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 23:08:04,125 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 23:08:04,261 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 23:08:05,002 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 23:08:05,176 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 23:08:05,177 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 23:08:05,196 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445094324383_0001 to jobTokenSecretManager
2015-10-17 23:08:05,616 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445094324383_0001 because: not enabled; too many maps; too much input;
2015-10-17 23:08:05,668 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445094324383_0001 = 1313861632. Number of splits = 10
2015-10-17 23:08:05,672 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445094324383_0001 = 1
2015-10-17 23:08:05,672 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445094324383_0001Job Transitioned from NEW to INITED
2015-10-17 23:08:05,676 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445094324383_0001.
2015-10-17 23:08:05,769 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 23:08:05,795 INFO [Socket Reader #1 for port 18819] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 18819
2015-10-17 23:08:05,862 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 23:08:05,863 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 23:08:05,863 INFO [IPC Server listener on 18819] org.apache.hadoop.ipc.Server: IPC Server listener on 18819: starting
2015-10-17 23:08:05,866 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-41.fareast.corp.microsoft.com/**************:18819
2015-10-17 23:08:06,120 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 23:08:06,133 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 23:08:06,169 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 23:08:06,186 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 23:08:06,186 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 23:08:06,197 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 23:08:06,198 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 23:08:06,232 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 18826
2015-10-17 23:08:06,232 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 23:08:06,356 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\4\Jetty_0_0_0_0_18826_mapreduce____.4yt5p8\webapp
2015-10-17 23:08:06,852 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:18826
2015-10-17 23:08:06,853 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 18826
2015-10-17 23:08:08,030 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 23:08:08,039 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445094324383_0001
2015-10-17 23:08:08,043 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 23:08:08,050 INFO [Socket Reader #1 for port 18836] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 18836
2015-10-17 23:08:08,068 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 23:08:08,069 INFO [IPC Server listener on 18836] org.apache.hadoop.ipc.Server: IPC Server listener on 18836: starting
2015-10-17 23:08:08,131 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 23:08:08,131 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 23:08:08,131 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 23:08:08,280 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at MSRA-SA-41/**************:8030
2015-10-17 23:08:08,458 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 23:08:08,458 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 23:08:08,467 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 23:08:08,474 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 23:08:08,494 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445094324383_0001Job Transitioned from INITED to SETUP
2015-10-17 23:08:08,498 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 23:08:08,522 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445094324383_0001Job Transitioned from SETUP to RUNNING
2015-10-17 23:08:08,576 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,578 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,587 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:08,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,591 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:08,593 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,593 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,594 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:08,594 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:08,596 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,597 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,597 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:08,598 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,598 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,599 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:08,600 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,600 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,600 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:08,601 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,602 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,602 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:08,603 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,603 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,604 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:08,605 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,605 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:08,606 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:08,608 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 23:08:08,612 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:08,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:08,613 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:08,614 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:08,614 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:08,615 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:08,616 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:08,618 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:08,618 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:08,620 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:08,621 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:08:08,625 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 23:08:08,646 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 23:08:08,729 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445094324383_0001, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445094324383_0001/job_1445094324383_0001_1.jhist
2015-10-17 23:08:09,464 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 23:08:09,588 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0001: ask=6 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-24> knownNMs=4
2015-10-17 23:08:09,593 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-17 23:08:09,594 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:10,597 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-17 23:08:10,598 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:11,602 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-17 23:08:11,603 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:12,608 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-17 23:08:12,608 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:13,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:08:13,637 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:13,640 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0001_01_000002 to attempt_1445094324383_0001_m_000000_0
2015-10-17 23:08:13,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-17 23:08:13,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:13,645 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:0 RackLocal:1
2015-10-17 23:08:13,773 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:08:13,815 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445094324383_0001/job.jar
2015-10-17 23:08:13,823 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445094324383_0001/job.xml
2015-10-17 23:08:13,826 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 23:08:13,826 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 23:08:13,827 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 23:08:13,944 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:08:13,954 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0001_01_000002 taskAttempt attempt_1445094324383_0001_m_000000_0
2015-10-17 23:08:13,958 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0001_m_000000_0
2015-10-17 23:08:13,960 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-17 23:08:14,453 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0001_m_000000_0 : 13562
2015-10-17 23:08:14,458 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0001_m_000000_0] using containerId: [container_1445094324383_0001_01_000002 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:52368]
2015-10-17 23:08:14,468 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:08:14,469 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0001_m_000000
2015-10-17 23:08:14,469 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:08:14,658 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-26> knownNMs=4
2015-10-17 23:08:14,659 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-17 23:08:14,659 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:15,663 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:15,664 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:16,666 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:16,667 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:17,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:17,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:18,670 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:18,671 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:19,673 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:19,673 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:20,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:20,674 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:21,676 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:21,677 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:22,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:22,678 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:23,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:23,679 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:24,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:24,683 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:25,684 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:25,685 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:26,686 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:26,686 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:27,688 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:27,688 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:28,689 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:28,690 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:28,867 INFO [Socket Reader #1 for port 18836] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0001 (auth:SIMPLE)
2015-10-17 23:08:29,002 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0001_m_000002 asked for a task
2015-10-17 23:08:29,003 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0001_m_000002 given task: attempt_1445094324383_0001_m_000000_0
2015-10-17 23:08:29,692 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:29,693 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:30,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:30,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:31,696 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:31,696 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:32,697 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:32,697 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:33,698 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:33,699 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:34,700 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:34,701 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:35,702 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:35,703 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:36,704 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:36,705 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:37,708 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:37,708 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:38,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:38,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:39,713 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:39,713 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:40,714 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:40,714 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:41,716 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:41,716 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:42,718 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:42,718 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:43,720 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:43,720 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:44,722 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:44,723 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:45,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:45,725 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:46,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:46,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:47,728 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:47,729 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:48,730 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:48,730 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:49,733 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:49,733 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:50,736 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:50,737 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:51,738 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:51,738 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:52,739 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:52,740 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:53,741 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:53,741 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:54,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:54,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:55,744 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:55,745 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:56,746 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:56,747 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:57,748 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:57,748 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:58,750 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:58,750 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:08:59,276 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.0706704
2015-10-17 23:08:59,755 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:08:59,755 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:00,757 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:00,757 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:01,760 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:01,760 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:02,762 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:02,763 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:03,567 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.12683338
2015-10-17 23:09:03,765 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:03,765 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:04,766 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:04,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:05,768 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:05,769 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:06,770 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:06,770 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:07,304 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.13101934
2015-10-17 23:09:07,772 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:07,772 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:08,774 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:08,774 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:09,776 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:09,776 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:10,778 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:10,778 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:10,942 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.13101934
2015-10-17 23:09:11,779 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:11,779 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:12,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:12,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:13,782 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:13,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:14,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:14,784 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:14,865 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.13101934
2015-10-17 23:09:15,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:15,786 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:16,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:16,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:17,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:17,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:18,542 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.13101934
2015-10-17 23:09:18,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:18,789 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:19,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:19,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:20,791 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:20,791 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:21,792 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:21,792 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:22,299 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.13101934
2015-10-17 23:09:22,793 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:22,793 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:23,794 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:23,794 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:24,798 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:09:24,799 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0001_01_000003 to attempt_1445094324383_0001_m_000001_0
2015-10-17 23:09:24,799 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:24,799 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:24,799 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:1 RackLocal:1
2015-10-17 23:09:24,799 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:09:24,800 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:09:24,801 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0001_01_000003 taskAttempt attempt_1445094324383_0001_m_000001_0
2015-10-17 23:09:24,801 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0001_m_000001_0
2015-10-17 23:09:24,801 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:09:24,815 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0001_m_000001_0 : 13562
2015-10-17 23:09:24,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0001_m_000001_0] using containerId: [container_1445094324383_0001_01_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 23:09:24,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:09:24,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0001_m_000001
2015-10-17 23:09:24,816 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:09:25,800 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:09:25,800 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:25,800 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:26,091 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.13101934
2015-10-17 23:09:26,802 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:26,802 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:27,803 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:27,803 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:28,643 INFO [Socket Reader #1 for port 18836] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0001 (auth:SIMPLE)
2015-10-17 23:09:28,663 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0001_m_000003 asked for a task
2015-10-17 23:09:28,663 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0001_m_000003 given task: attempt_1445094324383_0001_m_000001_0
2015-10-17 23:09:28,804 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:28,804 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:29,805 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:29,805 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:30,009 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.13101934
2015-10-17 23:09:30,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:30,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:31,808 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:31,808 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:32,809 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:32,809 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:33,810 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:33,810 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:33,837 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.13101934
2015-10-17 23:09:34,811 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:34,811 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:35,812 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:35,812 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:35,892 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.13102192
2015-10-17 23:09:36,813 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:36,813 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:37,678 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.13101934
2015-10-17 23:09:37,814 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:37,814 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:38,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:09:38,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0001_01_000004 to attempt_1445094324383_0001_m_000002_0
2015-10-17 23:09:38,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:38,824 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:38,824 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:2 RackLocal:1
2015-10-17 23:09:38,824 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:09:38,826 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:09:38,828 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0001_01_000004 taskAttempt attempt_1445094324383_0001_m_000002_0
2015-10-17 23:09:38,828 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0001_m_000002_0
2015-10-17 23:09:38,828 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:09:38,853 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0001_m_000002_0 : 13562
2015-10-17 23:09:38,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0001_m_000002_0] using containerId: [container_1445094324383_0001_01_000004 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:18641]
2015-10-17 23:09:38,854 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:09:38,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0001_m_000002
2015-10-17 23:09:38,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:09:38,904 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.13102192
2015-10-17 23:09:39,832 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0001: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:09:39,832 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:09:39,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0001_01_000005 to attempt_1445094324383_0001_m_000003_0
2015-10-17 23:09:39,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:39,833 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:39,833 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:09:39,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:3 RackLocal:1
2015-10-17 23:09:39,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:09:39,837 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0001_01_000005 taskAttempt attempt_1445094324383_0001_m_000003_0
2015-10-17 23:09:39,837 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0001_m_000003_0
2015-10-17 23:09:39,837 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:09:39,862 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0001_m_000003_0 : 13562
2015-10-17 23:09:39,863 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0001_m_000003_0] using containerId: [container_1445094324383_0001_01_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:18641]
2015-10-17 23:09:39,864 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:09:39,864 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0001_m_000003
2015-10-17 23:09:39,864 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:09:40,841 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0001: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:09:40,842 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:09:40,842 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0001_01_000006 to attempt_1445094324383_0001_m_000004_0
2015-10-17 23:09:40,843 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:40,843 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:40,843 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:09:40,843 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:4 RackLocal:1
2015-10-17 23:09:40,845 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:09:40,846 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0001_01_000006 taskAttempt attempt_1445094324383_0001_m_000004_0
2015-10-17 23:09:40,847 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0001_m_000004_0
2015-10-17 23:09:40,847 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:09:40,873 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0001_m_000004_0 : 13562
2015-10-17 23:09:40,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0001_m_000004_0] using containerId: [container_1445094324383_0001_01_000006 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:18641]
2015-10-17 23:09:40,874 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:09:40,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0001_m_000004
2015-10-17 23:09:40,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:09:41,388 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.13101934
2015-10-17 23:09:41,848 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0001: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:09:41,848 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:09:41,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0001_01_000007 to attempt_1445094324383_0001_m_000005_0
2015-10-17 23:09:41,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:41,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:41,849 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:09:41,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:5 RackLocal:1
2015-10-17 23:09:41,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:09:41,850 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0001_01_000007 taskAttempt attempt_1445094324383_0001_m_000005_0
2015-10-17 23:09:41,850 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0001_m_000005_0
2015-10-17 23:09:41,851 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:09:41,874 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0001_m_000005_0 : 13562
2015-10-17 23:09:41,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0001_m_000005_0] using containerId: [container_1445094324383_0001_01_000007 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:18641]
2015-10-17 23:09:41,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:09:41,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0001_m_000005
2015-10-17 23:09:41,875 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:09:41,917 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.13102192
2015-10-17 23:09:42,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:09:42,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:42,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:43,719 INFO [Socket Reader #1 for port 18836] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0001 (auth:SIMPLE)
2015-10-17 23:09:43,763 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0001_m_000004 asked for a task
2015-10-17 23:09:43,763 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0001_m_000004 given task: attempt_1445094324383_0001_m_000002_0
2015-10-17 23:09:43,854 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:09:43,854 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0001_01_000008 to attempt_1445094324383_0001_m_000006_0
2015-10-17 23:09:43,855 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:43,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:09:43,855 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:43,855 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:6 RackLocal:1
2015-10-17 23:09:43,855 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:09:43,856 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0001_01_000008 taskAttempt attempt_1445094324383_0001_m_000006_0
2015-10-17 23:09:43,856 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0001_m_000006_0
2015-10-17 23:09:43,856 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:09:43,880 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0001_m_000006_0 : 13562
2015-10-17 23:09:43,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0001_m_000006_0] using containerId: [container_1445094324383_0001_01_000008 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:18641]
2015-10-17 23:09:43,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:09:43,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0001_m_000006
2015-10-17 23:09:43,881 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:09:44,856 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:09:44,856 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:44,856 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:44,929 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.13102192
2015-10-17 23:09:44,993 INFO [Socket Reader #1 for port 18836] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0001 (auth:SIMPLE)
2015-10-17 23:09:45,032 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0001_m_000005 asked for a task
2015-10-17 23:09:45,032 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0001_m_000005 given task: attempt_1445094324383_0001_m_000003_0
2015-10-17 23:09:45,172 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.13101934
2015-10-17 23:09:45,860 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:45,860 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:46,160 INFO [Socket Reader #1 for port 18836] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0001 (auth:SIMPLE)
2015-10-17 23:09:46,203 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0001_m_000006 asked for a task
2015-10-17 23:09:46,204 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0001_m_000006 given task: attempt_1445094324383_0001_m_000004_0
2015-10-17 23:09:46,863 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:46,863 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:47,279 INFO [Socket Reader #1 for port 18836] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0001 (auth:SIMPLE)
2015-10-17 23:09:47,325 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0001_m_000007 asked for a task
2015-10-17 23:09:47,325 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0001_m_000007 given task: attempt_1445094324383_0001_m_000005_0
2015-10-17 23:09:47,865 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:47,865 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:47,933 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.2325369
2015-10-17 23:09:48,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:48,869 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:48,873 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.16576998
2015-10-17 23:09:49,301 INFO [Socket Reader #1 for port 18836] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0001 (auth:SIMPLE)
2015-10-17 23:09:49,344 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0001_m_000008 asked for a task
2015-10-17 23:09:49,345 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0001_m_000008 given task: attempt_1445094324383_0001_m_000006_0
2015-10-17 23:09:49,871 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:49,872 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:50,874 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:50,875 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:50,952 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.23921879
2015-10-17 23:09:51,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:09:51,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0001_01_000009 to attempt_1445094324383_0001_m_000007_0
2015-10-17 23:09:51,883 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:51,883 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:51,883 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:09:51,883 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:7 RackLocal:1
2015-10-17 23:09:51,885 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:09:51,886 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0001_01_000009 taskAttempt attempt_1445094324383_0001_m_000007_0
2015-10-17 23:09:51,886 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0001_m_000007_0
2015-10-17 23:09:51,887 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:09:51,905 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0001_m_000007_0 : 13562
2015-10-17 23:09:51,906 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0001_m_000007_0] using containerId: [container_1445094324383_0001_01_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:18641]
2015-10-17 23:09:51,907 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:09:51,907 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0001_m_000007
2015-10-17 23:09:51,907 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:09:52,876 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.23136197
2015-10-17 23:09:52,890 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0001: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:09:52,890 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:09:52,891 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0001_01_000010 to attempt_1445094324383_0001_m_000008_0
2015-10-17 23:09:52,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:52,892 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:09:52,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:52,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:8 RackLocal:1
2015-10-17 23:09:52,893 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:09:52,895 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0001_01_000010 taskAttempt attempt_1445094324383_0001_m_000008_0
2015-10-17 23:09:52,895 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0001_m_000008_0
2015-10-17 23:09:52,895 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:09:52,914 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0001_m_000008_0 : 13562
2015-10-17 23:09:52,914 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0001_m_000008_0] using containerId: [container_1445094324383_0001_01_000010 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 23:09:52,915 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:09:52,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0001_m_000008
2015-10-17 23:09:52,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:09:53,234 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.131014
2015-10-17 23:09:53,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:09:53,896 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:53,896 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:53,971 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.23921879
2015-10-17 23:09:54,244 INFO [Socket Reader #1 for port 18836] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0001 (auth:SIMPLE)
2015-10-17 23:09:54,261 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0001_m_000009 asked for a task
2015-10-17 23:09:54,262 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0001_m_000009 given task: attempt_1445094324383_0001_m_000007_0
2015-10-17 23:09:54,492 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.13102706
2015-10-17 23:09:54,898 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:54,899 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:55,349 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.13104042
2015-10-17 23:09:55,901 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:55,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:56,102 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.13104132
2015-10-17 23:09:56,269 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.131014
2015-10-17 23:09:56,393 INFO [Socket Reader #1 for port 18836] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0001 (auth:SIMPLE)
2015-10-17 23:09:56,416 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0001_m_000010 asked for a task
2015-10-17 23:09:56,417 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0001_m_000010 given task: attempt_1445094324383_0001_m_000008_0
2015-10-17 23:09:56,710 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.23921585
2015-10-17 23:09:56,904 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:56,904 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:56,988 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.23921879
2015-10-17 23:09:57,275 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.13101135
2015-10-17 23:09:57,527 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.13102706
2015-10-17 23:09:57,907 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:57,907 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:58,383 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.13104042
2015-10-17 23:09:58,910 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:58,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:09:59,136 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.13104132
2015-10-17 23:09:59,301 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.131014
2015-10-17 23:09:59,913 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:09:59,913 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:10:00,001 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.23921879
2015-10-17 23:10:00,310 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.13101135
2015-10-17 23:10:00,395 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.23921585
2015-10-17 23:10:00,562 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.14134356
2015-10-17 23:10:00,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:10:00,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:10:01,418 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.22316705
2015-10-17 23:10:01,568 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.13103712
2015-10-17 23:10:01,925 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:10:01,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0001_01_000011 to attempt_1445094324383_0001_m_000009_0
2015-10-17 23:10:01,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-17 23:10:01,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:10:01,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 23:10:01,927 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:10:01,929 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:10:01,930 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0001_01_000011 taskAttempt attempt_1445094324383_0001_m_000009_0
2015-10-17 23:10:01,930 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0001_m_000009_0
2015-10-17 23:10:01,931 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:10:01,950 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0001_m_000009_0 : 13562
2015-10-17 23:10:01,950 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0001_m_000009_0] using containerId: [container_1445094324383_0001_01_000011 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 23:10:01,951 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:10:01,951 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0001_m_000009
2015-10-17 23:10:01,952 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:10:02,170 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.22706155
2015-10-17 23:10:02,331 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.23919508
2015-10-17 23:10:02,929 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0001: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-17 23:10:03,014 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.3423261
2015-10-17 23:10:03,339 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.17236523
2015-10-17 23:10:03,599 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.23922287
2015-10-17 23:10:03,970 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.13102318
2015-10-17 23:10:04,242 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.23921585
2015-10-17 23:10:04,452 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.23924798
2015-10-17 23:10:04,602 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.13103712
2015-10-17 23:10:05,202 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.23922269
2015-10-17 23:10:05,362 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.23919508
2015-10-17 23:10:05,671 INFO [Socket Reader #1 for port 18836] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0001 (auth:SIMPLE)
2015-10-17 23:10:05,689 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0001_m_000011 asked for a task
2015-10-17 23:10:05,689 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0001_m_000011 given task: attempt_1445094324383_0001_m_000009_0
2015-10-17 23:10:06,040 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.34743196
2015-10-17 23:10:06,372 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.23923388
2015-10-17 23:10:06,633 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.23922287
2015-10-17 23:10:06,997 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.13102318
2015-10-17 23:10:07,483 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.23924798
2015-10-17 23:10:07,633 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.23924637
2015-10-17 23:10:08,233 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.23922269
2015-10-17 23:10:08,291 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.23921585
2015-10-17 23:10:08,394 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.23919508
2015-10-17 23:10:09,059 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.34743196
2015-10-17 23:10:09,404 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.23923388
2015-10-17 23:10:09,665 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.2728472
2015-10-17 23:10:10,020 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.13102318
2015-10-17 23:10:10,515 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.23924798
2015-10-17 23:10:10,666 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.23924637
2015-10-17 23:10:11,272 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.3474054
2015-10-17 23:10:11,425 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.3474061
2015-10-17 23:10:11,993 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.23921585
2015-10-17 23:10:12,071 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.34743196
2015-10-17 23:10:12,436 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.31138065
2015-10-17 23:10:12,698 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.3473985
2015-10-17 23:10:13,029 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.13102318
2015-10-17 23:10:13,098 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.16604526
2015-10-17 23:10:13,549 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.34742972
2015-10-17 23:10:13,697 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.23924637
2015-10-17 23:10:14,305 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.3474054
2015-10-17 23:10:14,456 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.3474061
2015-10-17 23:10:15,086 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.34743196
2015-10-17 23:10:15,468 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.34743717
2015-10-17 23:10:15,730 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.3473985
2015-10-17 23:10:15,956 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.23921585
2015-10-17 23:10:16,047 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.16845454
2015-10-17 23:10:16,107 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.16604526
2015-10-17 23:10:16,582 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.34742972
2015-10-17 23:10:16,728 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.34743145
2015-10-17 23:10:16,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-27>
2015-10-17 23:10:16,974 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:10:17,331 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.3474054
2015-10-17 23:10:17,487 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.3474061
2015-10-17 23:10:17,976 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-17 23:10:17,976 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:10:18,103 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.44832665
2015-10-17 23:10:18,501 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.34743717
2015-10-17 23:10:18,763 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.37119377
2015-10-17 23:10:18,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-17 23:10:18,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:10:19,056 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.23921506
2015-10-17 23:10:19,118 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.16604526
2015-10-17 23:10:19,614 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.3915492
2015-10-17 23:10:19,652 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.23921585
2015-10-17 23:10:19,760 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.34743145
2015-10-17 23:10:19,981 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-33>
2015-10-17 23:10:19,982 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:10:20,365 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.4498231
2015-10-17 23:10:20,517 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.45561612
2015-10-17 23:10:21,117 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.455629
2015-10-17 23:10:21,532 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.445251
2015-10-17 23:10:21,794 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.45559394
2015-10-17 23:10:21,987 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 23:10:21,987 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 23:10:22,078 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.23921506
2015-10-17 23:10:22,118 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.24577735
2015-10-17 23:10:22,647 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.45565325
2015-10-17 23:10:22,791 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.34743145
2015-10-17 23:10:23,397 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.45560944
2015-10-17 23:10:23,481 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.23921585
2015-10-17 23:10:23,549 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.45561612
2015-10-17 23:10:24,119 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.455629
2015-10-17 23:10:24,563 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.455643
2015-10-17 23:10:24,840 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.45559394
2015-10-17 23:10:25,087 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.23921506
2015-10-17 23:10:25,120 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.3031575
2015-10-17 23:10:25,677 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.45565325
2015-10-17 23:10:25,821 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.4556257
2015-10-17 23:10:26,428 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.45560944
2015-10-17 23:10:26,579 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.45561612
2015-10-17 23:10:27,141 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.455629
2015-10-17 23:10:27,148 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.23921585
2015-10-17 23:10:27,601 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.455643
2015-10-17 23:10:27,875 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.4781495
2015-10-17 23:10:28,088 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.3182659
2015-10-17 23:10:28,134 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.3031575
2015-10-17 23:10:28,722 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.47363076
2015-10-17 23:10:28,863 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.4556257
2015-10-17 23:10:29,470 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.49374956
2015-10-17 23:10:29,620 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.56381613
2015-10-17 23:10:30,150 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.54546815
2015-10-17 23:10:30,641 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.455643
2015-10-17 23:10:30,748 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.23921585
2015-10-17 23:10:30,908 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.5637838
2015-10-17 23:10:31,103 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.3474145
2015-10-17 23:10:31,155 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.3031575
2015-10-17 23:10:31,756 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.5638328
2015-10-17 23:10:31,895 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.4556257
2015-10-17 23:10:32,503 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.56381226
2015-10-17 23:10:32,652 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.56381613
2015-10-17 23:10:33,167 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.5638294
2015-10-17 23:10:33,663 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.5638263
2015-10-17 23:10:33,939 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.5637838
2015-10-17 23:10:34,119 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.3474145
2015-10-17 23:10:34,172 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.4402952
2015-10-17 23:10:34,686 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.24612412
2015-10-17 23:10:34,787 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.5638328
2015-10-17 23:10:34,925 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.4556257
2015-10-17 23:10:35,534 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.56381226
2015-10-17 23:10:35,683 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.56381613
2015-10-17 23:10:36,181 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.5638294
2015-10-17 23:10:36,695 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.5638263
2015-10-17 23:10:36,970 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.5637838
2015-10-17 23:10:37,119 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.3474145
2015-10-17 23:10:37,182 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.4402952
2015-10-17 23:10:37,818 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.5638328
2015-10-17 23:10:37,954 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.56380385
2015-10-17 23:10:38,260 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.3002792
2015-10-17 23:10:38,567 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.56381226
2015-10-17 23:10:38,714 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.64981246
2015-10-17 23:10:39,047 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.64981246
2015-10-17 23:10:39,182 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.5638294
2015-10-17 23:10:39,267 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.5637838
2015-10-17 23:10:39,730 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.60562634
2015-10-17 23:10:40,002 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.667
2015-10-17 23:10:40,021 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.56381226
2015-10-17 23:10:40,076 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.5638328
2015-10-17 23:10:40,134 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.45562187
2015-10-17 23:10:40,203 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.4402952
2015-10-17 23:10:40,490 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.60562634
2015-10-17 23:10:40,849 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.667
2015-10-17 23:10:40,984 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.56380385
2015-10-17 23:10:41,598 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.667
2015-10-17 23:10:41,744 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.667
2015-10-17 23:10:42,165 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.3474062
2015-10-17 23:10:42,204 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.5822521
2015-10-17 23:10:42,762 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.667
2015-10-17 23:10:43,032 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.667
2015-10-17 23:10:43,136 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.45562187
2015-10-17 23:10:43,211 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.5488722
2015-10-17 23:10:43,880 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.667
2015-10-17 23:10:44,015 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.5764975
2015-10-17 23:10:44,282 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.5822521
2015-10-17 23:10:44,629 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.667
2015-10-17 23:10:44,775 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.667
2015-10-17 23:10:45,079 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.5764975
2015-10-17 23:10:45,212 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.667
2015-10-17 23:10:45,792 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.3474062
2015-10-17 23:10:45,793 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.667
2015-10-17 23:10:46,063 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.667
2015-10-17 23:10:46,157 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.45562187
2015-10-17 23:10:46,211 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.5773621
2015-10-17 23:10:46,914 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.667
2015-10-17 23:10:47,046 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.667
2015-10-17 23:10:47,651 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.667
2015-10-17 23:10:47,798 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.6672151
2015-10-17 23:10:48,212 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.667
2015-10-17 23:10:48,818 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.667
2015-10-17 23:10:49,085 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.6771877
2015-10-17 23:10:49,166 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.5203565
2015-10-17 23:10:49,214 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.5773621
2015-10-17 23:10:49,634 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.3474062
2015-10-17 23:10:49,934 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.6770916
2015-10-17 23:10:50,070 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.667
2015-10-17 23:10:50,680 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.682681
2015-10-17 23:10:50,826 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.69667596
2015-10-17 23:10:51,214 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.667
2015-10-17 23:10:51,861 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.68423414
2015-10-17 23:10:52,125 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.7001851
2015-10-17 23:10:52,168 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.56384325
2015-10-17 23:10:52,227 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.59344625
2015-10-17 23:10:52,935 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.59344625
2015-10-17 23:10:52,970 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.69510317
2015-10-17 23:10:53,105 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.667
2015-10-17 23:10:53,309 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.3474062
2015-10-17 23:10:53,711 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.70239747
2015-10-17 23:10:53,858 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.71799546
2015-10-17 23:10:54,212 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.667
2015-10-17 23:10:54,893 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.714133
2015-10-17 23:10:55,156 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.7319962
2015-10-17 23:10:55,182 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.56384325
2015-10-17 23:10:55,235 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.667
2015-10-17 23:10:55,999 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.7329324
2015-10-17 23:10:56,134 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.67369574
2015-10-17 23:10:56,743 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.74136615
2015-10-17 23:10:56,807 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.3474062
2015-10-17 23:10:56,885 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.7582804
2015-10-17 23:10:57,213 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.6782231
2015-10-17 23:10:57,926 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.75445235
2015-10-17 23:10:58,186 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.77198774
2015-10-17 23:10:58,187 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.56384325
2015-10-17 23:10:58,243 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.667
2015-10-17 23:10:59,029 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.77307135
2015-10-17 23:10:59,171 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.71342194
2015-10-17 23:10:59,774 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.78069043
2015-10-17 23:10:59,915 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.79883695
2015-10-17 23:11:00,132 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.56384325
2015-10-17 23:11:00,220 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.7086346
2015-10-17 23:11:00,601 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.3474062
2015-10-17 23:11:00,958 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.7950586
2015-10-17 23:11:01,198 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.667
2015-10-17 23:11:01,217 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.81229806
2015-10-17 23:11:01,245 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.6904282
2015-10-17 23:11:02,061 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.81119627
2015-10-17 23:11:02,203 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.7532294
2015-10-17 23:11:02,806 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.81991744
2015-10-17 23:11:02,946 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.83926004
2015-10-17 23:11:03,231 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.7488028
2015-10-17 23:11:03,989 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.83545256
2015-10-17 23:11:04,215 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.667
2015-10-17 23:11:04,249 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.85251915
2015-10-17 23:11:04,260 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.7562838
2015-10-17 23:11:04,448 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.3474062
2015-10-17 23:11:05,094 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.8491329
2015-10-17 23:11:05,233 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.79310226
2015-10-17 23:11:05,836 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.858935
2015-10-17 23:11:05,978 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.87963444
2015-10-17 23:11:06,252 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.78121185
2015-10-17 23:11:07,020 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.8758634
2015-10-17 23:11:07,230 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.667
2015-10-17 23:11:07,263 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.8205765
2015-10-17 23:11:07,281 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.8925713
2015-10-17 23:11:08,126 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.88698655
2015-10-17 23:11:08,263 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.832716
2015-10-17 23:11:08,327 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.3474062
2015-10-17 23:11:08,865 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.89804375
2015-10-17 23:11:09,008 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.91999674
2015-10-17 23:11:09,263 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.82533586
2015-10-17 23:11:10,051 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.91617227
2015-10-17 23:11:10,245 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.667
2015-10-17 23:11:10,274 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.8779379
2015-10-17 23:11:10,312 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.93269503
2015-10-17 23:11:11,156 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.92493916
2015-10-17 23:11:11,293 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.8722594
2015-10-17 23:11:11,893 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.9371171
2015-10-17 23:11:11,917 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.3474062
2015-10-17 23:11:12,038 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.9604865
2015-10-17 23:11:12,275 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.879456
2015-10-17 23:11:13,081 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.9566553
2015-10-17 23:11:13,244 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.7123383
2015-10-17 23:11:13,274 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.93168366
2015-10-17 23:11:13,343 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 0.9726683
2015-10-17 23:11:14,190 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.95859027
2015-10-17 23:11:14,326 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.9068959
2015-10-17 23:11:14,923 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.97102475
2015-10-17 23:11:15,067 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 0.9956122
2015-10-17 23:11:15,275 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.93378484
2015-10-17 23:11:15,590 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000002_0 is : 1.0
2015-10-17 23:11:15,593 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0001_m_000002_0
2015-10-17 23:11:15,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:11:15,596 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0001_01_000004 taskAttempt attempt_1445094324383_0001_m_000002_0
2015-10-17 23:11:15,596 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0001_m_000002_0
2015-10-17 23:11:15,598 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:11:15,632 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:11:15,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0001_m_000002_0
2015-10-17 23:11:15,648 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:11:15,653 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 23:11:15,808 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.3474062
2015-10-17 23:11:16,096 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 23:11:16,099 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 23:11:16,099 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 23:11:16,099 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-17 23:11:16,100 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 23:11:16,117 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 0.9872441
2015-10-17 23:11:16,243 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445094324383_0001_m_000000
2015-10-17 23:11:16,243 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 23:11:16,243 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445094324383_0001_m_000000
2015-10-17 23:11:16,243 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.7691156
2015-10-17 23:11:16,245 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:11:16,245 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:11:16,246 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 23:11:16,274 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 0.998189
2015-10-17 23:11:16,294 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000003_0 is : 1.0
2015-10-17 23:11:16,297 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0001_m_000003_0
2015-10-17 23:11:16,297 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:11:16,298 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0001_01_000005 taskAttempt attempt_1445094324383_0001_m_000003_0
2015-10-17 23:11:16,298 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0001_m_000003_0
2015-10-17 23:11:16,299 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:11:16,317 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:11:16,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0001_m_000003_0
2015-10-17 23:11:16,318 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:11:16,319 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 23:11:16,426 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000009_0 is : 1.0
2015-10-17 23:11:16,429 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0001_m_000009_0
2015-10-17 23:11:16,429 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:11:16,430 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0001_01_000011 taskAttempt attempt_1445094324383_0001_m_000009_0
2015-10-17 23:11:16,430 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0001_m_000009_0
2015-10-17 23:11:16,431 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:11:16,489 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:11:16,489 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0001_m_000009_0
2015-10-17 23:11:16,490 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:11:16,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 23:11:17,100 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 23:11:17,112 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0001: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 23:11:17,112 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0001_01_000004
2015-10-17 23:11:17,114 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:3 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 23:11:17,114 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0001_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:11:17,222 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 0.9866287
2015-10-17 23:11:17,359 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.936332
2015-10-17 23:11:17,899 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000006_0 is : 1.0
2015-10-17 23:11:17,901 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0001_m_000006_0
2015-10-17 23:11:17,902 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:11:17,902 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0001_01_000008 taskAttempt attempt_1445094324383_0001_m_000006_0
2015-10-17 23:11:17,903 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0001_m_000006_0
2015-10-17 23:11:17,903 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:11:17,919 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:11:17,919 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0001_m_000006_0
2015-10-17 23:11:17,920 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:11:17,921 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 23:11:17,955 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 0.99757373
2015-10-17 23:11:18,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:4 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 23:11:18,121 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0001_01_000005
2015-10-17 23:11:18,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0001_01_000011
2015-10-17 23:11:18,122 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0001_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:11:18,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:11:18,122 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0001_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:11:18,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 23:11:18,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0001_01_000012 to attempt_1445094324383_0001_r_000000_0
2015-10-17 23:11:18,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 23:11:18,142 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:11:18,143 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:11:18,144 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0001_01_000012 taskAttempt attempt_1445094324383_0001_r_000000_0
2015-10-17 23:11:18,144 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0001_r_000000_0
2015-10-17 23:11:18,144 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:11:18,162 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0001_r_000000_0 : 13562
2015-10-17 23:11:18,162 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0001_r_000000_0] using containerId: [container_1445094324383_0001_01_000012 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:18641]
2015-10-17 23:11:18,163 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:11:18,163 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0001_r_000000
2015-10-17 23:11:18,164 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 23:11:18,281 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 0.9727657
2015-10-17 23:11:18,325 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000005_0 is : 1.0
2015-10-17 23:11:18,328 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0001_m_000005_0
2015-10-17 23:11:18,329 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:11:18,329 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0001_01_000007 taskAttempt attempt_1445094324383_0001_m_000005_0
2015-10-17 23:11:18,329 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0001_m_000005_0
2015-10-17 23:11:18,329 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:11:18,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:11:18,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0001_m_000005_0
2015-10-17 23:11:18,349 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:11:18,349 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 23:11:18,801 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000004_0 is : 1.0
2015-10-17 23:11:18,803 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0001_m_000004_0
2015-10-17 23:11:18,804 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:11:18,804 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0001_01_000006 taskAttempt attempt_1445094324383_0001_m_000004_0
2015-10-17 23:11:18,805 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0001_m_000004_0
2015-10-17 23:11:18,805 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:11:18,820 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:11:18,821 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0001_m_000004_0
2015-10-17 23:11:18,821 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:11:18,821 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 23:11:19,123 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:9 RackLocal:1
2015-10-17 23:11:19,130 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0001: ask=1 release= 0 newContainers=1 finishedContainers=2 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 23:11:19,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0001_01_000008
2015-10-17 23:11:19,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0001_01_000007
2015-10-17 23:11:19,131 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0001_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:11:19,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 23:11:19,132 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0001_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:11:19,132 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445094324383_0001_01_000013 to attempt_1445094324383_0001_m_000000_1
2015-10-17 23:11:19,132 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 23:11:19,133 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 23:11:19,134 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 23:11:19,135 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445094324383_0001_01_000013 taskAttempt attempt_1445094324383_0001_m_000000_1
2015-10-17 23:11:19,135 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445094324383_0001_m_000000_1
2015-10-17 23:11:19,135 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:11:19,178 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445094324383_0001_m_000000_1 : 13562
2015-10-17 23:11:19,179 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445094324383_0001_m_000000_1] using containerId: [container_1445094324383_0001_01_000013 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:18641]
2015-10-17 23:11:19,179 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 23:11:19,180 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445094324383_0001_m_000000
2015-10-17 23:11:19,251 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.8147234
2015-10-17 23:11:19,683 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.3474062
2015-10-17 23:11:20,137 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445094324383_0001: ask=4 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 23:11:20,137 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0001_01_000006
2015-10-17 23:11:20,138 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 23:11:20,138 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0001_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:11:20,416 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.9585469
2015-10-17 23:11:20,902 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000001_0 is : 1.0
2015-10-17 23:11:20,905 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0001_m_000001_0
2015-10-17 23:11:20,906 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:11:20,906 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0001_01_000003 taskAttempt attempt_1445094324383_0001_m_000001_0
2015-10-17 23:11:20,907 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0001_m_000001_0
2015-10-17 23:11:20,907 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:11:20,923 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:11:20,924 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0001_m_000001_0
2015-10-17 23:11:20,924 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:11:20,925 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 23:11:21,138 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 23:11:22,144 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0001_01_000003
2015-10-17 23:11:22,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 23:11:22,145 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0001_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:11:22,266 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.8685614
2015-10-17 23:11:22,685 INFO [Socket Reader #1 for port 18836] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0001 (auth:SIMPLE)
2015-10-17 23:11:22,726 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0001_r_000012 asked for a task
2015-10-17 23:11:22,726 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0001_r_000012 given task: attempt_1445094324383_0001_r_000000_0
2015-10-17 23:11:23,277 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.3794189
2015-10-17 23:11:23,460 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.97077787
2015-10-17 23:11:24,695 INFO [Socket Reader #1 for port 18836] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445094324383_0001 (auth:SIMPLE)
2015-10-17 23:11:24,728 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445094324383_0001_m_000013 asked for a task
2015-10-17 23:11:24,728 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445094324383_0001_m_000013 given task: attempt_1445094324383_0001_m_000000_1
2015-10-17 23:11:25,288 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.9152861
2015-10-17 23:11:26,409 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 23:11:26,542 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.9833408
2015-10-17 23:11:26,857 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.4160021
2015-10-17 23:11:27,436 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 23:11:28,298 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 0.9720296
2015-10-17 23:11:28,439 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 23:11:29,441 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 23:11:29,583 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 0.99713707
2015-10-17 23:11:30,433 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.43576014
2015-10-17 23:11:30,443 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 23:11:30,484 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000007_0 is : 1.0
2015-10-17 23:11:30,487 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0001_m_000007_0
2015-10-17 23:11:30,488 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:11:30,488 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0001_01_000009 taskAttempt attempt_1445094324383_0001_m_000007_0
2015-10-17 23:11:30,489 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0001_m_000007_0
2015-10-17 23:11:30,489 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:11:30,512 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:11:30,513 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0001_m_000007_0
2015-10-17 23:11:30,513 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:11:30,514 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 23:11:30,734 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000008_0 is : 1.0
2015-10-17 23:11:30,737 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0001_m_000008_0
2015-10-17 23:11:30,738 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:11:30,738 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0001_01_000010 taskAttempt attempt_1445094324383_0001_m_000008_0
2015-10-17 23:11:30,739 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0001_m_000008_0
2015-10-17 23:11:30,739 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 23:11:30,759 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:11:30,759 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0001_m_000008_0
2015-10-17 23:11:30,759 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:11:30,760 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 23:11:31,162 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 23:11:31,445 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 23:11:32,171 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0001_01_000009
2015-10-17 23:11:32,171 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0001_01_000010
2015-10-17 23:11:32,171 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0001_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:11:32,171 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 23:11:32,172 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0001_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:11:32,264 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.0
2015-10-17 23:11:32,449 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:33,452 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:34,036 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.13101934
2015-10-17 23:11:34,182 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.451396
2015-10-17 23:11:34,454 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:35,297 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.06666667
2015-10-17 23:11:35,457 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:36,459 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:37,058 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.13101934
2015-10-17 23:11:37,461 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:37,954 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.45563135
2015-10-17 23:11:38,334 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.10000001
2015-10-17 23:11:38,463 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:39,465 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:40,090 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.13101934
2015-10-17 23:11:40,467 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:41,365 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.16666667
2015-10-17 23:11:41,420 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.45563135
2015-10-17 23:11:41,470 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:42,473 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:43,125 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.13450515
2015-10-17 23:11:43,475 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:44,402 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.20000002
2015-10-17 23:11:44,477 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:44,761 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.45563135
2015-10-17 23:11:45,479 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:46,159 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.23921585
2015-10-17 23:11:46,481 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:47,433 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.20000002
2015-10-17 23:11:47,483 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:48,392 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.45563135
2015-10-17 23:11:48,485 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:49,181 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.23921585
2015-10-17 23:11:49,485 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:50,453 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.23333333
2015-10-17 23:11:50,486 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:51,488 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:52,184 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.45563135
2015-10-17 23:11:52,211 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.23921585
2015-10-17 23:11:52,491 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:53,493 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:53,495 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.26666668
2015-10-17 23:11:54,496 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:55,247 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.33305368
2015-10-17 23:11:55,500 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:55,685 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.45563135
2015-10-17 23:11:56,502 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:56,525 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.26666668
2015-10-17 23:11:57,504 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:58,265 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.3474062
2015-10-17 23:11:58,505 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:59,209 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.45563135
2015-10-17 23:11:59,506 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:11:59,543 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:00,508 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:01,291 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.3474062
2015-10-17 23:12:01,510 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:02,511 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:02,569 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:02,865 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.45563135
2015-10-17 23:12:03,514 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:04,323 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.41268286
2015-10-17 23:12:04,515 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:05,517 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:05,603 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:06,499 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.4605134
2015-10-17 23:12:06,519 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:07,351 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.45563135
2015-10-17 23:12:07,520 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:08,521 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:08,633 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:09,523 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:09,982 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.4715868
2015-10-17 23:12:10,382 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.45563135
2015-10-17 23:12:10,525 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:11,527 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:11,663 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:12,528 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:13,410 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.48309842
2015-10-17 23:12:13,529 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:13,825 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.49113047
2015-10-17 23:12:14,530 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:14,693 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:15,531 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:16,442 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.56380075
2015-10-17 23:12:16,534 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:17,424 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.50936747
2015-10-17 23:12:17,535 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:17,724 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:18,537 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:19,471 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.56380075
2015-10-17 23:12:19,540 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:20,541 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:20,756 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:20,983 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.53607506
2015-10-17 23:12:21,542 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:22,501 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.6159394
2015-10-17 23:12:22,543 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:23,133 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.6159394
2015-10-17 23:12:23,544 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:23,785 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:24,545 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:25,532 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.667
2015-10-17 23:12:25,546 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:25,746 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.5497528
2015-10-17 23:12:26,547 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:26,815 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:27,549 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:28,550 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:28,562 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.667
2015-10-17 23:12:29,320 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.55398756
2015-10-17 23:12:29,552 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:29,846 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:30,553 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:31,554 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:31,592 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.667
2015-10-17 23:12:32,555 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:32,876 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:32,899 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.56380075
2015-10-17 23:12:33,556 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:34,558 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:34,623 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.70042074
2015-10-17 23:12:35,559 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:35,907 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:36,561 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:36,747 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.56380075
2015-10-17 23:12:37,563 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:37,653 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.74118304
2015-10-17 23:12:38,565 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:38,936 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:39,567 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:40,569 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:40,684 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.7821447
2015-10-17 23:12:40,784 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.56380075
2015-10-17 23:12:41,571 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:41,964 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:42,573 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:43,575 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:43,714 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.82319903
2015-10-17 23:12:44,544 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.56380075
2015-10-17 23:12:44,576 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:44,993 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:45,577 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:46,578 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:46,744 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.864172
2015-10-17 23:12:47,579 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:48,023 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:48,373 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.56380075
2015-10-17 23:12:48,581 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:49,583 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:49,775 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.9051227
2015-10-17 23:12:50,584 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:51,056 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:51,586 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:51,993 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.56380075
2015-10-17 23:12:52,588 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:52,812 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.93844116
2015-10-17 23:12:53,590 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:54,094 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:54,592 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:55,578 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.56380075
2015-10-17 23:12:55,594 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:55,847 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.9579208
2015-10-17 23:12:56,595 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:57,127 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:12:57,597 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:58,599 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:12:58,884 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 0.9854432
2015-10-17 23:12:59,180 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.56380075
2015-10-17 23:12:59,601 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:13:00,159 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:13:00,603 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:13:01,605 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:13:01,898 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_1 is : 1.0
2015-10-17 23:13:01,899 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0001_m_000000_1
2015-10-17 23:13:01,900 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000000_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:13:01,900 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0001_01_000013 taskAttempt attempt_1445094324383_0001_m_000000_1
2015-10-17 23:13:01,900 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0001_m_000000_1
2015-10-17 23:13:01,901 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:13:01,915 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000000_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:13:01,915 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0001_m_000000_1
2015-10-17 23:13:01,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445094324383_0001_m_000000_0
2015-10-17 23:13:01,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:13:01,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 23:13:01,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000000_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 23:13:01,916 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0001_01_000002 taskAttempt attempt_1445094324383_0001_m_000000_0
2015-10-17 23:13:01,917 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0001_m_000000_0
2015-10-17 23:13:01,917 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:52368
2015-10-17 23:13:02,356 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 23:13:02,386 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445094324383_0001_m_000000
2015-10-17 23:13:02,386 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 23:13:02,606 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 23:13:02,656 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_m_000000_0 is : 0.5774359
2015-10-17 23:13:02,667 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000000_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 23:13:02,667 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 23:13:02,676 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/out/out4/_temporary/1/_temporary/attempt_1445094324383_0001_m_000000_0
2015-10-17 23:13:02,677 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_m_000000_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 23:13:03,207 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:13:03,361 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0001_01_000013
2015-10-17 23:13:03,361 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 23:13:03,361 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0001_m_000000_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:13:03,378 INFO [Socket Reader #1 for port 18836] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 18836: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 23:13:03,609 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:04,364 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445094324383_0001_01_000002
2015-10-17 23:13:04,364 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 23:13:04,364 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445094324383_0001_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 23:13:04,611 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:05,614 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:06,229 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:13:06,616 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445094324383_0001_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 23:13:07,589 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:13:07,614 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.3
2015-10-17 23:13:09,260 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.66796124
2015-10-17 23:13:12,280 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6685761
2015-10-17 23:13:15,311 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6698377
2015-10-17 23:13:18,330 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.67127293
2015-10-17 23:13:21,351 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6722672
2015-10-17 23:13:24,369 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.67375904
2015-10-17 23:13:27,400 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6769237
2015-10-17 23:13:30,427 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.67958575
2015-10-17 23:13:33,458 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6807213
2015-10-17 23:13:36,477 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.68130594
2015-10-17 23:13:39,496 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.68214124
2015-10-17 23:13:42,523 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.68253565
2015-10-17 23:13:45,554 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6830721
2015-10-17 23:13:48,585 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.68371904
2015-10-17 23:13:51,617 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.68470526
2015-10-17 23:13:54,642 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6858315
2015-10-17 23:13:57,672 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6867306
2015-10-17 23:14:00,702 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6880707
2015-10-17 23:14:03,734 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6894119
2015-10-17 23:14:06,755 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.69065714
2015-10-17 23:14:09,788 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6919671
2015-10-17 23:14:12,818 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6924252
2015-10-17 23:14:15,848 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6928356
2015-10-17 23:14:18,877 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.69334
2015-10-17 23:14:21,908 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.69373435
2015-10-17 23:14:24,936 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.694065
2015-10-17 23:14:27,959 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.694207
2015-10-17 23:14:30,988 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.69449115
2015-10-17 23:14:34,018 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6950438
2015-10-17 23:14:37,048 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6957694
2015-10-17 23:14:40,078 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.69662094
2015-10-17 23:14:43,107 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6973939
2015-10-17 23:14:46,136 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6980883
2015-10-17 23:14:49,164 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.6990509
2015-10-17 23:14:52,195 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7011656
2015-10-17 23:14:55,225 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7032171
2015-10-17 23:14:58,256 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7055045
2015-10-17 23:15:01,287 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7070025
2015-10-17 23:15:04,319 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.70888007
2015-10-17 23:15:07,351 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7115556
2015-10-17 23:15:10,370 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7131369
2015-10-17 23:15:13,388 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.71644694
2015-10-17 23:15:16,419 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7178391
2015-10-17 23:15:19,450 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.72107273
2015-10-17 23:15:22,482 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7229081
2015-10-17 23:15:25,513 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7241158
2015-10-17 23:15:28,545 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7263552
2015-10-17 23:15:31,576 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.72826326
2015-10-17 23:15:34,607 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.731445
2015-10-17 23:15:37,638 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7354852
2015-10-17 23:15:40,670 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.73987424
2015-10-17 23:15:43,701 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7442744
2015-10-17 23:15:46,733 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.74870616
2015-10-17 23:15:49,764 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.75312877
2015-10-17 23:15:52,797 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7575677
2015-10-17 23:15:55,831 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.76200974
2015-10-17 23:15:58,862 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.76603544
2015-10-17 23:16:01,892 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.76726556
2015-10-17 23:16:04,924 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.76822144
2015-10-17 23:16:07,955 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.76939076
2015-10-17 23:16:10,989 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7706349
2015-10-17 23:16:14,020 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7719283
2015-10-17 23:16:17,039 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.77291393
2015-10-17 23:16:20,069 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.773819
2015-10-17 23:16:23,101 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7746575
2015-10-17 23:16:26,133 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7752879
2015-10-17 23:16:29,163 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.77594995
2015-10-17 23:16:32,195 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.77667487
2015-10-17 23:16:35,226 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7773207
2015-10-17 23:16:38,257 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.77771527
2015-10-17 23:16:41,299 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7783006
2015-10-17 23:16:44,331 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.77888274
2015-10-17 23:16:47,362 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7794491
2015-10-17 23:16:50,394 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.77994084
2015-10-17 23:16:53,425 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.78087103
2015-10-17 23:16:56,457 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.78153354
2015-10-17 23:16:59,488 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7821801
2015-10-17 23:17:02,515 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.78281033
2015-10-17 23:17:05,543 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.78345764
2015-10-17 23:17:08,570 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.78430945
2015-10-17 23:17:11,598 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7851291
2015-10-17 23:17:14,625 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7857918
2015-10-17 23:17:17,652 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.78643966
2015-10-17 23:17:20,671 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.787481
2015-10-17 23:17:23,691 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7880957
2015-10-17 23:17:26,719 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7889678
2015-10-17 23:17:29,746 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.79011357
2015-10-17 23:17:32,773 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7908077
2015-10-17 23:17:35,800 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.791566
2015-10-17 23:17:38,834 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7921738
2015-10-17 23:17:41,869 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7929744
2015-10-17 23:17:44,897 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7937735
2015-10-17 23:17:47,924 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.79429376
2015-10-17 23:17:50,951 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.795176
2015-10-17 23:17:53,979 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.7962675
2015-10-17 23:17:57,007 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.797135
2015-10-17 23:18:00,034 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.79806566
2015-10-17 23:18:03,062 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.8012482
2015-10-17 23:18:06,089 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.80569184
2015-10-17 23:18:09,115 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.8096942
2015-10-17 23:18:12,143 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.8140179
2015-10-17 23:18:15,170 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.8180301
2015-10-17 23:18:18,197 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.82243806
2015-10-17 23:18:21,224 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.8268293
2015-10-17 23:18:24,252 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.83044183
2015-10-17 23:18:27,293 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.8336397
2015-10-17 23:18:30,335 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.83569163
2015-10-17 23:18:33,362 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.8390112
2015-10-17 23:18:36,410 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.84341323
2015-10-17 23:18:39,437 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.84776413
2015-10-17 23:18:42,465 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.8521694
2015-10-17 23:18:45,492 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.855839
2015-10-17 23:18:48,519 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.8597831
2015-10-17 23:18:51,546 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.8636056
2015-10-17 23:18:54,573 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.8672334
2015-10-17 23:18:57,601 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.8716736
2015-10-17 23:19:00,628 INFO [IPC Server handler 24 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.87608916
2015-10-17 23:19:03,656 INFO [IPC Server handler 2 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.88011706
2015-10-17 23:19:06,683 INFO [IPC Server handler 21 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.88454694
2015-10-17 23:19:09,710 INFO [IPC Server handler 25 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.88897896
2015-10-17 23:19:12,737 INFO [IPC Server handler 8 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.8935061
2015-10-17 23:19:15,764 INFO [IPC Server handler 15 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.89801013
2015-10-17 23:19:18,791 INFO [IPC Server handler 28 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.90248436
2015-10-17 23:19:21,818 INFO [IPC Server handler 10 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.90690017
2015-10-17 23:19:24,845 INFO [IPC Server handler 0 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.91131973
2015-10-17 23:19:27,872 INFO [IPC Server handler 12 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.91573286
2015-10-17 23:19:30,900 INFO [IPC Server handler 19 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.9201495
2015-10-17 23:19:33,918 INFO [IPC Server handler 1 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.9247246
2015-10-17 23:19:36,956 INFO [IPC Server handler 3 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.9283684
2015-10-17 23:19:39,983 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.93247354
2015-10-17 23:19:43,010 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.9368791
2015-10-17 23:19:46,038 INFO [IPC Server handler 29 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.94090116
2015-10-17 23:19:49,065 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.9441105
2015-10-17 23:19:52,092 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.9481446
2015-10-17 23:19:55,119 INFO [IPC Server handler 23 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.95170885
2015-10-17 23:19:58,146 INFO [IPC Server handler 18 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.95487654
2015-10-17 23:20:01,174 INFO [IPC Server handler 20 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.9574082
2015-10-17 23:20:04,202 INFO [IPC Server handler 13 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.9615436
2015-10-17 23:20:07,229 INFO [IPC Server handler 22 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.96594614
2015-10-17 23:20:10,256 INFO [IPC Server handler 6 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.9703545
2015-10-17 23:20:13,282 INFO [IPC Server handler 4 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.97476244
2015-10-17 23:20:16,310 INFO [IPC Server handler 9 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.9792036
2015-10-17 23:20:19,336 INFO [IPC Server handler 5 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.98361325
2015-10-17 23:20:22,362 INFO [IPC Server handler 14 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.9880266
2015-10-17 23:20:25,388 INFO [IPC Server handler 27 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.9924342
2015-10-17 23:20:28,415 INFO [IPC Server handler 26 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 0.99682975
2015-10-17 23:20:30,992 INFO [IPC Server handler 16 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445094324383_0001_r_000000_0
2015-10-17 23:20:30,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-17 23:20:30,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445094324383_0001_r_000000_0 given a go for committing the task output.
2015-10-17 23:20:30,993 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445094324383_0001_r_000000_0
2015-10-17 23:20:30,994 INFO [IPC Server handler 11 on 18836] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445094324383_0001_r_000000_0:true
2015-10-17 23:20:31,049 INFO [IPC Server handler 7 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445094324383_0001_r_000000_0 is : 1.0
2015-10-17 23:20:31,050 INFO [IPC Server handler 17 on 18836] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445094324383_0001_r_000000_0
2015-10-17 23:20:31,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 23:20:31,052 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445094324383_0001_01_000012 taskAttempt attempt_1445094324383_0001_r_000000_0
2015-10-17 23:20:31,052 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445094324383_0001_r_000000_0
2015-10-17 23:20:31,053 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:18641
2015-10-17 23:20:31,072 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445094324383_0001_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 23:20:31,073 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445094324383_0001_r_000000_0
2015-10-17 23:20:31,073 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445094324383_0001_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 23:20:31,073 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 23:20:31,075 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445094324383_0001Job Transitioned from RUNNING to COMMITTING
2015-10-17 23:20:31,076 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-17 23:20:31,167 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-17 23:20:31,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445094324383_0001Job Transitioned from COMMITTING to SUCCEEDED
2015-10-17 23:20:31,171 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-17 23:20:31,172 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-17 23:20:31,172 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-17 23:20:31,172 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-17 23:20:31,172 INFO [Thread-112] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-17 23:20:31,172 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-17 23:20:31,174 INFO [Thread-112] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-17 23:20:31,238 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 23:20:31,547 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445094324383_0001/job_1445094324383_0001_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0001-1445094472971-msrabi-word+count-1445095231162-10-1-SUCCEEDED-default-1445094488479.jhist_tmp
2015-10-17 23:20:31,597 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0001-1445094472971-msrabi-word+count-1445095231162-10-1-SUCCEEDED-default-1445094488479.jhist_tmp
2015-10-17 23:20:31,600 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445094324383_0001/job_1445094324383_0001_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0001_conf.xml_tmp
2015-10-17 23:20:32,118 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0001_conf.xml_tmp
2015-10-17 23:20:32,122 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0001.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0001.summary
2015-10-17 23:20:32,124 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0001_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0001_conf.xml
2015-10-17 23:20:32,126 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0001-1445094472971-msrabi-word+count-1445095231162-10-1-SUCCEEDED-default-1445094488479.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445094324383_0001-1445094472971-msrabi-word+count-1445095231162-10-1-SUCCEEDED-default-1445094488479.jhist
2015-10-17 23:20:32,126 INFO [Thread-112] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-17 23:20:32,130 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-17 23:20:32,131 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-41.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445094324383_0001
2015-10-17 23:20:32,145 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-17 23:20:33,148 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:12 ContRel:0 HostLocal:10 RackLocal:1
2015-10-17 23:20:33,150 INFO [Thread-112] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445094324383_0001
2015-10-17 23:20:33,158 INFO [Thread-112] org.apache.hadoop.ipc.Server: Stopping server on 18836
2015-10-17 23:20:33,161 INFO [IPC Server listener on 18836] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 18836
2015-10-17 23:20:33,163 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-17 23:20:33,163 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
