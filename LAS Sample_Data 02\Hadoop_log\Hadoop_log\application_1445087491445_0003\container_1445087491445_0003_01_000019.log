2015-10-17 21:31:11,927 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 21:31:12,037 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 21:31:12,037 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 21:31:12,052 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 21:31:12,052 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445087491445_0003, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@7f0eb4b4)
2015-10-17 21:31:12,177 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 21:31:12,568 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445087491445_0003
2015-10-17 21:31:12,833 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 21:31:13,302 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 21:31:13,318 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@6ad3381f
2015-10-17 21:31:13,552 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:671088640+134217728
2015-10-17 21:31:13,615 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 21:31:13,615 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 21:31:13,615 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 21:31:13,615 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 21:31:13,615 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 21:31:13,630 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 21:31:19,771 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:31:19,771 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34174307; bufvoid = 104857600
2015-10-17 21:31:19,771 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13786460(55145840); length = 12427937/6553600
2015-10-17 21:31:19,771 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44660065 kvi 11165012(44660048)
2015-10-17 21:31:29,193 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 21:31:29,193 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44660065 kv 11165012(44660048) kvi 8543584(34174336)
2015-10-17 21:31:40,428 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:31:40,428 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44660065; bufend = 78835555; bufvoid = 104857600
2015-10-17 21:31:40,428 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11165012(44660048); kvend = 24951772(99807088); length = 12427641/6553600
2015-10-17 21:31:40,428 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89321313 kvi 22330324(89321296)
2015-10-17 21:31:49,432 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 21:31:49,447 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 89321313 kv 22330324(89321296) kvi 19708896(78835584)
2015-10-17 21:32:03,120 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:32:03,120 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 89321313; bufend = 18640665; bufvoid = 104857600
2015-10-17 21:32:03,120 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 22330324(89321296); kvend = 9903048(39612192); length = 12427277/6553600
2015-10-17 21:32:03,260 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29126420 kvi 7281600(29126400)
2015-10-17 21:32:12,526 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 21:32:12,542 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29126420 kv 7281600(29126400) kvi 4660172(18640688)
2015-10-17 21:32:34,637 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:32:34,637 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29126420; bufend = 63303569; bufvoid = 104857600
2015-10-17 21:32:34,637 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7281600(29126400); kvend = 21068772(84275088); length = 12427229/6553600
2015-10-17 21:32:34,637 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 73789320 kvi 18447324(73789296)
2015-10-17 21:32:43,325 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 21:32:43,325 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 73789320 kv 18447324(73789296) kvi 15825900(63303600)
2015-10-17 21:32:50,544 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:32:50,544 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 73789320; bufend = 3105228; bufvoid = 104857600
2015-10-17 21:32:50,544 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 18447324(73789296); kvend = 6019188(24076752); length = 12428137/6553600
2015-10-17 21:32:50,544 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 13590982 kvi 3397740(13590960)
2015-10-17 21:32:59,700 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 21:32:59,716 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 13590982 kv 3397740(13590960) kvi 776312(3105248)
2015-10-17 21:33:02,841 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:33:02,841 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 13590982; bufend = 47768416; bufvoid = 104857600
2015-10-17 21:33:02,841 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 3397740(13590960); kvend = 17184988(68739952); length = 12427153/6553600
2015-10-17 21:33:02,841 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 58254176 kvi 14563540(58254160)
2015-10-17 21:33:03,763 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-17 21:33:11,310 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-17 21:33:11,310 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 58254176 kv 14563540(58254160) kvi 12520912(50083648)
2015-10-17 21:33:11,310 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:33:11,310 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 58254176; bufend = 63871496; bufvoid = 104857600
2015-10-17 21:33:11,310 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14563540(58254160); kvend = 12520916(50083664); length = 2042625/6553600
2015-10-17 21:33:12,388 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-17 21:33:12,404 INFO [main] org.apache.hadoop.mapred.Merger: Merging 7 sorted segments
2015-10-17 21:33:12,419 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 7 segments left of total size: 228436160 bytes
2015-10-17 21:33:32,639 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445087491445_0003_m_000006_1 is done. And is in the process of committing
2015-10-17 21:33:32,702 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445087491445_0003_m_000006_1' done.
