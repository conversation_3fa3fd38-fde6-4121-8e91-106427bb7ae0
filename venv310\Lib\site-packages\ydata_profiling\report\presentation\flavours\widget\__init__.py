from ydata_profiling.report.presentation.flavours.widget.alerts import WidgetAlerts
from ydata_profiling.report.presentation.flavours.widget.collapse import WidgetCollapse
from ydata_profiling.report.presentation.flavours.widget.container import (
    WidgetContainer,
)
from ydata_profiling.report.presentation.flavours.widget.correlation_table import (
    WidgetCorrelationTable,
)
from ydata_profiling.report.presentation.flavours.widget.dropdown import WidgetDropdown
from ydata_profiling.report.presentation.flavours.widget.duplicate import (
    WidgetDuplicate,
)
from ydata_profiling.report.presentation.flavours.widget.frequency_table import (
    WidgetFrequencyTable,
)
from ydata_profiling.report.presentation.flavours.widget.frequency_table_small import (
    WidgetFrequencyTableSmall,
)
from ydata_profiling.report.presentation.flavours.widget.html import WidgetHTML
from ydata_profiling.report.presentation.flavours.widget.image import WidgetImage
from ydata_profiling.report.presentation.flavours.widget.root import WidgetRoot
from ydata_profiling.report.presentation.flavours.widget.sample import WidgetSample
from ydata_profiling.report.presentation.flavours.widget.table import WidgetTable
from ydata_profiling.report.presentation.flavours.widget.toggle_button import (
    WidgetToggleButton,
)
from ydata_profiling.report.presentation.flavours.widget.variable import WidgetVariable
from ydata_profiling.report.presentation.flavours.widget.variable_info import (
    WidgetVariableInfo,
)

__all__ = [
    "WidgetCollapse",
    "WidgetContainer",
    "WidgetDuplicate",
    "WidgetDropdown",
    "WidgetFrequencyTable",
    "WidgetFrequencyTableSmall",
    "WidgetHTML",
    "WidgetImage",
    "WidgetRoot",
    "WidgetSample",
    "WidgetTable",
    "WidgetToggleButton",
    "WidgetVariable",
    "WidgetVariableInfo",
    "WidgetAlerts",
    "WidgetCorrelationTable",
]
