"""
LiDAR Point Cloud Analysis Tool
==============================
A comprehensive tool for analyzing and visualizing LiDAR LAS files.

Features:
- File information extraction
- Statistical analysis
- 2D and 3D visualizations
- Point cloud filtering and processing
- Export capabilities

Author: Generated for ONGC Project
Date: 2025-07-01
"""

import os
import sys
import time
from pathlib import Path
from typing import Optional, Tuple, Dict, Any

import laspy
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.colors import ListedColormap
import seaborn as sns

# Set style for better plots
plt.style.use('default')
sns.set_palette("husl")


class LiDARAnalyzer:
    """
    A comprehensive LiDAR point cloud analyzer with visualization capabilities.
    """

    def __init__(self, file_path: str):
        """
        Initialize the LiDAR analyzer with a LAS file.

        Args:
            file_path (str): Path to the LAS file
        """
        self.file_path = Path(file_path)
        self.las_data = None
        self.df = None
        self.classification_map = {
            0: 'Never Classified',
            1: 'Unclassified',
            2: 'Ground',
            3: 'Low Vegetation',
            4: 'Medium Vegetation',
            5: 'High Vegetation',
            6: 'Building',
            7: 'Low Point',
            8: 'Reserved',
            9: 'Water',
            10: 'Rail',
            11: 'Road Surface',
            12: 'Reserved',
            13: 'Wire - Guard',
            14: 'Wire - Conductor',
            15: 'Transmission Tower',
            16: 'Wire - Connector',
            17: 'Bridge Deck',
            18: 'High Noise'
        }

        self._load_data()

    def _safe_decode(self, value) -> str:
        """Safely decode bytes to string or return string as-is."""
        if isinstance(value, bytes):
            return value.decode('utf-8', errors='ignore').strip()
        elif isinstance(value, str):
            return value.strip()
        else:
            return str(value)

    def _load_data(self) -> None:
        """Load and validate the LAS file."""
        try:
            print(f"🔄 Loading LAS file: {self.file_path}")
            start_time = time.time()

            if not self.file_path.exists():
                raise FileNotFoundError(f"File not found: {self.file_path}")

            self.las_data = laspy.read(str(self.file_path))
            load_time = time.time() - start_time

            print(f"✅ Successfully loaded {len(self.las_data.points):,} points in {load_time:.2f} seconds")

        except Exception as e:
            print(f"❌ Error loading LAS file: {e}")
            sys.exit(1)

    def get_file_info(self) -> Dict[str, Any]:
        """
        Extract comprehensive file information.

        Returns:
            Dict containing file metadata
        """
        header = self.las_data.header

        info = {
            'file_path': str(self.file_path),
            'file_size_mb': self.file_path.stat().st_size / (1024 * 1024),
            'las_version': str(header.version),
            'point_format': header.point_format,
            'point_count': len(self.las_data.points),
            'creation_date': f"{header.creation_year}-{header.creation_day_of_year:03d}" if hasattr(header, 'creation_year') else 'Unknown',
            'system_id': self._safe_decode(getattr(header, 'system_identifier', 'Unknown')),
            'software': self._safe_decode(getattr(header, 'generating_software', 'Unknown')),
            'scale_factors': {
                'x': header.x_scale,
                'y': header.y_scale,
                'z': header.z_scale
            },
            'offsets': {
                'x': header.x_offset,
                'y': header.y_offset,
                'z': header.z_offset
            },
            'bounds': {
                'x_min': header.x_min, 'x_max': header.x_max,
                'y_min': header.y_min, 'y_max': header.y_max,
                'z_min': header.z_min, 'z_max': header.z_max
            },
            'dimensions': list(self.las_data.point_format.dimension_names)
        }

        return info

    def print_file_info(self) -> None:
        """Print formatted file information."""
        info = self.get_file_info()

        print("\n" + "="*60)
        print("📊 LiDAR FILE INFORMATION")
        print("="*60)

        print(f"📁 File: {info['file_path']}")
        print(f"💾 Size: {info['file_size_mb']:.2f} MB")
        print(f"📋 LAS Version: {info['las_version']}")
        print(f"🔢 Point Format: {info['point_format']}")
        print(f"📍 Total Points: {info['point_count']:,}")
        print(f"📅 Creation Date: {info['creation_date']}")
        print(f"🖥️  System ID: {info['system_id']}")
        print(f"💻 Software: {info['software']}")

        print(f"\n🔍 Scale Factors:")
        for axis, scale in info['scale_factors'].items():
            print(f"   {axis.upper()}: {scale}")

        print(f"\n📐 Coordinate Bounds:")
        bounds = info['bounds']
        print(f"   X: {bounds['x_min']:.3f} to {bounds['x_max']:.3f} (range: {bounds['x_max']-bounds['x_min']:.3f})")
        print(f"   Y: {bounds['y_min']:.3f} to {bounds['y_max']:.3f} (range: {bounds['y_max']-bounds['y_min']:.3f})")
        print(f"   Z: {bounds['z_min']:.3f} to {bounds['z_max']:.3f} (range: {bounds['z_max']-bounds['z_min']:.3f})")

        print(f"\n📊 Available Dimensions: {', '.join(info['dimensions'])}")

    def create_dataframe(self, sample_size: int = 10000) -> pd.DataFrame:
        """
        Create a pandas DataFrame from point cloud data.

        Args:
            sample_size (int): Number of points to include in DataFrame

        Returns:
            pd.DataFrame: Point cloud data
        """
        print(f"\n🔄 Creating DataFrame with {sample_size:,} points...")

        # Determine actual sample size
        actual_size = min(sample_size, len(self.las_data.points))

        # Create base DataFrame with coordinates
        data = {
            'X': self.las_data.x[:actual_size].copy(),
            'Y': self.las_data.y[:actual_size].copy(),
            'Z': self.las_data.z[:actual_size].copy()
        }

        # Add optional dimensions
        optional_dims = ['intensity', 'classification', 'return_number',
                        'number_of_returns', 'scan_angle_rank', 'user_data',
                        'point_source_id', 'red', 'green', 'blue']

        for dim in optional_dims:
            if hasattr(self.las_data, dim):
                dim_data = getattr(self.las_data, dim)
                if len(dim_data) > 0:
                    data[dim.title().replace('_', ' ')] = dim_data[:actual_size].copy()

        self.df = pd.DataFrame(data)
        print(f"✅ DataFrame created with {len(self.df)} points and {len(self.df.columns)} dimensions")

        return self.df

    def print_statistics(self) -> None:
        """Print statistical summary of the point cloud data."""
        if self.df is None:
            self.create_dataframe()

        print("\n" + "="*60)
        print("📈 STATISTICAL SUMMARY")
        print("="*60)

        print("\n📊 Basic Statistics:")
        print(self.df.describe())

        if 'Classification' in self.df.columns:
            print("\n🏷️  Classification Distribution:")
            class_counts = self.df['Classification'].value_counts().sort_index()
            for class_id, count in class_counts.items():
                class_name = self.classification_map.get(int(class_id), f'Unknown ({int(class_id)})')
                percentage = (count / len(self.df)) * 100
                print(f"   {int(class_id):2d}: {class_name:<20} - {count:,} points ({percentage:.1f}%)")

    def create_visualizations(self, plot_sample_size: int = 15000) -> None:
        """
        Create comprehensive visualizations of the point cloud data.

        Args:
            plot_sample_size (int): Number of points to use for plotting
        """
        print(f"\n🎨 Creating visualizations with {plot_sample_size:,} points...")

        # Determine actual sample size for plotting
        actual_size = min(plot_sample_size, len(self.las_data.points))

        # Extract data for plotting
        x_plot = self.las_data.x[:actual_size]
        y_plot = self.las_data.y[:actual_size]
        z_plot = self.las_data.z[:actual_size]

        # Optional data
        intensity_plot = None
        classification_plot = None

        if hasattr(self.las_data, 'intensity') and len(self.las_data.intensity) > 0:
            intensity_plot = self.las_data.intensity[:actual_size]

        if hasattr(self.las_data, 'classification') and len(self.las_data.classification) > 0:
            classification_plot = self.las_data.classification[:actual_size]

        # Create main visualization figure
        fig = plt.figure(figsize=(20, 16))
        fig.suptitle('LiDAR Point Cloud Analysis', fontsize=16, fontweight='bold')

        # 1. Top-down view colored by elevation
        ax1 = plt.subplot(3, 3, 1)
        scatter1 = ax1.scatter(x_plot, y_plot, c=z_plot, cmap='terrain', s=0.5, alpha=0.7)
        plt.colorbar(scatter1, ax=ax1, label='Elevation (m)', shrink=0.8)
        ax1.set_xlabel('X Coordinate (m)')
        ax1.set_ylabel('Y Coordinate (m)')
        ax1.set_title('Top-down View (Elevation)')
        ax1.set_aspect('equal')

        # 2. Side view X-Z
        ax2 = plt.subplot(3, 3, 2)
        ax2.scatter(x_plot, z_plot, c='steelblue', s=0.5, alpha=0.6)
        ax2.set_xlabel('X Coordinate (m)')
        ax2.set_ylabel('Elevation (m)')
        ax2.set_title('Side View (X-Z)')

        # 3. Side view Y-Z
        ax3 = plt.subplot(3, 3, 3)
        ax3.scatter(y_plot, z_plot, c='forestgreen', s=0.5, alpha=0.6)
        ax3.set_xlabel('Y Coordinate (m)')
        ax3.set_ylabel('Elevation (m)')
        ax3.set_title('Side View (Y-Z)')

        # 4. Elevation histogram
        ax4 = plt.subplot(3, 3, 4)
        ax4.hist(z_plot, bins=50, alpha=0.7, color='brown', edgecolor='black', linewidth=0.5)
        ax4.set_xlabel('Elevation (m)')
        ax4.set_ylabel('Frequency')
        ax4.set_title('Elevation Distribution')
        ax4.grid(True, alpha=0.3)

        # 5. Intensity plot (if available)
        ax5 = plt.subplot(3, 3, 5)
        if intensity_plot is not None:
            scatter5 = ax5.scatter(x_plot, y_plot, c=intensity_plot, cmap='viridis', s=0.5, alpha=0.7)
            plt.colorbar(scatter5, ax=ax5, label='Intensity', shrink=0.8)
            ax5.set_title('Top-down View (Intensity)')
        else:
            ax5.text(0.5, 0.5, 'Intensity data\nnot available',
                    ha='center', va='center', transform=ax5.transAxes, fontsize=12)
            ax5.set_title('Intensity Plot')
        ax5.set_xlabel('X Coordinate (m)')
        ax5.set_ylabel('Y Coordinate (m)')
        if intensity_plot is not None:
            ax5.set_aspect('equal')

        # 6. Classification plot (if available)
        ax6 = plt.subplot(3, 3, 6)
        if classification_plot is not None:
            unique_classes = np.unique(classification_plot)
            scatter6 = ax6.scatter(x_plot, y_plot, c=classification_plot,
                                 cmap='tab10', s=0.5, alpha=0.7)
            plt.colorbar(scatter6, ax=ax6, label='Classification', shrink=0.8)
            ax6.set_title('Top-down View (Classification)')
            ax6.set_aspect('equal')
        else:
            ax6.text(0.5, 0.5, 'Classification data\nnot available',
                    ha='center', va='center', transform=ax6.transAxes, fontsize=12)
            ax6.set_title('Classification Plot')
        ax6.set_xlabel('X Coordinate (m)')
        ax6.set_ylabel('Y Coordinate (m)')

        # 7. Point density heatmap
        ax7 = plt.subplot(3, 3, 7)
        hist, xedges, yedges = np.histogram2d(x_plot, y_plot, bins=50)
        extent = [xedges[0], xedges[-1], yedges[0], yedges[-1]]
        im7 = ax7.imshow(hist.T, extent=extent, origin='lower', cmap='hot', aspect='auto')
        plt.colorbar(im7, ax=ax7, label='Point Density', shrink=0.8)
        ax7.set_xlabel('X Coordinate (m)')
        ax7.set_ylabel('Y Coordinate (m)')
        ax7.set_title('Point Density Heatmap')

        # 8. Intensity histogram (if available)
        ax8 = plt.subplot(3, 3, 8)
        if intensity_plot is not None:
            ax8.hist(intensity_plot, bins=50, alpha=0.7, color='purple', edgecolor='black', linewidth=0.5)
            ax8.set_xlabel('Intensity')
            ax8.set_ylabel('Frequency')
            ax8.set_title('Intensity Distribution')
            ax8.grid(True, alpha=0.3)
        else:
            ax8.text(0.5, 0.5, 'Intensity data\nnot available',
                    ha='center', va='center', transform=ax8.transAxes, fontsize=12)
            ax8.set_title('Intensity Distribution')

        # 9. 3D scatter plot (subset)
        ax9 = plt.subplot(3, 3, 9, projection='3d')
        plot_3d_size = min(5000, actual_size)
        x_3d = x_plot[:plot_3d_size]
        y_3d = y_plot[:plot_3d_size]
        z_3d = z_plot[:plot_3d_size]

        if intensity_plot is not None:
            intensity_3d = intensity_plot[:plot_3d_size]
            scatter9 = ax9.scatter(x_3d, y_3d, z_3d, c=intensity_3d, cmap='viridis', s=1, alpha=0.6)
        else:
            ax9.scatter(x_3d, y_3d, z_3d, c='blue', s=1, alpha=0.6)

        ax9.set_xlabel('X (m)')
        ax9.set_ylabel('Y (m)')
        ax9.set_zlabel('Z (m)')
        ax9.set_title(f'3D View ({plot_3d_size} points)')

        plt.tight_layout()
        plt.savefig('lidar_comprehensive_analysis.png', dpi=300, bbox_inches='tight')
        print(f"✅ Comprehensive analysis plot saved as 'lidar_comprehensive_analysis.png'")
        plt.show()

        # Create separate detailed 3D plot
        self._create_3d_plot(actual_size)

    def _create_3d_plot(self, sample_size: int) -> None:
        """Create a detailed 3D visualization."""
        print("🎨 Creating detailed 3D visualization...")

        fig_3d = plt.figure(figsize=(14, 10))
        ax_3d = fig_3d.add_subplot(111, projection='3d')

        # Use subset for 3D performance
        plot_3d_size = min(8000, sample_size)
        x_3d = self.las_data.x[:plot_3d_size]
        y_3d = self.las_data.y[:plot_3d_size]
        z_3d = self.las_data.z[:plot_3d_size]

        if hasattr(self.las_data, 'intensity') and len(self.las_data.intensity) > 0:
            intensity_3d = self.las_data.intensity[:plot_3d_size]
            scatter_3d = ax_3d.scatter(x_3d, y_3d, z_3d, c=intensity_3d,
                                     cmap='viridis', s=2, alpha=0.6)
            plt.colorbar(scatter_3d, ax=ax_3d, label='Intensity', shrink=0.6)
        else:
            ax_3d.scatter(x_3d, y_3d, z_3d, c='blue', s=2, alpha=0.6)

        ax_3d.set_xlabel('X Coordinate (m)')
        ax_3d.set_ylabel('Y Coordinate (m)')
        ax_3d.set_zlabel('Elevation (m)')
        ax_3d.set_title(f'3D Point Cloud Visualization\n({plot_3d_size:,} points)')

        plt.savefig('lidar_3d_detailed.png', dpi=300, bbox_inches='tight')
        print(f"✅ Detailed 3D plot saved as 'lidar_3d_detailed.png'")
        plt.show()

    def export_data(self, output_file: str = 'lidar_sample_data.csv') -> None:
        """Export sample data to CSV file."""
        if self.df is None:
            self.create_dataframe()

        print(f"\n💾 Exporting data to {output_file}...")
        self.df.to_csv(output_file, index=False)
        print(f"✅ Data exported successfully!")

    def run_complete_analysis(self) -> None:
        """Run the complete analysis workflow."""
        print("🚀 Starting comprehensive LiDAR analysis...")

        # Print file information
        self.print_file_info()

        # Create DataFrame
        self.create_dataframe(sample_size=15000)

        # Print statistics
        self.print_statistics()

        # Create visualizations
        self.create_visualizations(plot_sample_size=20000)

        # Export sample data
        self.export_data()

        print("\n🎉 Analysis complete!")


def main():
    """Main execution function."""
    # File path to the LAS file
    las_file_path = r"C:\\Users\\<USER>\\OneDrive\\Desktop\\ONGC\\Project1\\LiDAR LAS sample_data\\2_1 - Cloud - Cloud.las"

    try:
        # Create analyzer instance and run analysis
        analyzer = LiDARAnalyzer(las_file_path)
        analyzer.run_complete_analysis()

    except KeyboardInterrupt:
        print("\n⚠️  Analysis interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during analysis: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
