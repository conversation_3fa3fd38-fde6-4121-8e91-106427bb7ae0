from __future__ import annotations

import typing as _t

from narwhals import dependencies, dtypes, exceptions, selectors
from narwhals._utils import (
    Implementation,
    generate_temporary_column_name,
    is_ordered_categorical,
    maybe_align_index,
    maybe_convert_dtypes,
    maybe_get_index,
    maybe_reset_index,
    maybe_set_index,
)
from narwhals.dataframe import DataFrame, LazyFrame
from narwhals.dtypes import (
    Array,
    Binary,
    Boolean,
    Categorical,
    Date,
    Datetime,
    Decimal,
    Duration,
    Enum,
    Field,
    Float32,
    Float64,
    Int8,
    Int16,
    Int32,
    Int64,
    Int128,
    List,
    Object,
    String,
    Struct,
    Time,
    UInt8,
    UInt16,
    UInt32,
    UInt64,
    UInt128,
    Unknown,
)
from narwhals.expr import Expr
from narwhals.functions import (
    all_ as all,
    all_horizontal,
    any_horizontal,
    coalesce,
    col,
    concat,
    concat_str,
    exclude,
    from_arrow,
    from_dict,
    from_numpy,
    get_level,
    len_ as len,
    lit,
    max,
    max_horizontal,
    mean,
    mean_horizontal,
    median,
    min,
    min_horizontal,
    new_series,
    nth,
    read_csv,
    read_parquet,
    scan_csv,
    scan_parquet,
    show_versions,
    sum,
    sum_horizontal,
    when,
)
from narwhals.schema import Schema
from narwhals.series import Series
from narwhals.translate import (
    from_native,
    get_native_namespace,
    narwhalify,
    to_native,
    to_py_scalar,
)

__version__: str

__all__ = [
    "Array",
    "Binary",
    "Boolean",
    "Categorical",
    "DataFrame",
    "Date",
    "Datetime",
    "Decimal",
    "Duration",
    "Enum",
    "Expr",
    "Field",
    "Float32",
    "Float64",
    "Implementation",
    "Int8",
    "Int16",
    "Int32",
    "Int64",
    "Int128",
    "LazyFrame",
    "List",
    "Object",
    "Schema",
    "Series",
    "String",
    "Struct",
    "Time",
    "UInt8",
    "UInt16",
    "UInt32",
    "UInt64",
    "UInt128",
    "Unknown",
    "all",
    "all_horizontal",
    "any_horizontal",
    "coalesce",
    "col",
    "concat",
    "concat_str",
    "dependencies",
    "dtypes",
    "exceptions",
    "exclude",
    "from_arrow",
    "from_dict",
    "from_native",
    "from_numpy",
    "generate_temporary_column_name",
    "get_level",
    "get_native_namespace",
    "is_ordered_categorical",
    "len",
    "lit",
    "max",
    "max_horizontal",
    "maybe_align_index",
    "maybe_convert_dtypes",
    "maybe_get_index",
    "maybe_reset_index",
    "maybe_set_index",
    "mean",
    "mean_horizontal",
    "median",
    "min",
    "min_horizontal",
    "narwhalify",
    "new_series",
    "nth",
    "read_csv",
    "read_parquet",
    "scan_csv",
    "scan_parquet",
    "selectors",
    "show_versions",
    "sum",
    "sum_horizontal",
    "to_native",
    "to_py_scalar",
    "when",
]


def __getattr__(name: _t.Literal["__version__"]) -> str:  # type: ignore[misc]
    if name == "__version__":
        global __version__  # noqa: PLW0603

        from importlib import metadata

        __version__ = metadata.version(__name__)
        return __version__
    else:
        msg = f"module {__name__!r} has no attribute {name!r}"
        raise AttributeError(msg)
