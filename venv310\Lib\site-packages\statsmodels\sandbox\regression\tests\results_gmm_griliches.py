'''Results for GMM estimation with Stata gmm

autogenerated, but edited
'''

import numpy as np

from statsmodels.tools.testing import ParamsTableTestBunch

# gmm twostep

est = dict(
    rank=13,
    N=758,
    Q=.0153053843867424,
    J=11.60148136515076,
    J_df=2,
    k_1=13,
    converged=1,
    has_xtinst=0,
    type=1,
    n_eq=1,
    k=13,
    n_moments=15,
    k_aux=13,
    k_eq_model=0,
    k_eq=13,
    cmdline="gmm (lw - {xb:s iq expr tenure rns smsa dyear*} - {b0}), instruments(expr tenure rns smsa dyear* med kww age mrt)",  # noqa:E501
    cmd="gmm",
    estat_cmd="gmm_estat",
    predict="gmm_p",
    marginsnotok="_ALL",
    eqnames="1",
    technique="gn",
    winit="Unadjusted",
    estimator="twostep",
    wmatrix="robust",
    vce="robust",
    vcetype="Robust",
    params="xb_s xb_iq xb_expr xb_tenure xb_rns xb_smsa xb_dyear_67 xb_dyear_68 xb_dyear_69 xb_dyear_70 xb_dyear_71 xb_dyear_73 b0",  # noqa:E501
    inst_1="expr tenure rns smsa dyear_67 dyear_68 dyear_69 dyear_70 dyear_71 dyear_73 med kww age mrt _cons",  # noqa:E501
    params_1="xb_s xb_iq xb_expr xb_tenure xb_rns xb_smsa xb_dyear_67 xb_dyear_68 xb_dyear_69 xb_dyear_70 xb_dyear_71 xb_dyear_73 b0",  # noqa:E501
    sexp_1="lw - ({xb_s} *s + {xb_iq} *iq + {xb_expr} *expr + {xb_tenure} *tenure + {xb_rns} *rns + {xb_smsa} *smsa + {xb_dyear_67} *dyear_67 + {xb_dyear_68} *dyear_68 + {xb_dyear_69} *dyear_69 + {xb_dyear_70} *dyear_70 + {xb_dyear_71} *dyear_71 + {xb_dyear_73} *dyear_73) - {b0}",  # noqa:E501
    properties="b V",
)

params_table = np.array([
    .17579576802018,  .02085135579518,  8.4309034744314,  3.430065999e-17,
    .1349278616328,  .21666367440756, np.nan,  1.9599639845401,
    0, -.00928615655842,  .00491818692744, -1.8881259893969,
    .05900903858228, -.01892562580544,  .00035331268861, np.nan,
    1.9599639845401,                0,  .05028275907275,  .00810402250669,
    6.2046667603925,  5.481292387e-10,  .03439916682973,  .06616635131577,
    np.nan,  1.9599639845401,                0,  .04252138311466,
    .00956014552889,  4.4477757149388,  8.676405373e-06,  .02378384219108,
    .06125892403824, np.nan,  1.9599639845401,                0,
    -.10409306762507,  .03372997947655, -3.0860696994324,  .00202821272955,
    -.17020261259839, -.03798352265175, np.nan,  1.9599639845401,
    0,  .12475123236049,  .03098732229429,  4.0258797186699,
    .00005676270037,  .06401719668634,  .18548526803464, np.nan,
    1.9599639845401,                0,  -.0530431735239,  .05178756424595,
    -1.0242453819993,  .30571938841786, -.15454493429301,  .04845858724521,
    np.nan,  1.9599639845401,                0,    .045954590377,
    .0500069437958,  .91896418554698,  .35811430537838, -.05205721843969,
    .14396639919369, np.nan,  1.9599639845401,                0,
    .15548006235586,  .04801256009054,   3.238320599082,  .00120235613037,
    .06137717377284,  .24958295093889, np.nan,  1.9599639845401,
    0,  .16698745541498,  .06133412154231,  2.7225865670836,
    .00647730609718,  .04677478616864,  .28720012466132, np.nan,
    1.9599639845401,                0,  .08464846645187,  .05581696231774,
    1.516536603515,  .12938372202927, -.02475076941733,  .19404770232108,
    np.nan,  1.9599639845401,                0,   .0996068440051,
    .06123938652803,  1.6265160324477,  .10383992558646, -.02042014802516,
    .21963383603536, np.nan,  1.9599639845401,                0,
    4.0039243732137,  .33647541282379,  11.899604608883,  1.189091957e-32,
    3.3444446823958,  4.6634040640315, np.nan,  1.9599639845401,
    0]).reshape(13, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = ['_cons'] * 13

cov = np.array([
    .0004347790385, -.00007935266682,  .00002810133321,  .00001482865838,
    -.00017806902959, -6.657494262e-06, -.00011587283596, -.00018806600631,
    -.00012201035826, -.00008278800394, -.00031494508432, -.00063549805233,
    .00264135978484, -.00007935266682,  .00002418856265,  4.931157962e-06,
    -.00001114070074,  .00006618982562,  -.0000220304377,  4.705098991e-07,
    .00003206383766, -.00002261417242, -.00006024150154, -.00001412826237,
    .00001471698858, -.00144299517314,  .00002810133321,  4.931157962e-06,
    .00006567518079, -.00002036677634,  .00005210753053, -.00003295500811,
    .00003592276303,  .00008754309097,  .00003052996731,  .00001691178156,
    -.00008575591893, -.00013143657018, -.00094318538399,  .00001482865838,
    -.00001114070074, -.00002036677634,  .00009139638253, -.00003771101246,
    7.851603106e-06,  .00008478298315,  .00006722294295,  .00011231163007,
    .0001007216652,  .00011202284802,  .00009437878507,  .00075643538833,
    -.00017806902959,  .00006618982562,  .00005210753053, -.00003771101246,
    .00113771151549,   .0001300530702,  .00018006693931,  .00018772842105,
    -9.500874246e-06,  .00001633701903, -.00005338908155,  .00008260866257,
    -.00499436928105, -6.657494262e-06,  -.0000220304377, -.00003295500811,
    7.851603106e-06,   .0001300530702,  .00096021414297,  .00005702363753,
    .00011167528598,  .00025281311283,  .00010653704891,  .00030212216421,
    .000307795004,  .00157107924026, -.00011587283596,  4.705098991e-07,
    .00003592276303,  .00008478298315,  .00018006693931,  .00005702363753,
    .00268195181053,  .00085892679447,  .00091106709634,  .00096277498668,
    .00090313286214,  .00102719488714,  .00034624154943, -.00018806600631,
    .00003206383766,  .00008754309097,  .00006722294295,  .00018772842105,
    .00011167528598,  .00085892679447,   .0025006944278,  .00092531815147,
    .00088200162521,  .00082339570405,  .00095012566921,  -.0020631120951,
    -.00012201035826, -.00002261417242,  .00003052996731,  .00011231163007,
    -9.500874246e-06,  .00025281311283,  .00091106709634,  .00092531815147,
    .00230520592645,  .00118209509709,  .00111002620771,  .00129242901685,
    .00256100032752, -.00008278800394, -.00006024150154,  .00001691178156,
    .0001007216652,  .00001633701903,  .00010653704891,  .00096277498668,
    .00088200162521,  .00118209509709,  .00376187446537,  .00124524263644,
    .00155856745623,  .00599140534858, -.00031494508432, -.00001412826237,
    -.00008575591893,  .00011202284802, -.00005338908155,  .00030212216421,
    .00090313286214,  .00082339570405,  .00111002620771,  .00124524263644,
    .00311553328238,  .00184297728198,  .00431291320555, -.00063549805233,
    .00001471698858, -.00013143657018,  .00009437878507,  .00008260866257,
    .000307795004,  .00102719488714,  .00095012566921,  .00129242901685,
    .00155856745623,  .00184297728198,  .00375026246233,  .00538820067052,
    .00264135978484, -.00144299517314, -.00094318538399,  .00075643538833,
    -.00499436928105,  .00157107924026,  .00034624154943,  -.0020631120951,
    .00256100032752,  .00599140534858,  .00431291320555,  .00538820067052,
    .11321570343494]).reshape(13, 13)

cov_colnames = ['_cons'] * 13

cov_rownames = ['_cons'] * 13


results_twostep = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    **est
    )

# begin gmm onestep

est = dict(
    rank=13,
    N=758,
    Q=.0175043949471787,
    J=13.26833136996146,
    J_df=2,
    k_1=13,
    converged=1,
    has_xtinst=0,
    type=1,
    n_eq=1,
    k=13,
    n_moments=15,
    k_aux=13,
    k_eq_model=0,
    k_eq=13,
    cmdline="gmm (lw - {xb:s iq expr tenure rns smsa dyear*} - {b0}), instruments(expr tenure rns smsa dyear* med kww age mrt) onestep",  # noqa:E501
    cmd="gmm",
    estat_cmd="gmm_estat",
    predict="gmm_p",
    marginsnotok="_ALL",
    eqnames="1",
    technique="gn",
    winit="Unadjusted",
    estimator="onestep",
    wmatrix="robust",
    vce="robust",
    vcetype="Robust",
    params="xb_s xb_iq xb_expr xb_tenure xb_rns xb_smsa xb_dyear_67 xb_dyear_68 xb_dyear_69 xb_dyear_70 xb_dyear_71 xb_dyear_73 b0",  # noqa:E501
    inst_1="expr tenure rns smsa dyear_67 dyear_68 dyear_69 dyear_70 dyear_71 dyear_73 med kww age mrt _cons",  # noqa:E501
    params_1="xb_s xb_iq xb_expr xb_tenure xb_rns xb_smsa xb_dyear_67 xb_dyear_68 xb_dyear_69 xb_dyear_70 xb_dyear_71 xb_dyear_73 b0",  # noqa:E501
    sexp_1="lw - ({xb_s} *s + {xb_iq} *iq + {xb_expr} *expr + {xb_tenure} *tenure + {xb_rns} *rns + {xb_smsa} *smsa + {xb_dyear_67} *dyear_67 + {xb_dyear_68} *dyear_68 + {xb_dyear_69} *dyear_69 + {xb_dyear_70} *dyear_70 + {xb_dyear_71} *dyear_71 + {xb_dyear_73} *dyear_73) - {b0}",  # noqa:E501
    properties="b V",
)

params_table = np.array([
    .1724253119448,  .02073946970972,  8.3138727440057,  9.262847838e-17,
    .13177669825528,  .21307392563431, np.nan,  1.9599639845401,
    0, -.00909883104783,  .00488623921543, -1.8621337692816,
    .06258423710802, -.01867568392991,  .00047802183425, np.nan,
    1.9599639845401,                0,  .04928948974112,  .00804979771953,
    6.1230718408647,  9.178828333e-10,    .033512176128,  .06506680335423,
    np.nan,  1.9599639845401,                0,  .04221709210829,
    .00946363451925,  4.4609808232133,  8.158539147e-06,   .0236687092877,
    .06076547492887, np.nan,  1.9599639845401,                0,
    -.10179345005432,   .0337105276595, -3.0196338390938,  .00253080446805,
    -.16786487016678, -.03572202994187, np.nan,  1.9599639845401,
    0,  .12611094948071,   .0308113805617,  4.0929989887401,
    .00004258295784,  .06572175326583,  .18650014569559, np.nan,
    1.9599639845401,                0,  -.0596171062088,  .05171372339438,
    -1.1528295062831,  .24898037089783, -.16097414156825,  .04173992915064,
    np.nan,  1.9599639845401,                0,  .04867955998567,
    .04981322392381,  .97724170714436,  .32844950450919, -.04895256485883,
    .14631168483017, np.nan,  1.9599639845401,                0,
    .15281763323761,  .04792849748935,   3.188450321681,  .00143037585682,
    .05887950432536,  .24675576214986, np.nan,  1.9599639845401,
    0,  .17443605153365,  .06112514589814,  2.8537527227227,
    .00432061472141,  .05463296702353,  .29423913604377, np.nan,
    1.9599639845401,                0,   .0916659665856,   .0554618025144,
    1.6527765494425,  .09837634840443, -.01703716886029,  .20036910203149,
    np.nan,  1.9599639845401,                0,  .09323976498299,
    .06084900556261,  1.5323137021041,    .125445041459, -.02602209441479,
    .21250162438078, np.nan,  1.9599639845401,                0,
    4.0335098954259,  .33503289261392,   12.03914595954,  2.212341104e-33,
    3.3768574922664,  4.6901622985855, np.nan,  1.9599639845401,
    0]).reshape(13, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = ['_cons'] * 13

cov = np.array([
    .00043012560384, -.00007821948168,  .00002814664044,  .00001470659469,
    -.00018137297337, -8.404214163e-06,   -.000116495836, -.00019098604401,
    -.00012670601919, -.00008672920733, -.00031350033095, -.00062509206531,
    .00258704275396, -.00007821948168,  .00002387533367,  4.911669728e-06,
    -.00001098678322,  .00006618473561, -.00002158670034,  8.107545213e-07,
    .00003255315461, -.00002143051924,  -.0000597535309, -.00001402380853,
    .00001385883996, -.00142630446035,  .00002814664044,  4.911669728e-06,
    .00006479924333, -.00001977796199,  .00005110284341, -.00003232809926,
    .00003557970376,  .00008581782553,  .00002961847494,  .00001478700432,
    -.00008727552546, -.00012994173168, -.00094120116335,  .00001470659469,
    -.00001098678322, -.00001977796199,  .00008956037831, -.00003784800308,
    7.059546860e-06,  .00008151950631,  .00006348047144,  .00010852497856,
    .00009624187488,  .00010823787214,  .00009132957164,  .00074787094553,
    -.00018137297337,  .00006618473561,  .00005110284341, -.00003784800308,
    .00113639967508,  .00013313518183,  .00019039509438,   .0002000965573,
    7.191780465e-06,  .00002329093697, -.00005087978271,  .00009086571425,
    -.00495748724374, -8.404214163e-06, -.00002158670034, -.00003232809926,
    7.059546860e-06,  .00013313518183,  .00094934117212,  .00006195450052,
    .00011810217311,  .00025505395817,  .00011081126685,  .00030134673539,
    .00030676742472,  .00155300401754,   -.000116495836,  8.107545213e-07,
    .00003557970376,  .00008151950631,  .00019039509438,  .00006195450052,
    .00267430918731,  .00086135304709,  .00092017339035,  .00095567351479,
    .000887006474,  .00102883960359,   .0003167617596, -.00019098604401,
    .00003255315461,  .00008581782553,  .00006348047144,   .0002000965573,
    .00011810217311,  .00086135304709,  .00248135727768,   .0009302682109,
    .0008777378644,  .00081079994623,  .00094288525746, -.00207087031796,
    -.00012670601919, -.00002143051924,  .00002961847494,  .00010852497856,
    7.191780465e-06,  .00025505395817,  .00092017339035,   .0009302682109,
    .00229714087159,  .00117701812554,  .00109484405919,  .00129252524238,
    .00250083573173, -.00008672920733,  -.0000597535309,  .00001478700432,
    .00009624187488,  .00002329093697,  .00011081126685,  .00095567351479,
    .0008777378644,  .00117701812554,  .00373628346107,  .00123495172035,
    .00154490399953,  .00600809353679, -.00031350033095, -.00001402380853,
    -.00008727552546,  .00010823787214, -.00005087978271,  .00030134673539,
    .000887006474,  .00081079994623,  .00109484405919,  .00123495172035,
    .00307601153815,   .0018118788444,  .00430884303498, -.00062509206531,
    .00001385883996, -.00012994173168,  .00009132957164,  .00009086571425,
    .00030676742472,  .00102883960359,  .00094288525746,  .00129252524238,
    .00154490399953,   .0018118788444,  .00370260147796,  .00534911865442,
    .00258704275396, -.00142630446035, -.00094120116335,  .00074787094553,
    -.00495748724374,  .00155300401754,   .0003167617596, -.00207087031796,
    .00250083573173,  .00600809353679,  .00430884303498,  .00534911865442,
    .11224703913325]).reshape(13, 13)

cov_colnames = ['_cons'] * 13

cov_rownames = ['_cons'] * 13


results_onestep = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    **est
)
