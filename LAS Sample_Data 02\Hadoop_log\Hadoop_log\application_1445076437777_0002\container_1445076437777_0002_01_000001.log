2015-10-17 18:09:20,743 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445076437777_0002_000001
2015-10-17 18:09:21,352 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 18:09:21,352 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 2 cluster_timestamp: 1445076437777 } attemptId: 1 } keyId: 291674728)
2015-10-17 18:09:21,571 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 18:09:22,383 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 18:09:22,461 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 18:09:22,493 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 18:09:22,493 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 18:09:22,508 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 18:09:22,508 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 18:09:22,508 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 18:09:22,508 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 18:09:22,508 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 18:09:22,508 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 18:09:22,555 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:09:22,586 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:09:22,602 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:09:22,618 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 18:09:22,680 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 18:09:23,024 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 18:09:23,086 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 18:09:23,086 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 18:09:23,086 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445076437777_0002 to jobTokenSecretManager
2015-10-17 18:09:23,290 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445076437777_0002 because: not enabled; too many maps; too much input;
2015-10-17 18:09:23,321 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445076437777_0002 = 1256521728. Number of splits = 10
2015-10-17 18:09:23,321 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445076437777_0002 = 1
2015-10-17 18:09:23,321 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0002Job Transitioned from NEW to INITED
2015-10-17 18:09:23,321 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445076437777_0002.
2015-10-17 18:09:23,368 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 18:09:23,383 INFO [Socket Reader #1 for port 53349] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 53349
2015-10-17 18:09:23,415 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 18:09:23,415 INFO [IPC Server listener on 53349] org.apache.hadoop.ipc.Server: IPC Server listener on 53349: starting
2015-10-17 18:09:23,415 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 18:09:23,415 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at 04DN8IQ.fareast.corp.microsoft.com/***********:53349
2015-10-17 18:09:23,508 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 18:09:23,524 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 18:09:23,540 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 18:09:23,540 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 18:09:23,540 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 18:09:23,540 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 18:09:23,540 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 18:09:23,555 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 53356
2015-10-17 18:09:23,555 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 18:09:23,618 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_53356_mapreduce____57m6yr\webapp
2015-10-17 18:09:23,852 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:53356
2015-10-17 18:09:23,852 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 53356
2015-10-17 18:09:24,461 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 18:09:24,477 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445076437777_0002
2015-10-17 18:09:24,477 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 18:09:24,493 INFO [Socket Reader #1 for port 53359] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 53359
2015-10-17 18:09:24,493 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 18:09:24,493 INFO [IPC Server listener on 53359] org.apache.hadoop.ipc.Server: IPC Server listener on 53359: starting
2015-10-17 18:09:24,524 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 18:09:24,524 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 18:09:24,524 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 18:09:24,571 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 18:09:24,665 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 18:09:24,665 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 18:09:24,680 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 18:09:24,680 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 18:09:24,696 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0002Job Transitioned from INITED to SETUP
2015-10-17 18:09:24,696 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 18:09:24,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0002Job Transitioned from SETUP to RUNNING
2015-10-17 18:09:24,743 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,743 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:09:24,758 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 18:09:24,774 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 18:09:24,821 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445076437777_0002, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0002/job_1445076437777_0002_1.jhist
2015-10-17 18:09:25,680 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 18:09:25,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:32768, vCores:-3> knownNMs=5
2015-10-17 18:09:25,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:32768, vCores:-3>
2015-10-17 18:09:25,743 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:26,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 10
2015-10-17 18:09:26,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0002_01_000002 to attempt_1445076437777_0002_m_000000_0
2015-10-17 18:09:26,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0002_01_000003 to attempt_1445076437777_0002_m_000001_0
2015-10-17 18:09:26,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0002_01_000004 to attempt_1445076437777_0002_m_000002_0
2015-10-17 18:09:26,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0002_01_000005 to attempt_1445076437777_0002_m_000003_0
2015-10-17 18:09:26,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0002_01_000006 to attempt_1445076437777_0002_m_000004_0
2015-10-17 18:09:26,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0002_01_000007 to attempt_1445076437777_0002_m_000005_0
2015-10-17 18:09:26,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0002_01_000008 to attempt_1445076437777_0002_m_000006_0
2015-10-17 18:09:26,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0002_01_000009 to attempt_1445076437777_0002_m_000007_0
2015-10-17 18:09:26,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0002_01_000010 to attempt_1445076437777_0002_m_000008_0
2015-10-17 18:09:26,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0002_01_000011 to attempt_1445076437777_0002_m_000009_0
2015-10-17 18:09:26,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:18432, vCores:-17>
2015-10-17 18:09:26,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:26,805 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 18:09:26,883 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,915 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0002/job.jar
2015-10-17 18:09:26,915 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0002/job.xml
2015-10-17 18:09:26,915 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 18:09:26,915 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 18:09:26,915 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 18:09:26,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:09:26,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:09:26,993 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0002_01_000002 taskAttempt attempt_1445076437777_0002_m_000000_0
2015-10-17 18:09:26,993 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0002_01_000003 taskAttempt attempt_1445076437777_0002_m_000001_0
2015-10-17 18:09:26,993 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0002_01_000004 taskAttempt attempt_1445076437777_0002_m_000002_0
2015-10-17 18:09:26,993 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0002_01_000006 taskAttempt attempt_1445076437777_0002_m_000004_0
2015-10-17 18:09:26,993 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0002_01_000005 taskAttempt attempt_1445076437777_0002_m_000003_0
2015-10-17 18:09:26,993 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0002_01_000007 taskAttempt attempt_1445076437777_0002_m_000005_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0002_01_000008 taskAttempt attempt_1445076437777_0002_m_000006_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0002_01_000009 taskAttempt attempt_1445076437777_0002_m_000007_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0002_01_000010 taskAttempt attempt_1445076437777_0002_m_000008_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0002_01_000011 taskAttempt attempt_1445076437777_0002_m_000009_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0002_m_000009_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0002_m_000006_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0002_m_000005_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0002_m_000007_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0002_m_000001_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0002_m_000002_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0002_m_000008_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0002_m_000004_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0002_m_000003_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0002_m_000000_0
2015-10-17 18:09:27,008 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:09:27,040 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:09:27,040 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:09:27,040 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:09:27,040 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:09:27,040 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:09:27,040 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:09:27,040 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:09:27,040 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:09:27,040 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:09:27,133 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0002_m_000000_0 : 13562
2015-10-17 18:09:27,133 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0002_m_000003_0 : 13562
2015-10-17 18:09:27,133 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0002_m_000004_0 : 13562
2015-10-17 18:09:27,133 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0002_m_000002_0 : 13562
2015-10-17 18:09:27,133 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0002_m_000001_0 : 13562
2015-10-17 18:09:27,133 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0002_m_000005_0 : 13562
2015-10-17 18:09:27,133 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0002_m_000001_0] using containerId: [container_1445076437777_0002_01_000003 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:09:27,133 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0002_m_000005_0] using containerId: [container_1445076437777_0002_01_000007 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0002_m_000003_0] using containerId: [container_1445076437777_0002_01_000005 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0002_m_000000_0] using containerId: [container_1445076437777_0002_01_000002 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0002_m_000004_0] using containerId: [container_1445076437777_0002_01_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0002_m_000002_0] using containerId: [container_1445076437777_0002_01_000004 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0002_m_000001
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0002_m_000005
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0002_m_000003
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0002_m_000000
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0002_m_000004
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0002_m_000002
2015-10-17 18:09:27,149 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:27,446 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0002_m_000007_0 : 13562
2015-10-17 18:09:27,446 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0002_m_000008_0 : 13562
2015-10-17 18:09:27,446 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0002_m_000009_0 : 13562
2015-10-17 18:09:27,446 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0002_m_000006_0 : 13562
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0002_m_000007_0] using containerId: [container_1445076437777_0002_01_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0002_m_000008_0] using containerId: [container_1445076437777_0002_01_000010 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0002_m_000009_0] using containerId: [container_1445076437777_0002_01_000011 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0002_m_000006_0] using containerId: [container_1445076437777_0002_01_000008 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0002_m_000007
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0002_m_000008
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0002_m_000009
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0002_m_000006
2015-10-17 18:09:27,446 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:09:27,805 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:16384, vCores:-19> knownNMs=5
2015-10-17 18:09:27,805 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:16384, vCores:-19>
2015-10-17 18:09:27,805 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:28,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:14336, vCores:-21>
2015-10-17 18:09:28,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:29,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-25>
2015-10-17 18:09:29,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:30,837 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-29>
2015-10-17 18:09:30,837 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:31,290 INFO [Socket Reader #1 for port 53359] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0002 (auth:SIMPLE)
2015-10-17 18:09:31,290 INFO [Socket Reader #1 for port 53359] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0002 (auth:SIMPLE)
2015-10-17 18:09:31,337 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0002_m_000004 asked for a task
2015-10-17 18:09:31,337 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0002_m_000003 asked for a task
2015-10-17 18:09:31,337 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0002_m_000003 given task: attempt_1445076437777_0002_m_000001_0
2015-10-17 18:09:31,337 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0002_m_000004 given task: attempt_1445076437777_0002_m_000002_0
2015-10-17 18:09:31,384 INFO [Socket Reader #1 for port 53359] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0002 (auth:SIMPLE)
2015-10-17 18:09:31,399 INFO [Socket Reader #1 for port 53359] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0002 (auth:SIMPLE)
2015-10-17 18:09:31,399 INFO [Socket Reader #1 for port 53359] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0002 (auth:SIMPLE)
2015-10-17 18:09:31,399 INFO [Socket Reader #1 for port 53359] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0002 (auth:SIMPLE)
2015-10-17 18:09:31,399 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0002_m_000006 asked for a task
2015-10-17 18:09:31,399 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0002_m_000006 given task: attempt_1445076437777_0002_m_000004_0
2015-10-17 18:09:31,415 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0002_m_000007 asked for a task
2015-10-17 18:09:31,415 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0002_m_000007 given task: attempt_1445076437777_0002_m_000005_0
2015-10-17 18:09:31,431 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0002_m_000005 asked for a task
2015-10-17 18:09:31,431 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0002_m_000005 given task: attempt_1445076437777_0002_m_000003_0
2015-10-17 18:09:31,431 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0002_m_000002 asked for a task
2015-10-17 18:09:31,431 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0002_m_000002 given task: attempt_1445076437777_0002_m_000000_0
2015-10-17 18:09:31,837 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-17 18:09:31,837 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:33,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-17 18:09:33,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:34,743 INFO [Socket Reader #1 for port 53359] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0002 (auth:SIMPLE)
2015-10-17 18:09:34,774 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0002_m_000010 asked for a task
2015-10-17 18:09:34,774 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0002_m_000010 given task: attempt_1445076437777_0002_m_000008_0
2015-10-17 18:09:34,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 18:09:34,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:35,540 INFO [Socket Reader #1 for port 53359] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0002 (auth:SIMPLE)
2015-10-17 18:09:35,540 INFO [Socket Reader #1 for port 53359] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0002 (auth:SIMPLE)
2015-10-17 18:09:35,540 INFO [Socket Reader #1 for port 53359] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0002 (auth:SIMPLE)
2015-10-17 18:09:35,571 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0002_m_000008 asked for a task
2015-10-17 18:09:35,571 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0002_m_000008 given task: attempt_1445076437777_0002_m_000006_0
2015-10-17 18:09:35,571 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0002_m_000009 asked for a task
2015-10-17 18:09:35,571 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0002_m_000009 given task: attempt_1445076437777_0002_m_000007_0
2015-10-17 18:09:35,587 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0002_m_000011 asked for a task
2015-10-17 18:09:35,587 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0002_m_000011 given task: attempt_1445076437777_0002_m_000009_0
2015-10-17 18:09:37,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:09:37,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:09:38,884 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.10593059
2015-10-17 18:09:38,884 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.10605047
2015-10-17 18:09:38,884 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.10251978
2015-10-17 18:09:38,900 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.10635664
2015-10-17 18:09:38,915 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.09884333
2015-10-17 18:09:39,212 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.10641261
2015-10-17 18:09:41,915 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.10635664
2015-10-17 18:09:41,915 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.10685723
2015-10-17 18:09:41,915 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.106493875
2015-10-17 18:09:41,915 INFO [IPC Server handler 17 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.10680563
2015-10-17 18:09:41,931 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.10660437
2015-10-17 18:09:42,212 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.1066108
2015-10-17 18:09:43,650 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.099219136
2015-10-17 18:09:44,478 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.28769955
2015-10-17 18:09:44,619 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.100928955
2015-10-17 18:09:44,759 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.09381917
2015-10-17 18:09:44,947 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.10680563
2015-10-17 18:09:44,962 INFO [IPC Server handler 17 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.10635664
2015-10-17 18:09:44,962 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.106493875
2015-10-17 18:09:44,962 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.10660437
2015-10-17 18:09:44,962 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.10685723
2015-10-17 18:09:45,322 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.1066108
2015-10-17 18:09:46,712 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.106881365
2015-10-17 18:09:47,540 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.295472
2015-10-17 18:09:47,665 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.106964506
2015-10-17 18:09:47,790 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.10681946
2015-10-17 18:09:48,009 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.10635664
2015-10-17 18:09:48,025 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.10685723
2015-10-17 18:09:48,025 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.106493875
2015-10-17 18:09:48,025 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.10680563
2015-10-17 18:09:48,040 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.10660437
2015-10-17 18:09:48,353 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.1066108
2015-10-17 18:09:49,775 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.106881365
2015-10-17 18:09:50,744 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.295472
2015-10-17 18:09:50,869 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.106964506
2015-10-17 18:09:50,931 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.10681946
2015-10-17 18:09:51,119 INFO [IPC Server handler 17 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.14265993
2015-10-17 18:09:51,119 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.13864124
2015-10-17 18:09:51,119 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.1459654
2015-10-17 18:09:51,119 INFO [IPC Server handler 17 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.11080518
2015-10-17 18:09:51,400 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.121471114
2015-10-17 18:09:51,416 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.13912758
2015-10-17 18:09:52,916 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.111657776
2015-10-17 18:09:53,963 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.35388952
2015-10-17 18:09:53,994 INFO [IPC Server handler 17 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.11355078
2015-10-17 18:09:54,181 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.19247705
2015-10-17 18:09:54,197 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.17708051
2015-10-17 18:09:54,197 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.19209063
2015-10-17 18:09:54,213 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.19242907
2015-10-17 18:09:54,291 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.10706719
2015-10-17 18:09:54,416 INFO [IPC Server handler 9 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.18637763
2015-10-17 18:09:54,431 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.19158794
2015-10-17 18:09:55,963 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.19258286
2015-10-17 18:09:57,041 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.5323719
2015-10-17 18:09:57,072 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.19266446
2015-10-17 18:09:57,244 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.19247705
2015-10-17 18:09:57,291 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.19242907
2015-10-17 18:09:57,291 INFO [IPC Server handler 9 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.19209063
2015-10-17 18:09:57,291 INFO [IPC Server handler 0 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.19212553
2015-10-17 18:09:57,369 INFO [IPC Server handler 5 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.19255035
2015-10-17 18:09:57,510 INFO [IPC Server handler 4 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.19211523
2015-10-17 18:09:57,822 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.19158794
2015-10-17 18:09:59,088 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.19258286
2015-10-17 18:10:00,119 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.5323719
2015-10-17 18:10:00,150 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.19266446
2015-10-17 18:10:00,369 INFO [IPC Server handler 5 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.19212553
2015-10-17 18:10:00,369 INFO [IPC Server handler 4 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.19209063
2015-10-17 18:10:00,369 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.19242907
2015-10-17 18:10:00,447 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.19255035
2015-10-17 18:10:01,182 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.19247705
2015-10-17 18:10:01,463 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.19211523
2015-10-17 18:10:02,119 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.19258286
2015-10-17 18:10:03,150 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.5323719
2015-10-17 18:10:03,182 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.19266446
2015-10-17 18:10:03,416 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.22331077
2015-10-17 18:10:03,416 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.22186098
2015-10-17 18:10:03,416 INFO [IPC Server handler 6 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.19212553
2015-10-17 18:10:03,510 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.19255035
2015-10-17 18:10:03,557 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.19158794
2015-10-17 18:10:04,228 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.23819141
2015-10-17 18:10:04,525 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.20697582
2015-10-17 18:10:05,197 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.24110219
2015-10-17 18:10:06,104 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.5323719
2015-10-17 18:10:06,229 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.667
2015-10-17 18:10:06,275 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.22462422
2015-10-17 18:10:06,494 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.27765483
2015-10-17 18:10:06,510 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.2781602
2015-10-17 18:10:06,510 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.2405138
2015-10-17 18:10:06,619 INFO [IPC Server handler 2 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.24371313
2015-10-17 18:10:06,635 INFO [IPC Server handler 12 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.27696857
2015-10-17 18:10:07,244 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.27813601
2015-10-17 18:10:07,557 INFO [IPC Server handler 2 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.26893634
2015-10-17 18:10:08,244 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.27811313
2015-10-17 18:10:09,354 INFO [IPC Server handler 0 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.667
2015-10-17 18:10:09,401 INFO [IPC Server handler 9 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.2783809
2015-10-17 18:10:09,604 INFO [IPC Server handler 12 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.27765483
2015-10-17 18:10:09,604 INFO [IPC Server handler 12 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.2781602
2015-10-17 18:10:09,604 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.27772525
2015-10-17 18:10:09,682 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.27825075
2015-10-17 18:10:09,697 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.27696857
2015-10-17 18:10:10,322 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.27813601
2015-10-17 18:10:10,619 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.27776006
2015-10-17 18:10:11,291 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.27811313
2015-10-17 18:10:12,479 INFO [IPC Server handler 6 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.667
2015-10-17 18:10:12,510 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.2783809
2015-10-17 18:10:12,682 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.27765483
2015-10-17 18:10:12,682 INFO [IPC Server handler 21 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.2781602
2015-10-17 18:10:12,682 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.27772525
2015-10-17 18:10:13,338 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.27813601
2015-10-17 18:10:13,651 INFO [IPC Server handler 12 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.27825075
2015-10-17 18:10:13,651 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.27696857
2015-10-17 18:10:13,666 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.27776006
2015-10-17 18:10:14,369 INFO [IPC Server handler 0 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.27811313
2015-10-17 18:10:15,573 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.70459354
2015-10-17 18:10:15,619 INFO [IPC Server handler 2 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.2783809
2015-10-17 18:10:16,385 INFO [IPC Server handler 0 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.32968736
2015-10-17 18:10:16,651 INFO [IPC Server handler 12 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.30721918
2015-10-17 18:10:16,651 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.30928624
2015-10-17 18:10:16,651 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.27772525
2015-10-17 18:10:16,698 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.33298326
2015-10-17 18:10:16,713 INFO [IPC Server handler 21 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.27776006
2015-10-17 18:10:16,713 INFO [IPC Server handler 23 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.28325725
2015-10-17 18:10:17,463 INFO [IPC Server handler 9 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.34264836
2015-10-17 18:10:18,666 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.7600004
2015-10-17 18:10:18,698 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.31975445
2015-10-17 18:10:19,495 INFO [IPC Server handler 6 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.36390656
2015-10-17 18:10:19,745 INFO [IPC Server handler 23 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.36388028
2015-10-17 18:10:19,745 INFO [IPC Server handler 21 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.36323506
2015-10-17 18:10:19,745 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.30894253
2015-10-17 18:10:19,745 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.3624012
2015-10-17 18:10:19,792 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.3638923
2015-10-17 18:10:19,792 INFO [IPC Server handler 17 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.321395
2015-10-17 18:10:20,557 INFO [IPC Server handler 5 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.3637686
2015-10-17 18:10:21,776 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.83356583
2015-10-17 18:10:21,776 INFO [IPC Server handler 17 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.36404583
2015-10-17 18:10:22,588 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.36390656
2015-10-17 18:10:22,838 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.36388028
2015-10-17 18:10:22,838 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.36323506
2015-10-17 18:10:22,885 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.3624012
2015-10-17 18:10:22,885 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.36317363
2015-10-17 18:10:22,885 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.36319977
2015-10-17 18:10:22,901 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.3638923
2015-10-17 18:10:23,667 INFO [IPC Server handler 12 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.3637686
2015-10-17 18:10:24,823 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.911346
2015-10-17 18:10:24,823 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.36404583
2015-10-17 18:10:25,682 INFO [IPC Server handler 4 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.36390656
2015-10-17 18:10:26,167 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.36388028
2015-10-17 18:10:26,182 INFO [IPC Server handler 0 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.36323506
2015-10-17 18:10:26,198 INFO [IPC Server handler 9 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.3624012
2015-10-17 18:10:26,214 INFO [IPC Server handler 6 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.36317363
2015-10-17 18:10:26,214 INFO [IPC Server handler 5 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.36319977
2015-10-17 18:10:26,229 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.3638923
2015-10-17 18:10:26,714 INFO [IPC Server handler 2 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.3637686
2015-10-17 18:10:27,901 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 0.99223256
2015-10-17 18:10:27,901 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.36404583
2015-10-17 18:10:28,323 INFO [IPC Server handler 2 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000009_0 is : 1.0
2015-10-17 18:10:28,370 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0002_m_000009_0
2015-10-17 18:10:28,370 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:10:28,370 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0002_01_000011 taskAttempt attempt_1445076437777_0002_m_000009_0
2015-10-17 18:10:28,370 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0002_m_000009_0
2015-10-17 18:10:28,401 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:10:28,604 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:10:28,620 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0002_m_000009_0
2015-10-17 18:10:28,620 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:10:28,635 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 18:10:28,760 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.42180264
2015-10-17 18:10:29,214 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0002_m_000006
2015-10-17 18:10:29,214 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:10:29,214 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0002_m_000006
2015-10-17 18:10:29,214 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:10:29,214 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:10:29,214 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000006_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:10:29,245 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.42259032
2015-10-17 18:10:29,245 INFO [IPC Server handler 2 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.43179628
2015-10-17 18:10:29,292 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.36319977
2015-10-17 18:10:29,307 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.36317363
2015-10-17 18:10:29,307 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.42738533
2015-10-17 18:10:29,323 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.43394333
2015-10-17 18:10:29,495 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 18:10:29,589 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:10:29,589 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:10:29,589 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 18:10:29,589 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:10240, vCores:-25> finalMapResourceLimit:<memory:9216, vCores:-23> finalReduceResourceLimit:<memory:1024, vCores:-2> netScheduledMapResource:<memory:11264, vCores:11> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-17 18:10:29,589 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-17 18:10:29,589 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:10 RackLocal:0
2015-10-17 18:10:29,870 INFO [IPC Server handler 23 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.3637686
2015-10-17 18:10:30,667 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0002: ask=1 release= 0 newContainers=1 finishedContainers=1 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:10:30,667 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0002_01_000011
2015-10-17 18:10:30,667 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:10:30,667 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0002_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:10:30,667 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0002_01_000012 to attempt_1445076437777_0002_m_000006_1
2015-10-17 18:10:30,667 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 18:10:30,667 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:10:30,667 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000006_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:10:30,667 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0002_01_000012 taskAttempt attempt_1445076437777_0002_m_000006_1
2015-10-17 18:10:30,667 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0002_m_000006_1
2015-10-17 18:10:30,667 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:10:31,245 INFO [IPC Server handler 9 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.44980705
2015-10-17 18:10:31,292 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0002_m_000006_1 : 13562
2015-10-17 18:10:31,292 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0002_m_000006_1] using containerId: [container_1445076437777_0002_01_000012 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:10:31,292 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000006_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:10:31,292 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0002_m_000006
2015-10-17 18:10:31,714 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:10:31,807 INFO [IPC Server handler 4 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.44950968
2015-10-17 18:10:32,307 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.44968578
2015-10-17 18:10:32,307 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.4486067
2015-10-17 18:10:32,370 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.43403906
2015-10-17 18:10:32,370 INFO [IPC Server handler 4 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.44789755
2015-10-17 18:10:32,386 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.411895
2015-10-17 18:10:32,448 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.44964966
2015-10-17 18:10:33,011 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.43256354
2015-10-17 18:10:33,792 INFO [Socket Reader #1 for port 53359] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0002 (auth:SIMPLE)
2015-10-17 18:10:33,901 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0002_m_000012 asked for a task
2015-10-17 18:10:33,901 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0002_m_000012 given task: attempt_1445076437777_0002_m_000006_1
2015-10-17 18:10:34,370 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.44980705
2015-10-17 18:10:34,886 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.44950968
2015-10-17 18:10:35,417 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.4486067
2015-10-17 18:10:35,417 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.44968578
2015-10-17 18:10:36,323 INFO [IPC Server handler 9 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.44789755
2015-10-17 18:10:36,323 INFO [IPC Server handler 2 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.448704
2015-10-17 18:10:36,386 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.44859612
2015-10-17 18:10:36,386 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.44950172
2015-10-17 18:10:36,401 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.44964966
2015-10-17 18:10:37,464 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.44980705
2015-10-17 18:10:37,933 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.44950968
2015-10-17 18:10:38,526 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.44968578
2015-10-17 18:10:38,526 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.4486067
2015-10-17 18:10:39,339 INFO [IPC Server handler 2 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.448704
2015-10-17 18:10:39,339 INFO [IPC Server handler 0 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.44789755
2015-10-17 18:10:39,417 INFO [IPC Server handler 5 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.44859612
2015-10-17 18:10:39,433 INFO [IPC Server handler 6 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.44964966
2015-10-17 18:10:39,448 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.44950172
2015-10-17 18:10:40,511 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.44980705
2015-10-17 18:10:40,964 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.48087052
2015-10-17 18:10:41,401 INFO [IPC Server handler 9 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.106468864
2015-10-17 18:10:41,589 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.4804043
2015-10-17 18:10:41,589 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.46807557
2015-10-17 18:10:42,386 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.4602772
2015-10-17 18:10:42,386 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.48619273
2015-10-17 18:10:42,433 INFO [IPC Server handler 0 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.44859612
2015-10-17 18:10:42,495 INFO [IPC Server handler 5 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.44950172
2015-10-17 18:10:42,808 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.49166283
2015-10-17 18:10:43,573 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.45771047
2015-10-17 18:10:43,980 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.5352021
2015-10-17 18:10:44,245 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0002_m_000002
2015-10-17 18:10:44,245 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:10:44,245 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0002_m_000002
2015-10-17 18:10:44,245 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:10:44,245 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:10:44,245 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000002_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:10:44,308 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 18:10:44,323 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0002: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:10:44,448 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.106964506
2015-10-17 18:10:45,417 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.53341997
2015-10-17 18:10:45,417 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.53425497
2015-10-17 18:10:45,511 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.51903635
2015-10-17 18:10:45,542 INFO [IPC Server handler 0 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.5352028
2015-10-17 18:10:45,542 INFO [IPC Server handler 2 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.5343203
2015-10-17 18:10:45,558 INFO [IPC Server handler 2 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.44950172
2015-10-17 18:10:45,886 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.5352825
2015-10-17 18:10:46,652 INFO [IPC Server handler 5 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.5200392
2015-10-17 18:10:47,245 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.5352021
2015-10-17 18:10:47,495 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.106964506
2015-10-17 18:10:48,433 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.53425497
2015-10-17 18:10:48,449 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.53341997
2015-10-17 18:10:48,527 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.5342037
2015-10-17 18:10:48,605 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.44950172
2015-10-17 18:10:48,605 INFO [IPC Server handler 17 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.5352028
2015-10-17 18:10:48,605 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.5343203
2015-10-17 18:10:48,949 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.5352825
2015-10-17 18:10:49,699 INFO [IPC Server handler 5 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.53543663
2015-10-17 18:10:50,277 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.5352021
2015-10-17 18:10:50,558 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.106964506
2015-10-17 18:10:51,496 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.53425497
2015-10-17 18:10:51,496 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.53341997
2015-10-17 18:10:51,589 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.5342037
2015-10-17 18:10:51,652 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.5352028
2015-10-17 18:10:51,652 INFO [IPC Server handler 23 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.5343203
2015-10-17 18:10:51,714 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.53521925
2015-10-17 18:10:52,027 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.5352825
2015-10-17 18:10:53,574 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.106964506
2015-10-17 18:10:53,636 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.53543663
2015-10-17 18:10:53,714 INFO [IPC Server handler 23 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.5908801
2015-10-17 18:10:54,527 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.5620181
2015-10-17 18:10:54,527 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.57389736
2015-10-17 18:10:54,605 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.5342037
2015-10-17 18:10:54,667 INFO [IPC Server handler 23 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.58318007
2015-10-17 18:10:54,667 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.60066
2015-10-17 18:10:54,730 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.53521925
2015-10-17 18:10:55,042 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.5435147
2015-10-17 18:10:56,636 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.19266446
2015-10-17 18:10:56,668 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.56752515
2015-10-17 18:10:56,730 INFO [IPC Server handler 23 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.6209487
2015-10-17 18:10:57,589 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.6197233
2015-10-17 18:10:57,589 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.61898744
2015-10-17 18:10:57,652 INFO [IPC Server handler 12 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.5974824
2015-10-17 18:10:57,683 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.6208445
2015-10-17 18:10:57,683 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.6199081
2015-10-17 18:10:57,777 INFO [IPC Server handler 23 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.53521925
2015-10-17 18:10:58,074 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.620844
2015-10-17 18:10:59,668 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.19266446
2015-10-17 18:10:59,761 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.6209487
2015-10-17 18:11:00,027 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.6210422
2015-10-17 18:11:01,246 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.61898744
2015-10-17 18:11:01,246 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.6199081
2015-10-17 18:11:01,246 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.6208445
2015-10-17 18:11:01,246 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.6196791
2015-10-17 18:11:01,246 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.6197233
2015-10-17 18:11:01,246 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.58057344
2015-10-17 18:11:01,402 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.620844
2015-10-17 18:11:02,730 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.19266446
2015-10-17 18:11:02,793 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.6209487
2015-10-17 18:11:03,105 INFO [IPC Server handler 6 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.6210422
2015-10-17 18:11:04,262 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.6197233
2015-10-17 18:11:04,262 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.6199081
2015-10-17 18:11:04,262 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.6196791
2015-10-17 18:11:04,277 INFO [IPC Server handler 4 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.61898744
2015-10-17 18:11:04,293 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.6208445
2015-10-17 18:11:04,293 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.6207798
2015-10-17 18:11:04,433 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.620844
2015-10-17 18:11:05,512 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.6209487
2015-10-17 18:11:06,293 INFO [IPC Server handler 4 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.23332548
2015-10-17 18:11:06,293 INFO [IPC Server handler 4 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.667
2015-10-17 18:11:06,449 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.6210422
2015-10-17 18:11:06,496 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.6199081
2015-10-17 18:11:06,574 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.620844
2015-10-17 18:11:07,324 INFO [IPC Server handler 4 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.667
2015-10-17 18:11:07,324 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.65896565
2015-10-17 18:11:07,340 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.6207798
2015-10-17 18:11:07,340 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.66665334
2015-10-17 18:11:07,340 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.64334345
2015-10-17 18:11:07,340 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.6196791
2015-10-17 18:11:07,371 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.66665334
2015-10-17 18:11:07,512 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.667
2015-10-17 18:11:07,730 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.65896565
2015-10-17 18:11:08,058 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.64334345
2015-10-17 18:11:08,074 INFO [IPC Server handler 17 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.6210422
2015-10-17 18:11:09,402 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.667
2015-10-17 18:11:09,402 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.2783809
2015-10-17 18:11:09,574 INFO [IPC Server handler 12 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.667
2015-10-17 18:11:10,121 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.6196791
2015-10-17 18:11:10,387 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.667
2015-10-17 18:11:10,387 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.667
2015-10-17 18:11:10,402 INFO [IPC Server handler 12 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.6207798
2015-10-17 18:11:10,402 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.667
2015-10-17 18:11:10,434 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.667
2015-10-17 18:11:10,434 INFO [IPC Server handler 23 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.667
2015-10-17 18:11:10,574 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.667
2015-10-17 18:11:12,481 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.667
2015-10-17 18:11:12,481 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.2783809
2015-10-17 18:11:13,418 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.667
2015-10-17 18:11:13,434 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.667
2015-10-17 18:11:13,434 INFO [IPC Server handler 12 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.667
2015-10-17 18:11:13,449 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.667
2015-10-17 18:11:13,481 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.667
2015-10-17 18:11:13,512 INFO [IPC Server handler 21 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.667
2015-10-17 18:11:13,512 INFO [IPC Server handler 17 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.6207798
2015-10-17 18:11:13,512 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.667
2015-10-17 18:11:13,637 INFO [IPC Server handler 2 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.6670068
2015-10-17 18:11:15,496 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.6900045
2015-10-17 18:11:15,527 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.2783809
2015-10-17 18:11:16,434 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.6794198
2015-10-17 18:11:16,496 INFO [IPC Server handler 23 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.667
2015-10-17 18:11:16,496 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.667
2015-10-17 18:11:16,496 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.6689798
2015-10-17 18:11:16,512 INFO [IPC Server handler 17 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.67393017
2015-10-17 18:11:16,559 INFO [IPC Server handler 2 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.667
2015-10-17 18:11:16,559 INFO [IPC Server handler 0 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.667
2015-10-17 18:11:16,684 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.6810913
2015-10-17 18:11:18,528 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.7236774
2015-10-17 18:11:19,278 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.2783809
2015-10-17 18:11:19,496 INFO [IPC Server handler 23 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.7084389
2015-10-17 18:11:19,574 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.667
2015-10-17 18:11:19,574 INFO [IPC Server handler 2 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.7070379
2015-10-17 18:11:19,574 INFO [IPC Server handler 0 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.6897155
2015-10-17 18:11:19,574 INFO [IPC Server handler 21 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.6951219
2015-10-17 18:11:19,606 INFO [IPC Server handler 9 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.667
2015-10-17 18:11:19,606 INFO [IPC Server handler 5 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.67622054
2015-10-17 18:11:19,746 INFO [IPC Server handler 6 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.698159
2015-10-17 18:11:21,637 INFO [IPC Server handler 6 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.7581113
2015-10-17 18:11:22,340 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.35673007
2015-10-17 18:11:22,528 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.74203914
2015-10-17 18:11:22,606 INFO [IPC Server handler 5 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.74111116
2015-10-17 18:11:22,606 INFO [IPC Server handler 9 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.69079053
2015-10-17 18:11:22,621 INFO [IPC Server handler 6 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.7236077
2015-10-17 18:11:22,621 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.7227776
2015-10-17 18:11:22,653 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.68272907
2015-10-17 18:11:22,684 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.6989311
2015-10-17 18:11:22,809 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.72110397
2015-10-17 18:11:24,668 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.7934222
2015-10-17 18:11:25,372 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.36404583
2015-10-17 18:11:25,559 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.7763625
2015-10-17 18:11:25,637 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.71894044
2015-10-17 18:11:25,637 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.7583986
2015-10-17 18:11:25,637 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.77665585
2015-10-17 18:11:25,653 INFO [IPC Server handler 6 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.7539213
2015-10-17 18:11:25,700 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.7184567
2015-10-17 18:11:25,747 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.7230493
2015-10-17 18:11:25,840 INFO [IPC Server handler 23 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.74635434
2015-10-17 18:11:27,731 INFO [IPC Server handler 7 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.82884675
2015-10-17 18:11:28,450 INFO [IPC Server handler 4 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.36404583
2015-10-17 18:11:28,637 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.8091957
2015-10-17 18:11:28,715 INFO [IPC Server handler 9 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.79028964
2015-10-17 18:11:28,715 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.81107736
2015-10-17 18:11:28,715 INFO [IPC Server handler 0 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.79307765
2015-10-17 18:11:28,715 INFO [IPC Server handler 6 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.7535849
2015-10-17 18:11:28,778 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.75621164
2015-10-17 18:11:28,809 INFO [IPC Server handler 23 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.7610495
2015-10-17 18:11:29,794 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.78452635
2015-10-17 18:11:30,778 INFO [IPC Server handler 5 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.86620194
2015-10-17 18:11:31,544 INFO [IPC Server handler 4 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.36404583
2015-10-17 18:11:31,700 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.84276646
2015-10-17 18:11:31,778 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.8458669
2015-10-17 18:11:31,778 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.78578496
2015-10-17 18:11:31,778 INFO [IPC Server handler 0 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.82874745
2015-10-17 18:11:31,778 INFO [IPC Server handler 6 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.8218626
2015-10-17 18:11:31,872 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.7945863
2015-10-17 18:11:31,887 INFO [IPC Server handler 4 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.7988789
2015-10-17 18:11:32,903 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.83445036
2015-10-17 18:11:33,856 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.8984712
2015-10-17 18:11:34,622 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.44980705
2015-10-17 18:11:34,762 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.8742123
2015-10-17 18:11:34,856 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.8784965
2015-10-17 18:11:34,856 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.8143009
2015-10-17 18:11:34,903 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.85277325
2015-10-17 18:11:34,903 INFO [IPC Server handler 13 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.86226785
2015-10-17 18:11:34,981 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.82366073
2015-10-17 18:11:34,981 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.83555734
2015-10-17 18:11:36,044 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.8670404
2015-10-17 18:11:36,887 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.9340887
2015-10-17 18:11:37,669 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.44980705
2015-10-17 18:11:37,794 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.9074969
2015-10-17 18:11:37,903 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.84909976
2015-10-17 18:11:37,903 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.91278785
2015-10-17 18:11:37,950 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.88867545
2015-10-17 18:11:37,950 INFO [IPC Server handler 9 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.8980509
2015-10-17 18:11:38,091 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.83824515
2015-10-17 18:11:38,091 INFO [IPC Server handler 23 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.86090153
2015-10-17 18:11:39,403 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.89758146
2015-10-17 18:11:39,950 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.96415186
2015-10-17 18:11:41,419 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.85925364
2015-10-17 18:11:41,434 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.8833475
2015-10-17 18:11:41,622 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.44980705
2015-10-17 18:11:41,716 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.9350749
2015-10-17 18:11:41,841 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.94172823
2015-10-17 18:11:41,841 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.8858973
2015-10-17 18:11:41,935 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.9269316
2015-10-17 18:11:41,935 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.9265441
2015-10-17 18:11:42,497 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.91537094
2015-10-17 18:11:43,888 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 0.99594784
2015-10-17 18:11:43,935 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000005_0 is : 1.0
2015-10-17 18:11:43,950 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0002_m_000005_0
2015-10-17 18:11:43,950 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:11:43,950 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0002_01_000007 taskAttempt attempt_1445076437777_0002_m_000005_0
2015-10-17 18:11:43,950 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0002_m_000005_0
2015-10-17 18:11:43,950 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:11:44,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:11:44,185 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0002_m_000005_0
2015-10-17 18:11:44,185 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:11:44,185 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 18:11:44,466 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.8954757
2015-10-17 18:11:44,481 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.9007541
2015-10-17 18:11:44,638 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:2 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 18:11:44,669 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.44980705
2015-10-17 18:11:44,763 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 0.97730553
2015-10-17 18:11:44,935 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 0.9849105
2015-10-17 18:11:44,950 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.9353545
2015-10-17 18:11:44,966 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 0.97605294
2015-10-17 18:11:44,981 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 0.9727006
2015-10-17 18:11:45,560 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.9332427
2015-10-17 18:11:46,341 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0002_01_000007
2015-10-17 18:11:46,341 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:11:46,341 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0002_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:11:46,341 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 18:11:46,341 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0002_01_000013 to attempt_1445076437777_0002_r_000000_0
2015-10-17 18:11:46,341 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 18:11:46,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:11:46,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:11:46,357 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0002_01_000013 taskAttempt attempt_1445076437777_0002_r_000000_0
2015-10-17 18:11:46,372 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0002_r_000000_0
2015-10-17 18:11:46,372 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:11:46,388 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000004_0 is : 1.0
2015-10-17 18:11:46,466 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0002_m_000004_0
2015-10-17 18:11:46,466 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:11:46,466 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0002_01_000006 taskAttempt attempt_1445076437777_0002_m_000004_0
2015-10-17 18:11:46,466 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0002_m_000004_0
2015-10-17 18:11:46,466 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:11:46,607 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0002_r_000000_0 : 13562
2015-10-17 18:11:46,607 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0002_r_000000_0] using containerId: [container_1445076437777_0002_01_000013 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:11:46,607 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:11:46,607 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0002_r_000000
2015-10-17 18:11:46,607 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:11:46,622 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:11:46,622 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0002_m_000004_0
2015-10-17 18:11:46,622 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:11:46,622 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 18:11:46,935 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000003_0 is : 1.0
2015-10-17 18:11:47,341 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 18:11:47,357 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0002_m_000003_0
2015-10-17 18:11:47,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:11:47,357 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0002: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:11:47,357 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0002_01_000005 taskAttempt attempt_1445076437777_0002_m_000003_0
2015-10-17 18:11:47,357 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0002_m_000003_0
2015-10-17 18:11:47,357 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:11:47,450 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000001_0 is : 1.0
2015-10-17 18:11:47,466 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0002_m_000001_0
2015-10-17 18:11:47,466 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:11:47,466 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0002_01_000003 taskAttempt attempt_1445076437777_0002_m_000001_0
2015-10-17 18:11:47,466 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0002_m_000001_0
2015-10-17 18:11:47,466 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:11:47,466 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:11:47,466 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0002_m_000003_0
2015-10-17 18:11:47,466 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:11:47,466 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 18:11:47,497 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.93112326
2015-10-17 18:11:47,497 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000000_0 is : 1.0
2015-10-17 18:11:47,513 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.92155516
2015-10-17 18:11:47,544 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0002_m_000000_0
2015-10-17 18:11:47,544 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:11:47,591 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0002_01_000002 taskAttempt attempt_1445076437777_0002_m_000000_0
2015-10-17 18:11:47,591 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0002_m_000000_0
2015-10-17 18:11:47,591 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:11:47,622 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:11:47,622 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0002_m_000001_0
2015-10-17 18:11:47,622 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:11:47,622 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 18:11:47,778 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.44980705
2015-10-17 18:11:47,903 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:11:47,919 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0002_m_000000_0
2015-10-17 18:11:47,919 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:11:47,919 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 18:11:47,982 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.9659891
2015-10-17 18:11:48,372 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:11 RackLocal:0
2015-10-17 18:11:48,450 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0002_01_000006
2015-10-17 18:11:48,450 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0002_01_000005
2015-10-17 18:11:48,450 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0002_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:11:48,450 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0002_01_000003
2015-10-17 18:11:48,450 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0002_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:11:48,450 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:11:48,450 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0002_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:11:48,450 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0002_01_000014 to attempt_1445076437777_0002_m_000002_1
2015-10-17 18:11:48,450 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 18:11:48,450 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:11:48,450 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000002_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:11:48,450 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0002_01_000014 taskAttempt attempt_1445076437777_0002_m_000002_1
2015-10-17 18:11:48,450 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0002_m_000002_1
2015-10-17 18:11:48,450 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:11:48,653 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0002_m_000002_1 : 13562
2015-10-17 18:11:48,653 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.95685065
2015-10-17 18:11:48,653 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0002_m_000002_1] using containerId: [container_1445076437777_0002_01_000014 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:11:48,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000002_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:11:48,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0002_m_000002
2015-10-17 18:11:49,466 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0002: ask=4 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:11:49,466 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0002_01_000002
2015-10-17 18:11:49,466 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 18:11:49,466 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0002_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:11:50,013 INFO [Socket Reader #1 for port 53359] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0002 (auth:SIMPLE)
2015-10-17 18:11:50,060 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0002_r_000013 asked for a task
2015-10-17 18:11:50,060 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0002_r_000013 given task: attempt_1445076437777_0002_r_000000_0
2015-10-17 18:11:50,560 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 0.967652
2015-10-17 18:11:50,560 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.94819546
2015-10-17 18:11:50,950 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_1 is : 0.53067434
2015-10-17 18:11:52,341 INFO [IPC Server handler 11 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 0.98824143
2015-10-17 18:11:52,341 INFO [IPC Server handler 28 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0002_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 18:11:52,341 INFO [IPC Server handler 25 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 0.9917122
2015-10-17 18:11:52,372 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000002_0 is : 1.0
2015-10-17 18:11:52,388 INFO [IPC Server handler 16 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0002_m_000002_0
2015-10-17 18:11:52,404 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:11:52,404 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0002_01_000004 taskAttempt attempt_1445076437777_0002_m_000002_0
2015-10-17 18:11:52,404 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0002_m_000002_0
2015-10-17 18:11:52,404 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:11:52,591 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:11:52,591 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0002_m_000002_0
2015-10-17 18:11:52,591 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445076437777_0002_m_000002_1
2015-10-17 18:11:52,591 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:11:52,591 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 18:11:52,591 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000002_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 18:11:52,591 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0002_01_000014 taskAttempt attempt_1445076437777_0002_m_000002_1
2015-10-17 18:11:52,591 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0002_m_000002_1
2015-10-17 18:11:52,591 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:11:52,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 18:11:52,732 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000002_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 18:11:52,747 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000007_0 is : 1.0
2015-10-17 18:11:52,763 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0002_m_000007_0
2015-10-17 18:11:52,763 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:11:52,763 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0002_01_000009 taskAttempt attempt_1445076437777_0002_m_000007_0
2015-10-17 18:11:52,810 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 18:11:52,810 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0002_m_000007_0
2015-10-17 18:11:52,810 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:11:52,810 INFO [Socket Reader #1 for port 53359] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 53359: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 18:11:53,044 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/1/_temporary/attempt_1445076437777_0002_m_000002_1
2015-10-17 18:11:53,044 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000002_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 18:11:53,091 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:11:53,091 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0002_m_000007_0
2015-10-17 18:11:53,091 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:11:53,091 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 18:11:53,419 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0002_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:11:53,419 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000008_0 is : 1.0
2015-10-17 18:11:53,435 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0002_m_000008_0
2015-10-17 18:11:53,435 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:11:53,435 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0002_01_000010 taskAttempt attempt_1445076437777_0002_m_000008_0
2015-10-17 18:11:53,435 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0002_m_000008_0
2015-10-17 18:11:53,435 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:11:53,654 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 18:11:53,654 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 0.9833774
2015-10-17 18:11:53,669 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0002_01_000004
2015-10-17 18:11:53,669 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0002_01_000009
2015-10-17 18:11:53,669 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 18:11:53,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:11:53,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0002_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:11:53,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0002_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:11:53,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0002_m_000008_0
2015-10-17 18:11:53,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:11:53,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 18:11:54,435 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0002_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:11:54,716 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 18:11:55,451 INFO [IPC Server handler 19 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:11:55,529 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_m_000006_0 is : 1.0
2015-10-17 18:11:55,544 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0002_m_000006_0
2015-10-17 18:11:55,544 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:11:55,544 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0002_01_000008 taskAttempt attempt_1445076437777_0002_m_000006_0
2015-10-17 18:11:55,544 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0002_m_000006_0
2015-10-17 18:11:55,544 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:11:56,497 INFO [IPC Server handler 6 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:11:56,716 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:11:56,716 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0002_m_000006_0
2015-10-17 18:11:56,716 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445076437777_0002_m_000006_1
2015-10-17 18:11:56,716 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:11:56,716 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 18:11:56,716 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000006_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 18:11:56,716 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0002_01_000012 taskAttempt attempt_1445076437777_0002_m_000006_1
2015-10-17 18:11:56,716 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0002_m_000006_1
2015-10-17 18:11:56,716 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:11:57,107 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000006_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 18:11:57,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0002_01_000014
2015-10-17 18:11:57,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0002_01_000010
2015-10-17 18:11:57,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0002_m_000002_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:11:57,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 18:11:57,169 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0002_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:11:57,201 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 18:11:57,216 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/1/_temporary/attempt_1445076437777_0002_m_000006_1
2015-10-17 18:11:57,216 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_m_000006_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 18:11:57,529 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0002_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:11:57,701 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.3
2015-10-17 18:11:58,357 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0002_01_000008
2015-10-17 18:11:58,357 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0002_01_000012
2015-10-17 18:11:58,357 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 18:11:58,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0002_m_000006_0: 
2015-10-17 18:11:58,357 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0002_m_000006_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:11:58,372 INFO [IPC Server handler 22 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.3
2015-10-17 18:11:58,419 INFO [IPC Server handler 8 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.3
2015-10-17 18:12:00,763 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.6769831
2015-10-17 18:12:05,951 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.6954165
2015-10-17 18:12:08,998 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.6974443
2015-10-17 18:12:21,639 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.70429844
2015-10-17 18:12:24,701 INFO [IPC Server handler 17 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.71517754
2015-10-17 18:12:27,780 INFO [IPC Server handler 17 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.72965014
2015-10-17 18:12:31,436 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.7455112
2015-10-17 18:12:34,483 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.7681022
2015-10-17 18:12:37,577 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.7871748
2015-10-17 18:12:40,608 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.8030871
2015-10-17 18:12:43,952 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.8118752
2015-10-17 18:12:47,202 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.8118752
2015-10-17 18:12:53,921 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.8139291
2015-10-17 18:12:57,046 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.82375133
2015-10-17 18:13:00,765 INFO [IPC Server handler 24 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.82968974
2015-10-17 18:13:03,781 INFO [IPC Server handler 1 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.83340013
2015-10-17 18:13:06,859 INFO [IPC Server handler 26 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.84421515
2015-10-17 18:13:09,890 INFO [IPC Server handler 3 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.849499
2015-10-17 18:13:13,000 INFO [IPC Server handler 10 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.8543539
2015-10-17 18:13:16,172 INFO [IPC Server handler 17 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.86066914
2015-10-17 18:13:19,266 INFO [IPC Server handler 9 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.86609447
2015-10-17 18:13:22,703 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.8712685
2015-10-17 18:13:25,813 INFO [IPC Server handler 12 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.877207
2015-10-17 18:13:28,844 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.88502604
2015-10-17 18:13:31,953 INFO [IPC Server handler 21 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.8897636
2015-10-17 18:13:35,063 INFO [IPC Server handler 5 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.8916876
2015-10-17 18:13:38,469 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.89570326
2015-10-17 18:13:41,563 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.9022963
2015-10-17 18:13:44,657 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.9049386
2015-10-17 18:13:47,735 INFO [IPC Server handler 12 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.90912044
2015-10-17 18:13:50,829 INFO [IPC Server handler 21 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.91556656
2015-10-17 18:13:53,923 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.92863464
2015-10-17 18:13:57,001 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.9438576
2015-10-17 18:14:00,079 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.96136796
2015-10-17 18:14:03,204 INFO [IPC Server handler 18 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.9732313
2015-10-17 18:14:07,142 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.97851527
2015-10-17 18:14:10,251 INFO [IPC Server handler 21 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 0.99384475
2015-10-17 18:14:13,408 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 1.0
2015-10-17 18:14:16,736 INFO [IPC Server handler 14 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 1.0
2015-10-17 18:14:21,283 INFO [IPC Server handler 15 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445076437777_0002_r_000000_0
2015-10-17 18:14:21,283 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-17 18:14:21,283 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445076437777_0002_r_000000_0 given a go for committing the task output.
2015-10-17 18:14:21,314 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445076437777_0002_r_000000_0
2015-10-17 18:14:21,314 INFO [IPC Server handler 27 on 53359] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445076437777_0002_r_000000_0:true
2015-10-17 18:14:21,392 INFO [IPC Server handler 20 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0002_r_000000_0 is : 1.0
2015-10-17 18:14:21,408 INFO [IPC Server handler 29 on 53359] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0002_r_000000_0
2015-10-17 18:14:21,408 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:14:21,408 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0002_01_000013 taskAttempt attempt_1445076437777_0002_r_000000_0
2015-10-17 18:14:21,424 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0002_r_000000_0
2015-10-17 18:14:21,424 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:14:21,955 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0002_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:14:21,955 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0002_r_000000_0
2015-10-17 18:14:21,955 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0002_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:14:21,955 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 18:14:21,955 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0002Job Transitioned from RUNNING to COMMITTING
2015-10-17 18:14:22,096 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-17 18:14:22,986 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 18:14:23,002 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0002_01_000013
2015-10-17 18:14:23,002 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 18:14:23,002 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0002_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:14:23,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-17 18:14:23,096 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0002Job Transitioned from COMMITTING to SUCCEEDED
2015-10-17 18:14:23,252 INFO [Thread-98] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-17 18:14:23,252 INFO [Thread-98] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-17 18:14:23,252 INFO [Thread-98] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-17 18:14:23,252 INFO [Thread-98] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-17 18:14:23,252 INFO [Thread-98] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-17 18:14:23,252 INFO [Thread-98] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-17 18:14:23,252 INFO [Thread-98] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-17 18:14:29,189 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0002/job_1445076437777_0002_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0002-1445076557428-msrabi-pagerank-1445076863096-10-1-SUCCEEDED-default-1445076564680.jhist_tmp
2015-10-17 18:14:31,424 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0002-1445076557428-msrabi-pagerank-1445076863096-10-1-SUCCEEDED-default-1445076564680.jhist_tmp
2015-10-17 18:14:31,518 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0002/job_1445076437777_0002_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0002_conf.xml_tmp
2015-10-17 18:14:34,205 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0002_conf.xml_tmp
2015-10-17 18:14:34,268 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0002.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0002.summary
2015-10-17 18:14:34,283 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0002_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0002_conf.xml
2015-10-17 18:14:34,283 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0002-1445076557428-msrabi-pagerank-1445076863096-10-1-SUCCEEDED-default-1445076564680.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0002-1445076557428-msrabi-pagerank-1445076863096-10-1-SUCCEEDED-default-1445076564680.jhist
2015-10-17 18:14:34,299 INFO [Thread-98] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-17 18:14:34,299 INFO [Thread-98] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-17 18:14:34,299 INFO [Thread-98] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://04DN8IQ.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445076437777_0002
2015-10-17 18:14:34,377 INFO [Thread-98] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-17 18:14:35,424 INFO [Thread-98] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:13 ContRel:0 HostLocal:12 RackLocal:0
2015-10-17 18:14:35,424 INFO [Thread-98] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0002
2015-10-17 18:14:35,502 INFO [Thread-98] org.apache.hadoop.ipc.Server: Stopping server on 53359
2015-10-17 18:14:35,565 INFO [IPC Server listener on 53359] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 53359
2015-10-17 18:14:35,565 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-17 18:14:35,565 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
