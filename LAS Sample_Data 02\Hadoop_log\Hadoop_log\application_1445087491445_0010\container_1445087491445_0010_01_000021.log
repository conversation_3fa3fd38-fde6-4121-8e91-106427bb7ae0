2015-10-17 22:44:56,814 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 22:44:56,870 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 22:44:56,870 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 22:44:56,885 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 22:44:56,885 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445087491445_0010, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@1c667739)
2015-10-17 22:44:56,982 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 22:44:57,589 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445087491445_0010
2015-10-17 22:44:58,685 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 22:44:59,477 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 22:44:59,516 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@423d01aa
2015-10-17 22:44:59,925 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:671088640+134217728
2015-10-17 22:45:00,051 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 22:45:00,051 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 22:45:00,051 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 22:45:00,051 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 22:45:00,051 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 22:45:00,065 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 22:45:02,861 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:45:02,861 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34177286; bufvoid = 104857600
2015-10-17 22:45:02,861 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13787204(55148816); length = 12427193/6553600
2015-10-17 22:45:02,861 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44663043 kvi 11165756(44663024)
2015-10-17 22:45:13,298 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 22:45:14,001 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44663043 kv 11165756(44663024) kvi 8544328(34177312)
2015-10-17 22:45:15,315 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:45:15,315 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44663043; bufend = 78834546; bufvoid = 104857600
2015-10-17 22:45:15,315 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11165756(44663024); kvend = 24951520(99806080); length = 12428637/6553600
2015-10-17 22:45:15,315 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89320305 kvi 22330072(89320288)
2015-10-17 22:45:23,893 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 22:45:23,897 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 89320305 kv 22330072(89320288) kvi 19708644(78834576)
2015-10-17 22:45:24,779 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:45:24,779 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 89320305; bufend = 18636739; bufvoid = 104857592
2015-10-17 22:45:24,779 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 22330072(89320288); kvend = 9902068(39608272); length = 12428005/6553600
2015-10-17 22:45:24,780 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29122497 kvi 7280620(29122480)
2015-10-17 22:45:34,646 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 22:45:34,650 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29122497 kv 7280620(29122480) kvi 4659192(18636768)
2015-10-17 22:45:35,504 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:45:35,504 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29122497; bufend = 63299153; bufvoid = 104857600
2015-10-17 22:45:35,504 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7280620(29122480); kvend = 21067672(84270688); length = 12427349/6553600
2015-10-17 22:45:35,504 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 73784912 kvi 18446224(73784896)
2015-10-17 22:45:44,427 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 22:45:44,433 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 73784912 kv 18446224(73784896) kvi 15824796(63299184)
2015-10-17 22:45:45,231 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:45:45,231 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 73784912; bufend = 3103634; bufvoid = 104857599
2015-10-17 22:45:45,231 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 18446224(73784896); kvend = 6018792(24075168); length = 12427433/6553600
2015-10-17 22:45:45,231 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 13589393 kvi 3397344(13589376)
2015-10-17 22:45:55,245 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 22:45:55,248 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 13589393 kv 3397344(13589376) kvi 775916(3103664)
2015-10-17 22:45:56,417 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:45:56,417 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 13589393; bufend = 47762632; bufvoid = 104857600
2015-10-17 22:45:56,418 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 3397344(13589376); kvend = 17183540(68734160); length = 12428205/6553600
2015-10-17 22:45:56,418 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 58248388 kvi 14562092(58248368)
2015-10-17 22:45:57,028 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-17 22:46:05,466 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-17 22:46:05,469 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 58248388 kv 14562092(58248368) kvi 12514280(50057120)
2015-10-17 22:46:05,469 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 22:46:05,469 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 58248388; bufend = 63878123; bufvoid = 104857600
2015-10-17 22:46:05,469 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14562092(58248368); kvend = 12514284(50057136); length = 2047809/6553600
2015-10-17 22:46:06,464 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-17 22:46:06,479 INFO [main] org.apache.hadoop.mapred.Merger: Merging 7 sorted segments
2015-10-17 22:46:06,488 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 7 segments left of total size: 228412824 bytes
2015-10-17 22:46:33,023 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445087491445_0010_m_000005_2 is done. And is in the process of committing
2015-10-17 22:46:33,097 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445087491445_0010_m_000005_2' done.
