2015-10-19 17:53:02,630 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445182159119_0018_000002
2015-10-19 17:53:04,224 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-19 17:53:04,224 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 18 cluster_timestamp: 1445182159119 } attemptId: 2 } keyId: 1694045684)
2015-10-19 17:53:04,630 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-19 17:53:06,724 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-19 17:53:06,881 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-19 17:53:06,943 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-19 17:53:06,943 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-19 17:53:06,943 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-19 17:53:06,959 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-19 17:53:06,959 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-19 17:53:06,959 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-19 17:53:06,959 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-19 17:53:06,959 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-19 17:53:07,115 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:53:07,162 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:53:07,193 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:53:07,209 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-19 17:53:07,224 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Recovery is enabled. Will try to recover from previous life on best effort basis.
2015-10-19 17:53:07,271 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:53:07,287 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Previous history file is at hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0018/job_1445182159119_0018_1.jhist
2015-10-19 17:53:07,334 WARN [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Unable to parse prior job history, aborting recovery
java.io.IOException: Incompatible event log version: null
	at org.apache.hadoop.mapreduce.jobhistory.EventReader.<init>(EventReader.java:71)
	at org.apache.hadoop.mapreduce.jobhistory.JobHistoryParser.parse(JobHistoryParser.java:139)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.parsePreviousJobHistory(MRAppMaster.java:1183)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.processRecovery(MRAppMaster.java:1152)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.serviceStart(MRAppMaster.java:1016)
	at org.apache.hadoop.service.AbstractService.start(AbstractService.java:193)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster$4.run(MRAppMaster.java:1500)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.initAndStartAppMaster(MRAppMaster.java:1496)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.main(MRAppMaster.java:1429)
2015-10-19 17:53:07,381 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 17:53:07,381 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Previous history file is at hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0018/job_1445182159119_0018_1.jhist
2015-10-19 17:53:07,490 WARN [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Could not parse the old history file. Will not have old AMinfos 
java.io.IOException: Incompatible event log version: null
	at org.apache.hadoop.mapreduce.jobhistory.EventReader.<init>(EventReader.java:71)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.readJustAMInfos(MRAppMaster.java:1229)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.processRecovery(MRAppMaster.java:1156)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.serviceStart(MRAppMaster.java:1016)
	at org.apache.hadoop.service.AbstractService.start(AbstractService.java:193)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster$4.run(MRAppMaster.java:1500)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.initAndStartAppMaster(MRAppMaster.java:1496)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.main(MRAppMaster.java:1429)
2015-10-19 17:53:07,599 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-19 17:53:08,193 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 17:53:08,334 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 17:53:08,334 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-19 17:53:08,349 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445182159119_0018 to jobTokenSecretManager
2015-10-19 17:53:12,334 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445182159119_0018 because: not enabled; too many maps; too much input;
2015-10-19 17:53:12,396 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445182159119_0018 = 1256521728. Number of splits = 10
2015-10-19 17:53:12,396 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445182159119_0018 = 1
2015-10-19 17:53:12,396 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0018Job Transitioned from NEW to INITED
2015-10-19 17:53:12,396 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445182159119_0018.
2015-10-19 17:53:12,459 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 17:53:12,537 INFO [Socket Reader #1 for port 64917] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 64917
2015-10-19 17:53:12,537 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-19 17:53:12,553 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MININT-FNANLI5.fareast.corp.microsoft.com/*************:64917
2015-10-19 17:53:12,568 INFO [IPC Server listener on 64917] org.apache.hadoop.ipc.Server: IPC Server listener on 64917: starting
2015-10-19 17:53:12,568 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 17:53:12,709 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-19 17:53:12,709 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-19 17:53:12,740 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-19 17:53:12,740 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-19 17:53:12,740 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-19 17:53:12,740 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-19 17:53:12,756 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-19 17:53:12,772 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 64924
2015-10-19 17:53:12,772 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-19 17:53:12,850 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_64924_mapreduce____.qllwx0\webapp
2015-10-19 17:53:13,568 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:64924
2015-10-19 17:53:13,568 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 64924
2015-10-19 17:53:14,365 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-19 17:53:14,381 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 17:53:14,428 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-19 17:53:14,428 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-19 17:53:14,428 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-19 17:53:14,709 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-19 17:53:16,490 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445182159119_0018
2015-10-19 17:53:16,506 INFO [IPC Server listener on 64927] org.apache.hadoop.ipc.Server: IPC Server listener on 64927: starting
2015-10-19 17:53:16,506 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 17:53:16,506 INFO [Socket Reader #1 for port 64927] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 64927
2015-10-19 17:53:16,662 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-19 17:53:16,662 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-19 17:53:16,678 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-19 17:53:16,678 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-19 17:53:16,756 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0018Job Transitioned from INITED to SETUP
2015-10-19 17:53:16,803 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-19 17:53:16,819 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0018Job Transitioned from SETUP to RUNNING
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:16,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:16,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 17:53:16,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000000_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:16,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000001_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:16,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000002_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:16,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000003_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:16,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000004_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:16,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000005_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:16,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000006_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:16,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000007_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:16,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000008_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:16,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000009_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:16,928 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_r_000000_1000 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:53:16,928 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-19 17:53:16,944 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-19 17:53:16,990 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445182159119_0018, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0018/job_1445182159119_0018_2.jhist
2015-10-19 17:53:17,959 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 17:53:18,194 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:4096, vCores:-23> knownNMs=4
2015-10-19 17:53:18,209 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-19 17:53:18,209 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:19,241 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-19 17:53:19,241 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:20,272 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-19 17:53:20,272 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:21,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-19 17:53:21,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:22,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-19 17:53:22,319 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:23,366 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:53:23,381 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:23,381 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000002 to attempt_1445182159119_0018_m_000000_1000
2015-10-19 17:53:23,381 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 17:53:23,381 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:23,381 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:0 RackLocal:1
2015-10-19 17:53:23,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:23,585 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0018/job.jar
2015-10-19 17:53:23,585 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0018/job.xml
2015-10-19 17:53:23,585 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-19 17:53:23,585 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-19 17:53:23,585 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-19 17:53:23,694 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000000_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:24,038 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000002 taskAttempt attempt_1445182159119_0018_m_000000_1000
2015-10-19 17:53:24,053 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000000_1000
2015-10-19 17:53:24,053 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:53:24,397 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-19 17:53:24,397 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 17:53:24,397 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:24,850 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000000_1000 : 13562
2015-10-19 17:53:24,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000000_1000] using containerId: [container_1445182159119_0018_02_000002 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 17:53:24,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000000_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:24,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000000
2015-10-19 17:53:24,850 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:25,663 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 17:53:25,663 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:26,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 17:53:26,694 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:27,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:53:27,710 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:27,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000003 to attempt_1445182159119_0018_m_000001_1000
2015-10-19 17:53:27,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 17:53:27,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:27,710 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:0 RackLocal:2
2015-10-19 17:53:27,710 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:27,725 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000001_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:27,772 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000003 taskAttempt attempt_1445182159119_0018_m_000001_1000
2015-10-19 17:53:27,772 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000001_1000
2015-10-19 17:53:27,772 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:53:28,069 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000001_1000 : 13562
2015-10-19 17:53:28,069 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000001_1000] using containerId: [container_1445182159119_0018_02_000003 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 17:53:28,069 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000001_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:28,069 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000001
2015-10-19 17:53:28,069 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:28,757 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-26> knownNMs=4
2015-10-19 17:53:28,757 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 17:53:28,757 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:29,804 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 17:53:29,804 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:30,851 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 17:53:30,851 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:30,866 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:53:30,976 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000002 asked for a task
2015-10-19 17:53:30,976 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000002 given task: attempt_1445182159119_0018_m_000000_1000
2015-10-19 17:53:31,960 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:53:31,960 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:31,960 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000004 to attempt_1445182159119_0018_m_000002_1000
2015-10-19 17:53:31,960 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:31,960 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:31,960 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:0 RackLocal:3
2015-10-19 17:53:31,960 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:31,960 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000002_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:32,413 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000004 taskAttempt attempt_1445182159119_0018_m_000002_1000
2015-10-19 17:53:32,413 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000002_1000
2015-10-19 17:53:32,413 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:53:32,819 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000002_1000 : 13562
2015-10-19 17:53:32,819 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000002_1000] using containerId: [container_1445182159119_0018_02_000004 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 17:53:32,819 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000002_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:32,819 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000002
2015-10-19 17:53:32,819 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:33,023 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-26> knownNMs=4
2015-10-19 17:53:33,023 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 17:53:33,023 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:34,101 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 17:53:34,101 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:35,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 17:53:35,226 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:36,257 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 17:53:36,257 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:37,413 INFO [Thread-56] org.apache.hadoop.hdfs.DFSClient: Exception in createBlockOutputStream
java.io.IOException: Bad connect ack with firstBadLink as ************:50010
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.createBlockOutputStream(DFSOutputStream.java:1461)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.nextBlockOutputStream(DFSOutputStream.java:1362)
	at org.apache.hadoop.hdfs.DFSOutputStream$DataStreamer.run(DFSOutputStream.java:589)
2015-10-19 17:53:37,429 INFO [Thread-56] org.apache.hadoop.hdfs.DFSClient: Abandoning BP-1347369012-**************-1444972147527:blk_1073744058_3283
2015-10-19 17:53:37,617 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:53:37,648 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:37,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000005 to attempt_1445182159119_0018_m_000003_1000
2015-10-19 17:53:37,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:37,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:37,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:0 RackLocal:4
2015-10-19 17:53:37,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:53:37,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000003_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:53:37,851 INFO [Thread-56] org.apache.hadoop.hdfs.DFSClient: Excluding datanode ************:50010
2015-10-19 17:53:37,945 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000005 taskAttempt attempt_1445182159119_0018_m_000003_1000
2015-10-19 17:53:37,945 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000003_1000
2015-10-19 17:53:37,945 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:53:38,648 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:53:38,726 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000004 asked for a task
2015-10-19 17:53:38,726 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000004 given task: attempt_1445182159119_0018_m_000002_1000
2015-10-19 17:53:39,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 17:53:39,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:39,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:39,289 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000003_1000 : 13562
2015-10-19 17:53:39,398 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000003_1000] using containerId: [container_1445182159119_0018_02_000005 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 17:53:39,398 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000003_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:53:39,398 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000003
2015-10-19 17:53:39,398 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:53:40,445 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:40,445 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:41,601 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:41,601 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:43,101 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:43,101 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:44,148 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:44,148 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:44,820 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:53:45,008 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000003 asked for a task
2015-10-19 17:53:45,008 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000003 given task: attempt_1445182159119_0018_m_000001_1000
2015-10-19 17:53:45,195 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:45,195 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:47,430 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:47,430 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:48,477 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:48,477 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:49,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:49,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:49,914 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.020516735
2015-10-19 17:53:50,883 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:50,883 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:51,961 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:51,961 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:53,024 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:53,024 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:53,149 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.03712723
2015-10-19 17:53:54,149 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:54,149 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:55,024 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.047548342
2015-10-19 17:53:55,164 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:55,164 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:56,211 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:56,211 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:56,258 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.044617485
2015-10-19 17:53:57,336 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:57,336 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:57,399 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.0052067237
2015-10-19 17:53:58,086 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.0746109
2015-10-19 17:53:58,118 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:53:58,383 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000005 asked for a task
2015-10-19 17:53:58,383 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000005 given task: attempt_1445182159119_0018_m_000003_1000
2015-10-19 17:53:58,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:58,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:53:59,337 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.052759945
2015-10-19 17:53:59,462 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:53:59,462 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:00,524 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:00,524 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:00,680 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.01400045
2015-10-19 17:54:01,118 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.083046496
2015-10-19 17:54:01,602 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:01,602 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:02,680 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:02,680 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:02,852 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.06350645
2015-10-19 17:54:03,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 17:54:03,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:04,431 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.03549564
2015-10-19 17:54:04,790 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.103892006
2015-10-19 17:54:05,306 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 17:54:05,306 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:06,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:54:06,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000006 to attempt_1445182159119_0018_m_000004_1000
2015-10-19 17:54:06,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 17:54:06,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:06,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:1 RackLocal:4
2015-10-19 17:54:06,399 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:54:06,399 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000004_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:54:06,540 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.0736033
2015-10-19 17:54:06,603 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000006 taskAttempt attempt_1445182159119_0018_m_000004_1000
2015-10-19 17:54:06,603 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000004_1000
2015-10-19 17:54:06,603 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:54:07,087 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000004_1000 : 13562
2015-10-19 17:54:07,087 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000004_1000] using containerId: [container_1445182159119_0018_02_000006 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:54:07,087 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000004_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:54:07,087 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000004
2015-10-19 17:54:07,087 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:54:07,524 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-26> knownNMs=4
2015-10-19 17:54:07,524 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 17:54:07,524 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:08,103 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.051778544
2015-10-19 17:54:08,556 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.10660437
2015-10-19 17:54:10,134 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.082721464
2015-10-19 17:54:11,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:11,165 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:12,040 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:54:12,056 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000006 asked for a task
2015-10-19 17:54:12,072 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000006 given task: attempt_1445182159119_0018_m_000004_1000
2015-10-19 17:54:12,275 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.06806502
2015-10-19 17:54:12,306 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:12,306 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:12,884 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.10660437
2015-10-19 17:54:13,462 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:13,462 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:14,619 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:14,619 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:14,712 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.06445631
2015-10-19 17:54:14,775 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.10601833
2015-10-19 17:54:15,712 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:15,712 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:16,712 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.10046023
2015-10-19 17:54:17,025 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.10660437
2015-10-19 17:54:17,259 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:17,259 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:18,228 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.10184928
2015-10-19 17:54:18,338 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:18,338 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:19,056 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.10635664
2015-10-19 17:54:20,291 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.10680563
2015-10-19 17:54:20,510 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:20,510 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:21,072 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.1066108
2015-10-19 17:54:21,635 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.10660437
2015-10-19 17:54:21,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:21,650 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:22,385 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.106493875
2015-10-19 17:54:23,135 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.10635664
2015-10-19 17:54:23,166 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:23,166 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:23,400 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.10680563
2015-10-19 17:54:24,275 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:24,275 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:25,119 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.1066108
2015-10-19 17:54:25,385 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:25,385 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:25,744 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.10660437
2015-10-19 17:54:26,463 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:26,463 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:26,479 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.106493875
2015-10-19 17:54:26,479 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.1308125
2015-10-19 17:54:27,432 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.10635664
2015-10-19 17:54:27,572 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:27,572 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:28,651 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:28,651 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:29,323 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.1066108
2015-10-19 17:54:29,557 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.19242907
2015-10-19 17:54:29,729 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:29,729 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:30,041 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.10660437
2015-10-19 17:54:30,682 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.106493875
2015-10-19 17:54:30,838 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:30,838 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:31,526 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.10635664
2015-10-19 17:54:31,995 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:31,995 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:32,604 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.19242907
2015-10-19 17:54:33,088 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:33,088 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:33,604 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.1066108
2015-10-19 17:54:34,182 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:34,182 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:34,229 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.106493875
2015-10-19 17:54:34,807 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.10660437
2015-10-19 17:54:35,245 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:35,245 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:35,526 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.10635664
2015-10-19 17:54:35,667 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.19242907
2015-10-19 17:54:36,307 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:36,307 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:37,479 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:37,479 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:37,510 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.1066108
2015-10-19 17:54:37,682 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.106493875
2015-10-19 17:54:38,542 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:38,542 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:38,823 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.120873496
2015-10-19 17:54:38,823 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.25679624
2015-10-19 17:54:39,636 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.10635664
2015-10-19 17:54:40,792 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:40,792 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:41,198 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.106493875
2015-10-19 17:54:41,792 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.1066108
2015-10-19 17:54:41,901 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.2781602
2015-10-19 17:54:41,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:41,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:42,620 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.15730198
2015-10-19 17:54:43,183 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.10635664
2015-10-19 17:54:43,339 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:43,339 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:45,245 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.106493875
2015-10-19 17:54:45,245 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.2781602
2015-10-19 17:54:45,386 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.1066108
2015-10-19 17:54:46,152 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.16153802
2015-10-19 17:54:46,167 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:46,167 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:46,964 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.10635664
2015-10-19 17:54:47,277 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:47,277 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:48,308 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.2781602
2015-10-19 17:54:48,370 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:54:48,370 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:54:48,386 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:54:48,386 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000007 to attempt_1445182159119_0018_m_000005_1000
2015-10-19 17:54:48,386 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:48,386 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:48,386 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:1 RackLocal:5
2015-10-19 17:54:48,386 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000005_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:54:48,495 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000007 taskAttempt attempt_1445182159119_0018_m_000005_1000
2015-10-19 17:54:48,495 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000005_1000
2015-10-19 17:54:48,495 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:54:48,730 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.106493875
2015-10-19 17:54:49,261 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.1066108
2015-10-19 17:54:49,902 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.18563628
2015-10-19 17:54:50,699 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000005_1000 : 13562
2015-10-19 17:54:50,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000005_1000] using containerId: [container_1445182159119_0018_02_000007 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 17:54:50,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000005_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:54:50,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000005
2015-10-19 17:54:50,714 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:54:50,761 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.11040469
2015-10-19 17:54:50,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 17:54:50,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:50,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:51,402 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.3613938
2015-10-19 17:54:52,292 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.106493875
2015-10-19 17:54:52,324 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:52,324 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:52,996 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.11659202
2015-10-19 17:54:53,418 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:53,418 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:53,793 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.19212553
2015-10-19 17:54:54,464 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.36388028
2015-10-19 17:54:54,558 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:54,558 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:54,668 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.12929589
2015-10-19 17:54:55,652 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:55,652 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:56,074 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.106493875
2015-10-19 17:54:56,761 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.14199515
2015-10-19 17:54:57,246 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.19212553
2015-10-19 17:54:57,543 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.36388028
2015-10-19 17:54:58,168 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.15339683
2015-10-19 17:54:59,371 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:54:59,371 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:54:59,387 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.12465956
2015-10-19 17:55:00,480 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:55:00,480 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000008 to attempt_1445182159119_0018_m_000006_1000
2015-10-19 17:55:00,480 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:00,480 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:00,480 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:2 RackLocal:5
2015-10-19 17:55:00,480 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:55:00,480 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000006_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:55:00,496 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.15209274
2015-10-19 17:55:00,559 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:55:00,621 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.36388028
2015-10-19 17:55:00,621 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000008 taskAttempt attempt_1445182159119_0018_m_000006_1000
2015-10-19 17:55:00,621 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000006_1000
2015-10-19 17:55:00,621 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:55:00,777 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000007 asked for a task
2015-10-19 17:55:00,777 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000007 given task: attempt_1445182159119_0018_m_000005_1000
2015-10-19 17:55:01,105 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.19212553
2015-10-19 17:55:01,590 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 17:55:01,590 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:01,590 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:01,590 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000006_1000 : 13562
2015-10-19 17:55:01,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000006_1000] using containerId: [container_1445182159119_0018_02_000008 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:55:01,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000006_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:55:01,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000006
2015-10-19 17:55:01,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:55:01,824 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.16707283
2015-10-19 17:55:02,699 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:02,699 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:03,027 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.16076325
2015-10-19 17:55:03,340 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:55:03,418 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000008 asked for a task
2015-10-19 17:55:03,418 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000008 given task: attempt_1445182159119_0018_m_000006_1000
2015-10-19 17:55:04,043 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.15958336
2015-10-19 17:55:04,199 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:04,199 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:04,559 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.19212553
2015-10-19 17:55:04,559 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.41183382
2015-10-19 17:55:05,278 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:05,278 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:05,715 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.17554215
2015-10-19 17:55:06,324 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:06,324 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:06,950 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.19209063
2015-10-19 17:55:07,403 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:07,403 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:07,668 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.44968578
2015-10-19 17:55:07,809 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.17065431
2015-10-19 17:55:08,496 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.19212553
2015-10-19 17:55:08,543 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:08,543 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:09,809 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.19158794
2015-10-19 17:55:10,465 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.19209063
2015-10-19 17:55:10,715 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.44968578
2015-10-19 17:55:10,715 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.106964506
2015-10-19 17:55:10,809 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:10,809 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:11,903 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:11,903 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:11,919 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.19211523
2015-10-19 17:55:12,497 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.19212553
2015-10-19 17:55:13,356 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:13,356 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:13,809 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.47804713
2015-10-19 17:55:13,809 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.19158794
2015-10-19 17:55:13,809 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.106964506
2015-10-19 17:55:14,387 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.19209063
2015-10-19 17:55:14,450 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:14,450 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:15,544 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:15,544 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:15,731 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.19211523
2015-10-19 17:55:16,544 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.19212553
2015-10-19 17:55:16,638 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:16,638 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:16,903 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.106964506
2015-10-19 17:55:16,903 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.5352028
2015-10-19 17:55:17,669 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.19158794
2015-10-19 17:55:19,216 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:19,216 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:19,278 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.19209063
2015-10-19 17:55:19,419 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.19211523
2015-10-19 17:55:20,013 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.5352028
2015-10-19 17:55:20,044 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.106964506
2015-10-19 17:55:20,294 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-19 17:55:20,294 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000009 to attempt_1445182159119_0018_m_000007_1000
2015-10-19 17:55:20,294 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000010 to attempt_1445182159119_0018_m_000008_1000
2015-10-19 17:55:20,294 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 17:55:20,294 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:20,294 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:4 RackLocal:5
2015-10-19 17:55:20,294 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:55:20,294 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000007_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:55:20,294 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:55:20,294 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000008_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:55:20,341 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.19540918
2015-10-19 17:55:20,497 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000009 taskAttempt attempt_1445182159119_0018_m_000007_1000
2015-10-19 17:55:20,497 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000007_1000
2015-10-19 17:55:20,497 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:55:20,591 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000010 taskAttempt attempt_1445182159119_0018_m_000008_1000
2015-10-19 17:55:20,591 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000008_1000
2015-10-19 17:55:20,591 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:55:21,310 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.045822732
2015-10-19 17:55:21,466 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:1024, vCores:-26> knownNMs=4
2015-10-19 17:55:21,466 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:55:21,466 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000011 to attempt_1445182159119_0018_m_000009_1000
2015-10-19 17:55:21,466 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 17:55:21,466 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:21,466 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:5 RackLocal:5
2015-10-19 17:55:21,466 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:55:21,466 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000009_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:55:21,747 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.19158794
2015-10-19 17:55:21,778 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000011 taskAttempt attempt_1445182159119_0018_m_000009_1000
2015-10-19 17:55:21,778 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000009_1000
2015-10-19 17:55:21,778 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:55:22,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-19 17:55:22,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:55:22,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Cannot assign container Container: [ContainerId: container_1445182159119_0018_02_000012, NodeId: MSRA-SA-41.fareast.corp.microsoft.com:10769, NodeHttpAddress: MSRA-SA-41.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 20, Token: Token { kind: ContainerToken, service: **************:10769 }, ] for a map as either  container memory less than required <memory:1024, vCores:1> or no pending map tasks - maps.isEmpty=true
2015-10-19 17:55:22,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 17:55:22,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:22,607 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:5 RackLocal:5
2015-10-19 17:55:22,779 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.19209063
2015-10-19 17:55:23,107 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.19266446
2015-10-19 17:55:23,107 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.5352028
2015-10-19 17:55:23,607 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.19211523
2015-10-19 17:55:23,763 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=0 release= 1 newContainers=0 finishedContainers=1 resourcelimit=<memory:4096, vCores:-23> knownNMs=4
2015-10-19 17:55:23,763 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000012
2015-10-19 17:55:23,763 ERROR [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Container complete event for unknown container id container_1445182159119_0018_02_000012
2015-10-19 17:55:23,763 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-23>
2015-10-19 17:55:23,763 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:24,560 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000007_1000 : 13562
2015-10-19 17:55:24,560 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000007_1000] using containerId: [container_1445182159119_0018_02_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:55:24,560 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000007_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:55:24,560 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000007
2015-10-19 17:55:24,560 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:55:24,638 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.21625197
2015-10-19 17:55:24,669 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000008_1000 : 13562
2015-10-19 17:55:24,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000008_1000] using containerId: [container_1445182159119_0018_02_000010 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:55:24,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000008_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:55:24,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000008
2015-10-19 17:55:24,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:55:24,716 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.08467691
2015-10-19 17:55:25,513 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000009_1000 : 13562
2015-10-19 17:55:25,513 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000009_1000] using containerId: [container_1445182159119_0018_02_000011 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:55:25,513 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000009_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:55:25,513 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000009
2015-10-19 17:55:25,513 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:55:25,685 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.19158794
2015-10-19 17:55:25,904 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:55:25,997 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000009 asked for a task
2015-10-19 17:55:25,997 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000009 given task: attempt_1445182159119_0018_m_000007_1000
2015-10-19 17:55:26,044 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-21>
2015-10-19 17:55:26,044 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:26,263 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.53771216
2015-10-19 17:55:26,263 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.19266446
2015-10-19 17:55:26,419 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:55:26,497 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000010 asked for a task
2015-10-19 17:55:26,497 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000010 given task: attempt_1445182159119_0018_m_000008_1000
2015-10-19 17:55:27,060 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.19211523
2015-10-19 17:55:27,669 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.19209063
2015-10-19 17:55:28,107 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.22765201
2015-10-19 17:55:28,201 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.10561958
2015-10-19 17:55:29,029 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.19158794
2015-10-19 17:55:29,326 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.6208445
2015-10-19 17:55:29,326 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.19266446
2015-10-19 17:55:29,638 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-20>
2015-10-19 17:55:29,638 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:30,357 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.19211523
2015-10-19 17:55:30,435 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:55:30,513 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000011 asked for a task
2015-10-19 17:55:30,513 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000011 given task: attempt_1445182159119_0018_m_000009_1000
2015-10-19 17:55:31,138 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.19209063
2015-10-19 17:55:31,591 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.2452567
2015-10-19 17:55:31,670 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.10685723
2015-10-19 17:55:32,248 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.19247802
2015-10-19 17:55:32,420 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.6208445
2015-10-19 17:55:32,435 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.27138975
2015-10-19 17:55:33,263 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.10681946
2015-10-19 17:55:33,388 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.19211523
2015-10-19 17:55:33,951 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.106881365
2015-10-19 17:55:34,482 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.19209063
2015-10-19 17:55:35,014 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.10685723
2015-10-19 17:55:35,451 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.26250154
2015-10-19 17:55:35,545 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.2783809
2015-10-19 17:55:35,545 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.6208445
2015-10-19 17:55:35,951 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.19866507
2015-10-19 17:55:36,389 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.10681946
2015-10-19 17:55:36,889 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.20126845
2015-10-19 17:55:37,045 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.106881365
2015-10-19 17:55:37,264 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-19>
2015-10-19 17:55:37,264 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:37,342 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.6208445
2015-10-19 17:55:37,920 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000009_1000 is : 0.295472
2015-10-19 17:55:37,967 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.19209063
2015-10-19 17:55:38,326 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.10685723
2015-10-19 17:55:38,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-18>
2015-10-19 17:55:38,389 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:55:38,623 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.667
2015-10-19 17:55:38,623 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.2783809
2015-10-19 17:55:38,889 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.2774802
2015-10-19 17:55:39,404 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.20843674
2015-10-19 17:55:39,467 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.13520743
2015-10-19 17:55:40,154 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.2126683
2015-10-19 17:55:40,186 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.13638107
2015-10-19 17:55:41,014 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000009_1000 is : 0.295472
2015-10-19 17:55:41,139 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.20648137
2015-10-19 17:55:42,280 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.27772525
2015-10-19 17:55:42,514 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.19255035
2015-10-19 17:55:42,576 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.32946047
2015-10-19 17:55:42,576 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.667
2015-10-19 17:55:42,748 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.21918301
2015-10-19 17:55:42,826 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.10685723
2015-10-19 17:55:43,248 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.19258286
2015-10-19 17:55:43,498 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.2195082
2015-10-19 17:55:44,077 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000009_1000 is : 0.33879992
2015-10-19 17:55:44,358 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.22537115
2015-10-19 17:55:45,577 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.27772525
2015-10-19 17:55:45,623 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.19255035
2015-10-19 17:55:45,655 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.6889216
2015-10-19 17:55:45,655 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.36404583
2015-10-19 17:55:45,952 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.23025686
2015-10-19 17:55:46,077 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.10685723
2015-10-19 17:55:46,358 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.19258286
2015-10-19 17:55:46,764 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.23937319
2015-10-19 17:55:47,170 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000009_1000 is : 0.5323719
2015-10-19 17:55:47,655 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.2416557
2015-10-19 17:55:48,702 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.19255035
2015-10-19 17:55:48,733 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.36404583
2015-10-19 17:55:48,733 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.73090875
2015-10-19 17:55:49,124 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.27772525
2015-10-19 17:55:49,249 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.10685723
2015-10-19 17:55:49,405 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.25338134
2015-10-19 17:55:49,467 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.19258286
2015-10-19 17:55:50,249 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000009_1000 is : 0.5323719
2015-10-19 17:55:51,030 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.26086965
2015-10-19 17:55:51,139 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.26469365
2015-10-19 17:55:51,780 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.27825075
2015-10-19 17:55:51,796 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.41820544
2015-10-19 17:55:51,796 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.7735609
2015-10-19 17:55:52,608 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.27811313
2015-10-19 17:55:52,733 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.10685723
2015-10-19 17:55:53,311 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.27772525
2015-10-19 17:55:53,311 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000009_1000 is : 0.5323719
2015-10-19 17:55:53,311 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.27696857
2015-10-19 17:55:54,452 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.27765483
2015-10-19 17:55:54,827 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.27825075
2015-10-19 17:55:54,827 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000009_1000 is : 0.5323719
2015-10-19 17:55:54,827 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.81154835
2015-10-19 17:55:54,827 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.44980705
2015-10-19 17:55:54,999 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.27776006
2015-10-19 17:55:56,374 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000009_1000 is : 0.667
2015-10-19 17:55:56,577 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.27811313
2015-10-19 17:55:56,718 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.1131849
2015-10-19 17:55:56,999 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.27772525
2015-10-19 17:55:57,093 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.27696857
2015-10-19 17:55:57,655 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.27765483
2015-10-19 17:55:57,890 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.27825075
2015-10-19 17:55:57,890 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.44980705
2015-10-19 17:55:57,890 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.85388786
2015-10-19 17:55:58,968 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.27776006
2015-10-19 17:55:59,405 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000009_1000 is : 0.667
2015-10-19 17:55:59,624 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.27811313
2015-10-19 17:55:59,984 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.13874051
2015-10-19 17:56:00,749 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.2856233
2015-10-19 17:56:00,765 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.27696857
2015-10-19 17:56:00,874 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.27765483
2015-10-19 17:56:00,937 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.47614723
2015-10-19 17:56:00,952 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.35389972
2015-10-19 17:56:00,968 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.896548
2015-10-19 17:56:02,484 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000009_1000 is : 0.6803089
2015-10-19 17:56:02,499 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.27776006
2015-10-19 17:56:03,562 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.3637686
2015-10-19 17:56:03,718 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.1556389
2015-10-19 17:56:03,984 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.53543663
2015-10-19 17:56:03,984 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.3638923
2015-10-19 17:56:03,999 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.9377837
2015-10-19 17:56:04,015 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.27765483
2015-10-19 17:56:04,234 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.31037566
2015-10-19 17:56:04,484 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.27696857
2015-10-19 17:56:05,484 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-17>
2015-10-19 17:56:05,484 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:56:05,578 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000009_1000 is : 0.76917416
2015-10-19 17:56:06,187 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.27776006
2015-10-19 17:56:06,593 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.3637686
2015-10-19 17:56:07,047 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.3638923
2015-10-19 17:56:07,047 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.53543663
2015-10-19 17:56:07,047 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.18437085
2015-10-19 17:56:07,062 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 0.9782903
2015-10-19 17:56:07,390 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.27765483
2015-10-19 17:56:07,609 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.3354512
2015-10-19 17:56:07,640 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:11264, vCores:-16>
2015-10-19 17:56:07,640 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 17:56:07,859 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.27696857
2015-10-19 17:56:08,687 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000009_1000 is : 0.86288613
2015-10-19 17:56:08,718 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000004_1000 is : 1.0
2015-10-19 17:56:08,765 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0018_m_000004_1000
2015-10-19 17:56:08,765 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000004_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:56:08,765 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000006 taskAttempt attempt_1445182159119_0018_m_000004_1000
2015-10-19 17:56:08,765 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000004_1000
2015-10-19 17:56:08,765 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:56:09,187 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000004_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:56:09,203 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0018_m_000004_1000
2015-10-19 17:56:09,203 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:56:09,203 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-19 17:56:09,719 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.3637686
2015-10-19 17:56:09,765 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:5 RackLocal:5
2015-10-19 17:56:09,828 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.27776006
2015-10-19 17:56:09,844 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:11264, vCores:-16>
2015-10-19 17:56:09,844 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-19 17:56:09,844 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-19 17:56:09,844 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:5 RackLocal:5
2015-10-19 17:56:10,125 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0018_m_000000
2015-10-19 17:56:10,125 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0018_m_000000
2015-10-19 17:56:10,125 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:10,125 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:10,125 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000000_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:56:10,125 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 17:56:10,219 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.4233872
2015-10-19 17:56:10,297 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.19247705
2015-10-19 17:56:10,437 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.53543663
2015-10-19 17:56:10,906 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:5 RackLocal:5
2015-10-19 17:56:10,937 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:12288, vCores:-15> knownNMs=4
2015-10-19 17:56:10,937 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000006
2015-10-19 17:56:10,937 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:5 RackLocal:5
2015-10-19 17:56:10,937 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000004_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:56:10,984 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.27765483
2015-10-19 17:56:11,328 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.36317363
2015-10-19 17:56:11,422 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.27696857
2015-10-19 17:56:11,719 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000009_1000 is : 0.9583059
2015-10-19 17:56:12,016 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-19 17:56:12,016 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-19 17:56:12,016 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000013 to attempt_1445182159119_0018_r_000000_1000
2015-10-19 17:56:12,016 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:12,016 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000014 to attempt_1445182159119_0018_m_000000_1001
2015-10-19 17:56:12,016 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:1 HostLocal:5 RackLocal:6
2015-10-19 17:56:12,016 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:12,016 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_r_000000_1000 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:56:12,016 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:12,031 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000000_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:56:12,031 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000013 taskAttempt attempt_1445182159119_0018_r_000000_1000
2015-10-19 17:56:12,031 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000014 taskAttempt attempt_1445182159119_0018_m_000000_1001
2015-10-19 17:56:12,031 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_r_000000_1000
2015-10-19 17:56:12,031 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000000_1001
2015-10-19 17:56:12,031 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:56:12,031 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:56:12,172 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_r_000000_1000 : 13562
2015-10-19 17:56:12,172 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_r_000000_1000] using containerId: [container_1445182159119_0018_02_000013 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:56:12,172 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_r_000000_1000 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:56:12,172 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_r_000000
2015-10-19 17:56:12,172 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 17:56:12,453 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000000_1001 : 13562
2015-10-19 17:56:12,453 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000000_1001] using containerId: [container_1445182159119_0018_02_000014 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 17:56:12,469 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000000_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:56:12,469 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000000
2015-10-19 17:56:12,797 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.44950172
2015-10-19 17:56:13,078 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-17> knownNMs=4
2015-10-19 17:56:13,422 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.27776006
2015-10-19 17:56:13,453 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000009_1000 is : 1.0
2015-10-19 17:56:13,500 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0018_m_000009_1000
2015-10-19 17:56:13,500 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000009_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:56:13,500 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000011 taskAttempt attempt_1445182159119_0018_m_000009_1000
2015-10-19 17:56:13,500 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000009_1000
2015-10-19 17:56:13,500 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:56:13,516 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.6210422
2015-10-19 17:56:13,516 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.19247705
2015-10-19 17:56:13,562 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.44964966
2015-10-19 17:56:13,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000009_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:56:13,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0018_m_000009_1000
2015-10-19 17:56:13,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:56:13,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-19 17:56:14,094 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:13 ContRel:1 HostLocal:5 RackLocal:6
2015-10-19 17:56:14,266 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.27765483
2015-10-19 17:56:14,313 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:56:14,344 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_r_000013 asked for a task
2015-10-19 17:56:14,344 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_r_000013 given task: attempt_1445182159119_0018_r_000000_1000
2015-10-19 17:56:14,813 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.36317363
2015-10-19 17:56:14,859 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.27696857
2015-10-19 17:56:15,156 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000011
2015-10-19 17:56:15,156 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:13 ContRel:1 HostLocal:5 RackLocal:6
2015-10-19 17:56:15,156 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000009_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:56:15,828 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.44950172
2015-10-19 17:56:16,360 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 0 maxEvents 10000
2015-10-19 17:56:16,578 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.6210422
2015-10-19 17:56:16,610 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.44964966
2015-10-19 17:56:16,797 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.27776006
2015-10-19 17:56:16,985 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.19247705
2015-10-19 17:56:17,406 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:17,500 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.30061513
2015-10-19 17:56:17,875 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.36317363
2015-10-19 17:56:17,938 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.30516484
2015-10-19 17:56:17,953 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:56:18,078 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000014 asked for a task
2015-10-19 17:56:18,078 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000014 given task: attempt_1445182159119_0018_m_000000_1001
2015-10-19 17:56:18,453 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:19,516 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:19,625 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.6210422
2015-10-19 17:56:19,657 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.4959935
2015-10-19 17:56:19,782 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.44950172
2015-10-19 17:56:19,907 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.3159116
2015-10-19 17:56:20,188 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.19247705
2015-10-19 17:56:20,547 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:20,719 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.32144856
2015-10-19 17:56:20,985 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.36317363
2015-10-19 17:56:21,063 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.3194922
2015-10-19 17:56:21,579 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.6210422
2015-10-19 17:56:21,625 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:22,625 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.06666667
2015-10-19 17:56:22,672 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:22,672 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.667
2015-10-19 17:56:22,672 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.5352825
2015-10-19 17:56:22,844 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.53521925
2015-10-19 17:56:23,438 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.36319977
2015-10-19 17:56:23,485 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.19247705
2015-10-19 17:56:23,735 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:23,860 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.3400116
2015-10-19 17:56:24,782 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:24,782 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.36317363
2015-10-19 17:56:24,922 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.35884127
2015-10-19 17:56:25,188 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0018_m_000005
2015-10-19 17:56:25,188 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 17:56:25,188 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0018_m_000005
2015-10-19 17:56:25,188 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:25,188 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:25,188 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000005_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:56:25,657 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.06666667
2015-10-19 17:56:25,719 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.667
2015-10-19 17:56:25,751 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.5352825
2015-10-19 17:56:25,798 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:25,876 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.53521925
2015-10-19 17:56:25,954 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:13 ContRel:1 HostLocal:5 RackLocal:6
2015-10-19 17:56:25,954 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:11264, vCores:-16> knownNMs=4
2015-10-19 17:56:26,766 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.19247705
2015-10-19 17:56:26,829 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:26,969 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.36319977
2015-10-19 17:56:26,985 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:56:26,985 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:26,985 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000015 to attempt_1445182159119_0018_m_000005_1001
2015-10-19 17:56:26,985 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:5 RackLocal:7
2015-10-19 17:56:26,985 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:26,985 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000005_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:56:26,985 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000015 taskAttempt attempt_1445182159119_0018_m_000005_1001
2015-10-19 17:56:26,985 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000005_1001
2015-10-19 17:56:26,985 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:56:27,344 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.35890532
2015-10-19 17:56:27,423 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.026378518
2015-10-19 17:56:27,501 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000005_1001 : 13562
2015-10-19 17:56:27,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000005_1001] using containerId: [container_1445182159119_0018_02_000015 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 17:56:27,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000005_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:56:27,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000005
2015-10-19 17:56:27,876 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:28,016 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-17> knownNMs=4
2015-10-19 17:56:28,407 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.36317363
2015-10-19 17:56:28,595 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.3624012
2015-10-19 17:56:28,688 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.06666667
2015-10-19 17:56:28,766 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.67453074
2015-10-19 17:56:28,766 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.5352825
2015-10-19 17:56:28,938 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:28,938 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.53521925
2015-10-19 17:56:29,970 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:30,063 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.19247705
2015-10-19 17:56:30,673 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.36323506
2015-10-19 17:56:30,735 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.36319977
2015-10-19 17:56:30,860 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.050131142
2015-10-19 17:56:30,985 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:31,720 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.06666667
2015-10-19 17:56:31,813 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.620844
2015-10-19 17:56:31,813 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.71844316
2015-10-19 17:56:31,985 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.6207798
2015-10-19 17:56:32,063 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:32,063 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.39757857
2015-10-19 17:56:32,235 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.3624012
2015-10-19 17:56:33,095 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:33,298 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.21088804
2015-10-19 17:56:33,642 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:56:33,751 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000015 asked for a task
2015-10-19 17:56:33,751 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000015 given task: attempt_1445182159119_0018_m_000005_1001
2015-10-19 17:56:33,829 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.36323506
2015-10-19 17:56:34,032 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.06774005
2015-10-19 17:56:34,126 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:34,407 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.36319977
2015-10-19 17:56:34,798 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.06666667
2015-10-19 17:56:34,892 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.75811
2015-10-19 17:56:34,892 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.620844
2015-10-19 17:56:35,001 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.6207798
2015-10-19 17:56:35,189 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:35,876 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.44859612
2015-10-19 17:56:36,017 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.3624012
2015-10-19 17:56:36,204 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:37,376 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.36323506
2015-10-19 17:56:37,517 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:37,564 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.078814656
2015-10-19 17:56:37,767 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.22960567
2015-10-19 17:56:37,798 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.36319977
2015-10-19 17:56:37,829 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.06666667
2015-10-19 17:56:37,908 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.80228364
2015-10-19 17:56:37,923 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.620844
2015-10-19 17:56:38,033 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.6207798
2015-10-19 17:56:38,517 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:39,376 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.44859612
2015-10-19 17:56:39,580 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:39,580 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.3624012
2015-10-19 17:56:40,142 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.6207798
2015-10-19 17:56:40,205 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0018_m_000001
2015-10-19 17:56:40,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0018_m_000001
2015-10-19 17:56:40,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:40,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:40,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000001_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:56:40,205 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 17:56:40,361 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.620844
2015-10-19 17:56:40,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:5 RackLocal:7
2015-10-19 17:56:40,595 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:40,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-17> knownNMs=4
2015-10-19 17:56:40,736 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.36323506
2015-10-19 17:56:40,876 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.06666667
2015-10-19 17:56:40,939 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.091842555
2015-10-19 17:56:40,939 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.8449531
2015-10-19 17:56:40,955 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.667
2015-10-19 17:56:41,064 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.667
2015-10-19 17:56:41,095 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.24881974
2015-10-19 17:56:41,298 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.36319977
2015-10-19 17:56:41,611 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:41,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:56:41,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000016 to attempt_1445182159119_0018_m_000001_1001
2015-10-19 17:56:41,627 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:6 RackLocal:7
2015-10-19 17:56:41,627 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:41,627 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000001_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:56:41,627 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000016 taskAttempt attempt_1445182159119_0018_m_000001_1001
2015-10-19 17:56:41,627 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000001_1001
2015-10-19 17:56:41,627 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:56:41,783 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000001_1001 : 13562
2015-10-19 17:56:41,783 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000001_1001] using containerId: [container_1445182159119_0018_02_000016 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 17:56:41,783 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000001_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:56:41,783 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000001
2015-10-19 17:56:42,627 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:42,658 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-18> knownNMs=4
2015-10-19 17:56:42,970 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.44859612
2015-10-19 17:56:43,158 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.3624012
2015-10-19 17:56:43,517 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:56:43,533 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000016 asked for a task
2015-10-19 17:56:43,533 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000016 given task: attempt_1445182159119_0018_m_000001_1001
2015-10-19 17:56:43,548 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.04005778
2015-10-19 17:56:43,627 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:43,908 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.06666667
2015-10-19 17:56:44,017 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.8852607
2015-10-19 17:56:44,017 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.667
2015-10-19 17:56:44,095 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.667
2015-10-19 17:56:44,127 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.36323506
2015-10-19 17:56:44,377 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.10161179
2015-10-19 17:56:44,580 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.26510364
2015-10-19 17:56:44,674 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:44,689 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.37974572
2015-10-19 17:56:45,720 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:46,064 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.44859612
2015-10-19 17:56:46,221 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.36639237
2015-10-19 17:56:46,814 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:46,955 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.06666667
2015-10-19 17:56:47,002 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.06936717
2015-10-19 17:56:47,080 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.92112255
2015-10-19 17:56:47,080 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.667
2015-10-19 17:56:47,189 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.667
2015-10-19 17:56:47,674 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.36323506
2015-10-19 17:56:47,846 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.10635664
2015-10-19 17:56:47,861 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:47,924 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.42631683
2015-10-19 17:56:48,174 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.27813601
2015-10-19 17:56:49,502 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.44859612
2015-10-19 17:56:49,627 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.39147097
2015-10-19 17:56:49,814 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:49,986 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.06666667
2015-10-19 17:56:50,143 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.69944525
2015-10-19 17:56:50,143 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.963861
2015-10-19 17:56:50,221 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.70006627
2015-10-19 17:56:50,471 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.10228736
2015-10-19 17:56:50,643 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.1066108
2015-10-19 17:56:50,830 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:51,127 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.36323506
2015-10-19 17:56:51,283 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.448704
2015-10-19 17:56:51,783 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.27813601
2015-10-19 17:56:51,783 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.10635664
2015-10-19 17:56:51,893 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:52,815 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.44859612
2015-10-19 17:56:52,908 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:53,096 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.06666667
2015-10-19 17:56:53,158 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.42929447
2015-10-19 17:56:53,190 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.7312007
2015-10-19 17:56:53,190 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 0.99802923
2015-10-19 17:56:53,283 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.7310806
2015-10-19 17:56:53,627 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000006_1000 is : 1.0
2015-10-19 17:56:53,721 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.1066108
2015-10-19 17:56:53,721 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0018_m_000006_1000
2015-10-19 17:56:53,721 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000006_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:56:53,721 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000008 taskAttempt attempt_1445182159119_0018_m_000006_1000
2015-10-19 17:56:53,721 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000006_1000
2015-10-19 17:56:53,721 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:56:53,971 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:54,002 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.10685723
2015-10-19 17:56:54,643 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.448704
2015-10-19 17:56:55,205 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0018_m_000003
2015-10-19 17:56:55,205 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 17:56:55,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0018_m_000003
2015-10-19 17:56:55,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:55,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:55,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000003_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:56:55,940 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.36323506
2015-10-19 17:56:55,940 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:55,987 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000006_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:56:55,987 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0018_m_000006_1000
2015-10-19 17:56:55,987 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:56:55,987 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-19 17:56:56,081 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000008
2015-10-19 17:56:56,081 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:6 RackLocal:7
2015-10-19 17:56:56,081 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000006_1000: 
2015-10-19 17:56:56,112 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.27813601
2015-10-19 17:56:56,174 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.06666667
2015-10-19 17:56:56,315 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.75580597
2015-10-19 17:56:56,362 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.46084145
2015-10-19 17:56:56,409 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.10635664
2015-10-19 17:56:56,471 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.75662994
2015-10-19 17:56:56,799 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.4458605
2015-10-19 17:56:56,799 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.17858447
2015-10-19 17:56:56,956 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 2 maxEvents 10000
2015-10-19 17:56:57,127 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:6 RackLocal:7
2015-10-19 17:56:57,159 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:10240, vCores:-17> knownNMs=4
2015-10-19 17:56:57,299 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.10685723
2015-10-19 17:56:57,987 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:56:58,190 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.448704
2015-10-19 17:56:58,221 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:56:58,268 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:58,268 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000017 to attempt_1445182159119_0018_m_000003_1001
2015-10-19 17:56:58,268 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:16 ContRel:1 HostLocal:6 RackLocal:8
2015-10-19 17:56:58,268 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:56:58,268 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000003_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:56:58,268 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000017 taskAttempt attempt_1445182159119_0018_m_000003_1001
2015-10-19 17:56:58,268 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000003_1001
2015-10-19 17:56:58,268 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:56:59,018 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:56:59,206 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.10000001
2015-10-19 17:56:59,300 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-18> knownNMs=4
2015-10-19 17:56:59,331 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000003_1001 : 13562
2015-10-19 17:56:59,331 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000003_1001] using containerId: [container_1445182159119_0018_02_000017 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 17:56:59,331 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000003_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:56:59,331 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000003
2015-10-19 17:56:59,378 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.7953889
2015-10-19 17:56:59,503 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.3945798
2015-10-19 17:56:59,503 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.7966491
2015-10-19 17:56:59,956 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.51750994
2015-10-19 17:57:00,268 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.44789755
2015-10-19 17:57:01,972 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.19211523
2015-10-19 17:57:01,972 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.10685723
2015-10-19 17:57:01,987 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.448704
2015-10-19 17:57:02,159 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:02,300 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.10000001
2015-10-19 17:57:02,409 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.8339531
2015-10-19 17:57:02,550 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.83539975
2015-10-19 17:57:02,784 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.27813601
2015-10-19 17:57:02,878 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.10635664
2015-10-19 17:57:02,925 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.43244186
2015-10-19 17:57:03,159 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:03,644 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.5342037
2015-10-19 17:57:03,753 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.44789755
2015-10-19 17:57:04,191 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:05,003 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.19211523
2015-10-19 17:57:05,222 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:05,316 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.10000001
2015-10-19 17:57:05,441 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.448704
2015-10-19 17:57:05,456 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.8646265
2015-10-19 17:57:05,534 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.10685723
2015-10-19 17:57:05,581 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.8660328
2015-10-19 17:57:06,269 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:06,269 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.4486067
2015-10-19 17:57:06,347 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.27813601
2015-10-19 17:57:06,425 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.10635664
2015-10-19 17:57:07,066 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.5342037
2015-10-19 17:57:07,300 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:07,300 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.44789755
2015-10-19 17:57:08,035 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.27318284
2015-10-19 17:57:08,332 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:08,347 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.10000001
2015-10-19 17:57:08,503 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.897795
2015-10-19 17:57:08,644 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.89891875
2015-10-19 17:57:08,878 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.10685723
2015-10-19 17:57:09,066 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.448704
2015-10-19 17:57:09,363 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:09,691 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.4486067
2015-10-19 17:57:09,816 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.27813601
2015-10-19 17:57:09,847 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.10635664
2015-10-19 17:57:10,222 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0018_m_000002
2015-10-19 17:57:10,222 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 17:57:10,222 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0018_m_000002
2015-10-19 17:57:10,238 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:57:10,238 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:57:10,238 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000002_1001 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 17:57:10,410 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:10,613 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.5342037
2015-10-19 17:57:10,847 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.44789755
2015-10-19 17:57:10,988 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:16 ContRel:1 HostLocal:6 RackLocal:8
2015-10-19 17:57:11,004 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-18> knownNMs=4
2015-10-19 17:57:11,082 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.27776006
2015-10-19 17:57:11,441 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.10000001
2015-10-19 17:57:11,441 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:11,582 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.9180865
2015-10-19 17:57:11,691 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.9190713
2015-10-19 17:57:12,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 17:57:12,082 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:57:12,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0018_02_000018 to attempt_1445182159119_0018_m_000002_1001
2015-10-19 17:57:12,082 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 17:57:12,082 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 17:57:12,082 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000002_1001 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 17:57:12,082 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0018_02_000018 taskAttempt attempt_1445182159119_0018_m_000002_1001
2015-10-19 17:57:12,082 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0018_m_000002_1001
2015-10-19 17:57:12,082 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:57:12,472 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.10685723
2015-10-19 17:57:12,488 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:12,519 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.501199
2015-10-19 17:57:12,519 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0018_m_000002_1001 : 13562
2015-10-19 17:57:12,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0018_m_000002_1001] using containerId: [container_1445182159119_0018_02_000018 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 17:57:12,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000002_1001 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 17:57:12,519 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0018_m_000002
2015-10-19 17:57:13,488 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.4486067
2015-10-19 17:57:13,504 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:13,582 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0018: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:8192, vCores:-19> knownNMs=4
2015-10-19 17:57:13,707 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.27813601
2015-10-19 17:57:13,801 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.10920026
2015-10-19 17:57:14,113 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.27776006
2015-10-19 17:57:14,191 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.5342037
2015-10-19 17:57:14,410 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.44789755
2015-10-19 17:57:14,519 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.10000001
2015-10-19 17:57:14,535 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:14,645 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.9522718
2015-10-19 17:57:14,754 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.95407397
2015-10-19 17:57:15,551 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:15,832 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.10685723
2015-10-19 17:57:16,410 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.5332474
2015-10-19 17:57:16,582 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:17,020 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.4486067
2015-10-19 17:57:17,051 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:57:17,145 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.29498166
2015-10-19 17:57:17,254 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.27813601
2015-10-19 17:57:17,270 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.14130817
2015-10-19 17:57:17,301 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000017 asked for a task
2015-10-19 17:57:17,301 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000017 given task: attempt_1445182159119_0018_m_000003_1001
2015-10-19 17:57:17,567 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.10000001
2015-10-19 17:57:17,629 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:17,629 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.5342037
2015-10-19 17:57:17,707 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 0.9747902
2015-10-19 17:57:17,801 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 0.97699165
2015-10-19 17:57:17,910 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.48572844
2015-10-19 17:57:18,660 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:20,270 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.53425497
2015-10-19 17:57:20,473 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.36319977
2015-10-19 17:57:20,582 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.4486067
2015-10-19 17:57:20,582 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:20,645 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.10000001
2015-10-19 17:57:20,661 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000007_1000 is : 1.0
2015-10-19 17:57:20,676 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0018_m_000007_1000
2015-10-19 17:57:20,692 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000007_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:57:20,692 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000009 taskAttempt attempt_1445182159119_0018_m_000007_1000
2015-10-19 17:57:20,692 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000007_1000
2015-10-19 17:57:20,692 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:57:21,036 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000008_1000 is : 1.0
2015-10-19 17:57:21,036 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.17308216
2015-10-19 17:57:21,036 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.2905878
2015-10-19 17:57:21,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000007_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:57:21,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0018_m_000007_1000
2015-10-19 17:57:21,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:57:21,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-19 17:57:21,051 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0018_m_000008_1000
2015-10-19 17:57:21,051 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000008_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:57:21,051 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000010 taskAttempt attempt_1445182159119_0018_m_000008_1000
2015-10-19 17:57:21,051 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000008_1000
2015-10-19 17:57:21,051 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:57:21,083 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.10685723
2015-10-19 17:57:21,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000008_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:57:21,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0018_m_000008_1000
2015-10-19 17:57:21,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:57:21,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-19 17:57:21,661 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 3 maxEvents 10000
2015-10-19 17:57:22,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 17:57:22,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000009
2015-10-19 17:57:22,145 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 17:57:22,145 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000007_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:57:22,348 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.5342037
2015-10-19 17:57:22,426 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.52873546
2015-10-19 17:57:22,817 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:23,161 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000010
2015-10-19 17:57:23,161 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000008_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:57:23,161 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 17:57:23,505 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.36319977
2015-10-19 17:57:23,661 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:57:23,817 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:23,989 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.4486067
2015-10-19 17:57:24,005 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.53425497
2015-10-19 17:57:24,583 INFO [Socket Reader #1 for port 64927] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0018 (auth:SIMPLE)
2015-10-19 17:57:24,583 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.10685723
2015-10-19 17:57:24,598 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.19158794
2015-10-19 17:57:24,598 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.3317937
2015-10-19 17:57:24,864 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:24,864 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0018_m_000018 asked for a task
2015-10-19 17:57:24,864 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0018_m_000018 given task: attempt_1445182159119_0018_m_000002_1001
2015-10-19 17:57:25,927 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:26,489 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.53341997
2015-10-19 17:57:26,536 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.36319977
2015-10-19 17:57:26,770 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:57:26,974 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.5587557
2015-10-19 17:57:26,974 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:27,536 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.4486067
2015-10-19 17:57:27,614 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.53425497
2015-10-19 17:57:28,036 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:28,036 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.12099055
2015-10-19 17:57:28,067 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.36390656
2015-10-19 17:57:28,114 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.19158794
2015-10-19 17:57:29,083 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:29,630 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.43557
2015-10-19 17:57:29,864 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:57:30,208 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:30,458 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.53341997
2015-10-19 17:57:30,974 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.6126446
2015-10-19 17:57:30,990 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.4486067
2015-10-19 17:57:31,255 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:31,583 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.53425497
2015-10-19 17:57:31,771 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.16516447
2015-10-19 17:57:31,818 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.36390656
2015-10-19 17:57:31,818 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.19158794
2015-10-19 17:57:32,287 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:32,646 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.448704
2015-10-19 17:57:32,880 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:57:33,318 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:34,177 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.53341997
2015-10-19 17:57:34,365 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:34,599 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.6196791
2015-10-19 17:57:34,615 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.4486067
2015-10-19 17:57:35,177 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.024750024
2015-10-19 17:57:35,224 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.53425497
2015-10-19 17:57:35,365 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:35,506 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.19247705
2015-10-19 17:57:35,584 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.19158794
2015-10-19 17:57:35,662 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.36390656
2015-10-19 17:57:35,677 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.448704
2015-10-19 17:57:35,927 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:57:36,396 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:37,412 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:37,709 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.53341997
2015-10-19 17:57:38,084 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.4486067
2015-10-19 17:57:38,131 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.6196791
2015-10-19 17:57:38,428 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:38,662 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.53425497
2015-10-19 17:57:38,693 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.5166581
2015-10-19 17:57:38,928 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.064325
2015-10-19 17:57:38,959 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:57:39,006 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.19247705
2015-10-19 17:57:39,225 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.19158794
2015-10-19 17:57:39,287 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.36390656
2015-10-19 17:57:39,443 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:40,490 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:41,537 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.45628068
2015-10-19 17:57:41,740 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.53425497
2015-10-19 17:57:41,756 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.53341997
2015-10-19 17:57:41,944 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:41,959 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.6196791
2015-10-19 17:57:42,006 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:57:42,600 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.19247705
2015-10-19 17:57:42,600 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.5482186
2015-10-19 17:57:42,662 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.10280651
2015-10-19 17:57:43,006 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.19158794
2015-10-19 17:57:43,006 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:43,006 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.36390656
2015-10-19 17:57:44,084 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:44,413 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.035172306
2015-10-19 17:57:44,850 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.53425497
2015-10-19 17:57:45,163 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:57:45,163 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:45,163 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.4786836
2015-10-19 17:57:45,225 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.53341997
2015-10-19 17:57:45,444 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.6196791
2015-10-19 17:57:46,053 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.58981025
2015-10-19 17:57:46,210 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:46,288 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.19247705
2015-10-19 17:57:46,475 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.106493875
2015-10-19 17:57:46,678 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.36390656
2015-10-19 17:57:46,741 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.19158794
2015-10-19 17:57:47,506 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:47,913 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.557132
2015-10-19 17:57:47,944 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.061227262
2015-10-19 17:57:48,272 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:57:48,553 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:48,882 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.5442163
2015-10-19 17:57:49,022 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.6196791
2015-10-19 17:57:49,335 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.6197233
2015-10-19 17:57:49,475 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.5175996
2015-10-19 17:57:49,569 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:49,991 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.19247705
2015-10-19 17:57:50,241 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.106493875
2015-10-19 17:57:50,444 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.36390656
2015-10-19 17:57:50,585 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.19158794
2015-10-19 17:57:50,600 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:50,960 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.6197233
2015-10-19 17:57:51,413 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:57:51,632 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:51,757 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.08974449
2015-10-19 17:57:52,663 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:53,085 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.5343203
2015-10-19 17:57:53,085 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.5736025
2015-10-19 17:57:53,085 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.6196791
2015-10-19 17:57:53,679 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.19247705
2015-10-19 17:57:53,679 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.6197233
2015-10-19 17:57:53,726 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:53,866 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.106493875
2015-10-19 17:57:53,991 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.6197233
2015-10-19 17:57:54,070 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.36390656
2015-10-19 17:57:54,179 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.19158794
2015-10-19 17:57:54,460 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:57:54,741 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:55,726 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.10660437
2015-10-19 17:57:55,773 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:56,585 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.5343203
2015-10-19 17:57:56,867 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:57,054 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.6164834
2015-10-19 17:57:57,070 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.6197233
2015-10-19 17:57:57,273 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.19247705
2015-10-19 17:57:57,398 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.106493875
2015-10-19 17:57:57,398 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.6291831
2015-10-19 17:57:57,523 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:57:57,679 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.36390656
2015-10-19 17:57:57,789 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.6197233
2015-10-19 17:57:57,914 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.19227205
2015-10-19 17:57:57,914 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:58,992 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:57:59,695 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.10660437
2015-10-19 17:57:59,914 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.6197233
2015-10-19 17:58:00,007 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.6291831
2015-10-19 17:58:00,054 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:00,117 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.667
2015-10-19 17:58:00,570 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:00,961 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.61898744
2015-10-19 17:58:01,289 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.667
2015-10-19 17:58:01,601 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.106493875
2015-10-19 17:58:01,648 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.36390656
2015-10-19 17:58:01,789 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.21967994
2015-10-19 17:58:01,789 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.19247705
2015-10-19 17:58:01,789 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.6197233
2015-10-19 17:58:01,883 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.5343203
2015-10-19 17:58:01,945 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:02,976 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:03,148 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.667
2015-10-19 17:58:03,617 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.10660437
2015-10-19 17:58:03,617 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:04,039 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:05,039 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.61898744
2015-10-19 17:58:05,102 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:05,352 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.106493875
2015-10-19 17:58:05,352 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.36390656
2015-10-19 17:58:05,367 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.667
2015-10-19 17:58:05,570 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.5343203
2015-10-19 17:58:05,586 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.19247705
2015-10-19 17:58:05,586 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.25492403
2015-10-19 17:58:05,899 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.6197233
2015-10-19 17:58:06,149 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:06,195 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.667
2015-10-19 17:58:06,664 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:07,196 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:07,742 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.10660437
2015-10-19 17:58:08,211 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:08,930 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.106493875
2015-10-19 17:58:08,977 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.61898744
2015-10-19 17:58:09,211 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.3858811
2015-10-19 17:58:09,289 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:09,289 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.667
2015-10-19 17:58:09,289 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.6723869
2015-10-19 17:58:09,430 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.5343203
2015-10-19 17:58:09,430 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.19247705
2015-10-19 17:58:09,477 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.27696857
2015-10-19 17:58:09,602 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.6197233
2015-10-19 17:58:09,696 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:10,352 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:11,415 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:11,415 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.10660437
2015-10-19 17:58:12,368 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.69081193
2015-10-19 17:58:12,493 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:12,680 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.106493875
2015-10-19 17:58:12,759 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:12,962 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.61898744
2015-10-19 17:58:13,009 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.41959402
2015-10-19 17:58:13,055 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.5343203
2015-10-19 17:58:13,087 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.27696857
2015-10-19 17:58:13,087 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.19247705
2015-10-19 17:58:13,165 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.667
2015-10-19 17:58:13,462 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.6197233
2015-10-19 17:58:13,524 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:14,571 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:15,212 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.10660437
2015-10-19 17:58:15,431 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.7107067
2015-10-19 17:58:15,634 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:15,806 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:16,587 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.106493875
2015-10-19 17:58:16,649 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:16,712 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.44864044
2015-10-19 17:58:16,743 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.27696857
2015-10-19 17:58:16,743 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.667
2015-10-19 17:58:16,806 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.61898744
2015-10-19 17:58:16,868 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.21023323
2015-10-19 17:58:16,899 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.5343203
2015-10-19 17:58:17,493 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.65136665
2015-10-19 17:58:17,712 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:18,478 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.65136665
2015-10-19 17:58:18,525 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.72890353
2015-10-19 17:58:18,759 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:18,853 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:19,025 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.10660437
2015-10-19 17:58:19,822 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:20,150 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.106493875
2015-10-19 17:58:20,447 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.44950968
2015-10-19 17:58:20,509 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.27696857
2015-10-19 17:58:20,509 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.24328375
2015-10-19 17:58:20,650 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.61898744
2015-10-19 17:58:20,728 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.667
2015-10-19 17:58:20,759 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.5343203
2015-10-19 17:58:20,869 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:21,384 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.667
2015-10-19 17:58:21,556 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.7491968
2015-10-19 17:58:21,931 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:21,931 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:22,759 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.117203236
2015-10-19 17:58:23,041 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:24,134 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:24,134 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.106508166
2015-10-19 17:58:24,306 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.62628853
2015-10-19 17:58:24,306 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.67060995
2015-10-19 17:58:24,322 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.44950968
2015-10-19 17:58:24,338 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.27696857
2015-10-19 17:58:24,353 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.27198297
2015-10-19 17:58:24,619 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.7857799
2015-10-19 17:58:24,838 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.667
2015-10-19 17:58:24,853 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.5343203
2015-10-19 17:58:25,025 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:25,197 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:26,103 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.14231999
2015-10-19 17:58:26,275 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:27,557 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.6425716
2015-10-19 17:58:27,588 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:27,635 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.69447124
2015-10-19 17:58:27,666 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.8200277
2015-10-19 17:58:27,791 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.14188068
2015-10-19 17:58:27,900 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.44950968
2015-10-19 17:58:28,025 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.27696857
2015-10-19 17:58:28,072 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:28,135 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.27813601
2015-10-19 17:58:28,353 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.667
2015-10-19 17:58:28,510 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.5343203
2015-10-19 17:58:28,619 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:29,541 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.17139554
2015-10-19 17:58:29,682 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:30,744 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.8395775
2015-10-19 17:58:30,744 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:31,135 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:31,526 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.17747502
2015-10-19 17:58:31,619 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.44950968
2015-10-19 17:58:31,666 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.667
2015-10-19 17:58:31,729 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.27696857
2015-10-19 17:58:31,776 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:31,776 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.667
2015-10-19 17:58:31,838 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.27813601
2015-10-19 17:58:31,838 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.71178705
2015-10-19 17:58:32,073 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.5389985
2015-10-19 17:58:32,338 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.667
2015-10-19 17:58:33,479 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.19212553
2015-10-19 17:58:33,682 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:33,776 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.8733364
2015-10-19 17:58:34,182 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:34,682 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:35,245 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.19209063
2015-10-19 17:58:35,448 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.44950968
2015-10-19 17:58:35,651 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.27696857
2015-10-19 17:58:35,667 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.667
2015-10-19 17:58:35,682 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.27813601
2015-10-19 17:58:35,729 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:35,948 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.57511616
2015-10-19 17:58:35,948 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.72846925
2015-10-19 17:58:36,198 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.667
2015-10-19 17:58:36,792 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:36,870 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.9067515
2015-10-19 17:58:37,276 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:37,448 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.19212553
2015-10-19 17:58:37,917 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:38,979 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:39,276 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.19209063
2015-10-19 17:58:39,339 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.44950968
2015-10-19 17:58:39,604 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.27696857
2015-10-19 17:58:39,714 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.60714394
2015-10-19 17:58:39,714 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.27813601
2015-10-19 17:58:39,823 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.667
2015-10-19 17:58:39,964 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.93909913
2015-10-19 17:58:40,042 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:40,214 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.667
2015-10-19 17:58:40,323 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.7428814
2015-10-19 17:58:40,479 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:41,120 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:41,386 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.19212553
2015-10-19 17:58:42,214 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:42,917 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.44950968
2015-10-19 17:58:42,964 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.19209063
2015-10-19 17:58:43,073 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.95739776
2015-10-19 17:58:43,308 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:43,308 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.27696857
2015-10-19 17:58:43,495 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.27813601
2015-10-19 17:58:43,495 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.6199081
2015-10-19 17:58:43,542 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:43,902 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.667
2015-10-19 17:58:44,417 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.6737305
2015-10-19 17:58:44,417 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:44,574 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.7566703
2015-10-19 17:58:45,370 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.19212553
2015-10-19 17:58:45,464 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:46,136 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 0.9770302
2015-10-19 17:58:46,558 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:46,636 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:46,917 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.19209063
2015-10-19 17:58:46,917 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.44950968
2015-10-19 17:58:47,214 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.27696857
2015-10-19 17:58:47,277 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.6199081
2015-10-19 17:58:47,527 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.27813601
2015-10-19 17:58:47,652 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:47,808 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.667
2015-10-19 17:58:48,386 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.7711255
2015-10-19 17:58:48,433 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1000 is : 0.68877065
2015-10-19 17:58:48,699 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:48,886 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000001_1001 is : 1.0
2015-10-19 17:58:48,949 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0018_m_000001_1001
2015-10-19 17:58:48,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000001_1001 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:58:48,949 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000016 taskAttempt attempt_1445182159119_0018_m_000001_1001
2015-10-19 17:58:48,949 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000001_1001
2015-10-19 17:58:48,949 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 17:58:49,340 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.19212553
2015-10-19 17:58:49,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000001_1001 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:58:49,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0018_m_000001_1001
2015-10-19 17:58:49,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0018_m_000001_1000
2015-10-19 17:58:49,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:58:49,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-19 17:58:49,355 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000001_1000 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 17:58:49,355 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000003 taskAttempt attempt_1445182159119_0018_m_000001_1000
2015-10-19 17:58:49,355 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000001_1000
2015-10-19 17:58:49,371 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:58:49,668 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.16666667
2015-10-19 17:58:49,777 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 5 maxEvents 10000
2015-10-19 17:58:50,387 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0018_m_000001
2015-10-19 17:58:50,387 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 17:58:50,418 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 17:58:50,418 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000016
2015-10-19 17:58:50,418 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 17:58:50,418 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000001_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:58:50,449 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000001_1000 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 17:58:50,574 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.19209063
2015-10-19 17:58:50,652 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 17:58:50,652 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.44950968
2015-10-19 17:58:50,902 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:58:50,902 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.29771745
2015-10-19 17:58:50,902 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out2/_temporary/2/_temporary/attempt_1445182159119_0018_m_000001_1000
2015-10-19 17:58:50,902 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000001_1000 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 17:58:50,933 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.6199081
2015-10-19 17:58:51,262 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.27813601
2015-10-19 17:58:51,574 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.667
2015-10-19 17:58:51,934 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:58:52,012 INFO [Socket Reader #1 for port 64927] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 64927: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 17:58:52,090 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.78500926
2015-10-19 17:58:52,746 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:58:52,980 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:58:53,137 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.19212553
2015-10-19 17:58:53,543 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000003
2015-10-19 17:58:53,543 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 17:58:53,543 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000001_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:58:54,012 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:58:54,246 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.19209063
2015-10-19 17:58:54,559 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.44950968
2015-10-19 17:58:54,574 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.33079928
2015-10-19 17:58:54,606 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.6199081
2015-10-19 17:58:54,840 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.27813601
2015-10-19 17:58:55,027 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:58:55,137 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.667
2015-10-19 17:58:55,606 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.80288494
2015-10-19 17:58:55,793 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:58:56,090 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:58:56,684 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.19212553
2015-10-19 17:58:57,106 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:58:58,012 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.19209063
2015-10-19 17:58:58,168 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:58:58,278 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.3599444
2015-10-19 17:58:58,418 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.44950968
2015-10-19 17:58:58,465 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.6199081
2015-10-19 17:58:58,715 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.27813601
2015-10-19 17:58:58,825 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.6717534
2015-10-19 17:58:58,856 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:58:59,215 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:58:59,215 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.8197479
2015-10-19 17:59:00,262 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:00,294 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.19212553
2015-10-19 17:59:01,340 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:01,606 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.19209063
2015-10-19 17:59:01,856 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.3624012
2015-10-19 17:59:01,981 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:02,044 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.47225592
2015-10-19 17:59:02,059 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.6199081
2015-10-19 17:59:02,231 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.69014364
2015-10-19 17:59:02,403 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:02,403 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.27813601
2015-10-19 17:59:02,591 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.8383986
2015-10-19 17:59:03,575 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:03,841 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.22504644
2015-10-19 17:59:04,638 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:05,466 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.7056888
2015-10-19 17:59:05,575 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.19209063
2015-10-19 17:59:05,575 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.5081734
2015-10-19 17:59:05,763 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.6199081
2015-10-19 17:59:05,763 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.3624012
2015-10-19 17:59:05,935 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:05,935 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:06,138 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.8572368
2015-10-19 17:59:06,138 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.27813601
2015-10-19 17:59:06,982 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:07,560 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.26718706
2015-10-19 17:59:08,029 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:08,982 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.72336173
2015-10-19 17:59:09,013 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:09,060 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:09,263 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.5352021
2015-10-19 17:59:09,388 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.19209063
2015-10-19 17:59:09,622 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.3624012
2015-10-19 17:59:09,622 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.6199081
2015-10-19 17:59:09,638 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.87450194
2015-10-19 17:59:09,888 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.29644322
2015-10-19 17:59:10,091 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:11,060 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.27772525
2015-10-19 17:59:11,107 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:12,107 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:12,138 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:12,466 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.74384314
2015-10-19 17:59:12,826 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.5352021
2015-10-19 17:59:13,029 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.19209063
2015-10-19 17:59:13,185 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:13,201 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.3624012
2015-10-19 17:59:13,232 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.8944082
2015-10-19 17:59:13,279 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.6199081
2015-10-19 17:59:13,591 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.32968476
2015-10-19 17:59:14,217 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:14,545 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.27772525
2015-10-19 17:59:15,138 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:15,232 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:15,904 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.7639405
2015-10-19 17:59:16,560 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:16,623 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.5352021
2015-10-19 17:59:16,920 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.91570467
2015-10-19 17:59:16,935 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.20913604
2015-10-19 17:59:17,045 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.3624012
2015-10-19 17:59:17,404 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.6199081
2015-10-19 17:59:17,436 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.36021554
2015-10-19 17:59:17,576 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:18,014 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.27772525
2015-10-19 17:59:18,186 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:18,639 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:19,639 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.7857475
2015-10-19 17:59:19,686 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:20,295 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.5352021
2015-10-19 17:59:20,514 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.93580043
2015-10-19 17:59:20,545 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.3624012
2015-10-19 17:59:20,545 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.24262787
2015-10-19 17:59:20,733 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:21,092 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.6199081
2015-10-19 17:59:21,248 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:21,358 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.36390656
2015-10-19 17:59:21,545 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.27772525
2015-10-19 17:59:22,639 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:23,108 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.80535686
2015-10-19 17:59:23,670 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:23,936 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.9546573
2015-10-19 17:59:23,983 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.5352021
2015-10-19 17:59:24,139 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.3624012
2015-10-19 17:59:24,202 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.27157965
2015-10-19 17:59:24,311 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:24,686 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:24,827 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.64085615
2015-10-19 17:59:24,999 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.27772525
2015-10-19 17:59:25,046 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.36390656
2015-10-19 17:59:25,702 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:26,577 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.8251642
2015-10-19 17:59:26,733 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:27,343 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:27,389 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.97379804
2015-10-19 17:59:27,749 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.5352021
2015-10-19 17:59:27,764 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:27,889 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.3624012
2015-10-19 17:59:27,889 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.27765483
2015-10-19 17:59:28,483 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.36390656
2015-10-19 17:59:28,499 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.667
2015-10-19 17:59:28,499 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.667
2015-10-19 17:59:28,546 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.2789185
2015-10-19 17:59:28,796 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:29,843 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.8461174
2015-10-19 17:59:29,874 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:30,671 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 0.99596274
2015-10-19 17:59:30,687 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:30,890 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:31,405 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.5352021
2015-10-19 17:59:31,437 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.27765483
2015-10-19 17:59:31,577 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1001 is : 0.29962674
2015-10-19 17:59:31,609 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.3624012
2015-10-19 17:59:31,921 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:32,062 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.667
2015-10-19 17:59:32,155 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000002_1000 is : 1.0
2015-10-19 17:59:32,155 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0018_m_000002_1000
2015-10-19 17:59:32,155 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000002_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:59:32,155 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000004 taskAttempt attempt_1445182159119_0018_m_000002_1000
2015-10-19 17:59:32,155 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000002_1000
2015-10-19 17:59:32,155 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:59:32,187 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000002_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:59:32,187 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0018_m_000002_1000
2015-10-19 17:59:32,187 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0018_m_000002_1001
2015-10-19 17:59:32,187 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:59:32,187 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-19 17:59:32,187 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000002_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 17:59:32,202 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000018 taskAttempt attempt_1445182159119_0018_m_000002_1001
2015-10-19 17:59:32,202 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000002_1001
2015-10-19 17:59:32,202 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:59:32,249 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000002_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 17:59:32,265 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.36390656
2015-10-19 17:59:32,280 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 17:59:32,499 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out2/_temporary/2/_temporary/attempt_1445182159119_0018_m_000002_1001
2015-10-19 17:59:32,499 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000002_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 17:59:32,702 INFO [Socket Reader #1 for port 64927] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 64927: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 17:59:32,890 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.8657787
2015-10-19 17:59:32,937 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 6 maxEvents 10000
2015-10-19 17:59:33,109 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 17:59:33,781 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:33,984 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:34,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000004
2015-10-19 17:59:34,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000018
2015-10-19 17:59:34,218 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000002_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:59:34,218 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 17:59:34,218 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000002_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:59:34,999 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.5352021
2015-10-19 17:59:35,015 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:35,093 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.3624012
2015-10-19 17:59:35,140 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.27765483
2015-10-19 17:59:35,765 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.667
2015-10-19 17:59:35,859 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.36390656
2015-10-19 17:59:36,062 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:36,281 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.89253706
2015-10-19 17:59:36,890 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:37,109 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:38,156 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:38,468 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.5352021
2015-10-19 17:59:38,672 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.27765483
2015-10-19 17:59:38,765 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.37522066
2015-10-19 17:59:39,219 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:39,219 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.667
2015-10-19 17:59:39,406 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.36390656
2015-10-19 17:59:39,453 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.91689265
2015-10-19 17:59:39,953 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:40,250 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:41,281 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:42,141 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.5352021
2015-10-19 17:59:42,141 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.41095832
2015-10-19 17:59:42,313 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:42,531 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.27765483
2015-10-19 17:59:42,563 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.9430256
2015-10-19 17:59:42,891 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.667
2015-10-19 17:59:42,969 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.36390656
2015-10-19 17:59:43,016 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:43,359 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:44,610 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:45,641 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:45,813 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.96911716
2015-10-19 17:59:45,860 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.44303095
2015-10-19 17:59:45,953 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.5352021
2015-10-19 17:59:46,078 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:46,250 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.27765483
2015-10-19 17:59:46,641 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.667
2015-10-19 17:59:46,672 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:46,813 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.36390656
2015-10-19 17:59:47,704 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:48,735 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:49,157 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.20000002
2015-10-19 17:59:49,204 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 0.9991892
2015-10-19 17:59:49,688 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.5453012
2015-10-19 17:59:49,751 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:49,891 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.44789755
2015-10-19 17:59:49,907 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.27765483
2015-10-19 17:59:50,376 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.667
2015-10-19 17:59:50,469 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.36390656
2015-10-19 17:59:50,798 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:51,813 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:52,204 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.23333333
2015-10-19 17:59:52,235 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 1.0
2015-10-19 17:59:52,454 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1000 is : 1.0
2015-10-19 17:59:52,454 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0018_m_000000_1000
2015-10-19 17:59:52,454 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000000_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 17:59:52,454 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000002 taskAttempt attempt_1445182159119_0018_m_000000_1000
2015-10-19 17:59:52,454 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000000_1000
2015-10-19 17:59:52,454 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 17:59:52,485 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000000_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 17:59:52,485 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0018_m_000000_1000
2015-10-19 17:59:52,485 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0018_m_000000_1001
2015-10-19 17:59:52,485 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 17:59:52,485 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-19 17:59:52,485 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000000_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 17:59:52,485 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000014 taskAttempt attempt_1445182159119_0018_m_000000_1001
2015-10-19 17:59:52,485 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000000_1001
2015-10-19 17:59:52,485 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 17:59:52,845 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 7 maxEvents 10000
2015-10-19 17:59:52,860 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 17:59:53,282 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.5794405
2015-10-19 17:59:53,392 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000000_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 17:59:53,392 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 17:59:53,454 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0018_m_000000
2015-10-19 17:59:53,454 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 17:59:53,579 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out2/_temporary/2/_temporary/attempt_1445182159119_0018_m_000000_1001
2015-10-19 17:59:53,579 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000000_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 17:59:53,626 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.27765483
2015-10-19 17:59:53,642 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000000_1001 is : 0.44789755
2015-10-19 17:59:53,907 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 17:59:53,907 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000002
2015-10-19 17:59:53,907 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 17:59:53,907 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000000_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:59:54,220 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.36390656
2015-10-19 17:59:54,220 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.667
2015-10-19 17:59:54,829 INFO [Socket Reader #1 for port 64927] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 64927: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 17:59:54,985 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 17:59:55,314 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.23333333
2015-10-19 17:59:56,064 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 17:59:56,064 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000014
2015-10-19 17:59:56,064 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 17:59:56,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000000_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 17:59:56,861 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.61178505
2015-10-19 17:59:57,126 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 17:59:57,282 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.27765483
2015-10-19 17:59:57,876 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.36390656
2015-10-19 17:59:58,142 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.667
2015-10-19 17:59:58,173 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 17:59:58,361 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.23333333
2015-10-19 17:59:59,204 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:00,267 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.6209487
2015-10-19 18:00:00,298 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:00,720 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.27765483
2015-10-19 18:00:01,642 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:01,642 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.23333333
2015-10-19 18:00:01,689 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.39151195
2015-10-19 18:00:01,877 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.667
2015-10-19 18:00:02,674 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:03,720 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:03,830 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.6209487
2015-10-19 18:00:04,267 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.2894808
2015-10-19 18:00:05,596 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:05,596 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.23333333
2015-10-19 18:00:05,611 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.673608
2015-10-19 18:00:06,189 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.43299374
2015-10-19 18:00:06,627 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:07,361 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.6209487
2015-10-19 18:00:07,643 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:07,768 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.32565653
2015-10-19 18:00:08,658 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:08,658 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.23333333
2015-10-19 18:00:09,408 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.6846974
2015-10-19 18:00:09,643 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.44950968
2015-10-19 18:00:09,830 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:10,784 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.6209487
2015-10-19 18:00:10,862 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:11,221 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.36323506
2015-10-19 18:00:11,706 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:11,862 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:12,799 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.6971308
2015-10-19 18:00:12,877 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:12,971 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.44950968
2015-10-19 18:00:13,893 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:14,456 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.6209487
2015-10-19 18:00:14,471 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.36323506
2015-10-19 18:00:14,815 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:14,924 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:15,940 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:16,081 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.70974386
2015-10-19 18:00:16,253 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.44950968
2015-10-19 18:00:16,956 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:17,768 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.6209487
2015-10-19 18:00:17,815 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.36323506
2015-10-19 18:00:17,862 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:18,019 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:19,034 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:19,409 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.72298336
2015-10-19 18:00:19,628 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.44950968
2015-10-19 18:00:20,050 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:20,894 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:20,972 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.6209487
2015-10-19 18:00:21,066 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:21,112 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.36323506
2015-10-19 18:00:22,066 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:22,675 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.73594046
2015-10-19 18:00:23,003 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.44950968
2015-10-19 18:00:23,066 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:23,941 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:24,081 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:24,300 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.6209487
2015-10-19 18:00:24,456 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.36323506
2015-10-19 18:00:25,097 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:25,972 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.74782866
2015-10-19 18:00:26,097 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:26,269 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.44950968
2015-10-19 18:00:26,988 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:27,113 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:27,582 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.6209487
2015-10-19 18:00:27,785 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.36323506
2015-10-19 18:00:28,113 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:29,113 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:29,598 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.44950968
2015-10-19 18:00:29,676 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.76044714
2015-10-19 18:00:30,035 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:30,129 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:31,004 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.6659449
2015-10-19 18:00:31,098 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.6659449
2015-10-19 18:00:31,098 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.36323506
2015-10-19 18:00:31,145 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:32,145 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:32,957 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.44950968
2015-10-19 18:00:33,067 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:33,098 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.77388
2015-10-19 18:00:33,145 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:34,176 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:34,348 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.667
2015-10-19 18:00:34,379 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.36323506
2015-10-19 18:00:35,192 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:36,114 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:36,192 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:36,286 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.46969542
2015-10-19 18:00:36,457 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.78731996
2015-10-19 18:00:37,208 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:37,676 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.667
2015-10-19 18:00:37,708 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.37091354
2015-10-19 18:00:38,208 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:39,145 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:39,208 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:39,786 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.515465
2015-10-19 18:00:39,895 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.8008233
2015-10-19 18:00:40,223 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:41,177 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.667
2015-10-19 18:00:41,239 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:41,286 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.41570795
2015-10-19 18:00:42,192 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:42,255 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:43,239 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.5352021
2015-10-19 18:00:43,270 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:43,349 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.81210697
2015-10-19 18:00:44,286 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:44,630 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.667
2015-10-19 18:00:44,646 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.4486067
2015-10-19 18:00:45,239 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:45,302 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:46,302 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:46,583 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.5352021
2015-10-19 18:00:46,708 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.8253738
2015-10-19 18:00:47,302 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:47,958 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.4486067
2015-10-19 18:00:47,990 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.667
2015-10-19 18:00:48,271 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:48,302 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:49,318 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:49,880 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.5352021
2015-10-19 18:00:50,037 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.8383498
2015-10-19 18:00:50,349 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:51,271 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.667
2015-10-19 18:00:51,302 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.4486067
2015-10-19 18:00:51,318 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:51,349 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:52,365 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:53,256 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.5352021
2015-10-19 18:00:53,365 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:53,396 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.85245776
2015-10-19 18:00:54,349 INFO [IPC Server handler 20 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:54,365 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:54,521 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.667
2015-10-19 18:00:54,631 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.4486067
2015-10-19 18:00:55,365 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:56,412 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:56,553 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.5352021
2015-10-19 18:00:56,709 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.86451614
2015-10-19 18:00:57,397 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:00:57,412 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:57,850 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.6736346
2015-10-19 18:00:57,990 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.4486067
2015-10-19 18:00:58,412 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:59,412 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:00:59,866 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.5352021
2015-10-19 18:00:59,959 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.87655854
2015-10-19 18:01:00,428 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:00,444 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:01:01,131 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.6859526
2015-10-19 18:01:01,209 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.4486067
2015-10-19 18:01:01,428 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:02,444 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:03,210 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.5352021
2015-10-19 18:01:03,319 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.8896533
2015-10-19 18:01:03,444 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:03,475 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:01:04,444 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:04,475 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.6980136
2015-10-19 18:01:04,553 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.4486067
2015-10-19 18:01:05,460 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:06,460 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:06,475 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.5352021
2015-10-19 18:01:06,522 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:01:06,710 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.90262115
2015-10-19 18:01:07,460 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:07,866 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.7093738
2015-10-19 18:01:07,991 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.4486067
2015-10-19 18:01:08,460 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:09,476 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:09,570 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:01:09,804 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.5352021
2015-10-19 18:01:10,007 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.9143419
2015-10-19 18:01:10,476 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:11,179 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.7204458
2015-10-19 18:01:11,335 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.4566087
2015-10-19 18:01:11,476 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:12,476 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:12,601 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:01:13,163 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.57707524
2015-10-19 18:01:13,429 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.9274205
2015-10-19 18:01:13,492 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:14,492 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:14,679 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.73236346
2015-10-19 18:01:14,882 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.50356084
2015-10-19 18:01:15,492 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:15,648 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:01:16,508 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:16,992 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.61605555
2015-10-19 18:01:17,133 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.9385616
2015-10-19 18:01:17,508 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:18,039 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.74138856
2015-10-19 18:01:18,195 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.5343203
2015-10-19 18:01:18,508 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:18,680 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:01:19,508 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:20,180 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.6209487
2015-10-19 18:01:20,336 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.95360935
2015-10-19 18:01:20,524 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:21,242 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.75676835
2015-10-19 18:01:21,445 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.5343203
2015-10-19 18:01:21,524 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:21,727 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:01:22,524 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:23,399 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.6209487
2015-10-19 18:01:23,524 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:23,727 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.9715591
2015-10-19 18:01:24,446 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.7732293
2015-10-19 18:01:24,539 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:24,586 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.5343203
2015-10-19 18:01:24,852 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:01:25,758 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:26,680 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.6209487
2015-10-19 18:01:27,008 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:27,008 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 0.9903326
2015-10-19 18:01:27,883 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1001 is : 0.5343203
2015-10-19 18:01:27,883 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:01:28,008 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:28,352 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.788839
2015-10-19 18:01:29,009 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:29,040 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000003_1000 is : 1.0
2015-10-19 18:01:29,040 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0018_m_000003_1000
2015-10-19 18:01:29,040 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000003_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 18:01:29,040 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000005 taskAttempt attempt_1445182159119_0018_m_000003_1000
2015-10-19 18:01:29,040 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000003_1000
2015-10-19 18:01:29,055 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 18:01:29,087 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000003_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 18:01:29,087 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0018_m_000003_1000
2015-10-19 18:01:29,087 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0018_m_000003_1001
2015-10-19 18:01:29,087 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 18:01:29,087 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-19 18:01:29,087 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000003_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 18:01:29,087 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000017 taskAttempt attempt_1445182159119_0018_m_000003_1001
2015-10-19 18:01:29,087 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000003_1001
2015-10-19 18:01:29,087 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 18:01:29,196 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000003_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 18:01:29,196 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 18:01:29,212 WARN [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out2/_temporary/2/_temporary/attempt_1445182159119_0018_m_000003_1001
2015-10-19 18:01:29,212 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000003_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 18:01:29,415 INFO [Socket Reader #1 for port 64927] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 64927: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 18:01:29,727 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.6209487
2015-10-19 18:01:30,009 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 18:01:30,024 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 8 maxEvents 10000
2015-10-19 18:01:30,931 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:01:31,009 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000005
2015-10-19 18:01:31,009 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000017
2015-10-19 18:01:31,009 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000003_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 18:01:31,009 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 18:01:31,009 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000003_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 18:01:31,024 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:31,415 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.80743015
2015-10-19 18:01:32,024 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:32,790 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.6209487
2015-10-19 18:01:33,024 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:33,978 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:01:34,040 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:34,603 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.82444745
2015-10-19 18:01:35,040 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:35,868 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.6209487
2015-10-19 18:01:36,040 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:37,009 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:01:37,056 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:37,665 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.8413676
2015-10-19 18:01:38,056 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:38,900 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.6513667
2015-10-19 18:01:39,056 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:40,056 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.26666668
2015-10-19 18:01:40,072 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:40,134 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.6513667
2015-10-19 18:01:40,822 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.8603797
2015-10-19 18:01:41,072 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:41,947 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.667
2015-10-19 18:01:42,072 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:43,088 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:43,088 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.3
2015-10-19 18:01:43,869 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.885641
2015-10-19 18:01:44,088 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:44,994 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.667
2015-10-19 18:01:45,088 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:46,104 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:46,135 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.3
2015-10-19 18:01:46,916 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.91127694
2015-10-19 18:01:47,104 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:48,057 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.667
2015-10-19 18:01:48,104 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:49,104 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:49,182 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.3
2015-10-19 18:01:49,963 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.93545854
2015-10-19 18:01:50,120 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:51,088 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.667
2015-10-19 18:01:51,120 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:52,120 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:52,214 INFO [IPC Server handler 23 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.3
2015-10-19 18:01:53,010 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.95804894
2015-10-19 18:01:53,135 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:54,136 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.68659765
2015-10-19 18:01:54,136 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:55,136 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:55,261 INFO [IPC Server handler 1 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.3
2015-10-19 18:01:56,042 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 0.98209965
2015-10-19 18:01:56,151 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:57,151 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:57,183 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1001 is : 0.7130274
2015-10-19 18:01:58,151 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:01:58,292 INFO [IPC Server handler 22 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.3
2015-10-19 18:01:58,401 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_m_000005_1000 is : 1.0
2015-10-19 18:01:58,401 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0018_m_000005_1000
2015-10-19 18:01:58,401 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000005_1000 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 18:01:58,401 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000007 taskAttempt attempt_1445182159119_0018_m_000005_1000
2015-10-19 18:01:58,401 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000005_1000
2015-10-19 18:01:58,401 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 18:01:58,448 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000005_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 18:01:58,448 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0018_m_000005_1000
2015-10-19 18:01:58,448 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0018_m_000005_1001
2015-10-19 18:01:58,448 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 18:01:58,448 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-19 18:01:58,448 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000005_1001 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 18:01:58,448 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000015 taskAttempt attempt_1445182159119_0018_m_000005_1001
2015-10-19 18:01:58,448 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_m_000005_1001
2015-10-19 18:01:58,448 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 18:01:58,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000005_1001 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 18:01:58,495 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 18:01:58,495 WARN [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out2/_temporary/2/_temporary/attempt_1445182159119_0018_m_000005_1001
2015-10-19 18:01:58,495 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_m_000005_1001 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 18:01:58,683 INFO [Socket Reader #1 for port 64927] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 64927: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 18:01:59,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 18:01:59,151 INFO [IPC Server handler 25 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 9 maxEvents 10000
2015-10-19 18:02:00,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000007
2015-10-19 18:02:00,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0018_02_000015
2015-10-19 18:02:00,042 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000005_1000: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 18:02:00,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 18:02:00,042 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0018_m_000005_1001: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 18:02:00,167 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 18:02:01,167 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 18:02:01,339 INFO [IPC Server handler 13 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.3
2015-10-19 18:02:02,167 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 18:02:03,183 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 18:02:04,183 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 18:02:04,386 INFO [IPC Server handler 21 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.3
2015-10-19 18:02:05,183 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 18:02:06,199 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 18:02:07,214 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 18:02:07,449 INFO [IPC Server handler 12 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.3
2015-10-19 18:02:08,230 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 18:02:09,230 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0018_r_000000_1000. startIndex 10 maxEvents 10000
2015-10-19 18:02:09,637 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.3
2015-10-19 18:02:09,699 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.3
2015-10-19 18:02:10,480 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.6667442
2015-10-19 18:02:13,527 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.67469454
2015-10-19 18:02:16,574 INFO [IPC Server handler 24 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.6848164
2015-10-19 18:02:19,606 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.70072997
2015-10-19 18:02:22,637 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.7114081
2015-10-19 18:02:25,684 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.725955
2015-10-19 18:02:28,732 INFO [IPC Server handler 7 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.7414013
2015-10-19 18:02:31,779 INFO [IPC Server handler 8 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.7508623
2015-10-19 18:02:34,810 INFO [IPC Server handler 5 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.7628613
2015-10-19 18:02:37,857 INFO [IPC Server handler 9 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.7796451
2015-10-19 18:02:40,889 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.79818386
2015-10-19 18:02:43,920 INFO [IPC Server handler 28 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.8113773
2015-10-19 18:02:46,951 INFO [IPC Server handler 27 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.8329296
2015-10-19 18:02:49,999 INFO [IPC Server handler 4 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.8445002
2015-10-19 18:02:53,030 INFO [IPC Server handler 6 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.8589246
2015-10-19 18:02:56,077 INFO [IPC Server handler 26 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.8749903
2015-10-19 18:02:59,109 INFO [IPC Server handler 16 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.8925982
2015-10-19 18:03:02,156 INFO [IPC Server handler 17 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.910984
2015-10-19 18:03:05,187 INFO [IPC Server handler 15 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.9256921
2015-10-19 18:03:08,234 INFO [IPC Server handler 10 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.9387835
2015-10-19 18:03:11,266 INFO [IPC Server handler 2 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.9578136
2015-10-19 18:03:14,297 INFO [IPC Server handler 29 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.9742087
2015-10-19 18:03:17,344 INFO [IPC Server handler 3 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.98430324
2015-10-19 18:03:20,391 INFO [IPC Server handler 18 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 0.9990057
2015-10-19 18:03:20,876 INFO [IPC Server handler 19 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445182159119_0018_r_000000_1000
2015-10-19 18:03:20,891 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_r_000000_1000 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-19 18:03:20,891 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445182159119_0018_r_000000_1000 given a go for committing the task output.
2015-10-19 18:03:20,891 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445182159119_0018_r_000000_1000
2015-10-19 18:03:20,891 INFO [IPC Server handler 0 on 64927] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445182159119_0018_r_000000_1000:true
2015-10-19 18:03:20,938 INFO [IPC Server handler 14 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0018_r_000000_1000 is : 1.0
2015-10-19 18:03:20,938 INFO [IPC Server handler 11 on 64927] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0018_r_000000_1000
2015-10-19 18:03:20,938 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_r_000000_1000 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 18:03:20,938 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0018_02_000013 taskAttempt attempt_1445182159119_0018_r_000000_1000
2015-10-19 18:03:20,954 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0018_r_000000_1000
2015-10-19 18:03:20,969 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 18:03:21,001 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0018_r_000000_1000 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 18:03:21,001 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0018_r_000000_1000
2015-10-19 18:03:21,001 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0018_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 18:03:21,001 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-19 18:03:21,016 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0018Job Transitioned from RUNNING to COMMITTING
2015-10-19 18:03:21,016 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-19 18:03:21,063 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 18:03:21,110 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-19 18:03:21,110 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0018Job Transitioned from COMMITTING to SUCCEEDED
2015-10-19 18:03:21,110 INFO [Thread-117] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-19 18:03:21,110 INFO [Thread-117] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-19 18:03:21,110 INFO [Thread-117] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-19 18:03:21,110 INFO [Thread-117] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-19 18:03:21,110 INFO [Thread-117] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-19 18:03:21,110 INFO [Thread-117] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-19 18:03:21,110 INFO [Thread-117] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-19 18:03:21,251 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0018/job_1445182159119_0018_2.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0018-1445247551536-msrabi-pagerank-1445249001110-10-1-SUCCEEDED-default-1445248396740.jhist_tmp
2015-10-19 18:03:21,407 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0018-1445247551536-msrabi-pagerank-1445249001110-10-1-SUCCEEDED-default-1445248396740.jhist_tmp
2015-10-19 18:03:21,407 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0018/job_1445182159119_0018_2_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0018_conf.xml_tmp
2015-10-19 18:03:21,563 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0018_conf.xml_tmp
2015-10-19 18:03:21,579 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0018.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0018.summary
2015-10-19 18:03:21,579 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0018_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0018_conf.xml
2015-10-19 18:03:21,579 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0018-1445247551536-msrabi-pagerank-1445249001110-10-1-SUCCEEDED-default-1445248396740.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0018-1445247551536-msrabi-pagerank-1445249001110-10-1-SUCCEEDED-default-1445248396740.jhist
2015-10-19 18:03:21,579 INFO [Thread-117] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-19 18:03:21,594 INFO [Thread-117] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-19 18:03:21,594 INFO [Thread-117] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MININT-FNANLI5.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445182159119_0018
2015-10-19 18:03:21,594 INFO [Thread-117] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-19 18:03:22,595 INFO [Thread-117] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:17 ContRel:1 HostLocal:6 RackLocal:9
2015-10-19 18:03:22,595 INFO [Thread-117] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0018
2015-10-19 18:03:22,610 INFO [Thread-117] org.apache.hadoop.ipc.Server: Stopping server on 64927
2015-10-19 18:03:22,610 INFO [IPC Server listener on 64927] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 64927
2015-10-19 18:03:22,610 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-19 18:03:22,610 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
