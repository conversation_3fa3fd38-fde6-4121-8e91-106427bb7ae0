2015-10-17 18:15:23,910 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445076437777_0005_000001
2015-10-17 18:15:25,738 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-17 18:15:25,738 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 5 cluster_timestamp: 1445076437777 } attemptId: 1 } keyId: 291674728)
2015-10-17 18:15:26,457 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-17 18:15:29,722 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-17 18:15:29,894 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-17 18:15:30,003 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-17 18:15:30,019 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-17 18:15:30,019 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-17 18:15:30,019 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-17 18:15:30,019 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-17 18:15:30,035 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-17 18:15:30,035 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-17 18:15:30,082 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-17 18:15:30,269 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:15:30,332 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:15:30,410 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-17 18:15:30,457 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-17 18:15:30,613 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-17 18:15:31,394 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 18:15:31,675 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 18:15:31,675 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-17 18:15:31,722 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445076437777_0005 to jobTokenSecretManager
2015-10-17 18:15:53,113 WARN [main] org.apache.hadoop.hdfs.BlockReaderFactory: I/O error constructing remote block reader.
java.net.ConnectException: Connection timed out: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:735)
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:206)
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:530)
	at org.apache.hadoop.hdfs.DFSClient.newConnectedPeer(DFSClient.java:3101)
	at org.apache.hadoop.hdfs.BlockReaderFactory.nextTcpPeer(BlockReaderFactory.java:755)
	at org.apache.hadoop.hdfs.BlockReaderFactory.getRemoteBlockReaderFromTcp(BlockReaderFactory.java:670)
	at org.apache.hadoop.hdfs.BlockReaderFactory.build(BlockReaderFactory.java:337)
	at org.apache.hadoop.hdfs.DFSInputStream.blockSeekTo(DFSInputStream.java:576)
	at org.apache.hadoop.hdfs.DFSInputStream.readWithStrategy(DFSInputStream.java:800)
	at org.apache.hadoop.hdfs.DFSInputStream.read(DFSInputStream.java:847)
	at java.io.DataInputStream.readFully(DataInputStream.java:195)
	at java.io.DataInputStream.readFully(DataInputStream.java:169)
	at org.apache.hadoop.mapreduce.split.SplitMetaInfoReader.readSplitMetaInfo(SplitMetaInfoReader.java:58)
	at org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl$InitTransition.createSplits(JobImpl.java:1563)
	at org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl$InitTransition.transition(JobImpl.java:1432)
	at org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl$InitTransition.transition(JobImpl.java:1390)
	at org.apache.hadoop.yarn.state.StateMachineFactory$MultipleInternalArc.doTransition(StateMachineFactory.java:385)
	at org.apache.hadoop.yarn.state.StateMachineFactory.doTransition(StateMachineFactory.java:302)
	at org.apache.hadoop.yarn.state.StateMachineFactory.access$300(StateMachineFactory.java:46)
	at org.apache.hadoop.yarn.state.StateMachineFactory$InternalStateMachine.doTransition(StateMachineFactory.java:448)
	at org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl.handle(JobImpl.java:996)
	at org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl.handle(JobImpl.java:138)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher.handle(MRAppMaster.java:1289)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.serviceStart(MRAppMaster.java:1057)
	at org.apache.hadoop.service.AbstractService.start(AbstractService.java:193)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster$4.run(MRAppMaster.java:1500)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.initAndStartAppMaster(MRAppMaster.java:1496)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.main(MRAppMaster.java:1429)
2015-10-17 18:15:53,113 WARN [main] org.apache.hadoop.hdfs.DFSClient: Failed to connect to /10.86.169.121:50010 for block, add to deadNodes and continue. java.net.ConnectException: Connection timed out: no further information
java.net.ConnectException: Connection timed out: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:735)
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:206)
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:530)
	at org.apache.hadoop.hdfs.DFSClient.newConnectedPeer(DFSClient.java:3101)
	at org.apache.hadoop.hdfs.BlockReaderFactory.nextTcpPeer(BlockReaderFactory.java:755)
	at org.apache.hadoop.hdfs.BlockReaderFactory.getRemoteBlockReaderFromTcp(BlockReaderFactory.java:670)
	at org.apache.hadoop.hdfs.BlockReaderFactory.build(BlockReaderFactory.java:337)
	at org.apache.hadoop.hdfs.DFSInputStream.blockSeekTo(DFSInputStream.java:576)
	at org.apache.hadoop.hdfs.DFSInputStream.readWithStrategy(DFSInputStream.java:800)
	at org.apache.hadoop.hdfs.DFSInputStream.read(DFSInputStream.java:847)
	at java.io.DataInputStream.readFully(DataInputStream.java:195)
	at java.io.DataInputStream.readFully(DataInputStream.java:169)
	at org.apache.hadoop.mapreduce.split.SplitMetaInfoReader.readSplitMetaInfo(SplitMetaInfoReader.java:58)
	at org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl$InitTransition.createSplits(JobImpl.java:1563)
	at org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl$InitTransition.transition(JobImpl.java:1432)
	at org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl$InitTransition.transition(JobImpl.java:1390)
	at org.apache.hadoop.yarn.state.StateMachineFactory$MultipleInternalArc.doTransition(StateMachineFactory.java:385)
	at org.apache.hadoop.yarn.state.StateMachineFactory.doTransition(StateMachineFactory.java:302)
	at org.apache.hadoop.yarn.state.StateMachineFactory.access$300(StateMachineFactory.java:46)
	at org.apache.hadoop.yarn.state.StateMachineFactory$InternalStateMachine.doTransition(StateMachineFactory.java:448)
	at org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl.handle(JobImpl.java:996)
	at org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl.handle(JobImpl.java:138)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher.handle(MRAppMaster.java:1289)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.serviceStart(MRAppMaster.java:1057)
	at org.apache.hadoop.service.AbstractService.start(AbstractService.java:193)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster$4.run(MRAppMaster.java:1500)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.initAndStartAppMaster(MRAppMaster.java:1496)
	at org.apache.hadoop.mapreduce.v2.app.MRAppMaster.main(MRAppMaster.java:1429)
2015-10-17 18:15:53,848 INFO [main] org.apache.hadoop.hdfs.DFSClient: Successfully connected to /**************:50010 for BP-1347369012-**************-1444972147527:blk_1073742635_1830
2015-10-17 18:15:53,879 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445076437777_0005 because: not enabled; too many maps; too much input;
2015-10-17 18:15:53,942 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445076437777_0005 = 1256521728. Number of splits = 10
2015-10-17 18:15:53,942 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445076437777_0005 = 1
2015-10-17 18:15:53,942 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0005Job Transitioned from NEW to INITED
2015-10-17 18:15:53,942 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445076437777_0005.
2015-10-17 18:15:54,051 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 18:15:54,129 INFO [Socket Reader #1 for port 53642] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 53642
2015-10-17 18:15:54,160 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-17 18:15:54,160 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at 04DN8IQ.fareast.corp.microsoft.com/***********:53642
2015-10-17 18:15:54,238 INFO [IPC Server listener on 53642] org.apache.hadoop.ipc.Server: IPC Server listener on 53642: starting
2015-10-17 18:15:54,238 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 18:15:54,488 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-17 18:15:54,504 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-17 18:15:54,535 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-17 18:15:54,551 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-17 18:15:54,551 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-17 18:15:54,551 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-17 18:15:54,567 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-17 18:15:54,629 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 53649
2015-10-17 18:15:54,629 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-17 18:15:54,785 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_53649_mapreduce____wazuk\webapp
2015-10-17 18:15:55,238 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:53649
2015-10-17 18:15:55,238 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 53649
2015-10-17 18:15:56,207 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-17 18:15:56,223 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-17 18:15:56,348 INFO [Socket Reader #1 for port 53652] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 53652
2015-10-17 18:15:56,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445076437777_0005
2015-10-17 18:15:56,395 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-17 18:15:56,395 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-17 18:15:56,395 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-17 18:15:56,488 INFO [IPC Server listener on 53652] org.apache.hadoop.ipc.Server: IPC Server listener on 53652: starting
2015-10-17 18:15:56,629 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-17 18:15:56,676 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-17 18:15:57,051 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-17 18:15:57,051 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-17 18:15:57,098 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-17 18:15:57,098 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-17 18:15:57,160 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0005Job Transitioned from INITED to SETUP
2015-10-17 18:15:57,223 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-17 18:15:57,348 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0005Job Transitioned from SETUP to RUNNING
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:15:57,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:15:57,442 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:15:57,442 INFO [Thread-51] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-17 18:15:57,504 INFO [Thread-51] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-17 18:15:57,535 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445076437777_0005, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0005/job_1445076437777_0005_1.jhist
2015-10-17 18:15:58,207 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-17 18:15:58,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-26> knownNMs=5
2015-10-17 18:15:58,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:9216, vCores:-26>
2015-10-17 18:15:58,332 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:15:59,379 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 7
2015-10-17 18:15:59,379 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0005_01_000002 to attempt_1445076437777_0005_m_000000_0
2015-10-17 18:15:59,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0005_01_000003 to attempt_1445076437777_0005_m_000001_0
2015-10-17 18:15:59,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0005_01_000004 to attempt_1445076437777_0005_m_000002_0
2015-10-17 18:15:59,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0005_01_000005 to attempt_1445076437777_0005_m_000003_0
2015-10-17 18:15:59,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0005_01_000006 to attempt_1445076437777_0005_m_000004_0
2015-10-17 18:15:59,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0005_01_000007 to attempt_1445076437777_0005_m_000005_0
2015-10-17 18:15:59,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0005_01_000008 to attempt_1445076437777_0005_m_000006_0
2015-10-17 18:15:59,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-33>
2015-10-17 18:15:59,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:15:59,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:7 RackLocal:0
2015-10-17 18:15:59,504 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:59,535 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0005/job.jar
2015-10-17 18:15:59,551 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0005/job.xml
2015-10-17 18:15:59,551 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-17 18:15:59,551 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-17 18:15:59,551 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-17 18:15:59,645 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:15:59,645 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:59,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:15:59,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:59,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:15:59,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:59,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:15:59,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:59,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:15:59,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:59,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:15:59,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:15:59,660 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:15:59,660 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0005_01_000002 taskAttempt attempt_1445076437777_0005_m_000000_0
2015-10-17 18:15:59,676 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0005_01_000007 taskAttempt attempt_1445076437777_0005_m_000005_0
2015-10-17 18:15:59,676 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0005_01_000008 taskAttempt attempt_1445076437777_0005_m_000006_0
2015-10-17 18:15:59,676 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0005_01_000006 taskAttempt attempt_1445076437777_0005_m_000004_0
2015-10-17 18:15:59,676 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0005_01_000005 taskAttempt attempt_1445076437777_0005_m_000003_0
2015-10-17 18:15:59,676 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0005_01_000004 taskAttempt attempt_1445076437777_0005_m_000002_0
2015-10-17 18:15:59,676 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0005_01_000003 taskAttempt attempt_1445076437777_0005_m_000001_0
2015-10-17 18:15:59,676 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0005_m_000001_0
2015-10-17 18:15:59,676 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0005_m_000006_0
2015-10-17 18:15:59,676 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0005_m_000000_0
2015-10-17 18:15:59,676 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0005_m_000005_0
2015-10-17 18:15:59,676 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0005_m_000004_0
2015-10-17 18:15:59,676 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0005_m_000002_0
2015-10-17 18:15:59,676 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0005_m_000003_0
2015-10-17 18:15:59,676 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:15:59,707 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:15:59,723 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:15:59,723 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:15:59,723 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:15:59,723 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:15:59,723 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:15:59,832 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0005_m_000005_0 : 13562
2015-10-17 18:15:59,832 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0005_m_000006_0 : 13562
2015-10-17 18:15:59,832 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0005_m_000004_0 : 13562
2015-10-17 18:15:59,832 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0005_m_000002_0 : 13562
2015-10-17 18:15:59,832 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0005_m_000006_0] using containerId: [container_1445076437777_0005_01_000008 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:15:59,832 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0005_m_000003_0 : 13562
2015-10-17 18:15:59,832 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:15:59,832 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0005_m_000004_0] using containerId: [container_1445076437777_0005_01_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:15:59,848 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0005_m_000000_0 : 13562
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0005_m_000005_0] using containerId: [container_1445076437777_0005_01_000007 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:15:59,848 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0005_m_000001_0 : 13562
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0005_m_000002_0] using containerId: [container_1445076437777_0005_01_000004 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0005_m_000003_0] using containerId: [container_1445076437777_0005_01_000005 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0005_m_000006
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0005_m_000004
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0005_m_000000_0] using containerId: [container_1445076437777_0005_01_000002 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0005_m_000001_0] using containerId: [container_1445076437777_0005_01_000003 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:28158]
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0005_m_000005
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0005_m_000002
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0005_m_000003
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0005_m_000000
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0005_m_000001
2015-10-17 18:15:59,848 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:16:00,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0005: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:16:00,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 18:16:00,395 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:16:00,395 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0005_01_000009 to attempt_1445076437777_0005_m_000007_0
2015-10-17 18:16:00,395 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:16:00,410 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:16:00,410 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:16:00,410 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0005_01_000010 to attempt_1445076437777_0005_m_000008_0
2015-10-17 18:16:00,410 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:16:00,410 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:16:00,410 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:7 RackLocal:2
2015-10-17 18:16:00,410 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:16:00,410 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:16:00,410 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0005_01_000009 taskAttempt attempt_1445076437777_0005_m_000007_0
2015-10-17 18:16:00,410 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0005_m_000007_0
2015-10-17 18:16:00,410 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:16:00,426 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0005_01_000010 taskAttempt attempt_1445076437777_0005_m_000008_0
2015-10-17 18:16:00,426 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0005_m_000008_0
2015-10-17 18:16:00,426 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:16:00,473 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0005_m_000007_0 : 13562
2015-10-17 18:16:00,473 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0005_m_000007_0] using containerId: [container_1445076437777_0005_01_000009 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 18:16:00,473 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:16:00,473 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0005_m_000007
2015-10-17 18:16:00,473 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:16:00,473 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0005_m_000008_0 : 13562
2015-10-17 18:16:00,473 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0005_m_000008_0] using containerId: [container_1445076437777_0005_01_000010 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:53425]
2015-10-17 18:16:00,473 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:16:00,473 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0005_m_000008
2015-10-17 18:16:00,473 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:16:01,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0005: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:0, vCores:-35> knownNMs=5
2015-10-17 18:16:01,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-17 18:16:01,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0005_01_000012 to attempt_1445076437777_0005_m_000009_0
2015-10-17 18:16:01,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Releasing unassigned and invalid container Container: [ContainerId: container_1445076437777_0005_01_000011, NodeId: MININT-75DGDAM1.fareast.corp.microsoft.com:53425, NodeHttpAddress: MININT-75DGDAM1.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 20, Token: Token { kind: ContainerToken, service: ************:53425 }, ]. RM may have assignment issues
2015-10-17 18:16:01,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:16:01,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-35>
2015-10-17 18:16:01,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:16:01,426 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:8 RackLocal:2
2015-10-17 18:16:01,426 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:16:01,520 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0005_01_000012 taskAttempt attempt_1445076437777_0005_m_000009_0
2015-10-17 18:16:01,520 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0005_m_000009_0
2015-10-17 18:16:01,551 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:16:01,645 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0005_m_000009_0 : 13562
2015-10-17 18:16:01,645 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0005_m_000009_0] using containerId: [container_1445076437777_0005_01_000012 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:16:01,645 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:16:01,645 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0005_m_000009
2015-10-17 18:16:01,645 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:16:02,457 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0005: ask=4 release= 1 newContainers=0 finishedContainers=1 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 18:16:02,457 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0005_01_000011
2015-10-17 18:16:02,457 ERROR [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Container complete event for unknown container id container_1445076437777_0005_01_000011
2015-10-17 18:16:02,457 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-34>
2015-10-17 18:16:02,457 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:16:03,098 INFO [Socket Reader #1 for port 53652] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0005 (auth:SIMPLE)
2015-10-17 18:16:03,129 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0005_m_000007 asked for a task
2015-10-17 18:16:03,129 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0005_m_000007 given task: attempt_1445076437777_0005_m_000005_0
2015-10-17 18:16:03,848 INFO [Socket Reader #1 for port 53652] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0005 (auth:SIMPLE)
2015-10-17 18:16:03,879 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0005_m_000008 asked for a task
2015-10-17 18:16:03,879 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0005_m_000008 given task: attempt_1445076437777_0005_m_000006_0
2015-10-17 18:16:03,879 INFO [Socket Reader #1 for port 53652] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0005 (auth:SIMPLE)
2015-10-17 18:16:03,911 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0005_m_000006 asked for a task
2015-10-17 18:16:03,911 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0005_m_000006 given task: attempt_1445076437777_0005_m_000004_0
2015-10-17 18:16:04,254 INFO [Socket Reader #1 for port 53652] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0005 (auth:SIMPLE)
2015-10-17 18:16:04,270 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0005_m_000010 asked for a task
2015-10-17 18:16:04,270 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0005_m_000010 given task: attempt_1445076437777_0005_m_000008_0
2015-10-17 18:16:04,426 INFO [Socket Reader #1 for port 53652] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0005 (auth:SIMPLE)
2015-10-17 18:16:04,442 INFO [Socket Reader #1 for port 53652] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0005 (auth:SIMPLE)
2015-10-17 18:16:04,442 INFO [Socket Reader #1 for port 53652] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0005 (auth:SIMPLE)
2015-10-17 18:16:04,457 INFO [Socket Reader #1 for port 53652] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0005 (auth:SIMPLE)
2015-10-17 18:16:04,457 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0005_m_000003 asked for a task
2015-10-17 18:16:04,457 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0005_m_000003 given task: attempt_1445076437777_0005_m_000001_0
2015-10-17 18:16:04,457 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0005_m_000005 asked for a task
2015-10-17 18:16:04,457 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0005_m_000005 given task: attempt_1445076437777_0005_m_000003_0
2015-10-17 18:16:04,473 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0005_m_000002 asked for a task
2015-10-17 18:16:04,473 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0005_m_000002 given task: attempt_1445076437777_0005_m_000000_0
2015-10-17 18:16:04,489 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0005_m_000004 asked for a task
2015-10-17 18:16:04,489 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0005_m_000004 given task: attempt_1445076437777_0005_m_000002_0
2015-10-17 18:16:04,801 INFO [Socket Reader #1 for port 53652] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0005 (auth:SIMPLE)
2015-10-17 18:16:04,848 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0005_m_000009 asked for a task
2015-10-17 18:16:04,848 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0005_m_000009 given task: attempt_1445076437777_0005_m_000007_0
2015-10-17 18:16:05,379 INFO [Socket Reader #1 for port 53652] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0005 (auth:SIMPLE)
2015-10-17 18:16:05,411 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0005_m_000012 asked for a task
2015-10-17 18:16:05,411 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0005_m_000012 given task: attempt_1445076437777_0005_m_000009_0
2015-10-17 18:16:05,551 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-32>
2015-10-17 18:16:05,551 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:16:10,629 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.10685723
2015-10-17 18:16:11,848 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.10680563
2015-10-17 18:16:11,848 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.106964506
2015-10-17 18:16:12,239 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.03647206
2015-10-17 18:16:12,348 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.10660437
2015-10-17 18:16:12,395 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.106493875
2015-10-17 18:16:12,426 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.10635664
2015-10-17 18:16:12,426 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.1066108
2015-10-17 18:16:12,958 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.295472
2015-10-17 18:16:13,176 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.014979548
2015-10-17 18:16:13,723 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.10685723
2015-10-17 18:16:14,958 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.106964506
2015-10-17 18:16:14,958 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.10680563
2015-10-17 18:16:15,411 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.056759655
2015-10-17 18:16:15,473 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.10660437
2015-10-17 18:16:15,567 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.106493875
2015-10-17 18:16:15,567 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.10635664
2015-10-17 18:16:15,583 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.1066108
2015-10-17 18:16:16,098 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.295472
2015-10-17 18:16:16,192 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.02409637
2015-10-17 18:16:16,848 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.10685723
2015-10-17 18:16:18,020 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.10680563
2015-10-17 18:16:18,020 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.106964506
2015-10-17 18:16:18,583 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.10660437
2015-10-17 18:16:18,614 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.08695462
2015-10-17 18:16:18,645 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.10635664
2015-10-17 18:16:18,661 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.106493875
2015-10-17 18:16:18,661 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.1066108
2015-10-17 18:16:19,161 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.295472
2015-10-17 18:16:19,239 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.036147524
2015-10-17 18:16:19,942 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.10685723
2015-10-17 18:16:21,192 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.13554075
2015-10-17 18:16:21,192 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.1305722
2015-10-17 18:16:21,645 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.19212553
2015-10-17 18:16:21,692 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.106881365
2015-10-17 18:16:21,723 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.19158794
2015-10-17 18:16:21,739 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.18158953
2015-10-17 18:16:21,739 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.15224797
2015-10-17 18:16:22,192 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.295472
2015-10-17 18:16:22,317 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.05861882
2015-10-17 18:16:22,989 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.16618674
2015-10-17 18:16:24,255 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.19208793
2015-10-17 18:16:24,255 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.19266446
2015-10-17 18:16:24,489 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-17 18:16:24,489 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-17 18:16:24,692 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.19212553
2015-10-17 18:16:24,755 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.19158794
2015-10-17 18:16:24,755 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.19209063
2015-10-17 18:16:24,770 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.19211523
2015-10-17 18:16:24,802 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.106881365
2015-10-17 18:16:25,224 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.5160533
2015-10-17 18:16:25,380 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.10681946
2015-10-17 18:16:26,005 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.19247705
2015-10-17 18:16:27,270 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.19242907
2015-10-17 18:16:27,270 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.19266446
2015-10-17 18:16:27,708 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.19212553
2015-10-17 18:16:27,770 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.19158794
2015-10-17 18:16:27,786 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.19209063
2015-10-17 18:16:27,786 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.19211523
2015-10-17 18:16:27,833 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.106881365
2015-10-17 18:16:28,224 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.5323719
2015-10-17 18:16:28,427 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.10681946
2015-10-17 18:16:29,021 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.19247705
2015-10-17 18:16:30,286 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.19266446
2015-10-17 18:16:30,286 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.19242907
2015-10-17 18:16:30,755 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.20623566
2015-10-17 18:16:30,817 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.24081527
2015-10-17 18:16:30,817 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.19209063
2015-10-17 18:16:30,833 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.19211523
2015-10-17 18:16:30,880 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.15258484
2015-10-17 18:16:31,255 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.5323719
2015-10-17 18:16:31,458 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.10681946
2015-10-17 18:16:32,067 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.19247705
2015-10-17 18:16:33,302 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.22682032
2015-10-17 18:16:33,317 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.21231171
2015-10-17 18:16:33,786 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.27772525
2015-10-17 18:16:33,833 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.27696857
2015-10-17 18:16:33,833 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.27501467
2015-10-17 18:16:33,849 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.25211897
2015-10-17 18:16:33,927 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.19258286
2015-10-17 18:16:34,271 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.5323719
2015-10-17 18:16:34,521 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.10681946
2015-10-17 18:16:35,068 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.2360922
2015-10-17 18:16:36,333 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.5323719
2015-10-17 18:16:36,349 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.2783809
2015-10-17 18:16:36,349 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.2781602
2015-10-17 18:16:36,833 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.27772525
2015-10-17 18:16:36,864 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.27696857
2015-10-17 18:16:36,927 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.27765483
2015-10-17 18:16:36,927 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.27776006
2015-10-17 18:16:36,989 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.19258286
2015-10-17 18:16:37,302 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.667
2015-10-17 18:16:37,568 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.10681946
2015-10-17 18:16:38,333 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.27813601
2015-10-17 18:16:39,552 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.2783809
2015-10-17 18:16:39,568 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.2781602
2015-10-17 18:16:39,880 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.27772525
2015-10-17 18:16:40,161 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.27696857
2015-10-17 18:16:40,161 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.27765483
2015-10-17 18:16:40,161 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.19258286
2015-10-17 18:16:40,161 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.27776006
2015-10-17 18:16:40,380 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.667
2015-10-17 18:16:40,755 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.10964788
2015-10-17 18:16:41,396 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.27813601
2015-10-17 18:16:42,911 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.31597838
2015-10-17 18:16:43,021 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.2783809
2015-10-17 18:16:43,021 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.2781602
2015-10-17 18:16:43,177 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.3624012
2015-10-17 18:16:43,177 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.3205538
2015-10-17 18:16:43,177 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.28339744
2015-10-17 18:16:43,208 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.2357924
2015-10-17 18:16:43,411 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.667
2015-10-17 18:16:43,786 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.1520925
2015-10-17 18:16:44,443 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.27813601
2015-10-17 18:16:45,974 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.36317363
2015-10-17 18:16:46,083 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.33493078
2015-10-17 18:16:46,083 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.31526673
2015-10-17 18:16:46,255 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.3624012
2015-10-17 18:16:46,255 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.36323506
2015-10-17 18:16:46,255 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.36253
2015-10-17 18:16:46,271 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.26227647
2015-10-17 18:16:46,474 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.7346076
2015-10-17 18:16:46,833 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.1739653
2015-10-17 18:16:47,490 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.3281354
2015-10-17 18:16:49,021 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.36317363
2015-10-17 18:16:49,130 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.36388028
2015-10-17 18:16:49,130 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.36404583
2015-10-17 18:16:49,287 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.36319977
2015-10-17 18:16:49,287 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.3624012
2015-10-17 18:16:49,287 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.36323506
2015-10-17 18:16:49,333 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.27811313
2015-10-17 18:16:49,490 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.82104445
2015-10-17 18:16:49,865 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.19255035
2015-10-17 18:16:50,521 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.36390656
2015-10-17 18:16:52,037 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.36317363
2015-10-17 18:16:52,130 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.36404583
2015-10-17 18:16:52,146 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.36388028
2015-10-17 18:16:52,302 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.36323506
2015-10-17 18:16:52,302 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.42083135
2015-10-17 18:16:52,318 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.36319977
2015-10-17 18:16:52,505 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 0.9378062
2015-10-17 18:16:52,896 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.19255035
2015-10-17 18:16:53,459 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.27811313
2015-10-17 18:16:53,537 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.36390656
2015-10-17 18:16:54,990 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000009_0 is : 1.0
2015-10-17 18:16:55,912 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.19255035
2015-10-17 18:16:55,927 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0005_m_000009_0
2015-10-17 18:16:55,927 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:16:55,927 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0005_01_000012 taskAttempt attempt_1445076437777_0005_m_000009_0
2015-10-17 18:16:55,927 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0005_m_000009_0
2015-10-17 18:16:55,974 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:16:55,974 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.42633218
2015-10-17 18:16:56,068 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:16:56,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0005_m_000009_0
2015-10-17 18:16:56,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:16:56,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-17 18:16:56,146 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.36404583
2015-10-17 18:16:56,146 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.36388028
2015-10-17 18:16:56,287 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.39291146
2015-10-17 18:16:56,302 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.4325814
2015-10-17 18:16:56,302 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.44789755
2015-10-17 18:16:56,506 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0005_m_000007
2015-10-17 18:16:56,506 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:16:56,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0005_m_000007
2015-10-17 18:16:56,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:16:56,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:16:56,506 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000007_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:16:56,506 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.29407635
2015-10-17 18:16:56,568 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.36390656
2015-10-17 18:16:57,021 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:8 RackLocal:2
2015-10-17 18:16:57,052 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:4096, vCores:-31> knownNMs=5
2015-10-17 18:16:57,052 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:4096, vCores:-31>
2015-10-17 18:16:57,052 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-17 18:16:57,052 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:14336, vCores:-21> finalMapResourceLimit:<memory:11264, vCores:11> finalReduceResourceLimit:<memory:3072, vCores:-32> netScheduledMapResource:<memory:11264, vCores:11> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-17 18:16:57,052 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Ramping up 1
2015-10-17 18:16:57,052 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:8 RackLocal:2
2015-10-17 18:16:58,084 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0005: ask=1 release= 0 newContainers=1 finishedContainers=1 resourcelimit=<memory:4096, vCores:-31> knownNMs=5
2015-10-17 18:16:58,084 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0005_01_000012
2015-10-17 18:16:58,084 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:16:58,084 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:16:58,084 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0005_01_000013 to attempt_1445076437777_0005_m_000007_1
2015-10-17 18:16:58,084 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:1 HostLocal:8 RackLocal:3
2015-10-17 18:16:58,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0005_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:16:58,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:16:58,084 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000007_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:16:58,084 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0005_01_000013 taskAttempt attempt_1445076437777_0005_m_000007_1
2015-10-17 18:16:58,084 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0005_m_000007_1
2015-10-17 18:16:58,084 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:16:58,115 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0005_m_000007_1 : 13562
2015-10-17 18:16:58,115 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0005_m_000007_1] using containerId: [container_1445076437777_0005_01_000013 on NM: [04DN8IQ.fareast.corp.microsoft.com:52150]
2015-10-17 18:16:58,115 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000007_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:16:58,115 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0005_m_000007
2015-10-17 18:16:59,006 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.19255035
2015-10-17 18:16:59,037 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.44859612
2015-10-17 18:16:59,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0005: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:3072, vCores:-32> knownNMs=5
2015-10-17 18:16:59,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:16:59,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-17 18:16:59,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0005_01_000014 to attempt_1445076437777_0005_r_000000_0
2015-10-17 18:16:59,115 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:1 HostLocal:8 RackLocal:3
2015-10-17 18:16:59,131 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:16:59,131 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:16:59,131 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0005_01_000014 taskAttempt attempt_1445076437777_0005_r_000000_0
2015-10-17 18:16:59,131 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0005_r_000000_0
2015-10-17 18:16:59,131 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:16:59,193 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.406693
2015-10-17 18:16:59,193 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.43487942
2015-10-17 18:16:59,318 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.44789755
2015-10-17 18:16:59,381 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0005_r_000000_0 : 13562
2015-10-17 18:16:59,381 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.4486067
2015-10-17 18:16:59,381 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.448704
2015-10-17 18:16:59,381 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0005_r_000000_0] using containerId: [container_1445076437777_0005_01_000014 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:53425]
2015-10-17 18:16:59,381 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:16:59,381 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0005_r_000000
2015-10-17 18:16:59,381 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-17 18:16:59,615 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.43302333
2015-10-17 18:16:59,615 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.34196666
2015-10-17 18:17:00,177 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0005: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-32> knownNMs=5
2015-10-17 18:17:01,662 INFO [Socket Reader #1 for port 53652] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0005 (auth:SIMPLE)
2015-10-17 18:17:01,678 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0005_m_000013 asked for a task
2015-10-17 18:17:01,678 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0005_m_000013 given task: attempt_1445076437777_0005_m_000007_1
2015-10-17 18:17:02,146 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.44859612
2015-10-17 18:17:02,224 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.44968578
2015-10-17 18:17:02,224 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.44980705
2015-10-17 18:17:02,412 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.44789755
2015-10-17 18:17:02,443 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.448704
2015-10-17 18:17:02,443 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.4486067
2015-10-17 18:17:02,553 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.2058287
2015-10-17 18:17:02,709 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.44950968
2015-10-17 18:17:02,709 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.3637686
2015-10-17 18:17:03,178 INFO [Socket Reader #1 for port 53652] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0005 (auth:SIMPLE)
2015-10-17 18:17:03,271 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0005_r_000014 asked for a task
2015-10-17 18:17:03,271 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0005_r_000014 given task: attempt_1445076437777_0005_r_000000_0
2015-10-17 18:17:05,178 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.44859612
2015-10-17 18:17:05,224 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.44968578
2015-10-17 18:17:05,240 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.44980705
2015-10-17 18:17:05,334 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 0 maxEvents 10000
2015-10-17 18:17:05,474 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.497955
2015-10-17 18:17:05,506 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.4486067
2015-10-17 18:17:05,506 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.448704
2015-10-17 18:17:05,599 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.21950707
2015-10-17 18:17:05,771 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.44950968
2015-10-17 18:17:05,959 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.3637686
2015-10-17 18:17:06,350 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:08,209 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.51754606
2015-10-17 18:17:08,256 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.44968578
2015-10-17 18:17:08,271 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.44980705
2015-10-17 18:17:08,521 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.53341997
2015-10-17 18:17:08,553 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.448704
2015-10-17 18:17:08,553 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.4486067
2015-10-17 18:17:08,631 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.25203678
2015-10-17 18:17:08,834 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.44950968
2015-10-17 18:17:08,959 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:09,037 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.3637686
2015-10-17 18:17:10,006 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:11,068 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:11,303 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.5342037
2015-10-17 18:17:11,318 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.0
2015-10-17 18:17:11,318 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.51520884
2015-10-17 18:17:11,334 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.53543663
2015-10-17 18:17:11,522 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0005_m_000008
2015-10-17 18:17:11,522 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0005_m_000008
2015-10-17 18:17:11,522 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:17:11,522 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:17:11,522 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:17:11,522 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000008_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:17:11,553 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.53341997
2015-10-17 18:17:11,584 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.5343203
2015-10-17 18:17:11,584 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.50503004
2015-10-17 18:17:11,662 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.27825075
2015-10-17 18:17:11,850 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.52210915
2015-10-17 18:17:12,084 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.3708789
2015-10-17 18:17:12,131 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:12,334 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:1 HostLocal:8 RackLocal:3
2015-10-17 18:17:12,350 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:3072, vCores:-32> knownNMs=5
2015-10-17 18:17:13,178 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:13,381 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:17:13,381 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0005_01_000015 to attempt_1445076437777_0005_m_000008_1
2015-10-17 18:17:13,381 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:9 RackLocal:3
2015-10-17 18:17:13,381 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:17:13,381 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000008_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:17:13,381 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0005_01_000015 taskAttempt attempt_1445076437777_0005_m_000008_1
2015-10-17 18:17:13,381 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0005_m_000008_1
2015-10-17 18:17:13,381 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:17:13,584 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0005_m_000008_1 : 13562
2015-10-17 18:17:13,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0005_m_000008_1] using containerId: [container_1445076437777_0005_01_000015 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:49130]
2015-10-17 18:17:13,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000008_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:17:13,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0005_m_000008
2015-10-17 18:17:14,225 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:14,365 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.5342037
2015-10-17 18:17:14,365 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.5352028
2015-10-17 18:17:14,365 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.53543663
2015-10-17 18:17:14,397 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.0
2015-10-17 18:17:14,397 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-33> knownNMs=5
2015-10-17 18:17:14,600 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.53341997
2015-10-17 18:17:14,615 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.53425497
2015-10-17 18:17:14,631 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.5343203
2015-10-17 18:17:14,709 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.27825075
2015-10-17 18:17:14,865 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.5352021
2015-10-17 18:17:15,178 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.41198808
2015-10-17 18:17:15,272 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:15,678 INFO [Socket Reader #1 for port 53652] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0005 (auth:SIMPLE)
2015-10-17 18:17:15,709 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0005_m_000015 asked for a task
2015-10-17 18:17:15,725 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0005_m_000015 given task: attempt_1445076437777_0005_m_000008_1
2015-10-17 18:17:16,350 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:17,397 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.5352028
2015-10-17 18:17:17,397 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.53543663
2015-10-17 18:17:17,397 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.5432584
2015-10-17 18:17:17,428 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:17,475 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.0
2015-10-17 18:17:17,662 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.61898744
2015-10-17 18:17:17,662 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.5343203
2015-10-17 18:17:17,662 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.53425497
2015-10-17 18:17:17,740 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.27825075
2015-10-17 18:17:17,897 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.5352021
2015-10-17 18:17:18,334 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.4341363
2015-10-17 18:17:18,537 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:18,912 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.0234458
2015-10-17 18:17:19,615 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:20,756 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.27825075
2015-10-17 18:17:20,991 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.53425497
2015-10-17 18:17:20,991 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.58797705
2015-10-17 18:17:20,991 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.61898744
2015-10-17 18:17:21,334 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.59341115
2015-10-17 18:17:21,334 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.5491705
2015-10-17 18:17:21,397 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.6196791
2015-10-17 18:17:21,412 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.5352021
2015-10-17 18:17:21,475 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.44950172
2015-10-17 18:17:21,553 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.0
2015-10-17 18:17:21,553 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:21,944 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.03679998
2015-10-17 18:17:22,616 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:22,616 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.106881365
2015-10-17 18:17:23,694 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:23,787 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.3002774
2015-10-17 18:17:24,069 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.61648554
2015-10-17 18:17:24,084 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.6199081
2015-10-17 18:17:24,084 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.61898744
2015-10-17 18:17:24,991 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.049827255
2015-10-17 18:17:25,319 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.59606814
2015-10-17 18:17:25,491 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.44950172
2015-10-17 18:17:25,537 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.0
2015-10-17 18:17:25,537 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:25,631 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.106881365
2015-10-17 18:17:26,459 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.61898744
2015-10-17 18:17:26,475 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.6210422
2015-10-17 18:17:26,475 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.6208445
2015-10-17 18:17:26,538 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0005_m_000005
2015-10-17 18:17:26,538 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:17:26,538 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445076437777_0005_m_000005
2015-10-17 18:17:26,538 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:17:26,538 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:17:26,538 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000005_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-17 18:17:26,553 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.6196791
2015-10-17 18:17:26,834 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.31819025
2015-10-17 18:17:27,116 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.6197233
2015-10-17 18:17:27,178 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.6199081
2015-10-17 18:17:27,178 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.667
2015-10-17 18:17:27,444 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:9 RackLocal:3
2015-10-17 18:17:27,506 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-33> knownNMs=5
2015-10-17 18:17:27,584 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:28,022 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.071322925
2015-10-17 18:17:28,381 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.6209487
2015-10-17 18:17:28,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-17 18:17:28,584 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:17:28,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445076437777_0005_01_000016 to attempt_1445076437777_0005_m_000005_1
2015-10-17 18:17:28,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:17:28,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-75DGDAM1.fareast.corp.microsoft.com to /default-rack
2015-10-17 18:17:28,584 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000005_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-17 18:17:28,584 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445076437777_0005_01_000016 taskAttempt attempt_1445076437777_0005_m_000005_1
2015-10-17 18:17:28,584 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445076437777_0005_m_000005_1
2015-10-17 18:17:28,584 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:17:28,694 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:28,709 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.106881365
2015-10-17 18:17:29,100 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445076437777_0005_m_000005_1 : 13562
2015-10-17 18:17:29,100 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445076437777_0005_m_000005_1] using containerId: [container_1445076437777_0005_01_000016 on NM: [MININT-75DGDAM1.fareast.corp.microsoft.com:53425]
2015-10-17 18:17:29,100 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000005_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-17 18:17:29,100 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445076437777_0005_m_000005
2015-10-17 18:17:29,209 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.6196791
2015-10-17 18:17:29,553 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.6208445
2015-10-17 18:17:29,553 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.6210422
2015-10-17 18:17:29,584 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.47093776
2015-10-17 18:17:29,600 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.0
2015-10-17 18:17:29,631 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.667
2015-10-17 18:17:29,663 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445076437777_0005: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:1024, vCores:-34> knownNMs=5
2015-10-17 18:17:29,803 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:29,866 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.33735365
2015-10-17 18:17:30,194 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.6197233
2015-10-17 18:17:30,272 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.667
2015-10-17 18:17:30,272 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.6199081
2015-10-17 18:17:31,053 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.09012569
2015-10-17 18:17:31,209 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:31,334 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.6199081
2015-10-17 18:17:31,413 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.6209487
2015-10-17 18:17:31,491 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.6210422
2015-10-17 18:17:31,741 INFO [Socket Reader #1 for port 53652] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445076437777_0005 (auth:SIMPLE)
2015-10-17 18:17:31,756 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.17902982
2015-10-17 18:17:31,834 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445076437777_0005_m_000016 asked for a task
2015-10-17 18:17:31,834 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445076437777_0005_m_000016 given task: attempt_1445076437777_0005_m_000005_1
2015-10-17 18:17:32,272 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:32,616 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.667
2015-10-17 18:17:32,616 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.63199234
2015-10-17 18:17:32,725 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.667
2015-10-17 18:17:32,835 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.49536356
2015-10-17 18:17:32,835 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.0
2015-10-17 18:17:32,913 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.35284323
2015-10-17 18:17:33,288 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.6385653
2015-10-17 18:17:33,350 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.667
2015-10-17 18:17:33,350 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.667
2015-10-17 18:17:33,397 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:34,085 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.10681946
2015-10-17 18:17:35,022 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.6209487
2015-10-17 18:17:35,022 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.6385653
2015-10-17 18:17:35,022 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.63199234
2015-10-17 18:17:35,022 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.667
2015-10-17 18:17:35,022 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:35,022 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.19258286
2015-10-17 18:17:35,663 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.667
2015-10-17 18:17:35,663 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.667
2015-10-17 18:17:35,772 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.667
2015-10-17 18:17:35,960 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.5050513
2015-10-17 18:17:35,960 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.0
2015-10-17 18:17:35,960 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.3638923
2015-10-17 18:17:36,053 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:36,335 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.667
2015-10-17 18:17:36,381 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.667
2015-10-17 18:17:36,397 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.6924244
2015-10-17 18:17:37,085 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:37,116 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.10681946
2015-10-17 18:17:38,085 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.667
2015-10-17 18:17:38,085 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.19258286
2015-10-17 18:17:38,116 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:38,678 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.667
2015-10-17 18:17:38,678 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.667
2015-10-17 18:17:39,007 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.3638923
2015-10-17 18:17:39,038 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.53521925
2015-10-17 18:17:39,053 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.0
2015-10-17 18:17:39,116 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.69160044
2015-10-17 18:17:39,147 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:39,366 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.667
2015-10-17 18:17:39,413 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.667
2015-10-17 18:17:39,428 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.72617596
2015-10-17 18:17:40,147 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.10681946
2015-10-17 18:17:40,241 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:41,100 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.667
2015-10-17 18:17:41,100 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.19258286
2015-10-17 18:17:41,257 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:41,694 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.7022332
2015-10-17 18:17:41,694 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.667
2015-10-17 18:17:42,038 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.3638923
2015-10-17 18:17:42,225 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.72930723
2015-10-17 18:17:42,257 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:42,272 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:17:42,272 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.53521925
2015-10-17 18:17:42,397 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.667
2015-10-17 18:17:42,444 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.667
2015-10-17 18:17:42,460 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.7611866
2015-10-17 18:17:43,179 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.10681946
2015-10-17 18:17:43,288 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:44,288 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:44,694 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.68607146
2015-10-17 18:17:44,694 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.24560033
2015-10-17 18:17:44,991 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.73598194
2015-10-17 18:17:44,991 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.67713016
2015-10-17 18:17:45,069 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.3676973
2015-10-17 18:17:45,913 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.763224
2015-10-17 18:17:45,913 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:45,944 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:17:45,944 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.6758723
2015-10-17 18:17:45,944 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.667
2015-10-17 18:17:45,944 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.53521925
2015-10-17 18:17:46,225 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.10747201
2015-10-17 18:17:46,225 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.79671025
2015-10-17 18:17:46,819 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_1 is : 0.047872417
2015-10-17 18:17:47,054 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:47,725 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.7176542
2015-10-17 18:17:47,725 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.27811313
2015-10-17 18:17:48,038 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.72914153
2015-10-17 18:17:48,038 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.7917625
2015-10-17 18:17:48,085 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.3805073
2015-10-17 18:17:48,100 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:48,991 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.8043194
2015-10-17 18:17:49,179 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:49,241 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.12701288
2015-10-17 18:17:49,319 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.6672342
2015-10-17 18:17:49,319 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.6951121
2015-10-17 18:17:49,366 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.5434857
2015-10-17 18:17:49,366 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:17:49,647 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.8434755
2015-10-17 18:17:50,257 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:50,257 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_1 is : 0.08663035
2015-10-17 18:17:51,069 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.7651263
2015-10-17 18:17:51,069 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.27811313
2015-10-17 18:17:51,069 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.74613816
2015-10-17 18:17:51,069 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.83026135
2015-10-17 18:17:51,132 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.39798364
2015-10-17 18:17:51,304 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:52,054 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.83907264
2015-10-17 18:17:52,272 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.15244453
2015-10-17 18:17:52,382 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.6858948
2015-10-17 18:17:52,382 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.7144839
2015-10-17 18:17:52,382 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:52,491 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.58297133
2015-10-17 18:17:52,507 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:17:52,726 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.8802828
2015-10-17 18:17:54,085 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.7779863
2015-10-17 18:17:54,085 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.80422676
2015-10-17 18:17:54,085 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.27811313
2015-10-17 18:17:54,116 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.87143683
2015-10-17 18:17:54,163 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.39895907
2015-10-17 18:17:54,241 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_1 is : 0.10389046
2015-10-17 18:17:54,335 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:55,101 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.874372
2015-10-17 18:17:55,304 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.18464008
2015-10-17 18:17:55,413 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:55,444 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.7379494
2015-10-17 18:17:55,444 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.70899016
2015-10-17 18:17:55,601 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.59881586
2015-10-17 18:17:55,616 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:17:55,788 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.9171319
2015-10-17 18:17:56,444 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:57,116 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.8421494
2015-10-17 18:17:57,116 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.31056005
2015-10-17 18:17:57,116 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.8054863
2015-10-17 18:17:57,132 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.91118544
2015-10-17 18:17:57,194 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.4258025
2015-10-17 18:17:57,304 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_1 is : 0.10685723
2015-10-17 18:17:58,054 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:17:58,148 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.90992874
2015-10-17 18:17:58,398 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.19255035
2015-10-17 18:17:59,132 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.95418996
2015-10-17 18:18:00,070 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.7360694
2015-10-17 18:18:00,070 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.76561344
2015-10-17 18:18:00,132 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.35970443
2015-10-17 18:18:00,132 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.8359034
2015-10-17 18:18:00,132 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.8798718
2015-10-17 18:18:00,163 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.95117927
2015-10-17 18:18:00,226 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.44097388
2015-10-17 18:18:01,085 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_1 is : 0.10685723
2015-10-17 18:18:01,163 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.9458809
2015-10-17 18:18:01,398 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:18:01,398 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:18:01,695 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.19255035
2015-10-17 18:18:02,163 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 0.9955957
2015-10-17 18:18:02,413 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:18:02,632 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000000_0 is : 1.0
2015-10-17 18:18:02,632 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0005_m_000000_0
2015-10-17 18:18:02,632 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:18:02,632 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0005_01_000002 taskAttempt attempt_1445076437777_0005_m_000000_0
2015-10-17 18:18:02,632 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0005_m_000000_0
2015-10-17 18:18:02,632 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:18:02,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:18:02,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0005_m_000000_0
2015-10-17 18:18:02,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:18:02,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-17 18:18:03,085 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:03,101 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.81683964
2015-10-17 18:18:03,101 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.7862926
2015-10-17 18:18:03,163 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.9230131
2015-10-17 18:18:03,179 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.8648657
2015-10-17 18:18:03,179 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.3637686
2015-10-17 18:18:03,179 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 0.9977958
2015-10-17 18:18:03,226 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.6207798
2015-10-17 18:18:03,257 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.44964966
2015-10-17 18:18:03,382 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000006_0 is : 1.0
2015-10-17 18:18:03,398 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0005_m_000006_0
2015-10-17 18:18:03,398 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:18:03,398 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0005_01_000008 taskAttempt attempt_1445076437777_0005_m_000006_0
2015-10-17 18:18:03,398 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0005_m_000006_0
2015-10-17 18:18:03,398 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:18:03,413 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 1 maxEvents 10000
2015-10-17 18:18:03,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:18:03,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0005_m_000006_0
2015-10-17 18:18:03,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:18:03,460 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-17 18:18:04,085 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:04,163 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0005_01_000002
2015-10-17 18:18:04,163 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:04,163 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0005_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:18:04,179 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_1 is : 0.14660366
2015-10-17 18:18:04,195 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 0.9821489
2015-10-17 18:18:04,445 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 2 maxEvents 10000
2015-10-17 18:18:04,460 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:18:04,726 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.19255035
2015-10-17 18:18:05,179 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0005_01_000008
2015-10-17 18:18:05,179 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:05,179 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0005_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:18:05,492 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 18:18:05,773 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000002_0 is : 1.0
2015-10-17 18:18:05,788 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0005_m_000002_0
2015-10-17 18:18:05,788 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:18:05,788 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0005_01_000004 taskAttempt attempt_1445076437777_0005_m_000002_0
2015-10-17 18:18:05,788 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0005_m_000002_0
2015-10-17 18:18:05,788 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:18:05,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:18:05,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0005_m_000002_0
2015-10-17 18:18:05,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:18:05,851 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-17 18:18:06,117 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.85370785
2015-10-17 18:18:06,132 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.8224724
2015-10-17 18:18:06,179 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:06,195 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 0.96914154
2015-10-17 18:18:06,195 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.3637686
2015-10-17 18:18:06,210 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.8961716
2015-10-17 18:18:06,288 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.44964966
2015-10-17 18:18:06,288 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.6207798
2015-10-17 18:18:06,554 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 3 maxEvents 10000
2015-10-17 18:18:07,195 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0005_01_000004
2015-10-17 18:18:07,195 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:07,195 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0005_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:18:07,242 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_1 is : 0.16316532
2015-10-17 18:18:07,538 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:18:07,601 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:18:07,757 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.19345202
2015-10-17 18:18:08,554 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000004_0 is : 1.0
2015-10-17 18:18:08,601 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0005_m_000004_0
2015-10-17 18:18:08,601 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:18:08,601 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0005_01_000006 taskAttempt attempt_1445076437777_0005_m_000004_0
2015-10-17 18:18:08,601 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0005_m_000004_0
2015-10-17 18:18:08,601 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:18:08,710 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:18:08,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:18:08,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0005_m_000004_0
2015-10-17 18:18:08,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:18:08,835 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-17 18:18:09,164 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.8727783
2015-10-17 18:18:09,179 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.84105146
2015-10-17 18:18:09,226 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.3637686
2015-10-17 18:18:09,226 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.927496
2015-10-17 18:18:09,257 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:09,320 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.44964966
2015-10-17 18:18:09,398 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.64224535
2015-10-17 18:18:09,835 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 4 maxEvents 10000
2015-10-17 18:18:10,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0005_01_000006
2015-10-17 18:18:10,289 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0005_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:18:10,289 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:10,367 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_1 is : 0.17879856
2015-10-17 18:18:10,664 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:18:10,789 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.21234436
2015-10-17 18:18:10,976 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:18:12,101 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:18:12,273 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.88837814
2015-10-17 18:18:12,273 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.85632366
2015-10-17 18:18:12,273 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.44950172
2015-10-17 18:18:12,273 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 0.9659052
2015-10-17 18:18:12,335 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.4631193
2015-10-17 18:18:12,507 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.65560013
2015-10-17 18:18:13,164 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:18:13,523 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_1 is : 0.19247705
2015-10-17 18:18:13,820 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.22243871
2015-10-17 18:18:13,820 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:18:14,242 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000005_0 is : 1.0
2015-10-17 18:18:14,273 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:18:14,289 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0005_m_000005_0
2015-10-17 18:18:14,289 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:18:14,289 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0005_01_000007 taskAttempt attempt_1445076437777_0005_m_000005_0
2015-10-17 18:18:14,289 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0005_m_000005_0
2015-10-17 18:18:14,289 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:18:14,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:18:14,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0005_m_000005_0
2015-10-17 18:18:14,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445076437777_0005_m_000005_1
2015-10-17 18:18:14,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:18:14,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-17 18:18:14,476 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000005_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 18:18:14,476 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0005_01_000016 taskAttempt attempt_1445076437777_0005_m_000005_1
2015-10-17 18:18:14,476 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0005_m_000005_1
2015-10-17 18:18:14,476 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:18:14,507 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:14,945 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000005_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 18:18:14,945 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 18:18:15,039 INFO [Socket Reader #1 for port 53652] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 53652: readAndProcess from client ************ threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 18:18:15,054 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445076437777_0005_m_000005
2015-10-17 18:18:15,054 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-17 18:18:15,054 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out2/_temporary/1/_temporary/attempt_1445076437777_0005_m_000005_1
2015-10-17 18:18:15,054 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000005_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 18:18:15,336 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.44950172
2015-10-17 18:18:15,336 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.65560013
2015-10-17 18:18:15,351 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.8811966
2015-10-17 18:18:15,351 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.91387296
2015-10-17 18:18:15,351 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 5 maxEvents 10000
2015-10-17 18:18:15,398 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.48298627
2015-10-17 18:18:15,539 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0005_01_000007
2015-10-17 18:18:15,539 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:15,539 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0005_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:18:15,648 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.667
2015-10-17 18:18:16,492 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:18:16,586 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0005_01_000016
2015-10-17 18:18:16,586 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:16,586 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0005_m_000005_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:18:16,851 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.24328214
2015-10-17 18:18:16,945 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:18:17,586 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:18:18,398 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.44950172
2015-10-17 18:18:18,429 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.90795654
2015-10-17 18:18:18,429 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.9411961
2015-10-17 18:18:18,429 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.501023
2015-10-17 18:18:18,664 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:18:19,648 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.667
2015-10-17 18:18:19,679 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:18:19,898 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.2724362
2015-10-17 18:18:20,054 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:18:20,742 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:18:21,461 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.51750934
2015-10-17 18:18:22,086 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:18:22,320 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.52078396
2015-10-17 18:18:22,367 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 0.97605336
2015-10-17 18:18:22,367 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.9422952
2015-10-17 18:18:22,695 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.667
2015-10-17 18:18:22,945 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.27825075
2015-10-17 18:18:23,117 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:18:23,117 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:18:24,070 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000003_0 is : 1.0
2015-10-17 18:18:24,086 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0005_m_000003_0
2015-10-17 18:18:24,086 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:18:24,086 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0005_01_000005 taskAttempt attempt_1445076437777_0005_m_000003_0
2015-10-17 18:18:24,086 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0005_m_000003_0
2015-10-17 18:18:24,086 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:18:24,117 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:18:24,117 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0005_m_000003_0
2015-10-17 18:18:24,117 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:18:24,117 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-17 18:18:24,133 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 6 maxEvents 10000
2015-10-17 18:18:24,492 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.5352825
2015-10-17 18:18:24,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:25,133 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 18:18:25,367 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.53521925
2015-10-17 18:18:25,414 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.9804147
2015-10-17 18:18:25,648 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0005_01_000005
2015-10-17 18:18:25,664 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:25,664 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0005_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:18:25,742 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.69112426
2015-10-17 18:18:25,976 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.27825075
2015-10-17 18:18:26,148 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 18:18:26,164 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:18:27,180 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 18:18:27,523 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.5352825
2015-10-17 18:18:28,211 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 18:18:28,398 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.53521925
2015-10-17 18:18:28,476 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 0.9993377
2015-10-17 18:18:28,695 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000001_0 is : 1.0
2015-10-17 18:18:28,711 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0005_m_000001_0
2015-10-17 18:18:28,711 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000001_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:18:28,711 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0005_01_000003 taskAttempt attempt_1445076437777_0005_m_000001_0
2015-10-17 18:18:28,711 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0005_m_000001_0
2015-10-17 18:18:28,711 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:28158
2015-10-17 18:18:28,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000001_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:18:28,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0005_m_000001_0
2015-10-17 18:18:28,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:18:28,758 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-17 18:18:28,820 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.7271987
2015-10-17 18:18:29,008 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.27825075
2015-10-17 18:18:29,226 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.033333335
2015-10-17 18:18:29,258 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 7 maxEvents 10000
2015-10-17 18:18:29,680 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:30,305 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:30,555 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.5352825
2015-10-17 18:18:30,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0005_01_000003
2015-10-17 18:18:30,695 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:30,695 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0005_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:18:31,336 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:31,430 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.60665977
2015-10-17 18:18:31,914 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.76429737
2015-10-17 18:18:32,055 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.27943486
2015-10-17 18:18:32,320 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.06666667
2015-10-17 18:18:32,383 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:33,445 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:33,586 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.5352825
2015-10-17 18:18:34,477 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.6207798
2015-10-17 18:18:34,523 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:35,023 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.8028829
2015-10-17 18:18:35,086 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.3044288
2015-10-17 18:18:36,320 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.06666667
2015-10-17 18:18:36,320 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:36,602 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.55724317
2015-10-17 18:18:37,367 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:38,117 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.31232682
2015-10-17 18:18:38,133 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.6207798
2015-10-17 18:18:38,133 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.83773214
2015-10-17 18:18:38,399 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:39,383 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.10000001
2015-10-17 18:18:39,414 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:39,649 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.5778057
2015-10-17 18:18:40,461 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:40,945 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.6207798
2015-10-17 18:18:41,149 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.32857633
2015-10-17 18:18:41,164 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.667
2015-10-17 18:18:41,196 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.87588555
2015-10-17 18:18:41,492 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:42,446 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.10000001
2015-10-17 18:18:42,539 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:42,664 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.6008844
2015-10-17 18:18:44,180 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.667
2015-10-17 18:18:44,196 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.34131393
2015-10-17 18:18:44,242 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.91191727
2015-10-17 18:18:45,164 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:45,492 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.10000001
2015-10-17 18:18:45,696 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.61098015
2015-10-17 18:18:46,180 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:47,227 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.6683285
2015-10-17 18:18:47,243 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.35352835
2015-10-17 18:18:47,243 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:47,305 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.95020163
2015-10-17 18:18:48,305 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:48,571 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.10000001
2015-10-17 18:18:48,727 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.620844
2015-10-17 18:18:49,321 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:50,243 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.7067541
2015-10-17 18:18:50,274 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.3638923
2015-10-17 18:18:50,336 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:50,399 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 0.978562
2015-10-17 18:18:51,680 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:51,680 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.10000001
2015-10-17 18:18:51,789 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.620844
2015-10-17 18:18:52,883 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:53,211 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_0 is : 1.0
2015-10-17 18:18:53,305 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.3638923
2015-10-17 18:18:53,493 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000008_1 is : 0.7391457
2015-10-17 18:18:53,493 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0005_m_000008_0
2015-10-17 18:18:53,493 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:18:53,493 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0005_01_000010 taskAttempt attempt_1445076437777_0005_m_000008_0
2015-10-17 18:18:53,493 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0005_m_000008_0
2015-10-17 18:18:53,493 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:18:53,930 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:18:53,930 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0005_m_000008_0
2015-10-17 18:18:53,930 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445076437777_0005_m_000008_1
2015-10-17 18:18:53,930 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:18:53,930 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-17 18:18:53,930 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000008_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 18:18:53,930 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0005_01_000015 taskAttempt attempt_1445076437777_0005_m_000008_1
2015-10-17 18:18:53,930 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0005_m_000008_1
2015-10-17 18:18:53,930 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:49130
2015-10-17 18:18:53,930 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 8 maxEvents 10000
2015-10-17 18:18:53,977 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000008_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 18:18:53,977 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 18:18:54,024 INFO [Socket Reader #1 for port 53652] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 53652: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 18:18:54,024 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out2/_temporary/1/_temporary/attempt_1445076437777_0005_m_000008_1
2015-10-17 18:18:54,024 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000008_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 18:18:54,774 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:54,821 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.10000001
2015-10-17 18:18:54,821 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.6240665
2015-10-17 18:18:54,993 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:18:56,040 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:18:56,118 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0005_01_000015
2015-10-17 18:18:56,118 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0005_01_000010
2015-10-17 18:18:56,118 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0005_m_000008_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:18:56,118 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:18:56,118 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0005_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:18:56,336 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.3638923
2015-10-17 18:18:57,102 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:18:57,852 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.6513658
2015-10-17 18:18:57,962 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.16666667
2015-10-17 18:18:58,462 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:18:59,368 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.36997458
2015-10-17 18:18:59,508 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:00,540 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:00,758 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.6513658
2015-10-17 18:19:00,883 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.667
2015-10-17 18:19:01,415 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.16666667
2015-10-17 18:19:01,555 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:02,399 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.39556876
2015-10-17 18:19:02,602 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:03,649 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:03,915 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.667
2015-10-17 18:19:04,477 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.16666667
2015-10-17 18:19:04,759 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:05,430 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.43733087
2015-10-17 18:19:05,821 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:06,946 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.667
2015-10-17 18:19:07,212 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:07,540 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.16666667
2015-10-17 18:19:08,227 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:08,477 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.44964966
2015-10-17 18:19:09,243 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:09,977 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.667
2015-10-17 18:19:10,274 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:10,587 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.20000002
2015-10-17 18:19:11,306 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:11,524 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.44964966
2015-10-17 18:19:12,352 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:13,009 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.683559
2015-10-17 18:19:13,384 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:13,681 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.20000002
2015-10-17 18:19:14,446 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:14,556 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.44964966
2015-10-17 18:19:15,478 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:16,040 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.71035266
2015-10-17 18:19:16,540 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:16,790 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.20000002
2015-10-17 18:19:17,587 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.44964966
2015-10-17 18:19:17,634 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:18,696 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:19,071 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.7371557
2015-10-17 18:19:19,759 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:19,884 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.20000002
2015-10-17 18:19:20,618 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.46116605
2015-10-17 18:19:21,228 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:22,103 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.7715656
2015-10-17 18:19:22,259 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:23,009 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.20000002
2015-10-17 18:19:23,306 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:23,650 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.49568966
2015-10-17 18:19:24,322 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:25,150 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.80277514
2015-10-17 18:19:25,353 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:26,275 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.23333333
2015-10-17 18:19:26,368 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:26,681 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.5352825
2015-10-17 18:19:27,368 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:28,197 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.82889915
2015-10-17 18:19:28,384 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:29,337 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.26666668
2015-10-17 18:19:29,400 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:29,712 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.5352825
2015-10-17 18:19:30,431 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:31,244 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.8586059
2015-10-17 18:19:31,478 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:32,415 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.26666668
2015-10-17 18:19:32,509 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:32,744 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.5352825
2015-10-17 18:19:33,540 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:34,275 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.88753045
2015-10-17 18:19:34,587 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:35,478 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.3
2015-10-17 18:19:35,619 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:35,775 INFO [IPC Server handler 12 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.5352825
2015-10-17 18:19:36,619 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:37,306 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.91510284
2015-10-17 18:19:37,619 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:38,650 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.3
2015-10-17 18:19:38,650 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:38,806 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.5352825
2015-10-17 18:19:39,900 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:40,338 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.94354206
2015-10-17 18:19:40,931 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:41,697 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.3
2015-10-17 18:19:41,853 INFO [IPC Server handler 24 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.60240734
2015-10-17 18:19:41,931 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:42,931 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:43,369 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.9673993
2015-10-17 18:19:43,947 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:44,728 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.3
2015-10-17 18:19:44,885 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_1 is : 0.620844
2015-10-17 18:19:44,947 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:45,947 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:46,400 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 0.99636704
2015-10-17 18:19:46,916 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_m_000007_0 is : 1.0
2015-10-17 18:19:46,916 INFO [IPC Server handler 29 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0005_m_000007_0
2015-10-17 18:19:46,916 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:19:46,916 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0005_01_000009 taskAttempt attempt_1445076437777_0005_m_000007_0
2015-10-17 18:19:46,916 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0005_m_000007_0
2015-10-17 18:19:46,916 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:19:46,947 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:19:46,947 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0005_m_000007_0
2015-10-17 18:19:46,947 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445076437777_0005_m_000007_1
2015-10-17 18:19:46,947 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:19:46,947 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-17 18:19:46,947 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000007_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-17 18:19:46,947 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0005_01_000013 taskAttempt attempt_1445076437777_0005_m_000007_1
2015-10-17 18:19:46,947 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0005_m_000007_1
2015-10-17 18:19:46,947 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:52150
2015-10-17 18:19:46,947 INFO [IPC Server handler 21 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 9 maxEvents 10000
2015-10-17 18:19:47,010 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000007_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-17 18:19:47,025 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-17 18:19:47,072 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out2/_temporary/1/_temporary/attempt_1445076437777_0005_m_000007_1
2015-10-17 18:19:47,072 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_m_000007_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-17 18:19:47,291 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:19:47,291 INFO [Socket Reader #1 for port 53652] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 53652: readAndProcess from client *********** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-17 18:19:47,822 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.3
2015-10-17 18:19:47,963 INFO [IPC Server handler 22 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:19:48,322 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0005_01_000009
2015-10-17 18:19:48,322 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445076437777_0005_01_000013
2015-10-17 18:19:48,322 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0005_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:19:48,322 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:19:48,322 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445076437777_0005_m_000007_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-17 18:19:49,025 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:19:50,103 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:19:50,978 INFO [IPC Server handler 11 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.3
2015-10-17 18:19:51,150 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:19:52,166 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:19:53,213 INFO [IPC Server handler 18 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:19:54,057 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.3
2015-10-17 18:19:54,260 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:19:55,338 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:19:56,416 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:19:57,166 INFO [IPC Server handler 7 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.3
2015-10-17 18:19:57,494 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:19:58,541 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:19:59,619 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:20:00,275 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.3
2015-10-17 18:20:00,713 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:20:01,807 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:20:02,854 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:20:03,401 INFO [IPC Server handler 2 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.3
2015-10-17 18:20:03,963 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445076437777_0005_r_000000_0. startIndex 10 maxEvents 10000
2015-10-17 18:20:04,666 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.3
2015-10-17 18:20:04,713 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.3
2015-10-17 18:20:06,463 INFO [IPC Server handler 4 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.67132217
2015-10-17 18:20:09,495 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.6908074
2015-10-17 18:20:12,526 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.6954033
2015-10-17 18:20:15,557 INFO [IPC Server handler 9 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.6954033
2015-10-17 18:20:30,620 INFO [IPC Server handler 25 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.71831864
2015-10-17 18:20:33,651 INFO [IPC Server handler 19 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.7377089
2015-10-17 18:20:36,683 INFO [IPC Server handler 17 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.7574623
2015-10-17 18:20:39,730 INFO [IPC Server handler 14 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.77736765
2015-10-17 18:20:42,745 INFO [IPC Server handler 26 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.7971815
2015-10-17 18:20:45,777 INFO [IPC Server handler 1 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.8162879
2015-10-17 18:20:48,824 INFO [IPC Server handler 15 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.8357233
2015-10-17 18:20:51,855 INFO [IPC Server handler 6 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.8557451
2015-10-17 18:20:54,902 INFO [IPC Server handler 27 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.87526006
2015-10-17 18:20:57,918 INFO [IPC Server handler 10 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.89491343
2015-10-17 18:21:00,949 INFO [IPC Server handler 3 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.9143118
2015-10-17 18:21:04,371 INFO [IPC Server handler 5 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.9339817
2015-10-17 18:21:07,402 INFO [IPC Server handler 28 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.95612204
2015-10-17 18:21:10,433 INFO [IPC Server handler 13 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.97541094
2015-10-17 18:21:13,465 INFO [IPC Server handler 16 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 0.99447256
2015-10-17 18:21:14,590 INFO [IPC Server handler 23 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445076437777_0005_r_000000_0
2015-10-17 18:21:14,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-17 18:21:14,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445076437777_0005_r_000000_0 given a go for committing the task output.
2015-10-17 18:21:14,590 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445076437777_0005_r_000000_0
2015-10-17 18:21:14,590 INFO [IPC Server handler 8 on 53652] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445076437777_0005_r_000000_0:true
2015-10-17 18:21:14,637 INFO [IPC Server handler 20 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445076437777_0005_r_000000_0 is : 1.0
2015-10-17 18:21:14,637 INFO [IPC Server handler 0 on 53652] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445076437777_0005_r_000000_0
2015-10-17 18:21:14,637 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-17 18:21:14,637 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445076437777_0005_01_000014 taskAttempt attempt_1445076437777_0005_r_000000_0
2015-10-17 18:21:14,637 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445076437777_0005_r_000000_0
2015-10-17 18:21:14,637 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-75DGDAM1.fareast.corp.microsoft.com:53425
2015-10-17 18:21:14,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445076437777_0005_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-17 18:21:14,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445076437777_0005_r_000000_0
2015-10-17 18:21:14,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445076437777_0005_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-17 18:21:14,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-17 18:21:14,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0005Job Transitioned from RUNNING to COMMITTING
2015-10-17 18:21:14,683 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-17 18:21:14,793 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-17 18:21:14,793 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445076437777_0005Job Transitioned from COMMITTING to SUCCEEDED
2015-10-17 18:21:14,793 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-17 18:21:14,793 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-17 18:21:14,793 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-17 18:21:14,793 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-17 18:21:14,793 INFO [Thread-106] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-17 18:21:14,793 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-17 18:21:14,793 INFO [Thread-106] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 0
2015-10-17 18:21:14,965 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0005/job_1445076437777_0005_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0005-1445076558325-msrabi-pagerank-1445077274793-10-1-SUCCEEDED-default-1445076957160.jhist_tmp
2015-10-17 18:21:15,137 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0005-1445076558325-msrabi-pagerank-1445077274793-10-1-SUCCEEDED-default-1445076957160.jhist_tmp
2015-10-17 18:21:15,137 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0005/job_1445076437777_0005_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0005_conf.xml_tmp
2015-10-17 18:21:15,355 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0005_conf.xml_tmp
2015-10-17 18:21:15,355 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0005.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0005.summary
2015-10-17 18:21:15,371 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0005_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0005_conf.xml
2015-10-17 18:21:15,371 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0005-1445076558325-msrabi-pagerank-1445077274793-10-1-SUCCEEDED-default-1445076957160.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445076437777_0005-1445076558325-msrabi-pagerank-1445077274793-10-1-SUCCEEDED-default-1445076957160.jhist
2015-10-17 18:21:15,371 INFO [Thread-106] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-17 18:21:15,387 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-17 18:21:15,387 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://04DN8IQ.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445076437777_0005
2015-10-17 18:21:15,402 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-17 18:21:16,402 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:9 RackLocal:4
2015-10-17 18:21:16,402 INFO [Thread-106] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445076437777_0005
2015-10-17 18:21:16,418 INFO [Thread-106] org.apache.hadoop.ipc.Server: Stopping server on 53652
2015-10-17 18:21:16,418 INFO [IPC Server listener on 53652] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 53652
2015-10-17 18:21:16,418 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-17 18:21:16,418 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
