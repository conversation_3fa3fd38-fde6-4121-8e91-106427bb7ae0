2015-10-19 15:49:53,716 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445182159119_0014_000001
2015-10-19 15:49:54,553 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-19 15:49:54,554 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 14 cluster_timestamp: 1445182159119 } attemptId: 1 } keyId: 1694045684)
2015-10-19 15:49:54,945 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-19 15:49:56,174 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-19 15:49:56,327 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-19 15:49:56,365 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-19 15:49:56,366 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-19 15:49:56,368 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-19 15:49:56,370 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-19 15:49:56,371 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-19 15:49:56,383 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-19 15:49:56,384 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-19 15:49:56,385 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-19 15:49:56,471 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 15:49:56,506 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 15:49:56,535 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 15:49:56,554 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-19 15:49:56,610 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-19 15:49:56,979 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 15:49:57,090 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 15:49:57,090 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-19 15:49:57,104 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445182159119_0014 to jobTokenSecretManager
2015-10-19 15:49:57,432 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445182159119_0014 because: not enabled; too many maps; too much input;
2015-10-19 15:49:57,461 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445182159119_0014 = 1256521728. Number of splits = 10
2015-10-19 15:49:57,462 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445182159119_0014 = 1
2015-10-19 15:49:57,463 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0014Job Transitioned from NEW to INITED
2015-10-19 15:49:57,464 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445182159119_0014.
2015-10-19 15:49:57,506 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 15:49:57,527 INFO [Socket Reader #1 for port 51075] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 51075
2015-10-19 15:49:57,566 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-19 15:49:57,567 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 15:49:57,571 INFO [IPC Server listener on 51075] org.apache.hadoop.ipc.Server: IPC Server listener on 51075: starting
2015-10-19 15:49:57,571 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at 04DN8IQ.fareast.corp.microsoft.com/*************:51075
2015-10-19 15:49:57,741 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-19 15:49:57,746 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-19 15:49:57,761 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-19 15:49:57,768 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-19 15:49:57,768 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-19 15:49:57,772 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-19 15:49:57,772 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-19 15:49:57,791 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 51082
2015-10-19 15:49:57,791 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-19 15:49:57,956 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\Jetty_0_0_0_0_51082_mapreduce____mzl5kb\webapp
2015-10-19 15:49:58,173 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:51082
2015-10-19 15:49:58,174 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 51082
2015-10-19 15:49:58,626 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-19 15:49:58,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445182159119_0014
2015-10-19 15:49:58,632 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 15:49:58,638 INFO [Socket Reader #1 for port 51086] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 51086
2015-10-19 15:49:58,646 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 15:49:58,650 INFO [IPC Server listener on 51086] org.apache.hadoop.ipc.Server: IPC Server listener on 51086: starting
2015-10-19 15:49:58,679 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-19 15:49:58,679 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-19 15:49:58,680 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-19 15:49:58,741 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at msra-sa-41/**************:8030
2015-10-19 15:49:58,857 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-19 15:49:58,857 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-19 15:49:58,864 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-19 15:49:58,867 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-19 15:49:58,878 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0014Job Transitioned from INITED to SETUP
2015-10-19 15:49:58,880 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-19 15:49:58,898 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0014Job Transitioned from SETUP to RUNNING
2015-10-19 15:49:58,932 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,934 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,938 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:58,939 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,939 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,939 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:58,940 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,940 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,940 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:58,940 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,940 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,941 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:58,941 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,941 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,942 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:58,942 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,942 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,943 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:58,943 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,943 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,943 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:58,944 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,944 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,944 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:58,944 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,944 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,945 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:58,945 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,945 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:58,945 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:58,946 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:58,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:58,950 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:58,950 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:58,950 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:58,950 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:58,951 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:58,951 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:58,951 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:58,951 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:58,951 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:58,952 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:58,954 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-19 15:49:58,965 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-19 15:49:59,000 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445182159119_0014, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0014/job_1445182159119_0014_1.jhist
2015-10-19 15:49:59,863 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 15:49:59,903 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:12288, vCores:-15> knownNMs=4
2015-10-19 15:49:59,906 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:12288, vCores:-15>
2015-10-19 15:49:59,906 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:00,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-17>
2015-10-19 15:50:00,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:01,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-17>
2015-10-19 15:50:01,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:02,936 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:50:02,937 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:02,939 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000002 to attempt_1445182159119_0014_m_000000_0
2015-10-19 15:50:02,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-20>
2015-10-19 15:50:02,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:02,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:0 RackLocal:1
2015-10-19 15:50:03,048 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:03,083 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0014/job.jar
2015-10-19 15:50:03,095 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0014/job.xml
2015-10-19 15:50:03,097 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-19 15:50:03,097 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-19 15:50:03,098 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-19 15:50:03,238 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:50:03,277 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000002 taskAttempt attempt_1445182159119_0014_m_000000_0
2015-10-19 15:50:03,281 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_m_000000_0
2015-10-19 15:50:03,316 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:50:03,634 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_m_000000_0 : 13562
2015-10-19 15:50:03,637 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_m_000000_0] using containerId: [container_1445182159119_0014_01_000002 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 15:50:03,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:50:03,644 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_m_000000
2015-10-19 15:50:03,644 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:50:03,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:6144, vCores:-21> knownNMs=4
2015-10-19 15:50:03,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:6144, vCores:-21>
2015-10-19 15:50:03,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:04,992 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:50:04,992 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:04,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000003 to attempt_1445182159119_0014_m_000001_0
2015-10-19 15:50:04,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 15:50:04,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:04,993 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:0 RackLocal:2
2015-10-19 15:50:04,994 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:04,995 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:50:05,082 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000003 taskAttempt attempt_1445182159119_0014_m_000001_0
2015-10-19 15:50:05,082 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_m_000001_0
2015-10-19 15:50:05,083 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:50:06,037 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-19 15:50:06,037 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 15:50:06,037 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:06,159 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_m_000001_0 : 13562
2015-10-19 15:50:06,160 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_m_000001_0] using containerId: [container_1445182159119_0014_01_000003 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 15:50:06,160 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:50:06,161 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_m_000001
2015-10-19 15:50:06,161 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:50:07,046 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 15:50:07,046 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:08,064 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 15:50:08,064 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:09,073 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:09,073 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:10,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:10,131 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:11,198 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:11,198 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:12,239 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:12,239 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:13,437 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:13,437 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:14,485 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:14,485 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:15,527 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:15,527 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:16,547 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:16,547 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:17,066 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:50:17,277 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_m_000002 asked for a task
2015-10-19 15:50:17,278 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_m_000002 given task: attempt_1445182159119_0014_m_000000_0
2015-10-19 15:50:17,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:17,584 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:17,585 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:50:18,720 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:18,720 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:18,948 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_m_000003 asked for a task
2015-10-19 15:50:18,949 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_m_000003 given task: attempt_1445182159119_0014_m_000001_0
2015-10-19 15:50:19,778 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:19,778 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:20,832 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:20,832 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:21,842 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:21,842 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:22,900 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:22,900 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:23,975 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:23,975 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:25,041 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:25,041 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:26,110 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:26,110 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:27,191 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:27,191 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:28,279 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:28,279 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:29,367 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:29,367 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:30,479 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:30,479 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:31,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:31,552 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:32,618 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:32,618 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:33,680 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:33,680 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:34,740 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:34,740 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:35,756 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:35,756 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:36,766 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:36,766 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:37,777 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:37,777 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:38,968 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:38,968 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:40,187 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:40,187 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:41,212 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:41,212 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:42,229 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:42,230 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:43,280 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:43,281 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:44,303 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:44,303 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:44,959 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.027680999
2015-10-19 15:50:45,323 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:45,323 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:46,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:46,362 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:47,388 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:47,388 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:48,427 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:48,427 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:49,448 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:49,448 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:50,028 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.08044359
2015-10-19 15:50:50,460 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:50,460 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:51,478 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:51,479 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:51,930 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.019536257
2015-10-19 15:50:52,494 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:52,495 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:53,522 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:53,522 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:54,546 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:54,546 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:55,229 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.0928173
2015-10-19 15:50:55,594 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:55,594 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:56,562 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.04070857
2015-10-19 15:50:56,634 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:56,634 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:57,687 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:57,687 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:58,722 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:58,722 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:59,765 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:59,765 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:00,089 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.10635664
2015-10-19 15:51:00,817 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.059595253
2015-10-19 15:51:00,828 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:00,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:01,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:01,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:02,928 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:02,928 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:03,965 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:03,965 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:04,825 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.10635664
2015-10-19 15:51:05,013 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:05,014 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:05,599 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.08532597
2015-10-19 15:51:06,027 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:06,027 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:07,049 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:07,049 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:08,057 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:08,057 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:09,057 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:09,057 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:09,604 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.10635664
2015-10-19 15:51:10,083 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:10,083 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:10,364 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.09965728
2015-10-19 15:51:11,093 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:11,093 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:12,118 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:12,118 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:13,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:13,128 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:14,151 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:14,151 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:14,733 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.10635664
2015-10-19 15:51:15,194 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:15,194 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:15,725 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.1066108
2015-10-19 15:51:16,229 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:16,230 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:17,283 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:17,283 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:18,294 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:18,294 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:19,322 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:19,323 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:19,761 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.10635664
2015-10-19 15:51:20,345 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:20,346 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:21,377 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:21,377 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:21,861 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.1066108
2015-10-19 15:51:22,405 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:22,405 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:23,429 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:23,429 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:24,460 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:24,460 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:24,700 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.10635664
2015-10-19 15:51:25,490 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:25,490 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:26,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:26,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:26,996 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.1066108
2015-10-19 15:51:27,549 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:27,549 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:28,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:28,615 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:29,664 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:29,664 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:30,102 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.10635664
2015-10-19 15:51:30,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:30,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:31,805 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:31,805 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:32,196 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.1066108
2015-10-19 15:51:32,852 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:32,852 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:33,930 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:33,930 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:34,664 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.10635664
2015-10-19 15:51:34,992 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:34,992 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:36,071 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:36,071 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:36,711 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.1066108
2015-10-19 15:51:37,149 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:37,149 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:38,196 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:38,196 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:39,227 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:39,227 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:39,508 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.10635664
2015-10-19 15:51:40,243 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:40,243 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:41,243 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:41,243 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:42,321 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:42,321 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:42,930 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.1066108
2015-10-19 15:51:43,430 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:43,430 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:44,555 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:44,555 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:45,071 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.10635664
2015-10-19 15:51:45,571 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:45,571 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:46,586 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:46,586 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:47,602 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:47,602 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:47,946 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.1066108
2015-10-19 15:51:48,602 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:48,602 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:49,633 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:49,633 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:50,415 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.10635664
2015-10-19 15:51:50,633 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:50,633 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:51,665 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:51,665 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:52,680 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:52,680 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:53,305 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.1066108
2015-10-19 15:51:53,680 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:53,680 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:54,712 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:54,712 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:55,493 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.12815438
2015-10-19 15:51:55,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:55,727 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:56,759 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:56,759 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:57,774 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:57,774 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:58,446 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.1066108
2015-10-19 15:51:58,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:58,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:00,368 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.16428363
2015-10-19 15:52:00,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:00,399 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:01,431 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:01,431 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:02,477 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:02,477 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:03,524 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:03,524 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:03,665 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.1066108
2015-10-19 15:52:04,603 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:04,603 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:05,478 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.19081266
2015-10-19 15:52:05,618 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:05,618 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:06,634 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:06,634 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:07,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:07,649 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:08,165 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.11872076
2015-10-19 15:52:08,696 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:08,696 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:09,728 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:09,728 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:10,759 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.19158794
2015-10-19 15:52:10,759 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:10,759 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:11,775 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:11,775 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:12,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:12,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:13,321 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.15646857
2015-10-19 15:52:13,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:13,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:14,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:14,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:15,743 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.19158794
2015-10-19 15:52:15,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:15,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:16,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:16,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:17,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:17,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:18,181 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.18238005
2015-10-19 15:52:18,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:18,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:19,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:19,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:20,869 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:20,869 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:20,994 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.19158794
2015-10-19 15:52:21,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:21,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:22,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:52:22,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000004 to attempt_1445182159119_0014_m_000002_0
2015-10-19 15:52:22,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:22,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:22,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:1 RackLocal:2
2015-10-19 15:52:22,915 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:22,915 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:52:22,947 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000004 taskAttempt attempt_1445182159119_0014_m_000002_0
2015-10-19 15:52:22,947 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_m_000002_0
2015-10-19 15:52:22,947 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:52:23,009 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_m_000002_0 : 13562
2015-10-19 15:52:23,009 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_m_000002_0] using containerId: [container_1445182159119_0014_01_000004 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:52:23,009 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:52:23,009 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_m_000002
2015-10-19 15:52:23,009 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:52:23,150 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.19211523
2015-10-19 15:52:23,947 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:52:23,947 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:23,947 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:25,009 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:25,009 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:25,791 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.19158794
2015-10-19 15:52:26,072 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:26,072 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:27,119 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:27,134 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:27,197 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:52:27,259 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_m_000004 asked for a task
2015-10-19 15:52:27,275 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_m_000004 given task: attempt_1445182159119_0014_m_000002_0
2015-10-19 15:52:28,166 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:28,166 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:28,416 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.19211523
2015-10-19 15:52:29,197 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:29,197 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:30,244 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:30,244 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:31,025 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.19158794
2015-10-19 15:52:31,306 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:31,306 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:32,400 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:32,400 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:33,431 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:33,431 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:34,478 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:34,478 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:34,541 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.19211523
2015-10-19 15:52:34,775 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.10660437
2015-10-19 15:52:35,525 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:35,525 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:36,119 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.19158794
2015-10-19 15:52:36,556 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:36,556 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:37,603 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:37,603 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:37,853 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.10660437
2015-10-19 15:52:38,603 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:38,603 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:39,353 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.19211523
2015-10-19 15:52:39,619 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:39,619 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:40,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:40,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:40,885 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.10660437
2015-10-19 15:52:41,525 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.19158794
2015-10-19 15:52:41,666 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:41,666 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:42,666 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:42,666 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:43,666 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:43,666 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:43,932 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.19211523
2015-10-19 15:52:43,932 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.19212553
2015-10-19 15:52:44,666 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:44,666 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:45,682 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:45,682 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:46,713 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:46,713 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:46,744 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.19158794
2015-10-19 15:52:46,963 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.19212553
2015-10-19 15:52:47,744 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:47,744 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:48,775 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:48,775 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:48,916 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.19211523
2015-10-19 15:52:49,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:49,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:49,994 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.19212553
2015-10-19 15:52:50,838 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:50,838 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:51,947 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:51,947 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:52,307 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.19158794
2015-10-19 15:52:52,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:52,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:53,041 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.2685725
2015-10-19 15:52:53,916 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.19211523
2015-10-19 15:52:53,994 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:53,994 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:55,026 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:55,026 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:56,088 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:56,088 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:56,104 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.27772525
2015-10-19 15:52:56,994 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.19158794
2015-10-19 15:52:57,104 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:57,119 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:58,213 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:58,213 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:58,979 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.19211523
2015-10-19 15:52:59,198 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.27772525
2015-10-19 15:52:59,338 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:59,338 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:00,432 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:00,432 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:01,510 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:01,510 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:02,104 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.2009838
2015-10-19 15:53:02,276 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.27772525
2015-10-19 15:53:02,588 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:02,588 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:03,651 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:03,651 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:03,854 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.19211523
2015-10-19 15:53:04,760 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:04,760 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:05,354 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.3598886
2015-10-19 15:53:05,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:05,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:06,870 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:06,870 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:07,073 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.23848547
2015-10-19 15:53:07,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:07,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:08,432 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.36317363
2015-10-19 15:53:08,651 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.19211523
2015-10-19 15:53:08,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:08,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:09,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:09,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:10,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:10,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:11,463 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.36317363
2015-10-19 15:53:11,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:11,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:12,651 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.27326387
2015-10-19 15:53:12,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:12,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:13,557 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.19211523
2015-10-19 15:53:13,995 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:13,995 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:14,510 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.40006182
2015-10-19 15:53:15,010 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:15,010 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:16,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:16,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:17,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:17,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:17,323 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.27696857
2015-10-19 15:53:17,604 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.44859612
2015-10-19 15:53:18,104 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:18,104 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:18,651 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.22394128
2015-10-19 15:53:19,104 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:19,104 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:20,149 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:20,149 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:20,714 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.44859612
2015-10-19 15:53:21,211 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:21,211 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:22,269 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:22,269 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:22,381 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.27696857
2015-10-19 15:53:23,291 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.249472
2015-10-19 15:53:23,291 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:23,291 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:23,760 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.44859612
2015-10-19 15:53:24,307 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:24,307 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:25,323 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:25,323 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:26,338 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:26,338 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:26,838 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.51463854
2015-10-19 15:53:26,932 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.27696857
2015-10-19 15:53:27,385 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:27,385 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:27,713 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.26901022
2015-10-19 15:53:28,432 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:28,432 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:29,448 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:29,448 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:30,026 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.5342037
2015-10-19 15:53:30,495 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:30,495 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:31,573 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:31,573 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:31,932 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.27696857
2015-10-19 15:53:32,307 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.27776006
2015-10-19 15:53:32,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:32,635 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:33,073 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.5342037
2015-10-19 15:53:33,729 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:33,729 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:34,792 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:34,792 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:35,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:35,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:36,135 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.55896276
2015-10-19 15:53:36,838 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:36,838 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:37,620 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.27776006
2015-10-19 15:53:37,635 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.27696857
2015-10-19 15:53:37,870 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:37,870 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:38,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:38,885 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:39,167 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.6196791
2015-10-19 15:53:39,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:39,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:40,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:40,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:41,948 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:41,948 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:42,229 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.6196791
2015-10-19 15:53:42,870 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.27696857
2015-10-19 15:53:42,964 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:42,964 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:43,057 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.27776006
2015-10-19 15:53:43,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:43,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:44,995 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:44,995 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:45,261 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.6196791
2015-10-19 15:53:46,011 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:46,011 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:47,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:47,042 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:47,432 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.27776006
2015-10-19 15:53:47,479 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.27696857
2015-10-19 15:53:48,120 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:48,120 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:48,292 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.65637237
2015-10-19 15:53:48,526 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.65637237
2015-10-19 15:53:49,136 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:49,136 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:50,151 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:50,151 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:51,167 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:51,167 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:51,339 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.667
2015-10-19 15:53:52,167 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:52,167 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:52,604 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.27696857
2015-10-19 15:53:52,636 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.27776006
2015-10-19 15:53:53,401 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:53,401 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:54,636 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:54,636 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:55,276 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.667
2015-10-19 15:53:55,651 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:55,651 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:56,667 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:56,667 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:57,698 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:57,698 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:57,901 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.27696857
2015-10-19 15:53:58,026 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.27776006
2015-10-19 15:53:58,401 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.6742255
2015-10-19 15:53:58,714 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:58,714 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:53:59,730 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:53:59,730 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:00,761 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:00,761 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:01,448 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.70006096
2015-10-19 15:54:01,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:01,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:02,870 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:02,870 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:02,964 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.27696857
2015-10-19 15:54:03,355 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.27776006
2015-10-19 15:54:03,870 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:03,870 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:04,495 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.7342274
2015-10-19 15:54:04,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:04,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:05,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:05,902 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:06,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:06,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:07,527 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.76882666
2015-10-19 15:54:07,558 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.28191242
2015-10-19 15:54:07,933 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:07,933 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:08,292 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.27776006
2015-10-19 15:54:08,949 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:08,949 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:09,964 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:09,964 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:10,558 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.8031962
2015-10-19 15:54:11,152 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:11,152 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:12,152 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:12,152 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:12,714 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.32190678
2015-10-19 15:54:13,167 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:13,167 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:13,230 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.27776006
2015-10-19 15:54:13,589 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.83704495
2015-10-19 15:54:14,167 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:14,167 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:15,183 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:15,183 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:16,199 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:16,199 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:16,652 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.8708832
2015-10-19 15:54:17,230 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:17,230 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:17,730 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.3547169
2015-10-19 15:54:17,996 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.27776006
2015-10-19 15:54:18,230 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:18,230 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:19,246 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:19,246 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:19,699 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.9004229
2015-10-19 15:54:20,261 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:20,261 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:21,277 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:21,277 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:22,308 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:22,308 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:22,371 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.3624012
2015-10-19 15:54:22,527 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.2970031
2015-10-19 15:54:22,730 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.9151572
2015-10-19 15:54:23,355 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:23,355 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:24,355 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:24,355 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:25,371 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:25,371 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:25,793 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.9300628
2015-10-19 15:54:26,387 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:54:26,387 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000005 to attempt_1445182159119_0014_m_000003_0
2015-10-19 15:54:26,387 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:26,387 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:26,387 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:2 RackLocal:2
2015-10-19 15:54:26,402 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:54:26,402 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:54:26,449 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000005 taskAttempt attempt_1445182159119_0014_m_000003_0
2015-10-19 15:54:26,449 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_m_000003_0
2015-10-19 15:54:26,449 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:54:27,246 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.3624012
2015-10-19 15:54:27,246 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_m_000003_0 : 13562
2015-10-19 15:54:27,246 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_m_000003_0] using containerId: [container_1445182159119_0014_01_000005 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:54:27,246 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:54:27,262 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_m_000003
2015-10-19 15:54:27,262 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:54:27,387 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-28> knownNMs=4
2015-10-19 15:54:27,387 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:27,387 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:27,965 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.33771262
2015-10-19 15:54:28,387 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:54:28,387 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000006 to attempt_1445182159119_0014_m_000004_0
2015-10-19 15:54:28,387 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:28,387 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:28,387 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:5 ScheduledReds:0 AssignedMaps:5 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:5 ContRel:0 HostLocal:3 RackLocal:2
2015-10-19 15:54:28,387 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:54:28,387 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:54:28,418 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000006 taskAttempt attempt_1445182159119_0014_m_000004_0
2015-10-19 15:54:28,418 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_m_000004_0
2015-10-19 15:54:28,418 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:54:28,902 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_m_000004_0 : 13562
2015-10-19 15:54:28,902 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_m_000004_0] using containerId: [container_1445182159119_0014_01_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:54:28,902 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:54:28,902 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_m_000004
2015-10-19 15:54:28,902 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:54:29,137 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.94617355
2015-10-19 15:54:29,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-28> knownNMs=4
2015-10-19 15:54:29,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:29,402 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:30,418 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-28>
2015-10-19 15:54:30,418 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:31,418 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-27>
2015-10-19 15:54:31,418 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:32,090 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.3624012
2015-10-19 15:54:32,137 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:54:32,168 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_m_000005 asked for a task
2015-10-19 15:54:32,168 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_m_000005 given task: attempt_1445182159119_0014_m_000003_0
2015-10-19 15:54:32,168 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 0.9755724
2015-10-19 15:54:32,355 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:54:32,371 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_m_000006 asked for a task
2015-10-19 15:54:32,371 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_m_000006 given task: attempt_1445182159119_0014_m_000004_0
2015-10-19 15:54:32,418 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-27>
2015-10-19 15:54:32,418 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:32,699 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.36319977
2015-10-19 15:54:33,434 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:54:33,434 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000007 to attempt_1445182159119_0014_m_000005_0
2015-10-19 15:54:33,434 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:33,434 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:33,434 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:4 RackLocal:2
2015-10-19 15:54:33,434 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:54:33,434 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:54:33,480 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000007 taskAttempt attempt_1445182159119_0014_m_000005_0
2015-10-19 15:54:33,480 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_m_000005_0
2015-10-19 15:54:33,480 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:54:33,574 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_m_000005_0 : 13562
2015-10-19 15:54:33,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_m_000005_0] using containerId: [container_1445182159119_0014_01_000007 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:54:33,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:54:33,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_m_000005
2015-10-19 15:54:33,574 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:54:34,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:54:34,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:34,449 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:54:34,793 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000002_0 is : 1.0
2015-10-19 15:54:34,793 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0014_m_000002_0
2015-10-19 15:54:34,793 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000002_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:54:34,840 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0014_01_000004 taskAttempt attempt_1445182159119_0014_m_000002_0
2015-10-19 15:54:34,840 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0014_m_000002_0
2015-10-19 15:54:34,840 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:54:34,934 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000002_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:54:34,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0014_m_000002_0
2015-10-19 15:54:34,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:54:34,949 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-19 15:54:35,465 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:4 RackLocal:2
2015-10-19 15:54:35,465 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:35,465 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-19 15:54:35,465 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:6144, vCores:-21> finalMapResourceLimit:<memory:5530, vCores:-19> finalReduceResourceLimit:<memory:614, vCores:-2> netScheduledMapResource:<memory:10240, vCores:10> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:35,934 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.3624012
2015-10-19 15:54:36,481 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0014_01_000004
2015-10-19 15:54:36,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:54:36,496 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0014_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:54:36,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000008 to attempt_1445182159119_0014_m_000006_0
2015-10-19 15:54:36,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:36,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:6144, vCores:-21> finalMapResourceLimit:<memory:5530, vCores:-19> finalReduceResourceLimit:<memory:614, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:36,496 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:5 RackLocal:2
2015-10-19 15:54:36,496 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:54:36,496 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:54:36,543 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000008 taskAttempt attempt_1445182159119_0014_m_000006_0
2015-10-19 15:54:36,543 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_m_000006_0
2015-10-19 15:54:36,543 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:54:36,621 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_m_000006_0 : 13562
2015-10-19 15:54:36,621 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_m_000006_0] using containerId: [container_1445182159119_0014_01_000008 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:54:36,621 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:54:36,621 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_m_000006
2015-10-19 15:54:36,621 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:54:36,746 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.36319977
2015-10-19 15:54:36,840 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:54:36,856 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_m_000007 asked for a task
2015-10-19 15:54:36,856 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_m_000007 given task: attempt_1445182159119_0014_m_000005_0
2015-10-19 15:54:37,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:54:37,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:54:37,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000009 to attempt_1445182159119_0014_m_000007_0
2015-10-19 15:54:37,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:37,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-20> finalMapResourceLimit:<memory:6452, vCores:-18> finalReduceResourceLimit:<memory:716, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:37,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:6 RackLocal:2
2015-10-19 15:54:37,512 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:54:37,512 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:54:37,559 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000009 taskAttempt attempt_1445182159119_0014_m_000007_0
2015-10-19 15:54:37,559 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_m_000007_0
2015-10-19 15:54:37,559 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:54:37,652 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_m_000007_0 : 13562
2015-10-19 15:54:37,652 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_m_000007_0] using containerId: [container_1445182159119_0014_01_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:54:37,652 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:54:37,652 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_m_000007
2015-10-19 15:54:37,652 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:54:38,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:54:38,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:38,512 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-20> finalMapResourceLimit:<memory:6452, vCores:-18> finalReduceResourceLimit:<memory:716, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:39,512 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.106493875
2015-10-19 15:54:39,527 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:39,527 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-20> finalMapResourceLimit:<memory:6452, vCores:-18> finalReduceResourceLimit:<memory:716, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:39,793 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.10680563
2015-10-19 15:54:40,293 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.3624012
2015-10-19 15:54:40,543 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:40,543 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-20> finalMapResourceLimit:<memory:6452, vCores:-18> finalReduceResourceLimit:<memory:716, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:40,934 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:54:40,965 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_m_000008 asked for a task
2015-10-19 15:54:40,965 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_m_000008 given task: attempt_1445182159119_0014_m_000006_0
2015-10-19 15:54:41,559 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:41,559 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-20> finalMapResourceLimit:<memory:6452, vCores:-18> finalReduceResourceLimit:<memory:716, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:41,653 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.36319977
2015-10-19 15:54:41,793 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:54:41,824 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_m_000009 asked for a task
2015-10-19 15:54:41,824 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_m_000009 given task: attempt_1445182159119_0014_m_000007_0
2015-10-19 15:54:42,543 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.106493875
2015-10-19 15:54:42,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:42,574 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:7168, vCores:-20> finalMapResourceLimit:<memory:6452, vCores:-18> finalReduceResourceLimit:<memory:716, vCores:-2> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:42,809 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.10680563
2015-10-19 15:54:43,590 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:54:43,590 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000010 to attempt_1445182159119_0014_m_000008_0
2015-10-19 15:54:43,590 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:43,590 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:43,590 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:7 RackLocal:2
2015-10-19 15:54:43,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:54:43,590 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:54:43,621 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000010 taskAttempt attempt_1445182159119_0014_m_000008_0
2015-10-19 15:54:43,621 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_m_000008_0
2015-10-19 15:54:43,621 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:54:43,684 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_m_000008_0 : 13562
2015-10-19 15:54:43,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_m_000008_0] using containerId: [container_1445182159119_0014_01_000010 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:54:43,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:54:43,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_m_000008
2015-10-19 15:54:43,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:54:44,590 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.3624012
2015-10-19 15:54:44,621 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:54:44,621 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:44,621 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:45,512 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.10685723
2015-10-19 15:54:45,559 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.106493875
2015-10-19 15:54:45,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:45,668 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:45,824 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.10680563
2015-10-19 15:54:46,246 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.36319977
2015-10-19 15:54:46,700 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:46,700 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:46,903 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:54:46,934 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_m_000010 asked for a task
2015-10-19 15:54:46,934 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_m_000010 given task: attempt_1445182159119_0014_m_000008_0
2015-10-19 15:54:47,700 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:47,700 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:48,559 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.10685723
2015-10-19 15:54:48,575 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.113282934
2015-10-19 15:54:48,746 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:48,746 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:48,840 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.10680563
2015-10-19 15:54:48,903 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.3624012
2015-10-19 15:54:49,418 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.106964506
2015-10-19 15:54:49,778 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:49,778 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:50,200 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.10681946
2015-10-19 15:54:50,793 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:50,793 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:50,856 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.36319977
2015-10-19 15:54:51,590 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.1821973
2015-10-19 15:54:51,590 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.10685723
2015-10-19 15:54:51,809 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:51,809 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:51,856 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.10680563
2015-10-19 15:54:52,450 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.106964506
2015-10-19 15:54:52,840 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:52,840 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:53,231 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.10681946
2015-10-19 15:54:53,747 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.3624012
2015-10-19 15:54:53,856 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:53,856 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:54,122 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.106881365
2015-10-19 15:54:54,606 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.19209063
2015-10-19 15:54:54,653 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.13854504
2015-10-19 15:54:54,856 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.17923078
2015-10-19 15:54:54,872 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:54,872 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:55,403 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.36319977
2015-10-19 15:54:55,481 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.106964506
2015-10-19 15:54:55,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:55,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:56,278 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.10681946
2015-10-19 15:54:56,903 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:56,903 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:57,137 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.106881365
2015-10-19 15:54:57,653 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.19209063
2015-10-19 15:54:57,684 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.19247705
2015-10-19 15:54:57,887 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.19242907
2015-10-19 15:54:57,934 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:57,934 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:58,356 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.3624012
2015-10-19 15:54:58,544 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.19266446
2015-10-19 15:54:58,965 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:58,965 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:54:59,372 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.19255035
2015-10-19 15:54:59,872 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.36319977
2015-10-19 15:54:59,965 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:54:59,981 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:55:00,153 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.106881365
2015-10-19 15:55:00,684 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.19209063
2015-10-19 15:55:00,715 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.19247705
2015-10-19 15:55:00,903 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.19242907
2015-10-19 15:55:00,981 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:55:00,981 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:55:01,575 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.19266446
2015-10-19 15:55:01,981 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:55:01,981 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:55:02,403 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.19255035
2015-10-19 15:55:02,434 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.3624012
2015-10-19 15:55:02,981 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:55:02,981 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:55:03,169 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.12775043
2015-10-19 15:55:03,700 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.21283701
2015-10-19 15:55:03,747 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.19247705
2015-10-19 15:55:03,919 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.19242907
2015-10-19 15:55:03,981 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:55:03,981 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:55:03,997 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.36319977
2015-10-19 15:55:04,622 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.19266446
2015-10-19 15:55:04,997 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:55:04,997 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:55:05,450 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.19255035
2015-10-19 15:55:06,044 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:55:06,044 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:55:06,184 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.19258286
2015-10-19 15:55:06,731 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.27625892
2015-10-19 15:55:06,794 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.27813601
2015-10-19 15:55:06,966 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.19242907
2015-10-19 15:55:07,091 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:55:07,091 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: completedMapPercent 0.1 totalResourceLimit:<memory:8192, vCores:-19> finalMapResourceLimit:<memory:7373, vCores:-18> finalReduceResourceLimit:<memory:819, vCores:-1> netScheduledMapResource:<memory:9216, vCores:9> netScheduledReduceResource:<memory:0, vCores:0>
2015-10-19 15:55:07,278 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.40272135
2015-10-19 15:55:07,684 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.2115963
2015-10-19 15:55:08,169 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:55:08,184 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:55:08,184 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000011 to attempt_1445182159119_0014_m_000009_0
2015-10-19 15:55:08,184 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:55:08,184 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-19 15:55:08,184 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-19 15:55:08,184 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:55:08,184 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:55:08,184 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000011 taskAttempt attempt_1445182159119_0014_m_000009_0
2015-10-19 15:55:08,184 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_m_000009_0
2015-10-19 15:55:08,184 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:55:08,450 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.36319977
2015-10-19 15:55:08,466 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_m_000009_0 : 13562
2015-10-19 15:55:08,481 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_m_000009_0] using containerId: [container_1445182159119_0014_01_000011 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 15:55:08,481 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:55:08,481 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_m_000009
2015-10-19 15:55:08,481 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:55:08,497 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.27015832
2015-10-19 15:55:08,575 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0014_m_000001
2015-10-19 15:55:08,575 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:55:08,575 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0014_m_000001
2015-10-19 15:55:08,575 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:55:08,575 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:55:08,575 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000001_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:55:09,198 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:10 ContRel:0 HostLocal:7 RackLocal:3
2015-10-19 15:55:09,225 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.19258286
2015-10-19 15:55:09,245 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=5 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:55:09,760 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.27765483
2015-10-19 15:55:09,859 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.27813601
2015-10-19 15:55:10,029 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.19242907
2015-10-19 15:55:10,756 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.2783809
2015-10-19 15:55:11,561 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.27825075
2015-10-19 15:55:12,249 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.19258286
2015-10-19 15:55:12,436 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.4429698
2015-10-19 15:55:12,749 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.37541217
2015-10-19 15:55:12,780 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.27765483
2015-10-19 15:55:12,905 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.27813601
2015-10-19 15:55:13,812 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.2783809
2015-10-19 15:55:14,624 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.27825075
2015-10-19 15:55:15,280 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.19258286
2015-10-19 15:55:15,812 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.31748053
2015-10-19 15:55:15,999 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.27824634
2015-10-19 15:55:16,093 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.2781602
2015-10-19 15:55:16,765 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.44789755
2015-10-19 15:55:16,905 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.2783809
2015-10-19 15:55:16,983 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.41443986
2015-10-19 15:55:17,718 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.304781
2015-10-19 15:55:18,327 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.21148959
2015-10-19 15:55:18,843 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.36323506
2015-10-19 15:55:19,030 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.36390656
2015-10-19 15:55:19,109 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.2781602
2015-10-19 15:55:19,937 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.31925717
2015-10-19 15:55:20,780 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.3638923
2015-10-19 15:55:21,343 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.27811313
2015-10-19 15:55:21,624 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.44789755
2015-10-19 15:55:21,624 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.448704
2015-10-19 15:55:21,859 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.36323506
2015-10-19 15:55:22,077 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.36390656
2015-10-19 15:55:22,171 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.2781602
2015-10-19 15:55:23,265 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.36404583
2015-10-19 15:55:23,437 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:55:23,484 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_m_000011 asked for a task
2015-10-19 15:55:23,484 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_m_000011 given task: attempt_1445182159119_0014_m_000009_0
2015-10-19 15:55:23,812 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.3638923
2015-10-19 15:55:24,671 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.27811313
2015-10-19 15:55:24,874 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.36323506
2015-10-19 15:55:25,124 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.36390656
2015-10-19 15:55:25,187 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.33899474
2015-10-19 15:55:26,140 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.44789755
2015-10-19 15:55:26,312 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.36404583
2015-10-19 15:55:26,452 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.448704
2015-10-19 15:55:26,812 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:55:26,812 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-19 15:55:26,812 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000012 to attempt_1445182159119_0014_r_000000_0
2015-10-19 15:55:26,812 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:0 HostLocal:7 RackLocal:3
2015-10-19 15:55:26,827 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:55:26,827 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:55:26,843 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000012 taskAttempt attempt_1445182159119_0014_r_000000_0
2015-10-19 15:55:26,843 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_r_000000_0
2015-10-19 15:55:26,843 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:55:26,859 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.3638923
2015-10-19 15:55:27,249 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_r_000000_0 : 13562
2015-10-19 15:55:27,249 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_r_000000_0] using containerId: [container_1445182159119_0014_01_000012 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 15:55:27,249 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:55:27,249 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_r_000000
2015-10-19 15:55:27,249 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:55:27,734 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.27811313
2015-10-19 15:55:27,859 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:55:27,890 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.371203
2015-10-19 15:55:28,156 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.44742498
2015-10-19 15:55:28,202 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.36388028
2015-10-19 15:55:29,374 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.36404583
2015-10-19 15:55:29,921 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.44964966
2015-10-19 15:55:30,249 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.44789755
2015-10-19 15:55:30,781 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.28729743
2015-10-19 15:55:30,937 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.44497404
2015-10-19 15:55:31,203 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.44950968
2015-10-19 15:55:31,249 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.36388028
2015-10-19 15:55:31,437 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.448704
2015-10-19 15:55:32,421 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.44980705
2015-10-19 15:55:32,968 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.44964966
2015-10-19 15:55:33,812 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.3637686
2015-10-19 15:55:33,953 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.4486067
2015-10-19 15:55:34,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:55:34,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000013 to attempt_1445182159119_0014_m_000001_1
2015-10-19 15:55:34,031 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:8 RackLocal:3
2015-10-19 15:55:34,031 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:55:34,031 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000001_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:55:34,031 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000013 taskAttempt attempt_1445182159119_0014_m_000001_1
2015-10-19 15:55:34,031 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_m_000001_1
2015-10-19 15:55:34,031 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:55:34,093 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:55:34,124 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_m_000001_1 : 13562
2015-10-19 15:55:34,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_m_000001_1] using containerId: [container_1445182159119_0014_01_000013 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:55:34,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000001_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:55:34,140 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_m_000001
2015-10-19 15:55:34,140 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_r_000012 asked for a task
2015-10-19 15:55:34,140 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_r_000012 given task: attempt_1445182159119_0014_r_000000_0
2015-10-19 15:55:34,234 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.44950968
2015-10-19 15:55:34,265 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.36388028
2015-10-19 15:55:34,859 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.44789755
2015-10-19 15:55:34,921 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0014_m_000000
2015-10-19 15:55:34,921 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0014_m_000000
2015-10-19 15:55:34,921 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:55:34,921 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:55:34,921 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:55:34,921 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:55:35,140 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:0 HostLocal:8 RackLocal:3
2015-10-19 15:55:35,140 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:55:35,484 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.44980705
2015-10-19 15:55:36,218 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.44964966
2015-10-19 15:55:36,234 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.448704
2015-10-19 15:55:36,484 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:55:36,515 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_m_000013 asked for a task
2015-10-19 15:55:36,515 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_m_000013 given task: attempt_1445182159119_0014_m_000001_1
2015-10-19 15:55:36,874 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.3637686
2015-10-19 15:55:36,999 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.4486067
2015-10-19 15:55:37,281 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.4657365
2015-10-19 15:55:37,328 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.39469105
2015-10-19 15:55:38,359 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:55:38,359 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:55:38,359 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000014 to attempt_1445182159119_0014_m_000000_1
2015-10-19 15:55:38,359 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:8 RackLocal:4
2015-10-19 15:55:38,359 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:55:38,359 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:55:38,359 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000014 taskAttempt attempt_1445182159119_0014_m_000000_1
2015-10-19 15:55:38,359 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_m_000000_1
2015-10-19 15:55:38,359 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:55:38,562 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.44980705
2015-10-19 15:55:39,265 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.49852645
2015-10-19 15:55:39,390 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.44789755
2015-10-19 15:55:39,390 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:55:39,609 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 0 maxEvents 10000
2015-10-19 15:55:39,921 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.3637686
2015-10-19 15:55:40,031 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.4486067
2015-10-19 15:55:40,062 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.11101393
2015-10-19 15:55:40,390 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.5352021
2015-10-19 15:55:40,437 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.44968578
2015-10-19 15:55:40,625 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:40,921 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.448704
2015-10-19 15:55:41,640 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:41,640 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.46907303
2015-10-19 15:55:42,343 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.5352825
2015-10-19 15:55:42,640 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:43,000 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.36753333
2015-10-19 15:55:43,140 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.15752049
2015-10-19 15:55:43,140 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.49303722
2015-10-19 15:55:43,484 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.5352021
2015-10-19 15:55:43,500 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.44968578
2015-10-19 15:55:43,609 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_m_000000_1 : 13562
2015-10-19 15:55:43,609 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_m_000000_1] using containerId: [container_1445182159119_0014_01_000014 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 15:55:43,609 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:55:43,609 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_m_000000
2015-10-19 15:55:43,640 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:43,937 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.44789755
2015-10-19 15:55:44,078 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.1066108
2015-10-19 15:55:44,593 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.0
2015-10-19 15:55:44,640 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:44,765 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.53543663
2015-10-19 15:55:45,406 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.5352825
2015-10-19 15:55:45,640 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:45,703 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.448704
2015-10-19 15:55:46,078 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.44950172
2015-10-19 15:55:46,203 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.5343203
2015-10-19 15:55:46,234 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.19802386
2015-10-19 15:55:46,547 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.44968578
2015-10-19 15:55:46,562 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.5352021
2015-10-19 15:55:46,640 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:47,125 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.1066108
2015-10-19 15:55:47,640 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:47,656 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.0
2015-10-19 15:55:47,937 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.53543663
2015-10-19 15:55:47,937 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.44789755
2015-10-19 15:55:48,609 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.5352825
2015-10-19 15:55:48,640 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:49,312 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.5343203
2015-10-19 15:55:49,343 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.22593029
2015-10-19 15:55:49,593 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.44950172
2015-10-19 15:55:49,656 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:49,672 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.46854916
2015-10-19 15:55:49,703 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.55720776
2015-10-19 15:55:49,922 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0014_m_000009
2015-10-19 15:55:49,922 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:55:49,922 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0014_m_000009
2015-10-19 15:55:49,922 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:55:49,922 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:55:49,922 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000009_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:55:49,984 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.448704
2015-10-19 15:55:50,343 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.1066108
2015-10-19 15:55:50,656 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:50,687 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:0 HostLocal:8 RackLocal:4
2015-10-19 15:55:50,734 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.0
2015-10-19 15:55:50,984 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:6144, vCores:-21> knownNMs=4
2015-10-19 15:55:51,109 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.53543663
2015-10-19 15:55:51,656 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:51,859 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.620844
2015-10-19 15:55:52,015 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.44789755
2015-10-19 15:55:52,172 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:55:52,172 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000015 to attempt_1445182159119_0014_m_000009_1
2015-10-19 15:55:52,172 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:55:52,172 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:55:52,172 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000009_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:55:52,172 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000015 taskAttempt attempt_1445182159119_0014_m_000009_1
2015-10-19 15:55:52,172 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_m_000009_1
2015-10-19 15:55:52,172 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:55:52,422 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.22593029
2015-10-19 15:55:52,593 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.5343203
2015-10-19 15:55:52,656 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:52,812 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.44950172
2015-10-19 15:55:52,875 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.5352028
2015-10-19 15:55:52,937 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.6209487
2015-10-19 15:55:52,984 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_m_000009_1 : 13562
2015-10-19 15:55:52,984 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_m_000009_1] using containerId: [container_1445182159119_0014_01_000015 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:55:52,984 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000009_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:55:52,984 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_m_000009
2015-10-19 15:55:53,500 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:5120, vCores:-22> knownNMs=4
2015-10-19 15:55:53,609 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.19211523
2015-10-19 15:55:53,656 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:53,828 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.0
2015-10-19 15:55:54,265 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.448704
2015-10-19 15:55:54,422 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.6210422
2015-10-19 15:55:54,672 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:55,015 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.620844
2015-10-19 15:55:55,344 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:55:55,453 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_m_000015 asked for a task
2015-10-19 15:55:55,453 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_m_000015 given task: attempt_1445182159119_0014_m_000009_1
2015-10-19 15:55:55,672 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:55,672 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.60515404
2015-10-19 15:55:55,953 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.50375605
2015-10-19 15:55:56,109 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.6209487
2015-10-19 15:55:56,109 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.5352028
2015-10-19 15:55:56,219 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.46622846
2015-10-19 15:55:56,672 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:56,781 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.19211523
2015-10-19 15:55:56,953 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.0
2015-10-19 15:55:57,500 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.6210422
2015-10-19 15:55:57,687 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:58,109 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.620844
2015-10-19 15:55:58,469 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.448704
2015-10-19 15:55:58,640 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.24212956
2015-10-19 15:55:58,703 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:58,875 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.6199081
2015-10-19 15:55:59,109 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.53521925
2015-10-19 15:55:59,234 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.5352028
2015-10-19 15:55:59,234 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.6209487
2015-10-19 15:55:59,640 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:55:59,719 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:55:59,906 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.19211523
2015-10-19 15:56:00,203 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.0
2015-10-19 15:56:00,375 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.50548667
2015-10-19 15:56:00,406 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_m_000014 asked for a task
2015-10-19 15:56:00,406 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_m_000014 given task: attempt_1445182159119_0014_m_000000_1
2015-10-19 15:56:00,609 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.6210422
2015-10-19 15:56:00,719 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:00,859 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.620844
2015-10-19 15:56:01,078 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.6209487
2015-10-19 15:56:01,203 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.667
2015-10-19 15:56:01,734 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:01,859 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.27723905
2015-10-19 15:56:01,953 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.6199081
2015-10-19 15:56:02,187 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.53521925
2015-10-19 15:56:02,375 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.5729343
2015-10-19 15:56:02,375 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.667
2015-10-19 15:56:02,750 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:02,875 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.295472
2015-10-19 15:56:02,922 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.46885464
2015-10-19 15:56:03,031 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.24786545
2015-10-19 15:56:03,234 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.6210422
2015-10-19 15:56:03,281 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.0
2015-10-19 15:56:03,703 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.667
2015-10-19 15:56:03,766 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:04,281 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.667
2015-10-19 15:56:04,734 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.53341997
2015-10-19 15:56:04,766 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:04,984 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.295472
2015-10-19 15:56:05,000 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.6246884
2015-10-19 15:56:05,250 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.53521925
2015-10-19 15:56:05,453 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.6208445
2015-10-19 15:56:05,453 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.667
2015-10-19 15:56:05,766 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:05,922 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.295472
2015-10-19 15:56:06,078 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.27776006
2015-10-19 15:56:06,344 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.0
2015-10-19 15:56:06,766 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.51454884
2015-10-19 15:56:06,766 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.667
2015-10-19 15:56:06,766 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:06,937 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.6246884
2015-10-19 15:56:07,375 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.667
2015-10-19 15:56:07,766 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:08,078 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.667
2015-10-19 15:56:08,094 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.295472
2015-10-19 15:56:08,328 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.56912845
2015-10-19 15:56:08,500 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.53341997
2015-10-19 15:56:08,500 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.667
2015-10-19 15:56:08,531 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.6208445
2015-10-19 15:56:08,781 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:08,984 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.295472
2015-10-19 15:56:09,141 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.27776006
2015-10-19 15:56:09,406 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.0
2015-10-19 15:56:09,797 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:09,891 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.667
2015-10-19 15:56:10,438 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.693526
2015-10-19 15:56:10,500 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.53425497
2015-10-19 15:56:10,797 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:11,141 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.667
2015-10-19 15:56:11,219 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.295472
2015-10-19 15:56:11,359 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.6165414
2015-10-19 15:56:11,563 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.69857675
2015-10-19 15:56:11,625 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.6208445
2015-10-19 15:56:11,797 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:12,000 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.295472
2015-10-19 15:56:12,203 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.2798099
2015-10-19 15:56:12,359 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.53341997
2015-10-19 15:56:12,469 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.0
2015-10-19 15:56:12,797 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:12,938 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.6950726
2015-10-19 15:56:13,516 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.72972196
2015-10-19 15:56:13,813 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:14,172 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.53425497
2015-10-19 15:56:14,188 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.667
2015-10-19 15:56:14,406 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.295472
2015-10-19 15:56:14,438 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.6207798
2015-10-19 15:56:14,656 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.7300258
2015-10-19 15:56:14,672 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.6208445
2015-10-19 15:56:14,828 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:15,063 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.47583267
2015-10-19 15:56:15,281 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.36319977
2015-10-19 15:56:15,610 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.0
2015-10-19 15:56:15,828 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:15,985 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.72105384
2015-10-19 15:56:16,188 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.53341997
2015-10-19 15:56:16,578 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.7567016
2015-10-19 15:56:16,828 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:17,235 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.6208445
2015-10-19 15:56:17,235 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.667
2015-10-19 15:56:17,453 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.6207798
2015-10-19 15:56:17,485 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.295472
2015-10-19 15:56:17,672 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.667
2015-10-19 15:56:17,703 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.7596203
2015-10-19 15:56:17,828 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:18,094 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.5323719
2015-10-19 15:56:18,313 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.36319977
2015-10-19 15:56:18,703 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.0
2015-10-19 15:56:18,781 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.53425497
2015-10-19 15:56:18,828 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:19,063 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.75603116
2015-10-19 15:56:19,625 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.79031485
2015-10-19 15:56:19,844 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:20,250 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.68766266
2015-10-19 15:56:20,266 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.53341997
2015-10-19 15:56:20,469 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.6207798
2015-10-19 15:56:20,672 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.295472
2015-10-19 15:56:20,703 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.667
2015-10-19 15:56:20,750 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.7918902
2015-10-19 15:56:20,891 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:21,110 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.5323719
2015-10-19 15:56:21,360 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.36319977
2015-10-19 15:56:21,922 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:22,094 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.7896353
2015-10-19 15:56:22,203 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.033333335
2015-10-19 15:56:22,657 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.8255825
2015-10-19 15:56:22,938 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:23,282 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.73301804
2015-10-19 15:56:23,469 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.6588571
2015-10-19 15:56:23,485 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.53425497
2015-10-19 15:56:23,703 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.6588571
2015-10-19 15:56:23,719 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.667
2015-10-19 15:56:23,782 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.8263291
2015-10-19 15:56:23,891 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.36372873
2015-10-19 15:56:23,969 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:24,125 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.5323719
2015-10-19 15:56:24,391 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.36319977
2015-10-19 15:56:24,875 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.53341997
2015-10-19 15:56:25,235 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.8238434
2015-10-19 15:56:25,235 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:25,688 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.86077154
2015-10-19 15:56:25,985 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.033333335
2015-10-19 15:56:26,282 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.7779373
2015-10-19 15:56:26,282 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:26,516 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.667
2015-10-19 15:56:26,735 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.6955297
2015-10-19 15:56:26,844 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.86064523
2015-10-19 15:56:27,094 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.444952
2015-10-19 15:56:27,141 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.6594681
2015-10-19 15:56:27,203 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.6594681
2015-10-19 15:56:27,282 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:27,438 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.448704
2015-10-19 15:56:28,282 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:28,328 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.53425497
2015-10-19 15:56:28,328 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.8590947
2015-10-19 15:56:28,719 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.020409824
2015-10-19 15:56:28,719 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.8954395
2015-10-19 15:56:29,157 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.033333335
2015-10-19 15:56:29,282 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:29,297 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.8113725
2015-10-19 15:56:29,407 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.53341997
2015-10-19 15:56:29,516 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.667
2015-10-19 15:56:29,750 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.73396534
2015-10-19 15:56:29,875 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.89473045
2015-10-19 15:56:30,172 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.5323719
2015-10-19 15:56:30,204 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.667
2015-10-19 15:56:30,282 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:30,469 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.448704
2015-10-19 15:56:31,282 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:31,360 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.8934127
2015-10-19 15:56:31,750 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.93016726
2015-10-19 15:56:32,219 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.033333335
2015-10-19 15:56:32,235 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.53425497
2015-10-19 15:56:32,282 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:32,329 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.8499098
2015-10-19 15:56:32,532 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.67387366
2015-10-19 15:56:32,610 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.061876956
2015-10-19 15:56:32,782 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.77715427
2015-10-19 15:56:32,938 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.92845744
2015-10-19 15:56:33,266 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.667
2015-10-19 15:56:33,266 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.53341997
2015-10-19 15:56:33,282 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:33,360 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.5323719
2015-10-19 15:56:33,500 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.448704
2015-10-19 15:56:34,282 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:34,407 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.9275603
2015-10-19 15:56:34,797 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 0.9653699
2015-10-19 15:56:35,282 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:35,407 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.9032194
2015-10-19 15:56:35,438 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.033333335
2015-10-19 15:56:35,579 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.71262
2015-10-19 15:56:35,610 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.53425497
2015-10-19 15:56:35,829 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.81686836
2015-10-19 15:56:35,891 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.080118164
2015-10-19 15:56:36,000 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.9633514
2015-10-19 15:56:36,297 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:36,313 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.667
2015-10-19 15:56:36,563 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.5122824
2015-10-19 15:56:36,625 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.5323719
2015-10-19 15:56:36,735 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.54779845
2015-10-19 15:56:37,344 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:37,516 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.9623481
2015-10-19 15:56:37,829 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 1.0
2015-10-19 15:56:37,891 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000007_0 is : 1.0
2015-10-19 15:56:37,907 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0014_m_000007_0
2015-10-19 15:56:37,907 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:56:37,907 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0014_01_000009 taskAttempt attempt_1445182159119_0014_m_000007_0
2015-10-19 15:56:37,907 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0014_m_000007_0
2015-10-19 15:56:37,907 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:56:38,047 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:56:38,047 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0014_m_000007_0
2015-10-19 15:56:38,047 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:56:38,047 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-19 15:56:38,360 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:56:38,422 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.94337463
2015-10-19 15:56:38,454 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:12 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:56:38,610 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.74464595
2015-10-19 15:56:38,719 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.033333335
2015-10-19 15:56:38,891 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.5409588
2015-10-19 15:56:38,907 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.84942484
2015-10-19 15:56:39,063 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 0.99751294
2015-10-19 15:56:39,360 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 15:56:39,360 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.726048
2015-10-19 15:56:39,407 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000005_0 is : 1.0
2015-10-19 15:56:39,454 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0014_m_000005_0
2015-10-19 15:56:39,454 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:56:39,454 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0014_01_000007 taskAttempt attempt_1445182159119_0014_m_000005_0
2015-10-19 15:56:39,454 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0014_m_000005_0
2015-10-19 15:56:39,454 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:56:39,501 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0014_01_000009
2015-10-19 15:56:39,501 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:2 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:56:39,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0014_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:56:39,672 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.53425497
2015-10-19 15:56:39,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:56:39,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0014_m_000005_0
2015-10-19 15:56:39,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:56:39,719 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-19 15:56:39,985 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.5323719
2015-10-19 15:56:40,376 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 15:56:40,532 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:56:40,610 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 0.9964376
2015-10-19 15:56:40,735 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.0882594
2015-10-19 15:56:40,813 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.5702717
2015-10-19 15:56:41,016 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000006_0 is : 1.0
2015-10-19 15:56:41,079 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0014_m_000006_0
2015-10-19 15:56:41,079 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:56:41,079 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0014_01_000008 taskAttempt attempt_1445182159119_0014_m_000006_0
2015-10-19 15:56:41,079 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0014_m_000006_0
2015-10-19 15:56:41,079 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:56:41,360 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:56:41,360 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0014_m_000006_0
2015-10-19 15:56:41,360 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:56:41,360 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-19 15:56:41,391 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:56:41,469 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 0.9812207
2015-10-19 15:56:41,626 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:56:41,704 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0014_01_000007
2015-10-19 15:56:41,704 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:56:41,704 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0014_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:56:41,719 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.78066516
2015-10-19 15:56:41,969 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.033333335
2015-10-19 15:56:41,985 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.8867593
2015-10-19 15:56:42,407 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 15:56:42,454 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.8008887
2015-10-19 15:56:42,626 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.5481231
2015-10-19 15:56:42,751 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.53425497
2015-10-19 15:56:42,751 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0014_01_000008
2015-10-19 15:56:42,751 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:56:42,751 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0014_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:56:43,157 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000003_0 is : 1.0
2015-10-19 15:56:43,172 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0014_m_000003_0
2015-10-19 15:56:43,188 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:56:43,188 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0014_01_000005 taskAttempt attempt_1445182159119_0014_m_000003_0
2015-10-19 15:56:43,188 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0014_m_000003_0
2015-10-19 15:56:43,188 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:56:43,219 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.5323719
2015-10-19 15:56:43,344 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:56:43,344 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0014_m_000003_0
2015-10-19 15:56:43,344 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:56:43,344 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-19 15:56:43,422 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 15:56:43,751 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:56:44,422 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:56:44,797 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.8135763
2015-10-19 15:56:44,844 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0014_01_000005
2015-10-19 15:56:44,844 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:56:44,844 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0014_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:56:45,048 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.033333335
2015-10-19 15:56:45,048 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.92120725
2015-10-19 15:56:45,423 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:56:45,485 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.10635664
2015-10-19 15:56:45,501 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.87787783
2015-10-19 15:56:45,829 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.5472694
2015-10-19 15:56:45,891 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.5953465
2015-10-19 15:56:46,298 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.5323719
2015-10-19 15:56:46,423 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:56:46,516 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.55300987
2015-10-19 15:56:47,423 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:56:47,876 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.86830115
2015-10-19 15:56:48,126 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 0.9783639
2015-10-19 15:56:48,141 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.033333335
2015-10-19 15:56:48,423 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:56:48,610 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 0.99266464
2015-10-19 15:56:48,938 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.60944855
2015-10-19 15:56:48,954 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_1 is : 1.0
2015-10-19 15:56:49,016 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0014_m_000009_1
2015-10-19 15:56:49,016 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000009_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:56:49,016 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0014_01_000015 taskAttempt attempt_1445182159119_0014_m_000009_1
2015-10-19 15:56:49,016 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0014_m_000009_1
2015-10-19 15:56:49,016 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:56:49,423 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000009_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:56:49,423 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0014_m_000009_1
2015-10-19 15:56:49,423 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0014_m_000009_0
2015-10-19 15:56:49,423 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:56:49,423 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-19 15:56:49,423 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000009_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 15:56:49,423 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0014_01_000011 taskAttempt attempt_1445182159119_0014_m_000009_0
2015-10-19 15:56:49,423 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0014_m_000009_0
2015-10-19 15:56:49,423 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:56:49,438 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:56:49,454 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.10635664
2015-10-19 15:56:49,469 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000009_0 is : 0.5323719
2015-10-19 15:56:49,516 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000009_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 15:56:49,673 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:56:49,673 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.61065584
2015-10-19 15:56:49,844 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/1/_temporary/attempt_1445182159119_0014_m_000009_0
2015-10-19 15:56:49,844 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000009_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 15:56:49,860 INFO [Socket Reader #1 for port 51086] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 51086: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 15:56:50,048 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000004_0 is : 1.0
2015-10-19 15:56:50,126 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:56:50,188 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0014_m_000004_0
2015-10-19 15:56:50,188 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:56:50,188 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0014_01_000006 taskAttempt attempt_1445182159119_0014_m_000004_0
2015-10-19 15:56:50,188 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0014_m_000004_0
2015-10-19 15:56:50,188 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:56:50,344 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.5621275
2015-10-19 15:56:50,391 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:56:50,391 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0014_m_000004_0
2015-10-19 15:56:50,391 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:56:50,391 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-19 15:56:50,454 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:56:50,969 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.9077276
2015-10-19 15:56:51,204 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:56:51,251 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.033333335
2015-10-19 15:56:51,266 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0014_01_000015
2015-10-19 15:56:51,266 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0014_01_000011
2015-10-19 15:56:51,266 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0014_m_000009_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:56:51,266 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:56:51,266 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0014_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:56:51,454 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:56:52,016 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.6197233
2015-10-19 15:56:52,345 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0014_01_000006
2015-10-19 15:56:52,345 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:56:52,345 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0014_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:56:52,454 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:56:52,923 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.10635664
2015-10-19 15:56:53,173 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.61898744
2015-10-19 15:56:53,454 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:56:53,891 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.5728763
2015-10-19 15:56:54,032 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 0.9584501
2015-10-19 15:56:54,376 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.033333335
2015-10-19 15:56:54,454 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:56:55,095 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.6197233
2015-10-19 15:56:55,470 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:56:56,329 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000008_0 is : 1.0
2015-10-19 15:56:56,376 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0014_m_000008_0
2015-10-19 15:56:56,376 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:56:56,376 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0014_01_000010 taskAttempt attempt_1445182159119_0014_m_000008_0
2015-10-19 15:56:56,376 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0014_m_000008_0
2015-10-19 15:56:56,376 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:56:56,407 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.10635664
2015-10-19 15:56:56,470 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:56:56,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:56:56,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0014_m_000008_0
2015-10-19 15:56:56,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:56:56,548 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-19 15:56:56,563 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:5 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:56:56,673 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.61898744
2015-10-19 15:56:57,438 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.033333335
2015-10-19 15:56:57,470 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:56:57,470 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.5862297
2015-10-19 15:56:57,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0014_01_000010
2015-10-19 15:56:57,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:56:57,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0014_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:56:58,173 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.6197233
2015-10-19 15:56:58,470 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:56:59,470 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:00,032 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.10635664
2015-10-19 15:57:00,407 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.61898744
2015-10-19 15:57:00,470 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:00,501 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.033333335
2015-10-19 15:57:01,251 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.6579765
2015-10-19 15:57:01,360 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.607725
2015-10-19 15:57:01,470 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:01,642 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.6579765
2015-10-19 15:57:02,470 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:03,470 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:03,548 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.06666667
2015-10-19 15:57:03,657 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.10635664
2015-10-19 15:57:04,032 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.61898744
2015-10-19 15:57:04,313 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.667
2015-10-19 15:57:04,470 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:05,470 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:05,548 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.6197233
2015-10-19 15:57:06,470 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:06,579 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.06666667
2015-10-19 15:57:07,376 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.667
2015-10-19 15:57:07,470 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:07,970 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.61898744
2015-10-19 15:57:08,032 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.10635664
2015-10-19 15:57:08,470 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:09,470 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:09,626 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.10000001
2015-10-19 15:57:09,735 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.6197233
2015-10-19 15:57:10,454 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.667
2015-10-19 15:57:10,470 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:11,470 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:11,642 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.10877808
2015-10-19 15:57:11,767 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.61898744
2015-10-19 15:57:12,470 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:12,673 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.10000001
2015-10-19 15:57:13,470 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:13,532 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.69156057
2015-10-19 15:57:13,876 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.6197233
2015-10-19 15:57:14,470 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:15,470 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:15,720 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.11528893
2015-10-19 15:57:15,720 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.10000001
2015-10-19 15:57:15,798 INFO [IPC Server handler 20 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.61898744
2015-10-19 15:57:16,470 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:16,579 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.7272665
2015-10-19 15:57:17,423 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.6197233
2015-10-19 15:57:17,470 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:18,470 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:18,814 INFO [IPC Server handler 18 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.10000001
2015-10-19 15:57:19,564 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:19,564 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.12310692
2015-10-19 15:57:19,626 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.76267016
2015-10-19 15:57:19,845 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.6324772
2015-10-19 15:57:20,564 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:21,564 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:57:21,876 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.10000001
2015-10-19 15:57:21,876 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_0 is : 0.10000001
2015-10-19 15:57:21,908 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.6197233
2015-10-19 15:57:21,954 FATAL [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Task: attempt_1445182159119_0014_r_000000_0 - exited : org.apache.hadoop.mapreduce.task.reduce.Shuffle$ShuffleError: error in shuffle in fetcher#1
	at org.apache.hadoop.mapreduce.task.reduce.Shuffle.run(Shuffle.java:134)
	at org.apache.hadoop.mapred.ReduceTask.run(ReduceTask.java:376)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
Caused by: org.apache.hadoop.fs.FSError: java.io.IOException: There is not enough space on the disk
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:248)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.write(BufferedOutputStream.java:126)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.write(FSDataOutputStream.java:58)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.fs.ChecksumFileSystem$ChecksumFSOutputSummer.writeChunk(ChecksumFileSystem.java:414)
	at org.apache.hadoop.fs.FSOutputSummer.writeChecksumChunks(FSOutputSummer.java:206)
	at org.apache.hadoop.fs.FSOutputSummer.write1(FSOutputSummer.java:124)
	at org.apache.hadoop.fs.FSOutputSummer.write(FSOutputSummer.java:110)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.write(FSDataOutputStream.java:58)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput.shuffle(OnDiskMapOutput.java:103)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.copyMapOutput(Fetcher.java:534)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.copyFromHost(Fetcher.java:329)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.run(Fetcher.java:193)
Caused by: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	... 14 more

2015-10-19 15:57:21,954 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Diagnostics report from attempt_1445182159119_0014_r_000000_0: Error: org.apache.hadoop.mapreduce.task.reduce.Shuffle$ShuffleError: error in shuffle in fetcher#1
	at org.apache.hadoop.mapreduce.task.reduce.Shuffle.run(Shuffle.java:134)
	at org.apache.hadoop.mapred.ReduceTask.run(ReduceTask.java:376)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
Caused by: org.apache.hadoop.fs.FSError: java.io.IOException: There is not enough space on the disk
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:248)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.write(BufferedOutputStream.java:126)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.write(FSDataOutputStream.java:58)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.fs.ChecksumFileSystem$ChecksumFSOutputSummer.writeChunk(ChecksumFileSystem.java:414)
	at org.apache.hadoop.fs.FSOutputSummer.writeChecksumChunks(FSOutputSummer.java:206)
	at org.apache.hadoop.fs.FSOutputSummer.write1(FSOutputSummer.java:124)
	at org.apache.hadoop.fs.FSOutputSummer.write(FSOutputSummer.java:110)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.write(FSDataOutputStream.java:58)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput.shuffle(OnDiskMapOutput.java:103)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.copyMapOutput(Fetcher.java:534)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.copyFromHost(Fetcher.java:329)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.run(Fetcher.java:193)
Caused by: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	... 14 more

2015-10-19 15:57:21,954 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0014_r_000000_0: Error: org.apache.hadoop.mapreduce.task.reduce.Shuffle$ShuffleError: error in shuffle in fetcher#1
	at org.apache.hadoop.mapreduce.task.reduce.Shuffle.run(Shuffle.java:134)
	at org.apache.hadoop.mapred.ReduceTask.run(ReduceTask.java:376)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
Caused by: org.apache.hadoop.fs.FSError: java.io.IOException: There is not enough space on the disk
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:248)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.write(BufferedOutputStream.java:126)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.write(FSDataOutputStream.java:58)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.fs.ChecksumFileSystem$ChecksumFSOutputSummer.writeChunk(ChecksumFileSystem.java:414)
	at org.apache.hadoop.fs.FSOutputSummer.writeChecksumChunks(FSOutputSummer.java:206)
	at org.apache.hadoop.fs.FSOutputSummer.write1(FSOutputSummer.java:124)
	at org.apache.hadoop.fs.FSOutputSummer.write(FSOutputSummer.java:110)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.write(FSDataOutputStream.java:58)
	at java.io.DataOutputStream.write(DataOutputStream.java:107)
	at org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput.shuffle(OnDiskMapOutput.java:103)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.copyMapOutput(Fetcher.java:534)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.copyFromHost(Fetcher.java:329)
	at org.apache.hadoop.mapreduce.task.reduce.Fetcher.run(Fetcher.java:193)
Caused by: java.io.IOException: There is not enough space on the disk
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:345)
	at org.apache.hadoop.fs.RawLocalFileSystem$LocalFSFileOutputStream.write(RawLocalFileSystem.java:246)
	... 14 more

2015-10-19 15:57:21,954 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_r_000000_0 TaskAttempt Transitioned from RUNNING to FAIL_CONTAINER_CLEANUP
2015-10-19 15:57:21,954 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0014_01_000012 taskAttempt attempt_1445182159119_0014_r_000000_0
2015-10-19 15:57:21,954 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0014_r_000000_0
2015-10-19 15:57:21,954 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:57:21,986 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_r_000000_0 TaskAttempt Transitioned from FAIL_CONTAINER_CLEANUP to FAIL_TASK_CLEANUP
2015-10-19 15:57:21,986 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:57:22,001 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/1/_temporary/attempt_1445182159119_0014_r_000000_0
2015-10-19 15:57:22,001 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_r_000000_0 TaskAttempt Transitioned from FAIL_TASK_CLEANUP to FAILED
2015-10-19 15:57:22,017 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_r_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:57:22,017 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: 1 failures on node 04DN8IQ.fareast.corp.microsoft.com
2015-10-19 15:57:22,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:57:22,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:8192, vCores:-19>
2015-10-19 15:57:22,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-19 15:57:22,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:57:22,658 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.798494
2015-10-19 15:57:23,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=1 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:9216, vCores:-18> knownNMs=4
2015-10-19 15:57:23,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0014_01_000012
2015-10-19 15:57:23,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:4 AssignedReds:0 CompletedMaps:8 CompletedReds:0 ContAlloc:14 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:57:23,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0014_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:57:23,626 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.14199789
2015-10-19 15:57:23,861 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.65038985
2015-10-19 15:57:24,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:57:24,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-19 15:57:24,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0014_01_000016 to attempt_1445182159119_0014_r_000000_1
2015-10-19 15:57:24,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:8 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:57:24,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:57:24,564 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_r_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:57:24,564 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0014_01_000016 taskAttempt attempt_1445182159119_0014_r_000000_1
2015-10-19 15:57:24,564 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0014_r_000000_1
2015-10-19 15:57:24,564 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:57:24,595 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0014_r_000000_1 : 13562
2015-10-19 15:57:24,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0014_r_000000_1] using containerId: [container_1445182159119_0014_01_000016 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:57:24,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_r_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:57:24,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0014_r_000000
2015-10-19 15:57:25,564 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0014: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:9216, vCores:-18> knownNMs=4
2015-10-19 15:57:25,673 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.8286716
2015-10-19 15:57:25,751 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.6197233
2015-10-19 15:57:27,033 INFO [Socket Reader #1 for port 51086] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0014 (auth:SIMPLE)
2015-10-19 15:57:27,048 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0014_r_000016 asked for a task
2015-10-19 15:57:27,048 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0014_r_000016 given task: attempt_1445182159119_0014_r_000000_1
2015-10-19 15:57:27,892 INFO [IPC Server handler 4 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.16019093
2015-10-19 15:57:27,892 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.667
2015-10-19 15:57:27,892 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.667
2015-10-19 15:57:28,579 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 0 maxEvents 10000
2015-10-19 15:57:28,704 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.8652473
2015-10-19 15:57:29,580 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:30,580 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:30,814 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.6197233
2015-10-19 15:57:31,580 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:31,736 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.8880809
2015-10-19 15:57:32,580 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:32,830 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.1877468
2015-10-19 15:57:32,830 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.667
2015-10-19 15:57:33,580 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:34,517 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.26666668
2015-10-19 15:57:34,595 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:34,767 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.920566
2015-10-19 15:57:35,002 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.6197233
2015-10-19 15:57:35,595 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:36,595 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:37,095 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.19158794
2015-10-19 15:57:37,236 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.667
2015-10-19 15:57:37,548 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.26666668
2015-10-19 15:57:37,611 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:37,814 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.9481112
2015-10-19 15:57:38,611 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:39,095 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.65955347
2015-10-19 15:57:39,908 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:40,423 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.65955347
2015-10-19 15:57:40,564 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.26666668
2015-10-19 15:57:40,830 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 0.97409177
2015-10-19 15:57:40,908 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:41,674 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.667
2015-10-19 15:57:41,736 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.19158794
2015-10-19 15:57:41,908 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:42,908 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:43,111 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_1 is : 1.0
2015-10-19 15:57:43,111 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0014_m_000001_1
2015-10-19 15:57:43,111 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000001_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:57:43,111 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0014_01_000013 taskAttempt attempt_1445182159119_0014_m_000001_1
2015-10-19 15:57:43,111 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0014_m_000001_1
2015-10-19 15:57:43,111 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:57:43,127 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000001_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:57:43,127 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0014_m_000001_1
2015-10-19 15:57:43,127 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0014_m_000001_0
2015-10-19 15:57:43,127 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0014_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:57:43,127 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-19 15:57:43,127 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000001_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 15:57:43,127 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0014_01_000003 taskAttempt attempt_1445182159119_0014_m_000001_0
2015-10-19 15:57:43,127 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0014_m_000001_0
2015-10-19 15:57:43,142 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:57:43,580 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.26666668
2015-10-19 15:57:43,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:57:43,642 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000001_0 is : 0.667
2015-10-19 15:57:43,783 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0014_m_000001
2015-10-19 15:57:43,783 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:57:43,908 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 8 maxEvents 10000
2015-10-19 15:57:44,220 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000001_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 15:57:44,220 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:57:44,236 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out4/_temporary/1/_temporary/attempt_1445182159119_0014_m_000001_0
2015-10-19 15:57:44,236 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0014_m_000001_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 15:57:44,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0014_01_000013
2015-10-19 15:57:44,595 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:57:44,595 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0014_m_000001_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:57:44,908 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:45,908 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:46,564 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.19158794
2015-10-19 15:57:46,564 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.667
2015-10-19 15:57:46,596 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:57:46,908 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:47,033 INFO [Socket Reader #1 for port 51086] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 51086: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 15:57:47,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0014_01_000003
2015-10-19 15:57:47,611 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:0 HostLocal:9 RackLocal:4
2015-10-19 15:57:47,611 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0014_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:57:47,908 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:48,908 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:49,611 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:57:49,908 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:50,892 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.19158794
2015-10-19 15:57:50,908 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:50,986 INFO [IPC Server handler 27 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.667
2015-10-19 15:57:51,908 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:52,611 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:57:52,908 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:53,908 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:54,908 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:55,236 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.667
2015-10-19 15:57:55,252 INFO [IPC Server handler 26 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.19158794
2015-10-19 15:57:55,611 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:57:55,908 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:56,908 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:57,924 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:58,643 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:57:58,939 INFO [IPC Server handler 16 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:57:59,236 INFO [IPC Server handler 6 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.667
2015-10-19 15:57:59,330 INFO [IPC Server handler 10 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.19158794
2015-10-19 15:57:59,955 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:00,955 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:01,658 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:01,955 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:02,955 INFO [IPC Server handler 23 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:03,111 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.19158794
2015-10-19 15:58:03,143 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.667
2015-10-19 15:58:03,955 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:04,674 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:04,955 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:05,955 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:06,955 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:07,143 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.67640436
2015-10-19 15:58:07,627 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.19158794
2015-10-19 15:58:07,690 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:07,955 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:08,955 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:09,955 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:10,705 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:10,955 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:11,268 INFO [IPC Server handler 22 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.69059074
2015-10-19 15:58:11,705 INFO [IPC Server handler 21 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.1996438
2015-10-19 15:58:11,955 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:12,955 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:13,752 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:13,971 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:14,971 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:15,408 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.70463574
2015-10-19 15:58:15,518 INFO [IPC Server handler 7 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.22765239
2015-10-19 15:58:16,283 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:16,768 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:17,283 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:18,283 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:19,283 INFO [IPC Server handler 8 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:19,768 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:19,815 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.71932274
2015-10-19 15:58:19,940 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.24621405
2015-10-19 15:58:20,283 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:21,284 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:22,284 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:22,784 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:23,284 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:24,143 INFO [IPC Server handler 12 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.7329515
2015-10-19 15:58:24,284 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.27584878
2015-10-19 15:58:24,284 INFO [IPC Server handler 13 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:25,299 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:25,799 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:26,299 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:27,315 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:28,190 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.74779546
2015-10-19 15:58:28,315 INFO [IPC Server handler 2 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:28,409 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.27696857
2015-10-19 15:58:28,815 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:29,315 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:30,315 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:31,315 INFO [IPC Server handler 14 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:31,831 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:32,315 INFO [IPC Server handler 0 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:33,002 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.76207924
2015-10-19 15:58:33,315 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:33,409 INFO [IPC Server handler 15 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.27696857
2015-10-19 15:58:34,315 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:34,846 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:35,315 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:36,315 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:37,206 INFO [IPC Server handler 11 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.7747717
2015-10-19 15:58:37,315 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:37,659 INFO [IPC Server handler 1 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.27696857
2015-10-19 15:58:37,862 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:38,331 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:39,346 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:40,346 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:40,878 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:41,018 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.7879315
2015-10-19 15:58:41,362 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:41,737 INFO [IPC Server handler 5 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.27696857
2015-10-19 15:58:42,362 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:43,362 INFO [IPC Server handler 17 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:43,893 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:44,362 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:45,346 INFO [IPC Server handler 9 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.80107063
2015-10-19 15:58:45,362 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:45,956 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.27696857
2015-10-19 15:58:46,362 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:46,909 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:47,362 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:48,362 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:49,159 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.815365
2015-10-19 15:58:49,362 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:49,925 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:49,940 INFO [IPC Server handler 28 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.27696857
2015-10-19 15:58:50,362 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:51,362 INFO [IPC Server handler 29 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:52,393 INFO [IPC Server handler 3 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:52,925 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_r_000000_1 is : 0.3
2015-10-19 15:58:53,190 INFO [IPC Server handler 19 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_0 is : 0.8294212
2015-10-19 15:58:53,425 INFO [IPC Server handler 24 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0014_r_000000_1. startIndex 9 maxEvents 10000
2015-10-19 15:58:53,815 INFO [IPC Server handler 25 on 51086] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0014_m_000000_1 is : 0.27696857
