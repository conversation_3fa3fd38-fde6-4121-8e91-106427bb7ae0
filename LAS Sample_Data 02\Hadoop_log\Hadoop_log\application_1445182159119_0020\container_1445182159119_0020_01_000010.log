2015-10-19 17:52:53,055 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 17:52:53,368 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 17:52:53,368 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-19 17:52:53,446 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-19 17:52:53,446 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445182159119_0020, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@75225918)
2015-10-19 17:52:53,868 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-19 17:52:55,133 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445182159119_0020
2015-10-19 17:52:57,180 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-19 17:52:59,102 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-19 17:52:59,352 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@4299c84a
2015-10-19 17:53:15,852 WARN [main] org.apache.hadoop.hdfs.BlockReaderFactory: I/O error constructing remote block reader.
java.net.ConnectException: Connection timed out: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:735)
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:206)
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:530)
	at org.apache.hadoop.hdfs.DFSClient.newConnectedPeer(DFSClient.java:3101)
	at org.apache.hadoop.hdfs.BlockReaderFactory.nextTcpPeer(BlockReaderFactory.java:755)
	at org.apache.hadoop.hdfs.BlockReaderFactory.getRemoteBlockReaderFromTcp(BlockReaderFactory.java:670)
	at org.apache.hadoop.hdfs.BlockReaderFactory.build(BlockReaderFactory.java:337)
	at org.apache.hadoop.hdfs.DFSInputStream.blockSeekTo(DFSInputStream.java:576)
	at org.apache.hadoop.hdfs.DFSInputStream.readWithStrategy(DFSInputStream.java:800)
	at org.apache.hadoop.hdfs.DFSInputStream.read(DFSInputStream.java:847)
	at org.apache.hadoop.hdfs.DFSInputStream.read(DFSInputStream.java:655)
	at java.io.DataInputStream.readByte(DataInputStream.java:265)
	at org.apache.hadoop.io.WritableUtils.readVLong(WritableUtils.java:308)
	at org.apache.hadoop.io.WritableUtils.readVIntInRange(WritableUtils.java:348)
	at org.apache.hadoop.io.Text.readString(Text.java:471)
	at org.apache.hadoop.io.Text.readString(Text.java:464)
	at org.apache.hadoop.mapred.MapTask.getSplitDetails(MapTask.java:358)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:751)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
2015-10-19 17:53:15,852 WARN [main] org.apache.hadoop.hdfs.DFSClient: Failed to connect to /10.86.165.66:50010 for block, add to deadNodes and continue. java.net.ConnectException: Connection timed out: no further information
java.net.ConnectException: Connection timed out: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:735)
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:206)
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:530)
	at org.apache.hadoop.hdfs.DFSClient.newConnectedPeer(DFSClient.java:3101)
	at org.apache.hadoop.hdfs.BlockReaderFactory.nextTcpPeer(BlockReaderFactory.java:755)
	at org.apache.hadoop.hdfs.BlockReaderFactory.getRemoteBlockReaderFromTcp(BlockReaderFactory.java:670)
	at org.apache.hadoop.hdfs.BlockReaderFactory.build(BlockReaderFactory.java:337)
	at org.apache.hadoop.hdfs.DFSInputStream.blockSeekTo(DFSInputStream.java:576)
	at org.apache.hadoop.hdfs.DFSInputStream.readWithStrategy(DFSInputStream.java:800)
	at org.apache.hadoop.hdfs.DFSInputStream.read(DFSInputStream.java:847)
	at org.apache.hadoop.hdfs.DFSInputStream.read(DFSInputStream.java:655)
	at java.io.DataInputStream.readByte(DataInputStream.java:265)
	at org.apache.hadoop.io.WritableUtils.readVLong(WritableUtils.java:308)
	at org.apache.hadoop.io.WritableUtils.readVIntInRange(WritableUtils.java:348)
	at org.apache.hadoop.io.Text.readString(Text.java:471)
	at org.apache.hadoop.io.Text.readString(Text.java:464)
	at org.apache.hadoop.mapred.MapTask.getSplitDetails(MapTask.java:358)
	at org.apache.hadoop.mapred.MapTask.runNewMapper(MapTask.java:751)
	at org.apache.hadoop.mapred.MapTask.run(MapTask.java:341)
	at org.apache.hadoop.mapred.YarnChild$2.run(YarnChild.java:163)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.security.auth.Subject.doAs(Subject.java:415)
	at org.apache.hadoop.security.UserGroupInformation.doAs(UserGroupInformation.java:1628)
	at org.apache.hadoop.mapred.YarnChild.main(YarnChild.java:158)
2015-10-19 17:53:18,540 INFO [main] org.apache.hadoop.hdfs.DFSClient: Successfully connected to /10.190.173.170:50010 for BP-1347369012-10.190.173.170-1444972147527:blk_1073744042_3267
2015-10-19 17:53:18,587 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:1207959552+48562176
2015-10-19 17:53:18,884 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-19 17:53:18,884 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-19 17:53:18,884 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-19 17:53:18,884 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-19 17:53:18,884 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-19 17:53:19,102 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-19 17:53:28,009 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:53:28,009 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48193401; bufvoid = 104857600
2015-10-19 17:53:28,009 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17291232(69164928); length = 8923165/6553600
2015-10-19 17:53:28,009 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57262153 kvi 14315532(57262128)
2015-10-19 17:54:06,384 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-19 17:54:06,400 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57262153 kv 14315532(57262128) kvi 12120076(48480304)
2015-10-19 17:54:11,478 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:54:11,478 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57262153; bufend = 658166; bufvoid = 104857600
2015-10-19 17:54:11,478 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14315532(57262128); kvend = 5407424(21629696); length = 8908109/6553600
2015-10-19 17:54:11,478 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9726918 kvi 2431724(9726896)
2015-10-19 17:54:50,979 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-19 17:54:50,995 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9726918 kv 2431724(9726896) kvi 228516(914064)
2015-10-19 17:54:56,479 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-19 17:54:56,479 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:54:56,479 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9726918; bufend = 49084664; bufvoid = 104857600
2015-10-19 17:54:56,479 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2431724(9726896); kvend = 21376588(85506352); length = 7269537/6553600
2015-10-19 17:55:26,683 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-19 17:55:26,792 INFO [main] org.apache.hadoop.mapred.Merger: Merging 3 sorted segments
2015-10-19 17:55:26,855 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 3 segments left of total size: 105653983 bytes
2015-10-19 17:56:03,590 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445182159119_0020_m_000009_0 is done. And is in the process of committing
2015-10-19 17:56:03,996 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445182159119_0020_m_000009_0' done.
2015-10-19 17:56:04,105 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Stopping MapTask metrics system...
2015-10-19 17:56:04,105 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system stopped.
2015-10-19 17:56:04,105 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system shutdown complete.
