# %%
import pandas as pd
import lasio
# Plotly Imports
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.graph_objects as go

# %%
df = lasio.read("C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/LAS Data/L0509_comp.las").df()

# %%
df.reset_index(inplace=True)

# %%
df.head()

# %%
df.describe()

# %% [markdown]
# ## Plotly Graph Objects
# Low level interface for creating interactive figures, adding traces traces and creating layouts

# %%
fig = go.Figure(data=go.Scatter(x=df['DEPT'], y=df['GR']))
fig.update_layout(xaxis_title='DEPTH', yaxis_title='GR')
fig.show()

# %%


# %% [markdown]
# ## Plotly Express
# High level wrapper around plotly, that allows users to interact with plotly using easier to use syntax

# %%
fig = px.line(df, x='DEPT', y='GR')
fig.show()

# %%
fig = px.line(df, x='DEPT', y='GR', 
              range_y=[0, 150], 
              range_x=[4000,4900])
fig.show()

# %%
fig = px.line(df, x='DEPT', y='GR', 
              range_y=[0, 150], 
              range_x=[4000,4900])
fig.update_xaxes(rangeslider_visible=True)
fig.show()

# %% [markdown]
# ## Creating Log Plots with make_subplots()

# %%
fig = make_subplots(rows=1, cols=3, )
fig.add_trace(go.Scatter(x=df['GR'], y=df['DEPT']), row=1, col=1)
fig.add_trace(go.Scatter(x=df['RHOB'], y=df['DEPT']), row=1, col=2)
fig.add_trace(go.Scatter(x=df['DT'], y=df['DEPT']), row=1, col=3)



fig.show()

# %%
fig = make_subplots(rows=1, cols=3, shared_yaxes=True)
fig.add_trace(go.Scatter(x=df['GR'], y=df['DEPT']), row=1, col=1)
fig.add_trace(go.Scatter(x=df['RHOB'], y=df['DEPT']), row=1, col=2)
fig.add_trace(go.Scatter(x=df['DT'], y=df['DEPT']), row=1, col=3)

fig.update_xaxes(title_text="GR", row=1, col=1)
fig.update_xaxes(title_text="RHOB", row=1, col=2)
fig.update_xaxes(title_text="DTC", row=1, col=3)


fig.update_yaxes(title_text="Depth", row=1, col=1, autorange='reversed')

fig.update_layout(title_text="Interactive Plot", height=900)

fig.show()

# %%
fig = make_subplots(rows=1, cols=3, shared_yaxes=True)
fig.add_trace(go.Scatter(x=df['GR'], y=df['DEPT'], fill='tozerox', fillcolor='green'), row=1, col=1)
fig.add_trace(go.Scatter(x=df['RHOB'], y=df['DEPT']), row=1, col=2)
fig.add_trace(go.Scatter(x=df['DT'], y=df['DEPT']), row=1, col=3)

fig.update_xaxes(title_text="GR", row=1, col=1)
fig.update_xaxes(title_text="RHOB", row=1, col=2)
fig.update_xaxes(title_text="DTC", row=1, col=3)


fig.update_yaxes(title_text="Depth", row=1, col=1, autorange='reversed')

fig.update_layout(title_text="Interactive Plot", height=900)

fig.show()

# %%


# %%


# %%



