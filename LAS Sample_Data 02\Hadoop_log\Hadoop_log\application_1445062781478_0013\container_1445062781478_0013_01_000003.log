2015-10-17 15:38:09,098 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 15:38:09,223 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 15:38:09,223 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 15:38:09,255 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 15:38:09,255 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445062781478_0013, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@1623b78d)
2015-10-17 15:38:09,411 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 15:38:10,052 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445062781478_0013
2015-10-17 15:38:10,473 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 15:38:11,239 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 15:38:11,255 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@db57326
2015-10-17 15:38:11,630 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:134217728+134217728
2015-10-17 15:38:11,723 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 15:38:11,723 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 15:38:11,723 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 15:38:11,723 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 15:38:11,723 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 15:38:11,739 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 15:38:16,583 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:38:16,583 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48254386; bufvoid = 104857600
2015-10-17 15:38:16,583 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17306472(69225888); length = 8907925/6553600
2015-10-17 15:38:16,583 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57323122 kvi 14330776(57323104)
2015-10-17 15:38:28,240 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 15:38:28,240 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57323122 kv 14330776(57323104) kvi 12127800(48511200)
2015-10-17 15:38:44,319 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:38:44,319 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57323122; bufend = 699496; bufvoid = 104857600
2015-10-17 15:38:44,319 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14330776(57323104); kvend = 5417752(21671008); length = 8913025/6553600
2015-10-17 15:38:44,319 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9768248 kvi 2442056(9768224)
2015-10-17 15:38:54,819 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 15:38:54,835 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9768248 kv 2442056(9768224) kvi 241544(966176)
2015-10-17 15:38:59,085 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:38:59,085 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9768248; bufend = 57981481; bufvoid = 104857600
2015-10-17 15:38:59,085 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2442056(9768224); kvend = 19738252(78953008); length = 8918205/6553600
2015-10-17 15:38:59,085 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67050233 kvi 16762552(67050208)
2015-10-17 15:39:09,835 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 15:39:09,835 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67050233 kv 16762552(67050208) kvi 14554320(58217280)
2015-10-17 15:39:16,914 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:39:16,914 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67050233; bufend = 10441503; bufvoid = 104857600
2015-10-17 15:39:16,914 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16762552(67050208); kvend = 7853256(31413024); length = 8909297/6553600
2015-10-17 15:39:16,914 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19510255 kvi 4877556(19510224)
2015-10-17 15:39:28,761 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 15:39:28,777 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 19510255 kv 4877556(19510224) kvi 2674180(10696720)
2015-10-17 15:39:39,621 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:39:39,621 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 19510255; bufend = 67733940; bufvoid = 104857600
2015-10-17 15:39:39,621 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 4877556(19510224); kvend = 22176360(88705440); length = 8915597/6553600
2015-10-17 15:39:39,621 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 76802676 kvi 19200664(76802656)
2015-10-17 15:39:51,090 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 15:39:51,090 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 76802676 kv 19200664(76802656) kvi 17001428(68005712)
2015-10-17 15:39:54,153 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:39:54,153 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 76802676; bufend = 20189504; bufvoid = 104857600
2015-10-17 15:39:54,153 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 19200664(76802656); kvend = 10290256(41161024); length = 8910409/6553600
2015-10-17 15:39:54,153 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29258256 kvi 7314560(29258240)
2015-10-17 15:40:04,340 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-17 15:40:04,340 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29258256 kv 7314560(29258240) kvi 5109580(20438320)
2015-10-17 15:40:10,231 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:40:10,231 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29258256; bufend = 77515779; bufvoid = 104857600
2015-10-17 15:40:10,231 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7314560(29258240); kvend = 24621820(98487280); length = 8907141/6553600
2015-10-17 15:40:10,231 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 86584515 kvi 21646124(86584496)
2015-10-17 15:40:20,404 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-17 15:40:20,404 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 86584515 kv 21646124(86584496) kvi 19440748(77762992)
2015-10-17 15:40:23,966 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-17 15:40:23,966 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 15:40:23,966 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 86584515; bufend = 20318567; bufvoid = 104857600
2015-10-17 15:40:23,966 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 21646124(86584496); kvend = 14513012(58052048); length = 7133113/6553600
2015-10-17 15:40:31,935 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 7
2015-10-17 15:40:31,967 INFO [main] org.apache.hadoop.mapred.Merger: Merging 8 sorted segments
2015-10-17 15:40:31,982 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 8 segments left of total size: 288817234 bytes
2015-10-17 15:40:48,327 INFO [communication thread] org.apache.hadoop.mapred.Task: Communication exception: java.io.IOException: Failed on local exception: java.io.IOException: An existing connection was forcibly closed by the remote host; Host Details : local host is: "MSRA-SA-39/**************"; destination host is: "04dn8iq.fareast.corp.microsoft.com":49470; 
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:772)
	at org.apache.hadoop.ipc.Client.call(Client.java:1472)
	at org.apache.hadoop.ipc.Client.call(Client.java:1399)
	at org.apache.hadoop.ipc.WritableRpcEngine$Invoker.invoke(WritableRpcEngine.java:244)
	at com.sun.proxy.$Proxy8.statusUpdate(Unknown Source)
	at org.apache.hadoop.mapred.Task$TaskReporter.run(Task.java:737)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at org.apache.hadoop.net.SocketInputStream$Reader.performIO(SocketInputStream.java:57)
	at org.apache.hadoop.net.SocketIOWithTimeout.doIO(SocketIOWithTimeout.java:142)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:161)
	at org.apache.hadoop.net.SocketInputStream.read(SocketInputStream.java:131)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at org.apache.hadoop.ipc.Client$Connection$PingInputStream.read(Client.java:513)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:246)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:265)
	at java.io.DataInputStream.readInt(DataInputStream.java:387)
	at org.apache.hadoop.ipc.Client$Connection.receiveRpcResponse(Client.java:1071)
	at org.apache.hadoop.ipc.Client$Connection.run(Client.java:966)

2015-10-17 15:40:55,268 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445062781478_0013_m_000001_0 is done. And is in the process of committing
2015-10-17 15:41:11,347 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 0 time(s); maxRetries=45
2015-10-17 15:41:31,352 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 1 time(s); maxRetries=45
2015-10-17 15:41:51,356 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 2 time(s); maxRetries=45
2015-10-17 15:42:11,357 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 3 time(s); maxRetries=45
2015-10-17 15:42:31,358 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 4 time(s); maxRetries=45
2015-10-17 15:42:51,364 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 5 time(s); maxRetries=45
2015-10-17 15:43:11,365 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 6 time(s); maxRetries=45
2015-10-17 15:43:31,369 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 7 time(s); maxRetries=45
2015-10-17 15:43:51,374 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 8 time(s); maxRetries=45
2015-10-17 15:44:11,375 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 9 time(s); maxRetries=45
2015-10-17 15:44:31,375 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 10 time(s); maxRetries=45
2015-10-17 15:44:51,376 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 11 time(s); maxRetries=45
2015-10-17 15:45:11,383 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 12 time(s); maxRetries=45
2015-10-17 15:45:31,384 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 13 time(s); maxRetries=45
2015-10-17 15:45:51,389 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 14 time(s); maxRetries=45
2015-10-17 15:46:11,390 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 15 time(s); maxRetries=45
2015-10-17 15:46:31,391 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 16 time(s); maxRetries=45
2015-10-17 15:46:51,395 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 17 time(s); maxRetries=45
2015-10-17 15:47:11,396 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 18 time(s); maxRetries=45
2015-10-17 15:47:31,400 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 19 time(s); maxRetries=45
2015-10-17 15:47:51,401 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 20 time(s); maxRetries=45
2015-10-17 15:48:11,402 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 21 time(s); maxRetries=45
2015-10-17 15:48:31,405 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 22 time(s); maxRetries=45
2015-10-17 15:48:51,406 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 23 time(s); maxRetries=45
2015-10-17 15:49:11,412 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 24 time(s); maxRetries=45
2015-10-17 15:49:31,415 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 25 time(s); maxRetries=45
2015-10-17 15:49:51,416 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 26 time(s); maxRetries=45
2015-10-17 15:50:11,417 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 27 time(s); maxRetries=45
2015-10-17 15:50:31,420 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 28 time(s); maxRetries=45
2015-10-17 15:50:51,422 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 29 time(s); maxRetries=45
2015-10-17 15:51:11,436 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 30 time(s); maxRetries=45
2015-10-17 15:51:31,437 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 31 time(s); maxRetries=45
2015-10-17 15:51:51,438 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 32 time(s); maxRetries=45
2015-10-17 15:52:11,439 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 33 time(s); maxRetries=45
2015-10-17 15:52:31,440 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 34 time(s); maxRetries=45
2015-10-17 15:52:51,441 INFO [communication thread] org.apache.hadoop.ipc.Client: Retrying connect to server: 04dn8iq.fareast.corp.microsoft.com/***********:49470. Already tried 35 time(s); maxRetries=45
