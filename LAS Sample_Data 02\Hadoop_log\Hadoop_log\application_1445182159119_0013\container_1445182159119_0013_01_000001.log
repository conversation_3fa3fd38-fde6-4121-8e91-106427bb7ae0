2015-10-19 15:49:52,156 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Created MRAppMaster for application appattempt_1445182159119_0013_000001
2015-10-19 15:49:53,019 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Executing with tokens:
2015-10-19 15:49:53,020 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Kind: YARN_AM_RM_TOKEN, Service: , Ident: (appAttemptId { application_id { id: 13 cluster_timestamp: 1445182159119 } attemptId: 1 } keyId: 1694045684)
2015-10-19 15:49:53,325 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Using mapred newApiCommitter.
2015-10-19 15:49:54,508 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter set in config null
2015-10-19 15:49:54,626 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: OutputCommitter is org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter
2015-10-19 15:49:54,689 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.jobhistory.EventType for class org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler
2015-10-19 15:49:54,692 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobEventDispatcher
2015-10-19 15:49:54,695 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskEventDispatcher
2015-10-19 15:49:54,697 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.TaskAttemptEventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$TaskAttemptEventDispatcher
2015-10-19 15:49:54,698 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventType for class org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler
2015-10-19 15:49:54,713 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.speculate.Speculator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$SpeculatorEventDispatcher
2015-10-19 15:49:54,714 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.rm.ContainerAllocator$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerAllocatorRouter
2015-10-19 15:49:54,717 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncher$EventType for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$ContainerLauncherRouter
2015-10-19 15:49:54,807 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 15:49:54,858 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 15:49:54,907 INFO [main] org.apache.hadoop.mapreduce.v2.jobhistory.JobHistoryUtils: Default file system [hdfs://msra-sa-41:9000]
2015-10-19 15:49:54,929 INFO [main] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Emitting job history data to the timeline server is not enabled
2015-10-19 15:49:55,035 INFO [main] org.apache.hadoop.yarn.event.AsyncDispatcher: Registering class org.apache.hadoop.mapreduce.v2.app.job.event.JobFinishEvent$Type for class org.apache.hadoop.mapreduce.v2.app.MRAppMaster$JobFinishEventHandler
2015-10-19 15:49:55,588 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 15:49:55,698 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 15:49:55,698 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MRAppMaster metrics system started
2015-10-19 15:49:55,713 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Adding job token for job_1445182159119_0013 to jobTokenSecretManager
2015-10-19 15:49:56,031 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Not uberizing job_1445182159119_0013 because: not enabled; too many maps; too much input;
2015-10-19 15:49:56,074 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Input size for job job_1445182159119_0013 = 1256521728. Number of splits = 10
2015-10-19 15:49:56,077 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Number of reduces for job job_1445182159119_0013 = 1
2015-10-19 15:49:56,077 INFO [main] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0013Job Transitioned from NEW to INITED
2015-10-19 15:49:56,080 INFO [main] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: MRAppMaster launching normal, non-uberized, multi-container job job_1445182159119_0013.
2015-10-19 15:49:56,165 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 15:49:56,190 INFO [Socket Reader #1 for port 17452] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 17452
2015-10-19 15:49:56,249 INFO [main] org.apache.hadoop.yarn.factories.impl.pb.RpcServerFactoryPBImpl: Adding protocol org.apache.hadoop.mapreduce.v2.api.MRClientProtocolPB to the server
2015-10-19 15:49:56,250 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 15:49:56,250 INFO [IPC Server listener on 17452] org.apache.hadoop.ipc.Server: IPC Server listener on 17452: starting
2015-10-19 15:49:56,252 INFO [main] org.apache.hadoop.mapreduce.v2.app.client.MRClientService: Instantiated MRClientService at MSRA-SA-41.fareast.corp.microsoft.com/**************:17452
2015-10-19 15:49:56,464 INFO [main] org.mortbay.log: Logging to org.slf4j.impl.Log4jLoggerAdapter(org.mortbay.log) via org.mortbay.log.Slf4jLog
2015-10-19 15:49:56,473 INFO [main] org.apache.hadoop.http.HttpRequestLog: Http request log for http.requests.mapreduce is not defined
2015-10-19 15:49:56,497 INFO [main] org.apache.hadoop.http.HttpServer2: Added global filter 'safety' (class=org.apache.hadoop.http.HttpServer2$QuotingInputFilter)
2015-10-19 15:49:56,508 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context mapreduce
2015-10-19 15:49:56,508 INFO [main] org.apache.hadoop.http.HttpServer2: Added filter AM_PROXY_FILTER (class=org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter) to context static
2015-10-19 15:49:56,515 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /mapreduce/*
2015-10-19 15:49:56,516 INFO [main] org.apache.hadoop.http.HttpServer2: adding path spec: /ws/*
2015-10-19 15:49:56,537 INFO [main] org.apache.hadoop.http.HttpServer2: Jetty bound to port 17459
2015-10-19 15:49:56,538 INFO [main] org.mortbay.log: jetty-6.1.26
2015-10-19 15:49:56,620 INFO [main] org.mortbay.log: Extract jar:file:/D:/hadoop-2.6.0-localbox/share/hadoop/yarn/hadoop-yarn-common-2.6.0-SNAPSHOT.jar!/webapps/mapreduce to C:\Users\<USER>\AppData\Local\Temp\2\Jetty_0_0_0_0_17459_mapreduce____.ew4oop\webapp
2015-10-19 15:49:56,971 INFO [main] org.mortbay.log: Started HttpServer2$SelectChannelConnectorWithSafeStartup@0.0.0.0:17459
2015-10-19 15:49:56,972 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Web app /mapreduce started at 17459
2015-10-19 15:49:57,498 INFO [main] org.apache.hadoop.yarn.webapp.WebApps: Registered webapp guice modules
2015-10-19 15:49:57,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: JOB_CREATE job_1445182159119_0013
2015-10-19 15:49:57,502 INFO [main] org.apache.hadoop.ipc.CallQueueManager: Using callQueue class java.util.concurrent.LinkedBlockingQueue
2015-10-19 15:49:57,505 INFO [Socket Reader #1 for port 17464] org.apache.hadoop.ipc.Server: Starting Socket Reader #1 for port 17464
2015-10-19 15:49:57,510 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: IPC Server Responder: starting
2015-10-19 15:49:57,510 INFO [IPC Server listener on 17464] org.apache.hadoop.ipc.Server: IPC Server listener on 17464: starting
2015-10-19 15:49:57,526 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: nodeBlacklistingEnabled:true
2015-10-19 15:49:57,527 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: maxTaskFailuresPerNode is 3
2015-10-19 15:49:57,527 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: blacklistDisablePercent is 33
2015-10-19 15:49:57,571 INFO [main] org.apache.hadoop.yarn.client.RMProxy: Connecting to ResourceManager at MSRA-SA-41/**************:8030
2015-10-19 15:49:57,632 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: maxContainerCapability: <memory:8192, vCores:32>
2015-10-19 15:49:57,632 INFO [main] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: queue: default
2015-10-19 15:49:57,635 INFO [main] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Upper limit on the thread pool size is 500
2015-10-19 15:49:57,637 INFO [main] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: yarn.client.max-cached-nodemanagers-proxies : 0
2015-10-19 15:49:57,643 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0013Job Transitioned from INITED to SETUP
2015-10-19 15:49:57,647 INFO [CommitterEvent Processor #0] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_SETUP
2015-10-19 15:49:57,655 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0013Job Transitioned from SETUP to RUNNING
2015-10-19 15:49:57,675 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,676 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,680 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,680 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,680 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000001 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,681 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,681 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,681 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000002 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,681 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,681 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,681 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000003 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,682 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,682 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000004 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000005 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,684 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000006 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,685 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,685 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,685 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000007 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,686 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,686 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,686 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000008 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,687 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,687 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:49:57,687 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000009 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,687 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_r_000000 Task Transitioned from NEW to SCHEDULED
2015-10-19 15:49:57,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000001_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000002_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000003_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000004_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,689 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000005_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000006_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000007_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000008_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000009_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,690 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_r_000000_0 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:49:57,691 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: mapResourceRequest:<memory:1024, vCores:1>
2015-10-19 15:49:57,698 INFO [Thread-50] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: reduceResourceRequest:<memory:1024, vCores:1>
2015-10-19 15:49:57,730 INFO [eventHandlingThread] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Event Writer setup for JobId: job_1445182159119_0013, File: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0013/job_1445182159119_0013_1.jhist
2015-10-19 15:49:58,634 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:10 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:0 ContRel:0 HostLocal:0 RackLocal:0
2015-10-19 15:49:58,704 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:14336, vCores:-13> knownNMs=4
2015-10-19 15:49:58,707 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:14336, vCores:-13>
2015-10-19 15:49:58,708 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:49:59,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:12288, vCores:-15>
2015-10-19 15:49:59,711 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:00,714 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:12288, vCores:-15>
2015-10-19 15:50:00,715 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:01,724 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:50:01,725 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:01,726 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0013_01_000002 to attempt_1445182159119_0013_m_000000_0
2015-10-19 15:50:01,728 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:10240, vCores:-17>
2015-10-19 15:50:01,728 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:01,728 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:9 ScheduledReds:0 AssignedMaps:1 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:1 ContRel:0 HostLocal:0 RackLocal:1
2015-10-19 15:50:01,776 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved 04DN8IQ.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:01,790 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-jar file on the remote FS is hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0013/job.jar
2015-10-19 15:50:01,807 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: The job-conf file on the remote FS is /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0013/job.xml
2015-10-19 15:50:01,808 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Adding #0 tokens and #1 secret keys for NM use for launching container
2015-10-19 15:50:01,808 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Size of containertokens_dob is 1
2015-10-19 15:50:01,809 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Putting shuffle token in serviceData
2015-10-19 15:50:01,863 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:50:01,958 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0013_01_000002 taskAttempt attempt_1445182159119_0013_m_000000_0
2015-10-19 15:50:01,962 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0013_m_000000_0
2015-10-19 15:50:01,964 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:50:02,096 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0013_m_000000_0 : 13562
2015-10-19 15:50:02,098 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0013_m_000000_0] using containerId: [container_1445182159119_0013_01_000002 on NM: [04DN8IQ.fareast.corp.microsoft.com:64260]
2015-10-19 15:50:02,102 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:50:02,102 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0013_m_000000
2015-10-19 15:50:02,103 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:50:02,738 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:7168, vCores:-20> knownNMs=4
2015-10-19 15:50:02,738 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:50:02,740 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:02,740 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0013_01_000003 to attempt_1445182159119_0013_m_000001_0
2015-10-19 15:50:02,741 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-20>
2015-10-19 15:50:02,741 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:02,741 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:8 ScheduledReds:0 AssignedMaps:2 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:2 ContRel:0 HostLocal:0 RackLocal:2
2015-10-19 15:50:02,741 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:02,743 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000001_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:50:02,745 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0013_01_000003 taskAttempt attempt_1445182159119_0013_m_000001_0
2015-10-19 15:50:02,746 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0013_m_000001_0
2015-10-19 15:50:02,746 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:50:02,799 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0013_m_000001_0 : 13562
2015-10-19 15:50:02,800 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0013_m_000001_0] using containerId: [container_1445182159119_0013_01_000003 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 15:50:02,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000001_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:50:02,801 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0013_m_000001
2015-10-19 15:50:02,802 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000001 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:50:03,744 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:7168, vCores:-20> knownNMs=4
2015-10-19 15:50:03,745 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:7168, vCores:-20>
2015-10-19 15:50:03,745 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:04,750 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:50:04,751 INFO [RMCommunicator Allocator] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:04,751 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0013_01_000004 to attempt_1445182159119_0013_m_000002_0
2015-10-19 15:50:04,752 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:3072, vCores:-24>
2015-10-19 15:50:04,752 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:04,752 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:7 ScheduledReds:0 AssignedMaps:3 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:3 ContRel:0 HostLocal:0 RackLocal:3
2015-10-19 15:50:04,752 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MININT-FNANLI5.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:50:04,753 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000002_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:50:04,755 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0013_01_000004 taskAttempt attempt_1445182159119_0013_m_000002_0
2015-10-19 15:50:04,755 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0013_m_000002_0
2015-10-19 15:50:04,755 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:50:05,520 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0013_m_000002_0 : 13562
2015-10-19 15:50:05,521 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0013_m_000002_0] using containerId: [container_1445182159119_0013_01_000004 on NM: [MININT-FNANLI5.fareast.corp.microsoft.com:59190]
2015-10-19 15:50:05,521 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000002_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:50:05,522 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0013_m_000002
2015-10-19 15:50:05,522 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000002 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:50:05,758 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:2048, vCores:-25> knownNMs=4
2015-10-19 15:50:05,759 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 15:50:05,759 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:06,761 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:2048, vCores:-25>
2015-10-19 15:50:06,761 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:07,652 INFO [Socket Reader #1 for port 17464] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0013 (auth:SIMPLE)
2015-10-19 15:50:07,717 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0013_m_000002 asked for a task
2015-10-19 15:50:07,717 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0013_m_000002 given task: attempt_1445182159119_0013_m_000000_0
2015-10-19 15:50:07,765 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 15:50:07,765 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:08,408 INFO [Socket Reader #1 for port 17464] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0013 (auth:SIMPLE)
2015-10-19 15:50:08,527 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0013_m_000003 asked for a task
2015-10-19 15:50:08,527 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0013_m_000003 given task: attempt_1445182159119_0013_m_000001_0
2015-10-19 15:50:08,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:08,767 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:09,769 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:09,770 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:10,773 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:10,773 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:11,775 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:11,775 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:12,776 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:12,776 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:13,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:13,780 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:14,781 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:14,781 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:15,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:15,783 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:16,785 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:16,785 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:17,362 INFO [Socket Reader #1 for port 17464] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0013 (auth:SIMPLE)
2015-10-19 15:50:17,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:17,787 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:18,306 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0013_m_000004 asked for a task
2015-10-19 15:50:18,306 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0013_m_000004 given task: attempt_1445182159119_0013_m_000002_0
2015-10-19 15:50:18,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:18,788 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:19,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:19,790 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:20,480 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.03680106
2015-10-19 15:50:20,791 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:20,791 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:21,792 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:21,792 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:22,794 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:22,794 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:23,607 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.04950373
2015-10-19 15:50:23,795 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:23,795 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:24,796 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:24,796 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:25,797 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:25,797 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:26,782 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.06774005
2015-10-19 15:50:26,799 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:26,799 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:27,802 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:27,802 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:28,805 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:28,805 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:29,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:29,807 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:30,030 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.09412222
2015-10-19 15:50:30,808 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:30,808 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:31,810 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:31,810 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:32,811 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:32,811 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:33,182 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.10635664
2015-10-19 15:50:33,812 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:33,812 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:34,814 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:34,814 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:35,817 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:35,817 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:36,346 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.10635664
2015-10-19 15:50:36,818 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:36,818 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:37,820 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:37,820 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:38,107 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.03295066
2015-10-19 15:50:38,821 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:38,822 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:39,777 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.10635664
2015-10-19 15:50:39,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:39,823 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:40,824 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:40,824 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:41,826 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:41,826 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:42,228 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.093113944
2015-10-19 15:50:42,827 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:42,827 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:42,976 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.10635664
2015-10-19 15:50:43,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:43,829 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:44,830 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:44,830 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:45,831 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:45,831 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:46,078 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.1066108
2015-10-19 15:50:46,128 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.10635664
2015-10-19 15:50:46,832 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:46,832 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:47,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:47,834 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:48,836 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:48,836 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:49,327 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.10635664
2015-10-19 15:50:49,839 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:49,839 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:49,939 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.1066108
2015-10-19 15:50:50,841 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:50,841 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:51,843 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:51,843 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:51,853 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.010094036
2015-10-19 15:50:52,494 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.10635664
2015-10-19 15:50:52,844 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:52,845 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:53,846 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:53,846 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:54,847 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:54,847 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:55,391 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.1066108
2015-10-19 15:50:55,685 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.12019617
2015-10-19 15:50:55,848 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:55,848 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:56,641 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.031263176
2015-10-19 15:50:56,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:56,849 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:57,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:57,850 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:58,829 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.16482174
2015-10-19 15:50:58,851 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:58,851 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:50:59,852 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:50:59,852 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:00,152 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.1066108
2015-10-19 15:51:00,794 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.043963056
2015-10-19 15:51:00,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:00,853 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:01,854 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:01,854 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:02,117 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.19158794
2015-10-19 15:51:02,855 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:02,855 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:03,856 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:03,856 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:04,437 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.1066108
2015-10-19 15:51:04,859 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:04,859 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:05,334 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.19158794
2015-10-19 15:51:05,374 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.06264149
2015-10-19 15:51:05,861 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:05,861 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:06,862 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:06,862 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:07,864 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:07,864 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:08,535 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.19158794
2015-10-19 15:51:08,865 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:08,865 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:09,188 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.1066108
2015-10-19 15:51:09,867 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:09,867 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:09,897 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.07881435
2015-10-19 15:51:10,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:10,868 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:11,703 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.19158794
2015-10-19 15:51:11,869 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:11,869 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:12,871 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:12,872 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:13,874 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:13,874 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:14,598 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.1066108
2015-10-19 15:51:14,875 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:14,875 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:15,208 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.19158794
2015-10-19 15:51:15,597 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.101552226
2015-10-19 15:51:15,876 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:15,876 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:16,878 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:16,878 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:17,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:17,880 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:18,440 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.19158794
2015-10-19 15:51:18,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:18,882 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:19,883 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:19,884 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:20,654 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.1066108
2015-10-19 15:51:20,886 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:20,886 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:21,659 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.19158794
2015-10-19 15:51:21,810 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.10660437
2015-10-19 15:51:21,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:21,887 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:22,888 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:22,888 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:23,889 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:23,889 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:24,891 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:24,891 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:24,959 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.19158794
2015-10-19 15:51:25,533 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.1066108
2015-10-19 15:51:25,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:25,892 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:26,794 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.10660437
2015-10-19 15:51:26,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:26,893 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:27,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:27,895 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:28,252 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.20780748
2015-10-19 15:51:28,897 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:28,897 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:29,898 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:29,898 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:30,156 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.113032594
2015-10-19 15:51:30,899 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:30,899 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:31,436 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.24328244
2015-10-19 15:51:31,791 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.10660437
2015-10-19 15:51:31,901 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:31,901 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:32,903 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:32,903 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:33,904 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:33,904 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:34,662 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.2693388
2015-10-19 15:51:34,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:34,905 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:35,166 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.14707811
2015-10-19 15:51:35,907 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:35,907 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:36,908 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:36,908 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:37,011 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.10660437
2015-10-19 15:51:37,910 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:37,910 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:37,999 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.27696857
2015-10-19 15:51:38,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:38,911 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:39,912 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:39,912 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:40,484 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.17830731
2015-10-19 15:51:40,913 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:40,913 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:41,265 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.27696857
2015-10-19 15:51:41,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:41,915 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:42,421 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.10660437
2015-10-19 15:51:42,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:42,917 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:43,919 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:43,919 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:44,676 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.27696857
2015-10-19 15:51:44,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:44,921 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:45,525 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.19211523
2015-10-19 15:51:45,923 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:45,923 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:46,924 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:46,924 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:47,333 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.10660437
2015-10-19 15:51:47,864 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.27696857
2015-10-19 15:51:47,925 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:47,925 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:48,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:48,926 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:49,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:49,927 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:50,549 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.19211523
2015-10-19 15:51:50,929 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:50,929 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:51,066 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.27696857
2015-10-19 15:51:51,930 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:51,930 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:52,491 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.10660437
2015-10-19 15:51:52,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:52,932 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:53,934 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:53,934 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:54,227 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.27696857
2015-10-19 15:51:54,936 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:54,936 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:55,578 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.19211523
2015-10-19 15:51:55,938 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:55,938 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:56,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:56,940 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:57,268 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.10660437
2015-10-19 15:51:57,348 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.2918112
2015-10-19 15:51:57,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:57,942 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:58,943 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:58,944 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:51:59,945 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:51:59,945 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:00,373 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.19211523
2015-10-19 15:52:00,469 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.31721494
2015-10-19 15:52:00,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:00,946 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:01,947 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:01,947 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:02,313 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.10660437
2015-10-19 15:52:02,948 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:02,948 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:03,949 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:03,949 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:03,979 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.3475023
2015-10-19 15:52:04,950 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:04,950 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:05,349 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.19211523
2015-10-19 15:52:05,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:05,951 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:06,954 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:06,954 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:07,234 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.3624012
2015-10-19 15:52:07,410 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.10660437
2015-10-19 15:52:07,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:07,956 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:08,958 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:08,958 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:09,959 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:09,959 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:10,391 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.19211523
2015-10-19 15:52:10,470 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.3624012
2015-10-19 15:52:10,963 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:52:10,964 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0013_01_000005 to attempt_1445182159119_0013_m_000003_0
2015-10-19 15:52:10,964 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:10,964 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:10,964 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:6 ScheduledReds:0 AssignedMaps:4 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:4 ContRel:0 HostLocal:1 RackLocal:3
2015-10-19 15:52:10,965 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:10,965 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000003_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:52:10,966 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0013_01_000005 taskAttempt attempt_1445182159119_0013_m_000003_0
2015-10-19 15:52:10,966 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0013_m_000003_0
2015-10-19 15:52:10,966 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:52:10,981 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0013_m_000003_0 : 13562
2015-10-19 15:52:10,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0013_m_000003_0] using containerId: [container_1445182159119_0013_01_000005 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:52:10,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000003_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:52:10,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0013_m_000003
2015-10-19 15:52:10,982 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000003 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:52:11,965 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:52:11,965 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:11,965 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:12,832 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.10713234
2015-10-19 15:52:12,966 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:12,966 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:13,736 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.3624012
2015-10-19 15:52:13,967 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:13,967 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:14,971 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-19 15:52:14,971 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0013_01_000006 to attempt_1445182159119_0013_m_000004_0
2015-10-19 15:52:14,972 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:14,972 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0013_01_000007 to attempt_1445182159119_0013_m_000005_0
2015-10-19 15:52:14,972 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:14,972 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:14,972 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:4 ScheduledReds:0 AssignedMaps:6 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:6 ContRel:0 HostLocal:3 RackLocal:3
2015-10-19 15:52:14,972 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000004_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:52:14,973 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:14,973 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000005_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:52:14,974 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0013_01_000006 taskAttempt attempt_1445182159119_0013_m_000004_0
2015-10-19 15:52:14,974 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0013_01_000007 taskAttempt attempt_1445182159119_0013_m_000005_0
2015-10-19 15:52:14,974 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0013_m_000004_0
2015-10-19 15:52:14,974 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0013_m_000005_0
2015-10-19 15:52:14,974 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:52:14,975 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:52:14,993 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0013_m_000005_0 : 13562
2015-10-19 15:52:14,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0013_m_000005_0] using containerId: [container_1445182159119_0013_01_000007 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:52:14,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000005_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:52:14,994 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0013_m_000005
2015-10-19 15:52:14,994 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000005 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:52:14,994 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0013_m_000004_0 : 13562
2015-10-19 15:52:14,994 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0013_m_000004_0] using containerId: [container_1445182159119_0013_01_000006 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:52:14,995 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000004_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:52:14,995 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0013_m_000004
2015-10-19 15:52:14,995 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000004 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:52:15,926 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.19211523
2015-10-19 15:52:15,975 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:52:15,975 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:52:15,975 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0013_01_000008 to attempt_1445182159119_0013_m_000006_0
2015-10-19 15:52:15,975 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:15,975 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:15,975 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:3 ScheduledReds:0 AssignedMaps:7 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:7 ContRel:0 HostLocal:4 RackLocal:3
2015-10-19 15:52:15,975 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:15,976 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000006_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:52:15,977 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0013_01_000008 taskAttempt attempt_1445182159119_0013_m_000006_0
2015-10-19 15:52:15,977 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0013_m_000006_0
2015-10-19 15:52:15,977 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:52:15,992 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0013_m_000006_0 : 13562
2015-10-19 15:52:15,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0013_m_000006_0] using containerId: [container_1445182159119_0013_01_000008 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:52:15,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000006_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:52:15,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0013_m_000006
2015-10-19 15:52:15,993 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000006 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:52:16,355 INFO [Socket Reader #1 for port 17464] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0013 (auth:SIMPLE)
2015-10-19 15:52:16,376 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0013_m_000005 asked for a task
2015-10-19 15:52:16,376 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0013_m_000005 given task: attempt_1445182159119_0013_m_000003_0
2015-10-19 15:52:16,876 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.3624012
2015-10-19 15:52:16,978 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:52:16,978 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:16,978 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:17,396 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.13515465
2015-10-19 15:52:17,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:17,979 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:18,396 INFO [Socket Reader #1 for port 17464] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0013 (auth:SIMPLE)
2015-10-19 15:52:18,419 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0013_m_000007 asked for a task
2015-10-19 15:52:18,419 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0013_m_000007 given task: attempt_1445182159119_0013_m_000005_0
2015-10-19 15:52:18,615 INFO [Socket Reader #1 for port 17464] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0013 (auth:SIMPLE)
2015-10-19 15:52:18,636 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0013_m_000006 asked for a task
2015-10-19 15:52:18,636 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0013_m_000006 given task: attempt_1445182159119_0013_m_000004_0
2015-10-19 15:52:18,982 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:52:18,983 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0013_01_000009 to attempt_1445182159119_0013_m_000007_0
2015-10-19 15:52:18,983 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:18,983 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:18,983 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:2 ScheduledReds:0 AssignedMaps:8 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:8 ContRel:0 HostLocal:5 RackLocal:3
2015-10-19 15:52:18,983 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:18,984 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000007_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:52:18,985 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0013_01_000009 taskAttempt attempt_1445182159119_0013_m_000007_0
2015-10-19 15:52:18,985 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0013_m_000007_0
2015-10-19 15:52:18,985 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:52:18,996 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0013_m_000007_0 : 13562
2015-10-19 15:52:18,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0013_m_000007_0] using containerId: [container_1445182159119_0013_01_000009 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:52:18,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000007_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:52:18,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0013_m_000007
2015-10-19 15:52:18,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000007 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:52:19,534 INFO [Socket Reader #1 for port 17464] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0013 (auth:SIMPLE)
2015-10-19 15:52:19,572 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0013_m_000008 asked for a task
2015-10-19 15:52:19,572 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0013_m_000008 given task: attempt_1445182159119_0013_m_000006_0
2015-10-19 15:52:19,988 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=4 release= 0 newContainers=1 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:52:19,988 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:52:19,989 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0013_01_000010 to attempt_1445182159119_0013_m_000008_0
2015-10-19 15:52:19,990 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:19,990 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:19,990 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:19,990 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:9 ContRel:0 HostLocal:6 RackLocal:3
2015-10-19 15:52:19,991 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000008_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:52:19,993 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0013_01_000010 taskAttempt attempt_1445182159119_0013_m_000008_0
2015-10-19 15:52:19,993 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0013_m_000008_0
2015-10-19 15:52:19,994 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:52:20,015 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0013_m_000008_0 : 13562
2015-10-19 15:52:20,016 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0013_m_000008_0] using containerId: [container_1445182159119_0013_01_000010 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:52:20,017 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000008_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:52:20,017 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0013_m_000008
2015-10-19 15:52:20,018 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000008 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:52:20,065 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.3624012
2015-10-19 15:52:20,845 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.19211523
2015-10-19 15:52:20,995 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=4 release= 0 newContainers=2 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:52:20,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 2
2015-10-19 15:52:20,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0013_01_000011 to attempt_1445182159119_0013_m_000009_0
2015-10-19 15:52:20,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Releasing unassigned and invalid container Container: [ContainerId: container_1445182159119_0013_01_000012, NodeId: MSRA-SA-41.fareast.corp.microsoft.com:10769, NodeHttpAddress: MSRA-SA-41.fareast.corp.microsoft.com:8042, Resource: <memory:1024, vCores:1>, Priority: 20, Token: Token { kind: ContainerToken, service: **************:10769 }, ]. RM may have assignment issues
2015-10-19 15:52:20,996 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:52:20,996 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:20,997 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:20,997 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:0 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:7 RackLocal:3
2015-10-19 15:52:20,997 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000009_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:52:20,998 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0013_01_000011 taskAttempt attempt_1445182159119_0013_m_000009_0
2015-10-19 15:52:20,998 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0013_m_000009_0
2015-10-19 15:52:20,998 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:52:21,009 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0013_m_000009_0 : 13562
2015-10-19 15:52:21,009 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0013_m_000009_0] using containerId: [container_1445182159119_0013_01_000011 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:52:21,009 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000009_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:52:21,009 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0013_m_000009
2015-10-19 15:52:21,010 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000009 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:52:21,999 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.15600029
2015-10-19 15:52:22,023 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=4 release= 1 newContainers=0 finishedContainers=1 resourcelimit=<memory:1024, vCores:-26> knownNMs=4
2015-10-19 15:52:22,023 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000012
2015-10-19 15:52:22,023 ERROR [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Container complete event for unknown container id container_1445182159119_0013_01_000012
2015-10-19 15:52:22,024 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:1024, vCores:-26>
2015-10-19 15:52:22,024 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:23,008 INFO [Socket Reader #1 for port 17464] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0013 (auth:SIMPLE)
2015-10-19 15:52:23,026 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:52:23,026 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold not met. completedMapsForReduceSlowstart 1
2015-10-19 15:52:23,042 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0013_m_000009 asked for a task
2015-10-19 15:52:23,042 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0013_m_000009 given task: attempt_1445182159119_0013_m_000007_0
2015-10-19 15:52:23,272 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.3624012
2015-10-19 15:52:23,831 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.10019525
2015-10-19 15:52:23,955 INFO [Socket Reader #1 for port 17464] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0013 (auth:SIMPLE)
2015-10-19 15:52:23,976 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0013_m_000010 asked for a task
2015-10-19 15:52:23,977 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0013_m_000010 given task: attempt_1445182159119_0013_m_000008_0
2015-10-19 15:52:24,462 INFO [Socket Reader #1 for port 17464] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0013 (auth:SIMPLE)
2015-10-19 15:52:24,494 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0013_m_000011 asked for a task
2015-10-19 15:52:24,494 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0013_m_000011 given task: attempt_1445182159119_0013_m_000009_0
2015-10-19 15:52:26,214 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.10685723
2015-10-19 15:52:26,256 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.10291381
2015-10-19 15:52:26,368 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.19211523
2015-10-19 15:52:26,568 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.3648384
2015-10-19 15:52:26,843 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.106493875
2015-10-19 15:52:27,630 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.18060715
2015-10-19 15:52:27,680 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.106964506
2015-10-19 15:52:29,230 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.10685723
2015-10-19 15:52:29,261 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.10680563
2015-10-19 15:52:29,703 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.3912252
2015-10-19 15:52:29,860 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.106493875
2015-10-19 15:52:30,691 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.106964506
2015-10-19 15:52:31,329 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.10681946
2015-10-19 15:52:31,516 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.19211523
2015-10-19 15:52:32,165 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.106881365
2015-10-19 15:52:32,254 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.10685723
2015-10-19 15:52:32,269 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.10680563
2015-10-19 15:52:32,578 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.295472
2015-10-19 15:52:32,856 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.42110762
2015-10-19 15:52:32,878 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.106493875
2015-10-19 15:52:32,886 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.19212553
2015-10-19 15:52:33,706 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.106964506
2015-10-19 15:52:34,365 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.10681946
2015-10-19 15:52:35,198 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.106881365
2015-10-19 15:52:35,267 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.10685723
2015-10-19 15:52:35,284 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.10680563
2015-10-19 15:52:35,612 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.295472
2015-10-19 15:52:35,890 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.15056948
2015-10-19 15:52:36,133 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.44789755
2015-10-19 15:52:36,715 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.10770795
2015-10-19 15:52:36,737 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.19211523
2015-10-19 15:52:37,401 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.10681946
2015-10-19 15:52:37,958 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.19212553
2015-10-19 15:52:38,232 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.106881365
2015-10-19 15:52:38,286 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.18266626
2015-10-19 15:52:38,296 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.110644855
2015-10-19 15:52:38,648 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.295472
2015-10-19 15:52:38,904 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.19209063
2015-10-19 15:52:39,290 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.44789755
2015-10-19 15:52:39,739 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.19266446
2015-10-19 15:52:40,434 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.18054438
2015-10-19 15:52:41,269 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.19258286
2015-10-19 15:52:41,294 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.19247705
2015-10-19 15:52:41,316 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.17589949
2015-10-19 15:52:41,552 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.22521956
2015-10-19 15:52:41,679 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.5323719
2015-10-19 15:52:41,923 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.19209063
2015-10-19 15:52:42,535 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.44789755
2015-10-19 15:52:42,751 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.19266446
2015-10-19 15:52:42,851 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.19212553
2015-10-19 15:52:43,466 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.19255035
2015-10-19 15:52:44,301 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.19247705
2015-10-19 15:52:44,302 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.19258286
2015-10-19 15:52:44,328 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.19242907
2015-10-19 15:52:44,712 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.5323719
2015-10-19 15:52:44,941 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.19209063
2015-10-19 15:52:45,754 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.44789755
2015-10-19 15:52:45,770 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.19266446
2015-10-19 15:52:46,499 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.19255035
2015-10-19 15:52:47,187 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.2616279
2015-10-19 15:52:47,315 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.19247705
2015-10-19 15:52:47,334 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.19258286
2015-10-19 15:52:47,348 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.19242907
2015-10-19 15:52:47,744 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.5323719
2015-10-19 15:52:47,957 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.2497011
2015-10-19 15:52:48,251 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.19212553
2015-10-19 15:52:48,779 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.19266446
2015-10-19 15:52:48,919 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.44789755
2015-10-19 15:52:49,533 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.19620275
2015-10-19 15:52:50,326 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.23175399
2015-10-19 15:52:50,356 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.19242907
2015-10-19 15:52:50,366 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.22827092
2015-10-19 15:52:50,778 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.61688703
2015-10-19 15:52:50,967 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.27765483
2015-10-19 15:52:51,127 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.61688703
2015-10-19 15:52:51,630 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.27776006
2015-10-19 15:52:51,793 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.2783809
2015-10-19 15:52:52,255 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.44789755
2015-10-19 15:52:52,554 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.27825075
2015-10-19 15:52:53,342 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.27813601
2015-10-19 15:52:53,356 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.21484685
2015-10-19 15:52:53,399 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.27811313
2015-10-19 15:52:53,672 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.19212553
2015-10-19 15:52:53,809 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.667
2015-10-19 15:52:53,982 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.27765483
2015-10-19 15:52:54,795 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.2783809
2015-10-19 15:52:55,522 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.44789755
2015-10-19 15:52:55,585 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.27825075
2015-10-19 15:52:56,363 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.2781602
2015-10-19 15:52:56,363 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.27813601
2015-10-19 15:52:56,431 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.27811313
2015-10-19 15:52:56,782 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.27776006
2015-10-19 15:52:56,841 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.667
2015-10-19 15:52:56,984 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.27765483
2015-10-19 15:52:57,795 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.2783809
2015-10-19 15:52:58,622 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.27825075
2015-10-19 15:52:58,791 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.19212553
2015-10-19 15:52:58,952 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.45335063
2015-10-19 15:52:59,374 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.27813601
2015-10-19 15:52:59,375 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.2781602
2015-10-19 15:52:59,467 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.27811313
2015-10-19 15:52:59,877 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.683343
2015-10-19 15:52:59,997 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.36197087
2015-10-19 15:53:00,818 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.29751402
2015-10-19 15:53:01,192 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.27776006
2015-10-19 15:53:01,658 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.27825075
2015-10-19 15:53:02,390 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.2781602
2015-10-19 15:53:02,391 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.34617823
2015-10-19 15:53:02,405 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.48070672
2015-10-19 15:53:02,502 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.29344246
2015-10-19 15:53:02,912 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.72597516
2015-10-19 15:53:02,997 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.36323506
2015-10-19 15:53:03,803 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.19212553
2015-10-19 15:53:03,828 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.36404583
2015-10-19 15:53:04,697 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.3638923
2015-10-19 15:53:05,405 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.36390656
2015-10-19 15:53:05,408 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.29850942
2015-10-19 15:53:05,535 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.3637686
2015-10-19 15:53:05,789 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.5183343
2015-10-19 15:53:05,862 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.27776006
2015-10-19 15:53:05,943 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.7923583
2015-10-19 15:53:05,998 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.36323506
2015-10-19 15:53:06,841 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.36404583
2015-10-19 15:53:07,728 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.3638923
2015-10-19 15:53:08,429 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.36388028
2015-10-19 15:53:08,430 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.36390656
2015-10-19 15:53:08,522 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.19212553
2015-10-19 15:53:08,567 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.3637686
2015-10-19 15:53:08,975 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.86358905
2015-10-19 15:53:09,005 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.37840152
2015-10-19 15:53:09,109 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.53341997
2015-10-19 15:53:09,864 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.36404583
2015-10-19 15:53:10,760 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.3638923
2015-10-19 15:53:11,043 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.27776006
2015-10-19 15:53:11,436 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.36390656
2015-10-19 15:53:11,437 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.36388028
2015-10-19 15:53:11,598 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.3637686
2015-10-19 15:53:12,008 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 0.93676525
2015-10-19 15:53:12,015 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.4486067
2015-10-19 15:53:12,347 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.53341997
2015-10-19 15:53:12,873 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.41829914
2015-10-19 15:53:13,478 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.19212553
2015-10-19 15:53:13,793 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.42575592
2015-10-19 15:53:14,438 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.44138122
2015-10-19 15:53:14,455 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.36388028
2015-10-19 15:53:14,630 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.44950172
2015-10-19 15:53:14,801 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000009_0 is : 1.0
2015-10-19 15:53:14,803 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0013_m_000009_0
2015-10-19 15:53:14,806 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000009_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:53:14,806 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0013_01_000011 taskAttempt attempt_1445182159119_0013_m_000009_0
2015-10-19 15:53:14,807 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0013_m_000009_0
2015-10-19 15:53:14,808 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:53:14,842 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000009_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:53:14,857 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0013_m_000009_0
2015-10-19 15:53:14,859 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000009 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:53:14,865 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 1
2015-10-19 15:53:15,037 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.4486067
2015-10-19 15:53:15,102 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:1 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:7 RackLocal:3
2015-10-19 15:53:15,107 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Recalculating schedule, headroom=<memory:0, vCores:-27>
2015-10-19 15:53:15,107 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Reduce slow start threshold reached. Scheduling reduces.
2015-10-19 15:53:15,107 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: All maps assigned. Ramping up all remaining reduces:1
2015-10-19 15:53:15,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:7 RackLocal:3
2015-10-19 15:53:15,676 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0013_m_000002
2015-10-19 15:53:15,676 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:53:15,676 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0013_m_000002
2015-10-19 15:53:15,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:53:15,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:53:15,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000002_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:53:15,731 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.53341997
2015-10-19 15:53:15,876 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.44980705
2015-10-19 15:53:16,108 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:10 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:7 RackLocal:3
2015-10-19 15:53:16,111 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=5 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:53:16,111 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000011
2015-10-19 15:53:16,113 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:1 AssignedMaps:9 AssignedReds:0 CompletedMaps:1 CompletedReds:0 ContAlloc:11 ContRel:1 HostLocal:7 RackLocal:3
2015-10-19 15:53:16,114 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0013_m_000009_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:53:16,245 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.27776006
2015-10-19 15:53:16,824 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.44964966
2015-10-19 15:53:17,117 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:53:17,118 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned to reduce
2015-10-19 15:53:17,118 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0013_01_000013 to attempt_1445182159119_0013_r_000000_0
2015-10-19 15:53:17,119 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:12 ContRel:1 HostLocal:7 RackLocal:3
2015-10-19 15:53:17,181 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:53:17,182 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_r_000000_0 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:53:17,183 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0013_01_000013 taskAttempt attempt_1445182159119_0013_r_000000_0
2015-10-19 15:53:17,183 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0013_r_000000_0
2015-10-19 15:53:17,183 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:53:17,204 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0013_r_000000_0 : 13562
2015-10-19 15:53:17,205 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0013_r_000000_0] using containerId: [container_1445182159119_0013_01_000013 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:53:17,206 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_r_000000_0 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:53:17,206 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0013_r_000000
2015-10-19 15:53:17,207 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_r_000000 Task Transitioned from SCHEDULED to RUNNING
2015-10-19 15:53:17,439 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.44950968
2015-10-19 15:53:17,467 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.36388028
2015-10-19 15:53:17,660 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.44950172
2015-10-19 15:53:18,049 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.4486067
2015-10-19 15:53:18,122 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=1 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-27> knownNMs=4
2015-10-19 15:53:18,402 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.19212553
2015-10-19 15:53:18,901 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.44980705
2015-10-19 15:53:19,353 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.53341997
2015-10-19 15:53:19,863 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.44964966
2015-10-19 15:53:20,260 INFO [Socket Reader #1 for port 17464] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0013 (auth:SIMPLE)
2015-10-19 15:53:20,279 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0013_r_000013 asked for a task
2015-10-19 15:53:20,279 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0013_r_000013 given task: attempt_1445182159119_0013_r_000000_0
2015-10-19 15:53:20,464 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.44950968
2015-10-19 15:53:20,475 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.41382185
2015-10-19 15:53:20,690 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.44950172
2015-10-19 15:53:21,046 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.27776006
2015-10-19 15:53:21,056 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.4683292
2015-10-19 15:53:21,813 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 0 maxEvents 10000
2015-10-19 15:53:21,915 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.44980705
2015-10-19 15:53:22,542 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.53341997
2015-10-19 15:53:22,819 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:22,896 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.44964966
2015-10-19 15:53:23,328 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.2009446
2015-10-19 15:53:23,473 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.44950968
2015-10-19 15:53:23,493 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.44968578
2015-10-19 15:53:23,722 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.44950172
2015-10-19 15:53:23,820 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:24,068 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.5337699
2015-10-19 15:53:24,134 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:53:24,135 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0013_01_000014 to attempt_1445182159119_0013_m_000002_1
2015-10-19 15:53:24,135 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:1 HostLocal:8 RackLocal:3
2015-10-19 15:53:24,136 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:53:24,137 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000002_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:53:24,138 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0013_01_000014 taskAttempt attempt_1445182159119_0013_m_000002_1
2015-10-19 15:53:24,138 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0013_m_000002_1
2015-10-19 15:53:24,138 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:53:24,155 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0013_m_000002_1 : 13562
2015-10-19 15:53:24,156 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0013_m_000002_1] using containerId: [container_1445182159119_0013_01_000014 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:53:24,157 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000002_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:53:24,157 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0013_m_000002
2015-10-19 15:53:24,823 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:24,935 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.48894462
2015-10-19 15:53:25,138 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-28> knownNMs=4
2015-10-19 15:53:25,463 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.27776006
2015-10-19 15:53:25,826 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:25,925 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.5130856
2015-10-19 15:53:25,926 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.53341997
2015-10-19 15:53:26,496 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.490735
2015-10-19 15:53:26,518 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.44968578
2015-10-19 15:53:26,752 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.53521925
2015-10-19 15:53:26,826 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:26,925 INFO [Socket Reader #1 for port 17464] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0013 (auth:SIMPLE)
2015-10-19 15:53:26,946 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0013_m_000014 asked for a task
2015-10-19 15:53:26,946 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0013_m_000014 given task: attempt_1445182159119_0013_m_000002_1
2015-10-19 15:53:27,096 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.5343203
2015-10-19 15:53:27,759 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:53:27,789 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.21397056
2015-10-19 15:53:27,826 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:27,950 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.53543663
2015-10-19 15:53:28,826 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:28,956 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.5352825
2015-10-19 15:53:29,241 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.53341997
2015-10-19 15:53:29,516 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.5352021
2015-10-19 15:53:29,524 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.44968578
2015-10-19 15:53:29,784 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.53521925
2015-10-19 15:53:29,826 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:30,006 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.27776006
2015-10-19 15:53:30,108 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.5343203
2015-10-19 15:53:30,678 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0013_m_000001
2015-10-19 15:53:30,678 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0013_m_000001
2015-10-19 15:53:30,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:53:30,679 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:53:30,679 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:53:30,680 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000001_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:53:30,765 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:53:30,825 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:30,965 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.53543663
2015-10-19 15:53:31,148 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:13 ContRel:1 HostLocal:8 RackLocal:3
2015-10-19 15:53:31,151 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-28> knownNMs=4
2015-10-19 15:53:31,826 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:31,987 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.5352825
2015-10-19 15:53:32,527 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.5352021
2015-10-19 15:53:32,532 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.4721047
2015-10-19 15:53:32,538 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.2410022
2015-10-19 15:53:32,816 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.53521925
2015-10-19 15:53:32,826 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:32,855 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.53341997
2015-10-19 15:53:33,122 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.5343203
2015-10-19 15:53:33,777 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:53:33,825 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:33,983 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.53543663
2015-10-19 15:53:34,116 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.10660437
2015-10-19 15:53:34,826 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:35,018 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.5352825
2015-10-19 15:53:35,081 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.27776006
2015-10-19 15:53:35,544 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.5352021
2015-10-19 15:53:35,547 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.5352028
2015-10-19 15:53:35,826 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:35,848 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.5887056
2015-10-19 15:53:36,134 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.58378667
2015-10-19 15:53:36,154 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.56770545
2015-10-19 15:53:36,789 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:53:36,827 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:36,993 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.54986966
2015-10-19 15:53:37,119 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.10660437
2015-10-19 15:53:37,826 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:38,048 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.620844
2015-10-19 15:53:38,270 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.26976833
2015-10-19 15:53:38,557 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.5352021
2015-10-19 15:53:38,560 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.5352028
2015-10-19 15:53:38,827 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:38,880 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.6207798
2015-10-19 15:53:39,136 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.6199081
2015-10-19 15:53:39,539 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.61237556
2015-10-19 15:53:39,793 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:53:39,827 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:40,001 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.6210422
2015-10-19 15:53:40,120 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.10660437
2015-10-19 15:53:40,357 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.27776006
2015-10-19 15:53:40,827 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:41,080 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.620844
2015-10-19 15:53:41,560 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.6168673
2015-10-19 15:53:41,579 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.5352028
2015-10-19 15:53:41,827 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:41,911 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.6207798
2015-10-19 15:53:42,134 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.6199081
2015-10-19 15:53:42,739 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.61898744
2015-10-19 15:53:42,808 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:53:42,826 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:43,015 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.6210422
2015-10-19 15:53:43,120 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.13141076
2015-10-19 15:53:43,770 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.27772525
2015-10-19 15:53:43,826 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:44,115 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.620844
2015-10-19 15:53:44,573 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.6209487
2015-10-19 15:53:44,595 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.5352028
2015-10-19 15:53:44,826 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:44,945 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.6207798
2015-10-19 15:53:45,135 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.6199081
2015-10-19 15:53:45,667 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.29550135
2015-10-19 15:53:45,822 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:53:45,826 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:45,922 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.61898744
2015-10-19 15:53:46,030 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.6210422
2015-10-19 15:53:46,137 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.19212553
2015-10-19 15:53:46,826 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:47,026 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.6199081
2015-10-19 15:53:47,146 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.620844
2015-10-19 15:53:47,282 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.6207798
2015-10-19 15:53:47,572 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.6209487
2015-10-19 15:53:47,607 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.5858395
2015-10-19 15:53:47,826 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:47,977 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.667
2015-10-19 15:53:48,135 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.667
2015-10-19 15:53:48,350 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.27772525
2015-10-19 15:53:48,394 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.620844
2015-10-19 15:53:48,457 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.6210422
2015-10-19 15:53:48,826 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:53:48,826 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:49,048 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.667
2015-10-19 15:53:49,138 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.61898744
2015-10-19 15:53:49,152 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.19212553
2015-10-19 15:53:49,827 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:50,177 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.667
2015-10-19 15:53:50,588 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.64226866
2015-10-19 15:53:50,619 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.6208445
2015-10-19 15:53:50,646 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.3291794
2015-10-19 15:53:50,826 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:51,010 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.667
2015-10-19 15:53:51,139 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.667
2015-10-19 15:53:51,291 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.64226866
2015-10-19 15:53:51,827 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:51,843 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:53:52,060 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.667
2015-10-19 15:53:52,170 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.19212553
2015-10-19 15:53:52,449 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.61898744
2015-10-19 15:53:52,827 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:53,215 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.667
2015-10-19 15:53:53,460 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.27772525
2015-10-19 15:53:53,589 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.667
2015-10-19 15:53:53,619 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.6208445
2015-10-19 15:53:53,827 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:54,048 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.667
2015-10-19 15:53:54,154 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.667
2015-10-19 15:53:54,827 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:54,855 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:53:55,074 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.667
2015-10-19 15:53:55,185 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.22004534
2015-10-19 15:53:55,620 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.61898744
2015-10-19 15:53:55,826 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:56,224 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.3570206
2015-10-19 15:53:56,249 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.667
2015-10-19 15:53:56,608 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.667
2015-10-19 15:53:56,622 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.6208445
2015-10-19 15:53:56,828 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:57,082 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.6794586
2015-10-19 15:53:57,179 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.68085545
2015-10-19 15:53:57,828 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:57,878 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:53:58,096 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.6680356
2015-10-19 15:53:58,203 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.27360255
2015-10-19 15:53:58,581 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.27772525
2015-10-19 15:53:58,818 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.61898744
2015-10-19 15:53:58,827 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:53:59,280 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.684892
2015-10-19 15:53:59,619 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.667
2015-10-19 15:53:59,637 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.6383372
2015-10-19 15:53:59,827 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:00,114 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.7092938
2015-10-19 15:54:00,183 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.7077276
2015-10-19 15:54:00,346 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.6383372
2015-10-19 15:54:00,828 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:00,891 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:54:01,082 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.36319977
2015-10-19 15:54:01,105 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.70205826
2015-10-19 15:54:01,214 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.27772525
2015-10-19 15:54:01,828 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:01,963 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.63079065
2015-10-19 15:54:02,310 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.7110443
2015-10-19 15:54:02,621 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.688392
2015-10-19 15:54:02,635 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.667
2015-10-19 15:54:02,828 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:03,146 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.7375966
2015-10-19 15:54:03,186 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.7553513
2015-10-19 15:54:03,829 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:03,908 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:54:03,910 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.27772525
2015-10-19 15:54:04,105 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.75023776
2015-10-19 15:54:04,121 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.63079065
2015-10-19 15:54:04,220 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.27772525
2015-10-19 15:54:04,829 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:05,259 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.667
2015-10-19 15:54:05,344 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.7446258
2015-10-19 15:54:05,644 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.667
2015-10-19 15:54:05,645 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.7384118
2015-10-19 15:54:05,784 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.36319977
2015-10-19 15:54:05,829 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:06,178 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.77314174
2015-10-19 15:54:06,204 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.79723144
2015-10-19 15:54:06,828 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:06,927 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:54:07,116 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.789495
2015-10-19 15:54:07,238 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.27772525
2015-10-19 15:54:07,828 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:08,375 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.77860206
2015-10-19 15:54:08,517 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.667
2015-10-19 15:54:08,661 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.667
2015-10-19 15:54:08,663 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.77463466
2015-10-19 15:54:08,828 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:09,146 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.27772525
2015-10-19 15:54:09,209 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.8087453
2015-10-19 15:54:09,220 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.8274339
2015-10-19 15:54:09,829 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:09,940 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:54:10,126 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.82019436
2015-10-19 15:54:10,253 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.33282357
2015-10-19 15:54:10,590 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.36319977
2015-10-19 15:54:10,844 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:11,407 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.81191653
2015-10-19 15:54:11,673 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.8097669
2015-10-19 15:54:11,676 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.67146844
2015-10-19 15:54:11,739 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.667
2015-10-19 15:54:11,844 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:12,235 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.8596288
2015-10-19 15:54:12,238 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.84365255
2015-10-19 15:54:12,844 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:12,957 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:54:13,141 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.8542274
2015-10-19 15:54:13,267 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.36317363
2015-10-19 15:54:13,844 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:13,851 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.27772525
2015-10-19 15:54:14,436 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.8447709
2015-10-19 15:54:14,685 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.84601283
2015-10-19 15:54:14,691 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.6987436
2015-10-19 15:54:14,843 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:14,899 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.667
2015-10-19 15:54:15,246 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.8951888
2015-10-19 15:54:15,265 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.8783368
2015-10-19 15:54:15,844 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:15,912 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.36319977
2015-10-19 15:54:15,972 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:54:16,152 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.8967645
2015-10-19 15:54:16,282 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.36317363
2015-10-19 15:54:16,844 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:17,465 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.8778862
2015-10-19 15:54:17,707 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.8957503
2015-10-19 15:54:17,708 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.72702366
2015-10-19 15:54:17,844 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:18,129 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.667
2015-10-19 15:54:18,145 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.27772525
2015-10-19 15:54:18,249 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.9372885
2015-10-19 15:54:18,296 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.91342115
2015-10-19 15:54:18,845 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:18,989 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:54:19,161 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.9344127
2015-10-19 15:54:19,302 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.36317363
2015-10-19 15:54:19,844 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:20,249 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:54:20,249 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0013_01_000015 to attempt_1445182159119_0013_m_000001_1
2015-10-19 15:54:20,250 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:9 RackLocal:3
2015-10-19 15:54:20,251 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:54:20,252 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000001_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:54:20,253 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0013_01_000015 taskAttempt attempt_1445182159119_0013_m_000001_1
2015-10-19 15:54:20,253 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0013_m_000001_1
2015-10-19 15:54:20,253 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:54:20,274 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0013_m_000001_1 : 13562
2015-10-19 15:54:20,274 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0013_m_000001_1] using containerId: [container_1445182159119_0013_01_000015 on NM: [MSRA-SA-41.fareast.corp.microsoft.com:10769]
2015-10-19 15:54:20,275 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000001_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:54:20,275 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0013_m_000001
2015-10-19 15:54:20,503 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.36319977
2015-10-19 15:54:20,508 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.90086734
2015-10-19 15:54:20,716 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.7598565
2015-10-19 15:54:20,721 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.92800677
2015-10-19 15:54:20,731 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0013_m_000000
2015-10-19 15:54:20,732 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:54:20,732 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Scheduling a redundant attempt for task task_1445182159119_0013_m_000000
2015-10-19 15:54:20,733 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:54:20,734 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-41.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:54:20,734 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000000_1 TaskAttempt Transitioned from NEW to UNASSIGNED
2015-10-19 15:54:20,850 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:21,250 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:1 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:9 RackLocal:3
2015-10-19 15:54:21,253 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=4 release= 0 newContainers=0 finishedContainers=0 resourcelimit=<memory:0, vCores:-28> knownNMs=4
2015-10-19 15:54:21,269 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 0.9662938
2015-10-19 15:54:21,337 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.93271863
2015-10-19 15:54:21,347 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.667
2015-10-19 15:54:21,849 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:22,008 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:54:22,176 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 0.9655331
2015-10-19 15:54:22,321 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.36317363
2015-10-19 15:54:22,848 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:23,136 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.27772525
2015-10-19 15:54:23,548 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.9154179
2015-10-19 15:54:23,722 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.78999645
2015-10-19 15:54:23,741 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 0.96432805
2015-10-19 15:54:23,849 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:24,289 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 1.0
2015-10-19 15:54:24,368 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000003_0 is : 1.0
2015-10-19 15:54:24,370 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0013_m_000003_0
2015-10-19 15:54:24,371 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000003_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:54:24,372 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0013_01_000005 taskAttempt attempt_1445182159119_0013_m_000003_0
2015-10-19 15:54:24,372 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0013_m_000003_0
2015-10-19 15:54:24,372 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:54:24,377 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.9480806
2015-10-19 15:54:24,391 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000003_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:54:24,391 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0013_m_000003_0
2015-10-19 15:54:24,392 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000003 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:54:24,393 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 2
2015-10-19 15:54:24,490 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.66944027
2015-10-19 15:54:24,779 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.36319977
2015-10-19 15:54:24,848 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 1 maxEvents 10000
2015-10-19 15:54:24,961 INFO [Socket Reader #1 for port 17464] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0013 (auth:SIMPLE)
2015-10-19 15:54:24,998 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0013_m_000015 asked for a task
2015-10-19 15:54:24,998 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0013_m_000015 given task: attempt_1445182159119_0013_m_000001_1
2015-10-19 15:54:25,022 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.033333335
2015-10-19 15:54:25,036 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000006_0 is : 1.0
2015-10-19 15:54:25,039 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0013_m_000006_0
2015-10-19 15:54:25,039 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000006_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:54:25,040 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0013_01_000008 taskAttempt attempt_1445182159119_0013_m_000006_0
2015-10-19 15:54:25,040 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0013_m_000006_0
2015-10-19 15:54:25,040 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:54:25,063 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000006_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:54:25,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0013_m_000006_0
2015-10-19 15:54:25,064 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000006 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:54:25,065 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 3
2015-10-19 15:54:25,259 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:1 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:14 ContRel:1 HostLocal:9 RackLocal:3
2015-10-19 15:54:25,264 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000005
2015-10-19 15:54:25,265 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Got allocated containers 1
2015-10-19 15:54:25,265 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0013_m_000003_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:54:25,265 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Assigned container container_1445182159119_0013_01_000016 to attempt_1445182159119_0013_m_000000_1
2015-10-19 15:54:25,265 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:11 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:54:25,266 INFO [AsyncDispatcher event handler] org.apache.hadoop.yarn.util.RackResolver: Resolved MSRA-SA-39.fareast.corp.microsoft.com to /default-rack
2015-10-19 15:54:25,267 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000000_1 TaskAttempt Transitioned from UNASSIGNED to ASSIGNED
2015-10-19 15:54:25,268 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_LAUNCH for container container_1445182159119_0013_01_000016 taskAttempt attempt_1445182159119_0013_m_000000_1
2015-10-19 15:54:25,268 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Launching attempt_1445182159119_0013_m_000000_1
2015-10-19 15:54:25,268 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:54:25,286 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Shuffle port returned by ContainerManager for attempt_1445182159119_0013_m_000000_1 : 13562
2015-10-19 15:54:25,287 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: TaskAttempt: [attempt_1445182159119_0013_m_000000_1] using containerId: [container_1445182159119_0013_01_000016 on NM: [MSRA-SA-39.fareast.corp.microsoft.com:28345]
2015-10-19 15:54:25,287 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000000_1 TaskAttempt Transitioned from ASSIGNED to RUNNING
2015-10-19 15:54:25,288 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: ATTEMPT_START task_1445182159119_0013_m_000000
2015-10-19 15:54:25,336 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.43679675
2015-10-19 15:54:25,852 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 2 maxEvents 10000
2015-10-19 15:54:26,269 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerRequestor: getResources() for application_1445182159119_0013: ask=4 release= 0 newContainers=0 finishedContainers=1 resourcelimit=<memory:0, vCores:-28> knownNMs=4
2015-10-19 15:54:26,269 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000008
2015-10-19 15:54:26,270 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:3 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:54:26,270 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0013_m_000006_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:54:26,476 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000005_0 is : 1.0
2015-10-19 15:54:26,478 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0013_m_000005_0
2015-10-19 15:54:26,479 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000005_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:54:26,480 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0013_01_000007 taskAttempt attempt_1445182159119_0013_m_000005_0
2015-10-19 15:54:26,480 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0013_m_000005_0
2015-10-19 15:54:26,481 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:54:26,500 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000005_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:54:26,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0013_m_000005_0
2015-10-19 15:54:26,501 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000005 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:54:26,502 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 4
2015-10-19 15:54:26,586 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.9306492
2015-10-19 15:54:26,743 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.8183101
2015-10-19 15:54:26,852 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 3 maxEvents 10000
2015-10-19 15:54:27,270 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:10 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:54:27,417 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.9644135
2015-10-19 15:54:27,691 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.6845275
2015-10-19 15:54:27,852 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 15:54:28,042 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.13333334
2015-10-19 15:54:28,183 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.27772525
2015-10-19 15:54:28,274 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000007
2015-10-19 15:54:28,275 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:4 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:54:28,275 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0013_m_000005_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:54:28,351 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.44859612
2015-10-19 15:54:28,569 INFO [Socket Reader #1 for port 17464] SecurityLogger.org.apache.hadoop.ipc.Server: Auth successful for job_1445182159119_0013 (auth:SIMPLE)
2015-10-19 15:54:28,587 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID : jvm_1445182159119_0013_m_000016 asked for a task
2015-10-19 15:54:28,587 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: JVM with ID: jvm_1445182159119_0013_m_000016 given task: attempt_1445182159119_0013_m_000000_1
2015-10-19 15:54:28,852 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 15:54:29,508 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.36319977
2015-10-19 15:54:29,618 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.9497003
2015-10-19 15:54:29,765 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.851343
2015-10-19 15:54:29,856 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 15:54:30,448 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 0.9863124
2015-10-19 15:54:30,856 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 15:54:30,953 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.7005427
2015-10-19 15:54:31,062 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.13333334
2015-10-19 15:54:31,372 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.44859612
2015-10-19 15:54:31,856 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 15:54:32,074 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000008_0 is : 1.0
2015-10-19 15:54:32,076 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0013_m_000008_0
2015-10-19 15:54:32,077 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000008_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:54:32,077 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0013_01_000010 taskAttempt attempt_1445182159119_0013_m_000008_0
2015-10-19 15:54:32,078 INFO [ContainerLauncher #8] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0013_m_000008_0
2015-10-19 15:54:32,078 INFO [ContainerLauncher #8] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:54:32,097 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000008_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:54:32,098 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0013_m_000008_0
2015-10-19 15:54:32,098 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000008 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:54:32,099 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 5
2015-10-19 15:54:32,280 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:9 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:54:32,401 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.29864827
2015-10-19 15:54:32,646 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 0.97591525
2015-10-19 15:54:32,779 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.882659
2015-10-19 15:54:32,856 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 4 maxEvents 10000
2015-10-19 15:54:33,284 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000010
2015-10-19 15:54:33,284 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:5 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:54:33,284 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0013_m_000008_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:54:33,427 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.36319977
2015-10-19 15:54:33,606 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.1066108
2015-10-19 15:54:33,857 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:34,075 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.16666667
2015-10-19 15:54:34,122 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.7158895
2015-10-19 15:54:34,403 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.44859612
2015-10-19 15:54:34,857 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:35,680 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000007_0 is : 1.0
2015-10-19 15:54:35,683 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0013_m_000007_0
2015-10-19 15:54:35,683 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000007_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:54:35,684 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0013_01_000009 taskAttempt attempt_1445182159119_0013_m_000007_0
2015-10-19 15:54:35,684 INFO [ContainerLauncher #9] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0013_m_000007_0
2015-10-19 15:54:35,685 INFO [ContainerLauncher #9] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:54:35,705 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000007_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:54:35,705 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0013_m_000007_0
2015-10-19 15:54:35,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000007 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:54:35,706 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 6
2015-10-19 15:54:35,794 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.9139187
2015-10-19 15:54:35,857 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 5 maxEvents 10000
2015-10-19 15:54:36,189 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.10530018
2015-10-19 15:54:36,288 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:8 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:54:36,319 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.3234012
2015-10-19 15:54:36,637 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.1066108
2015-10-19 15:54:36,857 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:54:37,091 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.20000002
2015-10-19 15:54:37,292 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000009
2015-10-19 15:54:37,292 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:6 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:54:37,292 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0013_m_000007_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:54:37,300 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.73020804
2015-10-19 15:54:37,341 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.36319977
2015-10-19 15:54:37,421 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.44859612
2015-10-19 15:54:37,857 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:54:38,807 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.9498869
2015-10-19 15:54:38,857 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:54:39,199 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.10635664
2015-10-19 15:54:39,680 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.1066108
2015-10-19 15:54:39,858 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:54:40,110 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.20000002
2015-10-19 15:54:40,435 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.5030492
2015-10-19 15:54:40,536 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.7458252
2015-10-19 15:54:40,858 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:54:41,560 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.3563107
2015-10-19 15:54:41,829 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 0.9931575
2015-10-19 15:54:41,857 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:54:42,214 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.10635664
2015-10-19 15:54:42,364 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000004_0 is : 1.0
2015-10-19 15:54:42,366 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0013_m_000004_0
2015-10-19 15:54:42,367 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000004_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:54:42,367 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0013_01_000006 taskAttempt attempt_1445182159119_0013_m_000004_0
2015-10-19 15:54:42,368 INFO [ContainerLauncher #0] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0013_m_000004_0
2015-10-19 15:54:42,368 INFO [ContainerLauncher #0] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:54:42,383 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000004_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:54:42,384 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0013_m_000004_0
2015-10-19 15:54:42,384 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000004 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:54:42,385 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 7
2015-10-19 15:54:42,473 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.36319977
2015-10-19 15:54:42,717 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.1066108
2015-10-19 15:54:42,858 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 6 maxEvents 10000
2015-10-19 15:54:43,121 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.20000002
2015-10-19 15:54:43,303 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:7 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:54:43,451 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.5342037
2015-10-19 15:54:43,682 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.76077104
2015-10-19 15:54:43,857 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:44,308 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000006
2015-10-19 15:54:44,308 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:7 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:54:44,308 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0013_m_000004_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:54:44,861 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:45,219 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.10635664
2015-10-19 15:54:45,747 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.1066108
2015-10-19 15:54:45,860 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:46,070 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.36317363
2015-10-19 15:54:46,146 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:54:46,468 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.5342037
2015-10-19 15:54:46,646 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.39274287
2015-10-19 15:54:46,861 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:47,002 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.7732143
2015-10-19 15:54:47,860 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:48,232 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.114055336
2015-10-19 15:54:48,777 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.19211523
2015-10-19 15:54:48,860 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:49,155 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:54:49,491 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.5342037
2015-10-19 15:54:49,861 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:50,483 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.7849494
2015-10-19 15:54:50,741 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.36317363
2015-10-19 15:54:50,861 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:51,239 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.19158794
2015-10-19 15:54:51,305 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.43402132
2015-10-19 15:54:51,800 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.19211523
2015-10-19 15:54:51,861 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:52,169 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:54:52,498 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.5831107
2015-10-19 15:54:52,861 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:53,754 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.79691976
2015-10-19 15:54:53,862 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:54,248 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.19158794
2015-10-19 15:54:54,829 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.19211523
2015-10-19 15:54:54,862 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:55,169 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:54:55,234 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.36317363
2015-10-19 15:54:55,497 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.6196791
2015-10-19 15:54:55,631 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.448704
2015-10-19 15:54:55,862 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:56,862 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:57,013 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.81015635
2015-10-19 15:54:57,252 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.19158794
2015-10-19 15:54:57,861 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:57,867 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.23401503
2015-10-19 15:54:58,175 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:54:58,500 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.6196791
2015-10-19 15:54:58,861 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:54:59,545 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.36317363
2015-10-19 15:54:59,861 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:00,060 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.448704
2015-10-19 15:55:00,205 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.82324666
2015-10-19 15:55:00,273 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.19158794
2015-10-19 15:55:00,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:00,897 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.27776006
2015-10-19 15:55:01,191 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:01,518 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.6196791
2015-10-19 15:55:01,861 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:02,861 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:03,284 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.26666534
2015-10-19 15:55:03,379 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.8388426
2015-10-19 15:55:03,861 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:03,867 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.36317363
2015-10-19 15:55:03,922 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.27776006
2015-10-19 15:55:04,204 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:04,532 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.6196791
2015-10-19 15:55:04,538 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.448704
2015-10-19 15:55:04,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:05,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:06,299 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.27696857
2015-10-19 15:55:06,657 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.85369545
2015-10-19 15:55:06,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:06,954 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.27776006
2015-10-19 15:55:07,218 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:07,545 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.6196791
2015-10-19 15:55:07,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:08,272 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.36317363
2015-10-19 15:55:08,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:09,311 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.27696857
2015-10-19 15:55:09,351 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.448704
2015-10-19 15:55:09,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:09,927 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.86542875
2015-10-19 15:55:09,984 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.36319977
2015-10-19 15:55:10,241 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:10,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:11,802 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.6196791
2015-10-19 15:55:11,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:12,319 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.27696857
2015-10-19 15:55:12,613 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.36317363
2015-10-19 15:55:12,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:13,014 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.36319977
2015-10-19 15:55:13,233 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.8770803
2015-10-19 15:55:13,250 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:13,546 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.667
2015-10-19 15:55:13,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:13,927 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.448704
2015-10-19 15:55:14,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:15,329 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.2994508
2015-10-19 15:55:15,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:16,045 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.36319977
2015-10-19 15:55:16,271 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:16,552 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.667
2015-10-19 15:55:16,637 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.88908243
2015-10-19 15:55:16,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:17,071 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.36317363
2015-10-19 15:55:17,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:18,342 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.3624012
2015-10-19 15:55:18,635 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.448704
2015-10-19 15:55:18,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:19,076 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.36621416
2015-10-19 15:55:19,279 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:19,563 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.66987866
2015-10-19 15:55:19,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:19,885 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.9009184
2015-10-19 15:55:20,862 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:21,348 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.3624012
2015-10-19 15:55:21,589 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.36317363
2015-10-19 15:55:21,865 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:22,106 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.448704
2015-10-19 15:55:22,284 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:22,580 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.71117455
2015-10-19 15:55:22,783 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.448704
2015-10-19 15:55:22,864 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:23,021 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.91282177
2015-10-19 15:55:23,864 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:24,359 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.3624012
2015-10-19 15:55:24,865 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:25,137 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.448704
2015-10-19 15:55:25,297 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:25,586 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.7462837
2015-10-19 15:55:25,864 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:26,133 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.3991516
2015-10-19 15:55:26,267 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.9245495
2015-10-19 15:55:26,865 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:27,353 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.448704
2015-10-19 15:55:27,367 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.3624012
2015-10-19 15:55:27,864 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:28,167 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.448704
2015-10-19 15:55:28,300 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:28,596 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.778657
2015-10-19 15:55:28,864 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:29,487 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.9366791
2015-10-19 15:55:29,865 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:30,380 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.43483263
2015-10-19 15:55:30,591 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.42468902
2015-10-19 15:55:30,864 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:31,197 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.53425497
2015-10-19 15:55:31,314 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:31,612 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.8125089
2015-10-19 15:55:31,864 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:32,485 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.448704
2015-10-19 15:55:32,864 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:32,917 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.9477072
2015-10-19 15:55:33,395 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.44789755
2015-10-19 15:55:33,864 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:34,224 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.53425497
2015-10-19 15:55:34,320 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:34,632 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.84483594
2015-10-19 15:55:34,864 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:35,485 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.44859612
2015-10-19 15:55:35,865 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:36,408 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.44789755
2015-10-19 15:55:36,470 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.9568193
2015-10-19 15:55:36,865 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:37,078 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.46848324
2015-10-19 15:55:37,254 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.53425497
2015-10-19 15:55:37,334 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:37,645 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.88599265
2015-10-19 15:55:37,865 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:38,865 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:39,407 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.44789755
2015-10-19 15:55:39,697 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.96750623
2015-10-19 15:55:39,865 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:40,280 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.53425497
2015-10-19 15:55:40,339 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.44859612
2015-10-19 15:55:40,347 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:40,661 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.9259385
2015-10-19 15:55:40,864 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:41,416 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.50612146
2015-10-19 15:55:41,864 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:42,410 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.50783724
2015-10-19 15:55:42,783 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.98146945
2015-10-19 15:55:42,865 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:43,298 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.6197233
2015-10-19 15:55:43,366 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:43,674 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 0.9583431
2015-10-19 15:55:43,865 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:44,864 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:45,067 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.44859612
2015-10-19 15:55:45,422 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_1 is : 0.53341997
2015-10-19 15:55:45,865 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:45,869 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.53425497
2015-10-19 15:55:45,981 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 0.99840504
2015-10-19 15:55:46,326 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.6197233
2015-10-19 15:55:46,375 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.23333333
2015-10-19 15:55:46,672 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 1.0
2015-10-19 15:55:46,723 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_1 is : 1.0
2015-10-19 15:55:46,727 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0013_m_000002_1
2015-10-19 15:55:46,728 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000002_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:55:46,728 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0013_01_000014 taskAttempt attempt_1445182159119_0013_m_000002_1
2015-10-19 15:55:46,729 INFO [ContainerLauncher #1] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0013_m_000002_1
2015-10-19 15:55:46,730 INFO [ContainerLauncher #1] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:55:46,744 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000002_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:55:46,744 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0013_m_000002_1
2015-10-19 15:55:46,745 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0013_m_000002_0
2015-10-19 15:55:46,745 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000002 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:55:46,746 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 8
2015-10-19 15:55:46,746 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000002_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 15:55:46,746 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0013_01_000004 taskAttempt attempt_1445182159119_0013_m_000002_0
2015-10-19 15:55:46,747 INFO [ContainerLauncher #2] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0013_m_000002_0
2015-10-19 15:55:46,747 INFO [ContainerLauncher #2] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:55:46,762 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000000_0 is : 1.0
2015-10-19 15:55:46,824 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0013_m_000000_0
2015-10-19 15:55:46,824 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000000_0 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:55:46,825 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0013_01_000002 taskAttempt attempt_1445182159119_0013_m_000000_0
2015-10-19 15:55:46,825 INFO [ContainerLauncher #3] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0013_m_000000_0
2015-10-19 15:55:46,826 INFO [ContainerLauncher #3] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : 04DN8IQ.fareast.corp.microsoft.com:64260
2015-10-19 15:55:46,842 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: DefaultSpeculator.addSpeculativeAttempt -- we are speculating task_1445182159119_0013_m_000002
2015-10-19 15:55:46,842 INFO [DefaultSpeculator background processing] org.apache.hadoop.mapreduce.v2.app.speculate.DefaultSpeculator: We launched 1 speculations.  Sleeping 15000 milliseconds.
2015-10-19 15:55:46,865 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 7 maxEvents 10000
2015-10-19 15:55:47,092 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:55:47,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0013_m_000000_0
2015-10-19 15:55:47,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0013_m_000000_1
2015-10-19 15:55:47,093 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:55:47,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 9
2015-10-19 15:55:47,094 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000000_1 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 15:55:47,094 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0013_01_000016 taskAttempt attempt_1445182159119_0013_m_000000_1
2015-10-19 15:55:47,095 INFO [ContainerLauncher #5] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0013_m_000000_1
2015-10-19 15:55:47,095 INFO [ContainerLauncher #5] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:55:47,114 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000000_1 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 15:55:47,115 INFO [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:55:47,128 WARN [CommitterEvent Processor #1] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445182159119_0013_m_000000_1
2015-10-19 15:55:47,131 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000000_1 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 15:55:47,140 INFO [Socket Reader #1 for port 17464] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 17464: readAndProcess from client ************** threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 15:55:47,383 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:6 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:55:47,384 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000014
2015-10-19 15:55:47,384 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000016
2015-10-19 15:55:47,384 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0013_m_000002_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:55:47,384 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:4 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:55:47,384 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0013_m_000000_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:55:47,429 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000002_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 15:55:47,430 INFO [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:55:47,431 WARN [CommitterEvent Processor #2] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445182159119_0013_m_000002_0
2015-10-19 15:55:47,432 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000002_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 15:55:47,865 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 8 maxEvents 10000
2015-10-19 15:55:48,385 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000002
2015-10-19 15:55:48,385 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:3 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:55:48,385 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0013_m_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:55:48,865 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:49,182 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000002_0 is : 0.44859612
2015-10-19 15:55:49,345 INFO [IPC Server handler 6 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.6197233
2015-10-19 15:55:49,376 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.26666668
2015-10-19 15:55:49,864 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:50,122 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.53425497
2015-10-19 15:55:50,468 INFO [Socket Reader #1 for port 17464] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 17464: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 15:55:50,865 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:51,716 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.6197233
2015-10-19 15:55:51,865 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:52,365 INFO [IPC Server handler 24 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.667
2015-10-19 15:55:52,376 INFO [IPC Server handler 9 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.26666668
2015-10-19 15:55:52,865 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:53,723 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.53425497
2015-10-19 15:55:53,868 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:54,392 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000004
2015-10-19 15:55:54,392 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:9 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:55:54,392 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0013_m_000002_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:55:54,876 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:55,382 INFO [IPC Server handler 26 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.667
2015-10-19 15:55:55,387 INFO [IPC Server handler 19 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.26666668
2015-10-19 15:55:55,868 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:56,869 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:57,835 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.53425497
2015-10-19 15:55:57,868 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:58,396 INFO [IPC Server handler 16 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.26666668
2015-10-19 15:55:58,401 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.667
2015-10-19 15:55:58,868 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:55:59,868 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:00,868 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:01,398 INFO [IPC Server handler 13 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.26666668
2015-10-19 15:56:01,429 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.6904911
2015-10-19 15:56:01,868 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:02,274 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.53425497
2015-10-19 15:56:02,868 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:03,875 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:04,417 INFO [IPC Server handler 23 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.26666668
2015-10-19 15:56:04,447 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.7222442
2015-10-19 15:56:04,875 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:05,866 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.53425497
2015-10-19 15:56:05,874 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:06,876 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:07,431 INFO [IPC Server handler 12 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.26666668
2015-10-19 15:56:07,465 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.75547105
2015-10-19 15:56:07,887 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:08,886 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:09,580 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.53425497
2015-10-19 15:56:09,887 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:10,435 INFO [IPC Server handler 21 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.26666668
2015-10-19 15:56:10,490 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.7895163
2015-10-19 15:56:10,887 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:11,887 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:12,887 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:13,133 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.53425497
2015-10-19 15:56:13,450 INFO [IPC Server handler 28 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.26666668
2015-10-19 15:56:13,517 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.8244606
2015-10-19 15:56:13,887 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:14,887 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:15,887 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:16,463 INFO [IPC Server handler 22 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.26666668
2015-10-19 15:56:16,544 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.8499442
2015-10-19 15:56:16,886 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:17,480 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.5590751
2015-10-19 15:56:17,886 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:18,887 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:19,475 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.3
2015-10-19 15:56:19,570 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.88197887
2015-10-19 15:56:19,887 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:20,886 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:21,888 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:21,956 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.59319663
2015-10-19 15:56:22,485 INFO [IPC Server handler 14 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.3
2015-10-19 15:56:22,597 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.9163921
2015-10-19 15:56:22,888 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:23,888 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:24,888 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:25,492 INFO [IPC Server handler 18 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.3
2015-10-19 15:56:25,623 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.95058703
2015-10-19 15:56:25,888 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:26,888 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:26,946 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.61685973
2015-10-19 15:56:27,887 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:28,500 INFO [IPC Server handler 25 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.3
2015-10-19 15:56:28,650 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 0.98438907
2015-10-19 15:56:28,888 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:29,888 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:30,168 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_1 is : 1.0
2015-10-19 15:56:30,170 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0013_m_000001_1
2015-10-19 15:56:30,171 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000001_1 TaskAttempt Transitioned from RUNNING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:56:30,171 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0013_01_000015 taskAttempt attempt_1445182159119_0013_m_000001_1
2015-10-19 15:56:30,172 INFO [ContainerLauncher #4] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0013_m_000001_1
2015-10-19 15:56:30,173 INFO [ContainerLauncher #4] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-41.fareast.corp.microsoft.com:10769
2015-10-19 15:56:30,194 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000001_1 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:56:30,195 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0013_m_000001_1
2015-10-19 15:56:30,195 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Issuing kill to other attempt attempt_1445182159119_0013_m_000001_0
2015-10-19 15:56:30,196 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_m_000001 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:56:30,196 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 10
2015-10-19 15:56:30,197 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000001_0 TaskAttempt Transitioned from RUNNING to KILL_CONTAINER_CLEANUP
2015-10-19 15:56:30,197 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0013_01_000003 taskAttempt attempt_1445182159119_0013_m_000001_0
2015-10-19 15:56:30,197 INFO [ContainerLauncher #6] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0013_m_000001_0
2015-10-19 15:56:30,199 INFO [ContainerLauncher #6] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MININT-FNANLI5.fareast.corp.microsoft.com:59190
2015-10-19 15:56:30,430 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:2 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:56:30,646 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000001_0 TaskAttempt Transitioned from KILL_CONTAINER_CLEANUP to KILL_TASK_CLEANUP
2015-10-19 15:56:30,647 INFO [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: TASK_ABORT
2015-10-19 15:56:30,650 WARN [CommitterEvent Processor #3] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Could not delete hdfs://msra-sa-41:9000/pageout/out3/_temporary/1/_temporary/attempt_1445182159119_0013_m_000001_0
2015-10-19 15:56:30,651 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_m_000001_0 TaskAttempt Transitioned from KILL_TASK_CLEANUP to KILLED
2015-10-19 15:56:30,887 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: MapCompletionEvents request from attempt_1445182159119_0013_r_000000_0. startIndex 9 maxEvents 10000
2015-10-19 15:56:31,075 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_m_000001_0 is : 0.6197233
2015-10-19 15:56:31,436 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000015
2015-10-19 15:56:31,437 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:1 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:56:31,437 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0013_m_000001_1: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:56:31,475 INFO [IPC Server handler 11 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.3
2015-10-19 15:56:31,529 INFO [IPC Server handler 27 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.33333334
2015-10-19 15:56:31,530 INFO [IPC Server handler 15 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.33333334
2015-10-19 15:56:32,513 INFO [Socket Reader #1 for port 17464] org.apache.hadoop.ipc.Server: Socket Reader #1 for port 17464: readAndProcess from client ************* threw exception [java.io.IOException: An existing connection was forcibly closed by the remote host]
java.io.IOException: An existing connection was forcibly closed by the remote host
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:197)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at org.apache.hadoop.ipc.Server.channelRead(Server.java:2593)
	at org.apache.hadoop.ipc.Server.access$2800(Server.java:135)
	at org.apache.hadoop.ipc.Server$Connection.readAndProcess(Server.java:1471)
	at org.apache.hadoop.ipc.Server$Listener.doRead(Server.java:762)
	at org.apache.hadoop.ipc.Server$Listener$Reader.doRunLoop(Server.java:636)
	at org.apache.hadoop.ipc.Server$Listener$Reader.run(Server.java:607)
2015-10-19 15:56:33,440 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000003
2015-10-19 15:56:33,440 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:0 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:56:33,440 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0013_m_000001_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:56:34,544 INFO [IPC Server handler 17 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.68389654
2015-10-19 15:56:37,563 INFO [IPC Server handler 2 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.70011914
2015-10-19 15:56:40,574 INFO [IPC Server handler 8 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.7175994
2015-10-19 15:56:43,594 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.73415387
2015-10-19 15:56:46,607 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.7568353
2015-10-19 15:56:49,612 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.7792296
2015-10-19 15:56:52,624 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.800672
2015-10-19 15:56:55,625 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.8282335
2015-10-19 15:56:58,623 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.85208464
2015-10-19 15:57:01,624 INFO [IPC Server handler 3 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.879298
2015-10-19 15:57:04,627 INFO [IPC Server handler 4 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.90682435
2015-10-19 15:57:07,641 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.93283224
2015-10-19 15:57:10,640 INFO [IPC Server handler 0 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.95471644
2015-10-19 15:57:13,646 INFO [IPC Server handler 10 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 0.97533226
2015-10-19 15:57:16,660 INFO [IPC Server handler 20 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 1.0
2015-10-19 15:57:17,588 INFO [IPC Server handler 1 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit-pending state update from attempt_1445182159119_0013_r_000000_0
2015-10-19 15:57:17,588 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_r_000000_0 TaskAttempt Transitioned from RUNNING to COMMIT_PENDING
2015-10-19 15:57:17,589 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: attempt_1445182159119_0013_r_000000_0 given a go for committing the task output.
2015-10-19 15:57:17,590 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Commit go/no-go request from attempt_1445182159119_0013_r_000000_0
2015-10-19 15:57:17,590 INFO [IPC Server handler 29 on 17464] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Result of canCommit for attempt_1445182159119_0013_r_000000_0:true
2015-10-19 15:57:17,615 INFO [IPC Server handler 5 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Progress of TaskAttempt attempt_1445182159119_0013_r_000000_0 is : 1.0
2015-10-19 15:57:17,616 INFO [IPC Server handler 7 on 17464] org.apache.hadoop.mapred.TaskAttemptListenerImpl: Done acknowledgement from attempt_1445182159119_0013_r_000000_0
2015-10-19 15:57:17,617 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_r_000000_0 TaskAttempt Transitioned from COMMIT_PENDING to SUCCESS_CONTAINER_CLEANUP
2015-10-19 15:57:17,617 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: Processing the event EventType: CONTAINER_REMOTE_CLEANUP for container container_1445182159119_0013_01_000013 taskAttempt attempt_1445182159119_0013_r_000000_0
2015-10-19 15:57:17,617 INFO [ContainerLauncher #7] org.apache.hadoop.mapreduce.v2.app.launcher.ContainerLauncherImpl: KILLING attempt_1445182159119_0013_r_000000_0
2015-10-19 15:57:17,618 INFO [ContainerLauncher #7] org.apache.hadoop.yarn.client.api.impl.ContainerManagementProtocolProxy: Opening proxy : MSRA-SA-39.fareast.corp.microsoft.com:28345
2015-10-19 15:57:17,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: attempt_1445182159119_0013_r_000000_0 TaskAttempt Transitioned from SUCCESS_CONTAINER_CLEANUP to SUCCEEDED
2015-10-19 15:57:17,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: Task succeeded with attempt attempt_1445182159119_0013_r_000000_0
2015-10-19 15:57:17,631 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskImpl: task_1445182159119_0013_r_000000 Task Transitioned from RUNNING to SUCCEEDED
2015-10-19 15:57:17,632 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Num completed Tasks: 11
2015-10-19 15:57:17,632 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0013Job Transitioned from RUNNING to COMMITTING
2015-10-19 15:57:17,633 INFO [CommitterEvent Processor #4] org.apache.hadoop.mapreduce.v2.app.commit.CommitterEventHandler: Processing the event EventType: JOB_COMMIT
2015-10-19 15:57:17,669 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: Calling handler for JobFinishedEvent 
2015-10-19 15:57:17,670 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.JobImpl: job_1445182159119_0013Job Transitioned from COMMITTING to SUCCEEDED
2015-10-19 15:57:17,671 INFO [Thread-108] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: We are finishing cleanly so this is the last retry
2015-10-19 15:57:17,671 INFO [Thread-108] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify RMCommunicator isAMLastRetry: true
2015-10-19 15:57:17,671 INFO [Thread-108] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: RMCommunicator notified that shouldUnregistered is: true
2015-10-19 15:57:17,671 INFO [Thread-108] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Notify JHEH isAMLastRetry: true
2015-10-19 15:57:17,671 INFO [Thread-108] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: JobHistoryEventHandler notified that forceJobCompletion is true
2015-10-19 15:57:17,672 INFO [Thread-108] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Calling stop for all the services
2015-10-19 15:57:17,672 INFO [Thread-108] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopping JobHistoryEventHandler. Size of the outstanding queue size is 1
2015-10-19 15:57:17,704 INFO [Thread-108] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: In stop, writing event JOB_FINISHED
2015-10-19 15:57:18,489 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Before Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:1 CompletedMaps:10 CompletedReds:1 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:57:19,491 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Received completed container container_1445182159119_0013_01_000013
2015-10-19 15:57:19,491 INFO [RMCommunicator Allocator] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: After Scheduling: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:57:19,491 INFO [AsyncDispatcher event handler] org.apache.hadoop.mapreduce.v2.app.job.impl.TaskAttemptImpl: Diagnostics report from attempt_1445182159119_0013_r_000000_0: Container killed by the ApplicationMaster.
Container killed on request. Exit code is 137
Container exited with a non-zero exit code 137

2015-10-19 15:57:19,608 INFO [Thread-108] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0013/job_1445182159119_0013_1.jhist to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0013-1445240988628-msrabi-pagerank-1445241437667-10-1-SUCCEEDED-default-1445240997639.jhist_tmp
2015-10-19 15:57:19,911 INFO [Thread-108] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0013-1445240988628-msrabi-pagerank-1445241437667-10-1-SUCCEEDED-default-1445240997639.jhist_tmp
2015-10-19 15:57:19,913 INFO [Thread-108] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copying hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0013/job_1445182159119_0013_1_conf.xml to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0013_conf.xml_tmp
2015-10-19 15:57:20,952 INFO [Thread-108] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Copied to done location: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0013_conf.xml_tmp
2015-10-19 15:57:20,955 INFO [Thread-108] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0013.summary_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0013.summary
2015-10-19 15:57:20,957 INFO [Thread-108] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0013_conf.xml_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0013_conf.xml
2015-10-19 15:57:20,958 INFO [Thread-108] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Moved tmp to done: hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0013-1445240988628-msrabi-pagerank-1445241437667-10-1-SUCCEEDED-default-1445240997639.jhist_tmp to hdfs://msra-sa-41:9000/tmp/hadoop-yarn/staging
							/history/done_intermediate/msrabi/job_1445182159119_0013-1445240988628-msrabi-pagerank-1445241437667-10-1-SUCCEEDED-default-1445240997639.jhist
2015-10-19 15:57:20,958 INFO [Thread-108] org.apache.hadoop.mapreduce.jobhistory.JobHistoryEventHandler: Stopped JobHistoryEventHandler. super.stop()
2015-10-19 15:57:20,961 INFO [Thread-108] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Setting job diagnostics to 
2015-10-19 15:57:20,963 INFO [Thread-108] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: History url is http://MSRA-SA-41.fareast.corp.microsoft.com:19888/jobhistory/job/job_1445182159119_0013
2015-10-19 15:57:20,968 INFO [Thread-108] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Waiting for application to be successfully unregistered.
2015-10-19 15:57:21,969 INFO [Thread-108] org.apache.hadoop.mapreduce.v2.app.rm.RMContainerAllocator: Final Stats: PendingReds:0 ScheduledMaps:0 ScheduledReds:0 AssignedMaps:0 AssignedReds:0 CompletedMaps:10 CompletedReds:1 ContAlloc:15 ContRel:1 HostLocal:10 RackLocal:3
2015-10-19 15:57:21,970 INFO [Thread-108] org.apache.hadoop.mapreduce.v2.app.MRAppMaster: Deleting staging directory hdfs://msra-sa-41:9000 /tmp/hadoop-yarn/staging/msrabi/.staging/job_1445182159119_0013
2015-10-19 15:57:21,975 INFO [Thread-108] org.apache.hadoop.ipc.Server: Stopping server on 17464
2015-10-19 15:57:21,977 INFO [IPC Server listener on 17464] org.apache.hadoop.ipc.Server: Stopping IPC Server listener on 17464
2015-10-19 15:57:21,977 INFO [IPC Server Responder] org.apache.hadoop.ipc.Server: Stopping IPC Server Responder
2015-10-19 15:57:21,978 INFO [TaskHeartbeatHandler PingChecker] org.apache.hadoop.mapreduce.v2.app.TaskHeartbeatHandler: TaskHeartbeatHandler thread interrupted
