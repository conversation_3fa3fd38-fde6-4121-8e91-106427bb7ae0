"""
🔮 DEMO: Using Trained Lithology Classification Models
====================================================
This script demonstrates how to use the trained models on new datasets.

Usage Examples:
1. Load and test models on new data
2. Make predictions with confidence scores
3. Visualize results

Author: ONGC Petrophysical Analysis Team
Date: 2025-01-19
"""

import pandas as pd
import numpy as np
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
import glob
import os
from datetime import datetime

def load_trained_models():
    """Load the latest trained models and preprocessing objects"""
    print("🔄 Loading trained models...")
    
    # Find model files
    model_files = glob.glob("model_results/*_model_*.joblib")
    preprocessing_files = glob.glob("model_results/preprocessing_objects_*.joblib")
    
    if not model_files or not preprocessing_files:
        raise FileNotFoundError("❌ No trained models found! Run the training pipeline first.")
    
    # Load latest models
    latest_timestamp = max([f.split('_')[-1].replace('.joblib', '') for f in model_files])
    
    models = {}
    for model_file in model_files:
        if latest_timestamp in model_file:
            if os.name == 'nt':  # Windows
                model_name = model_file.split('\\')[-1].split('_model_')[0]
            else:  # Unix/Linux
                model_name = model_file.split('/')[-1].split('_model_')[0]
            models[model_name] = joblib.load(model_file)
            print(f"   ✅ Loaded {model_name} model")
    
    # Load preprocessing objects
    latest_preprocessing = max(preprocessing_files, key=os.path.getctime)
    preprocessing_objects = joblib.load(latest_preprocessing)
    print(f"   ✅ Loaded preprocessing objects")
    
    return models, preprocessing_objects

def create_sample_data():
    """Create sample well log data for demonstration"""
    print("🔧 Creating sample well log data...")
    
    np.random.seed(42)
    n_samples = 100
    
    # Create realistic well log data
    data = {
        'WELL': [f'DEMO_WELL_{i//20 + 1}' for i in range(n_samples)],
        'DEPTH_MD': np.arange(2000, 2000 + n_samples * 0.5, 0.5),
        'GR': np.random.normal(80, 30, n_samples),  # Gamma Ray
        'RHOB': np.random.normal(2.3, 0.2, n_samples),  # Bulk Density
        'NPHI': np.random.normal(0.15, 0.1, n_samples),  # Neutron Porosity
        'RDEP': np.random.lognormal(2, 1, n_samples),  # Deep Resistivity
        'DTC': np.random.normal(100, 20, n_samples),  # Delta Time
        'PEF': np.random.normal(3.0, 0.5, n_samples)  # Photoelectric Factor
    }
    
    # Ensure realistic ranges
    data['GR'] = np.clip(data['GR'], 10, 200)
    data['RHOB'] = np.clip(data['RHOB'], 1.5, 3.0)
    data['NPHI'] = np.clip(data['NPHI'], 0.0, 0.6)
    data['RDEP'] = np.clip(data['RDEP'], 0.1, 1000)
    data['DTC'] = np.clip(data['DTC'], 40, 200)
    data['PEF'] = np.clip(data['PEF'], 1.0, 6.0)
    
    df = pd.DataFrame(data)
    print(f"   ✅ Created {len(df)} sample data points")
    
    return df

def make_predictions(models, preprocessing_objects, data, model_name='random_forest'):
    """Make lithology predictions on new data"""
    print(f"🔮 Making predictions with {model_name} model...")
    
    # Extract features
    feature_columns = ['GR', 'RHOB', 'NPHI', 'RDEP', 'DTC', 'PEF']
    available_features = [col for col in feature_columns if col in data.columns]
    
    if len(available_features) < 3:
        raise ValueError(f"Insufficient features. Need at least 3, got {len(available_features)}")
    
    X = data[available_features].copy()
    
    # Preprocess data
    X_imputed = pd.DataFrame(
        preprocessing_objects['imputer'].transform(X),
        columns=available_features,
        index=X.index
    )
    
    X_scaled = pd.DataFrame(
        preprocessing_objects['scaler'].transform(X_imputed),
        columns=available_features,
        index=X_imputed.index
    )
    
    # Make predictions
    model = models[model_name]
    predictions = model.predict(X_scaled)
    probabilities = model.predict_proba(X_scaled)
    
    # Decode predictions
    label_encoder = preprocessing_objects['label_encoder']
    predicted_labels = label_encoder.inverse_transform(predictions)
    max_probabilities = np.max(probabilities, axis=1)
    
    print(f"   ✅ Predictions completed!")
    
    return {
        'predictions': predicted_labels,
        'probabilities': probabilities,
        'max_probabilities': max_probabilities,
        'class_names': label_encoder.classes_,
        'features_used': available_features
    }

def visualize_results(data, results):
    """Create visualizations of the prediction results"""
    print("🎨 Creating visualizations...")
    
    # Add predictions to data
    data_with_predictions = data.copy()
    data_with_predictions['Predicted_Lithology'] = results['predictions']
    data_with_predictions['Confidence'] = results['max_probabilities']
    
    # Create subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Lithology Prediction Results', fontsize=16, fontweight='bold')
    
    # 1. Lithology distribution
    lith_counts = pd.Series(results['predictions']).value_counts()
    axes[0, 0].pie(lith_counts.values, labels=lith_counts.index, autopct='%1.1f%%')
    axes[0, 0].set_title('Predicted Lithology Distribution')
    
    # 2. Confidence distribution
    axes[0, 1].hist(results['max_probabilities'], bins=20, alpha=0.7, color='skyblue')
    axes[0, 1].set_xlabel('Prediction Confidence')
    axes[0, 1].set_ylabel('Frequency')
    axes[0, 1].set_title('Confidence Score Distribution')
    axes[0, 1].axvline(np.mean(results['max_probabilities']), color='red', 
                       linestyle='--', label=f'Mean: {np.mean(results["max_probabilities"]):.3f}')
    axes[0, 1].legend()
    
    # 3. Depth vs Lithology
    if 'DEPTH_MD' in data.columns:
        unique_lithologies = np.unique(results['predictions'])
        colors = plt.cm.Set3(np.linspace(0, 1, len(unique_lithologies)))
        
        for i, lith in enumerate(unique_lithologies):
            mask = data_with_predictions['Predicted_Lithology'] == lith
            axes[1, 0].scatter(np.ones(mask.sum()) * i, 
                             data_with_predictions.loc[mask, 'DEPTH_MD'],
                             c=[colors[i]], label=str(lith), alpha=0.7, s=20)
        
        axes[1, 0].set_xlabel('Lithology')
        axes[1, 0].set_ylabel('Depth (MD)')
        axes[1, 0].set_title('Lithology vs Depth')
        axes[1, 0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        axes[1, 0].invert_yaxis()
    
    # 4. Feature importance (if available)
    if 'GR' in data.columns and 'RHOB' in data.columns:
        scatter = axes[1, 1].scatter(data['GR'], data['RHOB'], 
                                   c=results['max_probabilities'], 
                                   cmap='viridis', alpha=0.7)
        axes[1, 1].set_xlabel('Gamma Ray (GR)')
        axes[1, 1].set_ylabel('Bulk Density (RHOB)')
        axes[1, 1].set_title('GR vs RHOB (colored by confidence)')
        plt.colorbar(scatter, ax=axes[1, 1], label='Confidence')
    
    plt.tight_layout()
    
    # Save plot
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plot_filename = f"demo_predictions_{timestamp}.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"   ✅ Visualization saved as: {plot_filename}")
    
    plt.show()

def print_summary(data, results):
    """Print a summary of the prediction results"""
    print("\n📊 PREDICTION SUMMARY")
    print("=" * 50)
    
    predictions = results['predictions']
    confidences = results['max_probabilities']
    
    print(f"📈 Total samples: {len(predictions):,}")
    print(f"📈 Average confidence: {np.mean(confidences):.3f}")
    print(f"📈 Min confidence: {np.min(confidences):.3f}")
    print(f"📈 Max confidence: {np.max(confidences):.3f}")
    
    print(f"\n🪨 LITHOLOGY DISTRIBUTION:")
    lith_counts = pd.Series(predictions).value_counts()
    for lith, count in lith_counts.items():
        pct = (count / len(predictions)) * 100
        print(f"   🔸 {lith}: {count:,} samples ({pct:.1f}%)")
    
    print(f"\n📊 CONFIDENCE DISTRIBUTION:")
    confidence_bins = pd.cut(confidences, bins=[0, 0.5, 0.7, 0.9, 1.0], 
                           labels=['Low (0-0.5)', 'Medium (0.5-0.7)', 
                                  'High (0.7-0.9)', 'Very High (0.9-1.0)'])
    conf_counts = confidence_bins.value_counts()
    for conf_level, count in conf_counts.items():
        pct = (count / len(predictions)) * 100
        print(f"   📊 {conf_level}: {count:,} samples ({pct:.1f}%)")

def main():
    """Main demonstration function"""
    print("🔮 LITHOLOGY CLASSIFICATION MODEL DEMO")
    print("=" * 60)
    
    try:
        # 1. Load trained models
        models, preprocessing_objects = load_trained_models()
        
        # 2. Create or load sample data
        print("\n📊 PREPARING TEST DATA")
        print("=" * 30)
        
        # Option 1: Use sample data
        sample_data = create_sample_data()
        
        # Option 2: Load your own data (uncomment the line below)
        # sample_data = pd.read_csv("your_test_data.csv")
        
        # 3. Make predictions
        print("\n🔮 MAKING PREDICTIONS")
        print("=" * 30)
        
        results = make_predictions(models, preprocessing_objects, sample_data)
        
        # 4. Print summary
        print_summary(sample_data, results)
        
        # 5. Create visualizations
        print("\n🎨 CREATING VISUALIZATIONS")
        print("=" * 30)
        
        visualize_results(sample_data, results)
        
        # 6. Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"demo_predictions_{timestamp}.csv"
        
        sample_data['Predicted_Lithology'] = results['predictions']
        sample_data['Confidence'] = results['max_probabilities']
        sample_data.to_csv(output_file, index=False)
        
        print(f"\n💾 Results saved to: {output_file}")
        print("\n🎉 DEMO COMPLETED SUCCESSFULLY!")
        print("💡 Your trained models are working perfectly!")
        
    except Exception as e:
        print(f"❌ Error in demo: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
