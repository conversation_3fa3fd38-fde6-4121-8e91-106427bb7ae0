2015-10-17 23:11:21,739 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 23:11:21,977 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 23:11:21,978 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-17 23:11:22,032 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 23:11:22,032 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445094324383_0001, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@3db9b677)
2015-10-17 23:11:22,372 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 23:11:23,115 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445094324383_0001
2015-10-17 23:11:24,750 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 23:11:26,232 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 23:11:26,285 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@3aa191be
2015-10-17 23:11:26,343 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@1cbd8f1
2015-10-17 23:11:26,401 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=140928608, maxSingleShuffleLimit=35232152, mergeThreshold=93012888, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-17 23:11:26,406 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445094324383_0001_r_000000_0 Thread started: EventFetcher for fetching Map Completion Events
2015-10-17 23:11:26,429 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-17 23:11:26,430 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-17 23:11:26,431 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-17 23:11:26,431 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 23:11:26,433 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445094324383_0001_r_000000_0: Got 7 new map-outputs
2015-10-17 23:11:26,549 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445094324383_0001&reduce=0&map=attempt_1445094324383_0001_m_000009_0 sent hash and received reply
2015-10-17 23:11:26,552 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445094324383_0001&reduce=0&map=attempt_1445094324383_0001_m_000002_0 sent hash and received reply
2015-10-17 23:11:26,560 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0001_m_000009_0: Shuffling to disk since 172334804 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 23:11:26,576 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0001_m_000002_0: Shuffling to disk since 216991624 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 23:11:26,576 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445094324383_0001_m_000009_0 decomp: 172334804 len: 172334808 to DISK
2015-10-17 23:11:26,587 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445094324383_0001_m_000002_0 decomp: 216991624 len: 216991628 to DISK
2015-10-17 23:11:31,447 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445094324383_0001_r_000000_0: Got 2 new map-outputs
2015-10-17 23:11:32,347 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 172334808 bytes from map-output for attempt_1445094324383_0001_m_000009_0
2015-10-17 23:11:32,374 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 5943ms
2015-10-17 23:11:32,374 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 2 to fetcher#1
2015-10-17 23:11:32,374 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 2 of 2 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-17 23:11:33,113 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445094324383_0001&reduce=0&map=attempt_1445094324383_0001_m_000001_0,attempt_1445094324383_0001_m_000008_0 sent hash and received reply
2015-10-17 23:11:33,116 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0001_m_000001_0: Shuffling to disk since 217009502 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 23:11:33,121 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445094324383_0001_m_000001_0 decomp: 217009502 len: 217009506 to DISK
2015-10-17 23:11:34,329 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216991628 bytes from map-output for attempt_1445094324383_0001_m_000002_0
2015-10-17 23:11:34,670 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 8239ms
2015-10-17 23:11:34,670 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 5 to fetcher#5
2015-10-17 23:11:34,670 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 5 of 5 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-17 23:11:34,692 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445094324383_0001&reduce=0&map=attempt_1445094324383_0001_m_000003_0,attempt_1445094324383_0001_m_000006_0,attempt_1445094324383_0001_m_000005_0,attempt_1445094324383_0001_m_000004_0,attempt_1445094324383_0001_m_000007_0 sent hash and received reply
2015-10-17 23:11:34,692 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0001_m_000003_0: Shuffling to disk since 216972750 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 23:11:34,702 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445094324383_0001_m_000003_0 decomp: 216972750 len: 216972754 to DISK
2015-10-17 23:11:36,999 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217009506 bytes from map-output for attempt_1445094324383_0001_m_000001_0
2015-10-17 23:11:37,007 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0001_m_000008_0: Shuffling to disk since 217015228 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 23:11:37,011 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445094324383_0001_m_000008_0 decomp: 217015228 len: 217015232 to DISK
2015-10-17 23:11:39,116 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216972754 bytes from map-output for attempt_1445094324383_0001_m_000003_0
2015-10-17 23:11:39,122 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0001_m_000006_0: Shuffling to disk since 217011663 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 23:11:39,126 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445094324383_0001_m_000006_0 decomp: 217011663 len: 217011667 to DISK
2015-10-17 23:11:40,754 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217015232 bytes from map-output for attempt_1445094324383_0001_m_000008_0
2015-10-17 23:11:40,763 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 8388ms
2015-10-17 23:11:43,045 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217011667 bytes from map-output for attempt_1445094324383_0001_m_000006_0
2015-10-17 23:11:43,051 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0001_m_000005_0: Shuffling to disk since 216990140 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 23:11:43,054 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445094324383_0001_m_000005_0 decomp: 216990140 len: 216990144 to DISK
2015-10-17 23:11:47,422 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216990144 bytes from map-output for attempt_1445094324383_0001_m_000005_0
2015-10-17 23:11:47,876 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0001_m_000004_0: Shuffling to disk since 216999709 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 23:11:47,878 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445094324383_0001_m_000004_0 decomp: 216999709 len: 216999713 to DISK
2015-10-17 23:11:51,196 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216999713 bytes from map-output for attempt_1445094324383_0001_m_000004_0
2015-10-17 23:11:51,203 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0001_m_000007_0: Shuffling to disk since 216976206 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 23:11:51,206 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445094324383_0001_m_000007_0 decomp: 216976206 len: 216976210 to DISK
2015-10-17 23:11:57,216 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216976210 bytes from map-output for attempt_1445094324383_0001_m_000007_0
2015-10-17 23:11:57,230 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 22560ms
2015-10-17 23:13:02,607 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445094324383_0001_r_000000_0: Got 1 new map-outputs
2015-10-17 23:13:02,607 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-17 23:13:02,608 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-17 23:13:02,620 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445094324383_0001&reduce=0&map=attempt_1445094324383_0001_m_000000_1 sent hash and received reply
2015-10-17 23:13:02,620 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445094324383_0001_m_000000_1: Shuffling to disk since 216988123 is greater than maxSingleShuffleLimit (35232152)
2015-10-17 23:13:02,631 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445094324383_0001_m_000000_1 decomp: 216988123 len: 216988127 to DISK
2015-10-17 23:13:07,581 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216988127 bytes from map-output for attempt_1445094324383_0001_m_000000_1
2015-10-17 23:13:07,587 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: EventFetcher is interrupted.. Returning
2015-10-17 23:13:07,587 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 4978ms
2015-10-17 23:13:07,590 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: finalMerge called with 0 in-memory map-outputs and 10 on-disk map-outputs
2015-10-17 23:13:07,597 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 10 files, 2125289789 bytes from disk
2015-10-17 23:13:07,598 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 0 segments, 0 bytes from memory into reduce
2015-10-17 23:13:07,602 INFO [main] org.apache.hadoop.mapred.Merger: Merging 10 sorted segments
2015-10-17 23:13:07,613 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 10 segments left of total size: 2125289689 bytes
2015-10-17 23:13:07,719 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: mapred.skip.on is deprecated. Instead, use mapreduce.job.skiprecords
2015-10-17 23:20:30,955 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445094324383_0001_r_000000_0 is done. And is in the process of committing
2015-10-17 23:20:30,995 INFO [main] org.apache.hadoop.mapred.Task: Task attempt_1445094324383_0001_r_000000_0 is allowed to commit now
2015-10-17 23:20:31,023 INFO [main] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Saved output of task 'attempt_1445094324383_0001_r_000000_0' to hdfs://msra-sa-41:9000/out/out4/_temporary/1/task_1445094324383_0001_r_000000
2015-10-17 23:20:31,051 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445094324383_0001_r_000000_0' done.
