{"cells": [{"cell_type": "code", "execution_count": null, "id": "b47d9a7a", "metadata": {}, "outputs": [], "source": ["# import pandas as pd\n", "# import numpy as np\n", "# from sklearn.model_selection import train_test_split\n", "# from sklearn.impute import SimpleImputer\n", "# from sklearn.preprocessing import StandardScaler\n", "# from sklearn.ensemble import RandomForestRegressor\n", "# from sklearn.metrics import mean_squared_error, r2_score\n", "# import matplotlib.pyplot as plt\n", "\n", "# # Load your well log data\n", "# df = pd.read_csv(\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/CSV Data/train.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "eb4d88b1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['WELL;DEPTH_MD;X_LOC;Y_LOC;Z_LOC;GROUP;FORMATION;CALI;RSHA;RMED;RDEP;RHOB;GR;SGR;NPHI;PEF;DTC;SP;BS;ROP;DTS;DCAL;DRHO;MUDWEIGHT;RMIC;ROPA;RXO;FORCE_2020_LITHOFACIES_LITHOLOGY;FORCE_2020_LITHOFACIES_CONFIDENCE']\n"]}], "source": ["# print(df.columns.tolist())\n"]}, {"cell_type": "code", "execution_count": 7, "id": "9e50444b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Columns in dataset: ['WELL;DEPTH_MD;X_LOC;Y_LOC;Z_LOC;GROUP;FORMATION;CALI;RSHA;RMED;RDEP;RHOB;GR;SGR;NPHI;PEF;DTC;SP;BS;ROP;DTS;DCAL;DRHO;MUDWEIGHT;RMIC;ROPA;RXO;FORCE_2020_LITHOFACIES_LITHOLOGY;FORCE_2020_LITHOFACIES_CONFIDENCE']\n", "🔍 Dataset shape before processing: (1170511, 1)\n"]}, {"ename": "KeyError", "evalue": "['NPHI']", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_30280\\1222174995.py\u001b[0m in \u001b[0;36m?\u001b[1;34m()\u001b[0m\n\u001b[0;32m     30\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     31\u001b[0m \u001b[0mfeatures\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;33m[\u001b[0m\u001b[0mcol\u001b[0m \u001b[1;32mfor\u001b[0m \u001b[0mcol\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mavailable_features\u001b[0m \u001b[1;32mif\u001b[0m \u001b[0mcol\u001b[0m \u001b[1;33m!=\u001b[0m \u001b[0mtarget_curve\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     32\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     33\u001b[0m \u001b[1;31m# Drop rows where target is missing\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 34\u001b[1;33m \u001b[0mdf\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mdf\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdropna\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0msubset\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mtarget_curve\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     35\u001b[0m \u001b[0mprint\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"✅ Data shape after dropping NaNs in target:\"\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mdf\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mshape\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     36\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     37\u001b[0m \u001b[1;31m# Step 1: Separate Features and Target\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\frame.py\u001b[0m in \u001b[0;36m?\u001b[1;34m(self, axis, how, thresh, subset, inplace, ignore_index)\u001b[0m\n\u001b[0;32m   6666\u001b[0m             \u001b[0max\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_get_axis\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0magg_axis\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   6667\u001b[0m             \u001b[0mindices\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0max\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mget_indexer_for\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0msubset\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   6668\u001b[0m             \u001b[0mcheck\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mindices\u001b[0m \u001b[1;33m==\u001b[0m \u001b[1;33m-\u001b[0m\u001b[1;36m1\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   6669\u001b[0m             \u001b[1;32mif\u001b[0m \u001b[0mcheck\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0many\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 6670\u001b[1;33m                 \u001b[1;32mraise\u001b[0m \u001b[0mKeyError\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnp\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0marray\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0msubset\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mcheck\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtolist\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   6671\u001b[0m             \u001b[0magg_obj\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtake\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mindices\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0maxis\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0magg_axis\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   6672\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   6673\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0mthresh\u001b[0m \u001b[1;32mis\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[0mlib\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mno_default\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31m<PERSON>eyError\u001b[0m: ['NPHI']"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "import matplotlib.pyplot as plt\n", "\n", "# Load your well log data\n", "file_path = r\"C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/CSV Data/train.csv\"\n", "df = pd.read_csv(file_path)\n", "\n", "# Display basic info\n", "print(\"🔍 Columns in dataset:\", df.columns.tolist())\n", "print(\"🔍 Dataset shape before processing:\", df.shape)\n", "\n", "# --- CONFIGURATION --- #\n", "target_curve = 'NPHI'  # Change this to the log curve you want to predict\n", "\n", "# All well log features (adjust if needed)\n", "log_features = ['CALI', 'RSHA', 'RMED', 'RDEP', 'RHOB', 'GR', 'SGR',\n", "                'NPHI', 'PEF', 'DTC', 'SP', 'BS', 'ROP', 'DTS',\n", "                '<PERSON><PERSON>', '<PERSON>H<PERSON>', 'RMI<PERSON>', 'ROP<PERSON>', 'RX<PERSON>']\n", "\n", "# Ensure all selected features exist in the dataset\n", "available_features = [f for f in log_features if f in df.columns]\n", "# if target_curve not in df.columns:\n", "#     raise ValueError(f\"❌ Target curve \"{target_curve}\" not found in dataset!\")\n", "\n", "features = [col for col in available_features if col != target_curve]\n", "\n", "# Drop rows where target is missing\n", "df = df.dropna(subset=[target_curve])\n", "print(\"✅ Data shape after dropping NaNs in target:\", df.shape)\n", "\n", "# Step 1: Separate Features and Target\n", "X = df[features]\n", "y = df[target_curve]\n", "\n", "# Step 2: <PERSON>le missing values in features\n", "imputer = SimpleImputer(strategy='mean')\n", "X_imputed = imputer.fit_transform(X)\n", "\n", "# Step 3: <PERSON>\n", "scaler = StandardScaler()\n", "X_scaled = scaler.fit_transform(X_imputed)\n", "\n", "# Step 4: Train-Test Split\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X_scaled, y, test_size=0.2, random_state=42\n", ")\n", "\n", "# Step 5: Train the Model\n", "model = RandomForestRegressor(n_estimators=100, random_state=42)\n", "model.fit(X_train, y_train)\n", "\n", "# Step 6: Predict and Evaluate\n", "y_pred = model.predict(X_test)\n", "\n", "mse = mean_squared_error(y_test, y_pred)\n", "r2 = r2_score(y_test, y_pred)\n", "\n", "print(f\"\\n🔍 Model Performance on '{target_curve}' Prediction:\")\n", "print(f\"   ➤ MSE: {mse:.4f}\")\n", "print(f\"   ➤ R² Score: {r2:.4f}\")\n", "\n", "# Step 7: Plot Actual vs Predicted\n", "plt.figure(figsize=(8, 6))\n", "plt.scatter(y_test, y_pred, alpha=0.5, edgecolor='k')\n", "plt.plot([y.min(), y.max()], [y.min(), y.max()], 'r--', lw=2)\n", "plt.xlabel('Actual Values')\n", "plt.ylabel('Predicted Values')\n", "plt.title(f'{target_curve} Prediction: Actual vs Predicted')\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}