2015-10-19 17:39:25,140 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-19 17:39:25,280 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-19 17:39:25,280 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-19 17:39:25,311 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-19 17:39:25,311 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445182159119_0016, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@1623b78d)
2015-10-19 17:39:25,483 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-19 17:39:26,140 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445182159119_0016
2015-10-19 17:39:26,608 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-19 17:39:27,562 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-19 17:39:27,593 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@db57326
2015-10-19 17:39:27,968 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/pageinput2.txt:402653184+134217728
2015-10-19 17:39:28,093 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-19 17:39:28,093 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-19 17:39:28,093 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-19 17:39:28,093 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-19 17:39:28,093 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-19 17:39:28,093 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-19 17:39:32,109 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:39:32,109 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 48271024; bufvoid = 104857600
2015-10-19 17:39:32,109 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 17310640(69242560); length = 8903757/6553600
2015-10-19 17:39:32,109 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 57339776 kvi 14334940(57339760)
2015-10-19 17:39:42,297 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-19 17:39:42,297 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 57339776 kv 14334940(57339760) kvi 12140764(48563056)
2015-10-19 17:39:44,922 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:39:44,922 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 57339776; bufend = 743078; bufvoid = 104857600
2015-10-19 17:39:44,922 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14334940(57339760); kvend = 5428644(21714576); length = 8906297/6553600
2015-10-19 17:39:44,922 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 9811814 kvi 2452948(9811792)
2015-10-19 17:39:55,235 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-19 17:39:55,235 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 9811814 kv 2452948(9811792) kvi 244148(976592)
2015-10-19 17:39:58,032 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:39:58,032 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 9811814; bufend = 58036090; bufvoid = 104857600
2015-10-19 17:39:58,032 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 2452948(9811792); kvend = 19751904(79007616); length = 8915445/6553600
2015-10-19 17:39:58,032 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 67104842 kvi 16776204(67104816)
2015-10-19 17:40:08,939 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-19 17:40:08,939 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 67104842 kv 16776204(67104816) kvi 14566280(58265120)
2015-10-19 17:40:11,252 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:40:11,252 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 67104842; bufend = 10444035; bufvoid = 104857600
2015-10-19 17:40:11,252 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 16776204(67104816); kvend = 7853884(31415536); length = 8922321/6553600
2015-10-19 17:40:11,252 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 19512771 kvi 4878188(19512752)
2015-10-19 17:40:20,783 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-19 17:40:20,783 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 19512771 kv 4878188(19512752) kvi 2672040(10688160)
2015-10-19 17:40:23,549 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:40:23,549 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 19512771; bufend = 67736095; bufvoid = 104857600
2015-10-19 17:40:23,549 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 4878188(19512752); kvend = 22176904(88707616); length = 8915685/6553600
2015-10-19 17:40:23,549 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 76804847 kvi 19201204(76804816)
2015-10-19 17:40:33,881 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-19 17:40:33,896 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 76804847 kv 19201204(76804816) kvi 17013340(68053360)
2015-10-19 17:40:36,365 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:40:36,365 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 76804847; bufend = 20212744; bufvoid = 104857600
2015-10-19 17:40:36,365 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 19201204(76804816); kvend = 10296064(41184256); length = 8905141/6553600
2015-10-19 17:40:36,365 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29281496 kvi 7320368(29281472)
2015-10-19 17:40:46,242 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-19 17:40:46,242 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29281496 kv 7320368(29281472) kvi 5113468(20453872)
2015-10-19 17:40:48,320 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:40:48,320 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29281496; bufend = 77550660; bufvoid = 104857600
2015-10-19 17:40:48,320 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7320368(29281472); kvend = 24630548(98522192); length = 8904221/6553600
2015-10-19 17:40:48,320 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 86619412 kvi 21654848(86619392)
2015-10-19 17:40:57,398 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-19 17:40:57,398 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 86619412 kv 21654848(86619392) kvi 19451324(77805296)
2015-10-19 17:40:58,648 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-19 17:40:58,648 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-19 17:40:58,648 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 86619412; bufend = 20122746; bufvoid = 104857600
2015-10-19 17:40:58,648 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 21654848(86619392); kvend = 14553008(58212032); length = 7101841/6553600
2015-10-19 17:41:07,493 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 7
2015-10-19 17:41:07,524 INFO [main] org.apache.hadoop.mapred.Merger: Merging 8 sorted segments
2015-10-19 17:41:07,524 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 8 segments left of total size: 288694349 bytes
2015-10-19 17:41:34,797 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445182159119_0016_m_000003_0 is done. And is in the process of committing
2015-10-19 17:41:34,859 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445182159119_0016_m_000003_0' done.
