"""
Configuration file for the Offline Semantic Search Application
"""

import os

# Model Configuration
SBERT_MODEL_NAME = "all-MiniLM-L6-v2"  # Lightweight, good performance
# Alternative models:
# "all-mpnet-base-v2"  # Better quality, larger size
# "paraphrase-MiniLM-L6-v2"  # Good for paraphrase detection

# File Paths
DATA_DIR = "data"
EMBEDDINGS_DIR = "embeddings"
MODELS_DIR = "models"
RESULTS_DIR = "results"

# Create directories if they don't exist
for directory in [DATA_DIR, EMBEDDINGS_DIR, MODELS_DIR, RESULTS_DIR]:
    os.makedirs(directory, exist_ok=True)

# Search Configuration
DEFAULT_TOP_K = 5
MAX_TOP_K = 20
SIMILARITY_THRESHOLD = 0.3

# UI Configuration
STREAMLIT_PAGE_TITLE = "ONGC Knowledge Management System"
STREAMLIT_PAGE_ICON = "🔍"
STREAMLIT_LAYOUT = "wide"

# Dataset Configuration
SAMPLE_DATASET_FILE = os.path.join(DATA_DIR, "technical_articles.json")
EMBEDDINGS_FILE = os.path.join(EMBEDDINGS_DIR, "article_embeddings.npy")
FAISS_INDEX_FILE = os.path.join(EMBEDDINGS_DIR, "faiss_index.bin")
METADATA_FILE = os.path.join(EMBEDDINGS_DIR, "metadata.json")

# Logging Configuration
LOG_LEVEL = "INFO"
LOG_FILE = "semantic_search.log"
