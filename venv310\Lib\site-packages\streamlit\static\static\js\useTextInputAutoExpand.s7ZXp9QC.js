import{bv as v,bw as j,bZ as H,r as s,cl as $,cm as A,z as F}from"./index.BTGIlECR.js";import{g as N,a as z,b as B,B as R}from"./base-input.Dl5fJ2xw.js";function w(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?w(Object(r),!0).forEach(function(n){C(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function C(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var O=v("div",function(e){return l(l({},N(l(l({$positive:!1},e),{},{$hasIconTrailing:!1}))),{},{width:e.$resize?"fit-content":"100%"})});O.displayName="StyledTextAreaRoot";O.displayName="StyledTextAreaRoot";var _=v("div",function(e){return z(l({$positive:!1},e))});_.displayName="StyledTextareaContainer";_.displayName="StyledTextareaContainer";var S=v("textarea",function(e){return l(l({},B(e)),{},{resize:e.$resize||"none"})});S.displayName="StyledTextarea";S.displayName="StyledTextarea";function g(e){"@babel/helpers - typeof";return g=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},g(e)}function y(){return y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},y.apply(this,arguments)}function D(e,t){return L(e)||U(e,t)||M(e,t)||K()}function K(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function M(e,t){if(e){if(typeof e=="string")return P(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return P(e,t)}}function P(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function U(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n=[],o=!0,i=!1,a,u;try{for(r=r.call(e);!(o=(a=r.next()).done)&&(n.push(a.value),!(t&&n.length===t));o=!0);}catch(c){i=!0,u=c}finally{try{!o&&r.return!=null&&r.return()}finally{if(i)throw u}}return n}}function L(e){if(Array.isArray(e))return e}function q(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function V(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Z(e,t,r){return t&&V(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function G(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&m(e,t)}function m(e,t){return m=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,o){return n.__proto__=o,n},m(e,t)}function W(e){var t=Y();return function(){var n=d(e),o;if(t){var i=d(this).constructor;o=Reflect.construct(n,arguments,i)}else o=n.apply(this,arguments);return X(this,o)}}function X(e,t){if(t&&(g(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return f(e)}function f(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Y(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function d(e){return d=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},d(e)}function p(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var J=function(e){G(r,e);var t=W(r);function r(){var n;q(this,r);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return n=t.call.apply(t,[this].concat(i)),p(f(n),"state",{isFocused:n.props.autoFocus||!1}),p(f(n),"onFocus",function(u){n.setState({isFocused:!0}),n.props.onFocus(u)}),p(f(n),"onBlur",function(u){n.setState({isFocused:!1}),n.props.onBlur(u)}),n}return Z(r,[{key:"render",value:function(){var o=this.props.overrides,i=o===void 0?{}:o,a=j(i.Root,O),u=D(a,2),c=u[0],h=u[1],b=H({Input:{component:S},InputContainer:{component:_}},i);return s.createElement(c,y({"data-baseweb":"textarea",$isFocused:this.state.isFocused,$isReadOnly:this.props.readOnly,$disabled:this.props.disabled,$error:this.props.error,$positive:this.props.positive,$required:this.props.required,$resize:this.props.resize},h),s.createElement(R,y({},this.props,{type:A.textarea,overrides:b,onFocus:this.onFocus,onBlur:this.onBlur,resize:this.props.resize})))}}]),r}(s.Component);p(J,"defaultProps",{autoFocus:!1,disabled:!1,readOnly:!1,error:!1,name:"",onBlur:function(){},onChange:function(){},onKeyDown:function(){},onKeyPress:function(){},onKeyUp:function(){},onFocus:function(){},overrides:{},placeholder:"",required:!1,rows:3,size:$.default});const Q=6.5,T=1,k=e=>{let t=0;const{current:r}=e;return r&&(r.style.height="auto",t=r.scrollHeight,r.style.height=""),t},ee=(e,t,r)=>e>0&&r.current?Math.abs(e-t)>T:!1,te=(e,t)=>{if(e.current&&t.current){const{offsetHeight:r}=e.current;t.current.minHeight=r,t.current.maxHeight=r*Q}},re=(e,t,r)=>e?`${t+T}px`:r?String(r):"",ne=e=>e?`${e}px`:"",ae=({textareaRef:e,dependencies:t=[]})=>{const r=F(),n=s.useRef({minHeight:0,maxHeight:0}),[o,i]=s.useState(0),[a,u]=s.useState(!1),c=s.useCallback(()=>{i(k(e))},[e,i]);s.useLayoutEffect(()=>{e.current&&te(e,n)},[e]),s.useLayoutEffect(()=>{const{minHeight:I}=n.current;u(ee(o,I,e))},[o,e]),s.useLayoutEffect(()=>{c()},[e,c,...t]);const{maxHeight:h}=n.current,b=r.sizes.minElementHeight,x=re(a,o,b),E=ne(h);return{isExtended:a,height:x,maxHeight:E,updateScrollHeight:c}};export{J as T,ae as u};
