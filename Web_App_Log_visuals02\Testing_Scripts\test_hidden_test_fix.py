"""
Quick test to verify the hidden_test.csv fix works
"""

import pandas as pd
import sys
import os

# Add the current directory to Python path
sys.path.append('.')

# Import the intelligent data processing functions
try:
    from well_log_app import smart_load_csv, validate_and_process_data
    print("✅ Successfully imported functions from well_log_app.py")
except ImportError as e:
    print(f"❌ Failed to import functions: {e}")
    sys.exit(1)

def test_hidden_test_processing():
    """Test processing of hidden_test.csv file"""
    print("\n🧪 Testing hidden_test.csv Processing")
    print("=" * 50)
    
    # Try both the demo file and original file
    test_files = [
        "demo_hidden_test.csv",
        "../Dataset/CSV Data/hidden_test.csv"
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            print(f"\n📁 Testing: {file_path}")
            
            try:
                # Simulate file upload by reading the file
                with open(file_path, 'rb') as f:
                    file_content = f.read()
                
                # Create a mock uploaded file object
                class MockUploadedFile:
                    def __init__(self, content):
                        self.content = content
                        self.position = 0
                    
                    def getvalue(self):
                        return self.content
                    
                    def seek(self, position):
                        self.position = position
                        return position
                    
                    def read(self):
                        return self.content[self.position:]
                
                mock_file = MockUploadedFile(file_content)
                
                # Test smart CSV loading
                print("   🔄 Testing smart CSV loading...")
                df_raw, separator, load_error = smart_load_csv(mock_file)
                
                if load_error:
                    print(f"   ❌ Load error: {load_error}")
                    continue
                
                print(f"   ✅ Loaded successfully: {df_raw.shape}")
                print(f"   📄 Separator detected: '{separator}'")
                
                # Store original length
                original_length = len(df_raw)
                print(f"   📊 Original length: {original_length:,} rows")
                
                # Test data processing
                print("   🔧 Testing data processing...")
                df_processed, processing_info = validate_and_process_data(df_raw)
                
                print(f"   ✅ Processing successful: {df_processed.shape}")
                print(f"   🔄 Mapped columns: {len(processing_info['mapped_columns'])}")
                print(f"   🧪 Synthetic columns: {len(processing_info['synthetic_columns'])}")
                print(f"   📊 Final length: {len(df_processed):,} rows")
                
                # Show some mapped columns
                print(f"   📋 Sample mappings:")
                for orig, standard in list(processing_info['mapped_columns'].items())[:5]:
                    print(f"      📍 {orig} → {standard}")
                
                # Test that we have the required columns
                required_cols = ['DEPTH_MD', 'GR', 'RDEP', 'RHOB', 'NPHI', 'CALI', 'DTC', 'PEF']
                available_cols = [col for col in required_cols if col in df_processed.columns]
                print(f"   ✅ Available required columns: {len(available_cols)}/{len(required_cols)}")
                
                # Show sample data
                if available_cols:
                    print(f"   📈 Sample data:")
                    sample_cols = available_cols[:4]  # Show first 4 columns
                    print(df_processed[sample_cols].head(3).to_string(index=False))
                
                print(f"   🎉 {file_path} - TEST PASSED!")
                return True
                
            except Exception as e:
                print(f"   ❌ Error processing {file_path}: {e}")
                import traceback
                traceback.print_exc()
                continue
    
    print(f"\n❌ No valid test files found or all tests failed")
    return False

def main():
    """Main test function"""
    print("🔧 HIDDEN TEST CSV FIX VERIFICATION")
    print("=" * 60)
    
    # Test the processing
    success = test_hidden_test_processing()
    
    if success:
        print(f"\n🎉 FIX VERIFICATION SUCCESSFUL!")
        print(f"✅ The 'original_length' error has been resolved")
        print(f"✅ hidden_test.csv can now be processed successfully")
        print(f"🚀 You can now upload the file to the Streamlit app")
        print(f"📱 App URL: http://localhost:8501")
    else:
        print(f"\n❌ Fix verification failed")
        print(f"Please check the error messages above")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
