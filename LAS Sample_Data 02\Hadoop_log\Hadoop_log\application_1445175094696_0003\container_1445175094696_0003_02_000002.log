2015-10-18 21:51:48,634 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-18 21:51:48,693 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-18 21:51:48,693 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: ReduceTask metrics system started
2015-10-18 21:51:48,708 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-18 21:51:48,708 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445175094696_0003, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@78093f60)
2015-10-18 21:51:48,806 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-18 21:51:49,039 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445175094696_0003
2015-10-18 21:51:49,594 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-18 21:51:50,018 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-18 21:51:50,049 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@7fc3caba
2015-10-18 21:51:50,080 INFO [main] org.apache.hadoop.mapred.ReduceTask: Using ShuffleConsumerPlugin: org.apache.hadoop.mapreduce.task.reduce.Shuffle@5bef7247
2015-10-18 21:51:50,099 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: MergerManager: memoryLimit=140928608, maxSingleShuffleLimit=35232152, mergeThreshold=93012888, ioSortFactor=10, memToMemMergeOutputsThreshold=10
2015-10-18 21:51:50,100 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0003_r_000000_1000 Thread started: EventFetcher for fetching Map Completion Events
2015-10-18 21:51:50,110 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 1 to fetcher#5
2015-10-18 21:51:50,110 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:51:50,111 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-18 21:51:50,111 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-18 21:51:50,111 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning 04DN8IQ.fareast.corp.microsoft.com:13562 with 1 to fetcher#4
2015-10-18 21:51:50,111 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: attempt_1445175094696_0003_r_000000_1000: Got 10 new map-outputs
2015-10-18 21:51:50,111 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to 04DN8IQ.fareast.corp.microsoft.com:13562 to fetcher#4
2015-10-18 21:51:50,132 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0003&reduce=0&map=attempt_1445175094696_0003_m_000000_0 sent hash and received reply
2015-10-18 21:51:50,132 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0003&reduce=0&map=attempt_1445175094696_0003_m_000006_1 sent hash and received reply
2015-10-18 21:51:50,135 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0003_m_000000_0: Shuffling to disk since 216988123 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:51:50,140 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0003_m_000006_1: Shuffling to disk since 217011663 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:51:50,140 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0003_m_000000_0 decomp: 216988123 len: 216988127 to DISK
2015-10-18 21:51:50,143 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445175094696_0003_m_000006_1 decomp: 217011663 len: 217011667 to DISK
2015-10-18 21:51:50,170 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0003&reduce=0&map=attempt_1445175094696_0003_m_000009_0 sent hash and received reply
2015-10-18 21:51:50,183 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0003_m_000009_0: Shuffling to disk since 172334804 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:51:50,188 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#4 about to shuffle output of map attempt_1445175094696_0003_m_000009_0 decomp: 172334804 len: 172334808 to DISK
2015-10-18 21:51:51,416 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216988127 bytes from map-output for attempt_1445175094696_0003_m_000000_0
2015-10-18 21:51:51,428 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 1318ms
2015-10-18 21:51:51,428 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-41.fareast.corp.microsoft.com:13562 with 6 to fetcher#5
2015-10-18 21:51:51,428 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 6 of 6 to MSRA-SA-41.fareast.corp.microsoft.com:13562 to fetcher#5
2015-10-18 21:51:51,434 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0003&reduce=0&map=attempt_1445175094696_0003_m_000001_0,attempt_1445175094696_0003_m_000002_0,attempt_1445175094696_0003_m_000003_0,attempt_1445175094696_0003_m_000004_0,attempt_1445175094696_0003_m_000005_0,attempt_1445175094696_0003_m_000008_1 sent hash and received reply
2015-10-18 21:51:51,435 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0003_m_000001_0: Shuffling to disk since 217009502 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:51:51,437 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0003_m_000001_0 decomp: 217009502 len: 217009506 to DISK
2015-10-18 21:51:52,074 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217011667 bytes from map-output for attempt_1445175094696_0003_m_000006_1
2015-10-18 21:51:52,085 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 1974ms
2015-10-18 21:51:52,085 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: Assigning MSRA-SA-39.fareast.corp.microsoft.com:13562 with 1 to fetcher#1
2015-10-18 21:51:52,085 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: assigned 1 of 1 to MSRA-SA-39.fareast.corp.microsoft.com:13562 to fetcher#1
2015-10-18 21:51:52,092 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: for url=13562/mapOutput?job=job_1445175094696_0003&reduce=0&map=attempt_1445175094696_0003_m_000007_0 sent hash and received reply
2015-10-18 21:51:52,093 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0003_m_000007_0: Shuffling to disk since 216976206 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:51:52,096 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#1 about to shuffle output of map attempt_1445175094696_0003_m_000007_0 decomp: 216976206 len: 216976210 to DISK
2015-10-18 21:51:52,613 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217009506 bytes from map-output for attempt_1445175094696_0003_m_000001_0
2015-10-18 21:51:52,624 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0003_m_000002_0: Shuffling to disk since 216991624 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:51:52,627 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0003_m_000002_0 decomp: 216991624 len: 216991628 to DISK
2015-10-18 21:51:53,820 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216991628 bytes from map-output for attempt_1445175094696_0003_m_000002_0
2015-10-18 21:51:53,832 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0003_m_000003_0: Shuffling to disk since 216972750 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:51:53,835 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0003_m_000003_0 decomp: 216972750 len: 216972754 to DISK
2015-10-18 21:51:54,032 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216976210 bytes from map-output for attempt_1445175094696_0003_m_000007_0
2015-10-18 21:51:54,042 INFO [fetcher#1] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-39.fareast.corp.microsoft.com:13562 freed by fetcher#1 in 1957ms
2015-10-18 21:51:55,007 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216972754 bytes from map-output for attempt_1445175094696_0003_m_000003_0
2015-10-18 21:51:55,017 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0003_m_000004_0: Shuffling to disk since 216999709 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:51:55,020 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0003_m_000004_0 decomp: 216999709 len: 216999713 to DISK
2015-10-18 21:51:56,119 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216999713 bytes from map-output for attempt_1445175094696_0003_m_000004_0
2015-10-18 21:51:56,130 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0003_m_000005_0: Shuffling to disk since 216990140 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:51:56,133 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0003_m_000005_0 decomp: 216990140 len: 216990144 to DISK
2015-10-18 21:51:57,542 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 216990144 bytes from map-output for attempt_1445175094696_0003_m_000005_0
2015-10-18 21:51:57,552 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: attempt_1445175094696_0003_m_000008_1: Shuffling to disk since 217015228 is greater than maxSingleShuffleLimit (35232152)
2015-10-18 21:51:57,555 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.Fetcher: fetcher#5 about to shuffle output of map attempt_1445175094696_0003_m_000008_1 decomp: 217015228 len: 217015232 to DISK
2015-10-18 21:51:58,690 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 217015232 bytes from map-output for attempt_1445175094696_0003_m_000008_1
2015-10-18 21:51:58,701 INFO [fetcher#5] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: MSRA-SA-41.fareast.corp.microsoft.com:13562 freed by fetcher#5 in 7273ms
2015-10-18 21:52:16,202 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.OnDiskMapOutput: Read 172334808 bytes from map-output for attempt_1445175094696_0003_m_000009_0
2015-10-18 21:52:16,216 INFO [fetcher#4] org.apache.hadoop.mapreduce.task.reduce.ShuffleSchedulerImpl: 04DN8IQ.fareast.corp.microsoft.com:13562 freed by fetcher#4 in 26105ms
2015-10-18 21:52:16,216 INFO [EventFetcher for fetching Map Completion Events] org.apache.hadoop.mapreduce.task.reduce.EventFetcher: EventFetcher is interrupted.. Returning
2015-10-18 21:52:16,222 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: finalMerge called with 0 in-memory map-outputs and 10 on-disk map-outputs
2015-10-18 21:52:16,237 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 10 files, 2125289789 bytes from disk
2015-10-18 21:52:16,238 INFO [main] org.apache.hadoop.mapreduce.task.reduce.MergeManagerImpl: Merging 0 segments, 0 bytes from memory into reduce
2015-10-18 21:52:16,245 INFO [main] org.apache.hadoop.mapred.Merger: Merging 10 sorted segments
2015-10-18 21:52:16,267 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 10 segments left of total size: 2125289689 bytes
2015-10-18 21:52:16,461 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: mapred.skip.on is deprecated. Instead, use mapreduce.job.skiprecords
2015-10-18 21:56:39,413 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445175094696_0003_r_000000_1000 is done. And is in the process of committing
2015-10-18 21:56:39,453 INFO [main] org.apache.hadoop.mapred.Task: Task attempt_1445175094696_0003_r_000000_1000 is allowed to commit now
2015-10-18 21:56:39,460 INFO [main] org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter: Saved output of task 'attempt_1445175094696_0003_r_000000_1000' to hdfs://msra-sa-41:9000/out/out2/_temporary/2/task_1445175094696_0003_r_000000
2015-10-18 21:56:39,491 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445175094696_0003_r_000000_1000' done.
