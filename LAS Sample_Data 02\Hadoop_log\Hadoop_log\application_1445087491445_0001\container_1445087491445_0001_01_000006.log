2015-10-17 21:24:19,786 INFO [main] org.apache.hadoop.metrics2.impl.MetricsConfig: loaded properties from hadoop-metrics2.properties
2015-10-17 21:24:20,000 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Scheduled snapshot period at 10 second(s).
2015-10-17 21:24:20,000 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system started
2015-10-17 21:24:20,045 INFO [main] org.apache.hadoop.mapred.YarnChild: Executing with tokens:
2015-10-17 21:24:20,045 INFO [main] org.apache.hadoop.mapred.YarnChild: Kind: mapreduce.job, Service: job_1445087491445_0001, Ident: (org.apache.hadoop.mapreduce.security.token.JobTokenIdentifier@3db9b677)
2015-10-17 21:24:20,344 INFO [main] org.apache.hadoop.mapred.YarnChild: Sleeping for 0ms before retrying again. Got null now.
2015-10-17 21:24:20,873 INFO [main] org.apache.hadoop.mapred.YarnChild: mapreduce.cluster.local.dir for child: /tmp/hadoop-msrabi/nm-local-dir/usercache/msrabi/appcache/application_1445087491445_0001
2015-10-17 21:24:22,303 INFO [main] org.apache.hadoop.conf.Configuration.deprecation: session.id is deprecated. Instead, use dfs.metrics.session-id
2015-10-17 21:24:23,561 INFO [main] org.apache.hadoop.yarn.util.ProcfsBasedProcessTree: ProcfsBasedProcessTree currently is supported only on Linux.
2015-10-17 21:24:23,604 INFO [main] org.apache.hadoop.mapred.Task:  Using ResourceCalculatorProcessTree : org.apache.hadoop.yarn.util.WindowsBasedProcessTree@3c33282e
2015-10-17 21:24:24,037 INFO [main] org.apache.hadoop.mapred.MapTask: Processing split: hdfs://msra-sa-41:9000/wordcount2.txt:0+134217728
2015-10-17 21:24:24,177 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 0 kvi 26214396(104857584)
2015-10-17 21:24:24,178 INFO [main] org.apache.hadoop.mapred.MapTask: mapreduce.task.io.sort.mb: 100
2015-10-17 21:24:24,178 INFO [main] org.apache.hadoop.mapred.MapTask: soft limit at 83886080
2015-10-17 21:24:24,178 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufvoid = 104857600
2015-10-17 21:24:24,178 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396; length = 6553600
2015-10-17 21:24:24,196 INFO [main] org.apache.hadoop.mapred.MapTask: Map output collector class = org.apache.hadoop.mapred.MapTask$MapOutputBuffer
2015-10-17 21:24:26,796 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:24:26,796 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 0; bufend = 34176387; bufvoid = 104857600
2015-10-17 21:24:26,796 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 26214396(104857584); kvend = 13786980(55147920); length = 12427417/6553600
2015-10-17 21:24:26,797 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 44662145 kvi 11165532(44662128)
2015-10-17 21:24:38,199 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 0
2015-10-17 21:24:38,202 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 44662145 kv 11165532(44662128) kvi 8544104(34176416)
2015-10-17 21:24:39,366 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:24:39,366 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 44662145; bufend = 78841060; bufvoid = 104857600
2015-10-17 21:24:39,366 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 11165532(44662128); kvend = 24953148(99812592); length = 12426785/6553600
2015-10-17 21:24:39,366 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 89326818 kvi 22331700(89326800)
2015-10-17 21:24:48,234 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 1
2015-10-17 21:24:48,237 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 89326818 kv 22331700(89326800) kvi 19710272(78841088)
2015-10-17 21:24:49,077 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:24:49,077 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 89326818; bufend = 18646062; bufvoid = 104857594
2015-10-17 21:24:49,077 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 22331700(89326800); kvend = 9904396(39617584); length = 12427305/6553600
2015-10-17 21:24:49,078 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 29131815 kvi 7282948(29131792)
2015-10-17 21:24:58,269 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 2
2015-10-17 21:24:58,274 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 29131815 kv 7282948(29131792) kvi 4661520(18646080)
2015-10-17 21:24:59,144 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:24:59,144 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 29131815; bufend = 63304471; bufvoid = 104857600
2015-10-17 21:24:59,145 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 7282948(29131792); kvend = 21068996(84275984); length = 12428353/6553600
2015-10-17 21:24:59,145 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 73790219 kvi 18447548(73790192)
2015-10-17 21:25:07,221 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 3
2015-10-17 21:25:07,226 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 73790219 kv 18447548(73790192) kvi 15826124(63304496)
2015-10-17 21:25:08,037 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:25:08,037 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 73790219; bufend = 3105692; bufvoid = 104857600
2015-10-17 21:25:08,038 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 18447548(73790192); kvend = 6019304(24077216); length = 12428245/6553600
2015-10-17 21:25:08,038 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 13591446 kvi 3397856(13591424)
2015-10-17 21:25:16,517 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 4
2015-10-17 21:25:16,522 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 13591446 kv 3397856(13591424) kvi 776428(3105712)
2015-10-17 21:25:17,770 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:25:17,770 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 13591446; bufend = 47766565; bufvoid = 104857600
2015-10-17 21:25:17,770 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 3397856(13591424); kvend = 17184524(68738096); length = 12427733/6553600
2015-10-17 21:25:17,771 INFO [main] org.apache.hadoop.mapred.MapTask: (EQUATOR) 58252322 kvi 14563076(58252304)
2015-10-17 21:25:18,251 INFO [main] org.apache.hadoop.mapred.MapTask: Starting flush of map output
2015-10-17 21:25:25,776 INFO [SpillThread] org.apache.hadoop.mapred.MapTask: Finished spill 5
2015-10-17 21:25:25,779 INFO [main] org.apache.hadoop.mapred.MapTask: (RESET) equator 58252322 kv 14563076(58252304) kvi 12517428(50069712)
2015-10-17 21:25:25,779 INFO [main] org.apache.hadoop.mapred.MapTask: Spilling map output
2015-10-17 21:25:25,779 INFO [main] org.apache.hadoop.mapred.MapTask: bufstart = 58252322; bufend = 63875956; bufvoid = 104857600
2015-10-17 21:25:25,779 INFO [main] org.apache.hadoop.mapred.MapTask: kvstart = 14563076(58252304); kvend = 12517432(50069728); length = 2045645/6553600
2015-10-17 21:25:26,755 INFO [main] org.apache.hadoop.mapred.MapTask: Finished spill 6
2015-10-17 21:25:26,770 INFO [main] org.apache.hadoop.mapred.Merger: Merging 7 sorted segments
2015-10-17 21:25:26,778 INFO [main] org.apache.hadoop.mapred.Merger: Down to the last merge-pass, with 7 segments left of total size: 228407901 bytes
2015-10-17 21:25:52,057 INFO [main] org.apache.hadoop.mapred.Task: Task:attempt_1445087491445_0001_m_000001_0 is done. And is in the process of committing
2015-10-17 21:25:52,154 INFO [main] org.apache.hadoop.mapred.Task: Task 'attempt_1445087491445_0001_m_000001_0' done.
2015-10-17 21:25:52,255 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: Stopping MapTask metrics system...
2015-10-17 21:25:52,256 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system stopped.
2015-10-17 21:25:52,256 INFO [main] org.apache.hadoop.metrics2.impl.MetricsSystemImpl: MapTask metrics system shutdown complete.
