"""
Quick test to verify the Well Log App works with Xeek data
"""

import pandas as pd
import sys
import os

# Add the current directory to Python path
sys.path.append('.')

# Import the plotting functions from the app
try:
    from well_log_app import (
        create_gamma_ray_track, 
        create_resistivity_track, 
        create_density_neutron_track, 
        create_density_neutron_crossplot,
        validate_data
    )
    print("✅ Successfully imported plotting functions from well_log_app.py")
except ImportError as e:
    print(f"❌ Failed to import functions: {e}")
    sys.exit(1)

def test_with_sample_data():
    """Test with sample data"""
    print("\n🧪 Testing with Sample Data")
    print("=" * 40)
    
    # Create sample data
    sample_data = {
        'DEPTH_MD': [1500, 1501, 1502, 1503, 1504, 1505],
        'GR': [45, 50, 55, 48, 52, 47],
        'RDEP': [10, 15, 8, 12, 18, 14],
        'RHOB': [2.3, 2.4, 2.2, 2.35, 2.45, 2.32],
        'NPHI': [0.15, 0.18, 0.12, 0.16, 0.20, 0.14],
        'CALI': [8.5, 8.7, 8.3, 8.6, 8.8, 8.4],
        'DTC': [65, 68, 62, 66, 70, 64],
        'PEF': [2.8, 3.1, 2.6, 2.9, 3.2, 2.7],
        'LITH': ['Shale', 'Sandstone', 'Shale', 'Limestone', 'Shale', 'Sandstone']
    }
    
    df = pd.DataFrame(sample_data)
    print(f"✅ Sample data created: {df.shape}")
    
    # Test data validation
    try:
        is_valid, optional_cols = validate_data(df)
        print(f"✅ Data validation: {'PASS' if is_valid else 'FAIL'}")
        print(f"   Optional columns found: {optional_cols}")
    except Exception as e:
        print(f"❌ Data validation failed: {e}")
        return False
    
    # Test each plotting function
    plot_tests = [
        ("Gamma Ray Track", lambda: create_gamma_ray_track(df, color_by_lithology=True)),
        ("Resistivity Track", lambda: create_resistivity_track(df, color_by_lithology=True)),
        ("Density-Neutron Track", lambda: create_density_neutron_track(df)),
        ("Crossplot (GR)", lambda: create_density_neutron_crossplot(df, 'GR')),
        ("Crossplot (LITH)", lambda: create_density_neutron_crossplot(df, 'LITH'))
    ]
    
    for test_name, test_func in plot_tests:
        try:
            fig = test_func()
            print(f"✅ {test_name}: SUCCESS")
        except Exception as e:
            print(f"❌ {test_name}: FAILED - {e}")
            return False
    
    return True

def test_with_xeek_data():
    """Test with actual Xeek data if available"""
    print("\n🧪 Testing with Xeek Data")
    print("=" * 40)
    
    # Try to find Xeek files
    xeek_files = [
        'xeek_lithology_balanced.csv',
        'xeek_single_well_15_9-13.csv'
    ]
    
    for filename in xeek_files:
        if os.path.exists(filename):
            try:
                print(f"📁 Testing with {filename}")
                df = pd.read_csv(filename)
                print(f"   Data shape: {df.shape}")
                
                # Test validation
                is_valid, optional_cols = validate_data(df)
                if not is_valid:
                    print(f"   ❌ Data validation failed")
                    continue
                
                print(f"   ✅ Data validation passed")
                print(f"   Optional columns: {optional_cols}")
                
                # Test a few plots with subset of data (for speed)
                test_df = df.head(100)  # Use first 100 rows for testing
                
                # Test gamma ray plot
                fig_gr = create_gamma_ray_track(test_df, color_by_lithology=True)
                print(f"   ✅ Gamma Ray plot created")
                
                # Test crossplot
                fig_cross = create_density_neutron_crossplot(test_df, 'LITH')
                print(f"   ✅ Crossplot created")
                
                print(f"   🎉 {filename} test PASSED")
                return True
                
            except Exception as e:
                print(f"   ❌ Error with {filename}: {e}")
                continue
    
    print("⚠️ No Xeek files found for testing")
    return True  # Not a failure, just no files to test

def main():
    """Main test function"""
    print("🧪 WELL LOG APP FUNCTIONALITY TEST")
    print("=" * 60)
    
    # Test with sample data
    sample_test = test_with_sample_data()
    
    # Test with Xeek data
    xeek_test = test_with_xeek_data()
    
    print(f"\n📊 FINAL RESULTS:")
    print(f"✅ Sample data test: {'PASS' if sample_test else 'FAIL'}")
    print(f"✅ Xeek data test: {'PASS' if xeek_test else 'FAIL'}")
    
    if sample_test and xeek_test:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"🚀 The Well Log App is ready to use!")
        print(f"📱 Open: http://localhost:8501")
        print(f"📁 Upload any of the Xeek CSV files to test")
    else:
        print(f"\n❌ Some tests failed")
    
    return sample_test and xeek_test

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
